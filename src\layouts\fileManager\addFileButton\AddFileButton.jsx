import React, { useEffect, useState } from "react";
import ReactD<PERSON> from "react-dom";

import { ProgressBar, Toast } from "react-bootstrap";
import { database, storage } from "../../../firebase";
import { ROOT_FOLDER } from "../../../hooks/useFolder";
import { v4 as uuidV4 } from "uuid";
import NoteAddIcon from "@mui/icons-material/NoteAdd";
import "./addFileButton.scss";
import {
  Alert,
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  IconButton,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import { DropzoneArea } from "material-ui-dropzone";
import { DialogActions } from "@material-ui/core";
import { convertBase64 } from "../../../hooks/useBase64";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../../services/mongo-refresh.context";
import { toastMessage, toastMessageSuccess } from "../../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../../components/buttons/Buttons";
import { Typography, message } from "antd";
import GetPreviewComponent from "../../../components/commons/getPreview.component";
import { useAuth } from "../../../hooks/AuthProvider";
import { useCheckAccess } from "../../../utils/useCheckAccess";

const AddFileButton = ({ currentFolder }) => {
  const [progress, setProgress] = useState(0);
  const [open, setOpen] = useState(false);
  const [fileType, setFileType] = useState("image");
  const [fileData, setFileData] = useState("");
  const [fileName, setFileName] = useState("");
  const [machineId, setMachineId] = useState("");
  const [machineData, setMachines] = useState([]);
  const [selectedFileForStorage, setSelectedFileForStorage] = useState("");
  const [update, setUpdate] = useState(false);
  const { currentMode } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const { currentUser } = useAuth();

  const typesImages = ["image/png", "image/jpeg", "image/jpg"];
  const videoTypes = ["video/mp4", "video/mkv", "video/mov"];
  const audioTypes = ["audio/mp3", "audio/mpeg"];
  const documentTypes = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  ];

  const hasPOSTAccess = useCheckAccess("files", "POST");
  console.log(hasPOSTAccess, "blahhhhhhhhhhhhhhhhh");
  // Fetch machines
  const fetchAllMachines = async () => {
    await axios.get(`${dbConfig.url}/machines`).then((res) => {
      setMachines(res.data.data);
    });
  };

  useEffect(() => {
    fetchAllMachines();
  }, []);

  // Handle file upload and validation
  const handleUpload = async (loadedFiles) => {
    const file = loadedFiles[0];
    if (!file) return;

    const base64 = await convertBase64(file);
    const fileTypeMap = {
      image: typesImages,
      video: videoTypes,
      audio: audioTypes,
      application: documentTypes,
    };

    if (
      fileTypeMap?.[fileType]?.includes(file.type) ||
      (fileType?.includes("application") &&
        fileTypeMap["application"].includes(fileType))
    ) {
      // Used for selecting icons for the files
      setFileType((prev) => (prev?.includes("application") ? file.type : prev));

      setFileData(base64);
      setSelectedFileForStorage(file);
      setUpdate(true);
    } else {
      toastMessage({ message: `Incorrect ${fileType} Format` });
      setUpdate(false);
    }
  };

  // Handle file deletion
  const handleDelete = () => {
    setFileData("");
    setSelectedFileForStorage("");
    setUpdate(false);
  };

  // Handle file confirmation
  const handleConfirm = async () => {
    if (!fileData) return toastMessage({ message: "Please add a valid file!" });
    if (!fileName.trim())
      return toastMessage({ message: "Please enter a file name!" });
    // if (!machineId) return toastMessage({ message: "Please select a machine!" });
    if (!update) return toastMessage({ message: "Please check your file!" });

    const data = {
      name: fileName,
      created_at: new Date(),
      parent_id: currentFolder?._id || "",
      file_type: fileType,
      mid: machineId,
      creator: currentUser?.email,
    };

    const config = {
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total,
        );
        setProgress(percentCompleted);
      },
    };

    let url = "";
    if (selectedFileForStorage) {
      const fd = new FormData();
      fd.append("image", selectedFileForStorage);
      const res = await axios.post(`${dbConfig?.url_storage}/upload`, fd);
      url = res?.data?.data;
    }

    await axios
      .post(`${dbConfig.url}/files`, { ...data, url }, config)
      .then(() => {
        setProgress(0);
        setOpen(false);
        setMachineId("");
        setFileName("");
        setFileData("");
        setSelectedFileForStorage("");
        setUpdate(false);
        setFileType("image");
        setRefreshCount(refreshCount + 1);
        toastMessageSuccess({ message: "File uploaded successfully" });
      })
      .catch((err) => {
        toastMessage({ message: "Upload failed. Please try again." });
      });
  };

  const handleCancel = () => {
    setFileName("");
    setFileData("");
    setSelectedFileForStorage("");
    setFileType("image");
    setUpdate(false);
    setMachineId("");
    setOpen(false);
  };

  return (
    <>
      <Tooltip title="Add New File">
        <IconButton
          sx={{ color: currentMode === "Dark" ? "white" : "inherit" }}
          onClick={() => setOpen(true)}
          disabled={!hasPOSTAccess}
        >
          <NoteAddIcon />
        </IconButton>
      </Tooltip>

      <Dialog
        open={open}
        onClose={handleCancel}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            padding: 1,
            bgcolor: currentMode === "Dark" ? "#333" : "white",
            height: "95%",
          },
        }}
      >
        <DialogTitle sx={{ fontWeight: "bold", pb: 1 }}>
          Upload a New File
        </DialogTitle>

        <DialogContent>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2, mt: 2 }}>
            {/* Machine Selection */}
            <FormControl fullWidth size="small">
              <InputLabel>Select Machine</InputLabel>
              <Select
                value={machineId}
                label="Select Machine"
                onChange={(e) => setMachineId(e.target.value)}
                required
              >
                <MenuItem value="" disabled>
                  -- Select a Machine --
                </MenuItem>
                {machineData?.map((data) => (
                  <MenuItem key={data._id} value={data._id}>
                    {data.title}
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>Select the associated machine</FormHelperText>
            </FormControl>

            <div className="flex flex-row items-center justify-between gap-x-8">
              {/* File Type Selection */}
              <FormControl fullWidth size="small">
                <InputLabel>File Type</InputLabel>
                <Select
                  // Needed as fileType will be used to indirectly set the file icons
                  value={
                    fileType.includes("application") ? "application" : fileType
                  }
                  label="File Type"
                  onChange={(e) => setFileType(e.target.value)}
                >
                  <MenuItem value="image">Image</MenuItem>
                  <MenuItem value="video">Video</MenuItem>
                  <MenuItem value="audio">Audio</MenuItem>
                  <MenuItem value="application">Document</MenuItem>
                </Select>
                <FormHelperText>{"Choose a file type"}</FormHelperText>
              </FormControl>

              {/* File Name Input */}
              <TextField
                label="File Name"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
                fullWidth
                size="small"
                placeholder="e.g., system-check"
                required
                helperText="Enter a descriptive name for your file"
              />
            </div>

            {/* Dropzone */}
            <Box>
              <DropzoneArea
                dropzoneClass={"dropZoneMaxHeight"}
                classes={{
                  textContainer: "dropZoneTextContainer",
                  text: "dropZoneText",
                  icon: "dropZoneIcon",
                }}
                onChange={handleUpload}
                onDelete={handleDelete}
                filesLimit={1}
                maxFileSize={100 * 1024 * 1024}
                dropzoneText="Drag & Drop or Click to Add File"
                showFileNames
                disabled={!fileName.trim() || !machineId}
                getDropRejectMessage={() => "File size exceeds 100MB!"}
                acceptedFiles={
                  fileType === "image"
                    ? typesImages
                    : fileType === "video"
                      ? videoTypes
                      : fileType === "audio"
                        ? audioTypes
                        : documentTypes
                }
                sx={{ borderRadius: 1 }}
              />
              <FormHelperText sx={{ mt: 1 }}>
                Max file size: 100MB. Supported formats:{" "}
                {fileType === "image"
                  ? "PNG, JPG"
                  : fileType === "video"
                    ? "MP4, MKV, MOV"
                    : fileType === "audio"
                      ? "MP3"
                      : "PDF, DOCX, XLSX, PPTX"}
              </FormHelperText>
            </Box>

            {/* Preview */}
            {fileData && (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: "1rem",
                }}
              >
                <GetPreviewComponent
                  sourceUrl={fileData}
                  fileFormat={fileType}
                  previewImageStyle={{
                    maxWidth: "100%",
                    maxHeight: "225px",
                    borderRadius: 2,
                  }}
                  previewVideoStyle={{
                    width: "520px",
                    maxHeight: "225px",
                    borderRadius: 2,
                  }}
                  previewAudioStyle={{ width: "100%", mt: 2 }}
                />
              </Box>
            )}

            {/* Progress Bar */}
            {progress > 0 && (
              <Box sx={{ mt: 2 }}>
                <LinearProgress variant="determinate" value={progress} />
                <Typography
                  variant="caption"
                  align="center"
                  sx={{ display: "block", mt: 1 }}
                >
                  {progress}% Uploaded
                </Typography>
              </Box>
            )}
          </Box>
        </DialogContent>

        <DialogActions sx={{ pt: 2 }}>
          <Button
            variant="outlined"
            color="error"
            onClick={handleCancel}
            sx={{ mr: 1 }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleConfirm}
            disabled={!fileData || !fileName.trim() || progress > 0}
          >
            Upload File
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AddFileButton;
