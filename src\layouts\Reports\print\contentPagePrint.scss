.contentViewPrintPage {
  padding-right: 15px;
  padding-left: 15px;
  width: 100%;
  min-height: 84vh; // 84vh
  text-align: left;

  //margin-bottom: .4rem;
  .outerMainConatiner {
    border: 2px solid black;
    width: 100%;
    //min-height: 86vh;
    text-align: left;
    ///background-color: #00274e;
    margin-bottom: 10px; // not working well in print
    // margin-top: 5px;
    //margin: 5px;
    padding-top: 8px;

    .allContentPreviewContainerPrintPage {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      height: 100%;
      //background-color: green;
      //padding: 5px;
      //border: 2px solid black;

      .contentPrintPageHeading {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 0.3rem;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        //padding-top: .5rem;
        //padding-bottom: .5rem;
        width: 99%;
        box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0);
        border-width: 2px;
        border-color: black;
        border-radius: 10px;
        color: #344767;

        .contentHeading_left {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          .contentHeadingInfoLeft {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;

            .contentHeadingTitle {
              font-size: 18px;
              font-weight: 500;
            }
          }
        }

        .contentHeading_right {
          .contentHeadingInfoRight {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            text-align: left;
          }
        }
      }

      .contentPageMain {
        margin: 1rem 0;
        padding: 2.5rem 1.6rem;
        width: 100%;
        background-color: #fff;
        box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0); // this is for no effect
        border-radius: 10px;
        color: #344767;
        border: 2px solid black;

        .contentMainContainer {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;

          ///min-height: 90vh;
          //
          .height90Vh {
            min-height: 86vh; // 86vh
            width: 100%;
          }
          .height80Vh {
            height: 83vh; // 83vh used in aproval table
            width: 100%;
          }
          .height75Vh {
            // not  used
            height: 82vh; // 86vh
            width: 100%;
          }

          .content_sub-section {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: center;
            margin-bottom: 16px;
            width: 100%;

            .contentMainTitle {
              margin-bottom: 16px;
              font-size: 24px;
              font-weight: 700;
              width: fit-content;
              border-bottom: 1px solid #344767;
            }

            .contentMainSubTitle {
              margin-bottom: 8px;
              font-size: 18px;
              font-weight: 700;
            }

            .title {
              font-size: 14px;
              font-weight: 500;
            }
          }
        }
      }

      .faBtn {
        position: fixed;
        bottom: 64px;
        right: 150px;

        background: #00274e;
        border-radius: 50%;
        z-index: 2;

        &:hover {
          background: #003972;
        }
      }

      .faBtnLeft {
        position: fixed;
        bottom: 64px;
        right: 64px;
        background: #00274e;
        border-radius: 50%;
        z-index: 2;

        &:hover {
          background: #003972;
        }
      }

      .fabDrawer {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        position: fixed;
        bottom: 64px;
        right: 195px;
        height: 56px;
        padding: 0px 25px 0px 8px;
        background: #eee;
        box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
        border-radius: 10px;

        button {
          height: 100%;
          padding: 0;

          i {
            font-size: 18px;
          }
        }
      }
    }
  }
}
