import React, { useContext, useState } from "react";

import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { usersData } from "../../utils/Users";
import { firebaseLooper } from "../../tools/tool";
import { alpha, styled } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import InputLabel from "@mui/material/InputLabel";
import { Box, Button, Pagination, TextField } from "@mui/material";
import Autocomplete, { createFilterOptions } from "@mui/material/Autocomplete";
import { useStateContext } from "../../context/ContextProvider";
import UserItem from "./UserItem";
import { UserListContext } from "../../services2/users/userList.context";
import { sharedCss } from "../../styles/sharedCss";
import NoDataComponent from "../../components/commons/noData.component";

// Roles options dropdown menu

const UsersListTable = () => {
  const [passwordToggel, setPasswordToggel] = useState(false);
  const { currentMode, currentColorLight } = useStateContext();
  const [searchKey, setSearchKey] = useState("");
  const [searchedUsers, setSearchedUsers] = useState([]);
  const commonCss = sharedCss();
  const {
    usersInView,
    setUsersInView,
    numberOfUsersInView,
    setNumberOfUsersInView,
    page,
    setPage,
    users,
    setUsers,
    usersInViewContainerCount,
    setUsersInViewContainerCount,
    usersPageInfoContainerCounter,
  } = useContext(UserListContext);

  function passwordViewToggel() {
    setPasswordToggel(true);
  }

  const handleNumberOfUserInView = (e) => {
    window.localStorage.setItem("numOfUserInView", e.target.value); //for preventing pagination mismatch while editing 1.1/3
    setNumberOfUsersInView(e.target.value);
    usersPageInfoContainerCounter(users?.length, e.target.value);
    setUsersInView(users.slice(0, e.target.value));
  };

  //
  const handlePageChange = (event, value) => {
    window.localStorage.setItem("usersActivePageNum", value); // for preventing pagination mismatch while editing 1/3
    window.localStorage.setItem("numOfUsersInView", numberOfUsersInView); //
    setPage(value);

    if (searchedUsers.length > 0) {
      if (value == usersInViewContainerCount) {
        setUsersInView(searchedUsers.slice((value - 1) * numberOfUsersInView));
      } else {
        setUsersInView(
          searchedUsers.slice(
            (value - 1) * numberOfUsersInView,
            value * numberOfUsersInView,
          ),
        );
      }
    } else {
      if (value == usersInViewContainerCount) {
        setUsersInView(users.slice((value - 1) * numberOfUsersInView));
      } else {
        setUsersInView(
          users.slice(
            (value - 1) * numberOfUsersInView,
            value * numberOfUsersInView,
          ),
        );
      }
    }
  };

  return (
    <div>
      <div className={commonCss?.generalBackground} style={{ padding: "1rem" }}>
        <TableContainer component={Paper} className="table">
          <Table
            // style={currentMode === 'Dark' ? { backgroundColor: "#161C24"} : { }}
            sx={{
              minWidth: 650,
            }}
          >
            <TableHead
              style={
                currentMode === "Dark"
                  ? { backgroundColor: "#212B36" }
                  : { backgroundColor: "" }
              }
            >
              <TableRow>
                <TableCell align="left">Full Name</TableCell>
                <TableCell align="left">Username</TableCell>
                {/* <TableCell align="left">Active Status</TableCell> */}
                <TableCell align="left">Role</TableCell>
                <TableCell align="left">Email</TableCell>
                <TableCell align="left">Contact</TableCell>
                {/* <Table Cell align="left">Company Id</TableCell> */}
                <TableCell align="left">Actions</TableCell>
                {/* <TableCell align="left" className='hover:cursor-pointer' onClick={() => setPasswordToggel(!passwordToggel)} >
                Password
                <span>
                  {passwordToggel ? <i className="ri-eye-fill text-green-500" /> : <i className="ri-eye-off-fill text-red-700 animate-pulse" />}
                </span>
              </TableCell> */}
              </TableRow>
            </TableHead>
            <TableBody>
              {usersInView.map((user, index) => (
                <UserItem
                  key={user.id + index}
                  user={user}
                  passwordToggel={passwordToggel}
                />
              ))}
              {!usersInView?.length && (
                <>
                  <NoDataComponent cellColSpan={6} />
                </>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {!!usersInView?.length && (
          <div
            className="mt-6"
            style={{ display: "flex", justifyContent: "center" }}
          >
            <Pagination
              count={usersInViewContainerCount}
              page={page}
              onChange={handlePageChange}
              color="primary"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default UsersListTable;
