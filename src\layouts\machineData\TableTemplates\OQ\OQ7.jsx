import React, { useState, useMemo, useContext } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "../TableTempletes.css";

import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import TextField from "@mui/material/TextField";

import { useStateContext } from "../../../../context/ContextProvider";
import { db, storage } from "../../../../firebase";
import { companies, companyId_constant } from "../../../../constants/data";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../../../components/buttons/Buttons";

import { useStorageTablesFile } from "../../../../utils/useStorageTablesFile";
import { InputLabel, LinearProgress, Link, Typography } from "@mui/material";
import { Empty } from "antd";
import { FileDownload, ImageSearchOutlined } from "@mui/icons-material";
import {
  FileImageFilled,
  FilePdfFilled,
  PictureFilled,
} from "@ant-design/icons";
import { DeleteByUrl } from "../../../../utils/StorageOptions";
import { DropzoneArea } from "material-ui-dropzone";
import { max } from "lodash";
import { FileManagerSelectorContext } from "../../../../services/fileManager/file-manager-select.context";
import FileSelector from "../../../FileSelector/screens/FileSelector";
import PreviewIcon from "@mui/icons-material/Preview";

export default function OQ7({
  rowData,
  type,
  machineName,
  fatDataDocId,
  useAt,
}) {
  const { currentMode, currentColorLight } = useStateContext();
  const [open, setOpen] = useState(false);
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");

  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useMemo(() => {
    // when theme changes then S.No array got reversed. So to stop that This Memonisation is used.
    setSortDirection("");
    //console.log("TableTemplates usememo")
  }, [currentMode]);

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#161c24" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

            <TableRow sx={{ border: theme.borderDesign }}>
              {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}><span className='pr-1'>S.No</span>
                                {useAt !== 'tableMain' ? <>
                                    {(sortDirection === 'down' || sortDirection === '') ? <span className={theme.arrow} onClick={() => setSortDirection('up')}>&#x21E9; </span>
                                        : <span className={theme.arrow} onClick={() => setSortDirection('down')}>&#x21E7; </span>
                                    } </>
                                    : null
                                }
                            </TableCell> */}
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Check-Point
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Acceptance criteria
              </TableCell>
              <TableCell
                sx={{ border: theme.borderDesign }}
                colSpan={2}
                align="center"
              >
                Actuals
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Avg
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Pass/Fail
              </TableCell>
              {useAt !== "tableMain" && (
                <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                  Image
                </TableCell>
              )}
            </TableRow>

            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }}>Min</TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>Max</TableCell>
              {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

              {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} >Image</TableCell> }           */}

              {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  sx={{
                    border: theme.borderDesign,
                    background: currentMode === "Dark" ? "#212b36" : "",
                  }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[2]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[3]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[4]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[5]}
                  </TableCell>
                  {/* <TableCell sx={{ border: theme.borderDesign }} >{data[6]}</TableCell>
                                <TableCell sx={{ border: theme.borderDesign }} >{data[7]}</TableCell> */}
                  {useAt !== "tableMain" && (
                    <TableCell
                      sx={{ border: theme.borderDesign }}
                      align="center"
                    >
                      {data[6] === null ? (
                        "Add One"
                      ) : (
                        <>
                          {data[6]?.includes(".pdf") ? (
                            <FilePdfFilled
                              className="text-red-700"
                              onContextMenu={() =>
                                (window.location.href = data[6])
                              }
                              title="Press right click to open file"
                            />
                          ) : (
                            <PictureFilled
                              onContextMenu={() =>
                                (window.location.href = data[6])
                              }
                              title="Press right click to open file"
                            />
                          )}
                        </>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
          <EditTableRow
            rowDataSelected={rowDataSelected}
            tableType={"OQ7"}
            type={type}
            fatDataDocId={fatDataDocId}
            machineName={machineName}
            handleClose={handleClose}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}

//////////////////

function EditTableRow({
  rowDataSelected,
  tableType,
  type,
  machineName,
  fatDataDocId,
  handleClose,
}) {
  const { currentMode } = useStateContext();

  //const [sNo, setSNo] = useState(rowDataSelected[0]);
  // const [tagNo, setTagNo] = useState(rowDataSelected[1]); //
  const [checkPoint, setCheckPoint] = useState(rowDataSelected[0]);
  // const [make, setMake] = useState(rowDataSelected[3]);
  // const [model, setModel] = useState(rowDataSelected[4]);
  const [acceptanceCriteria, setAcceptanceCriteria] = useState(
    rowDataSelected[1],
  ); //
  const [min, setMin] = useState(rowDataSelected[2]); //
  const [max, setMax] = useState(rowDataSelected[3]); //
  // const [rating, setRating] = useState(rowDataSelected[5]);
  //const [testReportNo, setTestReportNo] = useState(rowDataSelected[6]);
  const [avg, setAvg] = useState(rowDataSelected[4]);
  const [passFail, setPassFail] = useState(rowDataSelected[5]);
  // const [result, setResult] = useState(rowDataSelected[5]);
  const [urlData, setUrlData] = useState(rowDataSelected[6]);

  const [fileType, setFileType] = useState("");
  const [file, setFile] = useState(null);
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];
  const { fileUrl, changeFileUrl } = useContext(FileManagerSelectorContext);
  const [fType, setFType] = useState("index" in rowDataSelected ? true : false);
  //
  const [index, setIndex] = useState(rowDataSelected[8]);
  const handleChangeImage = (file) => {
    let selectedFile = file[0];
    //console.log(file[0]) //(selectedFile?.size/1024))

    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        // delete last uploaded file via url if image changes
        if (url) {
          DeleteByUrl(url);
        }
        //
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };
  // const { currentColor, currentMode } = useStateContext();
  const { progress, url } = useStorageTablesFile(file);

  //
  const handleUpdateRow = () => {
    if (fType && isNaN(parseInt(index))) {
      toastMessageWarning({ message: "The input of index should be a Number" });
      return;
    }
    let data = {
      check_point: checkPoint,
      acceptance_criteria: acceptanceCriteria,
      min: min,
      max: max,
      avg: avg,
      pass_fail: passFail,
      index: index,
      url: fileUrl === null ? urlData : fileUrl,
    };
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(type)
    //   .doc(fatDataDocId)
    //   .collection("table" + tableType)
    //   .doc(rowDataSelected[7]) // rowDataSelected[9] = id of the document
    //   .update(data)
    //   .then(() => {
    //     toastMessageSuccess({ message: "Row updated Successfully" });
    //     handleClose();
    //   })
    //   .catch((e) => {
    //     toastMessageWarning({ message: "Error ", e });
    //     console.log("OQ7:", e);
    //   });
  };

  const handleCancel = () => {
    if (url) {
      DeleteByUrl(url);
    }
    handleClose();
  };

  const handleDeleteDropZone = (url) => {
    DeleteByUrl(url); // to delete the file from storage
    setFile(null); // to remove the preview
  };

  return (
    <>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          <div className="flex justify-between py-2">
            <div className="w-2/12">
              <TextField
                label="Check-Point"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={checkPoint}
                onChange={(e) => setCheckPoint(e.target.value)}
              />
            </div>

            <div className="2/12">
              <TextField
                label="acceptance Criteria"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={acceptanceCriteria}
                onChange={(e) => setAcceptanceCriteria(e.target.value)}
              />
            </div>

            <div className="w-2/12">
              <TextField
                type="number"
                label="Min"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={min}
                onChange={(e) => setMin(e.target.value)}
              />
            </div>

            <div className="w-2/12">
              <TextField
                type="number"
                label="Max"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={max}
                onChange={(e) => setMax(e.target.value)}
              />
            </div>

            <div className="w-2/12">
              <TextField
                label="Avg"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={avg}
                onChange={(e) => setAvg(e.target.value)}
              />
            </div>

            <div className="w-2/12">
              <TextField
                label="Pass/Fail"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={passFail}
                onChange={(e) => setPassFail(e.target.value)}
              />
            </div>
            {/* {fType */}
            {(url?.includes(".pdf") ||
              fileUrl?.includes(".pdf") ||
              urlData?.includes(".pdf")) && (
              <div className="w-2/12">
                <TextField
                  label="Index"
                  id="outlined-size-small"
                  defaultValue="Na"
                  size="small"
                  value={index}
                  onChange={(e) => setIndex(e.target.value)}
                />
              </div>
            )}
          </div>
        </DialogContentText>
        <FileSelector />
        <Typography sx={{ ml: "20px" }} align="left">
          {" "}
          <PreviewIcon /> Preview
        </Typography>
        <img width="250px" src={urlData} />
      </DialogContent>
      <DialogActions>
        <ButtonBasic buttonTitle="Update" onClick={handleUpdateRow} />
        <ButtonBasicCancel
          buttonTitle="Cancel &#x2716;"
          onClick={handleCancel}
        />
      </DialogActions>
    </>
  );
}
