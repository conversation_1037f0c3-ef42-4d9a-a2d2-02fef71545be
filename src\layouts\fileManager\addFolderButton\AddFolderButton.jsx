import { useEffect, useState } from "react";
import {
  Button,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  FormControl,
  InputLabel,
  FormHelperText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import CreateNewFolderIcon from "@mui/icons-material/CreateNewFolder";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import { useStateContext } from "../../../context/ContextProvider";
import { useMongoRefresh } from "../../../services/mongo-refresh.context";
import { useAuth } from "../../../hooks/AuthProvider";
import { toastMessageSuccess, toastMessageWarning } from "../../../tools/toast";
import { ROOT_FOLDER } from "../../../hooks/useFolder";
import "./addFolderButton.scss";
import { useCheckAccess } from "../../../utils/useCheckAccess";

const dialogStyle = {
  width: 450,
  maxHeight: 350, // Reduced height
  bgcolor: "background.paper",
  boxShadow: 24,
  borderRadius: 2,
  p: 2, // Reduced padding for compactness
};

const AddFolderButton = ({ currentFolder }) => {
  const [open, setOpen] = useState(false);
  const [name, setName] = useState("");
  const [machineId, setMachineId] = useState("");
  const [machineData, setMachines] = useState([]);
  const { currentMode } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const { currentUser } = useAuth();

  const hasPOSTAccess = useCheckAccess("folders", "POST");

  // Fetch machines
  const fetchAllMachines = async () => {
    try {
      const res = await axios.get(`${dbConfig.url}/machines`);
      setMachines(res.data.data);
    } catch (error) {
      toastMessageWarning({
        message: "Failed to load machines. Please try again.",
      });
    }
  };

  useEffect(() => {
    fetchAllMachines();
  }, []);

  const openDialog = () => setOpen(true);

  const closeDialog = () => {
    setName("");
    setMachineId("");
    setOpen(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!currentFolder) return;

    if (!name.trim()) {
      return toastMessageWarning({
        message: "Please fill in all required fields!",
      });
    }

    const path = [...currentFolder.path];
    if (currentFolder !== ROOT_FOLDER) {
      path.push({ name: currentFolder.name, id: currentFolder._id });
    }

    try {
      await axios.post(`${dbConfig.url}/folders`, {
        name: name.trim(),
        mid: machineId,
        parent_id: currentFolder._id || "",
        path,
        creator: currentUser?.email,
        created_at: new Date(),
      });
      toastMessageSuccess({ message: "Folder added successfully!" });
      setRefreshCount(refreshCount + 1);
      closeDialog();
    } catch (error) {
      toastMessageWarning({
        message: "Failed to create folder. Please try again.",
      });
    }
  };

  return (
    <>
      <Tooltip title="Add New Folder">
        <IconButton
          onClick={openDialog}
          sx={{ color: currentMode === "Dark" ? "white" : "inherit" }}
          aria-label="Add new folder"
          disabled={!hasPOSTAccess}
        >
          <CreateNewFolderIcon />
        </IconButton>
      </Tooltip>

      <Dialog
        open={open}
        onClose={closeDialog}
        PaperProps={{
          sx: {
            ...dialogStyle,
            bgcolor: currentMode === "Dark" ? "#333" : "background.paper",
          },
          className: "modal",
        }}
      >
        <DialogTitle
          sx={{
            fontWeight: "bold",
            color: currentMode === "Dark" ? "white" : "black",
            pb: 1,
          }}
        >
          Create New Folder
        </DialogTitle>

        <DialogContent>
          <form onSubmit={handleSubmit}>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "16px",
                paddingTop: "8px",
              }}
            >
              {/* Folder Name */}
              <TextField
                label="Folder Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                fullWidth
                size="small"
                placeholder="e.g., Folder1"
                required
                helperText="Enter a unique folder name"
                sx={{
                  input: { color: currentMode === "Dark" ? "white" : "black" },
                }}
              />

              {/* Machine Selection */}
              <FormControl fullWidth size="small">
                <InputLabel>Select Machine</InputLabel>
                <Select
                  value={machineId}
                  label="Select Machine"
                  onChange={(e) => setMachineId(e.target.value)}
                  sx={{ color: currentMode === "Dark" ? "white" : "black" }}
                >
                  <MenuItem value="" disabled>
                    -- Select a Machine --
                  </MenuItem>
                  {machineData?.map((data) => (
                    <MenuItem key={data._id} value={data._id}>
                      {data.title}
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>
                  Associate this folder with a machine
                </FormHelperText>
              </FormControl>
            </div>
          </form>
        </DialogContent>

        <DialogActions sx={{ px: 2, pb: 1 }}>
          <Button
            variant="outlined"
            color="error"
            onClick={closeDialog}
            sx={{ minWidth: 100 }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={!name.trim()}
            sx={{ minWidth: 100 }}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AddFolderButton;
