.userPage {
  .userPageInfoContainer {
    width: 100%;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    overflow: hidden;
    padding: 1.5rem 1.2rem;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      color: #344767;

      h3 {
        font-size: 1.2rem;
        font-weight: 500;
        opacity: 0.9;
      }
    }
    .info {
      display: flex;
      flex-direction: column;
      h3 {
        font-size: 1.4rem;
        font-weight: 500;
        margin-bottom: 0.7rem;
      }
      p {
        font-size: 0.9rem;
        opacity: 0.9;
      }
    }

    .btn {
      .addUserBtn {
        padding: 0.7rem 1.8rem;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
        outline: none;
        cursor: pointer;
        border-radius: 8px;
        border: none;
        color: #fff;
        box-shadow:
          rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
          rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
        &:hover {
          opacity: 0.85;
        }
      }
    }
  }

  .userListTableContainer {
    margin: 2.5rem 0;
    .tableC {
      border-radius: 0.75rem;

      th,
      td {
        font-size: 0.85rem;
      }
      th {
        font-size: 0.72rem;
        text-transform: uppercase;
        opacity: 0.8;
        font-weight: 700;
      }

      .userNameImgCell {
        display: flex;
        align-items: center;
        .avatar {
          margin-right: 12px;
          width: 35px;
          height: 35px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 8px;
          }
        }
      }
    }
  }
}

.modal {
  .labelFields {
    margin: 0.3rem 0.5rem;
    label {
      font-size: 1.1rem;
      font-weight: 500;
    }
    .MuiInputBase-root {
      width: 100%;
    }

    .MuiAutocomplete-input {
      font-size: 0.9rem;
      opacity: 0.8;
    }
  }

  .buttons {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .btn {
      padding: 0.68rem 1.6rem;
      font-size: 0.8rem;
      font-weight: bold;
      text-transform: uppercase;
      outline: none;
      cursor: pointer;
      border-radius: 8px;
      border: none;
      box-shadow:
        rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
        rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
      &:hover {
        opacity: 0.85;
      }
    }
    .closeBtn {
      background-color: #e9ecef;
      color: #344767;
    }
    .createBtn {
      // color: #fff;
      margin-left: 1rem;
    }
  }
}
