import { StyleSheet, Text, View } from "@react-pdf/renderer";
import PropTypes from "prop-types";

MyTable.propTypes = {
  data: PropTypes.arrayOf(PropTypes.object.isRequired).isRequired,
  labelKeys: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ).isRequired,
};

const styles = StyleSheet.create({
  row: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    fontSize: 6,
    borderBottomColor: "#f0f0f0",
    borderBottomWidth: 1,
    paddingVertical: 2,
    // fontFamily: family,
  },
  headerrow: {
    width: "100%",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    fontSize: 6,
    backgroundColor: "#f0f0f0",
    // fontFamily: family,
    color: "#524746",
    fontWeight: 600,
    textTransform: "capitalize",
  },
  description: {
    minWidth: "5%",
    flex: 1,
    padding: 6,
    paddingRight: 15,
    paddingHorizontal: 10,
    textAlign: "left",
    // fontFamily: family,
    textTransform: "capitalize",
  },
  xyz: {
    // width: ,
    minWidth: "5%",
    flex: 1,
    padding: 6,
    // paddingRight: 15,
    // fontFamily: family,
    textTransform: "capitalize",
  },
  xyzF: {
    // width: ,
    minWidth: "5%",
    flex: 1,
    padding: 6,
    paddingRight: 15,
    fontWeight: 500,
    color: "#6B120A",
    // fontFamily: family,
  },
  tableContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    fontSize: 6,
    flex: 1,
    width: "100%",
    // fontFamily: family,
  },
});

/**
 * Custom table component for pdf generation.
 * @param {Object[]} data - The data to be rendered in tabular format.
 * @param {Object[]} labelKeys - The labels to be used as headers ( value should be an array of object {key:"Key",label:"LABEL"}) .
 * @returns {Object} - The JSX component.
 */
export function MyTable({
  data,
  labelKeys = [{ key: "key", label: "label" }],
}) {
  const SL_NO_LABEL = "Sl. No.";
  const LABEL_PREFIX = "label";
  const ROW_PREFIX = "row";

  return (
    <View style={styles.tableContainer}>
      <View wrap={false} style={styles.headerrow}>
        <Text
          style={{
            ...styles.xyz,
            paddingRight: 1,
            minWidth: "1%",
            maxWidth: "5%",
          }}
        >
          {SL_NO_LABEL}
        </Text>
        {labelKeys.map((d, idx) => (
          <Text key={`${LABEL_PREFIX}${idx}`} style={styles.xyz}>
            {d.label}
          </Text>
        ))}
      </View>
      {data.map((d, idx) => {
        return (
          <View wrap={false} style={styles.row} key={idx}>
            <Text
              style={{
                ...styles.xyz,
                paddingRight: 1,
                minWidth: "1%",
                maxWidth: "5%",
              }}
            >
              {idx + 1}
            </Text>
            {labelKeys.map((lk, i) => (
              <Text
                wrap={false}
                key={`${ROW_PREFIX}${idx}-${i}`}
                style={styles.xyz}
              >
                {typeof d[lk.key] === String ? d[lk.key].split("") : d[lk.key]}
              </Text>
            ))}
          </View>
        );
      })}
    </View>
  );
}
