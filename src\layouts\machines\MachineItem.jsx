import {
  Dialog,
  DialogTitle,
  IconButton,
  DialogContent,
  <PERSON>alogActions,
  <PERSON>ton,
  Card,
  Typography,
  Box,
} from "@mui/material";
import { useAuth } from "../../hooks/AuthProvider";

import React, { useState } from "react";
import { Link } from "react-router-dom";
import DeleteConfirmModal from "../../components/DeleteConfirmModal/DeleteConfirmModal";
import { companies, companyId_constant, machines } from "../../constants/data";
import { db } from "../../firebase";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import EditMachine from "./EditMachine";
import { useMaintenanceInfoSeter } from "../../context/MaintenanceContext";
import { useStateContext } from "../../context/ContextProvider";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import CloneMain from "../CloneMachine/CloneMain";
import { ButtonWithVisibilityIcon } from "../../components/buttons/Buttons";
import { themeColors } from "../../infrastructure/theme";
import { deleteDocument } from "../../utils/firebase-utils";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { makeStyles } from "@mui/styles";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { useDeleteMachineCfr } from "../../hooks/cfr/machineCfrProvider";
import { useMachinesSetter } from "../../services3/machines/MachineContext2";
import TimelineComponent from "./TimelineView";
import { useUtils } from "../../hooks/UtilsProvider";
import { useCheckAccess } from "../../utils/useCheckAccess";
import SettingsIcon from "@mui/icons-material/Settings";
import userRoles from "../../constants/UserRole";
import { checkUserRole } from "../../utils/roleUtils";

const useStyles = makeStyles((theme) => ({
  machineCard: {
    backgroundColor: theme.palette.custom.backgroundThird,
    color: theme.palette.custom.textColor,
    padding: "1.5rem",
    width: "100%", // Use 100% width to ensure it fits within its container
    maxWidth: "20rem", // Set a maximum width to prevent the card from becoming too wide
    minWidth: "15rem", // Set a minimum width to ensure it doesn't get too narrow
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    gap: "0.5rem", // Add gap between child elements
  },
  description: {
    minHeight: "3rem", // Ensure the description has a minimum height
    overflow: "hidden", // Hide overflow
    textOverflow: "ellipsis", // Add ellipsis for overflow text
    display: "-webkit-box",
    WebkitLineClamp: 2, // Limit to 2 lines
    WebkitBoxOrient: "vertical",
  },
  actions: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
}));

const MachineItem = ({ machine, useAt, onClickEdit, oee, onClickSettings }) => {
  console.log("Machine Item", machine);
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const { currentMode } = useStateContext();
  const [openClone, setOpenClone] = useState(false);
  const maintenanceInfoSetterFromContext = useMaintenanceInfoSeter();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const classes = useStyles();
  const deletemachinecfr = useDeleteMachineCfr();
  const { currentUser } = useAuth();
  const handlemachines = useMachinesSetter();
  const { envData, isFetching } = useUtils();

  const isPlanner = checkUserRole(currentUser, envData, "planner", isFetching);

  console.log("isPlanner", isPlanner);

  const deleteData = async () => {
    const date = new Date();
    const data = {
      activity: "machine deleted",
      dateTime: date,

      description: "a machine is deleted",
      machine: machine._id,
      module: "Machine",
      username: currentUser.username,
    };
    await axios
      .delete(`${dbConfig.url}/machines/${machine._id}`)
      .then(() => {
        toastMessageSuccess({ message: "Deleted Successfully!" });
        deletemachinecfr(data);
        handlemachines();
      })
      .then(() => {
        //setRefreshCount(refreshCount + 1);
        setOpenDel(false);
      })
      .catch((err) => {
        toastMessage({ message: err.message });
      });
  };

  return (
    <Card variant="outlined" className={classes.machineCard}>
      <Box
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
        }}
        id="22"
      >
        <Box style={{ display: "flex", gap: ".5rem" }}>
          <Typography variant="subtitle2">Block: </Typography>
          <Typography variant="subtitle2"> {machine.block} </Typography>
        </Box>
        <Box style={{ display: "flex", gap: ".5rem" }}>
          <Typography variant="subtitle2">Model: </Typography>
          <Typography variant="subtitle2"> {machine.model} </Typography>
        </Box>
      </Box>
      <Box style={{ display: "flex", gap: ".5rem" }}>
        <Typography variant="subtitle2">EQP ID:</Typography>
        <Typography variant="subtitle2"> {machine.equipment_id} </Typography>
      </Box>
      <Box style={{ paddingBlock: "1rem" }}>
        <Typography variant="h6">
          {machine.title?.slice(0, 15)}{" "}
          {machine.title.length > 15 ? "..." : null}
        </Typography>
        <Typography className={classes.description} variant="subtitle2">
          {machine.desc}
        </Typography>
      </Box>
      <Box className={classes.actions}>
        <Link
          to={`/maintenance/${machine._id}`}
          onClick={() => maintenanceInfoSetterFromContext()}
        >
          <Button startIcon={<VisibilityIcon />} variant="contained">
            View
          </Button>
        </Link>
        <Box>
          <IconButton
            onClick={() => setOpenEdit(true)}
            disabled={!useCheckAccess("machines", "PUT")}
            sx={{
              color: !useCheckAccess("machines", "PUT")
                ? "grey.500"
                : "primary.main",
            }}
          >
            <EditIcon style={{ fontSize: "22px" }} />
          </IconButton>

          <IconButton
            onClick={() => setOpenDel(true)}
            sx={{
              marginLeft: "8px",
              color: !useCheckAccess("machines", "DELETE")
                ? "grey.500"
                : "#f00",
            }}
            disabled={!useCheckAccess("machines", "DELETE")}
          >
            <DeleteIcon style={{ fontSize: "22px" }} />
          </IconButton>

          <IconButton
            color="primary"
            onClick={() => onClickSettings(machine)}
            disabled={!isPlanner}
            title="Manage Child Data"
          >
            <SettingsIcon />
          </IconButton>
        </Box>
      </Box>
      <Dialog open={openClone}>
        <DialogTitle>Clone Machine</DialogTitle>
        <DialogContent>
          <CloneMain machineDetails={machine} />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenClone(false)}
            variant="contained"
            style={{ backgroundColor: "red", color: "white" }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <DeleteConfirmModal
        handleClose={() => setOpenDel(false)}
        onDelete={deleteData}
        open={openDel}
        type={"Machine"}
        name={machine.title}
      />
      <EditMachine
        handleClose={() => setOpenEdit(false)}
        mid={machine.mid}
        data={machine}
        open={openEdit}
      />
    </Card>
  );
};

export default MachineItem;
