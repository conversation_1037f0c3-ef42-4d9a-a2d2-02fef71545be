import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from "@mui/material";
import { themeColors } from "../../infrastructure/theme";
import { dbConfig } from "../../infrastructure/db/db-config";
import { ButtonBasicCancel } from "../../components/buttons/Buttons";

const SopPreviewDialog = ({ open, onClose, sopUrl, title, currentMode }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="lg"
      PaperProps={{
        style: {
          backgroundColor:
            currentMode === "Dark" ? themeColors.dark.secondary : "white",
        },
      }}
    >
      <DialogTitle>SOP Preview - {title}</DialogTitle>
      <DialogContent>
        {sopUrl ? (
          <iframe
            src={`${dbConfig.url_storage}/${sopUrl}`}
            style={{ width: "100%", height: "80vh", border: "none" }}
            title="SOP PDF Preview"
          />
        ) : (
          <p>No SOP document available.</p>
        )}
      </DialogContent>
      <DialogActions>
        <ButtonBasicCancel
          type="button"
          buttonTitle="Close"
          onClick={onClose}
        ></ButtonBasicCancel>
      </DialogActions>
    </Dialog>
  );
};

export default SopPreviewDialog;
