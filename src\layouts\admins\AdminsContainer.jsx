import React, { useEffect, useState } from "react";
//import CompanyCard from './CompanyCard';
import { db, auth } from "../../firebase"; // Replace 'storage' with 'auth' or correct export
import { firebaseLooper } from "../../tools/tool";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { DropzoneArea } from "material-ui-dropzone";
import { useStorage } from "../../hooks/useStorage";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { alluser, companies } from "../../constants/data";
import Typography from "@mui/material/Typography";
import Pagination from "@mui/material/Pagination";
import Stack from "@mui/material/Stack";
//import { useCompanyId, useCompanyIdSeter } from '../../context/CompanyIdContext';
import { useNavigate } from "react-router-dom";
import AdminCard from "./AdminCard";
import AdminCardHeader from "./AdminCardHeader";
import { useStateContext } from "../../context/ContextProvider";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";
import { TableBody, TableContainer, Paper } from "@mui/material";

const AdminsContainer = () => {
  const [adminAll, setAllAdminAll] = useState([]);
  const [adminsInView, setAdminsInView] = useState([]);
  const [adminsInViewContainerCount, setAdminsInViewContainerCount] =
    useState(0); // number of pages
  const [open, setOpen] = useState(false);

  const [firstName, setFirstName] = useState();
  const [lastName, setLastName] = useState();
  const [adminName, setAdminName] = useState();
  const [email, setEmail] = useState();
  const [phone, setPhone] = useState();
  const [address, setAddress] = useState();
  const [file, setFile] = useState();

  const [imageUrl, setImageUrl] = useState();
  const [newImageSelected, setNewImageSelected] = useState(false); //
  const [docId, setDocId] = useState();
  const [page, setPage] = useState(1);
  const history = useNavigate();
  const { currentColorLight, currentMode } = useStateContext();

  //const companyIdSetterFun = useCompanyIdSeter();

  useEffect(() => {
    // db.collection(alluser)
    // .where("lsi", '==', true)
    //   .onSnapshot(snap => {
    //     const adminsData = firebaseLooper(snap)
    //     // console.log("all Admin:", adminAll);
    //     setAllAdminAll(adminsData);
    //     setAdminsInView(adminsData.slice(0, 6));
    //     adminsPageInfoContainerCounter(adminsData.length)
    //   })
  }, []);

  //
  const adminsPageInfoContainerCounter = (admins) => {
    var temp = Math.ceil(admins / 6);
    setAdminsInViewContainerCount(temp);
  };
  //
  const handlePageChange = (event, value) => {
    setPage(value);
    if (value == adminsInViewContainerCount) {
      setAdminsInView(adminAll.slice((value - 1) * 6));
    } else {
      setAdminsInView(adminAll.slice((value - 1) * 6, value * 6));
    }
  };
  //

  // const handleCardView = (data)=>{
  // companyIdSetterFun(data.id);
  // history.replace('/')
  // window.location.reload(false);
  // }

  //
  const handleCardEdit = (data) => {
    setFirstName(data.firstName);
    setLastName(data.lastName);
    setEmail(data.email);
    setPhone(data.phone);
    setAddress(data.address);
    setAdminName(data.username);

    setImageUrl(data.url);
    setDocId(data.id);
    setOpen(true);
    setFile("");
  };

  const handleClose = () => {
    setOpen(false);
  };
  //
  const handleChange = (loadedFiles) => {
    let selectedFile = loadedFiles[0];
    if (selectedFile) {
      setFile(selectedFile);

      setNewImageSelected(true);
    } else {
      setNewImageSelected(false);
    }
  };

  const { progress, url } = useStorage(file, "companies_dp");

  //
  const handleSave = () => {
    if (
      firstName &&
      lastName &&
      adminName &&
      file &&
      url &&
      email &&
      address &&
      phone
    ) {
      // db.collection(alluser).doc(docId).update({
      //  firstName:firstName,
      //  lastName:lastName,
      //  phone:phone,
      //  email:email,
      //  address:address,
      //  username:adminName,
      //   url: newImageSelected ? url : imageUrl,
      // })
      //   .then(() => {
      //     setOpen(false);
      //     toastMessageSuccess({ message: "successfully updated", type: "success" });
      //   }
      //   )
    } else {
      // toastMessage({ message: 'Missing field' })
    }
  };
  //To delete the file if selected i.e stored in the firebase storage
  const handleCancel = () => {
    if (newImageSelected) {
      var fileRef = auth.refFromURL(url); // Replace 'storage' with 'auth' or correct export
      fileRef
        .delete()
        .then(function () {
          // console.log("File Deleted");
          setOpen(false);
        })
        .catch(function (error) {
          console.log("file cancel:", error);
        });
    } else {
      setOpen(false);
    }
  };

  return (
    <div
      className="adminsContainerSection"
      style={
        currentMode === "Dark"
          ? { backgroundColor: "#161C24", color: "white" }
          : { backgroundColor: currentColorLight }
      }
    >
      <div className="adminsHeading">
        <div className="info">
          <h3>Admins</h3>
          {/* <p>List of all the active Companies.</p> */}
        </div>
      </div>

      <div className="cardsContainer">
        <TableContainer
          component={Paper}
          className="tableC"
          style={
            currentMode === "Dark"
              ? { border: "1px solid white" }
              : { border: "1px solid black" }
          }
        >
          <AdminCardHeader />
          {adminsInView
            ? adminsInView.map((data) => (
                <TableBody>
                  <AdminCard
                    image={data.url}
                    fullName={data.firstName + " " + data.lastName}
                    userName={data.username}
                    phone={data.phone}
                    email={data.email}
                    onClick={() => handleCardEdit(data)}
                  />
                </TableBody>
              ))
            : null}
        </TableContainer>
      </div>

      <div className="flex justify-center">
        <Stack spacing={2}>
          <Pagination
            count={adminsInViewContainerCount}
            page={page}
            onChange={handlePageChange}
          />
        </Stack>
      </div>

      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Edit</DialogTitle>
        <DialogContent>
          <TextField
            margin="dense"
            id="firstName"
            label="First Name"
            type="text"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            fullWidth
            variant="standard"
          />
          <TextField
            margin="dense"
            id="lastName"
            label="Last Name"
            type="text"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            fullWidth
            variant="standard"
          />
          <TextField
            margin="dense"
            id="email"
            label="Email"
            type="text"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            fullWidth
            variant="standard"
          />
          <TextField
            type="number"
            margin="dense"
            id="phone"
            label="Phone"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            fullWidth
            variant="standard"
          />
          <TextField
            margin="dense"
            id="address"
            label="Address"
            type="text"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            fullWidth
            variant="standard"
          />
          <TextField
            margin="dense"
            id="adminName"
            label="Admin Name"
            type="text"
            value={adminName}
            onChange={(e) => setAdminName(e.target.value)}
            fullWidth
            variant="standard"
          />
          <div className="text-gray-500 ">Image Section</div>
          <div className="flex justify-between bg-gray-300 p-2 rounded-sm mt-1">
            <div className=" w-1/2 justify-center justify-items-center items-center flex flex-col">
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) => handleChange(loadedFiles)}
                dropzoneText="Drag and Drop / Click to ADD Media"
                showAlerts={false}
                filesLimit={1}
              />

              {
                <div className="text-2xl text-gray-700 flex justify-end">
                  {file ? <p> {progress} % Uploaded</p> : null}
                </div>
              }
            </div>
            <img
              src={imageUrl}
              width="auto"
              className="max-w-xs max-h-40 self-center"
            ></img>
          </div>
        </DialogContent>
        <DialogActions>
          <ButtonBasicCancel
            buttonTitle="Cancel"
            onClick={() => handleCancel()}
          />
          <ButtonBasic buttonTitle="Update" onClick={() => handleSave()} />
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default AdminsContainer;
