import React from "react";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
);

const options = {
  indexAxis: "y",
  elements: {
    bar: {
      borderWidth: 2,
    },
  },
  responsive: true,
  plugins: {
    legend: {
      position: "right",
    },
    title: {
      display: true,
      text: "Uptime/Downtime Chart",
    },
  },
  scales: {
    x: {
      title: {
        display: true,
        text: "Minutes",
      },
    },
    y: {
      title: {
        display: true,
        text: "Date",
      },
    },
  },
};

// Sample static data for downtime and uptime in minutes
const data = {
  labels: [
    "2024-01-01",
    "2024-01-02",
    "2024-01-03",
    "2024-01-04",
    "2024-01-05",
  ],
  datasets: [
    {
      label: "Downtime",
      data: [30, 45, 20, 15, 60], // Downtime values in minutes
      borderColor: "rgb(255, 0, 0)",
      backgroundColor: "rgba(255, 0, 0, 0.5)",
    },
    {
      label: "Uptime",
      data: [240, 300, 360, 420, 480], // Uptime values in minutes
      borderColor: "rgb(0, 128, 0)",
      backgroundColor: "rgba(0, 128, 0, 0.5)",
    },
  ],
};

const UptimeDowntimeChart = () => {
  return <Bar options={options} data={data} />;
};

export default UptimeDowntimeChart;
