/* eslint-disable no-lone-blocks */
import {
  Badge,
  Box,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Tooltip,
  Typography,
  Button,
} from "@mui/material";
import React, { useState } from "react";
import { useEffect } from "react";
import AddLiveData from "../components/LiveData/AddLiveData";
import LiveDataHeader from "../components/LiveData/LiveDataHeader";
import { companies, companyId_constant } from "../constants/data";
import { useStateContext, hexToHSL } from "../context/ContextProvider";
import { db } from "../firebase";
import firebase from "firebase";
import LivedataNewItem from "../layouts/machineData/LivedataNewItem";
import MachineDataHeader from "../layouts/machineData/MachineDataHeader";
import { firebaseLooper } from "../tools/tool";
import "../layouts/machineData/machineData.scss";
import { useParams } from "react-router-dom";
import CloseIcon from "@mui/icons-material/Close";
import AnnotationChartNew from "../layouts/machineData/AnnotationChartNew";
import { ButtonBasic } from "../components/buttons/Buttons";
import { themeColors } from "../infrastructure/theme";
import { themeColors as TC } from "../constants/dummy";
import { BsCheck } from "react-icons/bs";
import { toastMessage, toastMessageWarning } from "../tools/toast";
import axios from "axios";
import { dbConfig } from "../infrastructure/db/db-config";
import { useMongoRefresh } from "../services/mongo-refresh.context";
import { sharedCss } from "../styles/sharedCss";
import { SearchOutlined } from "@mui/icons-material";
import moment from "moment";
import { v4 as uuid } from "uuid";
import TableHeader from "../layouts/machineData/TableHeader";
import NoDataComponent from "../components/commons/noData.component";
import CommonDropDownComponent from "../components/commons/dropDown.component";
import { useCommonOuterContainerStyle } from "../styles/useCommonOuterContainerStyle";
import NotAccessible from "../components/not-accessible/not-accessible";
import { useCheckAccess } from "../utils/useCheckAccess";
import TablePagination from "@mui/material/TablePagination";

const LiveDataProject = () => {
  const [liveData, setLiveData] = useState([]);
  const [open, setOpen] = useState(false);
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const { mid } = useParams();

  const [processValues, setProcessValues] = useState([]);
  const [activeChartsArrayFromSelected, setActiveChartsArrayFromSelected] =
    useState([]); // new implementation
  const [showGraphNew, setShowGraphNew] = useState(false);
  const [searchChartKeyword, setSearchChartKeyword] = useState("");
  const [dataLoading, setDataLoading] = useState(true);
  const [curColor, setCurColor] = useState(currentColorLight);
  const [indToUpdate, setIndex] = useState(-1);
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const [liveStatus, setLiveStatus] = useState([]);
  const [showLiveStatus, setShowLiveStatus] = useState(false);
  const commonCss = sharedCss();
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const paginatedLiveData = React.useMemo(() => {
    const startIndex = page * rowsPerPage;
    return liveData.slice(startIndex, startIndex + rowsPerPage);
  }, [liveData, page, rowsPerPage]);

  const paginatedProcessValues = React.useMemo(() => {
    const startIndex = page * rowsPerPage;
    return processValues.slice(startIndex, startIndex + rowsPerPage);
  }, [processValues, page, rowsPerPage]);

  const paginatedLiveStatus = React.useMemo(() => {
    const startIndex = page * rowsPerPage;
    return liveStatus.slice(startIndex, startIndex + rowsPerPage);
  }, [liveStatus, page, rowsPerPage]);

  const hasImageAnnotationGETAccess = useCheckAccess(
    "imageAnnotationModules",
    "GET",
  );
  const hasStatusGETAccess = useCheckAccess("live_status", "GET");
  const hasSensorGETAccess = useCheckAccess("liveData", "GET");
  const fetchLiveData = async () => {
    await axios
      .get(`${dbConfig.url}/imageAnnotationModules`)
      .then((response) => {
        const data = response.data?.data;
        console.log("dataImage", data);
        const sortedData = data.filter((data) => data.mid == mid);
        setLiveData(sortedData);
      })
      .catch((error) => {
        toastMessage({ message: error.message });
      });
  };

  const fetchLiveData2SensorValue = async () => {
    await axios
      .get(`${dbConfig.url}/livedata/latest/${mid}`)
      .then((response) => {
        const data = response.data?.data;
        setProcessValues(data); // Only the latest value
        setDataLoading(false);
      })
      .catch((error) => {
        toastMessage({ message: error.message });
        setDataLoading(false);
      });
  };

  useEffect(() => {
    fetchLiveData();
    fetchLiveData2SensorValue();

    axios.get(`${dbConfig.url}/live_status`).then((response) => {
      const data = response.data?.data;
      const sortedData = data?.filter((data) => data?.mid == mid);
      setDataLoading(false);
      setLiveStatus(sortedData);
      console.log("data 2", sortedData);
    });
  }, [refreshCount]);

  // Poll livedata every second (for chart)
  useEffect(() => {
    const fetchSensorValues = () => {
      fetchLiveData2SensorValue();
    };
    fetchSensorValues();
    const intervalId = setInterval(fetchSensorValues, 1000); // 1 second
    return () => clearInterval(intervalId);
  }, [mid]); // re-run if mid changes

  // Poll imageAnnotationModules every 30 seconds (or only on mount)
  useEffect(() => {
    fetchLiveData();
    const intervalId = setInterval(fetchLiveData, 30000); // 30 seconds
    return () => clearInterval(intervalId);
  }, [mid]);

  // Poll live_status every 30 seconds (or only on mount)
  useEffect(() => {
    const fetchStatus = () => {
      axios.get(`${dbConfig.url}/live_status`).then((response) => {
        const data = response.data?.data;
        const sortedData = data?.filter((data) => data?.mid == mid);
        setDataLoading(false);
        setLiveStatus(sortedData);
      });
    };
    fetchStatus();
    const intervalId = setInterval(fetchStatus, 30000); // 30 seconds
    return () => clearInterval(intervalId);
  }, [mid]);

  //
  const handleCheckBox = ({ e, pData }) => {
    console.log("pData", pData);
    if (e.target.checked) {
      setActiveChartsArrayFromSelected([
        ...activeChartsArrayFromSelected,
        e.target.id,
      ]); // id is title
      console.log("title and  event if", e.target.checked, ":", [
        ...activeChartsArrayFromSelected,
        e.target.id,
        pData,
      ]);
    } else {
      let temp = [...activeChartsArrayFromSelected];
      let temp2 = temp.filter((data) => data != e.target.id);
      setActiveChartsArrayFromSelected([...temp2]);
      //console.log("title and  event else", e.target.checked , ":" , [...temp2])
    }
  };

  return (
    <section>
      <Box>
        <MachineDataHeader />
        {/* Header */}
        <div className={commonCss.innerSection}>
          <div className={`${commonCss.sectionContainer} border-radius-outer`}>
            <Box>
              <LiveDataHeader setOpen={setOpen} />
            </Box>

            {/* Cards with project name and details */}
            <Box>
              <div>
                <TableContainer
                  component={Paper}
                  className="table border-radius-inner"
                  sx={commonOuterContainerStyle}
                >
                  {hasImageAnnotationGETAccess ? (
                    <Table sx={{ minWidth: 650 }}>
                      <TableHeader
                        currentMode={currentMode}
                        columns={[
                          { label: "Name", align: "left" },
                          { label: "Description", align: "left" },
                          { label: "Type", align: "center" },
                          { label: "Actions", align: "center" },
                        ]}
                      />
                      <TableBody>
                        {paginatedLiveData.length > 0 ? (
                          paginatedLiveData.map((item, idx) => (
                            <LivedataNewItem
                              key={item.id}
                              data={item}
                              lastItem={idx === paginatedLiveData.length - 1}
                            />
                          ))
                        ) : (
                          <>
                            <NoDataComponent cellColSpan={4} />
                          </>
                        )}
                      </TableBody>
                    </Table>
                  ) : (
                    <NotAccessible />
                  )}
                  
                </TableContainer>
                {liveData.length > 0 && (
                    <TablePagination
                      component="div"
                      count={liveData.length}
                      rowsPerPage={rowsPerPage}
                      page={page}
                      onPageChange={handleChangePage}
                      onRowsPerPageChange={handleChangeRowsPerPage}
                      className={commonCss.tablePagination}
                    />
                  )}
              </div>
            </Box>
          </div>
          <br />
          {/* ////////// */}
          <div className={`${commonCss.sectionContainer} ${commonCss.backgroundLight} border-radius-outer`}>
            <div className={commonCss.tableLable}>
              <Typography sx={{ ml: 2 }} fontWeight="bold" variant="h6">
                Charts 
              </Typography>
              <div
              className={commonCss.tableRightContent}
              style={{ display: "flex", alignItems: "center", gap: "1 rem" }}
            >
           
              <div style={{ display: "flex" }}>
                <CommonDropDownComponent
                  className={`${commonCss.dropDown} ${commonCss.inputAlignmentFix}`}
                  dropDownContainerStyle={{ minWidth: "220px" }} // ✅ Only for this dropdown
                  menuValue={!showLiveStatus ? "Sensors" : "Status"}
                  dropDownLabel={"Select chart data type"}
                  menuData={[
                    { _id: "false", label: "Sensors", value: "Sensors" },
                    { _id: "true", label: "Status", value: "Status" },
                  ]}
                  menuItemDisplay={"label"}
                  menuItemValue={"value"}
                  handleChange={(e) => {
                    setShowLiveStatus(e.target.value !== "Sensors");
                    e.target.value === "Status" &&
                      setActiveChartsArrayFromSelected([]);
                  }}
                />
              </div>
              <div>
                <Button
                  disabled={
                    showLiveStatus || !activeChartsArrayFromSelected.length
                  }
                  onClick={() => setShowGraphNew(true)}
                  variant="contained"
                >
                  Show
                </Button>
                </div>
              </div>
            </div>
            <div>
              <TableContainer
                component={Paper}
                className="table border-radius-inner"
                sx={commonOuterContainerStyle}
              >
                {showLiveStatus ? (
                  hasStatusGETAccess ? (
                    <Table sx={{ minWidth: 650 }}>
                      <TableHeader
                        currentMode={currentMode}
                        columns={[
                          { label: "Title", align: "left" },
                          { label: "Value", align: "left" },
                          { label: "Status", align: "center" },
                        ]}
                      />
                      <TableBody>
                        {paginatedLiveStatus?.length > 0 ? (
                          paginatedLiveStatus.map((pData, index) => {
                            const backendTime = moment(pData?.time); // This will work as-is for ISO format
                            const currentTime = moment();
                            const isMoreThan30SecondsBehind =
                              currentTime.diff(backendTime, "seconds") > 30;
                            const lastItem = paginatedLiveStatus?.length - 1 === index;

                            return (
                              <TableRow
                                key={index}
                                hover
                                sx={{
                                  "&:last-child td, &:last-child th": {
                                    border: 0,
                                  },
                                  "&:hover": { bgcolor: "#f5f5f5" },
                                  borderBottom: lastItem
                                    ? "none"
                                    : "0.05rem solid #e0e0e0",
                                }}
                              >
                                <TableCell align="left">{pData?.tag}</TableCell>
                                <TableCell align="left">
                                  {pData.value}
                                </TableCell>
                                <TableCell align="center">
                                  {isMoreThan30SecondsBehind ? (
                                    <Badge color="warning" variant="dot" />
                                  ) : (
                                    <Badge color="success" variant="dot" />
                                  )}
                                </TableCell>
                              </TableRow>
                            );
                          })
                        ) : (
                          <>
                            <NoDataComponent
                              cellColSpan={3}
                              dataLoading={dataLoading}
                              animatePulseNoData={true}
                            />
                          </>
                        )}
                      </TableBody>
                    </Table>
                  ) : (
                    <NotAccessible />
                  )
                ) : hasSensorGETAccess ? (
                  <Table sx={{ minWidth: 650 }}>
                    <TableHeader
                      currentMode={currentMode}
                      columns={[
                        { label: "Title", align: "left" },
                        { label: "Current Value", align: "left" },
                        { label: "Status", align: "center" },
                        { label: "Selection", align: "center" },
                      ]}
                    />
                    <TableBody>
                      {paginatedProcessValues?.length > 0 ? (
                        paginatedProcessValues.map((pData, index) => {
                          const backendTime = moment(pData?.time); // This will work as-is for ISO format
                          const currentTime = moment();
                          const isMoreThan30SecondsBehind =
                            currentTime.diff(backendTime, "seconds") > 30;
                          const lastItem = paginatedProcessValues?.length - 1 === index;

                          return (
                            <TableRow
                              key={index}
                              hover
                              sx={{
                                "&:last-child td, &:last-child th": {
                                  border: 0,
                                },
                                "&:hover": { bgcolor: "#f5f5f5" },
                                borderBottom: lastItem
                                  ? "none"
                                  : "0.05rem solid #e0e0e0",
                              }}
                            >
                              <TableCell align="left">{pData?.tag}</TableCell>
                              <TableCell align="left">
                                {parseInt(pData.value)?.toFixed(2)}{" "}
                                {pData?.unit}
                              </TableCell>
                              <TableCell align="center">
                                {isMoreThan30SecondsBehind ? (
                                  <Badge color="warning" variant="dot" />
                                ) : (
                                  <Badge color="success" variant="dot" />
                                )}
                              </TableCell>
                              <TableCell align="center">
                                <div className="flex items-center justify-center">
                                  <input
                                    className="w-4 h-4"
                                    type="checkbox"
                                    id={pData._id}
                                    style={{ accentColor: currentColor }}
                                    onChange={(event) =>
                                      handleCheckBox({
                                        e: event,
                                        pData: pData?.tag,
                                      })
                                    }
                                  />
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <>
                          <NoDataComponent
                            dataLoading={dataLoading}
                            cellColSpan={4}
                            animatePulseNoData={true}
                          />
                        </>
                      )}
                    </TableBody>
                  </Table>
                ) : (
                  <NotAccessible />
                )}
                
              </TableContainer>
              {(liveStatus.length > 0 || processValues.length > 0) && (
                  <TablePagination
                    component="div"
                    count={showLiveStatus ? liveStatus.length : processValues.length}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    className={commonCss.tablePagination}
                  />
                )}
            </div>
          </div>
        </div>
        {/* // */}

        {/* Adding a new project */}
        <Dialog
          onClose={() => setOpen(false)}
          open={open}
          fullWidth
          maxWidth="md"
        >
          <DialogTitle
            style={
              currentMode === "Dark"
                ? { backgroundColor: themeColors.dark.secordary }
                : { backgroundColor: "white" }
            }
          >
            Add Live Data
          </DialogTitle>
          <DialogContent
            style={
              currentMode === "Dark"
                ? { backgroundColor: themeColors.dark.secordary }
                : { backgroundColor: "white" }
            }
          >
            <AddLiveData
              mid={mid}
              onClose={async () => {
                // Fetch latest live data without reload
                await fetchLiveData();
                await fetchLiveData2SensorValue();
                setOpen(false);
              }}
            />
          </DialogContent>
        </Dialog>

        {/* dialog for MULTI and single charts/graphs view :- */}
        <Dialog
          open={showGraphNew}
          onClose={() => setShowGraphNew(false)}
          maxWidth="lg"
        >
          <DialogContent className={commonCss.backgroundLight}>
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
              }}
            >
              <div className="font-bold text-xl">Live Data Graphs</div>
              <IconButton onClick={() => setShowGraphNew(false)}>
                <CloseIcon />
              </IconButton>
            </div>

            <div>
              <div>
                <AnnotationChartNew
                  color={curColor}
                  activeChartsArrayFromSelected={activeChartsArrayFromSelected}
                  processValues={processValues}
                />
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </Box>
    </section>
  );
};

export default LiveDataProject;

// Card for Live Data Project

{
  /* <section class="p-10">
        <div class="container">
           <div class="flex justify-evenly flex-wrap -mx-2">
            {
                liveData.map((item, idx) => (
                    <> 
                   
                     <LiveDataCard title={item.title} img={item.imgUrl} desc={item.desc} type={item.type} key={item.id + idx} id={item.id} />
                        
                    </>
                      
                    
                ))
            }
            
            </div>
        </div>
        </section> */
}
