/* eslint-disable jsx-a11y/img-redundant-alt */
/* eslint-disable jsx-a11y/anchor-is-valid */

import {
  Box,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Tooltip,
} from "@mui/material";
import React, { useState } from "react";
import { Link } from "react-router-dom";
import DeleteIcon from "@mui/icons-material/Delete";
import { ButtonBasic } from "../buttons/Buttons";
import EditIcon from "@mui/icons-material/Edit";
import InfoIcon from "@mui/icons-material/Info";
import EditLiveData from "./EditLiveData";
import Delete from "../Delete/Delete";
import { companies, companyId_constant, machines } from "../../constants/data";
import { db } from "../../firebase";
import { toastMessageSuccess } from "../../tools/toast";
import { useStateContext } from "../../context/ContextProvider";

const LiveDataCard = ({ title, desc, type, img, id }) => {
  const [openEdit, setOpenEdit] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const { currentColor, currentMode } = useStateContext();

  const onDelete = () => {
    //       db.collection(companies).doc(companyId_constant)
    //   .collection('ImageAnnotationModule')
    //   .doc(id)
    //   .delete().then(() => {
    //    toastMessageSuccess({message: 'Successfully deleted'})
    //   })
  };

  return (
    <div
      style={
        currentMode === "Dark"
          ? {
              backgroundColor: "#212B36",
              color: "white",
              padding: 1,
              maxWidth: 400,
              margin: "3px",
              maxHeight: 430,
            }
          : {
              border: "1px solid black",
              padding: 1,
              maxWidth: 400,
              margin: "3px",
              maxHeight: 430,
            }
      }
      className="w-full md:w-1/2 xl:w-1/3 px-4 overflow-hidden shadow-xl rounded-lg"
    >
      <div class=" rounded-lg overflow-hidden mb-10">
        <div style={{ border: "1px solid #F9F3EE" }}>
          <img
            src={img}
            style={{ width: 400, height: 250 }}
            alt="image"
            className="w-full "
          />
        </div>

        <div class="p-8 sm:p-9 md:p-7 xl:p-9 ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <h3>
              <a
                href="javascript:void(0)"
                class="
                    font-semibold
                    text-dark text-xl
                    sm:text-[22px]
                    md:text-xl
                    lg:text-[22px]
                    xl:text-xl
                    2xl:text-[22px]
                    mb-4
                    block
                    hover:text-primary
                    "
              >
                {title} -{" "}
                <span className="text-sm text-indigo-400 uppercase leading-relaxed mb-2">
                  {type}
                </span>
              </a>
            </h3>
            <Tooltip title={desc}>
              <IconButton>
                <InfoIcon />
              </IconButton>
            </Tooltip>
          </div>

          {/* <p class="text-base text-body-color leading-relaxed mb-7">
                 {desc}
              </p> */}

          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Link to={`/annotations/${id}`}>
              <ButtonBasic buttonTitle="View Project" />
            </Link>
            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <IconButton onClick={(e) => setOpenEdit(true)}>
                <EditIcon color="primary" />
              </IconButton>
              <IconButton onClick={() => setOpenDelete(true)}>
                <DeleteIcon />
              </IconButton>
            </Box>
          </Box>

          <Dialog
            open={openEdit}
            fullWidth
            maxWidth="md"
            onClose={() => setOpenEdit(false)}
          >
            <DialogTitle>Edit Details</DialogTitle>
            <DialogContent>
              <EditLiveData
                annData={{ title, desc, imgUrl: img, id: id, type }}
                onClose={() => setOpenEdit(false)}
              />
            </DialogContent>
          </Dialog>

          <Dialog open={openDelete} onClose={() => setOpenDelete(false)}>
            <Delete onClose={() => setOpenDelete(false)} onDelete={onDelete} />
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default LiveDataCard;
