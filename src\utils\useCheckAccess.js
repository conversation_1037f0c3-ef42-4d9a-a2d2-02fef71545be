import { useAuth } from "../hooks/AuthProvider";
import { useUtils } from "../hooks/UtilsProvider";

export const useCheckAccess = (module, request) => {
  const { envData, isFetching } = useUtils();
  const { currentUser } = useAuth();

  // While fetching, deny access
  if (isFetching) return false;

  // After fetching, ensure envData and rules are loaded
  if (!envData || typeof envData !== "object" || !envData.rules) return false;

  // Get role-based permissions safely
  const role = currentUser?.role;
  const moduleRules = envData.rules[module] || {};
  const permissions = moduleRules[role] || [];

  return permissions.includes(request);
};
