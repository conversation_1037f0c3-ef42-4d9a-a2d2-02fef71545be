import { useState } from "react";
import { TableRow, TableCell } from "@mui/material";
import moment from "moment";
import PropTypes from "prop-types";

CfrRow.propTypes = {
  row: PropTypes.shape({
    timestamp: PropTypes.string.isRequired, // Assuming `timestamp` is a string
    module: PropTypes.string, // Optional string
    activity: PropTypes.string, // Optional string
    description: PropTypes.string, // Optional string
    user_name: PropTypes.string, // Optional string
    email: PropTypes.string, // Optional string
    role: PropTypes.string, // Optional string
  }).isRequired,
};

// UI Constants
const DEFAULT_VALUE = "N/A";
const SHOW_MORE_TEXT = "show more";
const SHOW_LESS_TEXT = "show less";
const LINK_COLOR = "blue";

export default function CfrRow({ row }) {
  const [showMore, setShowMore] = useState(false);

  return (
    <TableRow>
      <TableCell align="center">
        {moment(row?.timestamp)?.format("DD/MM/YYYY HH:mm")}
      </TableCell>
      <TableCell align="left">{row?.module ?? DEFAULT_VALUE}</TableCell>
      <TableCell align="left">{row?.activity ?? DEFAULT_VALUE}</TableCell>
      <TableCell align="left">
        {row.description !== undefined && row.description !== null ? (
          <>
            {showMore
              ? row?.description?.split("\n").map((substr) => (
                  <>
                    {substr}
                    <br />
                  </>
                ))
              : row?.description?.split("\n")[0]}
            {row?.description?.split("\n").length > 1 ? (
              <span
                style={{ cursor: "pointer", color: LINK_COLOR }}
                onClick={() => setShowMore(!showMore)}
              >{`${showMore ? SHOW_LESS_TEXT : SHOW_MORE_TEXT}`}</span>
            ) : (
              ""
            )}
          </>
        ) : (
          DEFAULT_VALUE
        )}
      </TableCell>
      <TableCell align="left">{row?.user_name ?? row?.email}</TableCell>
      <TableCell align="left">{row?.role}</TableCell>
    </TableRow>
  );
}
