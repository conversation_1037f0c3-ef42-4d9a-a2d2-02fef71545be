<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.png" />
    <script src="https://static.opentok.com/v2/js/opentok.min.js"></script>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <!-- <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" /> -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/favicon.png" />
    <script src='https://meet.jit.si/external_api.js'></script>
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css"
      rel="stylesheet"
    />
    <title>AR SMART</title>
    <style media="screen" type="text/css">
      .loading {
        -webkit-animation: sk-scaleout 1s infinite ease-in-out;
        animation: sk-scaleout 1s infinite ease-in-out;
        background-color: black;
        border-radius: 100%;
        height: 6em;
        width: 6em;
      }

      .container {
        align-items: center;
        background-color: white;
        display: flex;
        height: 100vh;
        justify-content: center;
        width: 100vw;
      }

      @keyframes sk-scaleout {
        0% {
          -webkit-transform: scale(0);
          transform: scale(0);
        }
        100% {
          -webkit-transform: scale(1);
          opacity: 0;
          transform: scale(1);
        }
      }
      .loader-hide {
        display: none;
      }

      .bar {
        width: 10px;
        height: 70px;
        background: #fff;
        display: inline-block;
        transform-origin: bottom center;
        border-top-right-radius: 20px;
        border-top-left-radius: 20px;
        animation: loader 1.2s linear infinite;
      }
      .bar1 {
        animation-delay: 0.1s;
      }
      .bar2 {
        animation-delay: 0.2s;
      }
      .bar3 {
        animation-delay: 0.3s;
      }
      .bar4 {
        animation-delay: 0.4s;
      }
      .bar5 {
        animation-delay: 0.5s;
      }
      .bar6 {
        animation-delay: 0.6s;
      }
      .bar7 {
        animation-delay: 0.7s;
      }
      .bar8 {
        animation-delay: 0.8s;
      }

      @keyframes loader {
        0% {
          transform: scaleY(0.1);
          background: none;
        }
        50% {
          transform: scaleY(1);
          background: blue;
        }
        100% {
          transform: scaleY(0.1);
          background: transparent;
        }
      }
    </style>
  </head>
  <body>
    <div id="spinner" class="container">
      <div class="bar bar1"></div>
      <div class="bar bar2"></div>
      <div class="bar bar3"></div>
      <div class="bar bar4"></div>
      <div class="bar bar5"></div>
      <div class="bar bar6"></div>
      <div class="bar bar7"></div>
      <div class="bar bar8"></div>
    </div>
    <div id="root"></div>
  </body>
</html>
