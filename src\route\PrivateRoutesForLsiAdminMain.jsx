import React from "react";
import AddAdminPage from "../pages/addAdminPage";
import AddCompanyPage from "../pages/addCompanyPage";
import AdminsPage from "../pages/adminsPage";
import CompanyPage from "../pages/companyPage";
import PrivateRouteLsi from "./PrivateRoteLsi";
import AdminSidebar from "../components/adminSidebar/AdminSidebar";
import HeaderLsi from "../components/HeaderLsi/HeaderLsi";
import AddMachineFormLsi from "../layouts/addMachineForm/addMachineFormLsi";
import Account from "../pages/account";

export default function PrivateRoutesForLsiAdminMain() {
  const [inactive, setInactive] = React.useState(false);

  return (
    <>
      <div className="adminApp">
        <AdminSidebar inactive={inactive} />
        <main className={`adminMain ${inactive ? "inactive" : ""}`}>
          <HeaderLsi setInactive={setInactive} inactive={inactive} />

          <PrivateRouteLsi exact path="/admins" component={AdminsPage} />
          <PrivateRouteLsi exact path="/add-admin" component={AddAdminPage} />
          <PrivateRouteLsi exact path="/companies" component={CompanyPage} />
          <PrivateRouteLsi
            exact
            path="/add-company"
            component={AddCompanyPage}
          />
          <PrivateRouteLsi exact path="/" component={CompanyPage} />
          <PrivateRouteLsi exact path="/account" component={Account} />
          <PrivateRouteLsi
            exact
            path="/machine-cloning"
            component={AddMachineFormLsi}
          />
          {/* <PrivateRouteLsi
                exact
                path="/*"
                component={CompanyPage}
            /> */}
        </main>
      </div>
    </>
  );
}
