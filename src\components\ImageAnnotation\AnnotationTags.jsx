import { Box, IconButton, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";

import { db } from "../../firebase";
import { companies, companyId_constant } from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";
import EditAnnotation from "./EditAnnotation";

const AnnotationTags = ({
  mqttList,
  annotations,
  setAnnotations,
  mode,
  mainMid,
}) => {
  return (
    <Box sx={{ p: 2 }}>
      <Typography gutterBottom variant="h5">
        <b>Tags</b>{" "}
      </Typography>
      {/* List of tags tags.map() */}
      <hr />

      <Box>
        <Box
          sx={{
            p: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            m: 2,
          }}
        >
          <Typography sx={{ width: "170px" }}>
            <b>Annotation - </b> <br />{" "}
            <span style={{ color: "#A2B5BB" }} className="text-sm italic ">
              (Process Variable)
            </span>{" "}
          </Typography>
          <Typography>
            <b> Actual Value </b>
          </Typography>
          {!mode && (
            <Typography>
              <b>Action</b>
            </Typography>
          )}
        </Box>
        {annotations.map((item, idx) => (
          <EditAnnotation
            key={item.id + idx}
            item={item}
            mqttList={mqttList}
            mainMid={mainMid}
            annotations={annotations}
            setAnnotations={setAnnotations}
            mode={mode}
          />
        ))}
      </Box>
    </Box>
  );
};

export default AnnotationTags;
