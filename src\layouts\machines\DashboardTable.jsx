import React from "react";
import {
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
} from "@mui/material";

const DashboardTable = ({ oeeData }) => {
  // Calculate Availability in percentage
  const calculateAvailability = () => {
    const { plannedProductionTime, stopTime } = oeeData;
    const runTime = plannedProductionTime - stopTime;
    return ((runTime / plannedProductionTime) * 100).toFixed(2);
  };

  // Calculate Performance in percentage
  const calculatePerformance = () => {
    const { idealCycleTime, totalCount, runTime } = oeeData;
    const theoreticalMaxOutput = (runTime * 60) / idealCycleTime; // Convert runTime to seconds
    return ((totalCount / theoreticalMaxOutput) * 100).toFixed(2);
  };

  // Calculate Quality in percentage
  const calculateQuality = () => {
    const { goodCount, totalCount } = oeeData;
    return ((goodCount / totalCount) * 100).toFixed(2);
  };

  // Calculate Overall OEE in percentage
  const calculateOverallOEE = () => {
    const availability = parseFloat(calculateAvailability()) / 100;
    const performance = parseFloat(calculatePerformance()) / 100;
    const quality = parseFloat(calculateQuality()) / 100;
    return (availability * performance * quality * 100).toFixed(2);
  };

  return (
    <div>
      <Typography variant="h6" gutterBottom>
        Machine Performance Metrics
      </Typography>
      <Table
        sx={{ border: "1px solid rgba(224, 224, 224, 1)" }}
        variant="outlined"
      >
        <TableHead>
          <TableRow>
            <TableCell sx={{ border: "1px solid rgba(224, 224, 224, 1)" }}>
              Metric
            </TableCell>
            <TableCell sx={{ border: "1px solid rgba(224, 224, 224, 1)" }}>
              Value
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow
            sx={{ "& > *": { border: "1px solid rgba(224, 224, 224, 1)" } }}
          >
            <TableCell>Overall OEE</TableCell>
            <TableCell>{calculateOverallOEE()}%</TableCell>
          </TableRow>
          <TableRow
            sx={{ "& > *": { border: "1px solid rgba(224, 224, 224, 1)" } }}
          >
            <TableCell>Availability</TableCell>
            <TableCell>{calculateAvailability()}%</TableCell>
          </TableRow>
          <TableRow
            sx={{ "& > *": { border: "1px solid rgba(224, 224, 224, 1)" } }}
          >
            <TableCell>Planned Runtime</TableCell>
            <TableCell>
              {(oeeData?.plannedProductionTime / 60).toFixed(2)} hours
            </TableCell>
          </TableRow>
          <TableRow
            sx={{ "& > *": { border: "1px solid rgba(224, 224, 224, 1)" } }}
          >
            <TableCell>Actual Runtime</TableCell>
            <TableCell>{(oeeData?.runTime / 60).toFixed(2)} hours</TableCell>
          </TableRow>
          <TableRow
            sx={{ "& > *": { border: "1px solid rgba(224, 224, 224, 1)" } }}
          >
            <TableCell>Unplanned Downtime</TableCell>
            <TableCell>{(oeeData?.stopTime / 60).toFixed(2)} hours</TableCell>
          </TableRow>
          <TableRow
            sx={{ "& > *": { border: "1px solid rgba(224, 224, 224, 1)" } }}
          >
            <TableCell>Performance</TableCell>
            <TableCell>{calculatePerformance()}%</TableCell>
          </TableRow>
          <TableRow
            sx={{ "& > *": { border: "1px solid rgba(224, 224, 224, 1)" } }}
          >
            <TableCell>Planned Quantity</TableCell>
            <TableCell>{oeeData?.totalCount}</TableCell>
          </TableRow>
          <TableRow
            sx={{ "& > *": { border: "1px solid rgba(224, 224, 224, 1)" } }}
          >
            <TableCell>Actual Quantity</TableCell>
            <TableCell>{oeeData?.goodCount}</TableCell>
          </TableRow>
          <TableRow
            sx={{ "& > *": { border: "1px solid rgba(224, 224, 224, 1)" } }}
          >
            <TableCell>Quality</TableCell>
            <TableCell>{calculateQuality()}%</TableCell>
          </TableRow>
          <TableRow
            sx={{ "& > *": { border: "1px solid rgba(224, 224, 224, 1)" } }}
          >
            <TableCell>Rejected Quantity</TableCell>
            <TableCell>{oeeData?.totalCount - oeeData?.goodCount}</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};

export default DashboardTable;
