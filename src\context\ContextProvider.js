import React, { createContext, useContext, useState } from "react";

const StateContext = createContext();

function hexToHSL(H) {
  // Convert hex to RGB first
  let r = 0,
    g = 0,
    b = 0;
  if (H.length == 4) {
    r = "0x" + H[1] + H[1];
    g = "0x" + H[2] + H[2];
    b = "0x" + H[3] + H[3];
  } else if (H.length == 7) {
    r = "0x" + H[1] + H[2];
    g = "0x" + H[3] + H[4];
    b = "0x" + H[5] + H[6];
  }
  // Then to HSL
  r /= 255;
  g /= 255;
  b /= 255;
  let cmin = Math.min(r, g, b),
    cmax = Math.max(r, g, b),
    delta = cmax - cmin,
    h = 0,
    s = 0,
    l = 0;

  if (delta == 0) h = 0;
  else if (cmax == r) h = ((g - b) / delta) % 6;
  else if (cmax == g) h = (b - r) / delta + 2;
  else h = (r - g) / delta + 4;

  h = Math.round(h * 60);

  if (h < 0) h += 360;

  l = (cmax + cmin) / 2;
  s = delta == 0 ? 0 : delta / (1 - Math.abs(2 * l - 1));
  s = +(s * 100).toFixed(1);
  l = +(l * 100).toFixed(1);

  return {
    lightColor: "hsl(" + h + "," + s + "%," + 96 + "%)", //this l '96' can be changed to adjust the color intensity
    lightColorDark: "hsl(" + h + "," + 30 + "%," + 95 + "%)", // s and l can be adjusted as per need }
  };
}

export const ContextProvider = ({ children }) => {
  const [currentColorLight, setCurrentColorLight] = useState("#ffffff"); // Example state
  const [themeSettings, setThemeSettings] = useState(false);
  const [currentMode, setCurrentMode] = useState("light"); // Example state
  const [currentColor, setCurrentColor] = useState("#03C9D7"); // Example state
  const [currentColorLightDark, setCurrentColorLightDark] =
    useState("hsl(184, 45%, 93%)");
  const [viewModelId, setViewModelId] = useState("");
  const [midForModels, setMidForModels] = useState("");

  console.log("ContextProvider initialized with:", {
    currentColorLight,
    currentMode,
  });
  const setMode = (mode) => {
    setCurrentMode(mode);
    sessionStorage.setItem("themeMode", mode);
  };

  const setColor = (color) => {
    setCurrentColor(color);
    sessionStorage.setItem("colorMode", color);

    //

    let colors = hexToHSL(color);
    setCurrentColorLight(colors.lightColor);
    sessionStorage.setItem("colorLight", colors.lightColor); // this is set at App.js

    setCurrentColorLightDark(colors.lightColorDark);
    sessionStorage.setItem("colorLightDark", colors.lightColorDark);
    //
  };

  return (
    <StateContext.Provider
      value={{
        currentColorLight,
        setCurrentColorLight,
        currentMode,
        setCurrentMode,
        currentColor,
        setCurrentColor,
        setMode,
        currentColorLightDark,
        setCurrentColorLightDark,
        setColor,
        themeSettings,
        setThemeSettings,
        viewModelId,
        setViewModelId,
        midForModels,
        setMidForModels,
      }}
    >
      {children}
    </StateContext.Provider>
  );
};

export const useStateContext = () => {
  const context = useContext(StateContext);
  if (!context) {
    throw new Error("useStateContext must be used within a ContextProvider");
  }
  return context;
};
