import { Box, Typography } from "@mui/material";
import axios from "axios";
import React, { useEffect, useState } from "react";
import { Line } from "react-chartjs-2";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toastMessage } from "../../tools/toast";

const LiveDataChart = ({ text, color, mqttId }) => {
  const [time, setTime] = useState([]);
  const [value, setValue] = useState("");
  const [chartData, setChartData] = useState([]);
  const [lastValues, setLastValues] = useState(10);

  const fetchLiveDataById = async () => {
    await axios
      .get(`${dbConfig.url}/livedata/${mqttId}`)
      .then((response) => {
        const data = response.data?.data;
        let previous = [];
        let time = [];
        for (let index = 0; index < data.previous.length; index++) {
          const element = data.previous[index];
          time.push(element.time?.slice(18, 25));
          previous.push(element.value);
        }
        console.log(data);
        console.log(previous);
        setChartData(previous);
        setTime(time);
      })
      .catch((error) => {
        toastMessage({ message: error.message });
      });
  };

  useEffect(() => {
    fetchLiveDataById();
  }, []);

  const state = {
    labels: time.slice(time.length - lastValues, time.length),
    datasets: [
      {
        label: text,
        radius: 0.3,
        fill: false,
        lineTension: 0.5,
        backgroundColor: color,
        borderColor: color,
        borderWidth: 2,
        data: chartData.slice(chartData.length - lastValues, chartData.length),
      },
    ],
  };

  return (
    <Box>
      <Box sx={{ display: "flex", justifyContent: "space-evenly", p: 4 }}>
        {/* Text */}
        <Box>
          <span style={{ color: color }}>{text}</span>{" "}
          <p style={{ color: "#A2B5BB" }} className="text-sm italic ">
            (Annotation)
          </p>
        </Box>
        {/* Process name  */}

        <div style={{ borderLeft: "1px solid #CFD2CF", height: "50px" }}></div>
        {/* process value */}
        {/* <Box>
          <span>
            <ShowMqttValue mqttId={mqttId} />
          </span>{" "}
          <p style={{ color: "#A2B5BB" }} className="text-sm italic">
            (Actual Value)
          </p>
        </Box> */}
      </Box>
      <Line
        data={state}
        options={{
          scales: {
            y: {
              ticks: {
                stepSize: 10,
              },
            },
          },
          plugins: {
            legend: {
              display: true,
              position: "bottom",
            },
          },
          animations: {
            tension: {
              duration: 10000,
              easing: "linear",
              from: 1,
              to: 0,
              loop: true,
            },
          },
        }}
      />
      <Box sx={{ display: "flex", justifyContent: "flex-end", p: 4 }}>
        <Box sx={{ mr: 2 }}>
          <span
            onClick={() => setLastValues(10)}
            className="cursor-pointer text-gray-400 hover:text-indigo-600"
          >
            10 <span className="text-xs italic">values</span>
          </span>
        </Box>
        <div
          style={{
            borderLeft: "1px solid #CFD2CF",
            height: "25px",
            marginRight: `10px`,
          }}
        ></div>
        <Box sx={{ mr: 2 }}>
          <span
            onClick={() => setLastValues(25)}
            className="cursor-pointer text-gray-400 hover:text-indigo-600"
          >
            25 <span className="text-xs italic">values</span>
          </span>
        </Box>
        <div
          style={{
            borderLeft: "1px solid #CFD2CF",
            height: "25px",
            marginRight: `10px`,
          }}
        ></div>
        <Box sx={{ mr: 2 }}>
          <span
            onClick={() => setLastValues(50)}
            className="cursor-pointer text-gray-400 hover:text-indigo-600"
          >
            50 <span className="text-xs italic">values</span>
          </span>
        </Box>
        <div
          style={{
            borderLeft: "1px solid #CFD2CF",
            height: "25px",
            marginRight: `10px`,
          }}
        ></div>
        <Box sx={{ mr: 2 }}>
          <span
            onClick={() => setLastValues(70)}
            className="cursor-pointer text-gray-400 hover:text-indigo-600"
          >
            70 <span className="text-xs italic">values</span>
          </span>
        </Box>
      </Box>
    </Box>
  );
};

export default LiveDataChart;
