.addMachineForm {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  flex-direction: column;
  margin-top: 4rem;
  .addMachineFormContainer {
    width: 80%;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #344767;
    .title {
      padding: 0 1.2rem;
      padding-top: 1.5rem;
      margin-bottom: 0.8rem;
      h3 {
        font-size: 1.5rem;
        font-weight: 500;
        opacity: 0.9;
      }
    }

    .desc {
      padding: 0 1.2rem;
      p {
        font-size: 1rem;
        opacity: 0.7;
      }
    }

    .machineFormContainer {
      padding: 1.2rem;
      width: 100%;

      .labelFields {
        margin: 0.5rem 0;
        label {
          font-size: 1.1rem;
          font-weight: 500;
          color: #344767;
        }
        .MuiInputBase-root {
          width: 100%;
        }
      }

      .buttons {
        margin: 2rem 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .btn {
          padding: 0.88rem 2rem;
          font-size: 0.85rem;
          font-weight: bold;
          text-transform: uppercase;
          outline: none;
          cursor: pointer;
          border-radius: 8px;
          border: none;
          box-shadow:
            rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
            rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
          &:hover {
            opacity: 0.85;
          }
        }
        .cancelBtn {
          background-color: #e9ecef;
          color: #344767;
        }
        .createBtn {
          color: #fff;
          margin-left: 1rem;
        }
      }
    }
  }
}
