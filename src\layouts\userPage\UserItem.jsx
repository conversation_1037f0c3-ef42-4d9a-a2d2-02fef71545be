import {
  Avatar,
  create<PERSON>ilterO<PERSON>s,
  Dialog,
  IconButton,
  InputBase,
  InputLabel,
  LinearProgress,
  MenuItem,
  Modal,
  Select,
  TableCell,
  TableRow,
  Typography,
} from "@mui/material";
import { Box } from "@mui/system";
import React, { useState } from "react";
import { alpha, styled } from "@mui/material/styles";
import { useStateContext } from "../../context/ContextProvider";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import { useStorage } from "../../utils/useStorage";
import { db } from "../../firebase";
import { companies, companyId_constant, users } from "../../constants/data";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";
import { themeColors } from "../../infrastructure/theme";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { convertBase64 } from "../../hooks/useBase64";
import Delete from "../../components/Delete/Delete";
import { useMongoRefresh } from "../../services/mongo-refresh.context";

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 750,
  bgcolor: "background.paper",
  boxShadow: 24,
  border: "none",
  borderRadius: "8px",
  p: 4,
};

const filterOptions = createFilterOptions({
  matchFrom: "start",
  stringify: (option) => option.role,
});

const BootstrapInput = styled(InputBase)(({ theme }) => ({
  "label + &": {
    marginTop: theme.spacing(0),
  },
  "& .MuiInputBase-input": {
    borderRadius: 4,
    position: "relative",
    border: "1px solid #ced4da",
    fontSize: 14,
    padding: "10px 12px",
    transition: theme.transitions.create([
      "border-color",
      "background-color",
      "box-shadow",
    ]),
    "&:focus": {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main,
    },
  },
}));

const roles = [
  { role: "admin" },
  { role: "manager" },
  { role: "supervisor" },
  { role: "trainee" },
];

const UserItem = ({ user }) => {
  const [open, setOpen] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const { currentMode } = useStateContext();
  const [userData, setUserData] = useState({ ...user });
  const types = ["image/png", "image/jpeg", "image/jpg"];
  const [file, setFile] = useState(null);
  const [fileUrl, setFileUrl] = useState("");
  const { refreshCount, setRefreshCount } = useMongoRefresh();

  function openModal() {
    setOpen(true);
  }

  function closeModal() {
    setOpen(false);
  }

  const handleStatusChange = async () => {
    const updatedUser = {
      ...userData,
      training_status: !userData.training_status,
    };
    try {
      await axios.put(`${dbConfig.url}/users/${userData._id}`, updatedUser);
      setUserData(updatedUser);
      toastMessageSuccess({
        message: `Training status updated to ${
          updatedUser.training_status ? "Completed" : "Incomplete"
        }`,
      });
      setRefreshCount(refreshCount + 1);
    } catch (error) {
      console.error("Error updating training status:", error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const dataSet = { ...userData };
    try {
      await axios.put(`${dbConfig.url}/users/${userData._id}`, dataSet);
      setOpen(false);
      toastMessageSuccess({ message: "Updated User details!" });
      setRefreshCount(refreshCount + 1);
    } catch (error) {
      console.error("Error updating user:", error);
    }
  };

  const handleDelete = async () => {
    await axios.delete(`${dbConfig.url}/users/${userData._id}`).then(() => {
      toastMessageSuccess({ message: "Deleted USER successfully" });
      setRefreshCount(refreshCount + 1);
      setOpenDelete(false);
    });
  };

  const handleChange = async (e) => {
    let selectedFile = e.target.files[0];
    const base64 = await convertBase64(selectedFile);
    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        setFileUrl(base64);
        setFile(selectedFile);
        setUserData((prev) => ({ ...prev, avatar: base64 }));
      } else {
        setFileUrl("");
        setFile(null);
        toastMessage({ message: "Please select an image file (png or jpg)" });
      }
    }
  };

  const { progress, url } = useStorage(file);

  return (
    <>
      <TableRow key={user.id}>
        <TableCell>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Avatar
              sx={{ width: 36, height: 36, marginRight: "16px" }}
              style={{}}
              src={user?.avatar}
            />
            {user?.fname + ` ` + user?.lname}
          </div>
        </TableCell>
        <TableCell align="left">{user.username}</TableCell>

        <TableCell align="left">{user.role}</TableCell>
        <TableCell align="left">{user.email}</TableCell>
        {/* <TableCell align="left">{user.phone}</TableCell> */}
        <TableCell align="left">
          <IconButton onClick={openModal} color="primary" variant="contained">
            <EditIcon />
          </IconButton>
          <IconButton
            onClick={() => setOpenDelete(true)}
            color="error"
            variant="contained"
          >
            <DeleteIcon />
          </IconButton>
        </TableCell>
      </TableRow>

      <Dialog open={openDelete} onClose={() => setOpenDelete(false)}>
        <Delete onClose={() => setOpenDelete(false)} onDelete={handleDelete} />
      </Dialog>

      <Modal open={open} onClose={closeModal}>
        <Box
          style={
            currentMode === "Dark"
              ? { backgroundColor: themeColors.dark.secordary }
              : { backgroundColor: "white" }
          }
          sx={style}
          className="modal"
        >
          <Box
            component="form"
            noValidate
            sx={{
              display: "grid",
              gridTemplateColumns: { sm: "1fr 1fr" },
              alignItems: "center",
              justifyContent: "center",
              gap: 2,
            }}
          >
            <div className="labelFields">
              <Avatar
                sx={{ width: 72, height: 72, marginBottom: "16px" }}
                src={fileUrl.length > 0 ? fileUrl : userData?.avatar}
              />
              {progress > 5 && (
                <LinearProgress
                  variant="determinate"
                  style={{ marginBottom: "10px", marginTop: "10px" }}
                  value={progress}
                />
              )}
            </div>
            <input
              type="file"
              className="form-control"
              onChange={handleChange}
              required
              accept="image/*"
            ></input>
            <div className="labelFields">
              <InputLabel shrink htmlFor="userFirstName">
                First Name
              </InputLabel>
              <BootstrapInput
                id="userFirstName"
                defaultValue={user?.fname}
                onChange={(e) =>
                  setUserData({ ...userData, fname: e.target.value })
                }
              />
            </div>

            <div className="labelFields">
              <InputLabel shrink htmlFor="userLastName">
                Last Name
              </InputLabel>
              <BootstrapInput
                id="userLastName"
                defaultValue={user?.lname}
                onChange={(e) =>
                  setUserData({ ...userData, lname: e.target.value })
                }
              />
            </div>

            <div className="labelFields">
              <InputLabel shrink htmlFor="userName">
                User Name
              </InputLabel>
              <BootstrapInput
                id="userName"
                defaultValue={user.username}
                onChange={(e) =>
                  setUserData({ ...userData, username: e.target.value })
                }
              />
            </div>
            <div className="labelFields">
              <InputLabel shrink htmlFor="userRole">
                Select Role
              </InputLabel>
              <Select
                value={userData.role}
                onChange={(e) =>
                  setUserData({ ...userData, role: e.target.value })
                }
                size="small"
                fullWidth
              >
                {roles.map((data) => (
                  <MenuItem key={data.role} value={data.role.toLowerCase()}>
                    {" "}
                    {data.role}{" "}
                  </MenuItem>
                ))}
              </Select>
            </div>

            <div className="labelFields">
              <InputLabel shrink htmlFor="userEmail">
                Email Address
              </InputLabel>
              <BootstrapInput
                id="userEmail"
                defaultValue={user.email}
                disabled
              />
            </div>

            <div className="labelFields">
              <InputLabel shrink htmlFor="userMobile">
                Phone Number
              </InputLabel>
              <BootstrapInput
                id="userMobile"
                defaultValue={user.phone}
                value={userData.phone}
                type="text"
                pattern="[0-9]*"
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  maxLength: 10,
                }}
                onChange={(e) => {
                  if (
                    e.target.value.toString().match(/^[0-9]+$/) ||
                    e.target.value === ""
                  ) {
                    setUserData({ ...userData, phone: e.target.value });
                  } else {
                    toastMessageWarning({
                      message: "Please Enter Valid Number",
                    });
                  }
                }}
              />
            </div>
          </Box>
          <div className="labelFields">
            <InputLabel htmlFor="trainingStatus">Training Status</InputLabel>
            <Select
              id="trainingStatus"
              value={userData.training_status}
              onChange={handleStatusChange}
            >
              <MenuItem value={true}>Completed</MenuItem>
              <MenuItem value={false}>Incomplete</MenuItem>
            </Select>
          </div>
          <br />
          {userData?.training_status && (
            <>
              <InputLabel htmlFor="trainingReview">Training Review</InputLabel>
              <BootstrapInput
                fullWidth
                id="trainingReview"
                value={userData.training_review}
                onChange={(e) =>
                  setUserData({ ...userData, training_review: e.target.value })
                }
              />
            </>
          )}
          <br />
          {userData?.training_list ? (
            <>
              <InputLabel htmlFor="trainingReview">Training List</InputLabel>
              {userData?.training_list?.map((item, idx) => (
                <Typography key={idx + item}>{item}</Typography>
              ))}
            </>
          ) : (
            <Typography color="error" variant="body2">
              No Training Done / Training has yet to be started
            </Typography>
          )}
          <div
            style={
              currentMode === "Dark"
                ? { backgroundColor: themeColors.dark.secordary }
                : { backgroundColor: "white" }
            }
            className=" mt-4 flex justify-between"
          >
            <ButtonBasicCancel
              className={"closeBtn"}
              buttonTitle="Close"
              type="button"
              onClick={closeModal}
            />
            <ButtonBasic
              className={"createBtn"}
              buttonTitle="Update"
              type="submit"
              onClick={handleSubmit}
            />
          </div>
        </Box>
      </Modal>
    </>
  );
};

export default UserItem;
