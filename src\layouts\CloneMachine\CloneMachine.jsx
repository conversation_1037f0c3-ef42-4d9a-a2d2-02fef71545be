import {
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import { companies, companyId_constant, machines } from "../../constants/data";
import { db } from "../../firebase";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";

const CloneMachine = ({ setNewMachineId, machineDetails }) => {
  const [openconfirm, setOpenConfirm] = useState(false);
  const [checked, setChecked] = useState(false);
  const [machineName, setMachineName] = useState("");

  const clone = () => {
    if (machineName != machineDetails.title) {
      return toastMessage({ message: "Incorrect Machine Name" });
    } else {
      setOpenConfirm(false);
      //       db.collection(companies).doc(companyId_constant)
      // .collection(machines).add(machineDetails).then((data) => {
      //     setNewMachineId(data.id)
      //     toastMessageSuccess({message: "successfully Cloned Machine Details "})
      //     setChecked(true)
      // })
    }
  };

  return (
    <div>
      <Checkbox
        checked={checked}
        onClick={() => {
          !checked && setOpenConfirm(true);
        }}
      />{" "}
      <span>Clone Machine Details </span>
      <Dialog open={openconfirm}>
        <DialogTitle>
          Are you sure you want to clone Machine Details?
        </DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Enter Machine Name "{machineDetails.title}" to Continue
          </Typography>
          <TextField
            onChange={(e) => setMachineName(e.target.value)}
            fullWidth
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenConfirm(false)}
            variant="contained"
            style={{ backgroundColor: "red", color: "white" }}
          >
            Cancel
          </Button>
          <Button onClick={clone} variant="contained" color="primary">
            Continue
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default CloneMachine;
