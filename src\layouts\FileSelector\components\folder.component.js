import { Box, Typography } from "@mui/material";
import React, { useContext, useEffect, useState } from "react";
import { companies, companyId_constant } from "../../../constants/data";
import { db } from "../../../firebase";
import { firebaseLooper } from "../../../tools/tool";
import FolderIcon from "@mui/icons-material/Folder";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import { FileManagerSelectorContext } from "../../../services/fileManager/file-manager-select.context";
import { useFolder } from "../../../hooks/useFolder";
import FolderBreadCrumbsSelector from "./bread-crumbs.component";

const FoldersList = ({ setFType }) => {
  // console.log(fType)
  const [folders, setFolders] = useState([]);
  const [folderIdSelected, setFolderIdSelected] = useState(null);
  const [files, setFiles] = useState([]);
  const { fileUrl, changeFileUrl } = useContext(FileManagerSelectorContext);
  const { folder, childFolders, childFiles } = useFolder(folderIdSelected);

  useEffect(() => {
    // db.collection(companies).doc(companyId_constant).collection('folders').onSnapshot(snap => {
    //     const data = firebaseLooper(snap)
    //     setFolders(data)
    // })
    // db.collection(companies).doc(companyId_constant).collection('files').onSnapshot(snap => {
    //     const data = firebaseLooper(snap)
    //     setFiles(data)
    // })
  }, []);

  return (
    <div style={{ borderTop: "1px solid black", padding: 2 }}>
      {fileUrl &&
        files
          ?.filter((data) => data?.url === fileUrl)
          .map((file) => (
            <>
              <Typography gutterBottom>
                {" "}
                <b>
                  Selected File :{" "}
                </b> <InsertDriveFileIcon color="primary" /> {file?.name}
              </Typography>
              <img width="245px" src={file?.url} />
            </>
          ))}
      <br />
      <FolderBreadCrumbsSelector
        setFolderIdSelected={setFolderIdSelected}
        currentFolder={folder}
      />

      <Box>
        {childFolders?.map((folder) => (
          <>
            <Box
              onClick={() => setFolderIdSelected(folder.id)}
              sx={{ p: 2, cursor: "pointer" }}
            >
              <Typography>
                <FolderIcon color="error" /> {folder?.name}
              </Typography>
            </Box>
          </>
        ))}
        {childFiles?.map((file) => (
          <>
            <Box
              onClick={() => {
                changeFileUrl(file?.url);
                // console.log()
                setFType(file?.url.includes("pdf"));
              }}
              sx={{ p: 2, cursor: "pointer" }}
            >
              <Typography>
                <InsertDriveFileIcon color="primary" /> {file?.name}
              </Typography>
            </Box>
          </>
        ))}
      </Box>
    </div>
  );
};

export default FoldersList;
