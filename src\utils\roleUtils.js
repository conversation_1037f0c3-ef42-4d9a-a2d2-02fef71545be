// utils/roleUtils.js
export const getRoleName = (roleValue, roles) => {
    if (!roles || roles.length === 0) return "";
    const roleObj = roles.find((r) => r.id === Number(roleValue));
    return roleObj ? roleObj.name : "";
};

// You can add more role-related utilities here
export const checkUserRole = (currentUser, envData, targetRole, isFetching = false) => {
    if (!currentUser || !envData.ROLES || isFetching) return false;
    return getRoleName(currentUser?.role, envData.ROLES).toLowerCase() === targetRole.toLowerCase();
};

/**
 * Determines if the current user has any of the specified roles.
 *
 * @param {Object} currentUser - The current user object containing user details.
 * @param {Object} envData - The environment data containing available roles.
 * @param {Array<string>} targetRoles - An array of target roles to check against the user's role.
 * @param {boolean} [isFetching=false] - Optional flag indicating if data is being fetched.
 * @return {boolean} Returns true if the user's role matches any of the target roles, false otherwise.
 */

export const hasAnyRole = (currentUser, envData, targetRoles, isFetching = false) => {
    if (!currentUser || !envData.ROLES || isFetching) return false;
    const userRole = getRoleName(currentUser?.role, envData.ROLES).toLowerCase();
    return targetRoles.map(role => role.toLowerCase()).includes(userRole);
};

/** 
 * Example usage:
    const isPlanner = checkUserRole(currentUser, envData, "planner", isFetching);

    // Or check for multiple roles
    const hasSettingsAccess = hasAnyRole(currentUser, envData, ["planner", "admin"], isFetching);


 */