import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Typography,
} from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import CircularProgress from "@mui/material/CircularProgress";
import { now } from "moment";
import axios from "axios";
import { toast } from "react-toastify";
import userRoles from "./UserRole";
import SelectRoleAlert from "./SelectRoleAlert";
import Pagination from "./UI_Components/pagination";
import SearchField from "./UI_Components/search-field";
import { HourglassEmptyOutlined } from "@mui/icons-material";
import PropTypes from "prop-types";
import { dbConfig } from "../../infrastructure/db/db-config";

const INITIAL_USER_STATE = {
  fname: "",
  lname: "",
  email: "",
  avatar: "",
  username: "",
  role: userRoles.admin,
  phone: "",
  password: "default@123",
  attempt: 0,
  password_created: now().valueOf(),
};

const LDAPUsersTable = ({
  refetchUsers,
  dbUsersList,
  ldapUsers,
  loading,
  refetchLdapUsersConfig,
}) => {
  const availableLdapUsers = useMemo(
    () =>
      ldapUsers.filter((lu) => !dbUsersList.find((u) => u.email === lu.email)),
    [ldapUsers, dbUsersList],
  );
  const [filteredLdapUsers, setFilteredLdapUsers] =
    useState(availableLdapUsers);
  const [selectedUser, setSelectedUser] = useState(null);
  const [roleAlertOpen, setRoleAlertOpen] = useState(false);
  const [userSubmitting, setUserSubmitting] = useState(false);
  const [search, setSearch] = useState("");
  const [selected, setSelected] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    itemsPerPage: 5,
  });

  useEffect(() => {
    setFilteredLdapUsers(
      availableLdapUsers.filter(
        (lu) =>
          (lu.username &&
            lu.username.toLowerCase().includes(search.toLowerCase())) ||
          (lu.email && lu.email.toLowerCase().includes(search.toLowerCase())),
      ),
    );
    setPagination((prev) => ({ ...prev, currentPage: 1 }));
  }, [search, availableLdapUsers]);

  const handleSelect = (id, isChecked) => {
    const newSelected = selected.includes(id)
      ? selected.filter((itemId) => itemId !== id)
      : [...selected, id];
    setSelected(newSelected);
  };

  const handleImport = () => {
    setSelectedUser(INITIAL_USER_STATE);
    setRoleAlertOpen(true);
  };

  const handleAddUserToDB = async () => {
    const selectedUsers = availableLdapUsers
      .filter((au) => selected.includes(au.username))
      .map((au) => ({
        ...INITIAL_USER_STATE,
        username: au.username || au.email || "",
        fname: au.givenName ?? "",
        lname: au.sn ?? "",
        email: au.email || au.username || "",
        role: selectedUser.role ?? au.role,
      }));
    setUserSubmitting(true);
    try {
      await Promise.all(
        selectedUsers.map((user) => axios.post(`${dbConfig.url}/users`, user)),
      );
      toast.success("User Added Successfully.");
    } catch (error) {
      toast.error("Some Error Occurred, Please try later.");
    } finally {
      setTimeout(() => {
        refetchUsers();
        refetchLdapUsersConfig();
        setUserSubmitting(false);
        setRoleAlertOpen(false);
        setSelected([]);
      }, 2000);
    }
  };

  if (loading) {
    return (
      <Box
        width="100%"
        height={324}
        sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        bgcolor: "#f5f7fa", // Light blue-gray container background
        borderRadius: 2,
        p: 2, // Padding inside the container
        border: "1px solid #e0e0e0", // Subtle border
      }}
    >
      <SearchField
        showPDFDownload={false}
        label="Search LDAP Users"
        search={search}
        setSearch={setSearch}
        placeholder="ID, Email"
        sx={{ mb: 2 }}
      />

      <TableContainer
        component={Paper}
        sx={{
          maxHeight: "400px",
          borderRadius: 1,
          boxShadow: "0 2px 4px rgba(0,0,0,0.05)", // Subtle shadow
          border: "1px solid #e0e0e0",
          backgroundColor: "#ffffff", // White table background
        }}
      >
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell sx={{ bgcolor: "#e3f2fd", fontWeight: 600 }}>
                {" "}
                {/* Light blue header */}
                <Checkbox
                  checked={
                    selected.length === filteredLdapUsers.length &&
                    filteredLdapUsers.length
                  }
                  onChange={(e) =>
                    setSelected(
                      e.target.checked
                        ? filteredLdapUsers.map((d) => d.username)
                        : [],
                    )
                  }
                  sx={{ "&.Mui-checked": { color: "#1976d2" } }}
                />
              </TableCell>
              <TableCell sx={{ bgcolor: "#e3f2fd", fontWeight: 600 }}>
                ID
              </TableCell>
              <TableCell sx={{ bgcolor: "#e3f2fd", fontWeight: 600 }}>
                Email
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredLdapUsers
              .slice(
                (pagination.currentPage - 1) * pagination.itemsPerPage,
                pagination.currentPage * pagination.itemsPerPage,
              )
              .map((user) => (
                <TableRow
                  key={user.username}
                  hover
                  sx={{ "&:hover": { bgcolor: "#f5f5f5" } }}
                >
                  <TableCell sx={{ borderBottom: "1px solid #e0e0e0" }}>
                    <Checkbox
                      checked={selected.includes(user.username)}
                      onChange={(e) =>
                        handleSelect(user.username, e.target.checked)
                      }
                      sx={{ "&.Mui-checked": { color: "#1976d2" } }}
                    />
                  </TableCell>
                  <TableCell sx={{ borderBottom: "1px solid #e0e0e0" }}>
                    {user.username ?? "N/A"}
                  </TableCell>
                  <TableCell sx={{ borderBottom: "1px solid #e0e0e0" }}>
                    {user.email}
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
        {!loading && filteredLdapUsers.length === 0 && (
          <Box sx={{ p: 4, textAlign: "center" }}>
            <HourglassEmptyOutlined color="action" fontSize="large" />
            <Typography color="text.secondary">
              {ldapUsers.length === 0
                ? "No LDAP Users Found"
                : "All Ldap Users Already Imported"}
            </Typography>
          </Box>
        )}
      </TableContainer>

      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: 2,
          mt: 2,
        }}
      >
        <Pagination
          totalRows={filteredLdapUsers.length}
          pagination={pagination}
          setPagination={setPagination}
        />
        <Button
          variant="contained"
          color="primary"
          disabled={userSubmitting || !selected.length}
          onClick={handleImport}
          sx={{
            borderRadius: 1,
            px: 3,
            py: 1,
            whiteSpace: "nowrap",
            "&:hover": { bgcolor: "#1565c0" },
          }}
        >
          {userSubmitting ? (
            <CircularProgress size={24} color="inherit" />
          ) : (
            "Import Selected"
          )}
        </Button>
      </Box>
      <SelectRoleAlert
        open={roleAlertOpen}
        setOpen={setRoleAlertOpen}
        selectedUser={selectedUser}
        setSelectedUser={setSelectedUser}
        action={handleAddUserToDB}
      />
    </Box>
  );
};

LDAPUsersTable.propTypes = {
  refetchUsers: PropTypes.func,
  dbUsersList: PropTypes.array,
  ldapUsers: PropTypes.array,
  loading: PropTypes.bool,
  refetchLdapUsersConfig: PropTypes.func,
};

export default LDAPUsersTable;
