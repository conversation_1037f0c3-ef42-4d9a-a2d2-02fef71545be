// --> Resets
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap");
* {
  padding: 10;
  margin: 10;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif !important;
}
ul {
  margin: 0;
  padding: 0;
}

li {
  list-style: none;
}

a {
  text-decoration: none;
  color: #344767;
}

.containerApp {
  width: 100vw;
  height: 100vh;
  // background-color: #f8f9fa;
  display: flex;
}

.borderReadius4 {
  border-radius: 4px 4px 0px 0px;
}

.main {
  width: 82%;
  margin-bottom: 0;
  overflow-x: hidden;
  margin-left: calc(18% + 1rem);
  padding: 1rem;
}

.main.inactive {
  width: 92%;
  margin-left: calc(8% + 1rem);
  transition: width 0.2s ease-in;
}

.adminApp {
  width: 100vw;
  height: 100vh;
  // background-color: #f8f9fa;
  display: flex;
}

.adminMain {
  width: 82%;
  margin-bottom: 0;
  overflow-x: hidden;
  margin-left: calc(18% + 1rem);
  padding: 1rem;
}

.adminMain.inactive {
  width: 92%;
  margin-left: calc(8% + 1rem);
  transition: width 0.2s ease-in;
}

hr {
  height: 1px !important;
  background: linear-gradient(
    to left,
    rgba(0, 0, 0, 0),
    rgba(52, 71, 103, 0.5),
    rgba(0, 0, 0, 0)
  ) !important;
  border: 0;
}
hr:after {
  display: block;
  content: "";
  height: 30px;
  background-image: radial-gradient(
    farthest-side at center top,
    #cccccc 0%,
    rgba(255, 255, 255, 0) 100%
  );
}

// scroll bar //
::-webkit-scrollbar {
  width: 8px;
  height: 3px;
}
::-webkit-scrollbar-track {
  // border-radius: 100vh;
  background: #ffffff;
  width: 40px;
  padding: 10px;
}
::-webkit-scrollbar-thumb {
  background: #053e77;
  border-radius: 100vh;
  border: 5px solid #dcdedf;
  margin: 5px;
}
::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

// custom title (tool-tip) //
[data-title]:hover:after {
  opacity: 1;
  transition: all 0.1s ease 0.5s;
  visibility: visible;
  z-index: 9999; //
}
[data-title]:after {
  content: attr(data-title);
  background-color: #f8f9fa;
  border-radius: 5px;
  color: #444;
  font-size: 125%;
  position: absolute;
  padding: 1px 2px 2px 5px;
  bottom: -1.6em;
  left: -80%;
  bottom: 100%;
  white-space: nowrap;
  box-shadow: 1px 1px 3px #868585;
  opacity: 0;
  border: 1px solid #f5e7e7;
  z-index: 99999;
  visibility: hidden;
}
[data-title] {
  position: relative;
}

.title {
  color: #344767;

  h3 {
    font-weight: 500;
    opacity: 0.9;
  }
}

/* 
  Fix for AR_SMART AS-65; 
  When the user clicks on the upload icon and 
  selects a file then On the left corner it should show in the middle
*/
.MuiDropzonePreviewList-root {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 100%;
}

.MuiDropzonePreviewList-imageContainer {
  margin: 0 auto;
}

// For Ai UI
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
.animate-gradient {
  background: linear-gradient(to right, #f97316, #d946ef, #a855f7);
  background-size: 200% 200%;
  animation: gradientShift 5s ease-in-out infinite;
}
