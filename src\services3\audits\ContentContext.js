import axios from "axios";
import React, { createContext, useEffect, useState, useContext } from "react";
import { dbConfig } from "../../infrastructure/db/db-config";

export const ContentGetterContext = createContext([]);
export const ContentSetterContext = createContext();
export const ContentEditCountContext = createContext();

export function useContentGetter() {
  return useContext(ContentGetterContext);
}
export function useContentSetter() {
  return useContext(ContentSetterContext);
}

export function useContentEditCount() {
  return useContext(ContentEditCountContext);
}

const ContentProvider = ({ children }) => {
  const [details, setDetails] = useState([]);
  const [contentEditCount, setContentEditCount] = useState(0);
  const handleContent = (id) => {
    axios
      .get(`${dbConfig.url}/fatdatas`)
      .then((res) => {
        console.log("fatdatas:", res.data);
        var data = res?.data;
        data.sort(function (a, b) {
          return a.index - b.index;
        });
        let fatDataFiltered = data?.filter((fData) => fData?.fid === id); // remove this filter and get it by new API
        setDetails(fatDataFiltered);
        console.log("fatdatas filtered,id:", id, fatDataFiltered);
      })
      .catch((e) => {
        console.log("error fatData:", e);
      });
  };

  return (
    <ContentGetterContext.Provider value={details}>
      <ContentSetterContext.Provider value={handleContent}>
        <ContentEditCountContext.Provider
          value={{ contentEditCount, setContentEditCount }}
        >
          {children}
        </ContentEditCountContext.Provider>
      </ContentSetterContext.Provider>
    </ContentGetterContext.Provider>
  );
};
export default ContentProvider;
