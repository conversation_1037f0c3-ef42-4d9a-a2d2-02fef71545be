/* eslint-disable jsx-a11y/anchor-is-valid */
import { Checkbox, IconButton } from "@mui/material";
import React, { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import { useNavigate, useParams } from "react-router-dom";
import { db } from "../../firebase";
import { companies, companyId_constant, fatReport } from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import emailjs from "@emailjs/browser";

const AddUsers = () => {
  const history = useNavigate();
  const { fid, fatId } = useParams();
  // const [approvalId, setApprovalId] = useState('')
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [type, setType] = useState("none");

  var otp = Math.floor(1000 + Math.random() * 9000);

  // useEffect(() => {
  //     db.collection(companies).doc(companyId_constant)
  //         .collection(fatReport).doc(fid).collection('fatData')
  //         .where('type', '==', 0).onSnapshot(snap => {
  //             const data = firebaseLooper(snap)
  //             setApprovalId(data[0].id)
  //             console.log(data[0].id)
  //         })

  // }, [])

  //updated

  const handleUsersToApproval = (e) => {
    e.preventDefault();
    toastMessageWarning({
      message: "Email server is not available for this version",
    }); // remove this toast after offline Email service integration

    // if (name.trim() === "" || email.trim() === "") {
    //   toastMessage({ message: "Empty inputs are invalid!" });
    //   return;
    // }
    const data = {
      fatId,
      name,
      email,
      date: new Date(),
      status: false,
      otp: otp,
      type,
    };

    // db.collection(companies).doc(companyId_constant)
    //     .collection(fatReport).doc(fid).collection('fatData')
    //     .doc(fatId).collection('approval')
    //     .add(data).then((e) => {
    //         toastMessageSuccess({ message: "Approval Invitation Sent ! " })

    //         emailjs.send("gmail", "template_wic96ns", {
    //             from_name: "LYO IMS Web",
    //             to_name: name,
    //             link: `https://arizon.vercel.app/fat-approval/${companyId_constant}/${fid}/${fatId}/${e.id}`,
    //             otp: otp,
    //             to_email: email,
    //         }, 'user_gqmHsTLEHxh06fhWlDnqq').then(() => {
    //             history.go(-1)
    //         });

    //     })
  };

  return (
    <div
      className="relative w-full py-16
                before:absolute before:inset-0 before:w-full before:h-[50%] before:bg-gray-200"
    >
      <div className="relative container m-auto px-6 text-gray-500 md:px-12 xl:px-40">
        <div className="m-auto space-y-8 md:w-8/12 lg:w-full">
          <div className="rounded-xl border bg-opacity-50 backdrop-blur-2xl bg-white shadow-xl">
            <div className="lg:grid lg:grid-cols-2">
              <div className="rounded-lg lg:block" hidden>
                <img
                  src="https://st2.depositphotos.com/1001599/43046/v/380/depositphotos_430460192-stock-illustration-sign-page-abstract-concept-vector.jpg?forcejpeg=true"
                  className="rounded-l-xl object-cover"
                  loading="lazy"
                  height=""
                  width=""
                  alt="music mood"
                />
              </div>
              <div className="p-6 sm:p-16">
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <h2 className="mb-8 text-2xl text-cyan-900 font-bold">
                    Enter Email to Add Users to Approve Document
                  </h2>
                  <IconButton onClick={() => history.go(-1)}>
                    <CloseIcon />
                  </IconButton>
                </div>

                <form onSubmit={handleUsersToApproval} className="space-y-8">
                  <div className="space-y-2">
                    <label for="email" className="text-gray-700">
                      Name
                    </label>
                    <input
                      onChange={(e) => setName(e.target.value)}
                      name="name"
                      id="name"
                      className="block w-full px-4 py-3 rounded-md border border-gray-300 text-gray-600 transition duration-300
        focus:ring-2 focus:ring-sky-300 focus:outline-none
        invalid:ring-2 invalid:ring-red-400"
                    />
                  </div>
                  <div className="space-y-2">
                    <label for="email" className="text-gray-700">
                      Email
                    </label>
                    <input
                      onChange={(e) => setEmail(e.target.value)}
                      type="email"
                      name="email"
                      id="email"
                      className="block w-full px-4 py-3 rounded-md border border-gray-300 text-gray-600 transition duration-300
        focus:ring-2 focus:ring-sky-300 focus:outline-none
        invalid:ring-2 invalid:ring-red-400"
                    />
                  </div>
                  <div className="space-y-2 flex justify-between">
                    <div>
                      <Checkbox
                        checked={type === "customer"}
                        onClick={() => setType("customer")}
                      />{" "}
                      <p>Customer</p>
                    </div>
                    <div>
                      <Checkbox
                        checked={type === "vendor"}
                        onClick={() => setType("vendor")}
                      />{" "}
                      <p>Vendor</p>
                    </div>
                  </div>
                  <button
                    type="submit"
                    className="w-full py-3 px-6 rounded-md bg-sky-600
                                        focus:bg-sky-700 active:bg-teal-500"
                  >
                    <span className="text-white">Continue</span>
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddUsers;
