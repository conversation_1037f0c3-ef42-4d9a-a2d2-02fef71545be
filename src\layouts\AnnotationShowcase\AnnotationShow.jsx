import React, { useState, useEffect } from "react";
import useInterval from "./useInterval";

import {
  ReactPictureAnnotation,
  defaultShapeStyle,
  DefaultInputSection,
} from "react-picture-annotation";
import { db } from "../../firebase";
import { companies, companyId_constant, liveData } from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";

//component to get all data from the parent
//data : annotationData from firebase
//annotationData --> manipulated

const AnnotationShow = ({
  color,
  textColor,
  annotationData,
  imgUrl,
  mqttData,
  onSelect,
  onChange,
}) => {
  const [main, setMain] = useState([]);
  const [pageSize, setPageSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });
  const onResize = () => {
    setPageSize({ width: window.innerWidth, height: window.innerHeight });
  };
  //console.log(main[0])
  const [data, setData] = useState(annotationData); // annotationData
  const [time, setTime] = React.useState(0); // counter as to how much calls to be made ?

  const isPaused = false;

  useEffect(() => {
    window.addEventListener("resize", onResize);
    return () => {
      window.removeEventListener("resize", onResize);
    };
    console.log(time);
  }, [time]);

  //Returns latest value from firebase
  //MQTT - from id --> value / every 30 seconds we will call db
  const intervalRef = useInterval(
    () => {
      setData((data) => {
        return data?.map((e, idx) => {
          //mqttId -- we will get the value
          //db.collection('mqtt').doc(e.mqttId).get().then(snap => set the comment )
          //return the new data to setData
          let comment = "";
          for (let x in mqttData) {
            console.log(mqttData[x].id);
            if (mqttData[x].id === e.mqttId) {
              comment =
                main[idx]?.comment +
                " : " +
                Math.round(mqttData[x].value * 1000) / 1000;
              // console.log("Value added ")
            }
          }
          //console.log(mqttData)
          return { ...e, comment };
          //   let comment = Math.random()
          //   return {...e, comment}
          //overwrite old comment to new comment
        });
      });
      if (time < 10000) {
        setTime(time + 1);
      } else {
        window.clearInterval(intervalRef.current);
      }
    },
    isPaused ? null : 15000, // a delay of 15 seconds
  );

  return (
    <>
      <ReactPictureAnnotation
        onSelect={onSelect}
        onChange={onChange}
        image={imgUrl}
        scrollSpeed={0}
        annotationStyle={{
          ...defaultShapeStyle,
          shapeStrokeStyle: color,
          transformerBackground: color,
          lineWidth: 5,
          fontBackground: color,
          fontColor: textColor,
        }}
        annotationData={data}
      />
    </>
  );
};

export default AnnotationShow;
