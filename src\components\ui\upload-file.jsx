import React from "react";
import { Box, IconButton, Typography } from "@mui/material";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import PropTypes from "prop-types";

const FileUpload = ({
  label,
  onChange = () => {},
  type = "file",
  accept = "application/pdf",
  required,
  value,
  width = "200px",
  error = false,
  errorMessage = "",
  ...props
}) => {
  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        padding: "10px 16px",
        border: error ? "1px dashed red" : "1px dashed #ccc",
        flexDirection: "column",
        borderRadius: "8px",
        width: width,
      }}
    >
      <input
        accept={accept}
        style={{ display: "none" }}
        id="file-upload"
        type={type}
        onChange={onChange}
      />
      <label
        htmlFor="file-upload"
        style={{
          cursor: "pointer",
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          gap: 1,
        }}
      >
        <IconButton color="primary" aria-label="upload file" component="span">
          <UploadFileIcon />
        </IconButton>
        <Typography
          variant="body1"
          sx={{
            marginLeft: "10px",
            fontSize: "12px",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            maxWidth: width,
            color: error ? "red" : "black",
          }}
        >
          {error ? errorMessage : value ? value.name : label}
        </Typography>
      </label>
    </Box>
  );
};
FileUpload.propTypes = {
  label: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  type: PropTypes.string,
  accept: PropTypes.string,
  required: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(File)]),
  width: PropTypes.string,
  error: PropTypes.bool,
  errorMessage: PropTypes.string,
};
export default FileUpload;
