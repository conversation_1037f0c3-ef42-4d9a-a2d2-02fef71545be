import React, {useCallback, useState, useEffect} from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  MenuItem,
  Box,
} from "@mui/material";
import {dbConfig} from "../../../infrastructure/db/db-config";
import axios from "axios";
import {toastMessage, toastMessageSuccess} from "../../../tools/toast";
import {
  ButtonBasicCancel,
  SubmitButtons,
} from "../../../components/buttons/Buttons";
import {useStateContext} from "../../../context/ContextProvider";

const alarmTypes = [
  {label: "Process", value: 0},
  {label: "Service", value: 1},
  {label: "System", value: 2},
];

// Choose SOP type
const sopTypes = [
  {label: "Maintenance", value: "Maintenance"},
  {label: "Alarm", value: "Alarm"},
];

const AddAlarm = ({
  open,
  handleClose,
  machineId,
  onAlarmAdded,
  maintenanceOfMachine,
  alarmSopData,
}) => {
  const {currentMode} = useStateContext();

  const initialFormData = {
    tag: "",
    desc: "",
    code: "",
    reason: "",
    action_taken: "",
    type: null,
    value: "",
    def_value: "",
    main_id: "",
    alarm_sop_id: "",
    sop_type: "Maintenance",
  };
  const [formData, setFormData] = useState(initialFormData);

  const handleResetFormData = useCallback(
    () => setFormData(initialFormData),
    [initialFormData, setFormData],
  );

  // Ensures the form is reset when the dialog is opened
  useEffect(() => {
    if (open) {
      handleResetFormData();
    }
  }, [open]);

  const handleChange = e => {
    setFormData(prev => ({...prev, [e.target.name]: e.target.value}));
  };

  const handleSubmit = async () => {
    try {
      const {type, ...formDataWithoutType} = formData;
      const payloadData =
        formData.sop_type === "Maintenance" ? formData : formDataWithoutType;
      const payload = {...payloadData, mid: machineId};
      await axios.post(`${dbConfig.url}/alarms_new`, payload);
      toastMessageSuccess({message: "Alarm added successfully"});
      handleClose();
      // Reset state after successful addition.
      handleResetFormData();
      onAlarmAdded();
    } catch (err) {
      console.error(err);
      toastMessage({message: "Failed to add alarm"});
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth="sm">
      <DialogTitle
        sx={{
          backgroundColor: currentMode === "Dark" ? "#212B36" : undefined,
          color: currentMode === "Dark" ? "#fff" : undefined,
        }}>
        Add New Alarm
      </DialogTitle>
      <DialogContent
        dividers
        sx={{
          backgroundColor: currentMode === "Dark" ? "#212B36" : undefined,
          color: currentMode === "Dark" ? "#fff" : undefined,
        }}>
        <Box
          component="form"
          sx={{display: "flex", flexDirection: "column", gap: 2}}>
          <TextField
            select
            fullWidth
            margin="dense"
            label="SOP Type"
            name="sop_type"
            value={formData.sop_type}
            onChange={handleChange}>
            {sopTypes.map(type => (
              <MenuItem key={type.value} value={type.value}>
                {type.label}
              </MenuItem>
            ))}
          </TextField>
          <TextField
            select
            fullWidth
            label={`Select ${
              formData.sop_type !== "Maintenance" ? "Alarm" : "Maintenance"
            }`}
            name={
              formData.sop_type !== "Maintenance" ? "alarm_sop_id" : "main_id"
            }
            value={
              formData.sop_type === "Maintenance"
                ? formData.main_id
                : formData.alarm_sop_id
            }
            onChange={handleChange}
            required>
            {(formData.sop_type !== "Maintenance"
              ? alarmSopData || []
              : maintenanceOfMachine || []
            )?.map(m => (
              <MenuItem key={m._id} value={m._id}>
                {m.title}
              </MenuItem>
            ))}
          </TextField>

          {[
            {label: "Tag", name: "tag"},
            {label: "Description", name: "desc"},
            {label: "Code", name: "code"},
            {label: "Reason", name: "reason"},
            {label: "Action Taken", name: "action_taken"},
            {label: "Default Value", name: "def_value"},
            {label: "Current Value", name: "value"},
          ].map(field => (
            <TextField
              key={field.name}
              fullWidth
              label={field.label}
              name={field.name}
              value={formData[field.name]}
              onChange={handleChange}
            />
          ))}

          {formData.sop_type !== "Alarm" && (
            <TextField
              select
              fullWidth
              label="Type"
              name="type"
              value={formData.type}
              onChange={handleChange}
              required>
              {alarmTypes.map(type => (
                <MenuItem key={type.value} value={type.value}>
                  {type.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        </Box>
      </DialogContent>

      <Box sx={{p: 2, display: "flex", justifyContent: "space-between"}}>
        <ButtonBasicCancel
          buttonTitle="Cancel"
          type="button"
          onClick={handleClose}
        />
        <SubmitButtons
          buttonTitle="Add Alarm"
          type="button"
          onClick={handleSubmit}
        />
      </Box>
    </Dialog>
  );
};

export default AddAlarm;
