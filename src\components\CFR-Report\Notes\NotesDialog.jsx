import { Button, TextField } from "@mui/material";
import React, { useState } from "react";
import { companies, companyId_constant } from "../../../constants/data";
// import { db } from '../../../firebase'
import { toastMessageSuccess } from "../../../tools/toast";

function NotesDialog({ handleClose }) {
  const [noteTitle, setNoteTitle] = useState("");
  const [noteDetail, setNoteDetail] = useState("");

  const handleSubmit = (e) => {
    // e.preventDefault()
    // db.collection(companies).doc(companyId_constant)
    // .collection('cfrNotes').add({noteTitle, noteDetail, date: new Date()})
    // .then(() => {
    //     toastMessageSuccess({message: "Note added successfully !"})
    // })
  };

  return (
    <form onSubmit={handleSubmit}>
      <TextField
        variant="outlined"
        fullWidth
        placeholder="Enter Note Title"
        onChange={(e) => setNoteTitle(e.target.value)}
        style={{ marginBottom: "20px" }}
      />
      <br />
      <TextField
        variant="outlined"
        fullWidth
        placeholder="Enter Note Details"
        onChange={(e) => setNoteDetail(e.target.value)}
        style={{ marginBottom: "20px" }}
      />
      <div>
        <Button type="submit" variant="contained" fullWidth color="primary">
          Save Note
        </Button>
      </div>
    </form>
  );
}

export default NotesDialog;
