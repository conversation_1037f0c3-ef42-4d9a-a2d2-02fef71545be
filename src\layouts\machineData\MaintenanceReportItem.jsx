import React, { useState, useEffect } from "react";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import {
  TableCell,
  TableRow,
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  Box,
  Typography,
  Tooltip,
  CircularProgress,
  Chip,
  Menu,
  MenuItem as MuiMenuItem,
} from "@mui/material";
import Delete from "../../components/Delete/Delete";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useStateContext } from "../../context/ContextProvider";
import DeleteIcon from "@mui/icons-material/Delete";
import { PictureAsPdf } from "@mui/icons-material";
import EditIcon from "@mui/icons-material/Edit";
import ExpandMoreIcon from "@mui/icons-material/ExpandLess";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { useMaintenanceInfo } from "../../context/MaintenanceContext";
import { useUser } from "../../context/UserContext";
import moment from "moment";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import SelectedComponent from "../../components/reports/SelectedComponent";
import AutoAwesomeIcon from "@mui/icons-material/AutoAwesome";
import EditMaintenanceReport from "./EditMaintenance/EditMaintenanceReport";
import ReportAnalyseAndChatByAiMain from "./AiSection/ReportAnalyseAndChatByAiMain";
import NoDataComponent from "../../components/commons/noData.component";
import { commonRowStyle } from "./MaintenanceReportDataMain";
import { PerformModal } from "../../components/reports/PerformModal";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import BuildIcon from "@mui/icons-material/Build";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";

const MaintenanceReportItem = ({
  stepData,
  data,
  machineData,
  setMaintenanceReportDataAll,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openAiDialog, setOpenAiDialog] = useState(false);
  const [openPerformModal, setOpenPerformModal] = useState(false); // State for PerformModal
  const [carousalData, setCarousalData] = useState([]);
  const [allSortKeys, setAllSortKeys] = useState([]); // new state for sortKeys
  const { currentMode } = useStateContext();
  const [enlarge, setEnlarge] = useState(false);
  const [eValue, setEValue] = useState("");
  const maintenanceInfoFromContext = useMaintenanceInfo();
  const user = useUser();
  const [selectedStep, setSelectedStep] = useState(0);
  const [reset, setReset] = useState(0);
  const [pdfGenrationActive, setPdfGenrationActive] = useState(false);
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const [anchorEl, setAnchorEl] = useState(null);
  const openMenu = Boolean(anchorEl);

  const hasMainReportPUTAccess = useCheckAccess("mainReportData", "PUT");
  const hasMainReportDELETEAccess = useCheckAccess("mainReportData", "DELETE");
  const hasMainReportStepGETAccess = useCheckAccess(
    "mainReportStepData",
    "GET",
  );
  // console.log("data of report status:", data)
  console.log("report status:", data?.report_status);

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  useEffect(() => {
    if (!data?._id) return;

    axios
      .get(`${dbConfig.url}/mainReportStepData/getFromReport/${data._id}`)
      .then((res) => {
        let dataNow = res?.data?.data;
        // Sort dataNow by sortKey ascending
        if (Array.isArray(dataNow)) {
          dataNow = [...dataNow].sort(
            (a, b) => (a.sortKey ?? 0) - (b.sortKey ?? 0),
          );
        }
        setCarousalData(dataNow);
        // Extract sortKey from each step, sort, and store in allSortKeys (ascending)
        const sortKeys = Array.isArray(dataNow)
          ? dataNow
              .map((step) => step.sortKey)
              .filter((k) => k !== undefined && k !== null)
          : [];
        setAllSortKeys(sortKeys);
        console.log("maintenance report step data from mongo : data:", dataNow);
      })
      .catch((e) => {
        console.log("error maintenance report step data from mongo : data:", e);
      });
  }, [reset, data]);

  const handleDownloadPDF = async () => {
    setPdfGenrationActive(true);
    try {
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const res = await axios.get(
        `${dbConfig.url}/mainReportStepData/pdf/export?manual_id=${
          data?._id
        }&timezone=${encodeURIComponent(timeZone)}`,
      );
      console.log("PDF response:", res);
      console.log("timezone:", timeZone);
      window.open(`${dbConfig.url}/${res.data.data}`, "_blank");
    } catch (err) {
      console.error(err);
      toastMessage({
        message: err?.response?.data?.message ?? "Error generating PDF",
      });
    } finally {
      setPdfGenrationActive(false);
    }
  };

  const machineName = machineData.filter(
    (machine) => machine?.id === data?.mid,
  )[0]?.title;

  async function deleteData() {
    await axios
      .delete(`${dbConfig.url}/mainReportData/${data?._id}`)
      .then(() => {
        setOpenDel(false);
        toastMessage({ message: `Deleted ${data?.title} successfully!` });
        setRefreshCount(refreshCount + 1);
        setMaintenanceReportDataAll((prev) =>
          prev.filter((item) => item._id !== data?._id),
        );
      })
      .catch(() => {
        toastMessage({ message: "Failed to delete" });
      });
  }

  const uOrA = async (updatedItem) => {
    var manualId;
    const document = {
      ...updatedItem,
      date: new Date(updatedItem.date),
      updated_at: new Date().toISOString(),
      sub_steps:
        updatedItem.sub_steps !== undefined ? updatedItem.sub_steps : false,
    };
    await axios
      .put(`${dbConfig.url}/mainReportStepData/${updatedItem._id}`, document)
      .then((res) => {
        manualId = res.data.data.manual_id;
        toastMessageSuccess({ message: "Updated Remarks Successfully" });
        setCarousalData((prev) =>
          prev.map((item) =>
            item._id === updatedItem._id ? { ...item, ...updatedItem } : item,
          ),
        );
        setReset(reset + 1);
      })
      .catch((err) => {
        console.error("Error updating remarks:", err);
      });

    // Check all steps' remarks for this report
    try {
      const stepsRes = await axios.get(
        `${dbConfig.url}/mainReportStepData/getFromReport/${manualId}`,
      );
      const steps = stepsRes?.data?.data || [];
      const allFilled =
        steps.length > 0 &&
        steps.every(
          (step) =>
            step.remarks !== "" &&
            step.remarks !== null &&
            step.remarks !== "NA",
        );
      if (allFilled) {
        await axios
          .put(`${dbConfig.url}/mainReportData/${manualId}`, {
            report_status: 1,
          })
          .then(() => {
            toastMessageSuccess({
              message: "Report Status Updated Successfully",
            });
            setRefreshCount(refreshCount + 1);
          })
          .catch((err) => {
            console.error("Error updating report status:", err);
          });
      }
    } catch (err) {
      console.error("Error checking all step remarks:", err);
    }
  };

  return (
    <>
      <TableRow sx={commonRowStyle}>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          <b className="capitalize">{data?.title}</b>
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          {moment(data?.date).format("DD MMM YYYY")}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          <b className="capitalize" data-title={data?.email}>
            {data?.email.split("@")[0]}
          </b>
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          <b className="capitalize" data-title={data?.remarks}>
            {data?.remarks}
          </b>
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="center"
        >
          <span className="font-bold">
            {data?.report_status === 0 && (
              <span className="text-yellow-500">In Progress</span>
            )}
            {data?.report_status === 1 && (
              <span className="text-blue-500">Ready for Review</span>
            )}
            {data?.report_status === 2 && (
              <span className="text-green-500">Reviewed</span>
            )}
          </span>
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="center"
        >
          <div className="dataBtns">
            <IconButton
              onClick={() => setOpenEdit(true)}
              disabled={!hasMainReportPUTAccess}
              sx={{
                color: !hasMainReportPUTAccess ? "grey.500" : "primary.main",
              }}
            >
              <EditIcon style={{ fontSize: "20px" }} />
            </IconButton>

            <IconButton onClick={handleMenuOpen}>
              <MoreVertIcon
                style={{
                  fontSize: "20px",
                  color: currentMode === "Dark" ? "#fff" : "#000",
                }}
              />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={openMenu}
              onClose={handleMenuClose}
              PaperProps={{
                style: {
                  backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
                  color: currentMode === "Dark" ? "#fff" : "#000",
                },
              }}
            >
              <MuiMenuItem
                onClick={() => {
                  setOpenDel(true);
                  handleMenuClose();
                }}
                disabled={!hasMainReportDELETEAccess}
              >
                <DeleteIcon
                  style={{
                    fontSize: "20px",
                    color: "#f00",
                    marginRight: "8px",
                  }}
                />
                Delete
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenAiDialog(true);
                  handleMenuClose();
                }}
              >
                <AutoAwesomeIcon
                  style={{
                    fontSize: "20px",
                    color: "forestgreen",
                    marginRight: "8px",
                  }}
                />
                AI Chat and Analysis
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  handleDownloadPDF();
                }}
                disabled={pdfGenrationActive || !hasMainReportStepGETAccess}
              >
                {pdfGenrationActive ? (
                  <CircularProgress size={20} style={{ marginRight: "8px" }} />
                ) : (
                  <PictureAsPdf
                    style={{
                      fontSize: "20px",
                      color: "red",
                      marginRight: "8px",
                    }}
                  />
                )}
                {pdfGenrationActive ? "Generating PDF..." : "Download PDF"}
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenPerformModal(true);
                  handleMenuClose();
                }}
              >
                <BuildIcon
                  style={{
                    fontSize: "20px",
                    color: "#2196f3",
                    marginRight: "8px",
                  }}
                />
                Perform
              </MuiMenuItem>
            </Menu>
            <IconButton
              onClick={() => setIsOpen(!isOpen)}
              style={currentMode === "Dark" ? { color: "white" } : {}}
            >
              {isOpen ? (
                <ExpandLessIcon style={{ fontSize: "20px" }} />
              ) : (
                <ExpandMoreIcon style={{ fontSize: "20px" }} />
              )}
            </IconButton>
          </div>
        </TableCell>
      </TableRow>

      {isOpen &&
        (carousalData?.length > 0 ? (
          <TableRow sx={{ "&:last-child td, &:last-child th": { border: 0 } }}>
            <TableCell colSpan={6}>
              <Box
                sx={{
                  padding: 1,
                  backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
                  border:
                    currentMode === "Dark"
                      ? "1px solid white"
                      : "1px solid black",
                }}
              >
                <Box sx={{ display: "flex", gap: "20px" }}>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      flexWrap: "wrap",
                      gap: 1,
                      maxHeight: "300px",
                      overflowY: "auto",
                    }}
                  >
                    {carousalData?.length > 0 && hasMainReportStepGETAccess
                      ? carousalData?.map((item, idx) => (
                          <Chip
                            key={idx}
                            label={`Step ${idx + 1}`}
                            onClick={() => setSelectedStep(idx)}
                            color={selectedStep === idx ? "primary" : "default"}
                            sx={{
                              borderRadius: "50%",
                              width: "50px",
                              height: "50px",
                              fontSize: "12px",
                              backgroundColor:
                                item.remarks === null ||
                                item.remarks === "NA" ||
                                item.remarks === ""
                                  ? "#E78895"
                                  : "#74E291",
                              "& .MuiChip-label": {
                                padding: "0",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                              },
                            }}
                          />
                        ))
                      : null}
                  </Box>

                  <Box sx={{ flexGrow: 1 }}>
                    {hasMainReportStepGETAccess ? (
                      carousalData[selectedStep] && (
                        <Box>
                          <SelectedComponent
                            index={selectedStep}
                            key={carousalData[selectedStep]._id}
                            item={carousalData[selectedStep]}
                            setEValue={setEValue}
                            setEnlarge={setEnlarge}
                          />
                        </Box>
                      )
                    ) : (
                      <NotAccessible />
                    )}
                  </Box>
                </Box>
              </Box>
            </TableCell>
          </TableRow>
        ) : hasMainReportStepGETAccess ? (
          <NoDataComponent
            noDataMessage={"No Steps Available"}
            paddText={true}
            padding={"12px"}
          />
        ) : (
          <NotAccessible />
        ))}

      <Dialog open={openDel}>
        <Delete onClose={() => setOpenDel(false)} onDelete={deleteData} />
      </Dialog>
      <Dialog open={openEdit} fullWidth>
        <DialogTitle>Edit Report [{data?.title}]</DialogTitle>
        <DialogContent>
          <EditMaintenanceReport
            handleClose={() => setOpenEdit(false)}
            mid={data.mid}
            data={data}
            userName={`${user?.fname} ${user?.lname}`}
            reportName={data.title}
            machineName={machineName}
          />
        </DialogContent>
      </Dialog>
      <Dialog
        open={enlarge}
        onClose={() => setEnlarge(false)}
        fullWidth
        maxWidth="lg"
      >
        <DialogTitle>Enlarged Image</DialogTitle>
        <DialogContent>
          <img
            src={`${dbConfig?.url_storage}/${eValue}`}
            alt="Enlarged"
            style={{ width: "100%", height: "100%" }}
          />
        </DialogContent>
      </Dialog>
      <Dialog open={openAiDialog} fullWidth maxWidth="lg">
        <DialogTitle>
          Chat and Analyze [{data?.title}]
          <Tooltip title="AI Chat and Analyze">
            <AutoAwesomeIcon
              className="animate-pulse"
              style={{ fontSize: "30px", color: "#00f" }}
            />
          </Tooltip>
        </DialogTitle>
        <DialogContent>
          <ReportAnalyseAndChatByAiMain
            handleClose={() => setOpenAiDialog(false)}
            mid={data.mid}
            data={data}
            userName={`${user?.fname} ${user?.lname}`}
            reportName={data.title}
            machineName={machineName}
          />
        </DialogContent>
      </Dialog>
      <PerformModal
        rowData={data}
        currStep={selectedStep}
        carousalData={carousalData}
        setEValue={setEValue}
        setEnlarge={setEnlarge}
        handleSubmit={async (item, updatedItem) => {
          await uOrA(updatedItem);
        }}
        open={openPerformModal}
        handleClose={() => setOpenPerformModal(false)}
        allSortKeys={allSortKeys} // pass allSortKeys as prop
      />
    </>
  );
};

export default MaintenanceReportItem;
