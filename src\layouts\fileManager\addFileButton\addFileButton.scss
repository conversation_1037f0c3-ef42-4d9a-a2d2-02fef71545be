.toast {
  width: 350px;
  max-width: 100%;
  font-size: 0.875rem;
  pointer-events: auto;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}
.fade {
  transition: opacity 0.15s linear;
}

.w-100 {
  width: 100% !important;
}
.d-block {
  display: block !important;
}
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.toast-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: #6c757d;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.toast-body {
  padding: 0.75rem;
  word-wrap: break-word;
}

.progress {
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.bg-danger {
  background-color: red;
}
.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #0d6efd;
  transition: width 0.6s ease;
}

.toast-header .btn-close {
  margin-right: -0.375rem;
  margin-left: 0.75rem;
}

[type="button"]:not(:disabled),
[type="reset"]:not(:disabled),
[type="submit"]:not(:disabled),
button:not(:disabled) {
  cursor: pointer;
}
.btn-close {
  box-sizing: content-box;
  width: 10px;
  height: 10px;
  padding: 0.25em 0.25em;
  color: #000;
  background: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 16 16%27 fill=%27%23000%27%3e%3cpath d=%27M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z%27/%3e%3c/svg%3e");
  border: 0;
  border-radius: 0.25rem;
  opacity: 0.5;
}
[type="button"],
[type="reset"],
[type="submit"],
button {
  -webkit-appearance: button;
}
button,
select {
  text-transform: none;
}
button {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.dropZoneMaxHeight {
  min-height: 18vh !important;
  padding: 0 !important;
}

.MuiDropzonePreviewList-imageContainer {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 !important;
  margin-top: 40px !important;
  width: 125px !important;
}

.MuiDropzonePreviewList-image {
  height: 50px !important;
  width: 50px !important;
  align-self: center !important;
}

.dropZoneTextContainer {
  min-height: 6vh !important;
  max-height: 6vh !important;
}

.dropZoneIcon {
  font-size: 4vh !important;
  height: 4vh !important;
  margin-top: -50px !important;
}

.dropZoneText {
  font-size: 16px !important;
  margin-top: 5px !important;
}
