.adminLoginPage {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  .logoContainer {
    position: absolute;
    top: 3rem;
    left: 3rem;
    width: 60px;
    height: 60px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .illustrationContainer {
    width: 50%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
    }
  }

  .formContainer {
    width: 50%;
    height: 100%;
    background-color: #f8f9fa;
    display: flex;
    padding: 7% 10%;
    flex-direction: column;
    color: #344767;

    .formTitle {
      font-size: 3.5rem;
      font-weight: bolder;
      color: #ff4d00d8;
    }
    .formDesc {
      padding-top: 2rem;
      padding-bottom: 1.5rem;
      font-size: 1rem;
      font-weight: 400;
      opacity: 0.7;
    }

    .form {
      .linkContainer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .link {
          font-size: 0.8rem;
          opacity: 0.6;
        }
      }

      .labelFields {
        margin: 2rem 0;
        label {
          font-size: 1.1rem;
          font-weight: 500;
          //color: #344767;
        }
        .MuiInputBase-root {
          width: 100%;
        }

        .MuiAutocomplete-input {
          font-size: 0.9rem;
          color: #344767;
          opacity: 0.8;
        }
      }

      button {
        margin-top: 1.5rem;
        color: #fff;
        width: 100%;
        padding: 1.3rem 2rem;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
        outline: none;
        cursor: pointer;
        border-radius: 8px;
        border: none;
        background-color: #ff4e00;
        background-image: linear-gradient(315deg, #ff4e00 0%, #ec9f05 74%);

        box-shadow:
          rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
          rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
        &:hover {
          opacity: 0.85;
        }
      }
    }
  }
}
