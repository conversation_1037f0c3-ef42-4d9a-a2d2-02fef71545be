import React, { useState, useEffect } from "react";
import {
  FormControlLabel,
  FormGroup,
  LinearProgress,
  TextField,
} from "@mui/material";
import { Button, Checkbox, IconButton, InputLabel } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CloseIcon from "@mui/icons-material/Close";
import { db } from "../../../firebase";
import { companies, companyId_constant } from "../../../constants/data";
import { firebaseLooper } from "../../../tools/tool";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../tools/toast";
import { useParams } from "react-router-dom";
import BookmarkIcon from "@mui/icons-material/Bookmark";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { useAuth } from "../../../hooks/AuthProvider";
import Editor from "../../../components/Editor/Editor";
import { DropzoneArea } from "material-ui-dropzone";
import { Empty } from "antd";
import { FileDownload } from "@mui/icons-material";
import { DeleteByUrl } from "../../../utils/StorageOptions";
import { useStorageTablesFile } from "../../../utils/useStorageTablesFile";
import { useStateContext } from "../../../context/ContextProvider";
import { ButtonBasic } from "../../../components/buttons/Buttons";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import { useContentEditCount } from "../../../services3/audits/ContentContext";

const EditDetailsDocumentation = ({
  details,
  type,
  handleClose,
  machineName,
  setOpenUploadTable,
  setOpenTable,
  setOpenEdit,
}) => {
  const { contentEditCount, setContentEditCount } = useContentEditCount();
  const [summary, setSummary] = useState(
    details.summary ? details.summary : false,
  );
  const [title, setTitle] = useState(details?.title ? details?.title : "");
  const [desc, setDesc] = useState(details?.desc ? details?.desc : []);
  const [method, setMethod] = useState(details?.method ? details?.method : "");
  const [objective, setObjective] = useState(
    details?.objective ? details?.objective : "",
  );
  const [comment, setComment] = useState(
    details?.comment ? details?.comment : "",
  );
  const [preValue, setPreValue] = useState("");
  const [proValue, setProValue] = useState("");
  const [pre, setPre] = useState(details?.pre ? details?.pre : []);
  const [pro, setPro] = useState(details?.procedure ? details?.procedure : []);
  const [user, setUser] = useState([]);
  const { docId } = useParams();
  const { currentUser } = useAuth();
  const [check1, setCheck1] = useState(details?.purpose ? true : false);
  const [check2, setCheck2] = useState(details?.scope ? true : false);
  const [check3, setCheck3] = useState(details?.objective ? true : false);
  const [check4, setCheck4] = useState(details?.method ? true : false);
  const [check5, setCheck5] = useState(
    details?.pre && details?.pre.length > 0 ? true : false,
  );
  const [check6, setCheck6] = useState(
    details?.procedure && details?.procedure.length > 0 ? true : false,
  );
  const [check7, setCheck7] = useState(details?.addDesc ? true : false);
  const [check8, setCheck8] = useState(details?.tet ? true : false);
  const [check9, setCheck9] = useState(details?.mtt ? true : false);
  const [check10, setCheck10] = useState(
    "urls" in details ? (details.urls.length > 0 ? true : false) : false,
  );
  const [check11, setCheck11] = useState(details?.summary ? true : false);
  const [check12, setCheck12] = useState(details?.comment ? true : false);
  const [purpose, setPurpose] = useState(
    details?.purpose ? details.purpose : "",
  );
  const [scope, setScope] = useState(details?.scope ? details?.scope : "");
  const [addDesc, setAddDesc] = useState(
    details?.addDesc ? details?.addDesc : [],
  );
  const [tetValue, setTETValue] = useState("");
  const [mttValue, setMTTValue] = useState("");
  const [tet, setTET] = useState(details?.tet ? details?.tet : []);
  const [mtt, setMTT] = useState(details?.mtt ? details.mtt : []);
  const [file, setFile] = useState(null);
  const { progress, url } = useStorageTablesFile(file);
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];
  const { currentMode } = useStateContext();
  const [urlData, setUrlData] = useState("");
  const [urls, setUrls] = useState(details?.urls ? details?.urls : []);

  useEffect(() => {
    if (!check1) setPurpose("");
    if (!check2) setScope("");
    if (!check3) setObjective("");
    if (!check4) setMethod("");
    if (!check5) setPre([]);
    if (!check6) setPro([]);
    if (!check7) setAddDesc({ blocks: [] }); //⭐ this default format is necessory
    if (!check8) setTET([]);
    if (!check9) setMTT("");
    if (!check10) setUrls([]);
    if (!check11) setSummary("");
    if (!check12) setComment("");
  }, [
    check1,
    check2,
    check3,
    check4,
    check5,
    check6,
    check7,
    check8,
    check9,
    check10,
    check11,
    check12,
  ]);

  const prePopData = (data) => {
    setPre((pre) => pre.filter((dataItem) => dataItem !== data));
  };
  const proPopData = (data) => {
    setPro((pro) => pro.filter((dataItem) => dataItem !== data));
  };

  const tetPopData = (data) => {
    setTET((tet) => tet.filter((dataItem) => dataItem !== data));
  };

  const mttPopData = (data) => {
    setMTT((mtt) => mtt.filter((dataItem) => dataItem !== data));
  };

  const urlsPopData = (data) => {
    setUrls((urls) => urls.filter((dataItem) => dataItem !== data));
  };

  const handleChangeImage = (file) => {
    let selectedFile = file[0];
    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        if (url) {
          DeleteByUrl(url);
        }
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };

  const handleDeleteDropZone = (url) => {
    DeleteByUrl(url);
    setFile(null);
  };

  const moduleName = type === "fatData" ? "FAT" : "SAT";
  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("dessssssssssss:", desc);
    const docdata = {
      title,
      desc,
      purpose,
      scope,
      objective,
      method,
      pre,
      procedure: pro,
      addDesc,
      tet,
      mtt,
      urls,
      summary,
      docId: docId,
      createdAt: `${new Date().toString()}`,
      comment,
    };
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(type)
    //     .doc(details.id)
    //     .update(docdata)
    //     .then((data) => {
    //         setOpenEdit(false)
    //         LoggingFunction(
    //             machineName,
    //             details.title,
    //             `${user?.fname} ${user?.lname}`,
    //             moduleName,
    //             `${details.title} is updated`
    //         )
    //         toastMessageSuccess({ message: "Updated details for documentation Successfully !" })
    //     })

    axios
      .put(`${dbConfig.url}/fatdatas/${details?._id}`, docdata)
      .then((res) => {
        setContentEditCount(contentEditCount + 1);
        handleClose();
        console.log(
          "fatdatas in EditMaster.jsx  fat update respone:",
          res.data,
        );
        toastMessageSuccess({
          message: "Updated details for documentation Successfully !",
        });
      })
      .catch((e) => {
        console.log("error in EditMAster , updating fatdatas:", e);
        toastMessageWarning({ message: "Update details failed!" });
      });
  };

  useEffect(() => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection("userData")
    //     .where("email", "==", currentUser.email)
    //     .onSnapshot((snap) => {
    //         const data = firebaseLooper(snap);
    //         setUser(data[0]);
    //     });
  }, []);

  return (
    <div style={{ display: "flex", minWidth: "500px" }}>
      {details?.type !== 1 && (
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                checked={check1}
                onChange={(e) => setCheck1(e.target.checked)}
              />
            }
            label="Purpose"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={check2}
                onChange={(e) => setCheck2(e.target.checked)}
              />
            }
            label="Scope"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={check3}
                onChange={(e) => setCheck3(e.target.checked)}
              />
            }
            label="Objective"
          ></FormControlLabel>
          <FormControlLabel
            control={
              <Checkbox
                checked={check4}
                onChange={(e) => setCheck4(e.target.checked)}
              />
            }
            label="Method"
          ></FormControlLabel>
          <FormControlLabel
            control={
              <Checkbox
                checked={check5}
                onChange={(e) => setCheck5(e.target.checked)}
              />
            }
            label="Prerequisites"
          ></FormControlLabel>
          <FormControlLabel
            control={
              <Checkbox
                checked={check6}
                onChange={(e) => setCheck6(e.target.checked)}
              />
            }
            label="Test Procedure"
          ></FormControlLabel>
          <FormControlLabel
            control={
              <Checkbox
                checked={check7}
                onChange={(e) => setCheck7(e.target.checked)}
              />
            }
            label="Additional Description Field"
          ></FormControlLabel>
          {/* <FormControlLabel control={<Checkbox checked={check8} onChange={e => setCheck8(e.target.checked)}/>} label="Test Execution Table"></FormControlLabel> */}
          {/* <FormControlLabel control={<Checkbox checked={check9} onChange={e => setCheck9(e.target.checked)}/>} label="Multiple Table Types"></FormControlLabel> */}
          <Button
            variant="contained"
            color="primary"
            style={{ width: "80%", fontSize: "12px", marginBottom: "6px" }}
            onClick={() => setOpenTable(true)}
          >
            Test Execution Table
          </Button>
          <Button
            variant="contained"
            color="primary"
            style={{ width: "80%", fontSize: "12px", marginBottom: "5px" }}
            onClick={() => setOpenUploadTable(true)}
          >
            Multiple Table Types
          </Button>
          <FormControlLabel
            control={
              <Checkbox
                checked={check10}
                onChange={(e) => setCheck10(e.target.checked)}
              />
            }
            label="Images / Attachments"
          ></FormControlLabel>
          <FormControlLabel
            control={
              <Checkbox
                checked={check11}
                onChange={(e) => setCheck11(e.target.checked)}
              />
            }
            label="Test Summary"
          ></FormControlLabel>
          <FormControlLabel
            control={
              <Checkbox
                checked={check12}
                onChange={(e) => setCheck12(e.target.checked)}
              />
            }
            label="Comment"
          ></FormControlLabel>
        </FormGroup>
      )}

      <form onSubmit={handleSubmit} style={{ width: "75%" }}>
        <InputLabel>Title</InputLabel>
        <TextField
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          style={{ marginBottom: "10px" }}
          variant="outlined"
          fullWidth
          placeholder="Enter Document Title"
          required
        />
        <InputLabel>Description</InputLabel>
        <Editor editorData={desc} setEditorData={setDesc} id={"something1"} />
        {/* <TextField value={desc} onChange={(e) => setDesc(e.target.value)} style={{ marginBottom: '10px' }} variant='outlined' fullWidth placeholder='Enter Document Description' /> */}
        {check1 && (
          <>
            <InputLabel>Purpose</InputLabel>
            <TextField
              value={purpose}
              onChange={(e) => setPurpose(e.target.value)}
              style={{ marginBottom: "10px" }}
              variant="outlined"
              fullWidth
              placeholder="Enter Document Purpose"
            />
          </>
        )}
        {check2 && (
          <>
            <InputLabel>Scope</InputLabel>
            <TextField
              value={scope}
              onChange={(e) => setScope(e.target.value)}
              style={{ marginBottom: "10px" }}
              variant="outlined"
              fullWidth
              placeholder="Enter Document Scope"
            />
          </>
        )}
        {check3 && (
          <>
            <InputLabel>Objective</InputLabel>
            <TextField
              value={objective}
              onChange={(e) => setObjective(e.target.value)}
              style={{ marginBottom: "10px" }}
              variant="outlined"
              fullWidth
              placeholder="Enter Document Objective"
            />
          </>
        )}
        {check4 && (
          <>
            <InputLabel>Method</InputLabel>
            <TextField
              value={method}
              onChange={(e) => setMethod(e.target.value)}
              style={{ marginBottom: "10px" }}
              variant="outlined"
              fullWidth
              placeholder="Enter Document Method"
            />
          </>
        )}
        {check5 && (
          <>
            <InputLabel>Pre Requisites</InputLabel>
            <section style={{ marginBottom: "10px" }} className="flex">
              <TextField
                onChange={(e) => setPreValue(e.target.value)}
                variant="outlined"
                fullWidth
                placeholder="Enter Pre-requisite data"
              />
              <IconButton onClick={() => setPre([...pre, preValue])}>
                <AddIcon />
              </IconButton>
            </section>
            <div style={{ marginBottom: "20px" }}>
              {pre?.map((data, idx) => (
                <div key={data + idx}>
                  <span className="font-bold">{idx + 1}. </span> {data}{" "}
                  <span>
                    <IconButton onClick={() => prePopData(data)}>
                      <RemoveIcon />
                    </IconButton>
                  </span>
                </div>
              ))}
            </div>
          </>
        )}
        {check6 && (
          <>
            <InputLabel>Procedure</InputLabel>
            <section style={{ marginBottom: "10px" }} className="flex">
              <TextField
                onChange={(e) => setProValue(e.target.value)}
                variant="outlined"
                fullWidth
                placeholder="Enter Procedure"
              />
              <IconButton onClick={() => setPro([...pro, proValue])}>
                <AddIcon />
              </IconButton>
            </section>
            <div style={{ marginBottom: "20px" }}>
              {pro?.map((data, idx) => (
                <div key={data + idx}>
                  <span className="font-bold">{idx + 1}. </span> {data}{" "}
                  <span>
                    <IconButton onClick={() => proPopData(data)}>
                      <RemoveIcon />
                    </IconButton>
                  </span>
                </div>
              ))}
            </div>
          </>
        )}
        {check7 && (
          <div style={{ marginBottom: "10px" }}>
            <InputLabel>Additional Description</InputLabel>
            {console.log(addDesc)}
            <Editor
              editorData={addDesc}
              setEditorData={setAddDesc}
              id={"something2"}
            />
            {/* <TextField value={addDesc} onChange={(e) => setAddDesc(e.target.value)} variant='outlined' fullWidth placeholder='Enter Document Additional Description' /> */}
          </div>
        )}
        {check8 && (
          <>
            {/* <InputLabel>Test Execution Table</InputLabel>
            <section style={{ marginBottom: "10px" }} className="flex">
              <TextField
                onChange={(e) => setTETValue(e.target.value)}
                variant="outlined"
                fullWidth
                placeholder="Enter Test Execution Table"
              />
              <IconButton onClick={() => setTET([...tet, tetValue])}>
                <AddIcon />
              </IconButton>
            </section> */}
            <div style={{ marginBottom: "20px" }}>
              {tet?.map((data, idx) => (
                <div key={data + idx}>
                  <span className="font-bold">{idx + 1}. </span> {data}{" "}
                  <span>
                    <IconButton onClick={() => tetPopData(data)}>
                      <RemoveIcon />
                    </IconButton>
                  </span>
                </div>
              ))}
            </div>
          </>
        )}
        {check9 && (
          <>
            <InputLabel>Multiple Table Types</InputLabel>
            <section style={{ marginBottom: "10px" }} className="flex">
              <TextField
                onChange={(e) => setMTTValue(e.target.value)}
                variant="outlined"
                fullWidth
                placeholder="Multiple Table Types"
              />
              <IconButton onClick={() => setMTT([...mtt, mttValue])}>
                <AddIcon />
              </IconButton>
            </section>
            <div style={{ marginBottom: "20px" }}>
              {mtt?.map((data, idx) => (
                <div key={data + idx}>
                  <span className="font-bold">{idx + 1}. </span> {data}{" "}
                  <span>
                    <IconButton onClick={() => mttPopData(data)}>
                      <RemoveIcon />
                    </IconButton>
                  </span>
                </div>
              ))}
            </div>
          </>
        )}
        {check10 && (
          <div className="flex">
            <section className="flex flex-wrap">
              {urls?.map((data, idx) => (
                <span
                  key={data + idx}
                  style={{ marginRight: "30px", marginBottom: "30px" }}
                >
                  {/* <span className='font-bold'>{idx + 1}. </span> */}
                  <img
                    style={{ width: "150px", height: "150px" }}
                    src={data}
                    alt="img"
                  />
                  <span>
                    <IconButton onClick={() => urlsPopData(data)}>
                      <RemoveIcon />
                    </IconButton>
                  </span>
                </span>
              ))}
              <div
                style={{ width: "300px" }}
                className={
                  currentMode === "Dark"
                    ? "bg-gray-700 rounded-sm p-1 shadow-md"
                    : "bg-gray-100 rounded-sm p-1 shadow-md"
                }
              >
                <InputLabel style={{ marginBottom: "10px" }}>Media</InputLabel>
                <DropzoneArea
                  showFileNames
                  onChange={(loadedFiles) => handleChangeImage(loadedFiles)}
                  dropzoneText="Drag and Drop / Click to ADD Media. MaxFileSize : 5Mb"
                  showAlerts={false}
                  filesLimit={1}
                  maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
                  onDelete={() => handleDeleteDropZone(url)}
                  dropzoneClass={
                    currentMode === "Dark"
                      ? "dropZoneClassDark"
                      : "dropZoneClassLight"
                  }
                />
                <div className="p-2 block">
                  <LinearProgress
                    style={{ marginBottom: "20px" }}
                    variant="determinate"
                    value={progress}
                  />
                  <div className="text-2xl text-gray-400 flex justify-end ">
                    <p> {progress} % Uploaded</p>
                  </div>
                  {!url ? ( // url when we upload
                    <>
                      {url ? (
                        <img alt="" src={url} />
                      ) : (
                        <>
                          <a target="_blank" href={urlData} rel="noreferrer">
                            {" "}
                            <img alt="" src={urlData} />{" "}
                          </a>
                          {urlData?.includes(".pdf") && (
                            <a
                              target="_blank"
                              href={urlData}
                              data-title="download"
                              rel="noreferrer"
                            >
                              <FileDownload className=" animate-bounce bg-slate-400 shadow-md rounded-sm hover:bg-slate-500" />
                              Pdf
                            </a>
                          )}
                        </>
                      )}
                    </>
                  ) : (
                    // <Empty
                    //     description={<span>Please Wait for Preview ...</span>}
                    // />
                    <></>
                  )}
                </div>
                {url && (
                  <ButtonBasic
                    type="button"
                    onClick={() => {
                      setUrls([...urls, url]);
                      setFile("");
                      setUrlData("");
                    }}
                    buttonTitle="Add"
                  ></ButtonBasic>
                )}
              </div>
              {/* <div style={{ marginBottom: '20px' }}> */}
              {/* </div> */}
            </section>
          </div>
        )}
        {details?.template != "0" && (
          <div>
            {check11 && (
              <>
                <InputLabel>Summaryyy</InputLabel>
                <section style={{ marginBottom: "10px" }} className="flex">
                  <div
                    onClick={() => setSummary(true)}
                    className={
                      summary
                        ? `flex p-2  cursor-pointer  border-2 border-blue-400 mr-4 rounded-md bg-blue-100 `
                        : `flex cursor-pointer  p-2 border-2 border-gray-400 mr-4 rounded-md  `
                    }
                  >
                    <Checkbox
                      label="Yes"
                      checked={summary}
                      icon={<CheckCircleIcon />}
                      checkedIcon={<CheckCircleIcon />}
                    />
                    <p style={summary ? { color: "black" } : {}}>YES</p>
                  </div>
                  <div
                    onClick={() => setSummary(false)}
                    className={
                      !summary
                        ? `flex cursor-pointer  p-2 border-2 border-purple-400 mr-4 rounded-md bg-purple-100 `
                        : `flex cursor-pointer p-2 border-2 border-gray-400 mr-4 rounded-md  `
                    }
                  >
                    <Checkbox
                      checked={!summary}
                      color="secondary"
                      icon={<CloseIcon />}
                      checkedIcon={<CloseIcon />}
                    />
                    <p style={!summary ? { color: "black" } : {}}>NO</p>
                  </div>
                </section>
              </>
            )}
            {check12 && (
              <>
                <InputLabel>Comment</InputLabel>
                <TextField
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  style={{ marginBottom: "10px" }}
                  variant="outlined"
                  fullWidth
                  placeholder="Comment here..."
                />
              </>
            )}
          </div>
        )}

        <div className="flex justify-between">
          <Button
            onClick={handleClose}
            variant="contained"
            color="secondary"
            endIcon={<CloseIcon />}
          >
            Cancel{" "}
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            endIcon={<BookmarkIcon />}
          >
            Save Details
          </Button>
        </div>
      </form>
    </div>
  );
};

export default EditDetailsDocumentation;
