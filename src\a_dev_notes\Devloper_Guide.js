{
  /* 
        🎧Notes related to implementation
        
        *

        //////////////////⭐ COMMON CODES ⭐//////////////////
        1. Axios post call:-
         👉 await axios
      .post(`${dbConfig.url}/folders`, {
        name: name,
        machineId: machineId,
        parentId: currentFolder._id === undefined ? "" : currentFolder._id,
        path: path,
        createdAt: new Date().toISOString(),
      })
      .then(() => {
        setName("");
        setMachineId("");
        closeModal();
        toastMessageSuccess({
          message: "Added Successfully!",
        });
        setRefreshCount(refreshCount + 1);
        setOpen(false)
      }); 👈

      2. Axios delete call:-
       👉 axios.delete(`${dbConfig.url}/fatlists/${data?._id}`)
      .then(() => {
        toastMessage({ message: "Deleted data successfully !" });
      }); 👈


*/
}
