import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  Box,
  TableRow,
  Typography,
  Skeleton,
  Button,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import UserItem from "./UserItem copy";
import { makeStyles } from "@mui/styles";
import { v4 as uuidv4 } from "uuid";
import Pagination from "./UI_Components/pagination";
import SearchField from "./UI_Components/search-field";
import PropTypes from "prop-types";
import TableHeader from "../machineData/TableHeader";
import CommonDropDown from "../../components/commons/dropDown.component";
import AddIcon from "@mui/icons-material/Add";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";
import { useUtils } from "../../hooks/UtilsProvider";

const useStyles = makeStyles((theme) => ({
  theming: {
    backgroundColor: theme.palette.custom.backgroundForth,
    color: theme.palette.custom.textColor,
  },
  container: {
    all: "unset",
    maxHeight: "84vh",
    backgroundColor: theme.palette.custom.backgroundForth,
    color: theme.palette.custom.textColor,
  },
  tableHeaderCell: {
    all: "unset",
    fontWeight: "bold !important",
    fontSize: "0.9rem !important",
    // minWidth: "6rem !important",
  },
  tableCell: { all: "unset" },
}));

export const columnCode = [
  "fname",
  "username",
  "user_role",
  "email",
  "actions",
];

const tableColumns = [
  { label: "Name", align: "left" },
  { label: "UserName", align: "left" },
  { label: "Role", align: "left" },
  { label: "Email", align: "left" },
  { label: "Actions", align: "center" },
];

const UserListTable = ({
  allUsersList,
  fetchAllUsers,
  loading,
  setloading,
  isModelOpen,
  setShowAddUser,
}) => {
  const { envData, isFetching } = useUtils(); // Access envData from useUtils
  const currentUserTemp = sessionStorage.getItem("@user-creds");
  let currentUser;

  const hasUserGETAccess = useCheckAccess("users", "GET");
  const hasUserPOSTAccess = useCheckAccess("users", "POST");

  // Create roleOptions from envData.ROLES
  const roleOptions =
    envData.ROLES && !isFetching
      ? [
          { id: "all", name: "All Roles" },
          ...envData.ROLES.map((role) => ({
            id: role.id.toString(), // Ensure id is a string for comparison
            name: role.name.charAt(0).toUpperCase() + role.name.slice(1),
          })),
        ]
      : [{ id: "all", name: "All Roles" }]; // Fallback if ROLES is not available

  console.log("roleOptions", roleOptions);
  try {
    currentUser = JSON.parse(currentUserTemp);
  } catch (error) {
    console.error("Failed to parse @user-creds from localStorage:", error);
  }

  const [passwordToggel, setPasswordToggel] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    itemsPerPage: 6,
  });
  const [search, setSearch] = useState("");
  const [selectedRole, setSelectedRole] = useState("all");
  const [sortedData, setSortedData] = useState([]);
  const [currentItems, setCurrentItems] = useState([]);
  const [currentMode] = useState("Light");
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  useEffect(() => {
    let filteredData = allUsersList.filter((user) => {
      // Find role name for this user's role id
      const roleObj =
        envData.ROLES && envData.ROLES.find((r) => r.id === Number(user.role));
      const roleName = roleObj ? roleObj.name : "";

      const matchesSearch =
        (user.username &&
          user.username.toLowerCase().includes(search.toLowerCase())) ||
        (user.email &&
          user.email.toLowerCase().includes(search.toLowerCase())) ||
        (roleName && roleName.toLowerCase().includes(search.toLowerCase()));

      const matchesRole =
        selectedRole === "all" ||
        (user.role && user.role.toString() === selectedRole);

      return matchesSearch && matchesRole;
    });

    setSortedData(filteredData);
    setPagination((prev) => ({ ...prev, currentPage: 1 }));
  }, [search, allUsersList, selectedRole, envData.ROLES]);

  useEffect(() => {
    updateCurrentItems(sortedData, setCurrentItems);
  }, [sortedData, pagination]);

  const updateCurrentItems = (users, _setCurrItems) => {
    const indexOfLastItem = pagination.currentPage * pagination.itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - pagination.itemsPerPage;
    _setCurrItems(users.slice(indexOfFirstItem, indexOfLastItem));
  };

  const classes = useStyles();

  useEffect(() => {
    fetchAllUsers();
    setloading(false);
  }, []);

  const visibleColumns =
    currentUser?.role === 5 && !isModelOpen
      ? tableColumns
      : tableColumns.filter((col) => col.label !== "Actions");

  const handleAddUser = () => {
    setShowAddUser(true);
  };

  return (
    <Box boxShadow={1} padding={2} className={classes.theming}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
          gap: 2,
        }}
        className={classes.theming}
      >
        <Typography variant="h4" fontWeight={600}>
          Users
        </Typography>

        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <SearchField
            showPDFDownload={false}
            label="Search"
            search={search}
            setSearch={setSearch}
            placeholder="UserName, Role"
          />
          <CommonDropDown
            dropDownLabel="Filter by Role"
            menuData={roleOptions}
            menuValue={selectedRole}
            menuItemDisplay="name"
            menuItemValue="id"
            handleChange={(e) => setSelectedRole(e.target.value)}
            dropDownContainerStyle={{ minWidth: "150px" }}
          />
          <Button
            variant="contained"
            onClick={handleAddUser}
            sx={{
              borderRadius: 1,
              fontWeight: 500,
              textTransform: "none",
              bgcolor: "primary.main",
              "&:hover": {
                bgcolor: "primary.dark",
              },
            }}
            disabled={!hasUserPOSTAccess}
          >
            Add New User
          </Button>
        </Box>
      </Box>

      {hasUserGETAccess ? (
        <>
          <Box>
            <TableContainer
              component={Paper}
              sx={commonOuterContainerStyle}
              className="table border-radius-inner"
            >
              <Table aria-label="responsive table">
                <TableHeader
                  columns={visibleColumns}
                  currentMode={currentMode}
                />
                <TableBody>
                  {loading || isFetching ? (
                    Array.from({ length: 7 }).map((_, index) => (
                      <TableRow
                        key={index + "u_l_t" + "table_row_a"}
                        className={classes.tableRow}
                      >
                        {visibleColumns?.map((column, colIndex) => (
                          <TableCell key={colIndex} align="center">
                            <Skeleton variant="text" />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : currentItems?.length > 0 ? (
                    currentItems?.map((user, index) => (
                      <UserItem
                        isModelOpen={isModelOpen}
                        fetchAllUsers={fetchAllUsers}
                        key={uuidv4()}
                        user={user}
                        passwordToggel={passwordToggel}
                      />
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={visibleColumns.length} align="center">
                        <Typography
                          variant="h5"
                          color="textSecondary"
                          fontWeight="bold"
                        >
                          No Data
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>

          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
            }}
          >
            <Pagination
              totalRows={sortedData.length}
              pagination={pagination}
              setPagination={setPagination}
            />
          </Box>
        </>
      ) : (
        <NotAccessible />
      )}
    </Box>
  );
};

UserListTable.propTypes = {
  allUsersList: PropTypes.arrayOf(
    PropTypes.shape({
      _id: PropTypes.string.isRequired,
    }),
  ).isRequired,
  fetchAllUsers: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
  setloading: PropTypes.func.isRequired,
  isModelOpen: PropTypes.bool.isRequired,
  setShowAddUser: PropTypes.func.isRequired,
};

export default UserListTable;
