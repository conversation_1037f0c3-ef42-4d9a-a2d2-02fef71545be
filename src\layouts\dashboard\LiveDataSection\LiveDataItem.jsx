import React, { useState } from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
const LiveDataItem = ({ options }) => {
  const [data, setData] = useState([]);
  return (
    <div className="chartContainer">
      <h2>Live Data Chart</h2>
      <div className="chart">
        <Line options={options} data={data} />
      </div>
    </div>
  );
};

export default LiveDataItem;
