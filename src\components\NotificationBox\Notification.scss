// Notification Container
.notificationBoxContainer {
  margin: 0px;
  box-sizing: border-box;
  width: 100%;
  max-width: 20vw;
  max-height: 150vh;
  scroll-behavior: smooth;
  contain: content;
  overflow: scroll;
  box-shadow: rgba(0, 0, 0, 0.05) 0rem 1.25rem 1.6875rem 0rem;
  border-radius: 10px;
  padding-left: 1rem;
  padding-right: 1rem;
  min-height: 250px;

  .heading {
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.9;
    padding-left: 4px;
    margin-bottom: 0.4rem;
    width: 100%;
  }

  .notificationBoxWrapper {
    display: flex;
    flex-direction: column;
    width: 100%;

    .notificationList {
      //smooth loading
      animation: fadeInAnimation ease 1s;
      animation-iteration-count: 1;
      animation-fill-mode: forwards;

      @keyframes fadeInAnimation {
        0% {
          opacity: 0;
        }

        100% {
          opacity: 1;
        }
      }

      //

      display: flex;
      align-items: center;
      padding: 0.35rem 0.55rem;
      margin: 0.5rem 0;
      cursor: pointer;
      border-radius: 10px;

      // &:hover {
      // 	background-color: rgb(236, 240, 247);
      // }

      .iconContainer {
        width: 35px;
        height: 35px;
        margin-right: 0.5rem;

        img {
          width: 100%;
          height: 100%;
          border-radius: 8px;
        }
      }

      .notificationInfoContainer {
        display: flex;
        flex-direction: column;

        .title {
          span {
            font-size: 0.9rem;
            font-weight: 500;
          }

          font-size: 0.8rem;
        }

        .time {
          display: flex;
          align-items: center;
          padding-top: 0.11rem;

          span {
            margin-right: 0.4rem;
            font-size: 0.9rem;
            font-weight: 500;
          }

          font-size: 0.8rem;
        }
      }
    }
  }
}
