import * as React from "react";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { TextField } from "@mui/material";
import dayjs from "dayjs";
import PropTypes from "prop-types";

const MyDateTimePicker = ({
  label = "Date Time",
  value,
  onChange,
  disableFuture = false,
  maxDateTime = dayjs().add(5, "years"),
  minDateTime = dayjs().subtract(5, "years"),
  size = "small",
  onError = () => {},
  format = "DD/MM/YYYY HH:mm",
  views = ["year", "month", "day", "hours", "minutes"],
  slotProps = {},
  ...props
}) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DateTimePicker
        sx={{ maxHeight: "10px" }}
        label={label}
        value={value}
        onChange={onChange}
        disableFuture={disableFuture}
        minDateTime={minDateTime}
        maxDateTime={maxDateTime}
        inputFormat={format}
        views={views}
        ampm={false}
        onError={onError}
        slotProps={{
          textField: { size, ...slotProps.textField },
        }}
        // Fix for renderInput error in certain MUI versions
        renderInput={(params) => <TextField {...params} />}
        {...props}
      />
    </LocalizationProvider>
  );
};

MyDateTimePicker.propTypes = {
  label: PropTypes.string,
  value: PropTypes.object.isRequired, // dayjs object
  onChange: PropTypes.func.isRequired,
  disableFuture: PropTypes.bool,
  maxDateTime: PropTypes.object,
  minDateTime: PropTypes.object,
  size: PropTypes.oneOf(["small", "medium", "large"]),
  onError: PropTypes.func,
  format: PropTypes.string,
  views: PropTypes.arrayOf(PropTypes.string),
  slotProps: PropTypes.shape({
    textField: PropTypes.object,
  }),
};

export default MyDateTimePicker;
