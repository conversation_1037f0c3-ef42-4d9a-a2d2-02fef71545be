import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from "@mui/material";
import React from "react";
import { Box } from "@mui/system";
import { Close } from "@mui/icons-material";
import PropTypes from "prop-types";

const DIALOG_WIDTH = "24rem";
const DEFAULT_DESCRIPTION = "field";

const MessagePopup = ({
  open = false,
  handleClose = () => {},
  desc = DEFAULT_DESCRIPTION,
}) => {
  console.log("Here", open);

  return (
    <Dialog open={open}>
      <Box sx={{ px: 4, pt: 1, pb: 2.5, width: DIALOG_WIDTH }}>
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            position: "relative",
            fontWeight: "bold",
          }}
        >
          <Button
            onClick={handleClose}
            sx={{
              position: "absolute",
              right: "-1.75rem",
              top: "5%",
            }}
          >
            <Close color="error" />
          </Button>
        </DialogTitle>
        <DialogContent
          sx={{
            textAlign: "justify",
          }}
        >
          <Typography variant="h6">{desc}</Typography>
        </DialogContent>
        <DialogActions>{/* */}</DialogActions>
      </Box>
    </Dialog>
  );
};

MessagePopup.propTypes = {
  open: PropTypes.bool.isRequired, // Controls the dialog's visibility
  handleClose: PropTypes.func.isRequired, // Callback to close the dialog
  desc: PropTypes.string, // The message or description to display in the popup
};

MessagePopup.defaultProps = {
  open: false, // Default dialog visibility is closed
  desc: DEFAULT_DESCRIPTION, // Default description text
};
export default MessagePopup;
