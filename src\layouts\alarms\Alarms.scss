.alarmsContainer {
  .alarmManagerHeader {
    width: 100%;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #344767;
    padding: 1.5rem 1.2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .alarmManagerMain {
    padding: 2rem 1.5rem;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #344767;
    margin-top: 36px;
    .header {
      width: 100%;
      height: fit-content;
      margin-bottom: 1.5rem;
      padding: 0rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      div {
        display: flex;
        align-items: center;
        text-align: left;
        h2 {
          text-transform: capitalize;
          font-size: 20px;
          font-weight: 700;
          margin-left: 6px;
        }
      }
    }
    .tableContainer {
      // border-radius: 0.5rem;
      .insideTable {
        // border-radius: 0.5rem;
        min-width: 650;
        width: 100%;
      }
    }
  }

  .title {
    font-size: 20px;
    font-weight: 700;
  }

  .desc {
    padding: 0 1.2rem;

    p {
      font-size: 0.9rem;
      opacity: 0.7;
    }
  }

  .alarmsOuterContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    min-height: 60vh;
    @media (max-width: 500px) {
      padding: 0.5rem 0;
    }

    .alarmsInnerContainer {
      //smooth loading
      animation: fadeInAnimation ease 1s;
      animation-iteration-count: 1;
      animation-fill-mode: forwards;

      @keyframes fadeInAnimation {
        0% {
          opacity: 0;
        }

        100% {
          opacity: 1;
        }
      }
      width: 100%;
      display: flex;
      flex-flow: row wrap;
      align-items: center;
      justify-content: center;

      .singleAlarmContainer {
        width: 30%;
        height: 240px;
        padding: 1rem;
        border-radius: 16px;
        margin: 0.75rem;
      }
    }
  }
}

.alarmCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  // border-radius: 16px;
  margin: 0.75rem;
  width: 30%;
  height: 240px;
  border-radius: 16px !important;
  .alarmInfo {
    text-align: center;
    .alertName {
      font-size: 22px;
      font-weight: bold;
      margin-bottom: 8px;
      text-transform: capitalize;
      color: #000;
    }
  }
  .actionContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
  }
}
