import React, { useState, useEffect, useRef } from "react";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import { useStateContext } from "../../../context/ContextProvider";
import { addData } from "../../../services2/issueModule/IssueModule.services";
import ReactFlow, {
  ReactFlowProvider,
  addEdge,
  removeElements,
  Controls,
  Background,
  MiniMap,
} from "react-flow-renderer";
import useEventListener from "@use-it/event-listener";
import Popup from "reactjs-popup";
import * as htmlToImage from "html-to-image";
import { jsPDF } from "jspdf";
import { toastMessageSuccess, toastMessage } from "../../../tools/toast";
import CustomNode from "../CustomNodes/CustomNode";
import CustomNode2 from "../CustomNodes/CustomNode2";
import CustomNode3 from "../CustomNodes/CustomNode3";
import CustomNode4 from "../CustomNodes/CustomNode4";
import CustomNode5 from "../CustomNodes/CustomNode5";
import CustomNode6 from "../CustomNodes/CustomNode6";
import CustomNode7 from "../CustomNodes/CustomNode7";
import CustomNode8 from "../CustomNodes/CustomNode8";
import CustomNode9 from "../CustomNodes/CustomNode9";
import CustomNode10 from "../CustomNodes/CustomNode10";
import CustomNode11 from "../CustomNodes/CustomNode11";
import CustomNode12 from "../CustomNodes/CustomNode12";
import CustomNode13 from "../CustomNodes/CustomNode13";
import CustomNode14 from "../CustomNodes/CustomNode14";
import CustomNode15 from "../CustomNodes/CustomNode15";
import CustomNode16 from "../CustomNodes/CustomNode16";
import CustomNode17 from "../CustomNodes/CustomNode17";
import CustomNode18 from "../CustomNodes/CustomNode18";
import CustomNode19 from "../CustomNodes/CustomNode19";
import CustomNode20 from "../CustomNodes/CustomNode20";
import CustomNode21 from "../CustomNodes/CustomNode21";
import CustomNode22 from "../CustomNodes/CustomNode22";
import CustomNode23 from "../CustomNodes/CustomNode23";
import CustomNode24 from "../CustomNodes/CustomNode24";
import CustomNode25 from "../CustomNodes/CustomNode25";
import CustomNode26 from "../CustomNodes/CustomNode26";
import CustomNode27 from "../CustomNodes/CustomNode27";
import CustomNode28 from "../CustomNodes/CustomNode28";
import CustomNode29 from "../CustomNodes/CustomNode29";
import CustomNode30 from "../CustomNodes/CustomNode30";
import CustomNode31 from "../CustomNodes/CustomNode31";
import CustomNode32 from "../CustomNodes/CustomNode32";
import CustomNode33 from "../CustomNodes/CustomNode33";
import CustomNode34 from "../CustomNodes/CustomNode34";
import CustomNode35 from "../CustomNodes/CustomNode35";
import CustomNode36 from "../CustomNodes/CustomNode36";
import CustomNode37 from "../CustomNodes/CustomNode37";
import CustomNode38 from "../CustomNodes/CustomNode38";
import CustomNode39 from "../CustomNodes/CustomNode39";
import CustomNode40 from "../CustomNodes/CustomNode40";
import CustomNode41 from "../CustomNodes/CustomNode41";
import CustomNode42 from "../CustomNodes/CustomNode42";
import CustomNode43 from "../CustomNodes/CustomNode43";
import CustomNode44 from "../CustomNodes/CustomNode44";
import CustomNode45 from "../CustomNodes/CustomNode45";
import CustomNode46 from "../CustomNodes/CustomNode46";
import CustomNode47 from "../CustomNodes/CustomNode47";
import CustomNode48 from "../CustomNodes/CustomNode48";
import CustomNode49 from "../CustomNodes/CustomNode49";
import "reactjs-popup/dist/index.css";
import Sidebar from "./Sidebar";
import useUndo from "./use-history";
import { useNavigate } from "react-router-dom";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  MenuItem,
  Select,
  Tooltip,
} from "@mui/material";
import { ButtonBasic } from "../../buttons/Buttons";
import { Button, TextField } from "@mui/material";
import { convertBase64 } from "../../../hooks/useBase64";
import { useMachines } from "../../../services/machines/MachineContext";
import imageForNode from "../../../assets/images/node.jpg";
import { useIssueModuleChangeCount } from "../../../services2/issueModule/IssueModule.context";
//import { useAuth } from "../../hooks/AuthProvider";
import { Confirm } from "../../commons/confirm.component";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import { VscJson } from "react-icons/vsc";
import { SaveIcon } from "lucide-react";
import { sharedCss } from "../../../styles/sharedCss";
import { makeStyles } from "@mui/styles";

//import { useAuth } from "../../context/AuthContext";

//const addissueModulecfr = useCreateMachineCfr();

const useCustomStyles = makeStyles((theme) => ({
  issueModulePageContainer: {
    height: "fit-content",
  },
}));

// const { currentUser } = useAuth();
const nodeTypes = {
  selectorNode: CustomNode,
  customNode: CustomNode2,
  nodeWithImageText: CustomNode3,
  nodeWithImageOnly: CustomNode4,
  nodeWithTextAndArea: CustomNode5,
  mainCustomNode: CustomNode6,
  nodeWith3Text: CustomNode7,
  nodeWith2TextArea: CustomNode8,
  nodeWith3TextArea: CustomNode9,
  nodeWith3Image: CustomNode10,
  nodeWith2Image: CustomNode11,
  nodeWith2Field1Area: CustomNode12,
  nodeWith2Field1Image: CustomNode13,
  nodeWith2Area1Field: CustomNode14,
  nodeWith2Area1Image: CustomNode15,
  nodeWith2Image1Field: CustomNode16,
  nodeWith2Image1Area: CustomNode17,
  nodeWithCode: CustomNode18,
  nodeWithOnlyText: CustomNode19,
  squareNode: CustomNode20,
  circleNode: CustomNode21,
  codethree: CustomNode22,
  oneTextTwoCode: CustomNode23,
  oneAreaTwoCode: CustomNode24,
  oneImageTwoCode: CustomNode25,
  twoTextOneCode: CustomNode26,
  twoAreaOneCode: CustomNode27,
  twoImageOneCode: CustomNode28,
  oneConeAoneI: CustomNode29,
  oneConeToneI: CustomNode30,
  oneConeAoneT: CustomNode31,
  onlyCode: CustomNode32,
  oneCodeoneImage: CustomNode33,
  oneCodeoneArea: CustomNode34,
  fourElements: CustomNode35,
  twoTexttwoArea: CustomNode36,
  twoTexttwoImage: CustomNode37,
  twoTexttwoCode: CustomNode38,
  twoAreatwoCode: CustomNode39,
  twoAreatwoImage: CustomNode40,
  twoImagetwoCode: CustomNode41,
  twoTextOneAreaOneImage: CustomNode42,
  twoTextOneAreaOneCode: CustomNode43,
  twoTextOneImageOneCode: CustomNode44,
  twoImageoneTextoneArea: CustomNode45,
  twoImageoneTextoneCode: CustomNode46,
  twoCodeoneTextoneArea: CustomNode47,
  twoCodeoneTextoneImage: CustomNode48,
  nodeWithOnlyTextArea: CustomNode49,
};
const initialElements = [];

const DnDFlow = (props) => {
  const { blockRouteNav, handleRouteNavigation } = props;
  const [toggle, setToggle] = useState(true);
  const handleToggleSidebar = (value) => {
    setToggle(value);
  };
  const { currentMode, currentColorLight } = useStateContext();
  const { undo } = useUndo(initialElements);
  const [reactFlowInstance, setReactFlowInstance] = useState(null);
  const [elements, setElements] = useState(initialElements);
  const { machines } = useMachines();
  let history = useNavigate();
  const commonCss = sharedCss();
  const customCss = useCustomStyles();

  // PDF confirm modal prompt
  const [showPdfConfirmModal, setShowPdfConfirmModal] = useState(false);

  // Save project modal prompt
  const [showSaveConfirmModal, setShowSaveConfirmModal] = useState(false);

  const onConnect = (params) =>
    setElements((els) =>
      addEdge(
        {
          ...params,
          type: "default",
          animated: true,
          style: { stroke: "#68C4BA", cursor: "pointer" },
          label: "Edge Label",
          labelStyle: {
            fill: "#000",
            fontWeight: "800",
            fontSize: "1rem",
            cursor: "pointer",
          },
          arrowHeadType: "arrow",
        },
        els,
      ),
    );
  const onElementsRemove = (elementsToRemove) =>
    setElements((els) => removeElements(elementsToRemove, els));

  const onLoad = (_reactFlowInstance) =>
    setReactFlowInstance(_reactFlowInstance);

  const onDragOver = (event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  };

  const reactFlowWrapper = useRef(null);

  const onDrop = (event) => {
    event.preventDefault();
    handleRouteNavigation(true);

    const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
    const type = event.dataTransfer.getData("application/reactflow");
    const design = event.dataTransfer.getData("application/reactflow2");
    const imageUrl = imageForNode;
    // Get the screen width and height
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    // Calculate the position for the drop, ensuring it's within the screen limits
    const position = reactFlowInstance.project({
      x: Math.min(event.clientX, screenWidth - 1250), // Ensure the position is within screen width
      y: Math.min(event.clientY, screenHeight - 500), // Ensure the position is within screen height
    });

    if (design === "twoCodeoneTextoneImage") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            label: "Text Field",
            source: `${imageUrl}`,
            code: `function fun(){
    name = "Daya"
  }`,
            code2: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoCodeoneTextoneArea") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            label: "Text Field",
            textarea: "Text Area",
            code: `function fun(){
    name = "Daya"
  }`,
            code2: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoImageoneTextoneCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            label: "Text Field",
            source: `${imageUrl}`,
            source2: `${imageUrl}`,
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }
    if (design === "twoImageoneTextoneArea") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            label: "Text Field",
            textarea: "Text Area",
            source: `${imageUrl}`,
            source2: `${imageUrl}`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoTextOneImageOneCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            label: "Text Field",
            label2: "2nd Text Field",
            source: `${imageUrl}`,
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoTextOneAreaOneImage") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            label: "Text Field",
            label2: "2nd Text Field",
            textarea: "Text Area",
            source: `${imageUrl}`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "nodeWithOnlyTextArea") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            textarea: "Text Area",
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoTextOneAreaOneCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            label: "Text Field",
            label2: "2nd Text Field",
            textarea: "Text Area",
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoAreatwoCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            textarea: "Text Area",
            textarea2: "2nd Text Area",
            code: `function fun(){
    name = "Daya"
  }`,
            code2: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoTexttwoArea") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            label: "Text Field",
            label2: "2nd Text Field",
            textarea: "Text Area",
            textarea2: "2nd Text Area",
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoTexttwoImage") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            label: "Text Field",
            label2: "2nd Text Field",
            source: `${imageUrl}`,
            source2: `${imageUrl}`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoImagetwoCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            source: `${imageUrl}`,
            source2: `${imageUrl}`,
            code: `function fun(){
    name = "Daya"
  }`,
            code2: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoAreatwoImage") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            textarea: "Text Area",
            textarea2: "2nd Text Area",
            source: `${imageUrl}`,
            source2: `${imageUrl}`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoTexttwoCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            label: "Text Field",
            label2: "2nd Text Field",
            code: `function fun(){
    name = "Daya"
  }`,
            code2: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "fourElements") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            background: "#fff",
            radius: 8,
            label: "Text Field",
            textarea: "Text Area",
            source: `${imageUrl}`,
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "onlyCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "oneCodeoneImage") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            source: `${imageUrl}`,
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "oneCodeoneArea") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            textarea: "Text Area",
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "threecode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            code: `function fun(){
    name = "Daya"
  }`,
            code2: `function fun2(){
    name = "Hello"
  }`,
            code3: `function fun3(){
    name = "Bye!"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "oneTextTwoCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            label: "Text Field",
            code: `function fun(){
    name = "Daya"
  }`,
            code2: `function fun2(){
    name = "Hello"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "oneAreaTwoCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            textarea: "Text Area",
            code: `function fun(){
    name = "Daya"
  }`,
            code2: `function fun2(){
    name = "Hello"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "oneImageTwoCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            source: `${imageUrl}`,
            code: `function fun(){
    name = "Daya"
  }`,
            code2: `function fun2(){
    name = "Hello"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoTextOneCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            label: "Text Field",
            label2: "2nd Text Field",
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoAreaOneCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            textarea: "Text Area",
            textarea2: "2nd Text Area",
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "twoImageOneCode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            source: `${imageUrl}`,
            source2: `${imageUrl}`,
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "oneConeAoneI") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            textarea: "Text Area",
            source: `${imageUrl}`,
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "oneConeAoneT") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            label: "Text Field",
            textarea: "Text Area",
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "oneConeToneI") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            background: "#fff",
            label: "Text Field",
            source: `${imageUrl}`,
            code: `function fun(){
    name = "Daya"
  }`,
          },
          style: {
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "codeNode") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            label: `Code Node`,
            width: width,
            height: height,
            settingWidth: callbackFunction,
            settingHeight: callbackFunction2,
            background: "#fff",
            code: `function daya(){
    name = "Daya"
  }`,
          },
        }),
      );
      return;
    }

    if (design === "circle") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: { label: `node`, background: "#fff" },
          style: {
            textAlign: "center",
            borderRadius: "50%",
            width: "100px",
            height: "100px",
          },
        }),
      );
      return;
    }

    if (design === "square") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: { label: `node`, background: "#fff" },
        }),
      );
      return;
    }

    if (design === "rectangleWithImageArea") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            source: `${imageUrl}`,
            textarea: "Text Area",
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "rectangleWithImage") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            label: `node`,
            source: `${imageUrl}`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "rectangleWithImageText") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            label: `node`,
            source: `${imageUrl}`,
            textarea: `Text Area`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "onlyImage") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            source: `${imageUrl}`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          },
        }),
      );
      return;
    }

    if (design === "nodeTextAndArea") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            label: `Text Field`,
            textarea: "Text Area ",
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "text2") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            label: `Text Field`,
            label2: `2nd Text Field`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "290px",
          },
        }),
      );
      return;
    }
    if (design === "text3") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            label: `Text Field`,
            label2: `2nd Text Field`,
            label3: `3rd Text Field`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "280px",
          },
        }),
      );
      return;
    }

    if (design === "textarea2") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            textarea: `Text area`,
            textarea2: `2nd Text area`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "textarea3") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            textarea: `Text area`,
            textarea2: `2nd Text area`,
            textarea3: `3rd Text area`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "290px",
          },
        }),
      );
      return;
    }

    if (design === "image2") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            source: `${imageUrl}`,
            source2: `${imageUrl}`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "220px",
            height: "2100px",
          },
        }),
      );
      return;
    }

    if (design === "image3") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            source: `${imageUrl}`,
            source2: `${imageUrl}`,
            source3: `${imageUrl}`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "220px",
            height: "180px",
          },
        }),
      );
      return;
    }

    if (design === "field2area1") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            label: `1st Text Field`,
            label2: `2nd Text Field`,
            textarea: `Text Area`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "field2image1") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            label: `1st Text Field`,
            label2: `2nd Text Field`,
            source: `${imageUrl}`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "280px",
          },
        }),
      );
      return;
    }

    if (design === "area2field1") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            textarea: `1st Text Area`,
            textarea2: `2nd Text Area`,
            label: `Text Field`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "280px",
          },
        }),
      );
      return;
    }

    if (design === "area2image1") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            textarea: `1st Text Area`,
            textarea2: `2nd Text Area`,
            source: `${imageUrl}`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "image2field1") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            label: `Text Field`,
            source: `${imageUrl}`,
            source2: `${imageUrl}`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "255px",
          },
        }),
      );
      return;
    }

    if (design === "image2area1") {
      setElements((es) =>
        es.concat({
          id: (es.length + 1).toString(),
          type,
          position,
          data: {
            radius: 8,
            textarea: `Text Area`,
            source: `${imageUrl}`,
            source2: `${imageUrl}`,
            background: "#fff",
          },
          style: {
            textAlign: "center",
            width: "255px",
          },
        }),
      );
      return;
    }

    setElements((es) =>
      es.concat({
        id: (es.length + 1).toString(),
        type,
        position,
        data: { label: `node`, background: "#fff" },
        style: {
          textAlign: "center",
          borderColor: "#ff0072",
          width: "172px",
        },
      }),
    );
  };

  // Node Properties
  const [nodeName, setNodeName] = useState("");
  const [nodeName2, setNodeName2] = useState("");
  const [nodeName3, setNodeName3] = useState("");
  const [nodeBg, setNodeBg] = useState("#fff");
  const [nodeImage, setNodeImage] = useState("");
  const [nodeImage2, setNodeImage2] = useState("");
  const [nodeImage3, setNodeImage3] = useState("");
  const [nodeX, setNodeX] = useState("");
  const [nodeY, setNodeY] = useState("");
  const [element, setElement] = useState({});
  const [nodeHidden, setNodeHidden] = useState(false);
  const [textArea, setTextArea] = useState("");
  const [textArea2, setTextArea2] = useState("");
  const [textArea3, setTextArea3] = useState("");
  const [fileName, setFileName] = useState("");
  const [error, setError] = useState(false);
  const [hideText1, setHideText1] = useState(false);
  const [hideText2, setHideText2] = useState(false);
  const [hideText3, setHideText3] = useState(false);
  const [hideTextArea1, setHideTextArea1] = useState(false);
  const [hideTextArea2, setHideTextArea2] = useState(false);
  const [hideTextArea3, setHideTextArea3] = useState(false);
  const [hideImage, setHideImage] = useState(false);
  const [hideImage2, setHideImage2] = useState(false);
  const [hideImage3, setHideImage3] = useState(false);
  const [hideCode, setHideCode] = useState(false);
  const [hideCode2, setHideCode2] = useState(false);
  const [hideCode3, setHideCode3] = useState(false);
  const [code, setCode] = useState("");
  const [code2, setCode2] = useState("");
  const [code3, setCode3] = useState("");
  const [width, setWidth] = useState(300);
  const [height, setHeight] = useState(200);
  const [radius, setRadius] = useState("");

  // Edge Properties

  const [edgeLabel, setEdgeLabel] = useState("Edge Label");
  const [edgeLabelStyle, setEdgeLabelStyle] = useState({});
  const [edgeType, setEdgeType] = useState("default");
  const [edgeAnimated, setEdgeAnimated] = useState(true);
  const [edgeStyle, setEdgeStyle] = useState({});
  const [edgeArrowHead, setEdgeArrowHead] = useState("arrow");
  const [showEdgeProperties, setShowEdgeProperties] = useState(false);

  const callbackFunction = (childData) => {
    setWidth(childData);
  };

  const callbackFunction2 = (childData) => {
    setHeight(childData);
  };

  const isEdge = (val) => {
    if (
      val === "default" ||
      val === "straight" ||
      val === "step" ||
      val === "smoothstep"
    ) {
      return true;
    } else {
      return false;
    }
  };

  const onElementClick = (event, elem) => {
    handleToggleSidebar(false);
    handleRouteNavigation(true);
    elements?.map((element) => {
      if (elem.id === element.id) {
        setElement(element);
        if (isEdge(element.type)) {
          setEdgeType(element.type);
          setEdgeLabel(element.label);
          setEdgeLabelStyle(element.labelStyle);
          setEdgeAnimated(element.animated);
          setEdgeStyle(element.style);
          setEdgeArrowHead(element.arrowHeadType);
        } else {
          setNodeX(element.position.x);
          setNodeY(element.position.y);
          setNodeName(element.data.label);
          setNodeName2(element.data.label2);
          setNodeName3(element.data.label3);
          setNodeImage(element.data.source);
          setNodeImage2(element.data.source2);
          setNodeImage3(element.data.source3);
          setTextArea(element.data.textarea);
          setTextArea2(element.data.textarea2);
          setTextArea3(element.data.textarea3);
          setCode(element.data.code);
          setCode2(element.data.code2);
          setCode3(element.data.code3);
          setNodeBg(element.data.background);
          setRadius(element.data.radius);
        }

        // Edge
        if (isEdge(element.type)) {
          setShowEdgeProperties(true);
        }

        // Node

        if (
          element.type === "nodeWithOnlyText" ||
          element.type === "circleNode" ||
          element.type === "squareNode"
        ) {
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "selectorNode") {
          setHideText1(true);
          setHideImage(true);
          setHideText2(false);
          setHideText3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "customNode") {
          setHideText1(true);
          setHideImage(true);
          setHideTextArea1(true);
          setHideText2(false);
          setHideText3(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWithOnlyTextArea") {
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWithImageText") {
          setHideText1(false);
          setHideImage(true);
          setHideTextArea1(true);
          setHideText2(false);
          setHideText3(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWithImageOnly") {
          setHideText1(false);
          setHideImage(true);
          setHideTextArea1(false);
          setHideText2(false);
          setHideText3(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWithTextAndArea") {
          setHideText1(true);
          setHideImage(false);
          setHideTextArea1(true);
          setHideText2(false);
          setHideText3(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "mainCustomNode") {
          setHideText1(true);
          setHideText2(true);
          setHideImage(false);
          setHideTextArea1(false);
          setHideText3(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "nodeWith3Text") {
          setHideText1(true);
          setHideText2(true);
          setHideText3(true);
          setHideImage(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWith2TextArea") {
          setHideTextArea1(true);
          setHideTextArea2(true);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideImage(false);
          setHideTextArea3(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "nodeWith3TextArea") {
          setHideTextArea1(true);
          setHideTextArea2(true);
          setHideTextArea3(true);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWith3Image") {
          setHideImage(true);
          setHideImage2(true);
          setHideImage3(true);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWith2Field1Area") {
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(true);
          setHideText3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWith2Field1Image") {
          setHideImage(true);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(true);
          setHideText3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWith2Area1Field") {
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(true);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWith2Area1Image") {
          setHideImage(true);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(true);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWith2Image") {
          setHideImage(true);
          setHideImage2(true);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWith2Image1Field") {
          setHideImage(true);
          setHideImage2(true);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWith2Image1Area") {
          setHideImage(true);
          setHideImage2(true);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "nodeWithCode") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "codethree") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(true);
          setHideCode3(true);
          setShowEdgeProperties(false);
        }
        if (element.type === "oneTextTwoCode") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(true);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "oneAreaTwoCode") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(true);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "oneImageTwoCode") {
          setHideCode(true);
          setHideImage(true);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(true);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "twoTextOneCode") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(true);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "twoTextOneCode") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(true);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "twoAreaOneCode") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(true);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "twoImageOneCode") {
          setHideCode(true);
          setHideImage(true);
          setHideImage2(true);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "oneConeAoneI") {
          setHideCode(true);
          setHideImage(true);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "oneConeAoneT") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "oneConeToneI") {
          setHideCode(true);
          setHideImage(true);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "onlyCode") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "oneCodeoneArea") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "oneCodeoneImage") {
          setHideCode(true);
          setHideImage(true);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "fourElements") {
          setHideCode(true);
          setHideImage(true);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "twoTexttwoArea") {
          setHideCode(false);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(true);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(true);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "twoTexttwoImage") {
          setHideCode(false);
          setHideImage(true);
          setHideImage2(true);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(true);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "twoTexttwoCode") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(true);
          setHideText3(false);
          setHideCode2(true);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "twoAreatwoCode") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(true);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(true);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "twoAreatwoImage") {
          setHideCode(false);
          setHideImage(true);
          setHideImage2(true);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(true);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "twoImagetwoCode") {
          setHideCode(true);
          setHideImage(true);
          setHideImage2(true);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(false);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(true);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "twoTextOneAreaOneImage") {
          setHideCode(false);
          setHideImage(true);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(true);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "twoTextOneAreaOneCode") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(true);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "twoTextOneImageOneCode") {
          setHideCode(true);
          setHideImage(true);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(true);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "twoImageoneTextoneArea") {
          setHideCode(false);
          setHideImage(true);
          setHideImage2(true);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "twoImageoneTextoneCode") {
          setHideCode(true);
          setHideImage(true);
          setHideImage2(true);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(false);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }

        if (element.type === "twoCodeoneTextoneArea") {
          setHideCode(true);
          setHideImage(false);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(true);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(true);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
        if (element.type === "twoCodeoneTextoneImage") {
          setHideCode(true);
          setHideImage(true);
          setHideImage2(false);
          setHideImage3(false);
          setHideTextArea1(false);
          setHideTextArea2(false);
          setHideTextArea3(false);
          setHideText1(true);
          setHideText2(false);
          setHideText3(false);
          setHideCode2(true);
          setHideCode3(false);
          setShowEdgeProperties(false);
        }
      }
    });
  };

  const onDragEnd = (event, element) => {
    setElement(element);
    setNodeX(element.position.x);
    setNodeY(element.position.y);
    handleRouteNavigation(true);
  };

  // Edge

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el = { ...el, type: edgeType };
        }
        return el;
      }),
    );
  }, [edgeType, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el = { ...el, label: edgeLabel };
        }
        return el;
      }),
    );
  }, [edgeLabel, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el = { ...el, animated: edgeAnimated };
        }
        return el;
      }),
    );
  }, [edgeAnimated, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el = { ...el, arrowHeadType: edgeArrowHead };
        }
        return el;
      }),
    );
  }, [setEdgeArrowHead, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.labelStyle = {
            ...el.labelStyle,
            fontSize: edgeLabelStyle.fontSize,
          };
        }
        return el;
      }),
    );
  }, [edgeLabelStyle, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.labelStyle = {
            ...el.labelStyle,
            fill: edgeLabelStyle.fill,
          };
        }
        return el;
      }),
    );
  }, [edgeLabelStyle, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.labelStyle = {
            ...el.labelStyle,
            fontWeight: edgeLabelStyle.fontWeight,
          };
        }
        return el;
      }),
    );
  }, [edgeLabelStyle, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.style = {
            ...el.style,
            stroke: edgeStyle.stroke,
          };
        }
        return el;
      }),
    );
  }, [edgeStyle, setElements]);

  // Node
  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = {
            ...el.data,
            radius: radius,
          };
        }
        return el;
      }),
    );
  }, [radius, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = {
            ...el.data,
            label: nodeName,
          };
        }
        return el;
      }),
    );
  }, [nodeName, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = {
            ...el.data,
            label2: nodeName2,
          };
        }
        return el;
      }),
    );
  }, [nodeName2, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = {
            ...el.data,
            label3: nodeName3,
          };
        }
        return el;
      }),
    );
  }, [nodeName3, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = { ...el.data, textarea: textArea };
        }

        return el;
      }),
    );
  }, [textArea, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = { ...el.data, textarea2: textArea2 };
        }

        return el;
      }),
    );
  }, [textArea2, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = { ...el.data, textarea3: textArea3 };
        }

        return el;
      }),
    );
  }, [textArea3, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = { ...el.data, background: nodeBg };
        }
        return el;
      }),
    );
  }, [nodeBg, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = { ...el.data, code: code };
        }
        return el;
      }),
    );
  }, [code, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = { ...el.data, code2: code2 };
        }
        return el;
      }),
    );
  }, [code2, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = { ...el.data, code3: code3 };
        }
        return el;
      }),
    );
  }, [code3, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = { ...el.data, width: width };
        }

        return el;
      }),
    );
  }, [width, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = { ...el.data, height: height };
        }

        return el;
      }),
    );
  }, [height, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.position = {
            ...el.position,
            x: nodeX,
          };
        }

        return el;
      }),
    );
  }, [nodeX, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.position = {
            ...el.position,
            y: nodeY,
          };
        }

        return el;
      }),
    );
  }, [nodeY, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = {
            ...el.data,
            source: nodeImage,
          };
        }

        return el;
      }),
    );
  }, [nodeImage, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = {
            ...el.data,
            source2: nodeImage2,
          };
        }

        return el;
      }),
    );
  }, [nodeImage2, setElements]);

  useEffect(() => {
    setElements((els) =>
      els?.map((el) => {
        if (el.id === element.id) {
          el.data = {
            ...el.data,
            source3: nodeImage3,
          };
        }

        return el;
      }),
    );
  }, [nodeImage3, setElements]);

  const handleResetSideBarData = () => {
    // Node properties

    setNodeName("");
    setNodeName2("");
    setNodeName3("");
    setNodeBg("#fff");
    setNodeImage(imageForNode);
    setNodeImage2(imageForNode);
    setNodeImage3(imageForNode);
    setNodeX("");
    setNodeY("");
    setElement({});
    setNodeHidden(false);
    setTextArea("");
    setTextArea2("");
    setTextArea3("");
    setHideText1(false);
    setHideText2(false);
    setHideText3(false);
    setHideTextArea1(false);
    setHideTextArea2(false);
    setHideTextArea3(false);
    setHideImage(false);
    setHideImage2(false);
    setHideImage3(false);
    setHideCode(false);
    setHideCode2(false);
    setHideCode3(false);
    setCode("");
    setCode2("");
    setCode3("");
    setWidth(300);
    setHeight(200);
    setRadius("");

    // Edge Properties

    setEdgeLabel("");
    setEdgeLabelStyle({});
    setEdgeType("default");
    setEdgeAnimated(true);
    setEdgeStyle({});
    setEdgeArrowHead("arrow");
    setShowEdgeProperties(false);
  };

  // Function to delete an edge by its ID
  const handleDeleteEdge = (edgeId) => {
    console.log("edge", edgeId);
    // Filter out the edge with the specific id from the elements array
    setElements((prevElements) =>
      prevElements.filter((element) => element.id !== edgeId),
    );

    // Hide right sidebar having the node details on deleting the node
    handleToggleSidebar(!toggle);

    // To reset right sidebar to default
    handleResetSideBarData();
  };

  // Function to delete a node by its ID
  const handleDeleteNode = (nodeId) => {
    console.log("node", nodeId);
    // Remove the node with the given id
    setElements((prevElements) =>
      prevElements.filter((element) => element.id !== nodeId),
    );

    // Remove edges that reference this node (both as source and target)
    setElements((prevElements) =>
      prevElements.filter(
        (element) => !(element.source === nodeId || element.target === nodeId),
      ),
    );

    // Hide right sidebar having the node details on deleting the node
    handleToggleSidebar(!toggle);

    // To reset right sidebar to default
    handleResetSideBarData();
  };
  const onImageChange = async (event) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      const base64 = await convertBase64(file);
      setNodeImage(base64);
    }
  };

  const onImageChange2 = async (event) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      const base64 = await convertBase64(file);
      setNodeImage2(base64);
    }
  };

  const onImageChange3 = async (event) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      const base64 = await convertBase64(file);
      setNodeImage3(base64);
    }
  };
  const { issueCount, setIssueCount } = useIssueModuleChangeCount();

  const saveFile = async () => {
    var data = {
      name: fileName,
      mid: filteredMachine,
      values: JSON.stringify(elements),
      date: new Date().toISOString(),
    };
    const date = new Date();
    // const data2 = {
    //   activity: "issue module created",
    //   dateTime: date,

    //   description: "an issue module is created",
    //   machine: filteredMachine,
    //   module: "Machine",
    //   username: currentUser.username,
    // };
    // addissueModulecfr(data2);
    // firestore code
    await addData("issueModule", data).then(() => {
      setIssueCount(issueCount + 1);
      handleRouteNavigation(false);
      toastMessageSuccess({
        message: `Successfully saved the Project ${fileName}`,
      });
      history("/issues");
    });
  };

  const download = (content, fileName, contentType) => {
    const a = document.createElement("a");
    const file = new Blob([content], { type: contentType });
    a.href = URL.createObjectURL(file);
    a.download = fileName;
    a.click();
  };

  const exportJson = () => {
    if (window.confirm("Download the file Now")) {
      download(JSON.stringify(elements), "data.json", "text/plain");
    }
  };

  function KeyPress(e) {
    // eslint-disable-next-line no-restricted-globals
    var evtobj = window.event ? event : e;
    if (evtobj.keyCode === 90 && evtobj.ctrlKey) {
      undo();
    }
  }
  function handler({ key }) {
    KeyPress();
  }

  useEventListener("keydown", handler);

  const [filteredMachine, setFilteredMachine] = useState("");
  const handleFilter = (value) => {
    setFilteredMachine(value);
  };

  const handleShowConfirmPdfExport = () => setShowPdfConfirmModal(true);
  const handleHideConfirmPdfExport = () => setShowPdfConfirmModal(false);

  const handleDownloadPdf = () => {
    htmlToImage
      .toPng(document.getElementById("reactflow-wrapper"), {
        quality: 1,
      })
      .then(function (dataUrl) {
        var link = document.createElement("a");
        link.download = "chart.jpeg";
        const pdf = new jsPDF();
        const imgProps = pdf.getImageProperties(dataUrl);
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
        pdf.addImage(dataUrl, "PNG", 0, 0, pdfWidth, pdfHeight);
        pdf.save("chart.pdf");
      })
      .catch((error) => {
        toastMessage({ message: error?.message ?? "An error occurred" });
      })
      .finally(() => {
        handleHideConfirmPdfExport();
      });
  };

  const handleShowSaveProject = () => setShowSaveConfirmModal(true);
  const handleHideSaveProject = () => setShowSaveConfirmModal(false);

  const handleMachineDropdownChange = (e) => handleFilter(e.target.value);

  const handleTextInputChange = (evt) => {
    let value = evt.target.value;
    if (value.trim() !== "" || value.length === 0) {
      setFileName(value);
    }
  };

  const handleTextInputBlur = () => {
    if (!fileName.trim()) {
      setFileName(""); // Reset if only spaces were entered
    }
  };

  const handleSave = () => {
    if (!fileName) {
      toastMessage({
        message: "Please provide Name of the project",
      });
    } else if (!filteredMachine) {
      toastMessage({
        message: "Please select Machine of the project",
      });
    } else {
      saveFile();
      // setTimeout(() => {
      //   window.location.reload();
      // }, 50);
      handleHideSaveProject();
    }
  };

  const handleCloseSave = () => {
    handleHideSaveProject();
    setFileName("");
    setFilteredMachine("");
  };

  return (
    <main className={customCss.issueModulePageContainer}>
      <header
        className={commonCss.headingContainer}
        style={{ padding: "1rem" }}
      >
        <div className="back-button">
          <Tooltip title="Back to Issues">
            <IconButton onClick={() => history("/issues")}>
              <ArrowBackIcon />
            </IconButton>
          </Tooltip>
        </div>
        <div className="header-right">
          <div className="btn-container">
            {/*
            <Popup
              trigger={<ButtonBasic buttonTitle={"Export to PDF"} />}
              modal
              nested
            >
              {(close) => (
                <div
                  className="modal"
                  style={
                    currentMode === "Dark"
                      ? { backgroundColor: "#212B36", color: "white" }
                      : {}
                  }
                >
                  <div
                    className="header"
                    style={currentMode === "Dark" ? { color: "white" } : {}}
                  >
                    Download PDF
                  </div>
                  <div className="content">
                    <p>
                      Before clicking on download button to download the pdf
                      file, make sure you hide the MiniMap for clear picture of
                      graph only.
                    </p>
                  </div>
                  <div className="actions">
                    <Button variant="contained" color="error" onClick={close}>
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      color="success"
                      onClick={() => {
                        htmlToImage
                          .toPng(document.getElementById("reactflow-wrapper"), {
                            quality: 1,
                          })
                          .then(function (dataUrl) {
                            var link = document.createElement("a");
                            link.download = "chart.jpeg";
                            const pdf = new jsPDF();
                            const imgProps = pdf.getImageProperties(dataUrl);
                            const pdfWidth = pdf.internal.pageSize.getWidth();
                            const pdfHeight =
                              (imgProps.height * pdfWidth) / imgProps.width;
                            pdf.addImage(
                              dataUrl,
                              "PNG",
                              0,
                              0,
                              pdfWidth,
                              pdfHeight
                            );
                            pdf.save("chart.pdf");
                          });
                        close();
                      }}
                    >
                      Download
                    </Button>
                  </div>
                </div>
              )}
            </Popup>
            */}
            <Tooltip title="Export to PDF">
              <IconButton onClick={handleShowConfirmPdfExport}>
                <PictureAsPdfIcon
                  style={{
                    color: "red",
                  }}
                />
              </IconButton>
            </Tooltip>
            <Confirm
              dialogOpen={showPdfConfirmModal}
              dialogTitle={"Download PDF"}
              dialogBodyContent={`Before clicking on download button to download the pdf
                file, make sure you hide the MiniMap for clear picture of
                graph only.`}
              dialogCancelContent={"Cancel"}
              dialogConfirmContent={"Download"}
              handleConfirm={handleDownloadPdf}
              handleClose={handleHideConfirmPdfExport}
            />
          </div>
          <div className="btn-container">
            <Tooltip title="Export to JSON">
              <IconButton onClick={exportJson}>
                <VscJson />
              </IconButton>
            </Tooltip>
          </div>
          <div className="btn-container">
            {/*
            <Popup
              trigger={
                <ButtonBasic
                  buttonTitle={"Save"}
                  onClick={() => {
                    handleRouteNavigation(false);
                  }}
                />
              }
              modal
              nested
            >
              {(close) => (
                <div
                  className="modal"
                  style={
                    currentMode === "Dark"
                      ? { backgroundColor: "#212B36", color: "white" }
                      : {}
                  }
                >
                  <div
                    className="header"
                    style={currentMode === "Dark" ? { color: "white" } : {}}
                  >
                    {" "}
                    Save File{" "}
                  </div>
                  <div className="content">
                    <div className="input-field-container">
                      <label
                        style={currentMode === "Dark" ? { color: "white" } : {}}
                      >
                        Name of the File:
                      </label>
                      <TextField
                        fullWidth
                        variant="outlined"
                        value={fileName || ""}
                        placeholder="New Project"
                        onChange={(evt) => {
                          let value = evt.target.value;
                          if (value.trim() !== "" || value.length === 0) {
                            setFileName(value);
                          }
                        }}
                        onBlur={() => {
                          if (!fileName.trim()) {
                            setFileName(""); // Reset if only spaces were entered
                          }
                        }}
                      />
                    </div>
                    <div className="input-field-container">
                      <label
                        style={currentMode === "Dark" ? { color: "white" } : {}}
                        htmlFor="machines"
                      >
                        Choose Machine
                      </label>
                      <Select
                        variant="outlined"
                        fullWidth
                        name="machines"
                        value={filteredMachine}
                        onChange={(e) => handleFilter(e.target.value)}
                      >
                        <MenuItem className="option-machine" value={""}>
                          {"Machine Not Selected"}
                        </MenuItem>
                        {machines?.map((machine) => {
                          return (
                            <MenuItem value={machine?._id}>
                              {machine?.title}
                            </MenuItem>
                          );
                        })}
                      </Select>
                    </div>
                    {fileName === "" && error ? (
                      <h3
                        style={{
                          color: "red",
                          marginTop: "16px",
                        }}
                      >
                        Please provide name of the file
                      </h3>
                    ) : (
                      ""
                    )}
                  </div>
                  <div className="actions">
                    <Button variant="contained" color="error" onClick={close}>
                      Cancel
                    </Button>
                    <Button
                      onClick={() => {
                        if (!fileName) {
                          toastMessage({
                            message: "Please provide Name of the project",
                          });
                        } else if (!filteredMachine) {
                          toastMessage({
                            message: "Please select Machine of the project",
                          });
                        } else {
                          saveFile();
                          // setTimeout(() => {
                          //   window.location.reload();
                          // }, 50);
                          close();
                        }
                      }}
                      variant="contained"
                      color="success"
                    >
                      Save
                    </Button>
                  </div>
                </div>
              )}
            </Popup>
             */}
            <Tooltip title="Save Project">
              <IconButton onClick={handleShowSaveProject}>
                <SaveIcon style={{ color: "green" }} />
              </IconButton>
            </Tooltip>
            <Confirm
              dialogOpen={showSaveConfirmModal}
              dialogTitle={"Save project"}
              useTextInputInDialog={true}
              handleTextChange={handleTextInputChange}
              handleTextBlur={handleTextInputBlur}
              textInputLabel={"Name of the file:"}
              textInputStyle={{
                marginBottom: "2vh",
              }}
              fileName={fileName}
              projectPlaceholder={"New project"}
              useDropDownInDialog={true}
              dropDownLabel={"Select machine: "}
              dropDownContainerStyle={{
                width: "100%",
                marginTop: "1vh",
              }}
              dropdownData={[
                { title: "Machine is not selected", _id: "" },
                ...machines,
              ]}
              dropDownDataValue={filteredMachine}
              dropDownDataDisplayKey={"title"}
              dropDownDataValueKey={"_id"}
              handleChange={handleMachineDropdownChange}
              dialogCancelContent={"Cancel"}
              dialogConfirmContent={"Save"}
              handleConfirm={handleSave}
              handleClose={handleCloseSave}
            />
          </div>
        </div>
      </header>
      <div className="dndflow" ref={reactFlowWrapper}>
        <ReactFlowProvider>
          <Sidebar />
          <div className="reactflow-wrapper" id="reactflow-wrapper">
            <ReactFlow
              elements={elements}
              onConnect={onConnect}
              onElementClick={onElementClick}
              onElementsRemove={onElementsRemove}
              nodeTypes={nodeTypes}
              onLoad={onLoad}
              onDrop={onDrop}
              onDragOver={onDragOver}
              onNodeDragStop={onDragEnd}
            >
              <Background
                style={
                  currentMode === "Dark"
                    ? { backgroundColor: "#161C24", color: "white !important" }
                    : {}
                }
                color={currentMode === "Dark" ? "#fff" : "#000"}
                gap={12}
              />
              {nodeHidden ? (
                ""
              ) : (
                <MiniMap
                  nodeColor={(n) => {
                    if (n.type === "input") return "blue";

                    return "#FFCC00";
                  }}
                />
              )}
              <Controls />
            </ReactFlow>
          </div>
          <aside
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#161C24", color: "white !important" }
                : {}
            }
            className={`${
              toggle ? "toggle-sidebar project-sidebar" : "project-sidebar"
            }`}
          >
            {showEdgeProperties ? (
              <div>
                <div
                  style={currentMode === "Dark" ? { color: "#fff" } : {}}
                  className="toggle-btn"
                  onClick={() => handleToggleSidebar(!toggle)}
                >
                  {toggle ? (
                    <KeyboardDoubleArrowLeftIcon />
                  ) : (
                    <KeyboardDoubleArrowRightIcon />
                  )}
                </div>
                <div className="description">
                  <h1 style={currentMode === "Dark" ? { color: "#fff" } : {}}>
                    Edit Edge
                  </h1>
                </div>
                <div className="checkboxWrapper">
                  <span style={currentMode === "Dark" ? { color: "#fff" } : {}}>
                    Hide MiniMap:
                  </span>
                  <label className="switch">
                    <input
                      type="checkbox"
                      checked={nodeHidden}
                      onChange={(evt) => setNodeHidden(evt.target.checked)}
                    />
                    <span class="slider round"></span>
                  </label>
                </div>
                <br></br>
                <div className="edge-input">
                  <label
                    style={currentMode === "Dark" ? { color: "#fff" } : {}}
                  >
                    Edge label:
                  </label>
                  <input
                    style={
                      currentMode === "Dark"
                        ? { color: "black !important" }
                        : {}
                    }
                    value={edgeLabel}
                    onChange={(evt) => {
                      setEdgeLabel(evt.target.value);
                    }}
                  />
                </div>
                <div className="edge-input">
                  <label
                    style={currentMode === "Dark" ? { color: "#fff" } : {}}
                  >
                    Edge Type
                  </label>
                  <select
                    style={
                      currentMode === "Dark"
                        ? { color: "black !important" }
                        : {}
                    }
                    name="edgeType"
                    id="edgeType"
                    value={edgeType}
                    onChange={(evt) => setEdgeType(evt.target.value)}
                  >
                    <option value="default">Default</option>
                    <option value="straight">Straight</option>
                    <option value="step">Step</option>
                    <option value="smoothstep">Smooth Step</option>
                  </select>
                </div>
                <div className="edge-input">
                  <label
                    style={currentMode === "Dark" ? { color: "#fff" } : {}}
                  >
                    Edge Colour
                  </label>
                  <input
                    type="color"
                    value={edgeStyle.stroke}
                    onChange={(evt) =>
                      setEdgeStyle({ ...edgeStyle, stroke: evt.target.value })
                    }
                  />
                </div>
                <div className="checkboxWrapper">
                  <span style={currentMode === "Dark" ? { color: "#fff" } : {}}>
                    Animated Edge
                  </span>
                  <label className="switch">
                    <input
                      type="checkbox"
                      checked={edgeAnimated}
                      onChange={(evt) => setEdgeAnimated(evt.target.checked)}
                    />
                    <span class="slider round"></span>
                  </label>
                </div>
                <div className="edge-input">
                  <label
                    style={currentMode === "Dark" ? { color: "#fff" } : {}}
                  >
                    Label Size
                  </label>
                  <input
                    style={
                      currentMode === "Dark"
                        ? { color: "black !important" }
                        : {}
                    }
                    type="number"
                    value={edgeLabelStyle.fontSize}
                    onChange={(evt) =>
                      setEdgeLabelStyle({
                        ...edgeLabelStyle,
                        fontSize: evt.target.value,
                      })
                    }
                  />
                </div>
                <div className="edge-input">
                  <label
                    style={currentMode === "Dark" ? { color: "#fff" } : {}}
                  >
                    Label Colour
                  </label>
                  <input
                    type="color"
                    value={edgeLabelStyle.fill}
                    onChange={(evt) =>
                      setEdgeLabelStyle({
                        ...edgeLabelStyle,
                        fill: evt.target.value,
                      })
                    }
                  />
                </div>
                <div className="edge-input">
                  <label
                    style={currentMode === "Dark" ? { color: "#fff" } : {}}
                  >
                    Label fontWeight
                  </label>
                  <select
                    style={
                      currentMode === "Dark"
                        ? { color: "black !important" }
                        : {}
                    }
                    name="edgeLabelFontWeight"
                    id="edgeLabel-fontWeight"
                    value={edgeLabelStyle.fontWeight}
                    onChange={(evt) =>
                      setEdgeLabelStyle({
                        ...edgeLabelStyle,
                        fontWeight: evt.target.value,
                      })
                    }
                  >
                    <option value="normal">light</option>
                    <option value="bolder" selected>
                      bold
                    </option>
                  </select>
                </div>
                {/* Delete Edge Button */}
                <div className="delete-button">
                  <button
                    style={
                      currentMode === "Dark"
                        ? { backgroundColor: "#ff4d4f", color: "#fff" }
                        : { backgroundColor: "#ff4d4f", color: "#fff" }
                    }
                    onClick={() => handleDeleteEdge(element.id)} // Add your delete edge function here
                  >
                    Delete Edge
                  </button>
                </div>
              </div>
            ) : (
              <div>
                <div
                  className="toggle-btn"
                  onClick={() => handleToggleSidebar(!toggle)}
                  style={currentMode === "Dark" ? { color: "#fff" } : {}}
                >
                  {toggle ? (
                    <KeyboardDoubleArrowLeftIcon />
                  ) : (
                    <KeyboardDoubleArrowRightIcon />
                  )}
                </div>
                <div className="description">
                  <h1 style={currentMode === "Dark" ? { color: "#fff" } : {}}>
                    Edit Node
                  </h1>
                </div>
                <div className="checkboxWrapper">
                  <span style={currentMode === "Dark" ? { color: "#fff" } : {}}>
                    Hide MiniMap:
                  </span>
                  <label className="switch">
                    <input
                      type="checkbox"
                      checked={nodeHidden}
                      onChange={(evt) => setNodeHidden(evt.target.checked)}
                    />
                    <span class="slider round"></span>
                  </label>
                </div>
                <br></br>
                <div className="node-input">
                  <label
                    style={currentMode === "Dark" ? { color: "#fff" } : {}}
                  >
                    Border Radius:
                  </label>
                  <input
                    style={
                      currentMode === "Dark"
                        ? { color: "black !important" }
                        : {}
                    }
                    type="number"
                    value={radius || ""}
                    onChange={(evt) => setRadius(evt.target.value)}
                  />
                </div>
                {hideText1 ? (
                  <div className="node-input">
                    <label
                      style={currentMode === "Dark" ? { color: "#fff" } : {}}
                    >
                      label:
                    </label>
                    <input
                      style={
                        currentMode === "Dark"
                          ? { color: "black !important" }
                          : {}
                      }
                      value={nodeName || ""}
                      onChange={(evt) => {
                        setNodeName(evt.target.value);
                      }}
                    />
                  </div>
                ) : (
                  ""
                )}
                {hideText2 ? (
                  <div className="node-input">
                    <label
                      style={currentMode === "Dark" ? { color: "#fff" } : {}}
                    >
                      label2:
                    </label>
                    <input
                      style={
                        currentMode === "Dark"
                          ? { color: "black !important" }
                          : {}
                      }
                      value={nodeName2 || ""}
                      onChange={(evt) => setNodeName2(evt.target.value)}
                    />
                  </div>
                ) : (
                  ""
                )}
                {hideText3 ? (
                  <div className="node-input">
                    <label
                      style={currentMode === "Dark" ? { color: "#fff" } : {}}
                    >
                      label3:
                    </label>
                    <input
                      style={
                        currentMode === "Dark"
                          ? { color: "black !important" }
                          : {}
                      }
                      value={nodeName3 || ""}
                      onChange={(evt) => setNodeName3(evt.target.value)}
                    />
                  </div>
                ) : (
                  ""
                )}
                <div className="node-position">
                  <label
                    style={currentMode === "Dark" ? { color: "#fff" } : {}}
                  >
                    <b>Position X:</b> &nbsp; {Math.trunc(nodeX)}
                  </label>
                  <label
                    style={currentMode === "Dark" ? { color: "#fff" } : {}}
                  >
                    <b>Position Y:</b> &nbsp; {Math.trunc(nodeY)}
                  </label>{" "}
                </div>
                <div className="node-input">
                  <label
                    style={currentMode === "Dark" ? { color: "#fff" } : {}}
                  >
                    background:
                  </label>
                  <input
                    type="color"
                    value={nodeBg}
                    onChange={(evt) => setNodeBg(evt.target.value)}
                  />
                </div>
                {hideCode ? (
                  <div
                    className="node-input-textarea node-input"
                    style={{ marginTop: "12px" }}
                  >
                    <label
                      style={currentMode === "Dark" ? { color: "#fff" } : {}}
                    >
                      Code:
                    </label>
                    <textarea
                      style={
                        currentMode === "Dark"
                          ? { color: "black !important" }
                          : {}
                      }
                      value={code}
                      rows="5"
                      onChange={(evt) => setCode(evt.target.value)}
                    ></textarea>
                  </div>
                ) : (
                  ""
                )}
                {hideCode2 ? (
                  <div
                    className="node-input-textarea node-input"
                    style={{ marginTop: "12px" }}
                  >
                    <label
                      style={currentMode === "Dark" ? { color: "#fff" } : {}}
                    >
                      Code2:
                    </label>
                    <textarea
                      style={
                        currentMode === "Dark"
                          ? { color: "black !important" }
                          : {}
                      }
                      value={code2}
                      rows="5"
                      onChange={(evt) => setCode2(evt.target.value)}
                    ></textarea>
                  </div>
                ) : (
                  ""
                )}
                {hideCode3 ? (
                  <div
                    className="node-input-textarea node-input"
                    style={{ marginTop: "12px" }}
                  >
                    <label
                      style={currentMode === "Dark" ? { color: "#fff" } : {}}
                    >
                      Code3:
                    </label>
                    <textarea
                      style={
                        currentMode === "Dark"
                          ? { color: "black !important" }
                          : {}
                      }
                      value={code3}
                      rows="5"
                      onChange={(evt) => setCode3(evt.target.value)}
                    ></textarea>
                  </div>
                ) : (
                  ""
                )}
                {hideImage ? (
                  <div className="image-node">
                    <div className="node-input">
                      <label
                        style={currentMode === "Dark" ? { color: "#fff" } : {}}
                      >
                        Image:
                      </label>
                      <input type="file" onChange={onImageChange} />
                    </div>{" "}
                    <img src={nodeImage} width="100%" alt="img" />{" "}
                  </div>
                ) : (
                  ""
                )}
                {hideImage2 ? (
                  <div className="image-node">
                    <label
                      style={currentMode === "Dark" ? { color: "#fff" } : {}}
                    >
                      Image2:
                    </label>
                    <input type="file" onChange={onImageChange2} />{" "}
                    <img src={nodeImage2} width="100%" alt="img" />{" "}
                  </div>
                ) : (
                  ""
                )}
                {hideImage3 ? (
                  <div className="image-node">
                    <label
                      style={currentMode === "Dark" ? { color: "#fff" } : {}}
                    >
                      Image3:
                    </label>
                    <input type="file" onChange={onImageChange3} />{" "}
                    <img src={nodeImage3} width="100%" alt="img" />{" "}
                  </div>
                ) : (
                  ""
                )}
                {hideTextArea1 ? (
                  <div
                    className="node-input-textarea node-input"
                    style={{ marginTop: "12px" }}
                  >
                    <label
                      style={currentMode === "Dark" ? { color: "#fff" } : {}}
                    >
                      Text Area:
                    </label>
                    <textarea
                      style={
                        currentMode === "Dark"
                          ? { color: "black !important" }
                          : {}
                      }
                      value={textArea}
                      rows="5"
                      onChange={(evt) => setTextArea(evt.target.value)}
                    ></textarea>
                  </div>
                ) : (
                  ""
                )}
                {hideTextArea2 ? (
                  <div
                    className="node-input-textarea node-input"
                    style={{ marginTop: "12px" }}
                  >
                    <label
                      style={currentMode === "Dark" ? { color: "#fff" } : {}}
                    >
                      Text Area2:
                    </label>
                    <textarea
                      style={
                        currentMode === "Dark"
                          ? { color: "black !important" }
                          : {}
                      }
                      value={textArea2}
                      rows="5"
                      onChange={(evt) => setTextArea2(evt.target.value)}
                    ></textarea>
                  </div>
                ) : (
                  ""
                )}
                {hideTextArea3 ? (
                  <div
                    className="node-input-textarea node-input"
                    style={{ marginTop: "12px" }}
                  >
                    <label
                      style={currentMode === "Dark" ? { color: "#fff" } : {}}
                    >
                      Text Area3:
                    </label>
                    <textarea
                      style={
                        currentMode === "Dark"
                          ? { color: "black !important" }
                          : {}
                      }
                      value={textArea3}
                      rows="5"
                      onChange={(evt) => setTextArea3(evt.target.value)}
                    ></textarea>
                  </div>
                ) : (
                  ""
                )}
                {/* Delete Node Button */}
                <div className="delete-button">
                  <button
                    style={
                      currentMode === "Dark"
                        ? { backgroundColor: "#ff4d4f", color: "#fff" }
                        : { backgroundColor: "#ff4d4f", color: "#fff" }
                    }
                    onClick={() => handleDeleteNode(element.id)} // Add your delete node function here
                  >
                    Delete Node
                  </button>
                </div>
              </div>
            )}
          </aside>
        </ReactFlowProvider>
      </div>
    </main>
  );
};

export default DnDFlow;
