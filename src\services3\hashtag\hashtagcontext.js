import axios from "axios";
import React, {
  createContext,
  useEffect,
  useState,
  useContext,
  Children,
} from "react";
import { dbConfig } from "../../infrastructure/db/db-config";

export const HashtagsContext = createContext([]);
export const HashtagsSetterContext = createContext([]);

export function useHashtagsAllGetter() {
  return useContext(HashtagsContext);
}

export function useHashtagsSetter() {
  return useContext(HashtagsSetterContext);
}

function HashtagcontextProvider({ children }) {
  const [hashtagArray, setHashtagArray] = useState([]);
  const hashtagfunction = (step) => {
    // console.log("ye function bhi chal rha hai", step.hashtag);
    const arr = Array?.isArray(step?.hashtag) ? step?.hashtag : [];
    // console.log("arr",arr)
    arr.filter((item) => {
      axios
        .get(`${dbConfig.url}/hashtags/${item}`)
        .then((response) => {
          // console.log(item,"ye har id")
          setHashtagArray((prevState) => [
            ...prevState,
            response?.data?.data?.title,
          ]);
          // console.log(hashtagArray, "har step ke liye");
        })
        .catch((err) => {
          console.log("error:", err);
        });
    });
    setHashtagArray([]);
  };
  return (
    <>
      <HashtagsContext.Provider value={hashtagArray}>
        <HashtagsSetterContext.Provider value={hashtagfunction}>
          {children}
        </HashtagsSetterContext.Provider>
      </HashtagsContext.Provider>
    </>
  );
}

export default HashtagcontextProvider;
