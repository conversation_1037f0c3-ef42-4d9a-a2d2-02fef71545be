import React, { useContext, useState, useEffect, createContext } from "react";
import { db } from "../firebase";
import {
  companies,
  companyId_constant,
  alluser,
  adminType_constant_temp,
} from "../constants/data";
import { firebaseLooper } from "../tools/tool";

const UserContext = createContext();
//
export function useUser() {
  return useContext(UserContext);
}

export function UserProvider({ children }) {
  const [user, setUser] = useState("");

  return <UserContext.Provider value={user}>{children}</UserContext.Provider>;
}
