const ipSession = localStorage.getItem("server_ip");
const portSession = localStorage.getItem("server_port");
// const ldap=localStorage?.getItem('ldapusers')
// if(!port&&!ip){
//    let port = `127.0.0.1`
//     let ip='3000'
//
// }

// export const LDAP_USERS = ldap ? JSON?.parse(ldap) : true;
export const port = portSession ? portSession : "5001"; //5000
export const ip = ipSession ? ipSession : window.location.hostname;

export const dbUrl =
  process.env.NODE_ENV === "production"
    ? "/api/v1"
    : ip
      ? `${port === "443" ? "https" : "http"}://${ip}:${port}/api/v1`
      : `http://localhost:${port}/api/v1`;
//export const dbUrl =  `${port === '443' ? 'https' : 'http'}://${ip}:${port}/api/v1`

// let ip=sessionStorage.getItem('server_ip')
// let port=sessionStorage.getItem('server_port')
// if(!port&&!ip){
//    let port = `127.0.0.1`
//     let ip='3000'
//
// }

// export const dbUrl = `http://127.0.0.1:5000/api/v1`
