.fc-daygrid-day-number {
  color: inherit;
  text-decoration: none;
  font-weight: 500;
}
.fc-daygrid-day-number:hover {
  color: inherit;
}
.fc-col-header-cell-cushion {
  color: inherit !important;
  padding-top: 5px;
  padding-bottom: 5px;
  text-decoration: none;
}
.fc-col-header-cell-cushion:hover {
  color: inherit;
}
.fc .fc-button-primary {
  background-color: #0a0a0a00;
  color: inherit;
  border: none;
  outline: none;
}

.fc .fc-button-primary:hover {
  background-color: #ffffff00;
  color: inherit;
  border: none;
}

.cal {
  margin-top: 60px;
  width: 60%;
  max-height: 40vh;
}

.fc-event-title-container {
  padding: 5px;
  font-family: "Roboto", sans-serif;
}

.fc-scrollgrid-sync-inner {
  border: 0px solid #eff4fa;
  text-transform: capitalize;
}

.fc-col-header-cell-cushion {
  color: #8f9bb3;
  box-shadow: none;
  text-transform: capitalize;
}

.fc .fc-daygrid-day.fc-day-today {
  /* background-color: #ffffff; */
  box-shadow: none;
  text-transform: capitalize;
}

.fc .fc-button-primary:not(:disabled):active,
.fc .fc-button-primary:not(:disabled).fc-button-active {
  text-decoration: underline #2b95eb 4px;
  box-shadow: none;
  text-transform: capitalize;
  /* background:white; */
  /* color: black */
}
/* //css code */
#overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
}

#popup {
  width: 300px;
  background-color: #fff;
  padding: 20px;
  margin: 100px auto;
}

#closeButton {
  margin-top: 10px;
}
