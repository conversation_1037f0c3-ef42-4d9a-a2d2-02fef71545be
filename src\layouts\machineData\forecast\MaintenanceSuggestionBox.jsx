import React from "react";
import { Card, CardContent, Typography } from "@mui/material";

const generateMaintenanceSuggestion = (values, forecastValues) => {
  const recentValue = values[values.length - 2]; // Last known real value
  const forecastedValue = forecastValues[forecastValues.length - 1]; // Latest forecast

  if (!recentValue || !forecastedValue) {
    return {
      message: "Routine checks recommended to ensure smooth operation.",
      severity: "normal",
    };
  }

  const change = ((forecastedValue - recentValue) / recentValue) * 100;

  if (change > 15) {
    return {
      message:
        "⚠️ Rapid increase detected! Machine may be overheating. Immediate inspection required.",
      severity: "critical",
    };
  } else if (change < -15) {
    return {
      message:
        "⚠️ Significant drop detected! Possible malfunction or breakdown risk. Schedule urgent maintenance.",
      severity: "critical",
    };
  } else if (change > 5) {
    return {
      message:
        "📈 Moderate increase in readings. Consider optimizing operational efficiency.",
      severity: "warning",
    };
  } else if (change < -5) {
    return {
      message:
        "📉 Slight decrease detected. Routine maintenance check advised.",
      severity: "warning",
    };
  } else {
    return {
      message: "✅ Machine is running smoothly. Continue regular monitoring.",
      severity: "normal",
    };
  }
};

const getBackgroundColor = (severity) => {
  switch (severity) {
    case "critical":
      return "#ffebee"; // Light Red
    case "warning":
      return "#fff3e0"; // Light Orange
    case "normal":
    default:
      return "#e8f5e9"; // Light Green
  }
};

const MaintenanceSuggestionBox = ({ values, forecastValues }) => {
  const suggestion = generateMaintenanceSuggestion(values, forecastValues);
  const backgroundColor = getBackgroundColor(suggestion.severity);

  return (
    <Card sx={{ maxWidth: 750, height: "150px", mt: 4, p: 2, backgroundColor }}>
      <CardContent>
        <Typography variant="h6" align="center" gutterBottom>
          Maintenance Suggestion
        </Typography>
        <Typography variant="body1" align="center">
          {suggestion.message}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default MaintenanceSuggestionBox;
