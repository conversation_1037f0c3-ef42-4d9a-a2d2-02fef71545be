import React from "react";
import arizon from "../../assets/arizon.png";

import { View, Text, StyleSheet, Image } from "@react-pdf/renderer";
import PropTypes from "prop-types";

const styles = StyleSheet.create({
  headerTable: {
    width: "100%",
    borderCollapse: "collapse",
  },
  headerRow: {
    flexDirection: "row",
  },
  headerRow2: {
    flexDirection: "row",
    height: "25px",
  },
  columnCell: {
    flexDirection: "column",
    // width: "100%",
    // textAlign: "center",
  },
  headerRowSpaced: {
    flexDirection: "row",
    // marginBottom: 5,
  },
  headerRowSpaced2: {
    flexDirection: "row",
    // marginBottom: 5,
    height: "25px",
  },
  cell: {
    borderWidth: 1,
    borderColor: "#000",
    paddingTop: 5,
    paddingBottom: 5,
    height: "25px",
  },
  cell2: {
    borderWidth: 1,
    borderColor: "#000",
    // paddingTop: 5,
    // height: "25px",
  },
  celllogo: {
    borderWidth: 1,
    borderColor: "#000",
    paddingTop: 5,
    paddingBottom: 5,
    // padding: 5,
    height: "52px",
  },
  logoCell: {
    width: "50%",
    textAlign: "center",
    display: "flex",
    alignItems: "center",
  },
  logo: {
    width: "50%",
    height: "60px",
  },
  textCell: {
    width: "50%",
    textAlign: "start",
  },
  textCell2: {
    width: "100%",
    textAlign: "start",
  },
  text: {
    paddingHorizontal: 15,
    fontSize: 8,
  },
  textBold: {
    fontWeight: "bold",
  },
});

const MyHeader = ({
  title = "Enter Title",
  UnitName = "Enter Unit Name",
  attachmentName,
  attachmentNo,
}) => {
  const UNKNOWN_USER = "Unknown";
  const TITLE_LABEL = "Title: Addendum";
  const UNIT_NAME_LABEL = "Unit Name: Gagillapur";
  const ATTACHMENT_NAME_LABEL = "Attachment Name:";
  const ATTACHMENT_NO_LABEL = "Attachment No:";
  const GRANULES_LOGO_ALT = "Granules Logo";

  // Browser-specific logic should be handled outside the component
  const userDetails = JSON.parse(window.sessionStorage.getItem("@user-creds"));
  const userName = userDetails
    ? `${userDetails.fname} ${userDetails.lname}`
    : UNKNOWN_USER;
  const downloadDate = new Date().toLocaleDateString("en-GB");
  const downloadTime = new Date().toLocaleTimeString();
  const dateTime = `${downloadDate} at ${downloadTime}`;

  return (
    <View style={styles.headerTable}>
      {/* Row 1: Logo */}
      <View style={styles.headerRow}>
        <View style={[styles.celllogo, styles.logoCell]}>
          <Image style={styles.logo} src={arizon} alt={GRANULES_LOGO_ALT} />
        </View>

        <View style={[styles.cell2, styles.textCell]}>
          <View style={styles.columnCell}>
            <View style={[styles.cell, styles.textCell2]}>
              <Text style={[styles.text, styles.textBold]}>{TITLE_LABEL}</Text>
            </View>
            <View style={[styles.cell, styles.textCell2]}>
              <Text style={styles.text}>{UNIT_NAME_LABEL}</Text>
            </View>
          </View>
        </View>
      </View>
      {/* Row 2: Printed By & Date & Time */}
      <View style={styles.headerRow2}>
        <View style={[styles.cell, styles.textCell2]}>
          <Text style={styles.text}>{ATTACHMENT_NAME_LABEL}</Text>
        </View>
        <View style={[styles.cell, styles.textCell2]}>
          <Text style={styles.text}>{attachmentName}</Text>
        </View>
      </View>
      <View style={styles.headerRow2}>
        <View style={[styles.cell, styles.textCell2]}>
          <Text style={styles.text}>{ATTACHMENT_NO_LABEL}</Text>
        </View>
        <View style={[styles.cell, styles.textCell2]}>
          <Text style={styles.text}>{attachmentNo}</Text>
        </View>
      </View>
    </View>
  );
};

MyHeader.propTypes = {
  title: PropTypes.string,
  UnitName: PropTypes.string,
  attachmentName: PropTypes.string.isRequired,
  attachmentNo: PropTypes.string.isRequired,
};

MyHeader.defaultProps = {
  title: "Enter Title",
  UnitName: "Enter Unit Name",
  attachmentName: "",
  attachmentNo: "",
};

export default MyHeader;
