// Note: this is copy of 'DataReportPrint' with jspdf
// Realtime design Test :http://raw.githack.com/MrRio/jsPDF/master/index.html#
//Implimentation: Here we have two components. In the first one all data has been processed and passed as props in the second component.
// Here table data is processed in diffrent way. Here "table1(2,3)" have all table1(2,3) data of all FATs of the report.
// table0 is the execution table.
// pre and post approval tables have images , and we can't use these url directly in autoTable(pdf.js dependent package). So it has
// been kept separatly.
// Extras: https://stackoverflow.com/questions/67225420/adding-header-footer-for-all-screens-in-jspdf

import React, { useEffect, useState } from "react";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";

import { useParams } from "react-router-dom";
import {
  companies,
  companyId_constant,
  fatReport,
  machines,
} from "../../../constants/data";
import { db } from "../../../firebase";
import { firebaseLooper } from "../../../tools/tool";
import "./contentPagePrint.scss";
import ReportItemPrint from "./ReportItemPrint";
import { useStateContext } from "../../../context/ContextProvider";
import { FaceRetouchingNatural, ThirtyFpsSharp } from "@mui/icons-material";
import { toastMessage } from "../../../tools/toast";
import moment from "moment";
import CircularProgress from "@mui/material/CircularProgress";
import { useCallback } from "react";
import logo from "../../../assets/images/logo.png";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";

export default function PrintReportPdfMain({ reportId }) {
  //const { reportId } = useParams() //ID of reports from which the fatData has to be fetched
  const [dataLoad, setDataLoad] = useState(true);
  const [reportDetails, setReportDetails] = useState([]);
  const [reportInfo, setReportInfo] = useState([]);
  const [reportTableDetails, setReportTableDetails] = useState([]);
  const [approvalTable, setApprovalTable] = useState([]);
  const [approvalUrlOnly, setApprovalUrlOnly] = useState([]); // array of base64 images
  const [postApprovalTable, setPostApprovalTable] = useState([]);
  const [postApprovalUrlOnly, setPostApprovalUrlOnly] = useState([]); // array of base64 images

  const [softwareListUrls, setSoftwareListUrls] = useState([]); // array of base64 images
  const [table0, setTable0] = useState([{}]);
  const [table1, setTable1] = useState([{}]);
  const [table2, setTable2] = useState([{}]);
  const [table3, setTable3] = useState([{}]);
  const [table4, setTable4] = useState([{}]);

  const [iq1, setIq1] = useState();
  const [iq2, setIq2] = useState();
  const [iq3, setIq3] = useState();
  const [iq4, setIq4] = useState();
  const [iq5, setIq5] = useState();
  const [iq6, setIq6] = useState();
  const [iq7, setIq7] = useState();
  const [iq8, setIq8] = useState();
  const [iq9, setIq9] = useState();
  const [iq10, setIq10] = useState();
  const [iq11, setIq11] = useState();
  const [iq12, setIq12] = useState();
  const [iq13, setIq13] = useState();
  const [iq14, setIq14] = useState();

  const [mfg1, setMfg1] = useState();

  const [eqp1, setEqp1] = useState();
  const [eqp2, setEqp2] = useState();
  const [eqp3, setEqp3] = useState();
  const [eqp4, setEqp4] = useState();
  const [eqp5, setEqp5] = useState();

  const [oq1, setOq1] = useState();
  const [oq2, setOq2] = useState();
  const [oq3, setOq3] = useState();
  const [oq4, setOq4] = useState();
  const [oq5, setOq5] = useState();
  const [oq6, setOq6] = useState();
  const [oq7, setOq7] = useState();
  const [oq8, setOq8] = useState();
  const [oq9, setOq9] = useState();
  const [oq10, setOq10] = useState();
  const [oq11, setOq11] = useState();

  const [pq1, setPq1] = useState([]);
  const [pq2, setPq2] = useState([]);
  const [pq2_staticData, setPq2_staticData] = useState([]);
  const [pq3, setPq3] = useState([]);
  const [pq3_staticData, setPq3_staticData] = useState([]);
  const [pq4, setPq4] = useState([]);
  const [pq4_staticData, setPq4_staticData] = useState([]);
  const [pq5, setPq5] = useState([]);

  const [machineAll, setMachineAll] = useState([]);
  const [companyInfo, setCompanyInfo] = useState({});

  const [companyImageUrl, setcompanyImageUrl] = useState("");

  const { currentMode } = useStateContext();

  // const databaseCollection = db
  //   .collection(companies)
  //   .doc(companyId_constant)
  //   .collection(fatReport)
  //   .doc(reportId)
  //   .collection(`fatData`);

  const handleAuditData = async () => {
    // fatData-report
    await axios
      .post(`${dbConfig.url}/fatdata-report/findsome`, { fid: reportId })
      .then((response) => {
        console.log("fatData report print data report id:", reportId);
        console.log("fatData report data print :", response?.data);
        setReportDetails(response?.data);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  const handleMachineAllData = async () => {
    // fatData-report
    await axios
      .get(`${dbConfig.url}/machines/`)
      .then((response) => {
        //console.log("fatData report print data report id:", reportId)
        //console.log("fatData machines :", response?.data)
        setMachineAll(response.data);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  // const databaseReportInfo = db
  //   .collection(companies)
  //   .doc(companyId_constant)
  //   .collection(fatReport)
  //   .doc(reportId);

  // const DatabaseMachines = db
  //   .collection(companies)
  //   .doc(companyId_constant)
  //   .collection(machines);

  // const DatabaseCompanyInfo = db.collection(companies).doc(companyId_constant);

  // fatlist report  data
  const handleFatListData = async () => {
    // fatList-report
    await axios
      .get(`${dbConfig.url}/fatlist-report/${reportId}`)
      .then((response) => {
        //console.log("fatList report data report id:", reportId)
        console.log("fatList report data:", response?.data);
        //setFatListReportData(response.data);
        setReportInfo(response?.data);
        return response?.data;
      })
      .catch((error) => {
        console.error(error);
      });
  };

  //Approval table (post)
  const handleApprovalTable = async () => {
    // Post Approval
    // It has implementation as other tables only.  But only API is diffrent

    axios
      .post(`${dbConfig.url}/general-table-report/findsome_report_approval`, {
        // fat_id: fatDataId,
        fat_report_id: reportId,
        table_type: "PostApproval",
      })
      .then((res) => {
        //console.log("Post Approval data get:", res?.data);
        //console.log("FatData id:", fatDataId);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            // temp.push(dataM["serial_no"]);
            temp.push(dataM["prep_by_tech_transfer"]);
            temp.push(dataM["rev_by_tech_transfer"]);
            temp.push(dataM["production"]);
            temp.push(dataM["engineering"]);
            temp.push(dataM["rev_by_qa"]);
            temp.push(dataM["approved_by_qa"]);
            //temp.push(dataM["url"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPostApprovalTable(tablesArray);
      })
      .catch((e) => {
        console.log("error Post Approval data get:", e);
      });
  };
  //

  // Mchine data Report datas
  const handleMachineData = async () => {
    // fatData-report
    await axios
      .get(`${dbConfig.url}/machines`)
      .then((response) => {
        console.log("Machine data:", response?.data?.data);
        setMachineAll(response?.data?.data);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  //Fetch fat Data from the collection fatReportData
  useEffect(() => {
    const fetchMain = async () => {
      const audit = await handleFatListData();
      await handleMachineData();
      await handleAuditData();
      await handleMachineAllData();
      await handleApprovalTable();

      //

      //PQ // table mapping test
      axios
        .post(`${dbConfig.url}/general-table-report/findsome_report`, {
          fat_report_id: reportId, //audit?._id,//
          // table_type: "PQ5",
        })
        .then((res) => {
          // console.log("All table report data get reportid:", reportId);
          // console.log("all report data get:", res?.data);

          axios
            .post(
              `${dbConfig.url}/general-table_static-data-report/findsome_report`,
              {
                fat_report_id: reportId, //audit?._id,//
                // table_type: "PQ5",
              },
            )
            .then((resStaticData) => {
              let resDataPQ1 = res?.data?.filter(
                (fData) => fData?.table_type === "PQ1",
              );
              let resDataPQ2 = res?.data?.filter(
                (fData) => fData?.table_type === "PQ2",
              );
              let resDataStaticPQ2 = resStaticData?.data?.filter(
                (fData) => fData?.table_type === "PQ2",
              );
              let resDataPQ3 = res?.data?.filter(
                (fData) => fData?.table_type === "PQ3",
              );
              let resDataStaticPQ3 = resStaticData?.data?.filter(
                (fData) => fData?.table_type === "PQ3",
              );
              let resDataPQ4 = res?.data?.filter(
                (fData) => fData?.table_type === "PQ4",
              );
              let resDataStaticPQ4 = resStaticData?.data?.filter(
                (fData) => fData?.table_type === "PQ4",
              );
              let resDataPQ5 = res?.data?.filter(
                (fData) => fData?.table_type === "PQ5",
              );

              console.log("pq2Static one:", resDataStaticPQ2);

              if (resDataPQ1?.length) {
                const tablesArray = []; // array of tables
                if (resDataPQ1?.length > 0) {
                  let dataSorted = resDataPQ1.sort(
                    (a, b) => a["serial_no"] - b["serial_no"], //
                  );
                  const tempArr2d = [];
                  dataSorted.map((dataM) => {
                    const temp = [];
                    temp.push(dataM["serial_no"]);
                    temp.push(dataM["speed_of_machine"]);
                    temp.push(dataM["speed_in_rpm"]);
                    // temp.push(dataM["url"]);
                    temp.push(dataM["fat_id"]);
                    temp.push(dataM["_id"]); // for update ,it should be at this order only
                    temp.push(dataM["index"]); // for those which has no serial number
                    temp.push(dataM["table_title"]); //
                    tempArr2d.push(temp);
                  });
                  tablesArray.push(tempArr2d);
                }
                console.log("pq1 sorted final data:", tablesArray);
                setPq1(tablesArray);
              }
              if (resDataPQ2) {
                //PQ2
                if (resDataPQ2?.length) {
                  const tablesArray = []; // array of tables
                  if (resDataPQ2?.length > 0) {
                    let dataSorted = resDataPQ2.sort(
                      (a, b) => a["serial_no"] - b["serial_no"], //
                    );
                    const tempArr2d = [];
                    dataSorted.map((dataM) => {
                      const temp = [];
                      // temp.push(dataM["serial_no"]);
                      temp.push(dataM["parameter"]);
                      temp.push(dataM["acceptance_criteria"]);
                      temp.push(dataM["initial"]);
                      temp.push(dataM["min10"]);
                      temp.push(dataM["min20"]);
                      temp.push(dataM["min30"]);
                      temp.push(dataM["fat_id"]);
                      temp.push(dataM["_id"]); // for update ,it should be at this order only
                      temp.push(dataM["index"]); // for those which has no serial number
                      temp.push(dataM["table_title"]); //
                      tempArr2d.push(temp);
                    });
                    tablesArray.push(tempArr2d);
                  }
                  console.log("pq2 sorted final data:", tablesArray);
                  setPq2(tablesArray);
                  if (resDataStaticPQ2?.length) {
                    console.log("pq2StaicData:", resDataStaticPQ2);
                    setPq2_staticData(resDataStaticPQ2);
                  }
                }
              }
              if (resDataPQ3) {
                //PQ3
                if (resDataPQ3?.length) {
                  const tablesArray = []; // array of tables
                  if (resDataPQ3?.length > 0) {
                    let dataSorted = resDataPQ3.sort(
                      (a, b) => a["serial_no"] - b["serial_no"], //
                    );
                    const tempArr2d = [];
                    dataSorted.map((dataM) => {
                      const temp = [];
                      // temp.push(dataM["serial_no"]);
                      temp.push(dataM["parameter"]);
                      temp.push(dataM["specification"]);
                      temp.push(dataM["rpm10"]);
                      temp.push(dataM["rpm20"]);
                      temp.push(dataM["rpm40"]);
                      temp.push(dataM["fat_id"]);
                      temp.push(dataM["_id"]); // for update ,it should be at this order only
                      temp.push(dataM["index"]); // for those which has no serial number
                      temp.push(dataM["table_title"]); //
                      tempArr2d.push(temp);
                    });
                    tablesArray.push(tempArr2d);
                  }
                  console.log("pq3 sorted final data:", tablesArray);
                  setPq3(tablesArray);
                  if (resDataStaticPQ3?.length) {
                    console.log("pq3StaicData:", resDataStaticPQ3);
                    setPq3_staticData(resDataStaticPQ3);
                  }
                }
              }
              if (resDataPQ4) {
                //PQ4
                if (resDataPQ4?.length) {
                  const tablesArray = []; // array of tables
                  if (resDataPQ4?.length > 0) {
                    let dataSorted = resDataPQ4.sort(
                      (a, b) => a["serial_no"] - b["serial_no"], //
                    );
                    const tempArr2d = [];
                    dataSorted.map((dataM) => {
                      const temp = [];
                      // temp.push(dataM["serial_no"]);
                      temp.push(dataM["serial_no"]); // it has serial no
                      temp.push(dataM["parameter"]);
                      temp.push(dataM["limits"]);
                      temp.push(dataM["rpm40"]);
                      temp.push(dataM["rpm45"]);
                      temp.push(dataM["rpm50"]);
                      temp.push(dataM["fat_id"]);
                      temp.push(dataM["_id"]); // for update ,it should be at this order only
                      temp.push(dataM["index"]); // for those which has no serial number
                      temp.push(dataM["table_title"]); //
                      tempArr2d.push(temp);
                    });
                    tablesArray.push(tempArr2d);
                  }
                  console.log("pq4 sorted final data:", tablesArray);
                  setPq4(tablesArray);
                  if (resDataStaticPQ4?.length) {
                    console.log("pq4StaicData:", resDataStaticPQ4);
                    setPq4_staticData(resDataStaticPQ4);
                  }
                }
              }
              if (resDataPQ5) {
                //PQ5
                if (resDataPQ5?.length) {
                  const tablesArray = []; // array of tables
                  if (resDataPQ5?.length > 0) {
                    let dataSorted = resDataPQ5.sort(
                      (a, b) => a["serial_no"] - b["serial_no"], //
                    );
                    const tempArr2d = [];
                    dataSorted.map((dataM) => {
                      const temp = [];
                      // temp.push(dataM["serial_no"]);
                      temp.push(dataM["serial_no"]); // it has serial no
                      temp.push(dataM["eqp_name"]);
                      temp.push(dataM["eqp_id"]);
                      temp.push(dataM["make"]);
                      temp.push(dataM["test_param"]);
                      temp.push(dataM["design_range"]);
                      temp.push(dataM["qualification_range"]);
                      temp.push(dataM["product_qualification_range"]);
                      temp.push(dataM["tolerance"]);
                      temp.push(dataM["fat_id"]);
                      temp.push(dataM["_id"]); // for update ,it should be at this order only
                      temp.push(dataM["index"]); // for those which has no serial number
                      temp.push(dataM["table_title"]); //
                      tempArr2d.push(temp);
                    });
                    tablesArray.push(tempArr2d);
                  }
                  console.log("pq5 sorted final data:", tablesArray);
                  setPq5(tablesArray);
                }
              }

              //
              // axios.post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              //   fat_id: docId,
              //   // table_title: res?.data[0]["table_title"],
              // }).then((res2) => {
              //   console.log("res222:", res2?.data)
              //   setPq5_staticData(res2?.data)
              // }).catch((e) => {
              //   console.log("error Pq5_staticData get:", e)
              // })
              //
            })
            .catch((e) => {
              console.log("error PQ static data fetch:", e);
            });
        })
        .catch((e) => {
          console.log("error PQ data fetch:", e);
        });
    };

    fetchMain();

    // DatabaseMachines.onSnapshot((snap) => {
    //   const data = firebaseLooper(snap);
    //   setMachineAll(data);
    //   //console.log("dataReport machines:", data)
    // });
    // DatabaseCompanyInfo.get().then((snap) => {
    //   setCompanyInfo(snap.data());
    //   //alert("companyInfo:", snap.data())
    //   console.log("companyInfo: ", snap.data());
    //   var img = new Image();
    //   img.onload = function () {
    //     //doc.addImage(this, 7, 7, 38, 28);
    //     setcompanyImageUrl(this);
    //   };
    //   img.crossOrigin = ""; // for demo as we are at different origin than image
    //   img.src = snap.data()?.url; //
    //   //
    // });
    //
    // databaseReportInfo
    //   .get()
    //   .then((snapshot) => {
    //     setReportInfo(snapshot.data());
    //   })
    //   .catch((e) => console.log("report info failed:[DataReportsPrint]", e));
    //Fetching all the data from the fatData collection inside reportId document and storing in a state
    // databaseCollection.onSnapshot((snap) => {
    //   const data = firebaseLooper(snap);
    //   data.sort(function (a, b) {
    //     return a.index - b.index;
    //   });
    //   setReportDetails(data);
    //   //using snap : fetch table details of the collection i.e
    //   data.forEach((record) => {
    //     //catching each record in the fn
    //     if (record?.type == 0 && (record?.index == 0 || record?.index == 1)) {
    //       // Pre approvals
    //       databaseCollection
    //         .doc(record.id)
    //         .collection("approval") //Approval table for only 1 section - Pre approvals
    //         .onSnapshot((snap) => {
    //           const data = firebaseLooper(snap);
    //           // setApprovalTable(data);
    //           //console.log("ap table: ", data);
    //           // array of object to array of array
    //           const aprovalTemp = [];
    //           const approvalUrlOnlyTemp = [];
    //           data.map((dataM) => {
    //             const temp = [];
    //             const tempUrlOnly = [];
    //             temp.push(dataM["name"]); // order is important
    //             temp.push(dataM["email"]);
    //             // temp.push(dataM['']); //dataM['url'] // we are keeping images in diffrent array after converting into base64
    //             temp.push(
    //               moment(dataM["date"]?.toDate()).format(
    //                 "DD/MM/YYYY, h:mm:ss a"
    //               )
    //             ); //.toString().substring(0, 15)); // moment().format( date, 'DD/MM/YYYY, h:mm:ss a');
    //             temp.push(dataM["status"] ? "Approved" : "Not-approved");
    //             temp.push(dataM["type"]);
    //             //// image conversion to base64 for PDF
    //             // var img = new Image();
    //             // img.onload = function () {
    //             //     tempUrlOnly.push(this);
    //             // };
    //             // img.crossOrigin = "";
    //             // img.src = dataM['url'] ? dataM['url'] : "https://firebasestorage.googleapis.com/v0/b/lyodatatest.appspot.com/o/not%20approved.JPG?alt=media&token=b2f221d9-9171-4cce-b207-f90fe2f2c95a";
    //             // //
    //             // approvalUrlOnlyTemp.push(tempUrlOnly)
    //             aprovalTemp.push(temp);
    //           });
    //           setApprovalTable(aprovalTemp);
    //           setApprovalUrlOnly(approvalUrlOnlyTemp);
    //         });
    //     } else if (
    //       record?.type == 0 &&
    //       (record?.index !== 1 || record?.index !== 0)
    //     ) {
    //       // Post approval
    //       databaseCollection
    //         .doc(record.id)
    //         .collection("approval") //Approval table for only 1 section - post approvals
    //         .onSnapshot((snap) => {
    //           const data = firebaseLooper(snap);
    //           // setApprovalTable(data);
    //           console.log("ap table: ", data);
    //           // array of object to array of array
    //           const aprovalTemp2 = [];
    //           const approvalUrlOnlyTemp2 = [];
    //           data.map((dataM) => {
    //             const temp = [];
    //             const tempUrlOnly2 = [];
    //             temp.push(dataM["name"]); // order is important
    //             temp.push(dataM[""]); //dataM['url'] // we are keeping images in diffrent array after converting into base64
    //             temp.push(dataM["date"]?.toDate().toString().substring(0, 15)); //
    //             // image conversion to base64 for PDF
    //             var img = new Image();
    //             img.onload = function () {
    //               tempUrlOnly2.push(this);
    //             };
    //             img.crossOrigin = "";
    //             img.src = dataM["url"]
    //               ? dataM["url"]
    //               : "https://firebasestorage.googleapis.com/v0/b/lyodatatest.appspot.com/o/not%20approved.JPG?alt=media&token=b2f221d9-9171-4cce-b207-f90fe2f2c95a";
    //             //
    //             approvalUrlOnlyTemp2.push(tempUrlOnly2);
    //             aprovalTemp2.push(temp);
    //           });
    //           setPostApprovalTable(aprovalTemp2);
    //           setPostApprovalUrlOnly(approvalUrlOnlyTemp2);
    //         });
    //     }
    //     //// setting IQ10 urls array. it is unique
    //     // else if (record?.urls) {
    //     //     console.log("uu", record?.urls)
    //     //     const tempSoftwareListUrls = [];
    //     //     record?.urls?.map((urlData) => {
    //     //         // image conversion to base64 for PDF
    //     //         var img = new Image();
    //     //         img.onload = function () {
    //     //             tempSoftwareListUrls.push(this);
    //     //         };
    //     //         img.crossOrigin = "";
    //     //         img.src = urlData;
    //     //         //
    //     //     })
    //     //     setSoftwareListUrls(tempSoftwareListUrls)
    //     // }
    //   });
    //   //all urls
    //   const allUrlsTemp = [];
    //   data?.forEach((record2) => {
    //     //  const allUrlsTemp = [];
    //     if (record2?.urls) {
    //       console.log("uu", record2?.urls);
    //       const tempSoftwareListUrls = [];
    //       record2?.urls?.map((urlData) => {
    //         // image conversion to base64 for PDF
    //         var img = new Image();
    //         img.onload = function () {
    //           tempSoftwareListUrls.push(this);
    //         };
    //         img.crossOrigin = "";
    //         img.src = urlData;
    //         //
    //       });
    //       // setSoftwareListUrls(tempSoftwareListUrls)
    //       allUrlsTemp.push({
    //         id: record2?.id,
    //         docId: record2?.id,
    //         urlsArray: tempSoftwareListUrls,
    //       });
    //     }
    //     // console.log("all UrlsArray:", allUrlsTemp);
    //     // setSoftwareListUrls(allUrlsTemp)
    //   });
    //   console.log("all UrlsArray:", allUrlsTemp);
    //   setSoftwareListUrls(allUrlsTemp);
    // });
    //tabletypes
    // databaseCollection.onSnapshot((snapOuter) => {
    //   const table0Temp = [{}];
    //   const table1Temp = [{}];
    //   const table2Temp = [{}];
    //   const table3Temp = [{}];
    //   const table4Temp = [{}];
    //   const tableIq1Temp = [{}];
    //   const tableIq2Temp = [{}];
    //   const tableIq3Temp = [{}];
    //   const tableIq4Temp = [{}];
    //   const tableIq5Temp = [{}];
    //   const tableIq6Temp = [{}];
    //   const tableIq7Temp = [{}];
    //   const tableIq8Temp = [{}];
    //   const tableIq9Temp = [{}];
    //   const tableIq10Temp = [{}];
    //   const tableIq11Temp = [{}];
    //   const tableIq12Temp = [{}];
    //   const tableIq13Temp = [{}];
    //   const tableIq14Temp = [{}];
    //   const tableMfg1Temp = [{}];
    //   const tableEqp1Temp = [{}];
    //   const tableEqp2Temp = [{}];
    //   const tableEqp3Temp = [{}];
    //   const tableEqp4Temp = [{}];
    //   const tableEqp5Temp = [{}];
    //   const tableOq1Temp = [{}];
    //   const tableOq2Temp = [{}];
    //   const tableOq3Temp = [{}];
    //   const tableOq4Temp = [{}];
    //   const tableOq5Temp = [{}];
    //   const tableOq6Temp = [{}];
    //   const tableOq7Temp = [{}];
    //   const tableOq8Temp = [{}];
    //   const tableOq9Temp = [{}];
    //   const tableOq10Temp = [{}];
    //   const tableOq11Temp = [{}];
    //   snapOuter.forEach((record) => {
    // table0
    // databaseCollection
    //   .doc(record.id) // fat id
    //   .collection("table") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     console.log("table0 data raw all:", data);
    //     let dataSorted = data.sort((a, b) => a["index"] - b["index"]);
    //     const tempArr2d = [];
    //     dataSorted.map((dataM) => {
    //       const temp = [];
    //       temp.push(dataM["check"]); // order is important
    //       temp.push(dataM["obeservation"]); //
    //       temp.push(dataM["pre_value"]); // acceptance criteria
    //       temp.push(dataM["confirm"]); //
    //       temp.push(dataM["dev"]); ////
    //       temp.push(dataM["id"]); ////
    //       temp.push(dataM["table_title"]); //
    //       // headTemp.map((dataH, index) => {
    //       //   temp.push(dataM[dataH])
    //       // })
    //       tempArr2d.push(temp);
    //     });
    //     //setTable3(tempArr2d);
    //     table0Temp.push({ fatId: record.id, table0Data: [...tempArr2d] });
    //     console.log("table0  data report pdf: ", tempArr2d);
    //   });
    // table type1 data
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("table1") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           temp.push(dataM["tag_no"]); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["make_vendor"]); //
    //           temp.push(dataM["details"]); //
    //           temp.push(dataM["size"]); //
    //           temp.push(dataM["documents"]); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     table1Temp.push({ fatId: record.id, table1Data: [...tablesArray] });
    //     console.log("tableT1 data: ", tablesArray);
    //   });
    // databaseCollection.doc(record.id) // fatId from report
    //     .collection("table1")  // table
    //     .onSnapshot((snap) => {
    //         const data = firebaseLooper(snap);
    //         let dataSorted = data.sort((a, b) => a['serial_no'] - b['serial_no']);
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM['serial_no']); // order is important
    //             temp.push(dataM['tag_no']); //
    //             temp.push(dataM['desc']); //
    //             temp.push(dataM['make_vendor']); //
    //             temp.push(dataM['details']); //
    //             temp.push(dataM['size']); //
    //             temp.push(dataM['documents']); //
    //             temp.push(dataM['result']); //
    //             temp.push(dataM['url']); //
    //             temp.push(dataM['id']); ////
    //             temp.push(dataM['table_title']); //
    //             tempArr2d.push(temp)
    //         })
    //         //setTable1([ ...table1 , {fatId: record.id , table1Data:[...tempArr2d]}]);
    //         tabl1Temp.push({ fatId: record.id, table1Data: [...tempArr2d] })
    //         // console.log("table1 data report pdf: ", tempArr2d);
    //         //window.localStorage.setItem('table1',JSON.stringify(tempArr2d))
    //     });
    //// table type 2
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("table2") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           temp.push(dataM["tag_no"]); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["type_size"]); //
    //           temp.push(dataM["documents"]); //
    //           temp.push(dataM["result"]); //
    //           //temp.push(dataM['url']); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     table2Temp.push({ fatId: record.id, table2Data: [...tablesArray] });
    //     console.log("tableT2 data: ", tablesArray);
    //   });
    // table3
    // databaseCollection.doc(record.id) // fat id
    //     .collection("table3")  // table
    //     .onSnapshot((snap) => {
    //         const data = firebaseLooper(snap);
    //         console.log("table3 data raw all:", data)
    //         let dataSorted = data.sort((a, b) => a['serial_no'] - b['serial_no']);
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM['serial_no']); // order is important
    //             temp.push(dataM['drawing_title']); //
    //             temp.push(dataM['drawing_no']); //
    //             temp.push(dataM['compliance']); //
    //             temp.push(dataM['id']); ////
    //             temp.push(dataM['table_title']); //
    //             // headTemp.map((dataH, index) => {
    //             //   temp.push(dataM[dataH])
    //             // })
    //             tempArr2d.push(temp)
    //         })
    //         //setTable3(tempArr2d);
    //         table3Temp.push({ fatId: record.id, table3Data: [...tempArr2d] })
    //         //console.log("table3  data report pdf: ", tempArr2d)
    //     });
    //
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("table3") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           temp.push(dataM["drawing_title"]); //
    //           temp.push(dataM["drawing_no"]); //
    //           temp.push(dataM["compliance"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           // headTemp.map((dataH, index) => {
    //           //   temp.push(dataM[dataH])
    //           // })
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     table3Temp.push({ fatId: record.id, table3Data: [...tablesArray] });
    //     console.log("tableT3 data: ", tablesArray);
    //   });
    // // table4
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("table4") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           temp.push(dataM["tag_no"]); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["make_vendor"]); //
    //           temp.push(dataM["details"]); //
    //           temp.push(dataM["doc_avail"]); //
    //           temp.push(dataM["sr_avail"]); //
    //           temp.push(dataM["type"]); // document type
    //           temp.push(dataM["result"]);
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     table4Temp.push({ fatId: record.id, table4Data: [...tablesArray] });
    //     console.log("tableT4 data: ", tablesArray);
    //   });
    // databaseCollection.doc(record.id) // fat id
    //     .collection("table4")  // table4
    //     .onSnapshot((snap) => {
    //         const data = firebaseLooper(snap);
    //         console.log("table0 data raw all:", data)
    //         let dataSorted = data.sort((a, b) => a['serial_no'] - b['serial_no']);
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM['serial_no']); // order is important
    //             temp.push(dataM['tag_no']); //
    //             temp.push(dataM['desc']); //
    //             temp.push(dataM['make_vendor']); //
    //             temp.push(dataM['details']); //
    //             temp.push(dataM['doc_avail']); //
    //             temp.push(dataM['sr_avail']); //
    //             temp.push(dataM['type']); // document type
    //             temp.push(dataM['result']);
    //             temp.push(dataM['url']); //
    //             temp.push(dataM['id']); ////
    //             temp.push(dataM['table_title']); //
    //             tempArr2d.push(temp)
    //         })
    //         //setTable3(tempArr2d);
    //         table4Temp.push({ fatId: record.id, table4Data: [...tempArr2d] })
    //         // console.log("table4  data report pdf: ", tempArr2d)
    //     });
    //IQ1
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ1") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           //temp.push(dataM['tag_no']); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["make_vendor"]); //
    //           temp.push(dataM["type"]); //
    //           temp.push(dataM["size"]); //
    //           temp.push(dataM["documents"]); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq1Temp.push({
    //       fatId: record.id,
    //       tableIq1Data: [...tablesArray],
    //     });
    //     console.log("tableIq1 data: ", tablesArray);
    //   });
    // //IQ2
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ2") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           temp.push(dataM["tag_no"]); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["make_vendor"]); //
    //           temp.push(dataM["type"]); //
    //           temp.push(dataM["size"]); //
    //           temp.push(dataM["documents"]); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq2Temp.push({
    //       fatId: record.id,
    //       tableIq2Data: [...tablesArray],
    //     });
    //     console.log("tableIq2 data: ", tablesArray);
    //   });
    // //IQ3
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ3") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           temp.push(dataM["tag_no"]); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["make_vendor"]); //
    //           temp.push(dataM["model"]); //
    //           temp.push(dataM["type"]); //
    //           temp.push(dataM["size"]); //
    //           temp.push(dataM["documents"]); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq3Temp.push({
    //       fatId: record.id,
    //       tableIq3Data: [...tablesArray],
    //     });
    //     console.log("tableIq3 data: ", tablesArray);
    //   });
    // //IQ4
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ4") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           temp.push(dataM["tag_no"]); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["make_vendor"]); //
    //           temp.push(dataM["model"]); //
    //           temp.push(dataM["type"]); //
    //           temp.push(dataM["rating"]); // IQ3:Size
    //           temp.push(dataM["documents"]); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq4Temp.push({
    //       fatId: record.id,
    //       tableIq4Data: [...tablesArray],
    //     });
    //     console.log("tableIq4 data: ", tablesArray);
    //   });
    // //IQ5
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ5") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           //temp.push(dataM['tag_no']); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["make_vendor"]); //
    //           temp.push(dataM["model"]); //
    //           temp.push(dataM["type"]); //
    //           temp.push(dataM["rating"]); // IQ3:Size
    //           temp.push(dataM["documents"]); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq5Temp.push({
    //       fatId: record.id,
    //       tableIq5Data: [...tablesArray],
    //     });
    //     console.log("tableIq5 data: ", tablesArray);
    //   });
    // //IQ6
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ6") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           temp.push(dataM["tag_no"]); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["make_vendor"]); //
    //           temp.push(dataM["model"]); //
    //           //temp.push(dataM['type']); //
    //           //temp.push(dataM['rating']); // IQ3:Size
    //           temp.push(dataM["calibration_certificate_number"]); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq6Temp.push({
    //       fatId: record.id,
    //       tableIq6Data: [...tablesArray],
    //     });
    //     console.log("tableIq6 data: ", tablesArray);
    //   });
    // //IQ7
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ7") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           //temp.push(dataM['tag_no']); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["module_type"]); //
    //           temp.push(dataM["position"]); //
    //           temp.push(dataM["application"]); //
    //           //temp.push(dataM['rating']); // IQ3:Size
    //           temp.push(dataM["calibration_certificate_number"]); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq7Temp.push({
    //       fatId: record.id,
    //       tableIq7Data: [...tablesArray],
    //     });
    //     console.log("tableIq7 data: ", tablesArray);
    //   });
    // //IQ8
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ8") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           temp.push(dataM["tag_no"]); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["make_vendor"]); //
    //           temp.push(dataM["model"]); //
    //           temp.push(dataM["test_report_no"]); //
    //           //temp.push(dataM['rating']); // IQ3:Size
    //           //temp.push(dataM['calibration_certificate_number']); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq8Temp.push({
    //       fatId: record.id,
    //       tableIq8Data: [...tablesArray],
    //     });
    //     console.log("tableIq8 data: ", tablesArray);
    //   });
    // //IQ9
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ9") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           temp.push(dataM["tag_no"]); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["make_vendor"]); //
    //           temp.push(dataM["model"]); //
    //           temp.push(dataM["type"]); //
    //           //temp.push(dataM['rating']); // IQ3:Size
    //           temp.push(dataM["calibration_certificate_number"]); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq9Temp.push({
    //       fatId: record.id,
    //       tableIq9Data: [...tablesArray],
    //     });
    //     console.log("tableIq9 data: ", tablesArray);
    //   });
    // //IQ9
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ10") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["software_name"]); // order is important
    //           temp.push(dataM["make"]); //
    //           temp.push(dataM["version"]); //
    //           temp.push(dataM["application"]); //
    //           temp.push(dataM["remarks"]); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq10Temp.push({
    //       fatId: record.id,
    //       tableIq10Data: [...tablesArray],
    //     });
    //     console.log("tableIq10 data: ", tablesArray);
    //   });
    // //IQ11
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ11") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]); // order is important
    //           temp.push(dataM["tag_no"]); //
    //           temp.push(dataM["desc"]); //
    //           temp.push(dataM["make"]); //
    //           temp.push(dataM["model"]); //
    //           temp.push(dataM["range"]); //
    //           //temp.push(dataM['rating']); // IQ3:Size
    //           temp.push(dataM["documents"]); //
    //           temp.push(dataM["result"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq11Temp.push({
    //       fatId: record.id,
    //       tableIq11Data: [...tablesArray],
    //     });
    //     console.log("tableIq11 data: ", tablesArray);
    //   });
    // //IQ12
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ12") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["drawing_title"]);
    //           temp.push(dataM["drawing_no"]);
    //           temp.push(dataM["compliance"]);
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq12Temp.push({
    //       fatId: record.id,
    //       tableIq12Data: [...tablesArray],
    //     });
    //     console.log("tableIq12 data: ", tablesArray);
    //   });
    // //IQ13
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ13") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["s_no"]);
    //           temp.push(dataM["tag_no"]);
    //           temp.push(dataM["desc"]);
    //           temp.push(dataM["model_vendor"]);
    //           temp.push(dataM["model_s_no"]);
    //           temp.push(dataM["typeTable"]);
    //           temp.push(dataM["size"]);
    //           temp.push(dataM["docs"]);
    //           temp.push(dataM["result"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]);
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq13Temp.push({
    //       fatId: record.id,
    //       tableIq13Data: [...tablesArray],
    //     });
    //     console.log("tableIq13 data: ", tablesArray);
    //   });
    // //IQ14
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableIQ14") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["s_no"]);
    //           temp.push(dataM["desc"]);
    //           temp.push(dataM["model_vendor"]);
    //           temp.push(dataM["model_s_no"]);
    //           temp.push(dataM["typeTable"]);
    //           temp.push(dataM["docs"]);
    //           temp.push(dataM["result"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]);
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableIq14Temp.push({
    //       fatId: record.id,
    //       tableIq14Data: [...tablesArray],
    //     });
    //     console.log("tableIq14 data: ", tablesArray);
    //   });
    // //MFG1
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableMFG1") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["desc"]); // order is important
    //           temp.push(dataM["document_number"]); //
    //           temp.push(dataM["yes_no"]); //
    //           temp.push(dataM["url"]); //
    //           temp.push(dataM["id"]); ////
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableMfg1Temp.push({
    //       fatId: record.id,
    //       tableMfg1Data: [...tablesArray],
    //     });
    //     console.log("tableMfg1 data: ", tablesArray);
    //   });
    // //Eqp1
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableEQP1") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     console.log("record fatData:", record?.data()); //
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["desc"]);
    //           temp.push(dataM["protocol_number"]);
    //           temp.push(dataM["compliance"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableEqp1Temp.push({
    //       fatId: record.id,
    //       tableEqp1Data: [...tablesArray],
    //     });
    //     console.log("tableEqp1 data: ", tablesArray);
    //   });
    // //Eqp2
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableEQP2") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["desc"]);
    //           temp.push(dataM["page_number"]);
    //           temp.push(dataM["compliance"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableEqp2Temp.push({
    //       fatId: record.id,
    //       tableEqp2Data: [...tablesArray],
    //     });
    //     console.log("tableEqp2 data: ", tablesArray);
    //   });
    // //Eqp3
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableEQP3") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["doc"]);
    //           temp.push(dataM["test_report_number"]);
    //           temp.push(dataM["compliance"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableEqp3Temp.push({
    //       fatId: record.id,
    //       tableEqp3Data: [...tablesArray],
    //     });
    //     console.log("tableEqp3 data: ", tablesArray);
    //   });
    //Eqp4
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableEQP4") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["s_no"]);
    //           temp.push(dataM["qualification_document_name"]);
    //           temp.push(dataM["page_number"]);
    //           temp.push(dataM["nature_of_deviation"]);
    //           temp.push(dataM["major"]);
    //           temp.push(dataM["minor"]);
    //           temp.push(dataM["action_taken"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableEqp4Temp.push({
    //       fatId: record.id,
    //       tableEqp4Data: [...tablesArray],
    //     });
    //     console.log("tableEqp4 data: ", tablesArray);
    //   });
    //Eqp5
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableEQP5") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["comapnyName"]);
    //           temp.push(dataM["name"]);
    //           temp.push(dataM["signature"]);
    //           temp.push(dataM["date"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableEqp5Temp.push({
    //       fatId: record.id,
    //       tableEqp5Data: [...tablesArray],
    //     });
    //     console.log("tableEqp5 data: ", tablesArray);
    //   });
    //Oq1
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableOQ1") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]);
    //           temp.push(dataM["desc"]);
    //           temp.push(dataM["mod_number"]);
    //           temp.push(dataM["ch_number"]);
    //           temp.push(dataM["signal"]);
    //           temp.push(dataM["result"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableOq1Temp.push({
    //       fatId: record.id,
    //       tableOq1Data: [...tablesArray],
    //     });
    //     console.log("tableOq1 data: ", tablesArray);
    //   });
    //Oq2
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableOQ2") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]);
    //           temp.push(dataM["desc"]);
    //           temp.push(dataM["performance_parameters"]);
    //           // temp.push(dataM['ch_number'])
    //           // temp.push(dataM['signal'])
    //           // temp.push(dataM['result'])
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableOq2Temp.push({
    //       fatId: record.id,
    //       tableOq2Data: [...tablesArray],
    //     });
    //     console.log("tableOq2 data: ", tablesArray);
    //   });
    //Oq3
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableOQ3") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["serial_no"]);
    //           temp.push(dataM["desc"]);
    //           temp.push(dataM["required"]);
    //           // temp.push(dataM['ch_number'])
    //           // temp.push(dataM['signal'])
    //           temp.push(dataM["actuals"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableOq3Temp.push({
    //       fatId: record.id,
    //       tableOq3Data: [...tablesArray],
    //     });
    //     console.log("tableOq3 data: ", tablesArray);
    //   });
    //Oq4
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableOQ4") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           // temp.push(dataM['serial_no'])
    //           temp.push(dataM["desc"]);
    //           temp.push(dataM["general_condition"]);
    //           temp.push(dataM["actual_condition"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableOq4Temp.push({
    //       fatId: record.id,
    //       tableOq4Data: [...tablesArray],
    //     });
    //     console.log("tableOq3 data: ", tablesArray);
    //   });
    // //Oq5
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableOQ5") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           //temp.push(dataM['serial_no'])
    //           temp.push(dataM["desc"]);
    //           //temp.push(dataM['required'])
    //           // temp.push(dataM['ch_number'])
    //           temp.push(dataM["acceptance_criteria"]);
    //           temp.push(dataM["observation"]);
    //           temp.push(dataM["pass_fail"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableOq5Temp.push({
    //       fatId: record.id,
    //       tableOq5Data: [...tablesArray],
    //     });
    //     console.log("tableOq5 data: ", tablesArray);
    //   });
    // //Oq6
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableOQ6") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           //temp.push(dataM['serial_no'])
    //           temp.push(dataM["check_point"]);
    //           //temp.push(dataM['required'])
    //           // temp.push(dataM['ch_number'])
    //           temp.push(dataM["acceptance_criteria"]);
    //           temp.push(dataM["observation"]);
    //           temp.push(dataM["pass_fail"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableOq6Temp.push({
    //       fatId: record.id,
    //       tableOq6Data: [...tablesArray],
    //     });
    //     console.log("tableOq6 data: ", tablesArray);
    //   });
    // //Oq7
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableOQ7") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           //temp.push(dataM['serial_no'])
    //           temp.push(dataM["check_point"]);
    //           //temp.push(dataM['required'])
    //           // temp.push(dataM['ch_number'])
    //           temp.push(dataM["acceptance_criteria"]);
    //           temp.push(dataM["min"]);
    //           temp.push(dataM["max"]);
    //           temp.push(dataM["avg"]);
    //           temp.push(dataM["pass_fail"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableOq7Temp.push({
    //       fatId: record.id,
    //       tableOq7Data: [...tablesArray],
    //     });
    //     console.log("tableOq7 data: ", tablesArray);
    //   });
    // //Oq8
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableOQ8") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           //temp.push(dataM['serial_no'])
    //           temp.push(dataM["temprature_set_point"]);
    //           //temp.push(dataM['required'])
    //           // temp.push(dataM['ch_number'])
    //           temp.push(dataM["acceptance_criteria"]);
    //           temp.push(dataM["average_time"]);
    //           temp.push(dataM["variation"]);
    //           //temp.push(dataM['avg'])
    //           temp.push(dataM["status"]); // ok/ Not ok
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableOq8Temp.push({
    //       fatId: record.id,
    //       tableOq8Data: [...tablesArray],
    //     });
    //     console.log("tableOq8 data: ", tablesArray);
    //   });
    //Oq9
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableOQ9") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["selfSP"]);
    //           temp.push(dataM["acceptance"]);
    //           temp.push(dataM["shelfNo"]);
    //           temp.push(dataM["shelfAverage"]);
    //           temp.push(dataM["allShelvesAverage"]); // ok/ Not ok
    //           temp.push(dataM["deviation"]);
    //           temp.push(dataM["passFail"]); //
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableOq9Temp.push({
    //       fatId: record.id,
    //       tableOq9Data: [...tablesArray],
    //     });
    //     console.log("tableOq9 data: ", tablesArray);
    //   });
    //Oq10
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableOQ10") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["checkPoint"]);
    //           temp.push(dataM["results"]);
    //           temp.push(dataM["confirm"]);
    //           temp.push(dataM["deviation"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableOq10Temp.push({
    //       fatId: record.id,
    //       tableOq10Data: [...tablesArray],
    //     });
    //     console.log("tableOq10 data: ", tablesArray);
    //   });
    //Oq11
    // databaseCollection
    //   .doc(record.id) //fat id
    //   .collection("tableOQ11") // table
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     const tablesArray = []; // array of tables
    //     //console.log("record fatData:", record?.data())//
    //     record?.data()?.table_list?.map((titleData) => {
    //       let filterdData = data?.filter(
    //         (fdata) => fdata?.table_title === titleData?.table_title
    //       );
    //       if (filterdData?.length > 0) {
    //         let dataSorted = filterdData.sort(
    //           (a, b) => a["serial_no"] - b["serial_no"]
    //         );
    //         const tempArr2d = [];
    //         dataSorted.map((dataM) => {
    //           const temp = [];
    //           temp.push(dataM["recipe"]);
    //           temp.push(dataM["range"]);
    //           temp.push(dataM["value"]);
    //           temp.push(dataM["acceptanceCriteria"]);
    //           temp.push(dataM["chamberLead"]);
    //           temp.push(dataM["confirm"]);
    //           temp.push(dataM["url"]);
    //           temp.push(dataM["id"]);
    //           temp.push(dataM["table_title"]); //
    //           tempArr2d.push(temp);
    //         });
    //         tablesArray.push(tempArr2d);
    //       }
    //     });
    //     tableOq11Temp.push({
    //       fatId: record.id,
    //       tableOq11Data: [...tablesArray],
    //     });
    //     console.log("tableOq11 data: ", tablesArray);
    //     //loader :- setTimeout is for processing the data // always call this in last table
    //     setTimeout(() => {
    //       setDataLoad(false);
    //     }, 2000);
    //   });
    // tabletype data end
    // });
    // setTable0(table0Temp);
    // setTable1(table1Temp);
    // setTable2(table2Temp);
    // setTable3(table3Temp);
    // setTable4(table4Temp);
    // setIq1(tableIq1Temp);
    // setIq2(tableIq2Temp);
    // setIq3(tableIq3Temp);
    // setIq4(tableIq4Temp);
    // setIq5(tableIq5Temp);
    // setIq6(tableIq6Temp);
    // setIq7(tableIq7Temp);
    // setIq8(tableIq8Temp);
    // setIq9(tableIq9Temp);
    // setIq10(tableIq10Temp);
    // setIq11(tableIq11Temp);
    // setIq12(tableIq12Temp);
    // setIq13(tableIq13Temp);
    // setIq14(tableIq14Temp);
    // setMfg1(tableMfg1Temp);
    // setEqp1(tableEqp1Temp);
    // setEqp2(tableEqp2Temp);
    // setEqp3(tableEqp3Temp);
    // setEqp4(tableEqp4Temp);
    // setEqp5(tableEqp5Temp);
    // setOq1(tableOq1Temp);
    // setOq2(tableOq2Temp);
    // setOq3(tableOq3Temp);
    // setOq4(tableOq4Temp);
    // setOq5(tableOq5Temp);
    // setOq6(tableOq6Temp);
    // setOq7(tableOq7Temp);
    // setOq8(tableOq8Temp);
    // setOq9(tableOq9Temp);
    // setOq10(tableOq10Temp);
    // setOq11(tableOq11Temp);
    // //loader :- setTimeout is for processing the data
    setTimeout(() => {
      setDataLoad(false);
    }, 2000);
    // }
    // );
  }, []);

  //console.log(reportTableDetails)

  //doc.save(fileName);

  return (
    <div>
      {/* PrintReportPdf
            : {companyInfo?.url} */}
      {/* <button onClick={() => doc.save(fileName)}>save</button> */}
      <PrintReport
        machineAll={machineAll}
        reportInfo={reportInfo}
        companyInfo={companyInfo}
        reportDetails={reportDetails}
        companyImageUrl={companyImageUrl}
        approvalTable={approvalTable}
        approvalUrlOnly={approvalUrlOnly}
        postApprovalTable={postApprovalTable}
        postApprovalUrlOnly={postApprovalUrlOnly}
        softwareListUrls={softwareListUrls}
        table0={table0}
        table1={table1}
        table2={table2}
        table3={table3}
        table4={table4}
        iq1={iq1}
        iq2={iq2}
        iq3={iq3}
        iq4={iq4}
        iq5={iq5}
        iq6={iq6}
        iq7={iq7}
        iq8={iq8}
        iq9={iq9}
        iq10={iq10}
        iq11={iq11}
        iq12={iq12}
        iq13={iq13}
        iq14={iq14}
        mfg1={mfg1}
        eqp1={eqp1}
        eqp2={eqp2}
        eqp3={eqp3}
        eqp4={eqp4}
        eqp5={eqp5}
        oq1={oq1}
        oq2={oq2}
        oq3={oq3}
        oq4={oq4}
        oq5={oq5}
        oq6={oq6}
        oq7={oq7}
        oq8={oq8}
        oq9={oq9}
        oq10={oq10}
        oq11={oq11}
        pq1={pq1}
        pq2={pq2}
        pq2_staticData={pq2_staticData}
        pq3={pq3}
        pq3_staticData={pq3_staticData}
        pq4={pq4}
        pq4_staticData={pq4_staticData}
        pq5={pq5}
      />

      {dataLoad && (
        <div className="flex justify-center">
          <CircularProgress color="inherit" />
        </div>
      )}
    </div>
  );
}
//

/////////// pdf.js implimentation //////////

export function PrintReport({
  machineAll,
  reportInfo,
  companyInfo,
  reportDetails,
  companyImageUrl,
  approvalTable,
  approvalUrlOnly,
  postApprovalTable,
  postApprovalUrlOnly,
  softwareListUrls,
  table0,
  table1,
  table2,
  table3,
  table4,
  iq1,
  iq2,
  iq3,
  iq4,
  iq5,
  iq6,
  iq7,
  iq8,
  iq9,
  iq10,
  iq11,
  iq12,
  iq13,
  iq14,
  mfg1,
  eqp1,
  eqp2,
  eqp3,
  eqp4,
  eqp5,
  oq1,
  oq2,
  oq3,
  oq4,
  oq5,
  oq6,
  oq7,
  oq8,
  oq9,
  oq10,
  oq11,
  pq1,
  pq2, // testing 0.3
  pq2_staticData,
  pq3,
  pq3_staticData,
  pq4,
  pq4_staticData,
  pq5,
}) {
  //console.log("approval table print2n:", approvalTable);
  //console.log("image url:", companyImageUrl)
  //console.log("image urls approvals:", approvalUrlOnly)
  //const [processing, setProcessing ]= useState("false");
  console.log("reportDetails:- ", reportDetails);
  console.log("reportInfo:- ", reportInfo);
  console.log("machineAll:- ", machineAll);
  console.log("pq2 static inside:", pq2_staticData);

  ///// testing 0.4
  const filterTableArrayBasedOnTitleForPq1 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        speed_of_machine: mData[1],
        "speed_in_rpm:": mData[2],
        fat_report_id: mData[3],
        id: mData[4],
        table_title: mData[6],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq2 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        parameter: mData[0],
        acceptance_criteria: mData[1],
        initial: mData[2],
        min10: mData[3],
        min20: mData[4],
        min30: mData[5],
        fat_report_id: mData[6],
        id: mData[7],
        table_title: mData[9],
      });
    });

    // for footer

    pq2_staticData?.map((mData) => {
      console.log("map of pq2:", mData);

      tempTableData?.push(
        {
          parameter: "Pre compression force", // key are for refrence only, not used
          pre_compression_force: mData?.pre_compression_force,
          fat_report_id: mData?.fat_id,
          table_title: mData?.table_title,
        },
        {
          parameter: "Main compression force",
          main_compression_force: mData?.main_compression_force,
          fat_report_id: mData?.fat_id,
          table_title: mData?.table_title,
        },
        {
          parameter: "Feeder speed",
          feeder_speed: mData?.feeder_speed,
          fat_report_id: mData?.fat_id,
          table_title: mData?.table_title,
        },
      );
    });

    console.log("pq2 after static add:", tempTableData);

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq3 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        parameter: mData[0],
        specification: mData[1],
        rpm10: mData[2],
        rpm20: mData[3],
        rpm40: mData[4],
        fat_report_id: mData[5],
        id: mData[6],
        table_title: mData[8],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq4 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        parameter: mData[1],
        limits: mData[2],
        rpm40: mData[3],
        rpm45: mData[4],
        rpm50: mData[5],
        fat_report_id: mData[6],
        id: mData[7],
        table_title: mData[9],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        eqp_name: mData[1],
        eqp_id: mData[2],
        make: mData[3],
        test_param: mData[4],
        design_range: mData[5],
        qualification_range: mData[6],
        product_qualification_range: mData[7],
        tolerance: mData[8],
        fat_report_id: mData[9],
        id: mData[10],
        table_title: mData[12],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPostApprovalTable = (tData) => {
    // not that much needed in Approvals, but to keep the flow we use this
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        prep_by_tech_transfer: mData[0],
        rev_by_tech_transfer: mData[1],
        production: mData[2],
        engineering: mData[3],
        rev_by_qa: mData[4],
        approved_by_qa: mData[5],
        id: mData[6],
        table_title: mData[8],
      });
    });
    console.log("fdd:", tempTableData);
    return tempTableData;
  };
  ////

  const [landScape, setLandScape] = useState(false);
  const { currentMode } = useStateContext();

  const handleSavePdf = () => {
    //alert("Close this alert and wait for 5-10 seconds...")
    var doc = new jsPDF();
    if (landScape) {
      doc = new jsPDF(
        // landScape ? ('l', 'mm', [297, 210]) : ('p','mm',[210, 297])
        "l",
        "mm",
        [297, 210],
      ).setProperties({ title: "String Splitting" });
      savePdf(doc);
    } else {
      doc = new jsPDF(
        //landScape ? ('l', 'mm', [297, 210]) : ('p','mm',[210, 297])
        "p",
        "mm",
        [210, 297],
      ).setProperties({ title: "String Splitting" });
      savePdf(doc);
    }
  };

  const savePdf = (doc) => {
    //alert("2nd", companyInfo?.url);
    //var node = doc.outline.add(null, 'Outline', null);
    // doc.outline.add(node, 'Hello', {pageNumber:1});

    // landScape
    var widthLandScapeCof = 1;
    var heightLandScapeCof = 1;
    if (landScape) {
      widthLandScapeCof = 297 / 210;
      heightLandScapeCof = 210 / 297;
    }

    //var pageWidth = 8.5 * 25.4;
    var pageWidth = 210 * widthLandScapeCof; //(8.5 * 25.4); //* 297 / 215 ;
    var lineHeight = 1.2;
    var margin = 18; //10 //0.5 * 25.4;
    var maxLineWidth = pageWidth - margin * 2;
    var fontSizeCoverBold = 15;
    var fontSizeCoverBolder = 22;
    var fontSizeHeadTop = 13;
    var fontSizeHeader = 12;
    var fontSizeParagraph = 10;
    var ptsPerMm = 72 / 24.4; // ptsPerMM
    // action sheet
    var widthRecActionSheet = pageWidth - margin * 2;
    var heightForSmallARecActionSheet = 10 * heightLandScapeCof;
    var heightForLargeRecActionSheet = 35 * heightLandScapeCof;
    const fonSizeActionSheet = 9;

    // var oneLineHeight = (fontSize * lineHeight) / ptsPerMm;

    var yAxisBody = 42; //36;//45; // after adding head and gap . page height : 297mm aprox

    // // splitTextToSize takes your string and turns it in to an array of strings,
    // // each of which can be displayed within the specified maxLineWidth.
    // var textLines = doc
    //     .setFont("helvetica")
    //     .setFontSize(fontSize)
    //     .splitTextToSize(text, maxLineWidth);

    // // doc.text can now add those lines easily; otherwise, it would have run text off the screen!
    // doc.text(textLines, margin, margin + 2 * oneLineHeight);

    // We can also calculate the height of the text contents very simply:
    // var textHeight = (textLines.length * fontSize * lineHeight) / ptsPerMm;

    // page number
    const addFooters = (doc) => {
      const pageCount = doc.internal.getNumberOfPages();

      doc.setFont("helvetica", "normal");
      doc.setFontSize(10);
      for (var i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.text(
          "Page " + String(i) + " of " + String(pageCount),
          180 * widthLandScapeCof,
          288 * heightLandScapeCof,
          {
            align: "center",
          },
        );
      }
    };
    //

    // const doc = new jsPDF(
    //     // {unit: "mm",
    //     // lineHeight: lineHeight,
    //     // //orientation: "landscape",
    //     // }
    //     landScape ? ('l', 'mm', [297, 210]) : ('p','mm',[210, 297])

    // ).setProperties({ title: "String Splitting" });

    const head = () => {
      doc.setDrawColor(0, 0, 0);
      doc.setLineWidth(0.2);
      doc.rect(
        8 * widthLandScapeCof,
        8 * heightLandScapeCof,
        194 * widthLandScapeCof,
        283 * heightLandScapeCof,
      ); // 2,2,206, 293

      doc.setLineWidth(0.1);
      doc.rect(12 * widthLandScapeCof, 12, 40 * widthLandScapeCof, 30); //6, 6, 40, 30
      doc.rect(52 * widthLandScapeCof, 12, 146 * widthLandScapeCof, 30); //46, 6, 158, 30

      doc.setFontSize(fontSizeHeadTop);
      doc.setFont("helvetica", "bold");

      // Fix: Access machineAll.data instead of machineAll
      const machineData = machineAll?.data || [];
      const machine = machineData.find((data) => data?._id === reportInfo?.mid);

      doc.text(machine?.title || "", 55 * widthLandScapeCof, 17); // 60,11

      let description =
        reportInfo?.description?.length < 60
          ? reportInfo?.description
          : `${reportInfo?.description?.slice(0, 60)}...`;
      doc.text(description, 55 * widthLandScapeCof, 23);
      doc.text("Model: " + (machine?.model || ""), 55 * widthLandScapeCof, 29);
      doc.text(
        "Serial No: " + (machine?.serialNo || ""),
        55 * widthLandScapeCof,
        35,
      );
      doc.text(
        "Protocol Number: " + reportInfo?.protocol_no,
        55 * widthLandScapeCof,
        41,
      );

      doc.setFontSize(fontSizeParagraph);
      doc.setFont("helvetica", "normal");
      doc.text(
        reportInfo?.rev_no ? "Rev No: " + reportInfo?.rev_no : "Rev No: Na",
        175 * widthLandScapeCof,
        41,
      );

      doc.addImage(
        logo,
        18 * widthLandScapeCof,
        16,
        30 * widthLandScapeCof,
        20,
      ); //11, 10, 30, 20
    };

    ///// Front or cover Page
    head();
    var yAxisBodyCoverTemp = yAxisBody;
    let oneLineHeightBold = (fontSizeCoverBold * lineHeight) / ptsPerMm; //
    let oneLineHeightBolder = (fontSizeCoverBolder * lineHeight) / ptsPerMm; //
    // cover head title 1
    let textLinesTitleFirst = doc
      .setFont("helvetica", "bold")
      .setFontSize(fontSizeCoverBolder)
      .splitTextToSize("Arizon Systems Pvt. Ltd.", maxLineWidth);

    doc.text(
      textLinesTitleFirst,
      margin,
      yAxisBodyCoverTemp + 1.5 * oneLineHeightBolder,
    ); //

    let textHeightFirst =
      (textLinesTitleFirst.length * fontSizeCoverBolder * lineHeight) /
      ptsPerMm; // area height
    yAxisBodyCoverTemp =
      yAxisBodyCoverTemp + 1.5 * oneLineHeightBolder + textHeightFirst;

    //// cover machine name 2
    let textLinesTitleSec = doc
      .setFont("helvetica", "bold")
      .setFontSize(fontSizeCoverBolder)
      .splitTextToSize(
        (
          machineAll?.data?.find((data) => data?._id === reportInfo?.mid)
            ?.title || ""
        ).toUpperCase(),
        maxLineWidth,
      );

    doc.text(
      textLinesTitleSec,
      margin,
      yAxisBodyCoverTemp + oneLineHeightBolder,
    ); //
    let textHeightSec =
      (textLinesTitleSec.length * fontSizeCoverBolder * lineHeight) / ptsPerMm; // area height
    yAxisBodyCoverTemp =
      yAxisBodyCoverTemp + oneLineHeightBolder + textHeightSec;
    // cover fat report title 2.1
    let textLinesTitleThird = doc
      .setFont("helvetica", "bold")
      .setFontSize(fontSizeCoverBolder)
      .splitTextToSize(reportInfo?.description, maxLineWidth);

    doc.text(textLinesTitleThird, margin, yAxisBodyCoverTemp + lineHeight); // oneLineHeightBolder
    let textHeightThird =
      (textLinesTitleThird.length * fontSizeCoverBolder * lineHeight) /
      ptsPerMm; // area height
    yAxisBodyCoverTemp =
      yAxisBodyCoverTemp + oneLineHeightBolder + textHeightThird;

    //// cover 'Customer' static head 3.1
    doc.text("Customer", margin, yAxisBodyCoverTemp + oneLineHeightBolder);
    let textHeightFourth = (1 * fontSizeCoverBolder * lineHeight) / ptsPerMm; // area height (static text)
    yAxisBodyCoverTemp =
      yAxisBodyCoverTemp + oneLineHeightBolder + textHeightFourth;
    // cover customer name 3.2
    let textLinesTitleFifth = doc
      .setFont("helvetica", "bold")
      .setFontSize(fontSizeCoverBolder)
      .splitTextToSize(reportInfo?.customer_name ?? "NA", maxLineWidth);

    doc.text(textLinesTitleFifth, margin, yAxisBodyCoverTemp + lineHeight); //oneLineHeightBolder
    let textHeightFifth =
      (textLinesTitleFifth.length * fontSizeCoverBolder * lineHeight) /
      ptsPerMm; // area height
    yAxisBodyCoverTemp = yAxisBodyCoverTemp + lineHeight + textHeightFifth;
    // cover customer address 3.3
    let textLinesTitleSixth = doc
      .setFont("helvetica", "bold")
      .setFontSize(fontSizeCoverBold)
      .splitTextToSize(
        reportInfo?.address ?? "NA",
        maxLineWidth,
        maxLineWidth / 2,
      );

    doc.text(textLinesTitleSixth, margin, yAxisBodyCoverTemp + lineHeight); //oneLineHeightBolder
    let textHeightSixth =
      (textLinesTitleSixth.length * fontSizeCoverBold * lineHeight) / ptsPerMm; // area height
    yAxisBodyCoverTemp = yAxisBodyCoverTemp + lineHeight + textHeightSixth;

    // cover model and serial 4.1
    doc.setFontSize(fontSizeCoverBolder);
    doc.text(
      "Model No. " +
        machineAll?.data?.find((data) => data?._id === reportInfo?.mid)?.model,
      margin,
      yAxisBodyCoverTemp + oneLineHeightBolder * 1.5,
    );
    let textHeightSeventh = (1 * fontSizeCoverBolder) / ptsPerMm; // area height (static text) single line
    yAxisBodyCoverTemp =
      yAxisBodyCoverTemp + 1.5 * oneLineHeightBolder + textHeightSeventh;
    // 4.2
    doc.text(
      "Serial No. " +
        machineAll?.data?.find((data) => data?._id === reportInfo?.mid)
          ?.serialNo,
      margin,
      yAxisBodyCoverTemp,
    ); // should be  + , need to debug and adjust all
    let textHeightEighth = (1 * fontSizeCoverBolder) / ptsPerMm; // area height (static text) single line
    yAxisBodyCoverTemp =
      yAxisBodyCoverTemp + 1 * oneLineHeightBolder + textHeightEighth;

    ///// front page end

    //////// INDEX ;
    doc.addPage();
    head();
    //const currentPageNo = doc.internal.getCurrentPageInfo().pageNumber;
    //doc.text("page: " + currentPageNo , margin , yAxisBody + 10)
    var yAxisBodyIndexTemp = yAxisBody;
    doc.setFont("helvetica", "bold");
    doc.setFontSize(fontSizeHeader);
    let oneLineHeight = (fontSizeHeader * lineHeight) / ptsPerMm; //
    doc.text(
      "TABLE OF CONTENTS",
      194 / 2.5,
      yAxisBodyIndexTemp + 3 * oneLineHeight,
    );
    reportDetails
      ?.sort((a, b) => a["index"] - b["index"])
      ?.map((data, index) => {
        if (yAxisBodyIndexTemp > 270) {
          yAxisBodyIndexTemp = yAxisBody + 2 * lineHeight;
          doc.addPage();
          head();
        }
        //body
        doc.setFont(undefined, "normal");
        let textLinesTitle = doc
          .setFont("helvetica")
          .setFontSize(fontSizeParagraph)
          .splitTextToSize(
            index + 1 + ".0     " + data?.title?.toUpperCase(),
            maxLineWidth,
          );

        let oneLineHeight = (fontSizeParagraph * lineHeight) / ptsPerMm; //

        doc.text(
          textLinesTitle,
          margin,
          yAxisBodyIndexTemp + 6 * oneLineHeight,
        ); //

        let textHeight =
          (textLinesTitle.length * fontSizeParagraph * lineHeight) / ptsPerMm; //
        yAxisBodyIndexTemp =
          yAxisBodyIndexTemp + 1 * oneLineHeight + textHeight;
        //body close
      });
    //index close

    // //approval table start // til line num: 2958
    // doc.addPage();
    // head();
    // if (approvalTable?.length > 0) {
    //   // console.log("aproval Data with type;", approvalTable)
    //   var customertableHeight = 0; // height of cutomer table
    //   doc.setFontSize(fontSizeHeader);
    //   doc.setFont("helvetica", "bold");
    //   doc.text("PRE-APPROVAL SIGNATURES", margin, 50); // 10, 44
    //   ////Vendor
    //   doc.text(
    //     "Granules India Limited.",
    //     (pageWidth + 15 - 2 * margin) / 2,
    //     50 + 10
    //   ); // 15 for adjutment
    //   //doc.line(10, 45, 95, 45); // (x,y,x,y)
    //   autoTable(doc, {
    //     // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
    //     startY: 50 + 15,
    //     headStyles: {
    //       fillColor: "#bbb",
    //       textColor: "#000",
    //       theme: "grid",
    //       lineWidth: 0.1,
    //       lineColor: "#000",
    //     },
    //     theme: "grid",
    //     head: [
    //       [
    //         { content: "Name", styles: { halign: "center", valign: "middle" } },
    //         {
    //           content: "Email",
    //           styles: { halign: "center", valign: "middle" },
    //         },
    //         {
    //           content: "Date",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //         {
    //           content: "Approval",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //       ],
    //     ],

    //     body: approvalTable?.filter(
    //       (fData) => fData[fData?.length - 1] === "vendor"
    //     ),
    //     bodyStyles: {
    //       minCellHeight: 10,
    //       minCellWidth: 40,
    //       valign: "middle",
    //       halign: "center",
    //       lineColor: "#000",
    //     },
    //     didDrawPage: (d) => {
    //       customertableHeight = d.cursor.y;
    //     },
    //     margin: { left: margin, right: margin },
    //   });

    //   /// Customer
    //   doc.text(
    //     reportInfo?.customer_name,
    //     (pageWidth + 15 - 2 * margin) / 2,
    //     customertableHeight + 10
    //   ); // 15 for adjutment
    //   autoTable(doc, {
    //     startY: customertableHeight + 15,
    //     headStyles: {
    //       fillColor: "#bbb",
    //       textColor: "#000",
    //       theme: "grid",
    //       lineWidth: 0.1,
    //       lineColor: "#000",
    //     },
    //     theme: "grid",
    //     head: [
    //       [
    //         { content: "Name", styles: { halign: "center", valign: "middle" } },
    //         {
    //           content: "Email",
    //           styles: { halign: "center", valign: "middle" },
    //         },
    //         {
    //           content: "Date",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //         {
    //           content: "Approval",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //       ],
    //     ],

    //     body: approvalTable?.filter(
    //       (fData) => fData[fData?.length - 1] === "customer"
    //     ),
    //     bodyStyles: {
    //       minCellHeight: 10,
    //       minCellWidth: 40,
    //       valign: "middle",
    //       halign: "center",
    //       lineColor: "#000",
    //     },
    //     margin: { left: margin, right: margin },
    //     // didDrawPage: (d) => {customertableHeight = (d.cursor.y)},
    //   });
    // } else {
    //   var customertableHeightElse = 0; // height of cutomer table
    //   doc.setFontSize(fontSizeHeader);
    //   doc.setFont("helvetica", "bold");
    //   doc.text("PRE-APPROVAL SIGNATURES", margin, 50);
    //   ////Vendor
    //   doc.text(
    //     "Lyophilization Systems India Pvt. Ltd.",
    //     105,
    //     50 + 10,
    //     null,
    //     null,
    //     "center"
    //   ); // 105 is for adjutment and static
    //   //doc.line(10, 45, 95, 45); // (x,y,x,y)
    //   autoTable(doc, {
    //     // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
    //     startY: 50 + 15,
    //     headStyles: {
    //       fillColor: "#bbb",
    //       textColor: "#000",
    //       theme: "grid",
    //       lineWidth: 0.1,
    //       lineColor: "#000",
    //     },
    //     theme: "grid",
    //     head: [
    //       [
    //         { content: "Name", styles: { halign: "center", valign: "middle" } },
    //         {
    //           content: "Email",
    //           styles: { halign: "center", valign: "middle" },
    //         },
    //         {
    //           content: "Date",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //         {
    //           content: "Approval",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //       ],
    //     ],

    //     body: [[], [], []],
    //     bodyStyles: {
    //       minCellHeight: 10,
    //       minCellWidth: 40,
    //       valign: "middle",
    //       halign: "center",
    //       lineColor: "#000",
    //     },
    //     didDrawPage: (d) => {
    //       customertableHeightElse = d.cursor.y;
    //     },
    //     margin: { left: margin, right: margin },
    //   });

    //   /// Customer
    //   doc.text(
    //     reportInfo?.customer_name ?? "Na",
    //     105,
    //     customertableHeightElse + 10,
    //     null,
    //     null,
    //     "center"
    //   ); // 15 for adjutment
    //   autoTable(doc, {
    //     startY: customertableHeightElse + 15,
    //     headStyles: {
    //       fillColor: "#bbb",
    //       textColor: "#000",
    //       theme: "grid",
    //       lineWidth: 0.1,
    //       lineColor: "#000",
    //     },
    //     theme: "grid",
    //     head: [
    //       [
    //         { content: "Name", styles: { halign: "center", valign: "middle" } },
    //         {
    //           content: "Email",
    //           styles: { halign: "center", valign: "middle" },
    //         },
    //         {
    //           content: "Date",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //         {
    //           content: "Approval",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //       ],
    //     ],

    //     body: [[], [], []],
    //     bodyStyles: {
    //       minCellHeight: 10,
    //       minCellWidth: 40,
    //       valign: "middle",
    //       halign: "center",
    //       lineColor: "#000",
    //     },
    //     margin: { left: margin, right: margin },
    //     // didDrawPage: (d) => {customertableHeight = (d.cursor.y)},
    //   });
    // }
    // //// aproval table ends

    {
      reportDetails?.map((data, index) => {
        // data?.template != 0 -> we can fremove it letter as we are keeping type only
        if (data?.type !== 0) {
          var yAxisBodyTemp = yAxisBody;
          doc.addPage();
          // const currentPageNumber = doc.getFontSize()
          // doc.outline.add(node, reportDetails[index]?.title?.toUpperCase(), {pageNumber: 1});
          // console.log("pageNum",currentPageNumber)
          head();
          // title
          doc.setFont(undefined, "bold");
          let textLinesTitle = doc
            .setFont("helvetica")
            .setFontSize(fontSizeHeader)
            .splitTextToSize(
              reportDetails[index]?.title?.toUpperCase() || "",
              maxLineWidth,
            );

          var oneLineHeight = fontSizeHeader / ptsPerMm; //

          doc.text(textLinesTitle, 18, yAxisBodyTemp + 2 * oneLineHeight); //

          let textHeight =
            (textLinesTitle.length * fontSizeHeader * lineHeight) / ptsPerMm; //
          yAxisBodyTemp = yAxisBodyTemp + 2 * oneLineHeight + textHeight; //
          // title end

          //doc.line(10, 50, Math.ceil(reportDetails[index]?.title?.length * 4.2), 50);

          //non-conformity

          // common function for string section
          const checkPageHeightAndAddPageForStringSection = () => {
            if (yAxisBodyTemp > 280 * heightLandScapeCof) {
              yAxisBodyTemp = yAxisBody + 2 * lineHeight;
              doc.addPage();
              head();
            }
          };

          //

          if (data?.type === 6) {
            //non conformity
            doc.setFontSize(10);
            doc.rect(margin, 60, maxLineWidth, 15);
            doc.text(
              "Number of Non-Confermity Records attached?",
              margin + 2,
              60 + 9,
            );
            //doc.text("10", 14+2+130 , 60+9);
            doc.text(
              "--------------------------------",
              margin + 2 + 100,
              60 + 9,
            );
            doc.rect(margin, 60 + 15, maxLineWidth, 15);
            doc.line(
              (maxLineWidth + margin) / 2,
              60 + 15,
              (maxLineWidth + margin) / 2,
              60 + 15 + 15,
            ); //tick
            doc.text("Checked by", margin + 2, 60 + 15 + 9);
            doc.text("--variable--", margin + 2 + 20, 60 + 15 + 9);
            doc.text("Date", (maxLineWidth + margin) / 2 + 2, 60 + 15 + 9);
            doc.text(
              "--variable--",
              (maxLineWidth + margin) / 2 + 2 + 20,
              60 + 15 + 9,
            );
          }

          //non-conformity

          //0 desc
          if (data?.desc?.blocks?.length > 0) {
            data?.desc?.blocks?.map((mData) => {
              // if (yAxisBodyTemp > 280 * (215 / 297)) {
              //     yAxisBodyTemp = yAxisBody + 2 * lineHeight;
              //     doc.addPage();
              //     head();
              // }
              checkPageHeightAndAddPageForStringSection();

              if (mData?.type === "header") {
                doc.setFont(undefined, "bold");
                let textLinesTitle = doc
                  .setFont("helvetica")
                  .setFontSize(fontSizeHeader)
                  .splitTextToSize(
                    mData?.data?.text?.replace(/<b>|<\/b>/g, ""),
                    maxLineWidth,
                  );

                let oneLineHeight = fontSizeHeader / ptsPerMm; //

                doc.text(textLinesTitle, 18, yAxisBodyTemp + oneLineHeight); //

                let textHeight =
                  (textLinesTitle.length * fontSizeHeader * lineHeight) /
                  ptsPerMm; // textArea height
                yAxisBodyTemp = yAxisBodyTemp + oneLineHeight + textHeight;
              } else if (mData?.type === "paragraph") {
                doc.setFont(undefined, "normal");
                var paragraphData = mData?.data?.text
                  ?.toString()
                  .replace(/<\/?b>|&nbsp;/g, "")
                  ?.replace(/&amp;/g, "&");
                let textLinesTitle = doc
                  .setFont("helvetica")
                  .setFontSize(fontSizeParagraph)
                  .splitTextToSize(paragraphData, maxLineWidth);

                let oneLineHeight = fontSizeParagraph / ptsPerMm; //

                doc.text(textLinesTitle, 18, yAxisBodyTemp + 1 * oneLineHeight); //

                let textHeight =
                  (textLinesTitle.length * fontSizeParagraph * lineHeight) /
                  ptsPerMm; //
                yAxisBodyTemp = yAxisBodyTemp + 1 * oneLineHeight + textHeight;
              }
            });
          }
          // 0.2
          if (data?.addDesc?.blocks?.length > 0) {
            data?.addDesc?.blocks?.map((mData) => {
              // if (yAxisBodyTemp > 280 * (215 / 297)) {
              //     yAxisBodyTemp = yAxisBody + 2 * lineHeight;
              //     doc.addPage();
              //     head();
              // }
              checkPageHeightAndAddPageForStringSection();

              if (mData?.type === "header") {
                doc.setFont(undefined, "bold");
                let textLinesTitle = doc
                  .setFont("helvetica")
                  .setFontSize(fontSizeHeader)
                  .splitTextToSize(
                    mData?.data?.text?.replace(/<b>|<\/b>/g, ""),
                    maxLineWidth,
                  );

                let oneLineHeight = fontSizeHeader / ptsPerMm; //

                doc.text(textLinesTitle, 18, yAxisBodyTemp + oneLineHeight); //

                let textHeight =
                  (textLinesTitle.length * fontSizeHeader * lineHeight) /
                  ptsPerMm; // textArea height
                yAxisBodyTemp = yAxisBodyTemp + oneLineHeight + textHeight;
              } else if (mData?.type === "paragraph") {
                doc.setFont(undefined, "normal");
                var paragraphData = mData?.data?.text
                  ?.toString()
                  .replace(/<\/?b>|&nbsp;/g, "")
                  ?.replace(/&amp;/g, "&");
                let textLinesTitle = doc
                  .setFont("helvetica")
                  .setFontSize(fontSizeParagraph)
                  .splitTextToSize(paragraphData, maxLineWidth);

                let oneLineHeight = fontSizeParagraph / ptsPerMm; //

                doc.text(textLinesTitle, 18, yAxisBodyTemp + 1 * oneLineHeight); //

                let textHeight =
                  (textLinesTitle.length * fontSizeParagraph * lineHeight) /
                  ptsPerMm; //
                yAxisBodyTemp = yAxisBodyTemp + 1 * oneLineHeight + textHeight;
              }
            });
          }

          //1
          if (data?.objective?.length) {
            // head
            // if (yAxisBodyTemp > 270) {
            //     yAxisBodyTemp = yAxisBody + 2 * lineHeight;
            //     doc.addPage();
            //     head(data?.rev_no);
            // }
            checkPageHeightAndAddPageForStringSection();

            doc.setFont(undefined, "bold");
            let textLinesTitleHead = doc
              .setFont("helvetica")
              .setFontSize(fontSizeHeader)
              .splitTextToSize("OBJECTIVE", maxLineWidth);

            let oneLineHeightHead = fontSizeHeader / ptsPerMm; //

            doc.text(
              textLinesTitleHead,
              margin,
              yAxisBodyTemp + 2 * oneLineHeightHead,
            ); //

            let textHeightHead =
              (textLinesTitleHead.length * fontSizeHeader * lineHeight) /
              ptsPerMm; //
            yAxisBodyTemp =
              yAxisBodyTemp + 2 * oneLineHeightHead + textHeightHead;
            // head close
            //body
            doc.setFont(undefined, "normal");
            let textLinesTitle = doc
              .setFont("helvetica")
              .setFontSize(fontSizeParagraph)
              .splitTextToSize(data?.objective, maxLineWidth);

            let oneLineHeight = (fontSizeParagraph * lineHeight) / ptsPerMm; //

            doc.text(textLinesTitle, margin, yAxisBodyTemp + 1 * oneLineHeight); //

            let textHeight =
              (textLinesTitle.length * fontSizeParagraph * lineHeight) /
              ptsPerMm; //
            yAxisBodyTemp = yAxisBodyTemp + 1 * oneLineHeight + textHeight;
            //body close
          }

          //2
          if (data?.method?.length) {
            // string
            // head
            // if (yAxisBodyTemp > 270) {
            //     yAxisBodyTemp = yAxisBody + 2 * lineHeight;
            //     doc.addPage();
            //     head(data?.rev_no);
            // }
            checkPageHeightAndAddPageForStringSection();

            doc.setFont(undefined, "bold");
            let textLinesTitleHead = doc
              .setFont("helvetica")
              .setFontSize(fontSizeHeader)
              .splitTextToSize("METHOD", maxLineWidth);

            let oneLineHeightHead = (fontSizeHeader * lineHeight) / ptsPerMm; //

            doc.text(
              textLinesTitleHead,
              margin,
              yAxisBodyTemp + 2 * oneLineHeightHead,
            ); //

            let textHeightHead =
              (textLinesTitleHead.length * fontSizeHeader * lineHeight) /
              ptsPerMm; //
            yAxisBodyTemp =
              yAxisBodyTemp + 2 * oneLineHeightHead + textHeightHead;
            // head close
            //body
            doc.setFont(undefined, "normal");
            let textLinesTitle = doc
              .setFont("helvetica")
              .setFontSize(fontSizeParagraph)
              .splitTextToSize(data?.method, maxLineWidth);

            let oneLineHeight = (fontSizeParagraph * lineHeight) / ptsPerMm; //

            doc.text(textLinesTitle, margin, yAxisBodyTemp + 1 * oneLineHeight); //

            let textHeight =
              (textLinesTitle.length * fontSizeParagraph * lineHeight) /
              ptsPerMm; //
            yAxisBodyTemp = yAxisBodyTemp + 1 * oneLineHeight + textHeight;
            //body close
          }

          //3
          if (data?.pre?.length) {
            // preRequest data is an array of strings

            // if (yAxisBodyTemp > 270) {
            //     yAxisBodyTemp = yAxisBody + 2 * lineHeight;
            //     doc.addPage();
            //     head(data?.rev_no);
            // }
            checkPageHeightAndAddPageForStringSection();
            // head
            doc.setFont(undefined, "bold");
            let textLinesTitleHead = doc
              .setFont("helvetica")
              .setFontSize(fontSizeHeader)
              .splitTextToSize("PREREQUISITES", maxLineWidth);

            let oneLineHeightHead = fontSizeHeader / ptsPerMm; //

            doc.text(
              textLinesTitleHead,
              margin,
              yAxisBodyTemp + 2 * oneLineHeightHead,
            ); //

            let textHeightHead =
              (textLinesTitleHead.length * fontSizeHeader * lineHeight) /
              ptsPerMm; //
            yAxisBodyTemp =
              yAxisBodyTemp + 2 * oneLineHeightHead + textHeightHead;
            // head close
            //body
            data?.pre?.map((mData, index) => {
              checkPageHeightAndAddPageForStringSection();
              doc.setFont(undefined, "normal");
              let textLinesTitle = doc
                .setFont("helvetica")
                .setFontSize(fontSizeParagraph)
                .splitTextToSize(mData, maxLineWidth);

              let oneLineHeight = fontSizeParagraph / ptsPerMm; //

              doc.text(
                textLinesTitle,
                margin,
                yAxisBodyTemp + 1 * oneLineHeight,
              ); //

              let textHeight =
                (textLinesTitle.length * fontSizeParagraph * lineHeight) /
                ptsPerMm; //
              yAxisBodyTemp = yAxisBodyTemp + 1 * oneLineHeight + textHeight;
            });
            //body close
          }

          //4
          if (data?.procedure?.length) {
            //procedure data is an array of strings
            // head
            // if (yAxisBodyTemp > 270) {
            //     yAxisBodyTemp = yAxisBody + 2 * lineHeight;
            //     doc.addPage();
            //     head(data?.rev_no);
            // }
            checkPageHeightAndAddPageForStringSection();

            doc.setFont(undefined, "bold");
            let textLinesTitleHead = doc
              .setFont("helvetica")
              .setFontSize(fontSizeHeader)
              .splitTextToSize("TEST PROCEDURE", maxLineWidth);

            let oneLineHeightHead = fontSizeHeader / ptsPerMm; //

            doc.text(
              textLinesTitleHead,
              margin,
              yAxisBodyTemp + 2 * oneLineHeightHead,
            ); //

            let textHeightHead =
              (textLinesTitleHead.length * fontSizeHeader * lineHeight) /
              ptsPerMm; //
            yAxisBodyTemp =
              yAxisBodyTemp + 2 * oneLineHeightHead + textHeightHead;
            // head close
            //body
            data?.procedure?.map((mData, index) => {
              // array of strings
              checkPageHeightAndAddPageForStringSection();
              doc.setFont(undefined, "normal");
              let textLinesTitle = doc
                .setFont("helvetica")
                .setFontSize(fontSizeParagraph)
                .splitTextToSize(mData, maxLineWidth - 5); // this extra five for indexing. just 2nd bottom line

              let oneLineHeight = fontSizeParagraph / ptsPerMm; //
              doc.text(
                index + 1 + ".",
                margin,
                yAxisBodyTemp + 1 * oneLineHeight,
              ); // this is for formatting multi-line procedure
              doc.text(
                textLinesTitle,
                margin + 5,
                yAxisBodyTemp + 1 * oneLineHeight,
              ); //

              let textHeight =
                (textLinesTitle.length * fontSizeParagraph * lineHeight) /
                ptsPerMm; //
              yAxisBodyTemp = yAxisBodyTemp + 1 * oneLineHeight + textHeight;
            });
            //body close
          }

          //// Non-conformity Action sheet (this is single page for now)
          var yAxisBodyActionSheetTemp = yAxisBody + 20;
          // small rectangle 1
          function smallRectangle(
            firstString,
            secondString,
            yAxisStartActionSheetParam,
          ) {
            doc.setFont("helvetica", "bold");
            doc.setFontSize(fonSizeActionSheet);
            doc.rect(
              margin,
              yAxisStartActionSheetParam,
              widthRecActionSheet / 2,
              heightForSmallARecActionSheet,
            );
            doc.rect(
              widthRecActionSheet / 2 + margin,
              yAxisStartActionSheetParam,
              widthRecActionSheet / 2,
              heightForSmallARecActionSheet,
            );

            doc.text(
              firstString,
              margin + 2,
              yAxisStartActionSheetParam +
                heightForSmallARecActionSheet / 2 +
                1,
            );
            doc.text(
              secondString,
              widthRecActionSheet / 2 + margin + 2,
              yAxisStartActionSheetParam +
                heightForSmallARecActionSheet / 2 +
                1,
            );
            yAxisBodyActionSheetTemp =
              yAxisStartActionSheetParam + heightForSmallARecActionSheet;
          }
          //

          // large rectangle 2
          function largeRectangle(firstString, yAxisStartActionSheetParam) {
            doc.setFont("helvetica", "bold");
            doc.setFontSize(fonSizeActionSheet);
            doc.rect(
              margin,
              yAxisStartActionSheetParam,
              widthRecActionSheet,
              heightForLargeRecActionSheet,
            );
            doc.text(firstString, margin + 2, yAxisStartActionSheetParam + 4); // 4 for text
            yAxisBodyActionSheetTemp =
              yAxisStartActionSheetParam + heightForLargeRecActionSheet;
          }
          // small rectangle with three col 3
          function smallRectangleThreeCol(
            firstString,
            secondString,
            thirdParamDate,
            yAxisStartActionSheetParam,
          ) {
            doc.setFont("helvetica", "bold");
            doc.setFontSize(fonSizeActionSheet);
            doc.rect(
              margin,
              yAxisStartActionSheetParam,
              widthRecActionSheet / 2,
              heightForSmallARecActionSheet,
            );
            doc.rect(
              widthRecActionSheet / 2 + margin,
              yAxisStartActionSheetParam,
              (2 * (widthRecActionSheet / 2)) / 3,
              heightForSmallARecActionSheet,
            ); // two third of half
            doc.rect(
              (2 * (widthRecActionSheet / 2)) / 3 +
                widthRecActionSheet / 2 +
                margin,
              yAxisStartActionSheetParam,
              (1 * (widthRecActionSheet / 2)) / 3,
              heightForSmallARecActionSheet,
            );

            doc.text(
              firstString,
              margin + 2,
              yAxisStartActionSheetParam +
                heightForSmallARecActionSheet / 2 +
                1,
            );
            doc.text(
              secondString,
              widthRecActionSheet / 2 + margin + 2,
              yAxisStartActionSheetParam +
                heightForSmallARecActionSheet / 2 +
                1,
            ); //// two third of half
            doc.text(
              thirdParamDate,
              widthRecActionSheet / 2 +
                margin +
                (2 * (widthRecActionSheet / 2)) / 3 +
                2,
              yAxisStartActionSheetParam +
                heightForSmallARecActionSheet / 2 +
                1,
            );
            yAxisBodyActionSheetTemp =
              yAxisStartActionSheetParam + heightForSmallARecActionSheet;
          }
          //

          if (data?.type === 5) {
            smallRectangle(
              "Deviation No: " + "",
              "Document Ref: " + "",
              yAxisBodyActionSheetTemp,
            );
            largeRectangle(
              "Deviation description: " + "",
              yAxisBodyActionSheetTemp,
            );
            largeRectangle(
              "Impact of deviation: " + "Impact deviation",
              yAxisBodyActionSheetTemp,
            );
            smallRectangleThreeCol(
              "Tester: " + "",
              "signature: " + "",
              "Date: " + "10-10-xxx2",
              yAxisBodyActionSheetTemp,
            );
            largeRectangle(
              "Corrective action: " + "",
              yAxisBodyActionSheetTemp,
            );
            smallRectangleThreeCol(
              "Engineering Approval: " + "",
              "signature: " + "",
              "Date: " + "10-10-xxx2",
              yAxisBodyActionSheetTemp,
            );
            largeRectangle(
              "Corrective Action Carried Out and Complete: " + "",
              yAxisBodyActionSheetTemp,
            );
            smallRectangleThreeCol(
              "Validation Approval: " + "",
              "signature: " + "",
              "Date: " + "10-10-xxx2",
              yAxisBodyActionSheetTemp,
            );
            smallRectangleThreeCol(
              "Customer Approval: " + "",
              "signature: " + "",
              "Date: " + "10-10-xxx2",
              yAxisBodyActionSheetTemp,
            );
          }

          ///// action sheet close

          ///////// tables ////////

          let lastTableHeight = yAxisBodyTemp;

          const checkPageHeightAndAddPage = () => {
            if (lastTableHeight + 6 > 235 * heightLandScapeCof) {
              // 10% variation in getting text size. so extra 6 is to adjust
              doc.addPage();
              head();
              lastTableHeight = yAxisBody + 5 * lineHeight;
            }
          };

          let t1Height = 0;
          let t2Height = 0;
          let t3Height = 0;
          let t4Height = 0;
          let t0Height = 0;

          //PQ starts

          // pq1 is execution table
          if (
            [...filterTableArrayBasedOnTitleForPq1(pq1)]?.filter(
              (fData) => fData?.fat_report_id === data?._id,
            )?.length
          ) {
            Object.values(
              Object?.groupBy(
                [...filterTableArrayBasedOnTitleForPq1(pq1)]?.filter(
                  (fData) => fData?.fat_report_id === data?._id,
                ),
                ({ table_title }) => table_title,
              ),
            )?.map((tData, idx) => {
              checkPageHeightAndAddPage();
              let yAxis =
                lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
              doc.setFont(undefined, "bold");
              doc.text(
                tData?.map(Object?.values)[0][
                  tData?.map(Object?.values)[0]?.length - 1
                ],
                margin,
                yAxis,
              ); // table_title

              autoTable(doc, {
                startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 12 : yAxisBodyTemp + 5, //yAxisBodyTemp , //50 + yAxisBodyTemp ,
                //startY: 50  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure, // first table need y
                //position precislly and then rest by didDraw hooks
                //startY: t3Height + 5,

                headStyles: {
                  fillColor: "#bbb",
                  textColor: "#000",
                  theme: "grid",
                  lineWidth: 0.1,
                  lineColor: "#000",
                },
                theme: "grid",
                head: [
                  [
                    {
                      content: "Sr. No",
                      styles: { halign: "center", valign: "middle" },
                    },
                    {
                      content: "Speed of the Machine",
                      styles: { halign: "center", valign: "middle" },
                    },
                    {
                      content: "Speed in RPM",
                      styles: { halign: "center", valign: "middle" },
                    },
                  ],
                ],

                body: tData?.map(Object?.values),
                //  table0?.filter((dataT0) => dataT0?.fatId === data.id)[0]
                //   ?.table0Data,
                bodyStyles: { lineColor: "#000" },
                showHead: "everyPage",
                margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                didDrawPage: function (data) {
                  lastTableHeight = data.cursor.y;
                  data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                  head();
                },
              });
            });
          }

          // pq2
          if (
            [...filterTableArrayBasedOnTitleForPq2(pq2)]?.filter(
              (fData) => fData?.fat_report_id === data?._id,
            )?.length
          ) {
            Object.values(
              Object?.groupBy(
                [...filterTableArrayBasedOnTitleForPq2(pq2)]?.filter(
                  (fData) => fData?.fat_report_id === data?._id,
                ),
                ({ table_title }) => table_title,
              ),
            )?.map((tData, idx) => {
              checkPageHeightAndAddPage();
              let yAxis =
                lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
              doc.setFont(undefined, "bold");
              doc.text(
                tData?.map(Object?.values)[0][
                  tData?.map(Object?.values)[0]?.length - 1
                ],
                margin,
                yAxis,
              ); // table_title

              autoTable(doc, {
                startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 12 : yAxisBodyTemp + 5, //yAxisBodyTemp , //50 + yAxisBodyTemp ,
                //startY: 50  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure, // first table need y
                //position precislly and then rest by didDraw hooks
                //startY: t3Height + 5,

                headStyles: {
                  fillColor: "#bbb",
                  textColor: "#000",
                  theme: "grid",
                  lineWidth: 0.1,
                  lineColor: "#000",
                },
                theme: "grid",
                head: [
                  [
                    {
                      content: "Parameter",
                      styles: { halign: "center", valign: "middle" },
                      rowSpan: 2,
                    },
                    {
                      content: "Acceptance criteria",
                      styles: { halign: "center", valign: "middle" },
                      rowSpan: 2,
                    },
                    {
                      content: "Sample interval time",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 4,
                    },
                  ],
                  [
                    {
                      content: "Initial",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 1,
                    },
                    {
                      content: "10 min",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 1,
                    },
                    {
                      content: "20 min",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 1,
                    },
                    {
                      content: "30 min",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 1,
                    },
                  ],
                ],

                body: tData?.map(Object?.values),
                // this is for footer static data
                bodyStyles: { lineColor: "#000" },
                showHead: "everyPage",
                margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                didDrawPage: function (data) {
                  lastTableHeight = data.cursor.y;
                  data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                  head();
                },

                // this is specialy for footer.  it is for css only
                didParseCell: function (data) {
                  var rows = data.table.body;
                  if (data.row.index > rows.length - 4) {
                    if (data.column.index === 1) {
                      data.cell.colSpan = 5;
                      data.cell.styles.halign = "center";
                    }
                  }
                },
              });
            });
          }

          // pq3
          if (
            [...filterTableArrayBasedOnTitleForPq3(pq3)]?.filter(
              (fData) => fData?.fat_report_id === data?._id,
            )?.length
          ) {
            Object.values(
              Object?.groupBy(
                [...filterTableArrayBasedOnTitleForPq3(pq3)]?.filter(
                  (fData) => fData?.fat_report_id === data?._id,
                ),
                ({ table_title }) => table_title,
              ),
            )?.map((tData, idx) => {
              checkPageHeightAndAddPage();
              let yAxis =
                lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
              doc.setFont(undefined, "bold");
              doc.text(
                tData?.map(Object?.values)[0][
                  tData?.map(Object?.values)[0]?.length - 1
                ],
                margin,
                yAxis,
              ); // table_title

              console.log("pq3F:", pq3_staticData);

              console.log(
                "pq3FF:",
                pq3_staticData?.filter(
                  (fData) =>
                    fData?.table_title ===
                    tData?.map(Object?.values)[0][
                      tData?.map(Object?.values)[0]?.length - 1
                    ],
                ),
              );

              autoTable(doc, {
                startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 12 : yAxisBodyTemp + 5, //yAxisBodyTemp , //50 + yAxisBodyTemp ,
                //startY: 50  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure, // first table need y
                //position precislly and then rest by didDraw hooks
                //startY: t3Height + 5,

                headStyles: {
                  fillColor: "#bbb",
                  textColor: "#000",
                  theme: "grid",
                  lineWidth: 0.1,
                  lineColor: "#000",
                },
                theme: "grid",
                head: [
                  [
                    {
                      content: "Process Parameter",
                      styles: { halign: "center", valign: "middle" },
                      rowSpan: 3,
                    },
                    {
                      content: "Specification",
                      styles: { halign: "center", valign: "middle" },
                      rowSpan: 3,
                    },
                    {
                      content: "Observation",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 3,
                    },
                  ],
                  [
                    {
                      // this content is special data from tableStaticReportData
                      content: `Validation Batch No.: ${
                        pq3_staticData?.filter(
                          (fData) =>
                            fData?.table_title ===
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ],
                        )[0]?.validation_batch_no ?? "NA"
                      }`,
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 3,
                    },
                  ],
                  [
                    {
                      content: "Slow speed (10RPM)",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 1,
                    },
                    {
                      content: "Optimum (20RPM)",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 1,
                    },
                    {
                      content: "High speed (40RPM)",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 1,
                    },
                  ],
                ],

                body: tData?.map(Object?.values),
                //  table0?.filter((dataT0) => dataT0?.fatId === data.id)[0]
                //   ?.table0Data,
                bodyStyles: { lineColor: "#000" },
                showHead: "everyPage",
                margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                didDrawPage: function (data) {
                  lastTableHeight = data.cursor.y;
                  data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                  head();
                },
              });
            });
          }

          // pq4
          if (
            [...filterTableArrayBasedOnTitleForPq4(pq4)]?.filter(
              (fData) => fData?.fat_report_id === data?._id,
            )?.length
          ) {
            Object.values(
              Object?.groupBy(
                [...filterTableArrayBasedOnTitleForPq4(pq4)]?.filter(
                  (fData) => fData?.fat_report_id === data?._id,
                ),
                ({ table_title }) => table_title,
              ),
            )?.map((tData, idx) => {
              checkPageHeightAndAddPage();
              let yAxis =
                lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
              doc.setFont(undefined, "bold");
              doc.text(
                tData?.map(Object?.values)[0][
                  tData?.map(Object?.values)[0]?.length - 1
                ],
                margin,
                yAxis,
              ); // table_title

              autoTable(doc, {
                startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 12 : yAxisBodyTemp + 5, //yAxisBodyTemp , //50 + yAxisBodyTemp ,
                //startY: 50  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure, // first table need y
                //position precislly and then rest by didDraw hooks
                //startY: t3Height + 5,

                headStyles: {
                  fillColor: "#bbb",
                  textColor: "#000",
                  theme: "grid",
                  lineWidth: 0.1,
                  lineColor: "#000",
                },
                theme: "grid",
                head: [
                  [
                    {
                      content: "S No.",
                      styles: { halign: "center", valign: "middle" },
                      rowSpan: 2,
                    },
                    {
                      content: "Parameter",
                      styles: { halign: "center", valign: "middle" },
                      rowSpan: 2,
                    },
                    {
                      content: "Limits",
                      styles: { halign: "center", valign: "middle" },
                      rowSpan: 2,
                    },
                    {
                      content: `Observation ,B. No.: ${
                        pq4_staticData?.filter(
                          (fData) =>
                            fData?.table_title ===
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ],
                        )[0]?.observation_batch_no ?? "NA"
                      }`,
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 3,
                    },
                  ],
                  [
                    {
                      content: "At 40RPM",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 1,
                    },
                    {
                      content: "At 45RPM",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 1,
                    },
                    {
                      content: "At 50RPM",
                      styles: { halign: "center", valign: "middle" },
                      colSpan: 1,
                    },
                  ],
                ],

                body: tData?.map(Object?.values),
                //  table0?.filter((dataT0) => dataT0?.fatId === data.id)[0]
                //   ?.table0Data,
                bodyStyles: { lineColor: "#000" },
                showHead: "everyPage",
                margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                didDrawPage: function (data) {
                  lastTableHeight = data.cursor.y;
                  data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                  head();
                },
              });
            });
          }

          // pq5
          // console.log("pq5:", [...filterTableArrayBasedOnTitleForPq5(pq5)]?.filter((fData)=> fData?.fat_report_id === data?._id))
          if (
            //pq5?.length
            [...filterTableArrayBasedOnTitleForPq5(pq5)]?.filter(
              (fData) => fData?.fat_report_id === data?._id,
            )?.length
          ) {
            Object.values(
              Object?.groupBy(
                [...filterTableArrayBasedOnTitleForPq5(pq5)]?.filter(
                  (fData) => fData?.fat_report_id === data?._id,
                ),
                ({ table_title }) => table_title,
              ),
            )?.map((tData, idx) => {
              checkPageHeightAndAddPage();
              let yAxis =
                lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
              doc.setFont(undefined, "bold");
              doc.text(
                tData?.map(Object?.values)[0][
                  tData?.map(Object?.values)[0]?.length - 1
                ],
                margin,
                yAxis,
              ); // table_title

              autoTable(doc, {
                startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 12 : yAxisBodyTemp + 5, //yAxisBodyTemp , //50 + yAxisBodyTemp ,
                //startY: 50  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure, // first table need y
                //position precislly and then rest by didDraw hooks
                //startY: t3Height + 5,

                headStyles: {
                  fillColor: "#bbb",
                  textColor: "#000",
                  theme: "grid",
                  lineWidth: 0.1,
                  lineColor: "#000",
                },
                theme: "grid",
                head: [
                  [
                    {
                      content: "S No.",
                      styles: { halign: "center", valign: "middle" },
                    },
                    {
                      content: "Eq. Name",
                      styles: { halign: "center", valign: "middle" },
                    },
                    {
                      content: "Eq. ID",
                      styles: { halign: "center", valign: "middle" },
                    },
                    {
                      content: "Make/Model",
                      styles: { halign: "center", valign: "middle" },
                    },
                    {
                      content: "Test Parameter",
                      styles: { halign: "center", valign: "middle" },
                    },
                    {
                      content: "Design range",
                      styles: { halign: "center", valign: "middle" },
                    },
                    {
                      content: "Qualification range",
                      styles: { halign: "center", valign: "middle" },
                    },
                    {
                      content: "For Product Qualification range",
                      styles: { halign: "center", valign: "middle" },
                    },
                    {
                      content: "Tolerance",
                      styles: { halign: "center", valign: "middle" },
                    },
                  ],
                ],

                body: tData?.map(Object?.values),
                //  table0?.filter((dataT0) => dataT0?.fatId === data.id)[0]
                //   ?.table0Data,
                bodyStyles: { lineColor: "#000" },
                showHead: "everyPage",
                margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                didDrawPage: function (data) {
                  lastTableHeight = data.cursor.y;
                  data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                  head();
                },
              });
            });
          }

          // // t0 is execution table
          // if (pq2
          //   // table0?.filter((dataT0) => dataT0?.fatId === data.id)[0]?.table0Data
          //   //   ?.length > 0
          // ) {
          //   (Object.values(Object?.groupBy([...filterTableArrayBasedOnTitleForPq2(pq2)], ({ table_title }) => table_title)))
          //     ?.map((tData, idx) => (

          //       autoTable(doc, {
          //         startY: lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5, //yAxisBodyTemp , //50 + yAxisBodyTemp ,
          //         //startY: 50  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure, // first table need y
          //         //position precislly and then rest by didDraw hooks
          //         //startY: t3Height + 5,
          //         headStyles: {
          //           fillColor: "#bbb",
          //           textColor: "#000",
          //           theme: "grid",
          //           lineWidth: 0.1,
          //           lineColor: "#000",
          //         },
          //         theme: "grid",
          //         head: [
          //           [
          //             {
          //               content: "Check Point",
          //               styles: { halign: "center", valign: "middle" },
          //             },
          //             {
          //               content: "Observation",
          //               styles: { halign: "center", valign: "middle" },
          //             },
          //             {
          //               content: "Acceptance Criteria",
          //               styles: { halign: "center", valign: "middle" },
          //             },
          //             {
          //               content: "Confirm (YES/NO)",
          //               styles: { halign: "center", valign: "middle" },
          //             },
          //             {
          //               content: "Deviation",
          //               styles: { halign: "center", valign: "middle" },
          //             },
          //           ],
          //         ],

          //         body: tData?.map(Object?.values),
          //         //  table0?.filter((dataT0) => dataT0?.fatId === data.id)[0]
          //         //   ?.table0Data,
          //         bodyStyles: { lineColor: "#000" },
          //         showHead: "everyPage",
          //         margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
          //         didDrawPage: function (data) {
          //           lastTableHeight = data.cursor.y;
          //           data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
          //           head();
          //         },
          //       })

          //     ))
          // }

          if (
            table1?.filter((dataT1) => dataT1?.fatId === data.id)[0]?.table1Data
              ?.length > 0
          ) {
            //var tableHeights = 0;
            table1
              ?.filter((dataT1) => dataT1?.fatId === data.id)[0]
              ?.table1Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 3,
                        styles: { halign: "center" },
                      },
                      {
                        content: "details",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "size",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "document",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      {
                        content: "Tag No",
                        colSpan: 1,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Description",
                        colSpan: 1,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make/Vendor",
                        colSpan: 1,
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],

                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // t2
          if (
            table2?.filter((dataT2) => dataT2?.fatId === data.id)[0]?.table2Data
              ?.length > 0
          ) {
            //var tableHeights = 0;
            table2
              ?.filter((dataT2) => dataT2?.fatId === data.id)[0]
              ?.table2Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 3,
                        styles: { halign: "center" },
                      },
                      {
                        content: "document",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      {
                        content: "Tag No /drawing No",
                        colSpan: 1,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Description",
                        colSpan: 1,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Type/Size",
                        colSpan: 1,
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],

                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // t3
          if (
            table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data
              ?.length > 0
          ) {
            table3
              ?.filter((dataT3) => dataT3?.fatId === data.id)[0]
              ?.table3Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Drawing Title",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Drawing No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Compliance (YES/NO)",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],

                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }
          //t4

          if (
            table4?.filter((dataT4) => dataT4?.fatId === data.id)[0]?.table4Data
              ?.length > 0
          ) {
            table4
              ?.filter((dataT4) => dataT4?.fatId === data.id)[0]
              ?.table4Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Tag No",
                        colSpan: 1,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Description",
                        colSpan: 1,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make/Vendor",
                        colSpan: 1,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Details",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Document (Yes/No)",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Sr No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Document Type",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Result",
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],

                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq1
          if (
            iq1?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq1Data
              ?.length > 0
          ) {
            iq1
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq1Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 6,
                        styles: { halign: "center" },
                      },
                    ],
                    [
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make/Vendor",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Type",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Size",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "document",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "result",
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],

                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq2
          if (
            iq2?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq2Data
              ?.length > 0
          ) {
            iq2
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq2Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 5,
                        styles: { halign: "center" },
                      },
                      {
                        content: "document",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      {
                        content: "Tag No/Drawing No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make/Vendor",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Type",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Size",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],

                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq3
          if (
            iq3?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq3Data
              ?.length > 0
          ) {
            iq3
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq3Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 6,
                        styles: { halign: "center" },
                      },
                      {
                        content: "document",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      {
                        content: "Tag No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make/Vendor",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Model/Serial no",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Type",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Size",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],

                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq4
          if (
            iq4?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq4Data
              ?.length > 0
          ) {
            iq4
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq4Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 6,
                        styles: { halign: "center" },
                      },
                      {
                        content: "document",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      {
                        content: "Tag No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make/Vendor",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Model/Serial no",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Type",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Rating",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  //  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.

                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq5
          if (
            iq5?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq5Data
              ?.length > 0
          ) {
            iq5
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq5Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 5,
                        styles: { halign: "center" },
                      },
                      {
                        content: "document",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      //   { content: "Tag No", styles: { halign: 'center', valign: 'middle' } },
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make/Vendor",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Model/Serial no",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Type",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Rating",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq6
          if (
            iq6?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq6Data
              ?.length > 0
          ) {
            iq6
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq6Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 5,
                        styles: { halign: "center" },
                      },
                      //   { content: "document", rowSpan: 2, styles: { halign: 'center', valign: 'middle' } },
                      {
                        content: "result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      {
                        content: "Tag No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make/Vendor",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Model/Serial no",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Calibration Certificate Number",
                        styles: { halign: "center", valign: "middle" },
                      },
                      //  { content: "Rating", styles: { halign: 'center', valign: 'middle' } },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq7
          if (
            iq7?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq7Data
              ?.length > 0
          ) {
            iq7
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq7Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 4,
                        styles: { halign: "center" },
                      },
                      {
                        content: "Calibration Certificate Number",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      //   { content: "Tag No", styles: { halign: 'center', valign: 'middle' } },
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Module Type",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Position/Channel no",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Application",
                        styles: { halign: "center", valign: "middle" },
                      },
                      //  { content: "Rating", styles: { halign: 'center', valign: 'middle' } },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq8
          if (
            iq8?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq8Data
              ?.length > 0
          ) {
            iq8
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq8Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      //   { content: 'Specifications', colSpan: 4, styles: { halign: 'center' } },
                      {
                        content: "Tag No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make/Vendor",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Module/Serial no",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Test Report no",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Result",
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq9
          if (
            iq9?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq9Data
              ?.length > 0
          ) {
            iq9
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq9Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Tag No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 4,
                        styles: { halign: "center" },
                      },
                      {
                        content: "Calibration Certificate Number",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      //   { content: "Tag No", styles: { halign: 'center', valign: 'middle' } },
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make Vendor",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Model/Serial no",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Type",
                        styles: { halign: "center", valign: "middle" },
                      },
                      //  { content: "Rating", styles: { halign: 'center', valign: 'middle' } },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq10
          if (
            iq10?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq10Data
              ?.length > 0
          ) {
            iq10
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq10Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Name of Software",
                        styles: { halign: "center", valign: "middle" },
                      },
                      //   { content: 'Specifications', colSpan: 4, styles: { halign: 'center' } },
                      {
                        content: "Make",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Version",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Aplication",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Remark",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Result",
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq11
          if (
            iq11?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq11Data
              ?.length > 0
          ) {
            iq11
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq11Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Tag No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 4,
                        styles: { halign: "center" },
                      },
                      {
                        content: "Documents",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      //   { content: "Tag No", styles: { halign: 'center', valign: 'middle' } },
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Model/Serial no",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Range",
                        styles: { halign: "center", valign: "middle" },
                      },
                      //  { content: "Rating", styles: { halign: 'center', valign: 'middle' } },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq12
          if (
            iq12?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq12Data
              ?.length > 0
          ) {
            iq12
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq12Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      //   { content: "Tag No", styles: { halign: 'center', valign: 'middle' } },
                      {
                        content: "Drawing Title",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Drawing No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Compliance (Yes/No)",
                        styles: { halign: "center", valign: "middle" },
                      },
                      //{ content: "Range", styles: { halign: 'center', valign: 'middle' } },
                      //  { content: "Rating", styles: { halign: 'center', valign: 'middle' } },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq13
          if (
            iq13?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq13Data
              ?.length > 0
          ) {
            iq13
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq13Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 6,
                        styles: { halign: "center" },
                      },
                      {
                        content: "Documents",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      {
                        content: "Tag No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make/Vendor",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Model/Serial no",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Type",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Size",
                        styles: { halign: "center", valign: "middle" },
                      },
                      //  { content: "Rating", styles: { halign: 'center', valign: 'middle' } },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Iq14
          if (
            iq14?.filter((tData) => tData?.fatId === data.id)[0]?.tableIq14Data
              ?.length > 0
          ) {
            iq14
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableIq14Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 4,
                        styles: { halign: "center" },
                      },
                      {
                        content: "Documents",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      // { content: "Tag No", styles: { halign: 'center', valign: 'middle' } },
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Make/Vendor",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Model/Serial no",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Type",
                        styles: { halign: "center", valign: "middle" },
                      },
                      // { content: "Size", styles: { halign: 'center', valign: 'middle' } },
                      //  { content: "Rating", styles: { halign: 'center', valign: 'middle' } },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // MFG1
          if (
            mfg1?.filter((tData) => tData?.fatId === data.id)[0]?.tableMfg1Data
              ?.length > 0
          ) {
            mfg1
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableMfg1Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Report/Document number",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Yes/No",
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Eqp1
          if (
            eqp1?.filter((tData) => tData?.fatId === data.id)[0]?.tableEqp1Data
              ?.length > 0
          ) {
            eqp1
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableEqp1Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Protocal Number",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Compliance (Compliance OR Not)",
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Eqp2
          if (
            eqp2?.filter((tData) => tData?.fatId === data.id)[0]?.tableEqp2Data
              ?.length > 0
          ) {
            eqp2
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableEqp2Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Page Number",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Compliance (Compliance OR Not)",
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Eqp3
          if (
            eqp3?.filter((tData) => tData?.fatId === data.id)[0]?.tableEqp3Data
              ?.length > 0
          ) {
            eqp3
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableEqp3Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Document",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Test Report Number",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Compliance (Compliance OR Not)",
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Eqp4
          if (
            eqp4?.filter((tData) => tData?.fatId === data.id)[0]?.tableEqp4Data
              ?.length > 0
          ) {
            eqp4
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableEqp4Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Document Qualification Name",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Page Number",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Nature of Deviation",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Impact of Deviation",
                        colSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center" },
                      },
                      {
                        content: "Action Taken",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      //   { content: "Tag No", styles: { halign: 'center', valign: 'middle' } },
                      {
                        content: "MAJOR",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "MINOR",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Eqp5
          if (
            eqp5?.filter((tData) => tData?.fatId === data.id)[0]?.tableEqp5Data
              ?.length > 0
          ) {
            eqp5
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableEqp5Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Test Result",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Pass Fail",
                        colSpan: 3,
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      //   { content: "Tag No", styles: { halign: 'center', valign: 'middle' } },
                      {
                        content: "Company Name",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Name",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Signature",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Date",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Oq1
          if (
            oq1?.filter((tData) => tData?.fatId === data.id)[0]?.tableOq1Data
              ?.length > 0
          ) {
            oq1
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableOq1Data?.map((mData, index) => {
                //  //
                //  if (lastTableHeight > 260) {
                //     lastTableHeight = yAxisBody + 4 * lineHeight; // table has heading so 4
                //     doc.addPage();
                //     head(data?.rev_no);
                // }
                // //
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Serial No",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Description",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Module Location",
                        colSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Signal on/off",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Result",
                        rowSpan: 2,
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      //   { content: "Tag No", styles: { halign: 'center', valign: 'middle' } },
                      {
                        content: "MOD No.",
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "CH No.",
                        lineWidth: 0.25,
                        lineColor: "#000",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Oq2
          if (
            oq2?.filter((tData) => tData?.fatId === data.id)[0]?.tableOq2Data
              ?.length > 0
          ) {
            oq2
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableOq2Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Specifications",
                        colSpan: 2,
                        styles: { halign: "center" },
                      },
                      // { content: "document", rowSpan: 2, styles: { halign: 'center', valign: 'middle' } },
                      // { content: "result", rowSpan: 2, lineWidth: 0.25, lineColor: "#000", styles: { halign: 'center', valign: 'middle' } },
                    ],
                    [
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Performance Parameter",
                        styles: { halign: "center", valign: "middle" },
                      },
                      // { content: "Type/Size", colSpan: 1, styles: { halign: 'center', valign: 'middle' } }
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Oq3
          if (
            oq3?.filter((tData) => tData?.fatId === data.id)[0]?.tableOq3Data
              ?.length > 0
          ) {
            oq3
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableOq3Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "S.No",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Requirements",
                        colSpan: 3,
                        styles: { halign: "center" },
                      },
                      // { content: "document", rowSpan: 2, styles: { halign: 'center', valign: 'middle' } },
                      // { content: "result", rowSpan: 2, lineWidth: 0.25, lineColor: "#000", styles: { halign: 'center', valign: 'middle' } },
                    ],
                    [
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Required",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Actuals",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Oq4
          if (
            oq4?.filter((tData) => tData?.fatId === data.id)[0]?.tableOq4Data
              ?.length > 0
          ) {
            oq4
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableOq4Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "General Condition",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Actual Condition",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Oq5
          if (
            oq5?.filter((tData) => tData?.fatId === data.id)[0]?.tableOq5Data
              ?.length > 0
          ) {
            oq5
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableOq5Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Description",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Acceptance Criteria",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Observation",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Pass/Fail",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.

                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Oq6
          if (
            oq6?.filter((tData) => tData?.fatId === data.id)[0]?.tableOq6Data
              ?.length > 0
          ) {
            oq6
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableOq6Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Check-Point",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Acceptance Criteria",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Observation",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Pass/Fail",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Oq7
          if (
            oq7?.filter((tData) => tData?.fatId === data.id)[0]?.tableOq7Data
              ?.length > 0
          ) {
            oq7
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableOq7Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Check-Point",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Acceptance Criteria",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Actuals",
                        colSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Avg",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Pass/Fail",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      {
                        content: "Min",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Max",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Oq8
          if (
            oq8?.filter((tData) => tData?.fatId === data.id)[0]?.tableOq8Data
              ?.length > 0
          ) {
            oq8
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableOq8Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Temprature Set Pont (*c)",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Acceptance Criteria",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Calculated Average after 30 minutes",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "variation",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Ok/Not ok",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Oq9
          if (
            oq9?.filter((tData) => tData?.fatId === data.id)[0]?.tableOq9Data
              ?.length > 0
          ) {
            oq9
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableOq9Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Shelf SP",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Acceptance Criteria",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Observation",
                        colSpan: 3,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Deviation",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Pass/Fail",
                        rowSpan: 2,
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                    [
                      {
                        content: "Shelf No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Shelf Average",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "All the shelves Average",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Oq10
          if (
            oq10?.filter((tData) => tData?.fatId === data.id)[0]?.tableOq10Data
              ?.length > 0
          ) {
            oq10
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableOq10Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Check Point",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Results",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Confirm Yes/No",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Deviation",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }

          // Oq11
          if (
            oq11?.filter((tData) => tData?.fatId === data.id)[0]?.tableOq11Data
              ?.length > 0
          ) {
            oq11
              ?.filter((tData) => tData?.fatId === data.id)[0]
              ?.tableOq11Data?.map((mData, index) => {
                checkPageHeightAndAddPage();
                let yAxis =
                  lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp + 5;
                doc.setFont(undefined, "bold");
                doc.text(mData[0][mData[0]?.length - 1], margin, yAxis); // table_title
                autoTable(doc, {
                  // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
                  startY: yAxis + 1, //lastTableHeight > 0 ? lastTableHeight + 5 + (index * 5) : lastTableHeight + 55  + gapFromObjective + gapFromMethod + gapFromPreRequest + gapFromProcedure,
                  headStyles: {
                    fillColor: "#bbb",
                    textColor: "#000",
                    theme: "grid",
                    lineWidth: 0.1,
                    lineColor: "#000",
                  },
                  theme: "grid",
                  head: [
                    [
                      {
                        content: "Recipe Information",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Range",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Set Value",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Acceptance Criteria",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Chamber Leak Rate",
                        styles: { halign: "center", valign: "middle" },
                      },
                      {
                        content: "Confirm Yes/No",
                        styles: { halign: "center", valign: "middle" },
                      },
                    ],
                  ],
                  body: mData, //table3?.filter((dataT3) => dataT3?.fatId === data.id)[0]?.table3Data,
                  bodyStyles: { lineColor: "#000" },
                  showHead: "everyPage",
                  margin: { left: margin, right: margin, bottom: 20 }, // length of page is 297.
                  didDrawPage: function (data) {
                    lastTableHeight = data.cursor.y;
                    data.settings.margin.top = 45; // passing head gap if new pages gets added for the table
                    head();
                  },
                });
              });
          }
          // Images (third last item )
          if ("urls" in data) {
            var softwareListUrlsYaxisStart =
              lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp;
            var softwareListUrlsXaxisStart = 1.5 * margin;
            var imageWidth = pageWidth - 2 * 1.5 * margin;
            var imageHeight = pageWidth / widthLandScapeCof - 3 * 1.5 * margin; //100

            // softwareListUrls?.map((mData, index) => {
            softwareListUrls
              ?.find((fData) => fData?.id === data?.id)
              ?.urlsArray?.map((mData) => {
                var imageOrginalWidth = mData?.img?.naturalWidth;
                var imageOrginalHeight = mData?.img?.naturalHeight;
                var imageWidthAdjustmentCof =
                  imageOrginalWidth / imageOrginalHeight
                    ? imageOrginalWidth / imageOrginalHeight
                    : 1;
                var imageHeightAdjustmentCof =
                  imageOrginalHeight / imageOrginalWidth
                    ? imageOrginalHeight / imageOrginalWidth
                    : 1;

                if (
                  softwareListUrlsYaxisStart + imageHeight >
                  274 * heightLandScapeCof
                ) {
                  // 5 is adjustment value // page break check
                  softwareListUrlsYaxisStart = yAxisBody + 10 * lineHeight;
                  doc.addPage();
                  head();
                }

                doc.addImage(
                  mData,
                  "JPEG",
                  softwareListUrlsXaxisStart,
                  softwareListUrlsYaxisStart,
                  imageWidth * imageWidthAdjustmentCof,
                  imageHeight * imageHeightAdjustmentCof,
                );

                softwareListUrlsYaxisStart =
                  softwareListUrlsYaxisStart + imageHeight + 2; ///

                if (index < softwareListUrls?.length - 1) {
                  /// updating the lastTableHeight for starting next section
                  lastTableHeight =
                    softwareListUrlsYaxisStart + imageHeight + 2;
                } else {
                  lastTableHeight = softwareListUrlsYaxisStart; //
                }
              });
          }

          // Images end

          // Test summary (second last item)
          if ("summary" in data) {
            var summaryYaxisStart =
              lastTableHeight > 0 ? lastTableHeight + 6 : yAxisBodyTemp;

            if (summaryYaxisStart + 5 > 274 * heightLandScapeCof) {
              // 5 is adjustment value
              summaryYaxisStart = yAxisBody + 10 * lineHeight;
              doc.addPage();
              head();
            }

            doc.setFontSize(fontSizeParagraph); //
            doc.setFont(undefined, "normal");
            doc.setDrawColor(0, 0, 0); // draw red lines

            doc.rect(margin, summaryYaxisStart, maxLineWidth, 12); // h=12 , startW=14, w=182
            doc.text("Document Complies", margin + 2, summaryYaxisStart + 8);

            doc.rect(25 + 70, summaryYaxisStart + 3, 6, 6);
            if (data?.summary) {
              // true
              doc.line(
                25 + 70 + 2,
                summaryYaxisStart + 3 + 5,
                25 + 70 + 1,
                summaryYaxisStart + 3 + 3,
              ); //tick
              doc.line(
                25 + 70 + 2,
                summaryYaxisStart + 3 + 5,
                25 + 70 + 2 + 3,
                summaryYaxisStart + 3 + 1,
              ); //tick
            }
            doc.text("PASS", 25 + 70 + 10, summaryYaxisStart + 8);

            doc.rect(25 + 70 + 10 + 30, summaryYaxisStart + 3, 6, 6);
            if (!data?.summary) {
              // false
              doc.line(
                25 + 70 + 10 + 30 + 2,
                summaryYaxisStart + 3 + 5,
                25 + 70 + 10 + 30 + 1,
                summaryYaxisStart + 3 + 3,
              ); //tick
              doc.line(
                25 + 70 + 10 + 30 + 2,
                summaryYaxisStart + 3 + 5,
                25 + 70 + 10 + 30 + 2 + 3,
                summaryYaxisStart + 3 + 1,
              ); //tick
            }
            doc.text("FAIL", 25 + 70 + 10 + 30 + 10, summaryYaxisStart + 8);
            //doc.text(data?.summary ? "true" : "false", 20, summaryYaxisStart);

            const summaryHeight = summaryYaxisStart + 12; // 12 is the height of main rectangle // uses will be in next section if any
          }

          // Comment (the last item)
        }
      });
    }

    ////post approval table start  // upto line 6853
    // doc.addPage();
    // head();
    // if (postApprovalTable?.length > 0) {
    //   // console.log("aproval Data with type;", approvalTable)
    //   var customertableHeight = 0; // height of cutomer table
    //   doc.setFontSize(fontSizeHeader);
    //   doc.setFont("helvetica", "bold");
    //   doc.text("POST-APPROVAL SIGNATURES", margin, 50); // 10, 44
    //   ////Vendor
    //   doc.text(
    //     "Granules India Limited.",
    //     (pageWidth + 15 - 2 * margin) / 2,
    //     50 + 10
    //   ); // 15 for adjutment
    //   //doc.line(10, 45, 95, 45); // (x,y,x,y)
    //   autoTable(doc, {
    //     // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
    //     startY: 50 + 15,
    //     headStyles: {
    //       fillColor: "#bbb",
    //       textColor: "#000",
    //       theme: "grid",
    //       lineWidth: 0.1,
    //       lineColor: "#000",
    //     },
    //     theme: "grid",
    //     head: [
    //       [
    //         { content: "Name", styles: { halign: "center", valign: "middle" } },
    //         {
    //           content: "Email",
    //           styles: { halign: "center", valign: "middle" },
    //         },
    //         {
    //           content: "Date",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //         {
    //           content: "Approval",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //       ],
    //     ],

    //     body: postApprovalTable?.filter(
    //       (fData) => fData[fData?.length - 1] === "vendor"
    //     ),
    //     bodyStyles: {
    //       minCellHeight: 10,
    //       minCellWidth: 40,
    //       valign: "middle",
    //       halign: "center",
    //       lineColor: "#000",
    //     },
    //     didDrawPage: (d) => {
    //       customertableHeight = d.cursor.y;
    //     },
    //     margin: { left: margin, right: margin },
    //   });

    //   /// Customer
    //   doc.text(
    //     reportInfo?.customer_name,
    //     (pageWidth + 15 - 2 * margin) / 2,
    //     customertableHeight + 10
    //   ); // 15 for adjutment
    //   autoTable(doc, {
    //     startY: customertableHeight + 15,
    //     headStyles: {
    //       fillColor: "#bbb",
    //       textColor: "#000",
    //       theme: "grid",
    //       lineWidth: 0.1,
    //       lineColor: "#000",
    //     },
    //     theme: "grid",
    //     head: [
    //       [
    //         { content: "Name", styles: { halign: "center", valign: "middle" } },
    //         {
    //           content: "Email",
    //           styles: { halign: "center", valign: "middle" },
    //         },
    //         {
    //           content: "Date",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //         {
    //           content: "Approval",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //       ],
    //     ],

    //     body: postApprovalTable?.filter(
    //       (fData) => fData[fData?.length - 1] === "customer"
    //     ),
    //     bodyStyles: {
    //       minCellHeight: 10,
    //       minCellWidth: 40,
    //       valign: "middle",
    //       halign: "center",
    //       lineColor: "#000",
    //     },
    //     margin: { left: margin, right: margin },
    //     // didDrawPage: (d) => {customertableHeight = (d.cursor.y)},
    //   });
    // } else {
    //   var customertableHeightElse = 0; // height of cutomer table
    //   doc.setFontSize(fontSizeHeader);
    //   doc.setFont("helvetica", "bold");
    //   doc.text("POST-APPROVAL SIGNATURES", margin, 50);
    //   ////Vendor
    //   doc.text(
    //     "Granules India Limited",
    //     105,
    //     50 + 10,
    //     null,
    //     null,
    //     "center"
    //   ); // 105 is for adjutment and static
    //   //doc.line(10, 45, 95, 45); // (x,y,x,y)
    //   autoTable(doc, {
    //     // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
    //     startY: 50 + 15,
    //     headStyles: {
    //       fillColor: "#bbb",
    //       textColor: "#000",
    //       theme: "grid",
    //       lineWidth: 0.1,
    //       lineColor: "#000",
    //     },
    //     theme: "grid",
    //     head: [
    //       [
    //         { content: "Name", styles: { halign: "center", valign: "middle" } },
    //         {
    //           content: "Email",
    //           styles: { halign: "center", valign: "middle" },
    //         },
    //         {
    //           content: "Date",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //         {
    //           content: "Approval",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //       ],
    //     ],

    //     body: [[], [], []],
    //     bodyStyles: {
    //       minCellHeight: 10,
    //       minCellWidth: 40,
    //       valign: "middle",
    //       halign: "center",
    //       lineColor: "#000",
    //     },
    //     didDrawPage: (d) => {
    //       customertableHeightElse = d.cursor.y;
    //     },
    //     margin: { left: margin, right: margin },
    //   });

    //   /// Customer
    //   doc.text(
    //     reportInfo?.customer_name ?? "NA",
    //     105,
    //     customertableHeightElse + 10,
    //     null,
    //     null,
    //     "center"
    //   ); // 15 for adjutment
    //   autoTable(doc, {
    //     startY: customertableHeightElse + 15,
    //     headStyles: {
    //       fillColor: "#bbb",
    //       textColor: "#000",
    //       theme: "grid",
    //       lineWidth: 0.1,
    //       lineColor: "#000",
    //     },
    //     theme: "grid",
    //     head: [
    //       [
    //         { content: "Name", styles: { halign: "center", valign: "middle" } },
    //         {
    //           content: "Email",
    //           styles: { halign: "center", valign: "middle" },
    //         },
    //         {
    //           content: "Date",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //         {
    //           content: "Approval",
    //           styles: { halign: "center", valign: "middle", minCellWidth: 20 },
    //         },
    //       ],
    //     ],

    //     body: [[], [], []],
    //     bodyStyles: {
    //       minCellHeight: 10,
    //       minCellWidth: 40,
    //       valign: "middle",
    //       halign: "center",
    //       lineColor: "#000",
    //     },
    //     margin: { left: margin, right: margin },
    //     // didDrawPage: (d) => {customertableHeight = (d.cursor.y)},
    //   });
    // }

    //// post aproval table ends

    // doc.addPage();
    // head();

    if (
      //
      [...filterTableArrayBasedOnTitleForPostApprovalTable(postApprovalTable)]
        ?.length
    ) {
      Object.values(
        Object?.groupBy(
          [
            ...filterTableArrayBasedOnTitleForPostApprovalTable(
              postApprovalTable,
            ),
          ],
          ({ table_title }) => table_title,
        ),
      )?.map((tData, idx) => {
        // if (postApprovalTable?.length > 0) {
        // console.log("aproval Data with type;", approvalTable)
        var customertableHeight = 0; // height of cutomer table
        doc.setFontSize(fontSizeHeader);
        doc.setFont("helvetica", "bold");
        // doc.text("POST-APPppROVAL SIGNATURES", margin, 50); // 10, 44 // this is overiding the title
        ////Vendor
        doc.text(
          "Arizon Systems Pvt. Ltd.",
          (pageWidth + 15 - 2 * margin) / 2.5,
          50 + 10,
        ); // 15 for adjutment
        //doc.line(10, 45, 95, 45); // (x,y,x,y)
        autoTable(doc, {
          // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
          startY: 50 + 15,
          headStyles: {
            fillColor: "#bbb",
            textColor: "#000",
            theme: "grid",
            lineWidth: 0.1,
            lineColor: "#000",
          },
          theme: "grid",
          head: [
            [
              {
                content: "Prepared By",
                styles: { halign: "center", valign: "middle" },
              },
              {
                content: "Reviewed By",
                styles: { halign: "center", valign: "middle" },
                colSpan: 4,
              },
              {
                content: "Approved By",
                styles: { halign: "center", valign: "middle" },
              },
            ],
            [
              {
                content: "Technology Transfer",
                styles: { halign: "center", valign: "middle" },
              },
              {
                content: "Technology Transfer",
                styles: { halign: "center", valign: "middle" },
              },
              {
                content: "Production",
                styles: { halign: "center", valign: "middle" },
              },
              {
                content: "Engineering",
                styles: { halign: "center", valign: "middle" },
              },
              {
                content: "Quality Assurance",
                styles: { halign: "center", valign: "middle" },
              },
              {
                content: "Quality Assurance",
                styles: { halign: "center", valign: "middle" },
              },
            ],
          ],

          body: tData?.map(Object?.values),
          // ?.filter(
          //   (fData) => fData[fData?.length - 1] === "vendor"
          // )
          bodyStyles: {
            minCellHeight: 20,
            //minCellWidth: 40,
            valign: "middle",
            halign: "center",
            lineColor: "#000",
          },
          didDrawPage: (d) => {
            customertableHeight = d.cursor.y;
          },
          margin: { left: margin, right: margin },
        });
      });
    } else {
      var customertableHeightElse = 0; // height of cutomer table
      doc.setFontSize(fontSizeHeader);
      doc.setFont("helvetica", "bold");
      doc.text("POST-APPROVAL SIGNATURES", margin, 50);
      ////Vendor
      doc.text("Arizon Systems Pvt. Ltd.", 105, 50 + 10, null, null, "center"); // 105 is for adjutment and static
      //doc.line(10, 45, 95, 45); // (x,y,x,y)
      autoTable(doc, {
        // startY: 50 + 12 * 8 + 7 * (objectiveSplits - 1) + 7 * (methodSplits - 1) + 7 * (preRequestSplits - 1) + 7 * (procedureSplits - 1),
        startY: 50 + 15,
        headStyles: {
          fillColor: "#bbb",
          textColor: "#000",
          theme: "grid",
          lineWidth: 0.1,
          lineColor: "#000",
        },
        theme: "grid",
        head: [
          [
            {
              content: "Prepared By",
              styles: { halign: "center", valign: "middle" },
            },
            {
              content: "Reviewed By",
              styles: { halign: "center", valign: "middle" },
              colSpan: 4,
            },
            {
              content: "Approved By",
              styles: { halign: "center", valign: "middle" },
            },
          ],
          [
            {
              content: "Technology Transfer",
              styles: { halign: "center", valign: "middle" },
            },
            {
              content: "Technology Transfer",
              styles: { halign: "center", valign: "middle" },
            },
            {
              content: "Production",
              styles: { halign: "center", valign: "middle" },
            },
            {
              content: "Engineering",
              styles: { halign: "center", valign: "middle" },
            },
            {
              content: "Quality Assurance",
              styles: { halign: "center", valign: "middle" },
            },
            {
              content: "Quality Assurance",
              styles: { halign: "center", valign: "middle" },
            },
          ],
        ],

        body: [[]],
        bodyStyles: {
          minCellHeight: 20,
          //minCellWidth: 40,
          valign: "middle",
          halign: "center",
          lineColor: "#000",
        },
        didDrawPage: (d) => {
          customertableHeightElse = d.cursor.y;
        },
        margin: { left: margin, right: margin },
      });
    }

    addFooters(doc);
    //doc.save(reportInfo?.protocol_no)
    doc.save(reportInfo?.protocol_no + ".pdf", { returnPromise: true });
  };

  return (
    <div className="flex flex-col justify-center">
      {/* { fatProcessedLoading ? "true" : "false"} */}
      {/* <button onClick={() => savePdf()}>save</button> */}

      <button
        onClick={() => setLandScape(!landScape)}
        className={
          currentMode === "Dark"
            ? "active:animate-pulse  active:text-md active:font-bold px-2 my-2 bg-slate-700 rounded-sm hover:text-green-400 hover:shadow-md shadow-white"
            : "active:animate-pulse  active:text-md active:font-bold px-2 my-2 bg-slate-200 rounded-sm hover:text-green-700 hover:shadow-md"
        }
      >
        Orientation: {landScape ? "Landscape" : "Portrait"}
      </button>

      <button
        onClick={() => handleSavePdf()}
        title="With new tabels and optimsed formating"
        className={
          currentMode === "Dark"
            ? "active:after:content-['Preparing'] active:animate-pulse  active:text-green-500 active:text-lg active:font-bold px-2 bg-slate-700 rounded-sm hover:text-green-400 hover:shadow-md shadow-white"
            : "active:after:content-['Preparing'] active:animate-pulse  active:text-green-800 active:text-lg active:font-bold px-2 bg-slate-200 rounded-sm hover:text-green-700 hover:shadow-md"
        }
      >
        Download
        <br />
      </button>
    </div>
  );
}
