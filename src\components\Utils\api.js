import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";

export const endpoints = {
  line: `${dbConfig.url}/lines`,
  shift: `${dbConfig.url}/shifts`,
  product_name: `${dbConfig.url}/products`,
  machine: `${dbConfig.url}/machines`,
  reasons: `${dbConfig.url}/losstreereasons`,
};

export const fetchData = async (type) => {
  if (!endpoints[type]) {
    throw new Error(`Invalid type: ${type}`);
  }

  try {
    console.log("Fetching from:", endpoints[type]);
    const response = await axios.get(endpoints[type], {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data.data || [];
  } catch (error) {
    console.error("Fetch Error:", error);
    throw new Error(
      `Failed to fetch ${type}: ${
        error.response?.data?.message || error.message
      }`,
    );
  }
};

export const saveData = async (type, data, isEdit = false) => {
  if (!endpoints[type]) {
    throw new Error(`Invalid type: ${type}`);
  }

  if (isEdit && (!data._id || data._id === "undefined")) {
    throw new Error("Valid ID is required for update operations");
  }

  try {
    const requestData = { ...data };
    console.log(`Before processing ${type} data:`, JSON.stringify(requestData));

    switch (type) {
      case "line":
        requestData.title = String(requestData.title || "").trim();
        requestData.description = String(requestData.description || "").trim();
        requestData.location = String(requestData.location || "").trim();
        break;
      case "product_name":
        requestData.title = String(requestData.title || "").trim();
        requestData.description = String(requestData.description || "").trim();
        requestData.pack_style = Number(requestData.pack_style || 0);
        requestData.running_rpm = Number(requestData.running_rpm || 0);
        requestData.optimal_speed = Number(requestData.optimal_speed || 0);
        requestData.qty_per_shipper = Number(requestData.qty_per_shipper || 0);
        requestData.quality = Number(requestData.quality || 0);
        requestData.mid = String(requestData.mid || "").trim();
        break;
      case "shift":
        requestData.title = String(requestData.title || "").trim();
        requestData.total_shift_time = Number(
          requestData.total_shift_time || 0,
        );
        requestData.instance_time = Number(requestData.instance_time || 0);
        requestData.description = String(requestData.description || "").trim();
        requestData.start_time = Number(data.start_time || 0);
        requestData.end_time = Number(data.end_time || 0);
        break;
    }

    console.log(`After processing ${type} data:`, JSON.stringify(requestData));

    const id = requestData._id;
    delete requestData._id;

    const response = isEdit
      ? await axios.put(`${endpoints[type]}/${id}`, requestData)
      : await axios.post(endpoints[type], requestData);

    if (!response.data || !response.data.data) {
      throw new Error("Invalid response format from server");
    }

    return response.data.data;
  } catch (error) {
    console.error("Save Error:", error);
    throw new Error(
      `Failed to ${isEdit ? "update" : "create"} ${type}: ${
        error.response?.data?.message || error.message
      }`,
    );
  }
};

export const deleteData = async (type, id) => {
  if (!endpoints[type]) {
    throw new Error(`Invalid type: ${type}`);
  }

  if (!id) {
    throw new Error("ID is required for deletion");
  }

  try {
    await axios.delete(`${endpoints[type]}/${id}`, {
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Delete Error:", error);
    throw new Error(
      `Failed to delete ${type}: ${
        error.response?.data?.message || error.message
      }`,
    );
  }
};

export const updateData = async (type, id, data) => {
  if (!endpoints[type]) {
    throw new Error(`Invalid type: ${type}`);
  }

  if (!id) {
    throw new Error("ID is required for update");
  }

  try {
    const response = await axios.put(`${endpoints[type]}/${id}`, data, {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data.data;
  } catch (error) {
    console.error("Update Error:", error);
    throw new Error(
      `Failed to update ${type}: ${
        error.response?.data?.message || error.message
      }`,
    );
  }
};

export const fetchById = async (type, id) => {
  if (!endpoints[type]) {
    throw new Error(`Invalid type: ${type}`);
  }

  if (!id) {
    throw new Error("ID is required");
  }

  try {
    const response = await axios.get(`${endpoints[type]}/${id}`, {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data.data;
  } catch (error) {
    console.error("Fetch By ID Error:", error);
    throw new Error(
      `Failed to fetch ${type}: ${
        error.response?.data?.message || error.message
      }`,
    );
  }
};
