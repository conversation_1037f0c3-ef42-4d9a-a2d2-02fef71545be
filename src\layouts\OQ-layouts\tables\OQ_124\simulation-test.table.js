import * as React from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import EditIcon from "@mui/icons-material/Edit";
import { Box, IconButton, Typography } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";

function createData(name, calories, fat, carbs, protein) {
  return { name, calories, fat, carbs, protein };
}

const rows = [
  createData("Frozen yoghurt", 159, 6.0, 24, 4.0),
  createData("Ice cream sandwich", 237, 9.0, 37, 4.3),
  createData("Eclair", 262, 16.0, 24, 6.0),
  createData("Cupcake", 305, 3.7, 67, 4.3),
  createData("Gingerbread", 356, 16.0, 49, 3.9),
];

export default function SimulationTestTable({ data }) {
  console.log(data);

  return (
    <Box sx={{ p: 4 }}>
      <Box sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}>
        <Typography variant="h6" align="left" gutterBottom>
          SNAP-OAC5
        </Typography>
        <Box>
          <IconButton sx={{ bgcolor: "#CFD2CF", mr: 2 }} size="small">
            {" "}
            <EditIcon />{" "}
          </IconButton>
          <IconButton sx={{ bgcolor: "#CFD2CF", mr: 2 }} size="small">
            {" "}
            <AddIcon />{" "}
          </IconButton>
        </Box>
      </Box>

      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow sx={{ bgcolor: "#CFD2CF" }}>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
              >
                Sr No.
              </TableCell>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
                align="center"
              >
                Description
              </TableCell>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
                colSpan={2}
                align="center"
              >
                Module Location
              </TableCell>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
                align="center"
              >
                Status
              </TableCell>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
                align="center"
              >
                Result
              </TableCell>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
                align="center"
              >
                Attachment & Actions
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
              ></TableCell>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
                align="center"
              ></TableCell>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
                align="center"
              >
                Mod No.
              </TableCell>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
                align="center"
              >
                CH No.
              </TableCell>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
                align="center"
              ></TableCell>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
                align="center"
              ></TableCell>
              <TableCell
                sx={{
                  border: "1px solid black",
                  borderRight: "none",
                  borderBottom: "none",
                }}
                align="center"
              ></TableCell>
            </TableRow>
          </TableHead>
          <TableBody sx={{ borderBottom: "1px solid black" }}>
            {data.map((row, idx) => (
              <TableRow
                key={row.id}
                //   sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
              >
                <TableCell
                  sx={{
                    border: "1px solid black",
                    borderRight: "none",
                    borderBottom: "none",
                  }}
                  scope="row"
                >
                  {idx + 1}
                </TableCell>
                <TableCell
                  sx={{
                    border: "1px solid black",
                    borderRight: "none",
                    borderBottom: "none",
                  }}
                  align="center"
                >
                  {row.desc}
                </TableCell>
                <TableCell
                  sx={{
                    border: "1px solid black",
                    borderRight: "none",
                    borderBottom: "none",
                  }}
                  align="center"
                >
                  {row.mod_number}
                </TableCell>
                <TableCell
                  sx={{
                    border: "1px solid black",
                    borderRight: "none",
                    borderBottom: "none",
                  }}
                  align="center"
                >
                  {row.ch_number}
                </TableCell>
                <TableCell
                  sx={{
                    border: "1px solid black",
                    borderRight: "none",
                    borderBottom: "none",
                  }}
                  align="center"
                >
                  {row.status}
                </TableCell>
                <TableCell
                  sx={{
                    border: "1px solid black",
                    borderRight: "none",
                    borderBottom: "none",
                  }}
                  align="center"
                >
                  {row.result}
                </TableCell>
                <TableCell
                  sx={{
                    border: "1px solid black",
                    borderRight: "none",
                    borderBottom: "none",
                  }}
                  align="center"
                ></TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}
