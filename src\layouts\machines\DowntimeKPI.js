import React, { useState } from "react";
import Chart from "chart.js/auto";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "react-chartjs-2";
import { ButtonGroup, Button, Grid, Typography } from "@mui/material";

const DownTimeKPI = ({ oeeData, type }) => {
  const [selectedTimeRange, setSelectedTimeRange] = useState("1hr");

  // Function to convert time range to minutes
  const getTimeInMinutes = (timeRange) => {
    switch (timeRange) {
      case "1hr":
        return 60;
      case "8hr":
        return 8 * 60;
      case "12hr":
        return 12 * 60;
      case "24hr":
        return 24 * 60;
      default:
        return 60; // Default to 1 hour
    }
  };

  // Data for the doughnut chart
  const doughnutChartData = {
    labels: oeeData?.downtimeReasons.map((reason) => reason.reason),
    datasets: [
      {
        data: oeeData?.downtimeReasons.map((reason) => reason.duration),
        backgroundColor: [
          "#FF5722",
          "#2196F3",
          "#4CAF50",
          "#FFC107",
          "#9C27B0",
        ], // Different colors for each reason
      },
    ],
  };

  // Data for the pie chart
  const totalDowntime = oeeData?.downtimeReasons.reduce(
    (acc, curr) => acc + curr.duration,
    0,
  );
  const pieChartData = {
    labels: ["Major", "Minor"],
    datasets: [
      {
        data: [
          oeeData?.downtimeReasons
            .filter((entry) => entry.criticality === "major")
            .reduce((acc, curr) => acc + curr.duration, 0),
          oeeData?.downtimeReasons
            .filter((entry) => entry.criticality === "minor")
            .reduce((acc, curr) => acc + curr.duration, 0),
          totalDowntime - (totalDowntime - getTimeInMinutes(selectedTimeRange)), // Remaining time is considered as active
        ],
        backgroundColor: ["#FF5722", "#2196F3", "#4CAF50"], // Major, Minor, Active colors
      },
    ],
  };

  return (
    <Grid container spacing={2}>
      {type !== "reports" && (
        <Grid item xs={12}>
          <ButtonGroup>
            <Button onClick={() => setSelectedTimeRange("1hr")}>
              Last 1 Hour
            </Button>
            <Button onClick={() => setSelectedTimeRange("8hr")}>
              Last 8 Hours
            </Button>
            <Button onClick={() => setSelectedTimeRange("12hr")}>
              Last 12 Hours
            </Button>
            <Button onClick={() => setSelectedTimeRange("24hr")}>
              Last 24 Hours
            </Button>
          </ButtonGroup>
        </Grid>
      )}
      <Grid item alignItems="center" xs={12} md={4}>
        <Typography variant="h6">Downtimes by Cause</Typography>
        <Doughnut data={doughnutChartData} />
      </Grid>
      <Grid item alignItems="center" xs={12} md={4}>
        <Typography variant="h6">Major / Minor Downtimes</Typography>
        <Pie data={pieChartData} />
      </Grid>
    </Grid>
  );
};

export default DownTimeKPI;
