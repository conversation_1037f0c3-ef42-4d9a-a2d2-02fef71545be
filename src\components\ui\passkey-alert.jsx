import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogContentText,
  DialogActions,
  DialogTitle,
  TextField,
} from "@mui/material";
import { useState } from "react";
import { dbUrl } from "../../constants/db";
import axios from "axios";
import SecretKeyInput from "./SecretKeyInput";
import { toast } from "react-toastify";

import PropTypes from "prop-types";

function PassKeyAlert({
  open,
  handleClose,
  callback,
  desc = "Enter passkey for approving the report.",
  submitBtnName = "Submit",
}) {
  const QA_VERIFY_SUCCESS = "QA Key verified successfully";
  const QA_VERIFY_FAILED = "QA Verification Failed";
  const CLOSE_BUTTON_ID = "404";
  const CLOSE_BUTTON_TEXT = "Close";

  const [secretKey, setSecretKey] = useState("");
  const [verified, setVerified] = useState(false);

  const verifyQA = async (value) => {
    if (value.length >= 8) {
      axios
        .post(`${dbUrl}/auth/qa`, {
          key: value,
        })
        .then((res) => {
          toast.success(QA_VERIFY_SUCCESS);
          setVerified(true);
        })
        .catch((err) => {
          toast.error(
            `${(err.response && err.response.data.message) ?? `${QA_VERIFY_FAILED} ${err.message}`}`,
          );
          setVerified(false);
        });
    } else {
      setVerified(false);
    }
  };
  return (
    <Dialog
      open={open}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title">{desc}</DialogTitle>
      <DialogContent>
        <DialogContentText
          id="alert-dialog-description"
          sx={{ margin: "1rem" }}
        >
          <SecretKeyInput
            secretKey={secretKey}
            setSecretKey={setSecretKey}
            verifyQA={verifyQA}
          />
          {/* <TextField
            label="Add Key"
            variant="outlined"
            value={secretKey}
            type='password'
            autoComplete="new-password"
            sx={{
              width: "100%",
            }}
            onChange={(e) => {
              setSecretKey(e.target.value)
              verifyQA(e)
            }}
          /> */}
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button
          color="error"
          id={CLOSE_BUTTON_ID}
          variant="contained"
          onClick={() => {
            setSecretKey("");
            handleClose();
          }}
        >
          {CLOSE_BUTTON_TEXT}
        </Button>
        <Button
          variant="contained"
          color="success"
          disabled={!verified}
          onClick={() => {
            callback();
            setSecretKey("");
          }}
          autoFocus
        >
          {submitBtnName}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

PassKeyAlert.propTypes = {
  open: PropTypes.bool.isRequired, // Determines whether the dialog is open
  handleClose: PropTypes.func.isRequired, // Function to close the dialog
  callback: PropTypes.func.isRequired, // Callback function to execute on successful passkey submission
  desc: PropTypes.string, // Description message for the dialog
  submitBtnName: PropTypes.string, // Customizable label for the submit button
};

PassKeyAlert.defaultProps = {
  desc: "Enter passkey for approving the report.", // Default description
  submitBtnName: "Submit", // Default submit button text
};

export default PassKeyAlert;
