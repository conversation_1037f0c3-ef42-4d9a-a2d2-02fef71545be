// Note: Content >> TableMain >> TableTemplates
//
import React, { useEffect, useMemo, useState } from "react";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import { T1, T2, T3, T4 } from "../TableTemplates/TableTemplates";
import readXlsxFile from "read-excel-file";
import { useStateContext } from "../../../context/ContextProvider";
import { ButtonBasic } from "../../../components/buttons/Buttons";
import { companies, companyId_constant } from "../../../constants/data";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../tools/toast";
import { db } from "../../../firebase";
import IQ1 from "../TableTemplates/iq/IQ1";
import IQ2 from "../TableTemplates/iq/IQ2";
import IQ3 from "../TableTemplates/iq/IQ3";
import IQ4 from "../TableTemplates/iq/IQ4";
import IQ5 from "../TableTemplates/iq/IQ5";
import IQ6 from "../TableTemplates/iq/IQ6";
import IQ7 from "../TableTemplates/iq/IQ7";
import IQ8 from "../TableTemplates/iq/IQ8";
import IQ9 from "../TableTemplates/iq/IQ9";
import IQ10 from "../TableTemplates/iq/IQ10";
import IQ11 from "../TableTemplates/iq/IQ11";
import IQ12 from "../TableTemplates/iq/IQ12";
import IQ13 from "../TableTemplates/iq/IQ13";
import IQ14 from "../TableTemplates/iq/IQ14";
import MFG1 from "../TableTemplates/manufacturing/MFG1";

import EQP1 from "../TableTemplates/EQP/EQP1";
import EQP2 from "../TableTemplates/EQP/EQP2";
import EQP3 from "../TableTemplates/EQP/EQP3";
import EQP4 from "../TableTemplates/EQP/EQP4";
import EQP5 from "../TableTemplates/EQP/EQP5";
import OQ1 from "../TableTemplates/OQ/OQ1";
import OQ2 from "../TableTemplates/OQ/OQ2";
import OQ3 from "../TableTemplates/OQ/OQ3";
import OQ4 from "../TableTemplates/OQ/OQ4";
import OQ5 from "../TableTemplates/OQ/OQ5";
import OQ6 from "../TableTemplates/OQ/OQ6";
import OQ7 from "../TableTemplates/OQ/OQ7";
import OQ8 from "../TableTemplates/OQ/OQ8";
import OQ9 from "../TableTemplates/OQ/OQ9";
import OQ10 from "../TableTemplates/OQ/OQ10";
import OQ11 from "../TableTemplates/OQ/OQ11";
import NonConformityCheckedBy from "../TableTemplates/Non-Conformity/NonConformityCheckedBy";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  Checkbox,
  Input,
  TextField,
  RadioGroup,
  Radio,
  FormControlLabel,
  Box,
} from "@mui/material";
//
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import { themeColors } from "../../../infrastructure/theme";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import Toolbar from "@mui/material/Toolbar";
import AppBar from "@mui/material/AppBar";
import CloseIcon from "@mui/icons-material/Close";

import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import Abbreviation from "../TableTemplates/Abbreviation/Abbreviation";
//
import Training from "../TableTemplates/Training/Training";
import Reference_Document from "../TableTemplates/Reference_Documents/Reference_Documents";
import Reference_Instruments from "../TableTemplates/Reference_Instruments/Reference_instruments";
import Post_Approval from "../TableTemplates/Post_Approval/post_approval";
import Summary_Conclusion from "../TableTemplates/Summary&Conclusion/summary_conclusion";
import Test_Result from "../TableTemplates/Test_Result/test_result";
import Execution_Table_PID from "../TableTemplates/Execution_Table_PID/execution_table_p&id";
import Pre_Requisites from "../TableTemplates/Pre_Requisites/pre_requisites";
import Execution_Table_12_1_5 from "../TableTemplates/ExecutionTables/execution_Table_12.1.5";
import Execution_Table_12_2_5 from "../TableTemplates/ExecutionTables/execution_table_12.2.5";
import Execution_Table_12_3_6 from "../TableTemplates/ExecutionTables/execution_table_12.3.6";
import Execution_Table_12_5_5 from "../TableTemplates/ExecutionTables/execution_table_12.5.5";
import Execution_Table_12_6_5 from "../TableTemplates/ExecutionTables/execution_table_12.6.5";
import Execution_Table_12_8_5 from "../TableTemplates/ExecutionTables/execution_table_12.8.5";
import Execution_Table_12_9_5 from "../TableTemplates/ExecutionTables/execution_table_12.9.5";
import Observation_Table_12_10_5 from "../TableTemplates/ObservationTables/observation_table_12.10.5";
import Observation_Table_12_11_4 from "../TableTemplates/ObservationTables/observation_table_12.11.4";
import Observation_Table_12_12_5 from "../TableTemplates/ObservationTables/observation_table_12.12.5";
import Observation_Table_13_2_4 from "../TableTemplates/ObservationTables/observation_table_13.2.4";
import Execution_Table_13_3_5 from "../TableTemplates/ExecutionTables/execution_table_13.3.5";
import Execution_Table_13_4_5 from "../TableTemplates/ExecutionTables/execution_table_13.4.5";
import Execution_Table_13_6_5 from "../TableTemplates/ExecutionTables/execution_table_13.6.5";
import Observation_Table_13_1_5 from "../TableTemplates/ObservationTables/observation_table_13.1.5";
import Observation_Table_13_5_5 from "../TableTemplates/ObservationTables/observation_table_13.5.5";
import SUMMARY_NON_CONFORMANCE from "../TableTemplates/Summary_Non_Conformace/summary_non_conformance";
import PQ1 from "../TableTemplates/PQ/PQ1";
import PQ2 from "../TableTemplates/PQ/PQ2";
import PQ3 from "../TableTemplates/PQ/PQ3";
import PQ4 from "../TableTemplates/PQ/PQ4";
import PQ5 from "../TableTemplates/PQ/PQ5";

export default function TableMain({
  type,
  machineName,
  fatDataDocId,
  handleClose,
  fatData,
  useAt,
}) {
  const [tableType, setTableType] = useState();
  //const [fileExcel, setFileExcel] = useState();
  const [excelData, setExcelData] = useState([]);
  const [excelDataProcessed, setExcelDataProcessed] = useState([]); // the body of table
  const [startRowsFrom, setStartRowsFrom] = useState(0);
  //const [tableData, setTableData] = useState([]); // formated data
  const [tableTitle, setTableTitle] = useState();
  const { currentMode } = useStateContext();
  const [addNew, setAddNew] = useState(false);
  const [editExisting, setEditExisting] = useState(false);
  const [curTables] = useState(
    "table_list" in fatData ? fatData["table_list"] : [],
  );
  const [curKeys, setCurKeys] = useState([]);
  const [tableKey, setTableKey] = useState("");
  const [coll, setColl] = useState("");
  const [sortDirection, setSortDirection] = useState("");
  const [fullWidth, setfullWidth] = useState(true);

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#000" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };
  useMemo(() => {
    // when theme changes then S.No array got reversed. So to stop that This Memonisation is used.
    setSortDirection("");
    //console.log("TableTemplates usememo")
  }, [currentMode]);

  const [open, setOpen] = React.useState(false);

  const tableList =
    //[
    {
      1: <T1 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      2: <T2 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      3: <T3 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      4: <T4 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      Abbreviation: (
        <Abbreviation
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Training: (
        <Training rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      Reference_Documents: (
        <Reference_Document
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Reference_Instruments: (
        <Reference_Instruments
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Post_Approval: (
        <Post_Approval
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Summary_Conclusion: (
        <Summary_Conclusion
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Test_Result: (
        <Test_Result
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Execution_Table_PID: (
        <Execution_Table_PID
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Pre_Requisites: (
        <Pre_Requisites
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Execution_Table_12_1_5: (
        <Execution_Table_12_1_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Execution_Table_12_2_5: (
        <Execution_Table_12_2_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Execution_Table_12_3_6: (
        <Execution_Table_12_3_6
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Execution_Table_12_5_5: (
        <Execution_Table_12_5_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Execution_Table_12_6_5: (
        <Execution_Table_12_6_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Execution_Table_12_8_5: (
        <Execution_Table_12_8_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Execution_Table_12_9_5: (
        <Execution_Table_12_9_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Execution_Table_13_3_5: (
        <Execution_Table_13_3_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Execution_Table_13_4_5: (
        <Execution_Table_13_4_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Execution_Table_13_6_5: (
        <Execution_Table_13_6_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Observation_Table_12_10_5: (
        <Observation_Table_12_10_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Observation_Table_12_11_4: (
        <Observation_Table_12_11_4
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Observation_Table_12_12_5: (
        <Observation_Table_12_12_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Observation_Table_13_2_4: (
        <Observation_Table_13_2_4
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Observation_Table_13_1_5: (
        <Observation_Table_13_1_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      Observation_Table_13_5_5: (
        <Observation_Table_13_5_5
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
      SUMMARY_NON_CONFORMANCE: (
        <SUMMARY_NON_CONFORMANCE
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),

      PQ1: <PQ1 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      PQ2: <PQ2 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      PQ3: <PQ3 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      PQ4: <PQ4 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      PQ5: <PQ5 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,

      IQ1: <IQ1 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      IQ2: <IQ2 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      IQ3: <IQ3 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      IQ4: <IQ4 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      IQ5: <IQ5 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      IQ6: <IQ6 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      IQ7: <IQ7 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      IQ8: <IQ8 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      IQ9: <IQ9 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      IQ10: (
        <IQ10 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      IQ11: (
        <IQ11 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      IQ12: (
        <IQ12 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      IQ13: (
        <IQ13 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      IQ14: (
        <IQ14 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      MFG1: (
        <MFG1 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      EQP1: (
        <EQP1 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      EQP2: (
        <EQP2 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      EQP3: (
        <EQP3 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      EQP4: (
        <EQP4 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      EQP5: (
        <EQP5 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      OQ1: <OQ1 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      OQ2: <OQ2 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      OQ3: <OQ3 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      OQ4: <OQ4 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      OQ5: <OQ5 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      OQ6: <OQ6 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      OQ7: <OQ7 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      OQ8: <OQ8 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      OQ9: <OQ9 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />,
      OQ10: (
        <OQ10 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      OQ11: (
        <OQ11 rowData={excelData?.slice(startRowsFrom)} useAt="tableMain" />
      ),
      Non_conformity_checked_by: (
        <NonConformityCheckedBy
          rowData={excelData?.slice(startRowsFrom)}
          useAt="tableMain"
        />
      ),
    };
  //];

  useEffect(() => {
    if (curTables.length === 0) return;
    const collect = curTables.filter(
      (options) => options.table_title === tableKey,
    );
    if (tableKey !== "") {
      setColl(collect[0].collection);
      setTableTitle(collect[0].table_title);
      setTableType(collect[0].collection.substring(5));
    } else setColl("");
  }, [tableKey]);

  const handleChangeTableType = (event) => {
    setTableType(event.target.value);
  };

  // excel file
  const handleFileChange = (e) => {
    // setFileExcel(e.target.files[0]);
    // console.log("TableMain one:", e.target.files[0]);

    readXlsxFile(e.target.files[0]).then((rows) => {
      console.log("Table Main two:", rows);
      setExcelData(rows);

      // const temp = [];
      // let head = rows.slice(0, 1);
      // console.log(" TableMain : head:", head[0][0]);
      // let body = rows.slice(1);
      // let promise = new Promise((resolve, regect) => {
      //     resolve();
      // })
      // promise.then(() => {
      //     body?.map((data) => {
      //         temp.push({
      //             [head[0][0]]: data[0], [head[0][1]]: data[1], [head[0][2]]: data[2], [head[0][3]]: data[3],
      //             [head[0][4]]: data[4], [head[0][5]]: data[5], [head[0][6]]: data[6], [head[0][7]]: data[7],
      //             [head[0][8]]: data[8], [head[0][9]]: data[9], [head[0][10]]: data[10], [head[0][11]]: data[11],
      //             [head[0][12]]: data[12], [head[0][13]]: data[13],
      //         })
      //     })
      // }).then(() => {
      //     setTableData([...temp]);
      //     console.log("temp:", temp)
      // })
    });
  };

  //
  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClosedialog = () => {
    setOpen(false);
  };
  const handleRowsfrom = (e) => {
    setStartRowsFrom(e.target.value);
    const rowNumber = e.target.value;
    //
    const temp = [];
    // let head = [...excelData.slice(rowNumber - 2, rowNumber - 1)]; // table head has info in two rows
    // let head2 = [...excelData.slice(rowNumber - 1, rowNumber)];  // table head has info in two rows
    // if(head?.length > 0 && head2?.length > 0) { // if statement is to remove warning and error
    // head[0]?.splice(1,3, head2[0][1], head2[0][2], head2[0][3]); // at position 1 remove 3 items and add 3 new items
    // }
    // console.log(" TableMain : head2:", head2);
    // console.log(" TableMain : head:", head);
    let body = excelData.slice(rowNumber);
    let promise = new Promise((resolve, regect) => {
      resolve();
    });
    promise
      .then(() => {
        // if(head?.length > 0 && head2?.length > 0) { // to remove warning and error
        //if(tableType === 1)
        switch (tableType) {
          case 1:
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                tag_no: data[1],
                desc: data[2],
                make_vendor: data[3],
                details: data[4],
                size: data[5],
                documents: data[6],
                result: data[7],
              });
            });
            break;

          //}
          // else if(tableType === 2) {
          case 2:
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                tag_no: data[1],
                desc: data[2],
                type_size: data[3],
                documents: data[4],
                result: data[5],
              });
            });
            break;
          //}
          // else if(tableType === 3) {
          case 3:
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                tag_no: data[1],
                desc: data[2],
                type_size: data[3],
              });
            });
            break;
          //}
          // else { //(tableType === 4)
          case 4:
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                tag_no: data[1],
                desc: data[2],
                make_vendor: data[3],
                details: data[4],
                doc_avail: data[5],
                sr_avail: data[6],
                type: data[7],
                result: data[8],
              });
            });
            break;

          case "Abbreviation":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                abbreviation: data[1],
                expanded_definition: data[2],
              });
            });
            break;
          case "Training":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                name: data[1],
                company_name: data[2],
                designation: data[3],
                department: data[4],
                sign_date: data[5],
              });
            });
            break;
          case "Reference_Documents":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                document_name: data[1],
                document_no: data[2],
                rev_no: data[3],
              });
            });
            break;

          case "Reference_Instruments":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                measuring_device: data[1],
                certificate_no: data[2],
                calibration_done_on: data[3],
                calibration_due_on: data[4],
              });
            });
            break;
          case "Post_Approval":
            body?.map((data) => {
              temp.push({
                responsibility: data[0],
                name: data[1],
                department: data[2],
                signature: data[3],
                date: data[4],
              });
            });
            break;

          case "Summary_Conclusion":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                installation_report_summary: data[1],
                obserbation: data[2],
              });
            });
            break;

          case "Pre_Requisites":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                pre_requisites: data[1],
                availability: data[2],
              });
            });
            break;

          case "Execution_Table_PID":
            body?.map((data) => {
              temp.push({
                test_criteria: data[0],
                method_of_verification: data[1],
                acceptance_criteria: data[2],
                obserbation: data[3],
              });
            });
            break;

          case "Test_Result":
            body?.map((data) => {
              temp.push({
                test_criteria: data[0],
                method_of_verification: data[1],
                acceptance_criteria: data[2],
                obserbation: data[3],
              });
            });
            break;

          case "Execution_Table_12_1_5":
            body?.map((data) => {
              temp.push({
                standard_ins_reading: data[0],
                indicator_reading: data[1],
                measuring_error: data[2],
                obserbation: data[3],
              });
            });
            break;

          case "Execution_Table_12_2_5":
            body?.map((data) => {
              temp.push({
                set_val: data[0],
                indicator_reading: data[1],
                tachometer_reading: data[2],
                measuring_error: data[3],
                max_allowable_err: data[4],
                obserbation: data[5],
              });
            });
            break;

          case "Execution_Table_12_3_6":
            body?.map((data) => {
              temp.push({
                calibration_point: data[0],
                reference_reading: data[1],
                indicator_reading: data[2],
                measuring_error: data[3],
                obserbation: data[4],
              });
            });
            break;
          case "Execution_Table_12_5_5":
            body?.map((data) => {
              temp.push({
                test_criteria: data[0],
                acceptance_criteria: data[1],
                obserbation: data[2],
              });
            });
            break;
          case "Execution_Table_12_6_5":
            body?.map((data) => {
              temp.push({
                from: data[0],
                to: data[1],
                acceptance_criteria: data[2],
                obserbation: data[3],
              });
            });
            break;
          case "Execution_Table_12_8_5":
            body?.map((data) => {
              temp.push({
                pass_details: data[0],
                settalable_range: data[1],
                acceptance_criteria: data[2],
                obserbation: data[3],
              });
            });
            break;
          case "Execution_Table_12_9_5":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                event_audit_trail: data[1],
                acceptance_criteria: data[2],
                obserbation: data[3],
              });
            });
            break;

          case "Execution_Table_13_3_5":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                sip_sequence_name: data[1],
                obserbation: data[2],
              });
            });
            break;
          case "Execution_Table_13_4_5":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                pressure_hold_test: data[1],
                obserbation: data[2],
              });
            });
            break;
          case "Execution_Table_13_6_5":
            body?.map((data) => {
              temp.push({
                requirement: data[0],
                obserbation: data[1],
              });
            });
            break;
          case "Observation_Table_12_10_5":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                condition: data[1],
                acceptance_criteria: data[2],
                actual: data[3],
                obserbation: data[4],
              });
            });
            break;

          case "Observation_Table_12_11_4":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                plc: data[1],
                acceptance_criteria: data[2],
                obserbation: data[3],
                result: data[4],
              });
            });
            break;
          case "Observation_Table_12_12_5":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                plc: data[1],
                condition: data[2],
                acceptance_criteria: data[3],
                expected_result: data[4],
                obserbation: data[5],
              });
            });
            break;

          case "Observation_Table_13_2_4":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                cip_sequence: data[1],
                obserbation: data[2],
              });
            });
            break;

          case "Observation_Table_13_1_5":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                pressure_hold_test: data[1],
                obserbation: data[2],
              });
            });
            break;
          case "Observation_Table_13_5_5":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                process_sequence: data[1],
                obserbation: data[2],
              });
            });
            break;

          case "SUMMARY_NON_CONFORMANCE":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                name_of_test: data[1],
                ncr_no: data[2],
                status: data[3],
              });
            });
            break;

          case "IQ1":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                desc: data[1],
                make_vendor: data[2],
                type: data[3],
                size: data[4],
                documents: data[5],
                result: data[6],
                // table_title: data[7],
              });
            });
            break;

          case "IQ2":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                tag_no: data[1],
                desc: data[2],
                make_vendor: data[3],
                type: data[4]?.toString(),
                size: data[5],
                documents: data[6],
                result: data[7],
              });
            });
            break;

          case "IQ3":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                tag_no: data[1],
                desc: data[2],
                make_vendor: data[3],
                model: data[4],
                type: data[5]?.toString(),
                size: data[6],
                documents: data[7],
                result: data[8],
              });
            });
            break;

          case "IQ4":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                tag_no: data[1],
                desc: data[2]?.toString(),
                make_vendor: data[3]?.toString(),
                model: data[4]?.toString(),
                type: data[5]?.toString(),
                rating: data[6]?.toString(),
                documents: data[7]?.toString(),
                result: data[8]?.toString(),
              });
            });
            break;

          case "IQ5":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                desc: data[1]?.toString(),
                make_vendor: data[2]?.toString(),
                model: data[3]?.toString(),
                type: data[4]?.toString(),
                rating: data[5]?.toString(),
                documents: data[6]?.toString(),
                result: data[7]?.toString(),
              });
            });
            break;

          case "IQ6":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                tag_no: data[1]?.toString(),
                desc: data[2]?.toString(),
                make_vendor: data[3]?.toString(),
                model: data[4]?.toString(),
                calibration_certificate_number: data[5]?.toString(),
                result: data[6]?.toString(),
              });
            });
            break;

          case "IQ7":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                desc: data[1]?.toString(),
                module_type: data[2]?.toString(),
                position: data[3]?.toString(),
                application: data[4]?.toString(),
                calibration_certificate_number: data[5]?.toString(),
                result: data[6]?.toString(),
              });
            });
            break;

          case "IQ8":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                tag_no: data[1]?.toString(),
                desc: data[2]?.toString(),
                make_vendor: data[3]?.toString(),
                model: data[4]?.toString(),
                test_report_no: data[5]?.toString(),
                result: data[6]?.toString(),
              });
            });
            break;

          case "IQ9":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                tag_no: data[1]?.toString(),
                desc: data[2]?.toString(),
                make_vendor: data[3]?.toString(),
                model: data[4]?.toString(),
                type: data[5]?.toString(),
                calibration_certificate_number: data[6]?.toString(),
                result: data[7]?.toString(),
              });
            });
            break;

          case "IQ10":
            body?.map((data) => {
              temp.push({
                software_name: data[0]?.toString(),
                make: data[1]?.toString(),
                version: data[2]?.toString(),
                application: data[3]?.toString(),
                remarks: data[4]?.toString(),
                result: data[5]?.toString(),
              });
            });
            break;

          case "IQ11":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                tag_no: data[1]?.toString(),
                desc: data[2]?.toString(),
                make: data[3]?.toString(),
                model: data[4]?.toString(),
                range: data[5]?.toString(),
                documents: data[6]?.toString(),
                result: data[7]?.toString(),
              });
            });
            break;

          case "IQ12":
            body?.map((data) => {
              temp.push({
                drawing_title: data[0]?.toString(),
                drawing_no: parseInt(data[1]),
                compliance: data[2]?.toString(),
              });
            });
            break;

          case "IQ13":
            body?.map((data) => {
              temp.push({
                serial_no: data[0], // s_no
                tag_no: data[1]?.toString(),
                desc: data[2]?.toString(),
                model_vendor: data[3]?.toString(),
                model_s_no: data[4]?.toString(),
                typeTable: data[5]?.toString(),
                size: data[6]?.toString(),
                docs: data[7]?.toString(),
                result: data[8]?.toString(),
              });
            });
            break;

          case "IQ14":
            body?.map((data) => {
              temp.push({
                serial_no: data[0], //s_no
                desc: data[1]?.toString(),
                model_vendor: data[2]?.toString(),
                model_s_no: data[3]?.toString(),
                typeTable: data[4]?.toString(),
                docs: data[5]?.toString(),
                result: data[6]?.toString(),
              });
            });
            break;

          case "MFG1":
            body?.map((data) => {
              temp.push({
                desc: data[0]?.toString(),
                document_number: data[1]?.toString(),
                yes_no: data[2]?.toString(),
              });
            });
            break;

          //
          case "EQP1":
            body?.map((data) => {
              temp.push({
                desc: data[0]?.toString(),
                protocol_number: data[1]?.toString(),
                compliance: data[2]?.toString(),
              });
            });
            break;

          case "EQP2":
            body?.map((data) => {
              temp.push({
                desc: data[0]?.toString(),
                page_number: data[1]?.toString(), //
                compliance: data[(2)?.toString()],
              });
            });
            break;

          case "EQP3":
            body?.map((data) => {
              temp.push({
                doc: data[0]?.toString(),
                test_report_number: data[1]?.toString(),
                compliance: data[2]?.toString(),
              });
            });
            break;

          case "EQP4":
            body?.map((data) => {
              temp.push({
                serial_no: data[0], // s_no
                qualification_document_name: data[1]?.toString(),
                page_number: data[2]?.toString(),
                nature_of_deviation: data[3]?.toString(),
                major: data[4]?.toString(),
                minor: data[5]?.toString(),
                action_taken: data[6]?.toString(),
              });
            });
            break;

          case "EQP5":
            body?.map((data) => {
              temp.push({
                comapnyName: data[0]?.toString(),
                name: data[1]?.toString(),
                signature: data[2]?.toString(),
                date: data[3]?.toString(),
              });
            });
            break;
          //

          case "OQ1":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                desc: data[1]?.toString(),
                desc_sec: data[2]?.toString(),
                mod_number: data[3]?.toString(),
                ch_number: data[4]?.toString(),
                signal: data[5]?.toString(),
                result: data[6]?.toString(),
              });
            });
            break;

          case "OQ2":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                desc: data[1]?.toString(),
                performance_parameters: data[2]?.toString(),
              });
            });
            break;

          case "OQ3":
            body?.map((data) => {
              temp.push({
                serial_no: data[0],
                desc: data[1]?.toString(),
                required: data[2]?.toString(),
                actuals: data[3]?.toString(),
              });
            });
            break;

          case "OQ4":
            body?.map((data) => {
              temp.push({
                desc: data[0]?.toString(),
                general_condition: data[1]?.toString(),
                actual_condition: data[2]?.toString(),
              });
            });
            break;

          case "OQ5":
            body?.map((data) => {
              temp.push({
                desc: data[0]?.toString(),
                acceptance_criteria: data[1]?.toString(),
                observation: data[2]?.toString(),
                pass_fail: data[3]?.toString(),
              });
            });
            break;

          case "OQ6":
            body?.map((data) => {
              temp.push({
                check_point: data[0]?.toString(),
                acceptance_criteria: data[1]?.toString(),
                observation: data[2]?.toString(),
                pass_fail: data[3]?.toString(),
              });
            });
            break;

          case "OQ7":
            body?.map((data) => {
              temp.push({
                check_point: data[0]?.toString(),
                acceptance_criteria: data[1]?.toString(),
                min: parseInt(data[2]),
                max: parseInt(data[3]),
                avg: data[4]?.toString(),
                pass_fail: data[5]?.toString(),
              });
            });
            break;

          case "OQ8":
            body?.map((data) => {
              temp.push({
                temprature_set_point: data[0]?.toString(),
                acceptance_criteria: data[1]?.toString(),
                average_time: parseInt(data[2]),
                variation: parseInt(data[3]),
                status: data[4]?.toString(),
              });
            });
            break;

          case "OQ9":
            body?.map((data) => {
              temp.push({
                shelf_sP: data[0]?.toString(), //selfSP
                acceptance: data[1]?.toString(),
                shelf_no: data[2]?.toString(), // shelfNo
                shelf_average: data[3]?.toString(), // shelfAverage
                all_shelves_average: data[4]?.toString(), // allShelvesAverage
                deviation: data[5]?.toString(),
                pass_fail: data[6]?.toString(), // passFail
              });
            });
            break;

          case "OQ10":
            body?.map((data) => {
              temp.push({
                check_point: data[0]?.toString(), //checkPoint
                results: data[1]?.toString(),
                confirm: data[2]?.toString(),
                deviation: data[3]?.toString(),
              });
            });
            break;

          case "OQ11":
            body?.map((data) => {
              temp.push({
                recipe: data[0]?.toString(),
                range: data[1]?.toString(),
                value: data[2]?.toString(),
                acceptance_criteria: data[3]?.toString(),
                chamber_lead: data[4]?.toString(),
                confirm: data[5]?.toString(),
              });
            });
            break;

          //
          case "Non_conformity_checked_by":
            body?.map((data) => {
              temp.push({
                company_name: data[0]?.toString(),
                name: data[1]?.toString(),
                signatures: data[2]?.toString(),
                date: data[3]?.toString(),
              });
            });
            break;
          // PQ starts
          //
          case "PQ1":
            body?.map((data) => {
              temp.push({
                serial_no: data[0]?.toString(),
                speed_of_machine: data[1]?.toString(),
                speed_in_rpm: data[2]?.toString(),
              });
            });
            break;

          //
          case "PQ2":
            body?.map((data, index) => {
              temp.push({
                serial_no: index?.toString(),
                parameter: data[0]?.toString(),
                acceptance_criteria: data[1]?.toString(),
                initial: data[2]?.toString(),
                min10: data[3]?.toString(),
                min20: data[4]?.toString(),
                min30: data[5]?.toString(),
              });
            });
            break;

          //
          case "PQ3":
            body?.map((data, index) => {
              temp.push({
                serial_no: index?.toString(),
                parameter: data[0]?.toString(),
                specification: data[1]?.toString(),
                rpm10: data[3]?.toString(),
                rpm20: data[4]?.toString(),
                rpm40: data[5]?.toString(),
              });
            });
            break;

          //
          case "PQ4":
            body?.map((data, index) => {
              temp.push({
                serial_no: data[0], //index?.toString(),
                parameter: data[1]?.toString(),
                limits: data[2]?.toString(),
                rpm40: data[3]?.toString(),
                rpm45: data[4]?.toString(),
                rpm50: data[5]?.toString(),
              });
            });
            break;

          //
          case "PQ5":
            body?.map((data, index) => {
              temp.push({
                serial_no: data[0], //index?.toString(),
                eqp_name: data[1]?.toString(),
                eqp_id: data[2]?.toString(),
                make: data[3]?.toString(),
                test_param: data[4]?.toString(),
                design_range: data[5]?.toString(),
                qualification_range: data[6]?.toString(),
                product_qualification_range: data[7]?.toString(),
                tolerance: data[8],
              });
            });
            break;
        }
      })
      .then(() => {
        // setExcelDataProcessed([...temp]);
        setExcelDataProcessed([...temp]);
        // console.log("temp:", temp);
      });
  };

  const handleAdd = (e) => {
    console.log("dd", excelData, tableTitle);
    if (excelData?.length > 0 && (tableTitle || editExisting)) {
      let promise = new Promise((resolve, reject) => {
        resolve();
      });

      promise
        .then(() => {
          // fatdata update with table title
          let dataForUpdate = fatData?.table_list
            ? {
                ...fatData,
                table_list: [
                  ...fatData?.table_list,
                  { collection: "table" + tableType, table_title: tableTitle },
                ],
              }
            : {
                ...fatData,
                table_list: [
                  { collection: "table" + tableType, table_title: tableTitle },
                ],
              };

          if (addNew) {
            // db.collection(companies)
            // .doc(companyId_constant)
            // .collection(type)
            // .doc(fatDataDocId)
            // .update(dataForUpdate)
            // .then(() => console.log("fatData table list added updated"));
            axios
              .put(`${dbConfig.url}/fatdatas/${fatDataDocId}`, dataForUpdate)
              .then(() => console.log("fatData table list added updated"))
              .catch((e) =>
                console.log(
                  "error fatData table list added updated failed:",
                  e,
                ),
              );
          }
          //

          // excelDataProcessed?.map((data, index) => {
          //   db.collection(companies)
          //     .doc(companyId_constant) // .collection('fatReportData').doc('QBuz970zml5oGFTfB0sK')
          //     .collection(type)
          //     .doc(fatDataDocId) // .doc('ifRbwpgjmwz54FuBIjyu')
          //     .collection(editExisting ? coll : "table" + tableType)
          //     .add({ ...data, url: null, table_title: tableTitle }) //{ tableData: JSON.stringify(excelData?.slice(startRowsFrom)), tableType: tableType}
          //     .then(() => {
          //       console.log(" Table added");
          //     })
          //     .catch((e) => {
          //       toastMessageWarning({
          //         message: "Something went wrong with table: ",
          //       });
          //     });
          // });
          console.log("jj", excelDataProcessed);
          excelDataProcessed?.map((data, index) => {
            axios
              .post(`${dbConfig.url}/generalTable`, {
                ...data,
                fat_id: fatDataDocId,
                table_type: tableType,
                url: undefined,
                table_title: tableTitle,
              })
              .then(() => console.log("generalTable table row added"))
              .catch((e) =>
                console.log("error generalTable table row added:", e),
              );
          });
        })
        .then(() => {
          handleClose();
          toastMessageSuccess({
            message: excelDataProcessed?.length + " rows added Successfully !",
          });
        });
      // promise.allSettled
    } else {
      toastMessage({ message: "! File not found or field/fields are missing" });
    }
  };

  return (
    <>
      <div className=" flex  justify-start flex-col">
        <div className="flex my-5">
          <ButtonBasic
            style={{ marginRight: "20px" }}
            buttonTitle="Add New"
            onClick={() => {
              setAddNew(true);
              setTableType("");
              setTableKey("");
              setExcelData([]);
              setExcelDataProcessed([]);
              setEditExisting(false);
            }}
          ></ButtonBasic>
          <ButtonBasic
            buttonTitle="Edit existing"
            onClick={() => {
              setEditExisting(true);
              setTableType("");
              setTableKey("");
              setExcelData([]);
              setExcelDataProcessed([]);
              setAddNew(false);
            }}
          ></ButtonBasic>
        </div>

        {addNew && (
          <div className=" flex  justify-between items-center  w-full">
            <div className="flex justify-center content-center mr-6 pl-2">
              <ButtonBasic
                buttonTitle="Select Table Type"
                onClick={handleClickOpen}
              />
            </div>
            <Dialog open={open} fullWidth maxWidth="md">
              <DialogTitle
                style={
                  currentMode === "Dark"
                    ? {
                        backgroundColor: themeColors.dark.secordary,
                        color: "white",
                      }
                    : {}
                }
              >
                <Box display="flex" alignItems="center">
                  <Box flexGrow={1}>Select Table Type</Box>
                  <Box>
                    <IconButton
                      edge="start"
                      color="inherit"
                      onClick={handleClosedialog}
                      aria-label="close"
                    >
                      <CloseIcon />
                    </IconButton>
                  </Box>
                </Box>
              </DialogTitle>
              <DialogContent>
                <FormControl sx={{ m: 1, maxWidth: 200 }} size="small">
                  <RadioGroup
                    labelId="demo-select-small"
                    id="demo-select-small"
                    value={tableType}
                    label="Table type"
                    onChange={handleChangeTableType}
                  >
                    <FormControlLabel
                      value={""}
                      control={<Radio />}
                      label="None"
                    />
                    <FormControlLabel
                      value={1}
                      control={<Radio />}
                      label="T1"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
            <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
          </TableRow>

          <TableRow  sx={{ border: theme.borderDesign }}>
            <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
          </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={3}
                          align="center"
                        >
                          specification
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                          align="center"
                        >
                          Details
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Size
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Documents
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Image
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Tag No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Make/Vendor
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>

                    <FormControlLabel
                      value={2}
                      control={<Radio />}
                      label="T2"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={7} className='uppercase'>product chamber and door</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={7}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow>

            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={7} className='uppercase'> Make : Lsi </TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className=" pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ minWidth: 500, border: theme.borderDesign }}
                          colSpan={3}
                          align="center"
                        >
                          specification
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Documents
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Image
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign, maxWidth: "6rem" }}
                        >
                          Tag No / Drawing No
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign, maxWidth: "6rem" }}
                        >
                          Description
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign, maxWidth: "6rem" }}
                        >
                          Type and Size
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={3}
                      control={<Radio />}
                      label="T3"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={4} className='uppercase'> Drawing review & verification</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          <span className=" pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          {" "}
                          Drawing Title{" "}
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          {" "}
                          Drawing No{" "}
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          {" "}
                          Compliance (YES / NO){" "}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          align="center"
                        >
                          Image
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={4}
                      control={<Radio />}
                      label="T4"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
            <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
          </TableRow>

          <TableRow  sx={{ border: theme.borderDesign }}>
            <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
          </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign, maxWidth: "5rem" }}
                          colSpan={2}
                        >
                          Tag No / Drawing No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Make/Vendor
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Type & Details
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign, maxWidth: "5rem" }}
                        >
                          Document Aavilable (Yes/No)
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign, maxWidth: "5rem" }}
                        >
                          Sr.No (Y/No)
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Type of doc
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Result
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Image
                        </TableCell>
                      </TableRow>
                    </TableHead>

                    {/* // Abbrevaiation */}
                    <FormControlLabel
                      value={"Abbreviation"}
                      control={<Radio />}
                      label="Abbreviation"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            align="center"
                          >
                            Abbreviation
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Expanded Definition
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Training"}
                      control={<Radio />}
                      label="Training"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            align="center"
                          >
                            Name
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Company Name
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Designation
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Department
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Sign & Date
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Reference_Documents"}
                      control={<Radio />}
                      label="Reference Documents"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            align="center"
                          >
                            Document Name
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Document No.
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Rev No.
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Reference_Instruments"}
                      control={<Radio />}
                      label="Reference Instruments"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            rowSpan={2}
                          >
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            rowSpan={2}
                            align="center"
                          >
                            Measuring device/Instrument/solutions
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            rowSpan={2}
                          >
                            Certificate No.
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={2}
                          >
                            Calibration
                          </TableCell>
                        </TableRow>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Done On
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Due On
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Post_Approval"}
                      control={<Radio />}
                      label="Post Approval"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={5}
                          >
                            AMNEAL ONCOLOGY PVT LTD
                          </TableCell>
                        </TableRow>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            RESPONSIBILITY
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            NAME
                          </TableCell>

                          <TableCell sx={{ border: theme.borderDesign }}>
                            DEPARTMENT
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            SIGNATURE
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            DATE
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Summary_Conclusion"}
                      control={<Radio />}
                      label="Summary & Conclusion"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Installation and Operational Qualification Report
                            Summary
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observtion( Pass / Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Pre_Requisites"}
                      control={<Radio />}
                      label="Pre Requisites"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Pre-requisites
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Availability (Yes / No / NA)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>
                    <FormControlLabel
                      value={"Execution_Table_PID"}
                      control={<Radio />}
                      label="Execution Table for P&ID Drawing"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Test criteria
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Reference / Method of Verification
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Acceptance Criteria
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation (Pass / Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Test_Result"}
                      control={<Radio />}
                      label="Test Result"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Test criteria
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Reference / Method of Verification
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Acceptance Criteria
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation (Pass / Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>
                    <FormControlLabel
                      value={"Execution_Table_12_1_5"}
                      control={<Radio />}
                      label="Execution Table 12.1.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Standard instrument reading (S)(°C)
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Indicator Reading (I) (°C)
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Measuring error ( E= I – S ) (°C)
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation (Pass / Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>
                    <FormControlLabel
                      value={"Execution_Table_12_2_5"}
                      control={<Radio />}
                      label="Execution Table 12.2.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Set Value (RPM)
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Indicator reading (RPM) (S)
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Tachometer reading (I) RPM
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Measuring error ( E= I – S ) (RPM)
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Max. Allowable error (RPM)
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation (Pass / Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>
                    <FormControlLabel
                      value={"Execution_Table_12_3_6"}
                      control={<Radio />}
                      label="Execution Table 12.3.6"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Calibration point
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Reference reading (S)
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Indicator(I)
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Measuring error ( E= I – S )
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation (Pass / Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Execution_Table_12_5_5"}
                      control={<Radio />}
                      label="Execution Table 12.5.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Test Criteria
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Acceptance Criteria
                          </TableCell>

                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation (Pass / Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Execution_Table_12_6_5"}
                      control={<Radio />}
                      label="Execution Table 12.6.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            From
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            To
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Acceptance Criteria
                          </TableCell>

                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation (Pass / Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Execution_Table_12_8_5"}
                      control={<Radio />}
                      label="Execution Table 12.8.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Password Details
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Settable range
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Acceptance Criteria
                          </TableCell>

                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation (Pass / Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Execution_Table_12_9_5"}
                      control={<Radio />}
                      label="Execution Table 12.9.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            align="center"
                          >
                            Event of the Audit Trail
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Acceptance Criteria
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation Pass / Fail
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>
                    <FormControlLabel
                      value={"Execution_Table_13_3_5"}
                      control={<Radio />}
                      label="Execution Table 13.3.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            align="center"
                          >
                            Name of the SIP Sequence
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation Pass / Fail
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>
                    <FormControlLabel
                      value={"Execution_Table_13_4_5"}
                      control={<Radio />}
                      label="Execution Table  13.4.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Pressure Hold Test
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation(Pass /Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>
                    <FormControlLabel
                      value={"Execution_Table_13_6_5"}
                      control={<Radio />}
                      label="Execution Table  13.6.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Requirement
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation(Pass /Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Observation_Table_12_10_5"}
                      control={<Radio />}
                      label="Observation Table 12.10.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            align="center"
                          >
                            Condition
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Acceptance Criteria
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Actual (Y/N)
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation Pass / Fail
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Observation_Table_12_11_4"}
                      control={<Radio />}
                      label="Observation Table 12.11.4"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            align="center"
                          >
                            PLC
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Acceptance Criteria
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Result Pass / Fail
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Observation_Table_12_12_5"}
                      control={<Radio />}
                      label="Observation Table 12.12.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            align="center"
                          >
                            PLC
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Condition
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Acceptance Criteria
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Expected Result(Yes/ No)
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation Pass/ Fail
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Observation_Table_13_2_4"}
                      control={<Radio />}
                      label="Observation Table 13.2.4"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            align="center"
                          >
                            CIP Sequence
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation Pass/ Fail
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Observation_Table_13_1_5"}
                      control={<Radio />}
                      label="Observation Table 13.1.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Pressure Hold Test
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation(Pass /Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"Observation_Table_13_5_5"}
                      control={<Radio />}
                      label="Observation Table 13.5.5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Name of the Process Sequence
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Observation(Pass /Fail)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>
                    <FormControlLabel
                      value={"SUMMARY_NON_CONFORMANCE"}
                      control={<Radio />}
                      label="SUMMARY OF NON-CONFORMANCE"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Name of the Test
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            NCR No.
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Status(Open / Closed)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    {/* // PQ */}

                    <FormControlLabel
                      value={"PQ1"}
                      control={<Radio />}
                      label="PQ1"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">Sr.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            align="center"
                          >
                            Speed of the Machine
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Speed in RPM
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"PQ2"}
                      control={<Radio />}
                      label="PQ2"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            rowSpan={2}
                          >
                            <span className="pr-1">Parameter</span>
                            {/* For serial number :-*/}
                            {/* {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" || sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null} */}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            // colSpan={6}
                            rowSpan={2}
                            align="center"
                          >
                            Acceptance criteria
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                            colSpan={4}
                          >
                            Sample interval time
                          </TableCell>
                        </TableRow>

                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Initial
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            10 min
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            20 min
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            30 min
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"PQ3"}
                      control={<Radio />}
                      label="PQ3"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            rowSpan={3}
                          >
                            <span className="pr-1">Process Parameter</span>
                            {/* For serial number :-*/}
                            {/* {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" || sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null} */}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            // colSpan={6}
                            rowSpan={3}
                            align="center"
                          >
                            Specification
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                            colSpan={3}
                          >
                            Observation
                          </TableCell>
                        </TableRow>

                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={3}
                            align="center"
                          >
                            Validation Batch No.: {"1234"}
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Slow speed <br />
                            (10 RPM)
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Slow speed <br />
                            (20 RPM)
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Slow speed <br />
                            (40 RPM)
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"PQ4"}
                      control={<Radio />}
                      label="PQ4"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            rowSpan={3}
                          >
                            <span className="pr-1">S No.</span>
                            {/* For serial number :-*/}
                            {/* {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" || sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null} */}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            // colSpan={6}
                            rowSpan={2}
                            align="center"
                          >
                            Parameters
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            // colSpan={6}
                            rowSpan={2}
                            align="center"
                          >
                            Limits
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                            colSpan={3}
                          >
                            Observation, B. No Abc123/123
                          </TableCell>
                        </TableRow>

                        <TableRow>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            At 40 RPM
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            At 45 RPM
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            At 50 RPM
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"PQ5"}
                      control={<Radio />}
                      label="PQ5"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            <span className="pr-1">S No.</span>
                            {/* For serial number :-*/}
                            {/* {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" || sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null} */}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            // colSpan={6}
                            align="center"
                          >
                            Eq. Name
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            // colSpan={6}
                            align="center"
                          >
                            Eq. ID
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Make/Model
                          </TableCell>

                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Test Parameter
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Design range
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Qualification range
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            For Product Qualification range
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            align="center"
                          >
                            Tolerance
                          </TableCell>
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"IQ1"}
                      control={<Radio />}
                      label="IQ1"
                    />
                    <div>
                      <TableHead>
                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            rowSpan={2}
                          >
                            <span className="pr-1">S.No</span>
                            {useAt !== "tableMain" ? (
                              <>
                                {sortDirection === "down" ||
                                sortDirection === "" ? (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("up")}
                                  >
                                    &#x21E9;{" "}
                                  </span>
                                ) : (
                                  <span
                                    className={theme.arrow}
                                    onClick={() => setSortDirection("down")}
                                  >
                                    &#x21E7;{" "}
                                  </span>
                                )}{" "}
                              </>
                            ) : null}
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            colSpan={6}
                            align="center"
                          >
                            specification
                          </TableCell>
                          <TableCell
                            sx={{ border: theme.borderDesign }}
                            rowSpan={2}
                          >
                            Image
                          </TableCell>
                        </TableRow>

                        <TableRow
                          sx={{
                            border: theme.borderDesign,
                            background: theme.backgroundColor,
                          }}
                        >
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Description
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Make/ Vendor
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Type
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Size
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Documents
                          </TableCell>
                          <TableCell sx={{ border: theme.borderDesign }}>
                            Result
                          </TableCell>
                          {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                        </TableRow>
                      </TableHead>
                    </div>

                    <FormControlLabel
                      value={"IQ2"}
                      control={<Radio />}
                      label="IQ2"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={5}
                          align="center"
                        >
                          Specification
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Documents
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Image
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Tag No/Drawing No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Make/ Vendor
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Type
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Size
                        </TableCell>

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ3"}
                      control={<Radio />}
                      label="IQ3"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={6}
                          align="center"
                        >
                          Specification
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Documents
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Image
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Tag No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Make/ Vendor
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Model/ Serial no
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Type
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Size
                        </TableCell>

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ4"}
                      control={<Radio />}
                      label="IQ4"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={6}
                          align="center"
                        >
                          Specification
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Documents
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Image
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Tag No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Make/ Vendor
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Model/ Serial no
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Type
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Rating
                        </TableCell>

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ5"}
                      control={<Radio />}
                      label="IQ5"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={5}
                          align="center"
                        >
                          Specification
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Documents
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Tag No</TableCell> */}
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Make/ Vendor
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Model/ Serial no
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Type
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Rating
                        </TableCell>

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ6"}
                      control={<Radio />}
                      label="IQ6"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={5}
                          align="center"
                        >
                          Specification
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Documents</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Result</TableCell> */}
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Tag No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Make/ Vendor
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Model/ Serial no
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Calibration Certificate Number
                        </TableCell>

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ7"}
                      control={<Radio />}
                      label="IQ7"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={4}
                          align="center"
                        >
                          Specification
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          {" "}
                          Calibration Certificate Number
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Tag No</TableCell> */}
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Module Type
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Position / Channel no
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Application
                        </TableCell>

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ8"}
                      control={<Radio />}
                      label="IQ8"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan={4} align='center'>Specification</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}> Calibration Certificate Number</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Result</TableCell>
                            { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Image</TableCell> } */}
                        {/* </TableRow> */}

                        {/* <TableRow sx={{ border: theme.borderDesign }}> */}
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Tag No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Make/ Vendor
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Model/ Serial no
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Test Report no
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Result
                        </TableCell>

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ9"}
                      control={<Radio />}
                      label="IQ9"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Tag No
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={4}
                          align="center"
                        >
                          Specification
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          {" "}
                          Calibration Certificate Number
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Make/ Vendor
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Model/ Serial no
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Type
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

                        {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} >Image</TableCell> }           */}

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ10"}
                      control={<Radio />}
                      label="IQ10"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          <span className="pr-1">Name of Software</span>
                          {/* {useAt !== 'tableMain' ? <>
                                    {(sortDirection === 'down' || sortDirection === '') ? <span className={theme.arrow} onClick={() => setSortDirection('up')}>&#x21E9; </span>
                                        : <span className={theme.arrow} onClick={() => setSortDirection('down')}>&#x21E7; </span>
                                    } </>
                                    : null
                                } */}
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan={4} align='center'>Specification</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}> Calibration Certificate Number</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Result</TableCell>
                            { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Image</TableCell> } */}
                        {/* </TableRow> */}

                        {/* <TableRow sx={{ border: theme.borderDesign }}> */}
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Tag No</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }}>Description</TableCell> */}
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Make
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          version
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Application
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Remark
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Result
                        </TableCell>

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ11"}
                      control={<Radio />}
                      label="IQ11"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Tag No
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={4}
                          align="center"
                        >
                          Specification
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          {" "}
                          Documents{" "}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Make
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Model/ Serial no
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Range
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

                        {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} >Image</TableCell> }           */}

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ12"}
                      control={<Radio />}
                      label="IQ12"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Drawing Title
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Drawing No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Compliance (Yes/No)
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ13"}
                      control={<Radio />}
                      label="IQ13"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          S.No
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={8}
                        >
                          Specification
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Tag No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Model Vendor
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Model Serial No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Type
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Size
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Documents
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Result
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"IQ14"}
                      control={<Radio />}
                      label="IQ14"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          S.No
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={4}
                        >
                          Specification
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Documents
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Model Vendor
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Model Serial No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Type
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"MFG1"}
                      control={<Radio />}
                      label="MFG1"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        {/* <TableCell sx={{ border: theme.borderDesign }}><span className='pr-1'>Name  of Software</span> */}
                        {/* {useAt !== 'tableMain' ? <>
                                    {(sortDirection === 'down' || sortDirection === '') ? <span className={theme.arrow} onClick={() => setSortDirection('up')}>&#x21E9; </span>
                                        : <span className={theme.arrow} onClick={() => setSortDirection('down')}>&#x21E7; </span>
                                    } </>
                                    : null
                                } */}
                        {/* </TableCell> */}
                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan={4} align='center'>Specification</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}> Calibration Certificate Number</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Result</TableCell>
                            { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Image</TableCell> } */}
                        {/* </TableRow> */}

                        {/* <TableRow sx={{ border: theme.borderDesign }}> */}
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Tag No</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }}>Description</TableCell> */}
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Report / Document number
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Yes / No
                        </TableCell>

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"EQP1"}
                      control={<Radio />}
                      label="EQP1"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Protocol Number
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Compliance (Complies OR Not)
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"EQP2"}
                      control={<Radio />}
                      label="EQP2s"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Page Number
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Compliance (Complies OR Not)
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"EQP3"}
                      control={<Radio />}
                      label="EQP3"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Documents
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Test Report Number
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Compliance (Complies OR Not)
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"EQP4"}
                      control={<Radio />}
                      label="EQP4"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          S.No
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Document Qualification Name
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Page Number
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Nature of Deviation
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={2}
                        >
                          Impact of Deviation
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Action Taken
                        </TableCell>
                      </TableRow>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          MAJOR
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          MINOR
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"EQP5"}
                      control={<Radio />}
                      label="EQP5"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Test Result
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={3}
                        >
                          PASS FAIL
                        </TableCell>
                      </TableRow>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Company's Name
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Name
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Signature
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Date
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"OQ1"}
                      control={<Radio />}
                      label="OQ1"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Tag No</TableCell> */}
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                          colSpan={2}
                        >
                          Description
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={2}
                          align="center"
                        >
                          Module Location
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          {" "}
                          Signal on/off{" "}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Result
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          MOD no
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          CH no
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Model/ Serial no</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }}>Range</TableCell>   */}
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

                        {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} >Image</TableCell> }           */}

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"OQ2"}
                      control={<Radio />}
                      label="OQ2"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Tag No</TableCell> */}
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={2}
                          align="center"
                        >
                          Specifications
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}> Signal on/off </TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Result</TableCell> */}
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Performance Parameters
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Model/ Serial no</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }}>Range</TableCell>   */}
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

                        {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} >Image</TableCell> }           */}

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"OQ3"}
                      control={<Radio />}
                      label="OQ3"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          <span className="pr-1">S.No</span>
                          {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" ||
                              sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null}
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Tag No</TableCell> */}
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={3}
                          align="center"
                        >
                          Requirements
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}> Signal on/off </TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Result</TableCell> */}
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Required
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Actuals
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Model/ Serial no</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }}>Range</TableCell>   */}
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

                        {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} >Image</TableCell> }           */}

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"OQ4"}
                      control={<Radio />}
                      label="OQ4"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          General condition
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Actual condition
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Model/ Serial no</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }}>Range</TableCell>   */}
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"OQ5"}
                      control={<Radio />}
                      label="OQ5"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Description
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Acceptance criteria
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Observation
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Pass/Fail
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Model/ Serial no</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }}>Range</TableCell>   */}
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"OQ6"}
                      control={<Radio />}
                      label="OQ6"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Check-Point
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Acceptance criteria
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Observation
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Pass/Fail
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Model/ Serial no</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }}>Range</TableCell>   */}
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"OQ7"}
                      control={<Radio />}
                      label="OQ7"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}><span className='pr-1'>S.No</span>
                                {useAt !== 'tableMain' ? <>
                                    {(sortDirection === 'down' || sortDirection === '') ? <span className={theme.arrow} onClick={() => setSortDirection('up')}>&#x21E9; </span>
                                        : <span className={theme.arrow} onClick={() => setSortDirection('down')}>&#x21E7; </span>
                                    } </>
                                    : null
                                }
                            </TableCell> */}
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Check-Point
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Acceptance criteria
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={2}
                          align="center"
                        >
                          Actuals
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Avg
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Pass/Fail
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Min
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Max
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

                        {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} >Image</TableCell> }           */}

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"OQ8"}
                      control={<Radio />}
                      label="OQ8"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}><span className='pr-1'>S.No</span>
                                {useAt !== 'tableMain' ? <>
                                    {(sortDirection === 'down' || sortDirection === '') ? <span className={theme.arrow} onClick={() => setSortDirection('up')}>&#x21E9; </span>
                                        : <span className={theme.arrow} onClick={() => setSortDirection('down')}>&#x21E7; </span>
                                    } </>
                                    : null
                                }
                            </TableCell> */}
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Temprature Set Point (*c)
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Acceptance criteria
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Calculated average after 30 Minutes
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Variation
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Ok/Not ok
                        </TableCell>

                        {/* </TableRow>

                        <TableRow sx={{ border: theme.borderDesign }}> */}

                        {/* <TableCell sx={{ border: theme.borderDesign }}>Min</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }}>Max</TableCell>   */}
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

                        {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} >Image</TableCell> }           */}

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"OQ9"}
                      control={<Radio />}
                      label="OQ9"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Shelf SP
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          Acceptance Criteria
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          colSpan={3}
                          align="center"
                        >
                          Observation
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          {" "}
                          Deviation{" "}
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          rowSpan={2}
                        >
                          PASS/FAIL
                        </TableCell>
                      </TableRow>

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Shelf No
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Shelf Average
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          All the shelves Average
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"OQ10"}
                      control={<Radio />}
                      label="OQ10"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Check Point
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Results
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Confirm YES/NO
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Deviation
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"OQ11"}
                      control={<Radio />}
                      label="OQ11"
                    />
                    <TableHead>
                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Recipe Information
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Range
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Set Value
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Acceptance Criteria
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Chamber Leak Rate
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          Confirm YES/NO
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <FormControlLabel
                      value={"Non_conformity_checked_by"}
                      control={<Radio />}
                      label="Non Conformity Checked by"
                    />
                    <TableHead>
                      {/* <TableRow  sx={{ border: theme.borderDesign }}>
<TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
</TableRow>

<TableRow  sx={{ border: theme.borderDesign }}>
<TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
</TableRow> */}

                      <TableRow
                        sx={{
                          border: theme.borderDesign,
                          background: theme.backgroundColor,
                        }}
                      >
                        {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}><span className='pr-1'>S.No</span>
        {useAt !== 'tableMain' ? <>
            {(sortDirection === 'down' || sortDirection === '') ? <span className={theme.arrow} onClick={() => setSortDirection('up')}>&#x21E9; </span>
                : <span className={theme.arrow} onClick={() => setSortDirection('down')}>&#x21E7; </span>
            } </>
            : null
        }
    </TableCell> */}
                        {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Tag No</TableCell> */}
                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan={3} align='center'>Requirements</TableCell> */}
                        {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}> Signal on/off </TableCell>
    <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Result</TableCell> */}
                        {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Image</TableCell> } */}
                        {/* </TableRow> */}

                        {/* <TableRow sx={{ border: theme.borderDesign }}> */}

                        <TableCell sx={{ border: theme.borderDesign }}>
                          {" "}
                          COMPANY'S NAME{" "}
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          {" "}
                          NAME
                        </TableCell>
                        <TableCell
                          sx={{ border: theme.borderDesign }}
                          width="15%"
                        >
                          {" "}
                          SIGNATURE{" "}
                        </TableCell>
                        <TableCell sx={{ border: theme.borderDesign }}>
                          {" "}
                          DATE{" "}
                        </TableCell>
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Model/ Serial no</TableCell>
    <TableCell sx={{ border: theme.borderDesign }}>Range</TableCell>   */}
                        {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

                        {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} >Image</TableCell> }           */}

                        {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
                      </TableRow>
                    </TableHead>
                  </RadioGroup>
                </FormControl>
              </DialogContent>
            </Dialog>

            {/* /////////////////////////////////////////////////////////// */}

            <input
              className="w-3/12 "
              type="file"
              disabled={tableType ? false : true}
              onChange={(e) => handleFileChange(e)}
            />

            {/* <ButtonBasic buttonTitle=" Add + " onClick={handleAdd} /> */}
            <div className="w-3/12">
              <div className="flex justify-center  items-center rounded-md px-1 py-1">
                <label for="row" style={{}}>
                  {" "}
                  Adjust Rows :{" "}
                </label>
                <input
                  id="row"
                  className={
                    currentMode === "Dark"
                      ? " bg-neutral-700 text-white p-1 rounded-md border-2 border-gray-200 w-6/12  "
                      : "bg-white text-gray-700 p-1 rounded-md border-2 border-gray-400  w-6/12"
                  }
                  type="number"
                  min={0}
                  onChange={(e) => handleRowsfrom(e)}
                  required
                  disabled={excelData ? false : true}
                />
              </div>
            </div>

            {/* <div className={excelDataProcessed.length > 0 ? 'animate-pulse px-1' : 'px-1'}>
            <ButtonBasic buttonTitle="Add" onClick={handleAdd} />
          </div> */}

            {/* <InputLabel style={{ marginBottom: '10px' }}>Title</InputLabel> */}
            {/* same table can repeat but title will be diffrent */}
            <div className="w-3/12">
              <div className="flex justify-center  items-center rounded-md px-1 py-1">
                <TextField
                  label="Table Title"
                  onChange={(e) => setTableTitle(e.target.value)}
                  onBlur={() => setTableTitle(tableTitle?.trim())}
                  value={tableTitle}
                  required
                  variant="outlined"
                  size="small"
                  //fullWidth multiline
                  //style={{ marginBottom: '12px' }}
                />
              </div>
            </div>
          </div>
        )}

        {editExisting && (
          <div className="flex justify-between">
            <FormControl
              sx={{ m: 1, minWidth: 120, maxWidth: 200 }}
              size="small"
            >
              <InputLabel id="demo-select-small">Table type</InputLabel>
              <Select
                labelId="demo-select-small"
                id="demo-select-small"
                value={tableKey}
                label="Table type"
                onChange={(e) => setTableKey(e.target.value)}
              >
                {curTables.map((options, index) => {
                  return (
                    <MenuItem
                      value={
                        "table_key" in options
                          ? options.table_key
                          : options.table_title
                      }
                    >
                      {options?.table_title}{" "}
                      {"table_key" in options ? options.table_key : ""}
                    </MenuItem>
                  );
                })}
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
              </Select>
            </FormControl>
            <input
              className="w-3/12 "
              type="file"
              onChange={(e) => handleFileChange(e)}
            />
            {/* <ButtonBasic buttonTitle=" Add + " onClick={handleAdd} /> */}
            <div className="w-3/12">
              <div className="flex justify-center  items-center rounded-md px-1 py-1">
                <label for="row" style={{}}>
                  {" "}
                  Adjust Rows :{" "}
                </label>
                <input
                  id="row"
                  className={
                    currentMode === "Dark"
                      ? " bg-neutral-700 text-white p-1 rounded-md border-gray-200 w-6/12  "
                      : "bg-white text-gray-700 p-1 rounded-md border-gray-400  w-6/12"
                  }
                  type="number"
                  min={0}
                  onChange={(e) => handleRowsfrom(e)}
                  required
                  disabled={excelData ? false : true}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="w-1/12">
        <div className=" flex justify-center  items-center rounded-md px-1 py-1">
          <div
            className={
              excelDataProcessed.length > 0 ? "animate-pulse px-1" : "px-1"
            }
          >
            <ButtonBasic buttonTitle="Add" onClick={handleAdd} />
          </div>
        </div>
      </div>
      {tableType && typeof tableType === "number"
        ? tableList[tableType]
        : tableList[tableType]}
    </>
  );
}
