import React, {
  Children,
  createContext,
  useContext,
  useEffect,
  useState,
} from "react";
import LoggingFunction from "../../components/CFR-Report/LoggingFunction";
import {
  companies,
  companyId_constant,
  subStep,
  testValues,
} from "../../constants/data";
import { db } from "../../firebase";
import { toastMessageSuccess } from "../../tools/toast";
import { firebaseLooper } from "../../tools/tool";
const SubStepcontext = createContext();
const SubStepCountcontext = createContext();

export function useSubStepContext() {
  return useContext(SubStepcontext);
}
export function useSubStepCountContext() {
  return useContext(SubStepCountcontext);
}

const SubStepProvider = ({ children }) => {
  const [subSteps, setSubSteps] = useState([]);
  const [subStepCount, setSubStepCount] = useState(0);
  const [testValueAll, setTestValueAll] = useState([]);
  const [openDelete, setOpenDelete] = useState(false);

  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(subStep)
    //   // .where("step_id", "==", step.id) //
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     setSubSteps(data);
    //     setSubStepCount(data?.length);
    //     // console.log("manualitem steps:"+data);
    //   });
    // Here all TestValue data has called. more optimisation can be done to reduce num of calls
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(testValues)
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     setTestValueAll(data);
    //     //console.log(data);
    //   });
    // Here all SensorValue data has called. more optimisation can be done to reduce num of calls
    // db.collection(companies)
    // .doc(companyId_constant)
    // .collection(liveEvents)
    // .onSnapshot((snap) => {
    //   const data = firebaseLooper(snap);
    //   setSensorValueAll(data);
    //console.log(data);
    //});
  }, []);

  return (
    <SubStepcontext.Provider value={subSteps}>
      <SubStepCountcontext.Provider value={subStepCount}>
        {children}
      </SubStepCountcontext.Provider>
    </SubStepcontext.Provider>
  );
};

export default SubStepProvider;
