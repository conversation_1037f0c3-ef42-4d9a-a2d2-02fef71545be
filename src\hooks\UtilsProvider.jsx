import "../types.js";
import React, { createContext, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { dbConfig } from "../infrastructure/db/db-config.js";
import axios from "axios";
// Constants
const SESSION_EXPIRED_MESSAGE = "You Session Expired. Please Login Again";
const DEFAULT_CARD_BG_COLOR = "#fff";
const DEFAULT_ROW_PER_PAGE = 10;
const DEFAULT_ADMINS_ACCESS = ["/ENV"];

/**
 * An object containing all available roles.
 * @property {string} ADMIN - The role for an admin user.
 * @property {string} USER - The role for a regular user.
 * @property {string} PLANNER - The role for a planner user.
 * @property {string} PERFORMER - The role for a performer user.
 * @property {string} APPROVER - The role for an approver user.
 */
export const allAvailableRoles = {
  ADMIN: "admin",
  USER: "user",
  PLANNER: "planner",
  PERFORMER: "performer",
  APPROVER: "approver",
};

const UtilsContext = createContext();

export function UtilsProvider({ children }) {
  const [envData, setEnvData] = useState({});
  const [isFetching, setIsFetching] = useState(true);

  useEffect(() => {
    async function fetchEnvData() {
      try {
        console.log("Fetching envData...");
        // Replace with your actual API call or initialization logic
        const response = await axios.get(`${dbConfig.url}/envdata`); // Example API endpoint
        const data = response.data;

        try {
          if (data && typeof data === "object" && "PORT" in data) {
            setEnvData(data);
            console.log("Fetched envData with PORT:", data);
          } else {
            console.warn(
              "Fetched envData does not contain PORT property:",
              data,
            );
            setEnvData({});
          }
        } catch (jsonError) {
          console.error("Response is not valid JSON:", jsonError);
          setEnvData({});
        }
      } catch (error) {
        console.error("Error fetching envData:", error);
        setEnvData({});
      } finally {
        setIsFetching(false);
      }
    }

    const userLoggedIn =
      sessionStorage.getItem("@user-creds") &&
      sessionStorage.getItem("@user-token");
    const userData = JSON.parse(sessionStorage.getItem("@user-creds"));
    console.log("User data:", userData);
    if (userLoggedIn) {
      fetchEnvData();
    } else {
      console.log("User is not logged in. Skipping envData fetch.");
      setIsFetching(false);
    }
  }, []);

  const contextValue = {
    envData,
    isFetching,
    currUser: null, // Example placeholder
    haveAccess: () => true, // Example placeholder
    notHaveAccess: () => false, // Example placeholder
  };

  return (
    <UtilsContext.Provider value={contextValue}>
      {children}
    </UtilsContext.Provider>
  );
}

export function useUtils() {
  const context = useContext(UtilsContext);
  if (!context) {
    console.error(
      "UtilsContext is not available. Ensure UtilsProvider is wrapping the component tree.",
    );
  } else {
    // console.log("UtilsContext is available:", context);
  }
  return context || { envData: {}, isFetching: false }; // Add fallback
}

UtilsProvider.propTypes = {
  children: PropTypes.node,
};

export default UtilsProvider;
