import React from "react";
import "./Buttons.scss";
import { useStateContext } from "../../context/ContextProvider";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { CancelSharp } from "@mui/icons-material";
import { Button, Tooltip } from "@mui/material";

function EditButtons(props) {
  const { currentColorLightDark, currentColor, currentMode } =
    useStateContext();
  return (
    <button
      style={{
        color: currentColor,
        width: props?.width,
        ...props?.style,
      }}
      className="viewBtn"
      onClick={props?.onClick}
    >
      <i
        className="ri-pencil-fill"
        style={{ fontSize: props?.fontSize, fontWeight: "bolder" }}
      ></i>
    </button>
  );
}

function ViewButtons(props) {
  const { currentColorLightDark, currentColor, currentMode } =
    useStateContext();
  return (
    <button
      className="viewBtn"
      style={{
        color: currentColor,
        width: props?.width,
        ...props?.style,
      }}
      onClick={props?.onClick}
    >
      View
    </button>
  );
}
function CommonButtons(props) {
  const { currentColor, currentMode } = useStateContext();
  return (
    <button className="viewBtn" onClick={props?.onClick}>
      {props?.buttonTitle}
    </button>
  );
}

export const ButtonBasic = ({ buttonTitle, onClick, ...props }) => {
  return (
    <Button
      size={props.size}
      variant="contained"
      style={{ width: props?.width }}
      color="primary"
      type={props?.type}
      onClick={onClick}
      {...props}
    >
      {buttonTitle} {/* Use buttonTitle as the button's content */}
    </Button>
  );
};

function ButtonBasicCancel(props) {
  const { currentColor, currentMode } = useStateContext();
  return (
    // <Tooltip title={props?.title} placement="top" arrow>
    <Button
      size={props.size}
      variant="contained"
      style={{ width: props?.width }}
      color="error"
      type={props?.type}
      onClick={props?.onClick}
      {...props}
    >
      {props?.buttonTitle}
    </Button>
    // </Tooltip>
  );
}

function ButtonWithVisibilityIcon(props) {
  const { currentColor, currentMode } = useStateContext();
  return (
    <Button
      variant="contained"
      type={props?.type}
      className="viewBtn shadow-sm shadow-amber-600 hover:shadow-md hover:shadow-amber-900 "
      //  style={{ backgroundColor: currentColor, width: props?.width }}
      onClick={props?.onClick}
    >
      <VisibilityIcon
        sx={{
          width: "22px",
          marginRight: "8px",
          ...props?.style,
          ...props?.sx,
        }}
      />
      {props?.buttonTitle}
    </Button>
  );
}

function SubmitButtons(props) {
  // Light green color, can be customized as needed
  const lightGreen = "rgb(59, 202, 15)";
  return (
    <Button
      size={props.size}
      variant="contained"
      style={{
        backgroundColor: lightGreen,
        color: "#fff",
        width: props?.width,
        ...props?.style,
      }}
      type={props?.type}
      onClick={props?.onClick}
      {...props}
    >
      {props?.buttonTitle || "Submit"}
    </Button>
  );
}

export {
  EditButtons,
  ViewButtons,
  CommonButtons,
  ButtonBasicCancel,
  ButtonWithVisibilityIcon,
  SubmitButtons, // export the new button
};
