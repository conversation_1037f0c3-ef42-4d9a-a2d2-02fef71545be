import React from "react";
import { useNavigate, useLocation } from "react-router-dom"; // Use useNavigate for react-router-dom v6+

export function TestComponent() {
  const navigate = useNavigate(); // Use useNavigate instead of useNavigate
  const location = useLocation();

  console.log(
    "TestComponent: useNavigate and useLocation are working correctly.",
  );
  console.log("Current location:", location);

  return (
    <div>
      <button onClick={() => navigate("/")}>Go Home</button>{" "}
      {/* Use navigate */}
    </div>
  );
}
