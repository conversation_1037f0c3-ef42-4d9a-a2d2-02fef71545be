import React, { useState, useContext, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import "./Header.scss";
import NotificationBox from "../NotificationBox/NotificationBox";
import Avatar from "@mui/material/Avatar";
import AccountCircleRoundedIcon from "@mui/icons-material/AccountCircleRounded";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import PersonAdd from "@mui/icons-material/PersonAdd";
import Settings from "@mui/icons-material/Settings";
import Logout from "@mui/icons-material/Logout";
import { ThemeContext } from "../../context/ThemeContext";
import { ReactComponent as MoonIcon } from "../../icons/moon.svg";
import { ReactComponent as SunIcon } from "../../icons/sun.svg";
import { db } from "../../firebase";
import { alluser, companies, companyId_constant } from "../../constants/data";
import { useAuth } from "../../hooks/AuthProvider";
import { firebaseLooper } from "../../tools/tool";
import { toastMessage } from "../../tools/toast";
import UserSessionLog from "../CFR-Report/UserSessionLog";
import { useStateContext } from "../../context/ContextProvider";
import ThemeSettings from "../ThemeSettings";

const HeaderLsi = ({ setInactive, inactive }) => {
  const [openNotification, setOpenNotification] = useState(false);
  const { currentUser, logout } = useAuth();
  const [anchorEl, setAnchorEl] = React.useState(null);
  const history = useNavigate();
  const open = Boolean(anchorEl);
  const [user, setUser] = useState([]);
  const {
    setCurrentColor,
    setCurrentMode,
    currentMode,
    activeMenu,
    currentColor,
    themeSettings,
    setThemeSettings,
    setMode,
  } = useStateContext();
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNotification = (data) => {
    if (data == "dashboard") {
      setOpenNotification(false);
    } else {
      setOpenNotification(!openNotification);
    }
  };
  const location = useLocation();
  const pathName = location.pathname.split("/")[1];
  // const newPathName = pathName.replace(/\\|\//g, '');
  // const [newPathName, setNewPathName] = useState(pathName.substring(1))
  // theme switchers

  const toggleTheme = () => {
    if (currentMode === "Dark") {
      let e = { target: { value: "Light" } }; // setMode has taken value from checkbox in new theme settings which is taking e as prop
      setMode(e);
    } else {
      let e = { target: { value: "Dark" } };
      setMode(e);
    }
  };

  useEffect(() => {
    // Theme switching on time. this will work when some activity will be going on.
    // let hour = new Date().getHours();
    // if (hour >= 18 || hour <= 6) {
    //   theme.dispatch({ type: 'DARKMODE' });
    // }
    // else {
    //   theme.dispatch({ type: 'LIGHTMODE' });
    // }

    if (pathName == "/") {
      setOpenNotification(false);
    }

    if (currentUser) {
    }
  }, []);

  async function handleLogout() {
    try {
      await logout();
      window.localStorage.removeItem("companyId");
      window.localStorage.removeItem("adminType");
      history("/company-id");
      UserSessionLog(
        "Logged Out",
        currentUser.email,
        user?.role,
        `${user?.fname} ${user?.lname}`,
      );
    } catch {
      toastMessage({ message: "Failed to log out" });
    }
  }

  return (
    <>
      <div
        style={
          currentMode === "Dark"
            ? { backgroundColor: "#242A2D", color: "white" }
            : {}
        }
        className="header"
      >
        {/* Left side */}
        <div className="headerLeft">
          <div
            className="menu"
            onClick={() => {
              setInactive(!inactive);
            }}
          >
            <i
              className={inactive ? "ri-menu-unfold-fill" : "ri-menu-line"}
            ></i>
          </div>
          <div className="themeSwitcher">
            <button
              onClick={toggleTheme}
              //   className={`themeBtn ${darkMode ? 'darkThemeBtn' : 'lightThemeBtn'
              className={`themeBtn ${
                currentMode === "Dark" ? "darkThemeBtn" : "lightThemeBtn"
              }`}
            >
              <SunIcon />
              <MoonIcon />
            </button>
          </div>
          <div className="pageName">{pathName}</div>
        </div>
        {/* Right side */}
        <div className="headerRight">
          <div className="notification">
            {pathName != "/" ? (
              <>
                {openNotification ? (
                  <i
                    className="ri-notification-4-fill shadow-orange-400 shadow-sm"
                    onClick={() => handleNotification()}
                  />
                ) : (
                  <i
                    className="ri-notification-4-fill"
                    onClick={() => handleNotification()}
                  />
                )}
              </>
            ) : (
              <i
                className="ri-notification-4-line text-gray-400 shadow-sm shadow-orange-400 "
                onClick={() => handleNotification("dashboard")}
              />
            )}

            <div
              className={
                openNotification
                  ? "notificationContainer"
                  : "notificationContainerHide"
              }
            >
              {/* <NotificationBox /> */}
              <div className="bg-red-500 flex justify-center rounded-sm">
                Arizon notifications Beta...
              </div>
            </div>
          </div>

          <div className="signin" onClick={handleClick}>
            <Avatar size="small" src={user.avatar || user.url} />
            <p className="text">{user?.fname + " " + user?.lname}</p>
          </div>
          <Menu
            anchorEl={anchorEl}
            id="demo-positioned-button"
            open={open}
            onClose={handleClose}
            onClick={handleClose}
            PaperProps={{
              style: {
                // this style is for avoiding horizontal menu items
                width: 150,
                padding: 1,
              },

              elevation: 0,
              sx: {
                overflow: "visible",
                filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
                mt: 1.5,
                "& .MuiAvatar-root": {
                  width: 32,
                  height: 32,
                  ml: -0.5,
                  mr: 1,
                },
                "&:before": {
                  content: '""',
                  display: "block",
                  position: "absolute",
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: "background.paper",
                  transform: "translateY(-50%) rotate(45deg)",
                  zIndex: 0,
                },
              },
            }}
            //transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
          >
            <MenuItem>
              <ListItemIcon>
                <AccountCircleRoundedIcon fontSize="small" />
              </ListItemIcon>
              {/* <s>My account</s> */}
              <Link style={{ textDecoration: "none" }} to="/account">
                {" "}
                My account
              </Link>
            </MenuItem>
            <MenuItem>
              <ListItemIcon>
                <PersonAdd fontSize="small" />
              </ListItemIcon>
              <s>Tasks</s>
            </MenuItem>

            <MenuItem onClick={() => setThemeSettings(true)}>
              <ListItemIcon>
                <Settings fontSize="small" />
              </ListItemIcon>
              Settings
            </MenuItem>

            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <Logout fontSize="small" />
              </ListItemIcon>
              Logout
            </MenuItem>
          </Menu>
        </div>
      </div>
      <div className="fixed right-4 bottom-4" style={{ zIndex: "1000" }}>
        {/* <Tooltip
              content="Settings"
              position="Top"
            >
              <button
                type="button"
                
                style={{ background: currentColor, borderRadius: '50%' }}
                className="text-3xl text-white p-3 hover:drop-shadow-xl hover:bg-light-gray"
              >
                <FiSettings />
              </button>

            </Tooltip> */}
        {themeSettings && <ThemeSettings />}
      </div>
    </>
  );
};

export default HeaderLsi;
