// It can't be used but can be used by directly implementing in the component for mobile number validation and
// other validations

import { InputLabel } from "@mui/material";
import React from "react";
import { useStateContext } from "../../context/ContextProvider";

function InputFieldNumber({ props }) {
  const { currentMode } = useStateContext();
  const indexSetter = (inputtxt) => {
    const phoneno = new RegExp("^[0-9]+$");
    if (inputtxt?.match(phoneno)) {
      setIndex(inputtxt);
    } else {
      return false;
    }
  };
  return (
    <>
      <InputLabel style={{ marginBottom: "10px" }}> {props?.label} </InputLabel>
      <input
        className={
          currentMode === "Dark"
            ? " bg-neutral-700 text-white p-4 rounded-md border-2 border-gray-500 w-full"
            : "bg-white text-gray-700 p-4 rounded-md border-2 border-gray-700 w-full"
        }
        onChange={props?.onChange}
        onBlur={props?.onBlur}
        value={props?.value}
        required
        style={{ marginBottom: "12px" }}
      />
    </>
  );
}

export { InputFieldNumber };
