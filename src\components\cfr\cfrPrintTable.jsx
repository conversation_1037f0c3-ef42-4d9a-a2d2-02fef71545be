import {
  Box,
  Paper,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import React from "react";
import { columnCode } from "../../pages/cfr";
import CfrRow from "./cfrRow";
import PropTypes from "prop-types";

// Table Constants
const DATE_TIME_HEADER = "Date/Time";
const MODULE_HEADER = "Module";
const ACTIVITY_HEADER = "Activity";
const USER_HEADER = "User";
const ROLE_HEADER = "Role";
const NO_DATA_MESSAGE = "No Data";

// Style Constants
const HEADER_BG_COLOR = "lightgrey";
const HEADER_TEXT_COLOR = "black";
const HEADER_FONT_SIZE = "0.9rem";
const HEADER_MIN_WIDTH = "6rem";

CfrPrintTable.propTypes = {
  currentItems: PropTypes.arrayOf(
    PropTypes.shape({
      _id: PropTypes.string.isRequired,
    }),
  ).isRequired,
  loading: PropTypes.bool.isRequired,
};

const useStyles = makeStyles((theme) => ({
  container: {
    maxHeight: "84vh",
  },
  tableHeaderCell: {
    backgroundColor: `${HEADER_BG_COLOR} !important`,
    color: `${HEADER_TEXT_COLOR} !important`,
    fontWeight: "bold !important",
    fontSize: `${HEADER_FONT_SIZE} !important`,
    minWidth: `${HEADER_MIN_WIDTH} !important`,
  },
}));

function CfrPrintTable({ currentItems, loading }) {
  const classes = useStyles();

  return (
    <Box sx={{ width: "-webkit-fill-available" }}>
      <TableContainer component={Paper} className={classes.container}>
        <Table stickyHeader aria-label="responsive table">
          <TableHead>
            <TableRow>
              <TableCell className={classes.tableHeaderCell} align="center">
                {DATE_TIME_HEADER}
              </TableCell>
              <TableCell className={classes.tableHeaderCell} align="left">
                {MODULE_HEADER}
              </TableCell>
              <TableCell className={classes.tableHeaderCell} align="left">
                {ACTIVITY_HEADER}
              </TableCell>
              <TableCell className={classes.tableHeaderCell} align="left">
                {USER_HEADER}
              </TableCell>
              <TableCell className={classes.tableHeaderCell} align="left">
                {ROLE_HEADER}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading &&
              Array.from({ length: 7 }).map((_, index) => (
                <TableRow key={index} className={classes.tableRow}>
                  {columnCode.map((column, colIndex) => (
                    <TableCell key={colIndex} align="center">
                      <Skeleton variant="text" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            {currentItems?.length > 0 ? (
              currentItems?.map((row, index) => (
                <CfrRow row={row} key={index + "cfr" + row._id} />
              ))
            ) : !loading ? (
              <TableRow>
                <TableCell colSpan={8} align="center">
                  <Typography
                    variant="h5"
                    color="textSecondary"
                    fontWeight="bold"
                  >
                    {NO_DATA_MESSAGE}
                  </Typography>
                </TableCell>
              </TableRow>
            ) : null}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}

export default CfrPrintTable;
