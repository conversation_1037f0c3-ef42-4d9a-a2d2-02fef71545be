import { Carousel } from "antd";
import React, { useEffect, useState } from "react";
import { db } from "../../../firebase";
import { firebaseLooper } from "../../../tools/tool";

const contentStyle = {
  height: "160px",
  color: "#fff",
  lineHeight: "160px",
  textAlign: "center",
  background: "#364d79",
};

const SubTraining = ({ title, manual_id }) => {
  const [steps, setSteps] = useState([]);

  useEffect(() => {
    // db.collection('companies').doc('LSxIRvLDr63DiXvFySR6')
    // .collection('steps').where('manual_id', '==',manual_id).onSnapshot(snap => {
    //     const data = firebaseLooper(snap)
    //     setSteps(data)
    // })
  }, []);

  return (
    <div className="w-full">
      <Carousel dotPosition="right" effect="fade">
        <div>
          <h3 style={contentStyle}>1</h3>
        </div>
        <div>
          <h3 style={contentStyle}>2</h3>
        </div>
        <div>
          <h3 style={contentStyle}>3</h3>
        </div>
        <div>
          <h3 style={contentStyle}>4</h3>
        </div>
      </Carousel>
    </div>
  );
};

export default SubTraining;
