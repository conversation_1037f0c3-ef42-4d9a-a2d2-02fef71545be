/* eslint-disable no-lone-blocks */
import {
  FormControl,
  InputLabel,
  Select,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  MenuItem,
  TableContainer,
  Paper,
  IconButton,
  Box,
  Typography,
} from "@mui/material";
import axios from "axios";
import {dbConfig} from "../../infrastructure/db/db-config";
import React, {useEffect, useState} from "react";
import {
  companies,
  companyId_constant,
  fatReport,
  satReport,
  machines,
} from "../../constants/data";
import {makeStyles} from "@mui/styles";
import {db} from "../../firebase";
import {firebaseLooper} from "../../tools/tool";
import "../machineData/contentPage.scss";
import Item from "./Item";
import {useStateContext} from "../../context/ContextProvider";
import "./Reports.scss";
import CFR from "../../components/CFR-Report/CFR";
import MaintenanceReportDataMain from "../machineData/MaintenanceReportDataMain";
import TrainingReportDataMain from "../machineData/TrainingReportDataMain";
import KeyboardDoubleArrowDownIcon from "@mui/icons-material/KeyboardDoubleArrowDown";
import KeyboardDoubleArrowUpIcon from "@mui/icons-material/KeyboardDoubleArrowUp";
import {sharedCss} from "../../styles/sharedCss";
import ChangeOverReportDataMain from "../machineData/ChangeOverReportMain";
import TimelineComponent from "../oee-reports/OeeReports";
import {v4 as uuidv4} from "uuid";
import MachineDropdown from "./MachineDropdown";
import NoDataComponent from "../../components/commons/noData.component";
import {useCommonOuterContainerStyle} from "../../styles/useCommonOuterContainerStyle";
import AlarmSopReportDataMain from "../machineData/AlarmReports/AlarmSopReportDataMain";

const REPORT_TYPE = [
  // "FAT Reports",
  // "SAT Reports",
  "Audit Reports",
  "ChangeOver Reports",
  "Maintenance Reports",
  "Training Reports",
  // "CFR Reports",
  "OEE Reports",
  "Alarm SOP Reports",
];

const FATReport = () => {
  const [reportType, setReportType] = useState(
    REPORT_TYPE[localStorage.getItem("fatReportTab") || 0],
  );
  //All fat reports
  const [FATReports, setFATReports] = useState([]);
  //All filtered fat reports
  const [filteredFATReports, setFilteredFATReports] = useState([]);
  //All sat reports
  const [SATReports, setSATReports] = useState([]);
  //All filtered sat reports
  const [filteredSATReports, setFilteredSATReports] = useState([]);
  //All machines
  const [machineData, setMachineData] = useState([]);
  const [machineId, setMachineId] = useState("All");
  const {currentMode, currentColorLight} = useStateContext();
  const [targetDesc, setTargetDesc] = useState("ascending");
  const [targetDate, setTargetDate] = useState("ascending");
  const [targetProt, setTargetProt] = useState("ascending");
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  // localStorage.removeItem("fatReportTab");
  const useCustomStyles = makeStyles(theme => ({
    machinePageContainer: {
      padding: "1rem",
      border: "1px solid gainsboro",
    },
  }));
  const handleMachineData = async () => {
    await axios
      .get(`${dbConfig.url}/machines`)
      .then(response => {
        setMachineData(response.data.data);
      })
      .catch(error => {
        console.error(error);
      });
  };

  // Fat or Audit Report list data
  const handleAuditListData = async () => {
    await axios
      .get(`${dbConfig.url}/fatlist-report`)
      .then(response => {
        console.log("fatlist report data:", response?.data);
        setFATReports(response.data);
        setFilteredFATReports(response?.data);
      })
      .catch(error => {
        console.error(error);
      });
  };
  useEffect(() => {
    handleMachineData();
    handleAuditListData();
  }, []);

  const handleSortDesc = arr => {
    if (targetDesc === "ascending") {
      const newObj = [...arr];
      newObj.sort((a, b) => {
        const nameA = a.description.toUpperCase();
        const nameB = b.description.toUpperCase();
        if (nameA < nameB) {
          return -1;
        }
        if (nameA > nameB) {
          return 1;
        }
        return 0;
      });
      setFilteredFATReports([...newObj]);
    } else if (targetDesc === "descending") {
      const newObj = [...arr];
      newObj.sort((a, b) => {
        const nameA = a.description.toUpperCase();
        const nameB = b.description.toUpperCase();
        if (nameA < nameB) {
          return 1;
        }
        if (nameA > nameB) {
          return -1;
        }
        return 0;
      });
      setFilteredFATReports([...newObj]);
    }
  };

  const handleSortProt = arr => {
    if (targetProt === "ascending") {
      const newObj = [...arr];
      newObj.sort((a, b) => {
        const nameA = a.protocol_no.toUpperCase();
        const nameB = b.protocol_no.toUpperCase();
        if (nameA < nameB) {
          return -1;
        }
        if (nameA > nameB) {
          return 1;
        }
        return 0;
      });
      setFilteredFATReports([...newObj]);
    } else if (targetProt === "descending") {
      const newObj = [...arr];
      newObj.sort((a, b) => {
        const nameA = a.protocol_no.toUpperCase();
        const nameB = b.protocol_no.toUpperCase();
        if (nameA < nameB) {
          return 1;
        }
        if (nameA > nameB) {
          return -1;
        }
        return 0;
      });
      setFilteredFATReports([...newObj]);
    }
  };

  const handleSortDate = arr => {
    const obj = {
      Jan: "01",
      Feb: "02",
      Mar: "03",
      Apr: "04",
      May: "05",
      Jun: "06",
      Jul: "07",
      Aug: "08",
      Sep: "09",
      Oct: "10",
      Nov: "11",
      Dec: "12",
    };
    if (targetDate === "ascending") {
      const newObj = [...arr];
      newObj.sort((a, b) => {
        const nameA = a?.date.toString().substring(4, 15);
        const nameB = b?.date.toString().substring(4, 15);
        const d1 = new Date(
          nameA.substring(7) +
            "-" +
            obj[nameA.substring(0, 3)] +
            "-" +
            nameA.substring(4, 7),
        );
        const d2 = new Date(
          nameB.substring(7) +
            "-" +
            obj[nameB.substring(0, 3)] +
            "-" +
            nameB.substring(4, 7),
        );
        if (d1 < d2) {
          return -1;
        }
        if (d1 > d2) {
          return 1;
        }
        return 0;
      });
      setFilteredFATReports([...newObj]);
    } else if (targetDate === "descending") {
      const newObj = [...arr];
      newObj.sort((a, b) => {
        const nameA = a?.date.toString().substring(4, 15);
        const nameB = b?.date.toString().substring(4, 15);
        const d1 = new Date(
          nameA.substring(7) +
            "-" +
            obj[nameA.substring(0, 3)] +
            "-" +
            nameA.substring(4, 7),
        );
        const d2 = new Date(
          nameB.substring(7) +
            "-" +
            obj[nameB.substring(0, 3)] +
            "-" +
            nameB.substring(4, 7),
        );
        if (d1 < d2) {
          return 1;
        }
        if (d1 > d2) {
          return -1;
        }
        return 0;
      });
      setFilteredFATReports([...newObj]);
    }
  };

  const handleChangeId = e => {
    setMachineId(e.target.value);

    let temp = FATReports.filter(data => data.mid === e.target.value);
    setFilteredFATReports(temp);
    if (e.target.value === "All") {
      setFilteredFATReports(FATReports);
    }
  };
  const commonCss = sharedCss();
  const customCss = useCustomStyles();
  console.log(
    machineId,
    "\n",
    machineData.filter(({_id}) => _id === machineId)[0]?.title || "All",
  );

  return (
    <div>
      <div className="reportSection">
        <header className={`reportHeader`}>
          <div className="tabsContainer">
            {/* Ensuring OEE Reports is last */}
            {[
              ...REPORT_TYPE.filter(reportType => reportType !== "OEE Reports"),
              "OEE Reports",
            ].map(type => (
              <div
                key={`${type.replace(" ", "_")}_tab`}
                onClick={() => setReportType(type)}
                className={
                  reportType === type
                    ? "report_tab report_active_tab"
                    : "report_tab"
                }>
                {type}
              </div>
            ))}
          </div>
        </header>

        {reportType === REPORT_TYPE[0] && (
          <main
            className={`${commonCss.sectionContainer} ${commonCss.backgroundLight} border-radius-outer`}>
            <Box className={commonCss.tableLable}>
              <Typography fontWeight="bold" variant="h6">
                {reportType}
              </Typography>
              {/* Use the common dropdown component */}
              <MachineDropdown
                machineId={machineId}
                machineData={machineData}
                handleChangeId={handleChangeId}
              />
            </Box>
            <TableContainer
              className="table border-radius-inner"
              component={Paper}
              sx={commonOuterContainerStyle}>
              <Table
                style={{
                  minWidth: 650,
                  width: "100%",
                }}>
                <TableHead
                  style={
                    currentMode === "Dark"
                      ? {backgroundColor: "#212B36"}
                      : {backgroundColor: "#F5F5F5"}
                  }>
                  <TableRow>
                    <TableCell
                      style={
                        currentMode === "Dark"
                          ? {color: "white"}
                          : {backgroundColor: "#E0E0E0"}
                      }
                      align="left">
                      Protocol No.
                      <IconButton
                        onClick={() => {
                          if (targetProt === "descending")
                            setTargetProt("ascending");
                          else setTargetProt("descending");
                          handleSortProt(filteredFATReports);
                        }}>
                        {targetProt === "descending" ? (
                          <KeyboardDoubleArrowDownIcon />
                        ) : (
                          <KeyboardDoubleArrowUpIcon />
                        )}
                      </IconButton>
                    </TableCell>
                    <TableCell
                      style={
                        currentMode === "Dark"
                          ? {color: "white"}
                          : {backgroundColor: "#E0E0E0"}
                      }
                      align="left">
                      Description
                      <IconButton
                        onClick={() => {
                          if (targetDesc === "descending")
                            setTargetDesc("ascending");
                          else setTargetDesc("descending");
                          handleSortDesc(filteredFATReports);
                        }}>
                        {targetDesc === "descending" ? (
                          <KeyboardDoubleArrowDownIcon />
                        ) : (
                          <KeyboardDoubleArrowUpIcon />
                        )}
                      </IconButton>
                    </TableCell>
                    <TableCell
                      style={
                        currentMode === "Dark"
                          ? {color: "white"}
                          : {backgroundColor: "#E0E0E0"}
                      }
                      align="left">
                      Performed By
                    </TableCell>
                    <TableCell
                      style={
                        currentMode === "Dark"
                          ? {color: "white"}
                          : {backgroundColor: "#E0E0E0"}
                      }
                      align="left">
                      Date
                      <IconButton
                        onClick={() => {
                          if (targetDate === "descending")
                            setTargetDate("ascending");
                          else setTargetDate("descending");
                          handleSortDate(filteredFATReports);
                        }}>
                        {targetDate === "descending" ? (
                          <KeyboardDoubleArrowDownIcon />
                        ) : (
                          <KeyboardDoubleArrowUpIcon />
                        )}
                      </IconButton>
                    </TableCell>
                    <TableCell
                      style={
                        currentMode === "Dark"
                          ? {color: "white"}
                          : {backgroundColor: "#E0E0E0"}
                      }
                      align="left">
                      Actions
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredFATReports?.map(data => (
                    <Item data={data} key={uuidv4()} />
                  ))}
                  {filteredFATReports?.length === 0 && (
                    <>
                      {/*
                    <TableRow>
                      <TableCell
                        colSpan={6}
                        sx={{ textTransform: "capitalize" }}
                        style={{ padding: "24px" }}
                      >
                        <div className="flex justify-center w-full content-center animate-pulse">
                          No Data
                        </div>
                      </TableCell>
                    </TableRow>
                    */}
                      <NoDataComponent cellColSpan={6} paddText={true} />
                    </>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </main>
        )}

        {reportType === REPORT_TYPE[1] && (
          <ChangeOverReportDataMain
            machineData={machineData}
            machineId={machineId}
            handleChangeId={handleChangeId}
          />
        )}

        {reportType === REPORT_TYPE[2] && (
          <MaintenanceReportDataMain
            machineData={machineData}
            machineId={machineId}
            handleChangeId={handleChangeId}
          />
        )}

        {reportType === REPORT_TYPE[3] && (
          <TrainingReportDataMain
            machineData={machineData}
            machineId={machineId}
            handleChangeId={handleChangeId}
          />
        )}

        {reportType === REPORT_TYPE[5] && (
          <AlarmSopReportDataMain
            machineData={machineData}
            machineId={machineId}
            handleChangeId={handleChangeId}
          />
        )}

        {/* {reportType === REPORT_TYPE[4] && (
          <CFR filteredMachineName={machineData.filter(({ _id }) => _id === machineId)[0]?.title || "All"}/>
        )} */}

        {reportType === REPORT_TYPE[4] && (
          <TimelineComponent machineId={machineId} />
        )}
      </div>
    </div>
  );
};

export default FATReport;
