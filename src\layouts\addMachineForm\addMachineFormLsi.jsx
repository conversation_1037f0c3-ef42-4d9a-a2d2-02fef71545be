import React, { useEffect, useState } from "react";
import { alpha, styled } from "@mui/material/styles";
import {
  <PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>le,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputBase,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import "./addMachineForm.scss";
import { Link, useNavigate } from "react-router-dom";
import { db } from "../../firebase";
import {
  companies,
  companyId_constant,
  liveData,
  machines,
  maintenance,
  steps,
  training,
} from "../../constants/data";
import { toastMessageSuccess } from "../../tools/toast";
import { useAuth } from "../../hooks/AuthProvider";
import { useStateContext } from "../../context/ContextProvider";
import { firebaseLooper } from "../../tools/tool";
import { Box } from "@mui/system";
import {
  But<PERSON><PERSON>asi<PERSON>,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";

const BootstrapInput = styled(InputBase)(({ theme }) => ({
  "label + &": {
    marginTop: theme.spacing(0),
  },
  "& .MuiInputBase-input": {
    borderRadius: 4,
    position: "relative",
    backgroundColor: theme.palette.mode === "light" ? "#fcfcfb" : "#161C24",
    border: "1px solid #ced4da",
    fontSize: 14,
    color: theme.palette.mode === "light" ? "#344767" : "#fcfcfb",
    padding: "12px",
    transition: theme.transitions.create([
      "border-color",
      "background-color",
      "box-shadow",
    ]),
    "&:focus": {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main,
    },
  },
}));

const AddMachineFormLsi = () => {
  const { currentMode, currentColor } = useStateContext();
  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");
  const [location, setLocation] = useState("");
  const [equipmentId, setEquipmentId] = useState("");
  const [serialNo, setSerialNo] = useState("");
  const [model, setModel] = useState("");
  const [block, setBlock] = useState("");
  const [warranty, setWarranty] = useState("");
  const [status, setStatus] = useState("");
  const [machinesDetails, setMachines] = useState([]);
  const [newMachineId, setNewMachineId] = useState("");
  const [cloneMachineId, setCloneMachineId] = useState("");
  const [openClone, setOpenClone] = useState(false);
  const [machineDetails, setMachineDetails] = useState([]);
  const [wait, setWait] = useState(false);
  const [cloneComplete, setCloneComplete] = useState(false);
  const [mClone, setMClone] = useState(false);
  const history = useNavigate();

  const [companyList, setCompanyList] = useState([]);
  const [companyIdFrom, setCompanyIdFrom] = useState("");
  const [companyIdTo, setCompanyIdTo] = useState("");

  const [openCloneModule, setOpenCloneModule] = useState(false); //Confirm Whether to clone or not --> After submit

  // var ROOT = db.collection(companies).doc(companyIdFrom);
  // var ROOTTo = db.collection(companies).doc(companyIdTo)

  //clone modules
  const [liveDataClone, setLiveDataClone] = useState(null);
  const [maintenanceClone, setMaintenanaceClone] = useState(null);
  const [trainingClone, setTrainingClone] = useState(null);
  const [fatListClone, setFatListClone] = useState(null);
  const [processValues, setProcessValues] = useState(null);

  //subModules Clone module
  const [maintenanceStepsClone, setMaintenanceStepsClone] = useState(null); //by manual_id
  const [trainingStepsClone, setTrainingStepsClone] = useState(null); //by manual_id
  const [fatDataClone, setFatDataClone] = useState(null); //sorted by fid

  //subModules FAT Table
  const [fatTableClone, setFatTableClone] = useState(null); //by fatData --> Table

  const { currentUser } = useAuth();

  useEffect(() => {
    // ROOT.collection(machines).onSnapshot(snap => {
    //   const data = firebaseLooper(snap)
    //   setMachines(data)
    //   // console.log(data)
    // })
    // db.collection(companies).onSnapshot(snap => {
    //     const data = firebaseLooper(snap);
    //     console.log("all companies:", data)
    //     setCompanyList(data);
    // })
    //cloning here - with live machine changes to
  }, []);

  //
  const handleOnChangeCompanyFrom = (e) => {
    setCompanyIdFrom(e.target.value);
    // db.collection(companies).doc(e.target.value).collection(machines).onSnapshot(snap => {
    //     const data = firebaseLooper(snap);
    //     setMachines(data);
    // })
  };

  const handleOnChangeCompanyTo = (e) => {
    setCompanyIdTo(e.target.value);
  };

  // var ROOT = db.collection(companies).doc(companyIdFrom);

  const handledataClone = (e) => {
    //  var ROOT = db.collection(companies).doc(companyIdFrom);
    // const mid = e.target.value
    // setMClone(true)
    // db.collection(companies).doc(companyIdFrom).collection(machines).doc(mid).get().then(snap => {
    //     const data = snap.data()
    //     setMachineDetails(data)
    //     setTitle(data.title)
    //     setDesc(data.desc)
    //     setLocation(data.location)
    //     setBlock(data.block)
    //     setWarranty(data.warranty)
    //     setModel(data.model)
    //     setSerialNo(data.serialNo)
    //     setStatus(data.status)
    //     setEquipmentId(data.equipmentId)
    //     setCloneMachineId(data.id)
    //     //clone live data
    //     ROOT.collection('ImageAnnotationModule').where('mid', '==', `${mid}`).get().then(snap => {
    //         const live = firebaseLooper(snap)
    //         // console.log('from live', live)
    //         setLiveDataClone(live)
    //     })
    //     ROOT.collection('liveData2').where('mid', '==', `${mid}`).get().then(snap => {
    //         const live = firebaseLooper(snap)
    //         // console.log('from live', live)
    //         setProcessValues(live)
    //     })
    //     //clone fatList
    //     ROOT.collection('fatList').where('mid', '==', `${mid}`).get().then(snap => {
    //         const fat = firebaseLooper(snap)
    //         // console.log('from fat', fat)
    //         setFatListClone(fat)
    //         let fatMain = []
    //         //FAT data --> test execution table
    //         let tableClone = []
    //         for (let x in fat) {
    //             ROOT.collection('fatData').where('fid', '==', `${fat[x].id}`).get().then(snap => {
    //                 const fatReq = firebaseLooper(snap)
    //                 fatMain.push(...fatReq) //FAT DATA FROM FAT LIST
    //                 console.log('from fat Clone - FatData', fatMain)
    //                 // for (let y in fatReq) {
    //                 //     ROOT.collection('fatData').doc(fatReq[y].id).collection('table').get().then(snap => {
    //                 //         const tableData = firebaseLooper(snap)
    //                 //         tableClone.push({ tableData, fatMainId: fatReq[y].id })
    //                 //         // console.log('Table cloned', tableClone)
    //                 //     })
    //                 // }
    //             })
    //         }
    //         //set clone FAT Data to state
    //         setFatDataClone(fatMain)
    //         for (let y in fatMain) {
    //             ROOT.collection('fatData').doc(fatMain[y].id).collection('table').get().then(snap => {
    //                 const tableData = firebaseLooper(snap)
    //                 tableClone.push({ tableData, fatMainId: fatMain[y].id })
    //                 // console.log('Table cloned', tableClone)
    //             })
    //         }
    //         setFatTableClone(tableClone)
    //     })
    //     //clone maintenance
    //     ROOT.collection(maintenance).where('mid', '==', `${mid}`).get().then(snap => {
    //         const main = firebaseLooper(snap)
    //         setMaintenanaceClone(main)
    //         let stepsClone = [] //temp array
    //         //iterate through manuals
    //         for (let i in main) {
    //             ROOT.collection(steps).where('manual_id', '==', `${main[i].id}`).get().then(snap => {
    //                 const mainSteps = firebaseLooper(snap)
    //                 stepsClone.push(...mainSteps)
    //                 // console.log(`steps cloned`, i)
    //                 // console.log('steps cloned - Main', stepsClone)
    //             })
    //         }
    //         //Set to stepsClone
    //         setMaintenanceStepsClone(stepsClone)
    //     })
    //     //clone training
    //     ROOT.collection(training).where('mid', '==', `${mid}`).get().then(snap => {
    //         const train = firebaseLooper(snap)
    //         setTrainingClone(train)
    //         let stepsClone = [] //temp array
    //         //iterate through manuals
    //         for (let i in train) {
    //             ROOT.collection(steps).where('manual_id', '==', `${train[i].id}`).get().then(snap => {
    //                 const mainSteps = firebaseLooper(snap)
    //                 stepsClone.push(...mainSteps)
    //                 // console.log(`steps cloned`, i)
    //                 // console.log('steps cloned Training', stepsClone)
    //             })
    //         }
    //         //Set to stepsClone
    //         setTrainingStepsClone(stepsClone)
    //     })
    // })
  };

  //CREATE NEW MACHINE
  const handleSubmit = (e) => {
    // var ROOTTo = db.collection(companies).doc(companyIdTo)
    // e.preventDefault()
    // if (mClone) {
    //     setWait(true)
    // }
    // const machineNew = { title, desc, location, equipmentId, serialNo, model, block, warranty, status, createdBy: currentUser.email }
    // if (mClone) {
    //     ROOTTo.collection(machines).add(machineNew).then((event) => {
    //         //CREATE NEW NODES FOR LIVE DATA
    //         for (let i in liveDataClone) {
    //             ROOTTo.collection('ImageAnnotationModule').add({ ...liveDataClone[i], mid: event.id })
    //                 .then(() => {
    //                     // console.log('Live added', i)
    //                 })
    //         }
    //         for (let i in processValues) {
    //             ROOTTo.collection('liveData2').add({ ...processValues[i], mid: event.id })
    //                 .then(() => {
    //                     // console.log('Live added', i)
    //                 })
    //         }
    //         //CREATE NEW NODES FOR MAINTENANCE
    //         for (let i in maintenanceClone) {
    //             ROOTTo.collection(maintenance).add({ ...maintenanceClone[i], mid: event.id })
    //                 .then((mainEvent) => {
    //                     // console.log('main added', i)
    //                     //CREATE Steps
    //                     for (let x in maintenanceStepsClone) {
    //                         if (maintenanceStepsClone[x].manual_id === maintenanceClone[i].id) {
    //                             ROOTTo.collection(steps).add({ ...maintenanceStepsClone[x], manual_id: mainEvent.id }).then((e) => {
    //                                 // console.log('step added to clone from maintenance', x)
    //                             })
    //                         }
    //                     }
    //                 })
    //         }
    //         //CREATE NEW NODES FOR TRAINING
    //         for (let i in trainingClone) {
    //             ROOTTo.collection(training).add({ ...trainingClone[i], mid: event.id })
    //                 .then((trainingEvent) => {
    //                     // console.log('train added', i)
    //                     //CREATE Steps
    //                     for (let x in trainingStepsClone) {
    //                         if (trainingStepsClone[x].manual_id === trainingClone[i].id) {
    //                             ROOTTo.collection(steps).add({ ...trainingStepsClone[x], manual_id: trainingEvent.id }).then((e) => {
    //                                 // console.log('step added to clone from training', x)
    //                             })
    //                         }
    //                     }
    //                 })
    //         }
    //         //CREATE NEW NODES FOR FATLIST
    //         for (let i in fatListClone) {
    //             ROOTTo.collection('fatList').add({ ...fatListClone[i], mid: event.id })
    //                 .then((fEvent) => {
    //                     // console.log('fatList added', i)
    //                     //FAT Data from - Fat List Component
    //                     for (let x in fatDataClone) {
    //                         if (fatDataClone[x].fid === fatListClone[i].id) {
    //                             ROOTTo.collection('fatData').add({ ...fatDataClone[x], fid: fEvent.id, mid: event.id })
    //                                 .then((newEvent) => {
    //                                     // console.log('FAT Data added', x)
    //                                     var ROOT = db.collection(companies).doc(companyIdFrom);
    //                                     const tablesNameList = ['table', 'table1', 'table2', 'table3', 'table4',
    //                                         'tableIQ1', 'tableIQ2', 'tableIQ3', 'tableIQ4', 'tableIQ5', 'tableIQ6', 'tableIQ7', 'tableIQ8', 'tableIQ9', 'tableIQ10', 'tableIQ11', 'tableIQ12', 'tableIQ13', 'tableIQ14',
    //                                         'tableOQ1', 'tableOQ2', 'tableOQ3', 'tableOQ4', 'tableOQ5', 'tableOQ6', 'tableOQ7', 'tableOQ8', 'tableOQ9', 'tableOQ10', 'tableOQ11',
    //                                         'tableEQP1', 'tableEQP2', 'tableEQP3', 'tableEQP4', 'tableEQP5',
    //                                         'tableMFG1',
    //                                         'actionSheet']
    //                                     //Add table inside fat added
    //                                     // for (let y in fatTableClone) {
    //                                     //   if (fatTableClone[y].fatMainId === fatDataClone[x].id) {
    //                                     //     ROOT.collection('fatData').doc(newEvent.id).collection('table').add({ ...fatTableClone[y].tableData, createdAt: new Date() })
    //                                     //       .then(() => {
    //                                     //         // console.log('Fat table added', y)
    //                                     //       })
    //                                     //   }
    //                                     // }
    //                                     tablesNameList?.map((tableName) => {
    //                                         ROOT.collection('fatData').doc(fatDataClone[x].id).collection(tableName).onSnapshot(snap => {
    //                                             var data = firebaseLooper(snap);
    //                                             if (data?.length) {
    //                                                 data?.map((tData) => {
    //                                                     ROOTTo.collection('fatData').doc(newEvent?.id).collection(tableName).add({ ...tData, createdAt: new Date() })
    //                                                         .then(() => {
    //                                                             console.log('Fat table added: ', tableName)
    //                                                         })
    //                                                 })
    //                                             }
    //                                             else {
    //                                                 console.log("table not exist: ", tableName)
    //                                             }
    //                                         })
    //                                     })
    //                                 })
    //                         }
    //                     }
    //                 })
    //                 console.log("fatList length:",fatListClone?.length  )
    //                 if( i == fatListClone?.length - 1){
    //                     //alert("hi")
    //                      setTimeout(()=>{
    //                       setCloneComplete(true)
    //                      },2000)
    //                   }
    //         }
    //         //setCloneComplete(true)
    //     })
    // } else {
    //     ROOTTo.collection(machines).add(machineNew).then(() => {
    //         toastMessageSuccess({ message: "Machine Created successfully !" })
    //         history('/machines')
    //     })
    // }
  };

  return (
    <section className="addMachineForm">
      <div
        className="addMachineFormContainer"
        style={
          currentMode === "Dark"
            ? {
                backgroundColor: "#161C24",
                color: "white",
                border: "1px solid white",
              }
            : { border: "1px solid black" }
        }
      >
        <div
          style={{ display: "flex", justifyContent: "space-between" }}
          className="title"
        >
          <h3>New Machine</h3>
          <Box
            component="form"
            sx={{
              "& > :not(style)": { width: "20ch" },
            }}
          >
            <InputLabel id="demo-simple-select-label">
              {" "}
              Company From{" "}
            </InputLabel>
            <Select
              onChange={(e) => handleOnChangeCompanyFrom(e)}
              variant="outlined"
              size="small"
              fullWidth
              label=" Company From "
            >
              {companyList.map((data) => (
                <MenuItem key={data.id} value={data.id}>
                  {data.title}
                </MenuItem>
              ))}
            </Select>
          </Box>

          <Box
            component="form"
            sx={{
              "& > :not(style)": { width: "20ch" },
            }}
          >
            <InputLabel id="demo-simple-select-label">Company To</InputLabel>
            <Select
              onChange={(e) => handleOnChangeCompanyTo(e)}
              variant="outlined"
              size="small"
              fullWidth
              label="Company To"
            >
              {companyList.map((data) => (
                <MenuItem key={data.id} value={data.id}>
                  {data.title}
                </MenuItem>
              ))}
            </Select>
          </Box>

          <Box
            component="form"
            sx={{
              "& > :not(style)": { width: "20ch" },
            }}
          >
            <InputLabel id="demo-simple-select-label">
              Select Machine
            </InputLabel>
            <Select
              onChange={handledataClone}
              variant="outlined"
              size="small"
              fullWidth
              label="Select Machine"
            >
              {machinesDetails.map((data) => (
                <MenuItem key={data.id} value={data.id}>
                  {data.title}
                </MenuItem>
              ))}
            </Select>
          </Box>
        </div>
        <div className="desc">
          <p>Add information about new machine</p>
        </div>
        {/* onSubmit={() => setOpenCloneModule(true)} */}
        <form onSubmit={handleSubmit} className="machineFormContainer">
          <div className="flex justify-between">
            <div className="labelFields w-5/12">
              <InputLabel
                style={
                  currentMode === "Dark"
                    ? { color: "white", marginBottom: "4px", fontSize: "16px" }
                    : { marginBottom: "4px", fontSize: "16px" }
                }
                htmlFor="machineName"
              >
                Machine Name
              </InputLabel>
              <BootstrapInput
                onChange={(e) => setTitle(e.target.value)}
                onBlur={() => setTitle(title?.trim())}
                value={title}
                id="machineName"
                placeholder="Add machine name here ..."
                required
              />
            </div>
            <div className="labelFields w-5/12">
              <InputLabel
                style={
                  currentMode === "Dark"
                    ? { color: "white", marginBottom: "4px", fontSize: "16px" }
                    : { marginBottom: "4px", fontSize: "16px" }
                }
                htmlFor="equipment"
              >
                Equipment ID
              </InputLabel>
              <BootstrapInput
                onChange={(e) => setEquipmentId(e.target.value)}
                onBlur={() => setEquipmentId(equipmentId?.trim())}
                value={equipmentId}
                id="equipment"
                placeholder="Add equipment ID here ..."
                required
              />
            </div>
          </div>

          <div className="flex justify-between">
            <div className="labelFields w-5/12">
              <InputLabel
                style={
                  currentMode === "Dark"
                    ? { color: "white", marginBottom: "4px", fontSize: "16px" }
                    : { marginBottom: "4px", fontSize: "16px" }
                }
                htmlFor="serialNo"
              >
                Serial No.
              </InputLabel>
              <BootstrapInput
                onChange={(e) => setSerialNo(e.target.value)}
                onBlur={() => setSerialNo(serialNo?.trim())}
                value={serialNo}
                id="equipment"
                placeholder="Add serial no here ..."
                required
              />
            </div>
            <div className="labelFields w-5/12">
              <InputLabel
                style={
                  currentMode === "Dark"
                    ? { color: "white", marginBottom: "4px", fontSize: "16px" }
                    : { marginBottom: "4px", fontSize: "16px" }
                }
                htmlFor="model"
              >
                Model
              </InputLabel>
              <BootstrapInput
                onChange={(e) => setModel(e.target.value)}
                onBlur={() => setModel(model?.trim())}
                value={model}
                id="model"
                placeholder="Add model here ..."
                required
              />
            </div>
          </div>

          <div className="flex justify-between">
            <div className="labelFields w-5/12">
              <InputLabel
                style={
                  currentMode === "Dark"
                    ? { color: "white", marginBottom: "4px", fontSize: "16px" }
                    : { marginBottom: "4px", fontSize: "16px" }
                }
                htmlFor="block"
              >
                Block
              </InputLabel>
              <BootstrapInput
                onChange={(e) => setBlock(e.target.value)}
                onBlur={() => setBlock(block?.trim())}
                value={block}
                id="block"
                placeholder="Add block here ..."
                required
              />
            </div>

            <div className="labelFields w-5/12">
              <InputLabel
                style={
                  currentMode === "Dark"
                    ? { color: "white", marginBottom: "4px", fontSize: "16px" }
                    : { marginBottom: "4px", fontSize: "16px" }
                }
                htmlFor="machineLocation"
              >
                Machine Location
              </InputLabel>
              <BootstrapInput
                onChange={(e) => setLocation(e.target.value)}
                onBlur={(e) => setLocation(location?.trim())}
                value={location}
                id="machineLocation"
                placeholder="Write machine location here...."
                required
              />
            </div>
          </div>

          <div className="flex justify-between">
            <div className="labelFields w-5/12">
              <InputLabel
                style={
                  currentMode === "Dark"
                    ? { color: "white", marginBottom: "4px", fontSize: "16px" }
                    : { marginBottom: "4px", fontSize: "16px" }
                }
                htmlFor="warranty"
              >
                Warranty
              </InputLabel>
              <BootstrapInput
                onChange={(e) => setWarranty(e.target.value)}
                onBlur={() => setWarranty(warranty?.trim())}
                value={warranty}
                id="warranty"
                placeholder="Add warranty here ..."
                required
              />
            </div>
            <div className="labelFields w-5/12">
              <InputLabel
                style={
                  currentMode === "Dark"
                    ? { color: "white", marginBottom: "4px", fontSize: "16px" }
                    : { marginBottom: "4px", fontSize: "16px" }
                }
                htmlFor="status"
              >
                Status
              </InputLabel>
              <BootstrapInput
                onChange={(e) => setStatus(e.target.value)}
                onBlur={() => setStatus(status?.trim())}
                value={status}
                id="status"
                placeholder="Add status here ..."
                required
              />
            </div>
          </div>
          <div className="labelFields">
            <InputLabel
              style={
                currentMode === "Dark"
                  ? { color: "white", marginBottom: "4px", fontSize: "16px" }
                  : { marginBottom: "4px", fontSize: "16px" }
              }
              htmlFor="machineDesc"
            >
              Machine Description
            </InputLabel>
            <BootstrapInput
              onChange={(e) => setDesc(e.target.value)}
              onBlur={() => setDesc(desc?.trim())}
              value={desc}
              id="machineDesc"
              placeholder="Write details about machine here ..."
              required
            />
          </div>
          <div className=" mt-4 flex justify-between">
            <Link to="/machines">
              {/* <button className="cancelBtn btn">Cancel </button> */}
              <ButtonBasicCancel buttonTitle="Cancel" type="button" />
            </Link>
            {/* <button
              type="submit"
              className="createBtn btn"
              style={{ backgroundColor: currentColor }}
            >Create Machine</button> */}
            <ButtonBasic buttonTitle="Submit" type="submit" />
          </div>
        </form>
        {/* Confirm action on cloning the machine */}
        <Dialog open={wait} fullWidth>
          <DialogTitle>Please Wait .....</DialogTitle>
          <DialogContent>
            {!cloneComplete ? (
              <LinearProgress variant="indeterminate" />
            ) : (
              <>
                <Typography variant="body1">
                  Cloning of Data Completed
                </Typography>
                <div>
                  {/* <Button onClick={() => history('/machines')}>Continue</Button> */}
                  <ButtonBasic
                    onClick={() => setWait(false)}
                    buttonTitle="Continue"
                  ></ButtonBasic>
                </div>
              </>
            )}
            <br />
            <Alert
              variant="filled"
              severity={cloneComplete ? `success` : `warning`}
            >
              <AlertTitle>
                {cloneComplete
                  ? `Cloning has been completed . you may continue `
                  : `  Please Do not redirect while cloning. It may hamper the data cloning process`}
              </AlertTitle>
            </Alert>
          </DialogContent>
        </Dialog>

        {/* On confirm - Tick mark all the possible cloning */}
        <Dialog open={openClone} fullWidth>
          <DialogTitle>Select Modules you want to clone :</DialogTitle>
          <DialogContent>{newMachineId}</DialogContent>
        </Dialog>
      </div>
    </section>
  );
};

export default AddMachineFormLsi;
