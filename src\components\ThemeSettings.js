import React, { useState, useEffect } from "react";
import { MdOutlineCancel } from "react-icons/md";
import { BsCheck } from "react-icons/bs";
// import { TooltipComponent } from '@syncfusion/ej2-react-popups';
import { themeColors, themeColorsPremium } from "../constants/dummy";
import { useStateContext } from "../context/ContextProvider";
import { Button, Dialog, DialogContent, Tooltip } from "@mui/material";
import ServerConfigDialog from "../infrastructure/db/ServerConfigDialog";
import { useAuth } from "../hooks/AuthProvider";
import axios from "axios";
import { dbConfig } from "../infrastructure/db/db-config";
import { toastMessageSuccess, toastMessageError } from "../tools/toast";
const ThemeSettings = () => {
  const { setColor, setMode, currentMode, currentColor, setThemeSettings } =
    useStateContext();
  const [open, setOpen] = useState(false);
  const [user, setUser] = useState();
  const { currentUser } = useAuth();

  const getuserDetails = async () => {
    const id = currentUser._id;
    await axios.get(`${dbConfig.url}/users/${id}`).then((response) => {
      setUser(response.data?.data);
    });
  };

  const updateUserTheme = async (color) => {
    const updatedData = { themecolor: color, thememode: currentMode };
    const id = currentUser._id;
    await axios.put(`${dbConfig.url}/users/${id}`, updatedData).then(() => {
      toastMessageSuccess({
        message: "Theme Updated!",
      });
    });
  };
  useEffect(() => {
    getuserDetails();
  }, []);

  return (
    <div className="bg-half-transparent w-screen fixed nav-item top-0 right-0">
      <div className="float-right bg-gray-600 text-white h-screen dark:text-gray-200   dark:bg-[#484B52] w-400">
        <div className="flex justify-between items-center py-4 ml-1">
          <button
            type="button"
            onClick={() => setThemeSettings(false)}
            style={{
              color: currentMode === "Light" ? `rgb(153, 171, 180)` : "#aaaaaa",
              borderRadius: "50%",
            }}
            className="text-2xl p-3 hover:drop-shadow-xl hover:bg-slate-300"
          >
            <MdOutlineCancel />
          </button>
        </div>
        <div className="flex-col border-t-1 border-color p-4 ml-4">
          <p className="font-semibold text-xl ">Theme Option</p>

          <div className="mt-4">
            <input
              type="radio"
              id="light"
              name="theme"
              value="Light"
              className="cursor-pointer"
              onChange={(e) => setMode(e.target.value)}
              checked={currentMode === "Light"}
            />
            {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
            <label htmlFor="light" className="ml-2 text-md cursor-pointer">
              Light
            </label>
          </div>
          <div className="mt-2">
            <input
              type="radio"
              id="dark"
              name="theme"
              value="Dark"
              onChange={(e) => setMode(e.target.value)}
              className="cursor-pointer"
              checked={currentMode === "Dark"}
            />
            {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
            <label htmlFor="dark" className="ml-2 text-md cursor-pointer">
              Dark
            </label>
          </div>
        </div>
        <div className="p-4 border-t-1 border-color ml-4">
          <p className="font-semibold text-xl ">Theme Colors</p>
          <div className="flex gap-3">
            {themeColors.map((item, index) => (
              <Tooltip key={index} content={item.name} position="TopCenter">
                <div
                  className="relative mt-2 cursor-pointer flex gap-5 items-center"
                  key={item.name}
                >
                  <button
                    type="button"
                    className="h-10 w-10 rounded-full cursor-pointer"
                    style={{ backgroundColor: item.color }}
                    onClick={() => {
                      setColor(item.color);
                      updateUserTheme(item.color);
                    }}
                  >
                    <BsCheck
                      className={`ml-2 text-2xl text-white ${
                        item.color === currentColor ? "block" : "hidden"
                      }`}
                    />
                  </button>
                </div>
              </Tooltip>
            ))}
          </div>
        </div>
        <div className="p-4 border-t-1 border-color ml-4">
          <Button variant="contained" onClick={() => setOpen(true)}>
            Server setting
          </Button>
        </div>
      </div>
      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="sm">
        <DialogContent>
          <ServerConfigDialog />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ThemeSettings;
