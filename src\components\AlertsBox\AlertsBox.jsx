import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import NotificationsActiveIcon from "@mui/icons-material/NotificationsActive";
import {
  InputLabel,
  MenuItem,
  FormControl,
  Select,
  Badge,
  Typography,
  Button,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";

import { dbConfig } from "../../infrastructure/db/db-config";
import { useAuth } from "../../hooks/AuthProvider";
import { useCheckAccess } from "../../utils/useCheckAccess";
import { useStateContext } from "../../context/ContextProvider";
import { sharedCss } from "../../styles/sharedCss";
import NotificationItem from "./NotificationItem";
import NotAccessible from "../not-accessible/not-accessible";

const useStyles = makeStyles((theme) => ({
  notificationBoxHeading: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    paddingBottom: "2rem",
  },
  titleContainer: {
    display: "flex",
    flexDirection: "column",
  },
  titleText: {
    fontSize: "1.25rem",
    fontWeight: 500,
  },
  subtitleText: {
    fontSize: "0.75rem",
    color: "#333",
  },
  viewAllButton: {
    backgroundColor: theme.palette.mode === "dark" ? "#90caf9" : "#1976d2",
    color: "#fff",
    "&:hover": {
      backgroundColor: theme.palette.mode === "dark" ? "#64b5f6" : "#1565c0",
    },
  },
}));

const NotificationBox2 = () => {
  const [alerts, setAlerts] = useState([]);
  const [allMachines, setAllMachines] = useState([]);
  const [selectedMachine, setSelectedMachine] = useState([]);
  const [selectedMachineValue, setSelectedMachineValue] = useState("");
  const [machineHaveAlerts, setMachineHaveAlerts] = useState(true);
  const [allMachinesWithAlertsCount, setAllMachinesWithAlertsCount] = useState([]);

  const { currentUser } = useAuth();
  const hasGETAccess = useCheckAccess("alarms_new", "GET");
  const { currentColor } = useStateContext();
  const navigate = useNavigate();

  const classes = useStyles();
  const commonCss = sharedCss();

  const fetchMachineData = async () => {
    if (!currentUser) return;

    try {
      const machinesRes = await axios.get(`${dbConfig.url}/machines`);
      const allMachinesTemp = machinesRes.data?.data?.reverse() || [];
      setAllMachines(allMachinesTemp);

      const alertsRes = await axios.get(`${dbConfig.url}/alarms_new`);
      const alertTemp = alertsRes.data?.data || [];
      setAlerts(alertTemp);

      const counts = allMachinesTemp.map((machine) => {
        const count = alertTemp.filter((a) => a?.mid === machine?._id).length;
        return { ...machine, alertsCount: count };
      });

      setAllMachinesWithAlertsCount(counts.reverse());
    } catch (err) {
      console.error(err);
    }
  };

  useEffect(() => {
    fetchMachineData();
  }, [currentUser]);

  const handleChangeSelect = (event) => {
    const selected = event.target.value;
    setSelectedMachine([selected]);
    setSelectedMachineValue(selected);

    if (selected === "all") {
      setSelectedMachine([]);
    } else {
      const hasAlerts = alerts.some((alert) => alert?.mid === selected?._id);
      setMachineHaveAlerts(hasAlerts);
    }
  };

  const handleViewAll = () => {
    if (selectedMachineValue && selectedMachineValue !== "all") {
      navigate(`/alarmsnew/${selectedMachineValue._id}`);
    }
  };

  return (
    <div
      className={`notificationBoxContainer ${commonCss.backgroundLight} border-radius-inner`}
      style={{
        maxWidth: 420,
        width: "100%",
        padding: "2rem",
        boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
        minHeight: 380,
        boxSizing: "border-box",
        overflow: "hidden",
      }}
    >
      <div className={classes.notificationBoxHeading}>
        <div className={classes.titleContainer}>
          <Typography variant="h5">Alarms</Typography>
          <Typography variant="subtitle2">Some Alarms</Typography>
        </div>

        {selectedMachineValue && selectedMachineValue !== "all" && (
          <Button
            variant="contained"
            onClick={handleViewAll}
            className={classes.viewAllButton}
          >
            View All
          </Button>
        )}
      </div>

      {hasGETAccess ? (
        <>
          <div style={{ marginBottom: 24 }}>
            <FormControl
              size="small"
              fullWidth
              sx={{
                background: "#fff",
                borderRadius: "4px",
              }}
            >
              <InputLabel id="machine-select-label">Machine</InputLabel>
              <Select
                labelId="machine-select-label"
                id="machine-select"
                value={selectedMachineValue}
                label="Machine"
                onChange={handleChangeSelect}
                sx={{
                  background: (theme) =>
                    theme.palette.mode === "dark" ? "#23272b" : "#fafbfc",
                  borderRadius: "4px",
                }}
              >
                <MenuItem value="all">All Machines</MenuItem>
                {allMachinesWithAlertsCount?.map((data) => (
                  <MenuItem key={uuidv4()} value={data}>
                    {data?.title}
                    {data?.alertsCount > 0 && (
                      <Badge badgeContent={data.alertsCount} sx={{ marginLeft: 1 }}>
                        <NotificationsActiveIcon
                          className="text-red-500 ml-2"
                          fontSize="small"
                        />
                      </Badge>
                    )}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>

          <ul
            className="notificationBoxWrapper"
            style={{
              padding: 0,
              margin: 0,
              listStyle: "none",
              maxHeight: 320,
              overflowY: "auto",
              width: "100%",
              boxSizing: "border-box",
            }}
          >
            {selectedMachine?.length > 0 ? (
              machineHaveAlerts ? (
                selectedMachine.map((dataM) => (
                  <NotificationItem
                    key={uuidv4()}
                    dataM={dataM}
                    alerts={alerts}
                  />
                ))
              ) : (
                <div
                  className="flex justify-center mt-1"
                  style={{ padding: 24, color: "#888" }}
                >
                  No Active Alarms
                </div>
              )
            ) : (
              allMachines.map((dataM) => (
                <NotificationItem
                  key={uuidv4()}
                  dataM={dataM}
                  alerts={alerts?.length > 5 ? alerts.slice(0, 5) : alerts}
                />
              ))
            )}
          </ul>
        </>
      ) : (
        <NotAccessible />
      )}
    </div>
  );
};

export default NotificationBox2;
