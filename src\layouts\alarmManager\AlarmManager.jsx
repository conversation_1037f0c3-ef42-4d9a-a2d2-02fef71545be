// Notes: needs optimisation and simplification of the codes of this component

import React, { useEffect, useState } from "react";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import { db } from "../../firebase";
import {
  companies,
  companyId_constant,
  lyoAlarm,
  machines,
  maintenance,
} from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";
import { toastMessageSuccess, toastMessageWarning } from "../../tools/toast";
import AlarmManagerCard from "./AlarmManagerCard";
import { Pagination } from "@mui/material";
import {
  TextField,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Box,
  InputLabel,
  MenuItem,
  FormControl,
  Select,
  Paper,
  CircularProgress,
  Typography,
} from "@mui/material";
import InputAdornment from "@mui/material/InputAdornment";
import { SearchOutlined } from "@mui/icons-material";
import PageHeader from "../../components/commons/page-header.component";
import { makeStyles } from "@mui/styles";
import { sharedCss } from "../../styles/sharedCss";

const useCustomStyles = makeStyles((theme) => ({
  machineContainer: {
    padding: "1rem",

    borderRadius: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  machinesOuterContainer: {
    width: "100%",
  },
  machinesInnerContainer: {
    display: "flex",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  addButton: {
    width: "max-content",
  },
  machinePageContainer: {
    padding: "1rem",
    border: "1px solid gainsboro",
  },
}));

export default function AlarmManager() {
  const { currentMode, currentColorLight } = useStateContext();
  // const dbrefAlarm = db
  //   .collection(companies)
  //   .doc(companyId_constant)
  //   .collection(lyoAlarm)
  //   .doc("alarm");
  // const dbrefMaintenance = db
  //   .collection(companies)
  //   .doc(companyId_constant)
  //   .collection(maintenance);
  // const dbrefAlarmsCollection = db
  //   .collection(companies)
  //   .doc(companyId_constant)
  //   .collection("Alarms");
  // const dbrefMachines = db
  //   .collection(companies)
  //   .doc(companyId_constant)
  //   .collection(machines);
  const [open, setOpen] = useState(false);
  const [alarmSelected, setAlarmSelected] = useState("");
  const [alarmData, setAlarmData] = useState([]);
  // alarms which are already added will not available for selection
  const [alarmUnique, setAlarmUnique] = useState([]);
  const [maintenanceData, setMaintenanceData] = useState([]);
  const [maintenanceSelected, setMaintenanceSelected] = useState("");
  const [machineData, setMachineData] = useState([]);
  const [machineSelected, setMachineSelected] = useState("");
  const [searchKeyWordAlarm, setSearchKeyWordAlarm] = useState("");
  const [alarmWithMaintenance, setAlarmWithMainenance] = useState([]);
  const [contentLoading, setContentLoading] = useState(true);
  //New variables created for pagination
  const [AlarmsInViewContainerCount, setAlarmsInViewContainerCount] =
    useState(0);
  const [page, setPage] = useState(1);
  const [numberOfAlarmsInView, setNumberOfAlarmsInView] = useState(6);
  const [alarms, setAlarms] = React.useState([]);
  const [alarmsInView, setAlarmsInView] = useState([]);
  //end
  const customCss = useCustomStyles();

  useEffect(() => {
    //    //alert("useefect")
    //     // for implementing pagination
    //     window.localStorage.clear(); // this will run for the first time
    //     dbrefAlarmsCollection.onSnapshot((snap) => {   // this will run on add , update , delete
    //         const data = firebaseLooper(snap);
    //         setAlarmsInView(data.slice(0, numberOfAlarmsInView));
    //         // window.localStorage.clear();
    //         let alarmsActivePageNum = window.localStorage.getItem('alarmsActivePageNum'); // for preventing pagination mismatch while editing 2/3
    //         let numOfAlarmsInViewSession = window.localStorage.getItem('numOfAlarmsInView');//
    //         setAlarms(data); // data?.reverse()
    //         alarmsPageInfoContainerCounter(data.length, numberOfAlarmsInView)
    //         // for preventing pagination mismatch while editing 3/3
    //         if (alarmsActivePageNum != 1 && alarmsActivePageNum) {
    //             let machineInViewContainerCountTemp = alarmsPageInfoContainerCounter(data.length, numOfAlarmsInViewSession); //if it is last page
    //             if (alarmsActivePageNum == machineInViewContainerCountTemp) {
    //                 setAlarmsInView(data.slice(((alarmsActivePageNum - 1) * numOfAlarmsInViewSession)));
    //                 setNumberOfAlarmsInView(numOfAlarmsInViewSession);
    //                 setPage(alarmsActivePageNum);
    //                 setNumberOfAlarmsInView(numOfAlarmsInViewSession);
    //                 //alert("useefect2")
    //             }
    //             else { // if it's not the last page
    //                 setAlarmsInView(data.slice(((alarmsActivePageNum - 1) * numOfAlarmsInViewSession), ((alarmsActivePageNum) * numOfAlarmsInViewSession)));
    //                 setNumberOfAlarmsInView(numOfAlarmsInViewSession);
    //                 setPage(alarmsActivePageNum);
    //                 setNumberOfAlarmsInView(numOfAlarmsInViewSession);
    //                 //alert("useefect3")
    //             }
    //         }
    //     });
    //     //end
    //     let alarmPredefinedTemp = [];
    //     dbrefAlarm.get().then((doc) => {
    //         let temp = [];
    //         //console.log("alrms:", Object.keys(doc.data()).length, doc.data());
    //         for (const keys in Object.keys(doc.data())) {
    //             temp = [...temp, doc.data()[keys]]
    //         }
    //         // for (let i = 0; i < Object.keys(doc.data()).length; i++) {
    //         //     temp.push(doc.data()[i]);
    //         //     alarmPredefinedTemp.push(doc.data()[i]);//
    //         // }
    //         alarmPredefinedTemp = [...temp]
    //         //console.log(temp)
    //         setAlarmData([...temp]);
    //     }).then(() => {
    //         dbrefAlarmsCollection.onSnapshot(snap => {  // WARNING: this shold not called twice
    //             const data = firebaseLooper(snap);
    //             const alarmUniqueTemp = data.map((dataM) => dataM.name);
    //             const alarmUniqueTemp2 = alarmPredefinedTemp.map((dataM) => dataM.name);
    //             const newAlarmUnique = alarmUniqueTemp2.filter((dataF) => !alarmUniqueTemp.includes(dataF));
    //            // setAlarmWithMainenance(data);
    //             //setAlarmsInView(data.slice(0, numberOfAlarmsInView)); // lin 70
    //             setAlarmUnique(newAlarmUnique);
    //             setContentLoading(false);
    //         })
    //     })
    //         .catch((e) => console.log("alarms:", e));
    //     dbrefMaintenance.onSnapshot(snap => {
    //         const data = firebaseLooper(snap);
    //         setMaintenanceData(data?.reverse());
    //     })
    //     dbrefMachines.onSnapshot(snap => {
    //         const data = firebaseLooper(snap);
    //         setMachineData(data?.reverse());
    //     })
  }, []);
  //For pagination
  const handleNumberOfAlarmsInView = (e) => {
    window.localStorage.setItem("numOfAlarmsInView", e.target.value); //for preventing pagination mismatch while editing 1.1/3
    setNumberOfAlarmsInView(e.target.value);
    alarmsPageInfoContainerCounter(alarms?.length, e.target.value);
    setAlarmsInView(alarms.slice(0, e.target.value));
  };

  //
  const alarmsPageInfoContainerCounter = (
    allAlarmsCount,
    numberOfAlarmsInViewProp,
  ) => {
    var temp = Math.ceil(allAlarmsCount / numberOfAlarmsInViewProp);
    setAlarmsInViewContainerCount(temp);
    return temp;
  };
  //
  const handlePageChange = (event, value) => {
    window.localStorage.setItem("alarmsActivePageNum", value); // for preventing pagination mismatch while editing 1/3
    window.localStorage.setItem("numOfAlarmsInView", numberOfAlarmsInView); //
    setPage(value);

    if (alarmWithMaintenance.length > 0) {
      if (value === AlarmsInViewContainerCount) {
        setAlarmsInView(
          alarmWithMaintenance.slice((value - 1) * numberOfAlarmsInView),
        );
      } else {
        setAlarmsInView(
          alarmWithMaintenance.slice(
            (value - 1) * numberOfAlarmsInView,
            value * numberOfAlarmsInView,
          ),
        );
      }
    } else {
      if (value === AlarmsInViewContainerCount) {
        setAlarmsInView(alarms.slice((value - 1) * numberOfAlarmsInView));
      } else {
        setAlarmsInView(
          alarms.slice(
            (value - 1) * numberOfAlarmsInView,
            value * numberOfAlarmsInView,
          ),
        );
      }
    }
  };
  //end

  const handleChangeAlarm = (event) => {
    setAlarmSelected(event.target.value);
  };

  const handleChangeMaintenance = (event) => {
    setMaintenanceSelected(event.target.value);
  };

  const handleChangeMachine = (event) => {
    setMachineSelected(event.target.value);
  };

  const handleAddAlarm = (data) => {
    // dbrefAlarmsCollection
    //   .add({
    //     name: alarmSelected,
    //     maintenanceId: maintenanceSelected,
    //     mid: machineSelected,
    //   })
    //   .then(() => {
    //     toastMessageSuccess({ message: "added successfully" });
    //     setMachineSelected("");
    //     setAlarmSelected("");
    //     setMaintenanceSelected("");
    //     setOpen(false);
    //   })
    //   .catch((e) => toastMessageWarning({ message: e }));
  };

  const handleClickOpen = (name) => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const theme = {
    darkCell: {
      padding: "20px 10px",
      color: "white",
      borderBottom: "1px solid white",
      fontSize: 18,
    },
    lightCell: {
      padding: "20px 10px",
      borderBottom: "1px solid #000",
      fontSize: 18,
    },
    dark: { backgroundColor: "#161C24", color: "white", padding: "0px" },
    light: { backgroundColor: currentColorLight, padding: "0px" },
  };
  const commonCss = sharedCss();

  return (
    <>
      <section className={customCss.machinePageContainer}>
        <header className={commonCss?.headingContainer}>
          <Box>
            <Typography variant="h4">Alarm Manager</Typography>
            <Typography variant="subtitle1">
              Manage All Alarms & Details
            </Typography>
          </Box>

          {/* <PageHeader
								title='Alarm Manager'
								subText='Manage All Alarms & Details'
							/> */}

          <Box
            style={{
              display: "flex",
              flexDirection: "row",
              gap: "1rem",
              placeItems: "center",
            }}
          >
            <Box
              component="form"
              sx={{
                "& > :not(style)": { width: "35ch" },
              }}
            >
              <TextField
                size="small"
                className={commonCss.searchBox}
                label="Alarm"
                type="text"
                value={searchKeyWordAlarm}
                onChange={(e) => setSearchKeyWordAlarm(e.target.value)}
                placeholder="Alarm..."
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchOutlined />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            <div className="flex justify-center content-center mr-6 pl-2">
              <ButtonBasic
                buttonTitle="+ Add Alarm"
                onClick={handleClickOpen}
              />
            </div>
          </Box>
        </header>
        <main
          className={commonCss?.generalBackground}
          style={{ padding: "1rem" }}
        >
          <TableContainer
            component={Paper}
            className="tableContainer"
            style={{
              border:
                currentMode === "Dark" ? "1px solid #fff" : "1px solid #000",
            }}
          >
            <Table className="insideTable">
              <TableHead>
                <TableRow>
                  <TableCell align="left">Name</TableCell>
                  <TableCell align="left">Machine</TableCell>
                  <TableCell align="left">Maintenance</TableCell>
                  <TableCell align="left">Action</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {contentLoading && (
                  <TableCell colSpan="4" align="center">
                    <CircularProgress />
                  </TableCell>
                )}
                {alarmsInView
                  ?.filter(
                    (dataF) =>
                      dataF.name
                        ?.toUpperCase()
                        .search(searchKeyWordAlarm.toUpperCase()) >= 0,
                  )
                  .map((data, index) => (
                    <AlarmManagerCard
                      key={index}
                      data={data}
                      maintenanceData={maintenanceData}
                      machineData={machineData}
                      page={page}
                    />
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
          <div
            className="mt-6"
            style={{ display: "flex", justifyContent: "center" }}
          >
            <Pagination
              count={AlarmsInViewContainerCount}
              page={page}
              onChange={handlePageChange}
              color="primary"
            />
          </div>
        </main>

        <Dialog open={open} onClose={handleClose} maxWidth="xl">
          <DialogTitle>Alarm Manager</DialogTitle>
          <DialogContent>
            <DialogContentText>
              <div className="flex justify-between py-2">
                <div className="px-2">
                  <Box sx={{ minWidth: 120 }}>
                    <FormControl fullWidth>
                      <InputLabel id="demo-simple-select-label">
                        Alarms
                      </InputLabel>
                      <Select
                        labelId="demo-simple-select-label"
                        id="demo-simple-select"
                        value={alarmSelected}
                        label="Alarms"
                        onChange={handleChangeAlarm}
                      >
                        {alarmUnique?.map((data, index) => (
                          <MenuItem key={index} value={data}>
                            {data}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                </div>
                <div className="px-2">
                  <Box sx={{ minWidth: 150 }}>
                    <FormControl fullWidth>
                      <InputLabel id="demo-simple-select-label">
                        Machine
                      </InputLabel>
                      <Select
                        labelId="demo-simple-select-label"
                        id="demo-simple-select"
                        value={machineSelected}
                        label="Machine"
                        onChange={handleChangeMachine}
                      >
                        {machineData?.map((data, index) => (
                          <MenuItem key={index} value={data.id}>
                            {data.title}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                </div>

                <div className="px-2">
                  <Box sx={{ minWidth: 150 }}>
                    <FormControl fullWidth>
                      <InputLabel id="demo-simple-select-label">
                        Maintenance
                      </InputLabel>
                      <Select
                        labelId="demo-simple-select-label"
                        id="demo-simple-select"
                        value={maintenanceSelected}
                        label="MaintenanceData"
                        onChange={handleChangeMaintenance}
                      >
                        {maintenanceData
                          ?.filter((mData) => mData?.mid === machineSelected)
                          ?.map((data, index) => (
                            <MenuItem key={index} value={data.id}>
                              {data.title}
                            </MenuItem>
                          ))}
                      </Select>
                    </FormControl>
                  </Box>
                </div>
              </div>
            </DialogContentText>
            {/* <div>bbbbb</div> */}
          </DialogContent>
          <DialogActions>
            <div className="flex justify-between w-full px-6">
              <ButtonBasic buttonTitle="Cancel" onClick={handleClose} />
              <ButtonBasic buttonTitle=" + Add" onClick={handleAddAlarm} />
            </div>
          </DialogActions>
        </Dialog>
      </section>
    </>
  );
}
