import React from "react";
import { Button, Menu, styled, Box } from "@mui/material";
import { alpha } from "@mui/material/styles";
import { useStateContext } from "../../context/ContextProvider";
import { ButtonBasicCancel } from "../buttons/Buttons";

const StyledMenu = styled((props) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: "bottom",
      horizontal: "left",
    }}
    transformOrigin={{
      vertical: "top",
      horizontal: "left",
    }}
    {...props}
  />
))(({ theme }) => ({
  "& .MuiPaper-root": {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 300, // Adjust for DateRangePicker width
    color:
      theme.palette.mode === "light"
        ? "rgb(55, 65, 81)"
        : theme.palette.grey[700],
    boxShadow:
      "rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px",
    "& .MuiMenu-list": {
      padding: "4px 0",
    },
    "& .MuiMenuItem-root": {
      "& .MuiSvgIcon-root": {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: theme.spacing(1.5),
      },
      "&:active": {
        backgroundColor: alpha(
          theme.palette.primary.main,
          theme.palette.action.selectedOpacity,
        ),
      },
    },
  },
}));

const DateRangeMenu = ({
  displayText,
  items,
  textShadow,
  color,
  fontSize,
  resetDateFilter,
}) => {
  const { currentMode } = useStateContext();
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleClear = () => {
    resetDateFilter(); // Reset the date filter
    handleClose(); // Close the menu
  };

  return (
    <div>
      <Button
        onClick={handleClick}
        sx={{
          textShadow,
          color: color || (currentMode === "Dark" ? "#fff" : "#000"),
          fontSize: fontSize || "0.75rem",
          textTransform: "none",
          border: "1px solid #ccc",
          borderRadius: "8px",
          backgroundColor: currentMode === "Dark" ? "#2a2a2a" : "#fff",
          padding: "6px 16px",
          "&:hover": {
            backgroundColor: currentMode === "Dark" ? "#333" : "#f5f5f5",
          },
        }}
      >
        {displayText}
      </Button>
      <StyledMenu
        id="date-range-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "date-range-button",
        }}
      >
        <Box sx={{ padding: "8px" }}>
          {items}
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              marginTop: "8px",
            }}
          >
            <ButtonBasicCancel
              onClick={handleClear}
              buttonTitle="CLEAR"
              type="button"
            >
              Clear
            </ButtonBasicCancel>
          </Box>
        </Box>
      </StyledMenu>
    </div>
  );
};

export default DateRangeMenu;
