export const numberFields = [
  "pack_style",
  "running_rpm",
  "optimal_speed",
  "quality",
  "qty_per_shipper",
  "total_shift_time",
  "actual_minutes",
  "actual_run_hrs",
  "total_downtime",
  "target",
  "output_as_per_optimal_speed",
  "availability",
  "performance",
  "oee_percentage",
  "start_shipper_nos",
  "end_shipper_nos",
  "shift_instance",
  "casual_manpower",
  "start_time",
  "end_time",
  "time_taken",
];

export const autoFilledFields = [
  "date",
  "pack_style",
  "running_rpm",
  "optimal_speed",
  "quality",
  "qty_per_shipper",
  "total_shift_time",
  "actual_minutes",
  // "actual_run_hrs",
  "total_downtime",
  "target",
  "output_as_per_optimal_speed",
  "availability",
  "performance",
  "oee_percentage",
  "time_taken",
];

export const dropdownKeys = [
  "shift",
  "line",
  "product_name",
  "reason_category",
  "operator",
  "executive",
  "incharge",
];

export const sections = [
  {
    title: "General Information",
    fields: ["start_date", "shift", "line", "product_name", "batch_number"],
  },
  {
    title: "Shift Details",
    fields: [
      "pack_style",
      "running_rpm",
      "total_shift_time",
      "actual_run_hrs",
      "total_downtime",
      "target",
      "start_shipper_nos",
      "end_shipper_nos",
      "qty_per_shipper",
      "actual_minutes",
      "shift_instance",
    ],
  },
  {
    title: "Personnel Details",
    fields: ["operator", "executive", "incharge", "casual_manpower"],
  },
  {
    title: "Additional Details",
    fields: ["reason", "concatenate"],
  },
  {
    title: "Performance Metrics",
    fields: [
      "optimal_speed",
      "output_as_per_optimal_speed",
      "availability",
      "performance",
      "quality",
      "oee_percentage",
    ],
  },
];
