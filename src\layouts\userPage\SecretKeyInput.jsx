import React, { useState, useEffect } from "react";
import { TextField, IconButton, InputAdornment, Button } from "@mui/material";
import {
  Visibility,
  VisibilityOff,
  CheckCircleOutline,
} from "@mui/icons-material";
import PropTypes from "prop-types";

const SecretKeyInput = ({ secretKey, setSecretKey, verifyQA }) => {
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    // Clear the secret key value when the component mounts
    setSecretKey("");
  }, [setSecretKey]);

  const handleToggleVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const handleVerifyClick = (e) => {
    e.preventDefault();
    verifyQA(secretKey);
  };

  return (
    <TextField
      label="Add Key"
      size="small"
      fullWidth
      required
      name="unique-secret-key"
      autoComplete="new-password"
      type={showPassword ? "text" : "password"}
      value={secretKey}
      onChange={(e) => {
        e.preventDefault();
        setSecretKey(e.target.value.trimStart());
      }}
      InputProps={{
        endAdornment: (
          <InputAdornment position="end">
            <IconButton
              onClick={handleToggleVisibility}
              onMouseDown={handleMouseDownPassword}
              edge="end"
            >
              {showPassword ? <VisibilityOff /> : <Visibility />}
            </IconButton>
            <IconButton
              onClick={handleVerifyClick}
              edge="end"
              disabled={secretKey.length < 8}
            >
              <CheckCircleOutline />
            </IconButton>
          </InputAdornment>
        ),
        onCopy: (e) => e.preventDefault(),
        onPaste: (e) => e.preventDefault(),
      }}
    />
  );
};

SecretKeyInput.propTypes = {
  secretKey: PropTypes.string.isRequired, // The current value of the secret key input field
  setSecretKey: PropTypes.func.isRequired, // Function to update the secret key value
  verifyQA: PropTypes.func.isRequired, // Function to verify the secret key
};

export default SecretKeyInput;
