import React, { useState, useEffect } from "react";
import axios from "axios";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Collapse,
  IconButton,
  Box,
  Typography,
} from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import { dbConfig } from "../../infrastructure/db/db-config";
import TimelineReports from "../machines/TimelineViewReports";
import NoDataComponent from "../../components/commons/noData.component";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";

const TimelineComponent = ({ machineId }) => {
  const [data, setData] = useState([]);
  const [open, setOpen] = useState({});

  const hasOeeReportGETAccess = useCheckAccess("oeeReports", "GET");

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(`${dbConfig.url}/oeeReports`);
        setData(response.data);
      } catch (error) {
        console.error("Failed to fetch OEE reports:", error);
      }
    };

    fetchData();
  }, []);

  const handleOpen = (id) => {
    setOpen((prevOpen) => ({
      ...prevOpen,
      [id]: !prevOpen[id],
    }));
  };

  const filteredDataByMachineId = data?.filter(
    (timelineData) => timelineData?.mid === machineId || machineId === "All",
  );

  return (
    <TableContainer elevation={1} sx={{ mt: 4 }} component={Paper}>
      {hasOeeReportGETAccess ? (
        <Table aria-label="collapsible table">
          <TableHead>
            <TableRow>
              <TableCell />
              <TableCell>MID</TableCell>
              <TableCell align="right">Report Date</TableCell>
              <TableCell align="right">Shift Info</TableCell>
              <TableCell align="right">Run Time</TableCell>
              <TableCell align="right">Planned Production Time</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredDataByMachineId.map((row, index) => (
              <React.Fragment key={index}>
                <TableRow sx={{ "& > *": { borderBottom: "unset" } }}>
                  <TableCell>
                    <IconButton
                      aria-label="expand row"
                      size="small"
                      onClick={() => handleOpen(index)}
                    >
                      {open[index] ? (
                        <KeyboardArrowUpIcon />
                      ) : (
                        <KeyboardArrowDownIcon />
                      )}
                    </IconButton>
                  </TableCell>
                  <TableCell component="th" scope="row">
                    {row.mid}
                  </TableCell>
                  <TableCell align="right">{row.reportDate}</TableCell>
                  <TableCell align="right">{`Shift #${row.shiftInfo?.shiftNumber}`}</TableCell>
                  <TableCell align="right">{row.runTime}</TableCell>
                  <TableCell align="right">
                    {row.plannedProductionTime}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell
                    style={{ paddingBottom: 0, paddingTop: 0 }}
                    colSpan={6}
                  >
                    <Collapse in={open[index]} timeout="auto" unmountOnExit>
                      <Box sx={{ margin: 1 }}>
                        <TimelineReports oeeData={row} />
                      </Box>
                    </Collapse>
                  </TableCell>
                </TableRow>
              </React.Fragment>
            ))}
            {!filteredDataByMachineId.length && (
              <>
                {/*
            <TableRow>
              <TableCell
                colSpan={6}
                sx={{ textTransform: "capitalize" }}
                style={{ padding: "24px" }}
              >
                <div className="flex justify-center  w-full content-center animate-pulse">
                  No Data
                </div>
              </TableCell>
            </TableRow>
          */}
                <NoDataComponent cellColSpan={6} paddText={true} />
              </>
            )}
          </TableBody>
        </Table>
      ) : (
        <NotAccessible />
      )}
    </TableContainer>
  );
};

export default TimelineComponent;
