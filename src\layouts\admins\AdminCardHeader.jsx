import { TableCell, TableHead, TableRow } from "@mui/material";
import React from "react";
import { useStateContext } from "../../context/ContextProvider";

export default function AdminCardHeader() {
  const { currentMode, currentColorLight } = useStateContext();

  return (
    <TableHead
      style={
        currentMode === "Dark"
          ? { border: "1px solid red", backgroundColor: "#212B36" }
          : { backgroundColor: "" }
      }
    >
      <TableRow>
        <TableCell
          style={{ padding: "20px 10px" }}
          sx={
            currentMode === "Dark"
              ? {
                  color: "white",
                  borderBottom: "1px solid white",
                  fontSize: 18,
                }
              : { fontSize: 18 }
          }
          align="left"
          className="self-center w-2/12"
        >
          Picture
        </TableCell>
        <TableCell
          style={{ padding: "20px 10px" }}
          sx={
            currentMode === "Dark"
              ? {
                  color: "white",
                  borderBottom: "1px solid white",
                  fontSize: 18,
                }
              : { fontSize: 18 }
          }
          align="left"
          className="self-center w-2/12"
        >
          Name
        </TableCell>
        <TableCell
          style={{ padding: "20px 10px" }}
          sx={
            currentMode === "Dark"
              ? {
                  color: "white",
                  borderBottom: "1px solid white",
                  fontSize: 18,
                }
              : { fontSize: 18 }
          }
          align="left"
          className="self-center w-2/12"
        >
          Admin Name
        </TableCell>
        <TableCell
          style={{ padding: "20px 10px" }}
          sx={
            currentMode === "Dark"
              ? {
                  color: "white",
                  borderBottom: "1px solid white",
                  fontSize: 18,
                }
              : { fontSize: 18 }
          }
          align="left"
          className="self-center w-2/12"
        >
          Contact
        </TableCell>
        <TableCell
          style={{ padding: "20px 10px" }}
          sx={
            currentMode === "Dark"
              ? {
                  color: "white",
                  borderBottom: "1px solid white",
                  fontSize: 18,
                }
              : { fontSize: 18 }
          }
          align="left"
          className="self-center w-2/12"
        >
          Email
        </TableCell>
        <TableCell
          style={{ padding: "20px 10px" }}
          sx={
            currentMode === "Dark"
              ? {
                  color: "white",
                  borderBottom: "1px solid white",
                  fontSize: 18,
                }
              : { fontSize: 18 }
          }
          align="left"
          className="self-center w-2/12"
        >
          {" "}
          Action
        </TableCell>
      </TableRow>
    </TableHead>
  );
}
