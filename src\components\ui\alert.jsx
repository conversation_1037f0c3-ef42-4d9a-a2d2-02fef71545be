import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>A<PERSON>,
  <PERSON>alogContent,
  <PERSON>alog<PERSON><PERSON>le,
  <PERSON>rid,
  Text<PERSON>ield,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { dbUrl } from "../../constants/db";
import axios from "axios";
import SecretKeyInput from "./SecretKeyInput";
import { toast } from "react-toastify";
import PropTypes from "prop-types";

// Constants for hardcoded strings
const QA_SUCCESS_MESSAGE = "QA Key verified successfully";
const QA_ERROR_PREFIX = "QA Verification Failed ";
const QNN_LABEL = "Quality Notification Number";
const REMARK_LABEL = "Remark";
const ALIGNMENT_HELPER_TEXT = "To Fix the alignment.";
const YES_BUTTON_LABEL = "Yes";
const NO_BUTTON_LABEL = "No";
const ADD_KEY_LABEL = "Add Key";
const MODAL_DEFAULT_DESC = "field";

const AlertModal = ({
  open = false,
  setOpen = () => {},
  action = () => {},
  desc = MODAL_DEFAULT_DESC,
  pending = false,
  qnn = " ",
  setQnn = () => {},
  remark = " ",
  setRemark = () => {},
  hideQnn = false,
}) => {
  const [secretKey, setSecretKey] = useState("");
  const [verified, setVerified] = useState(false);

  const resetForm = () => {
    setQnn("");
    setRemark("");
    setSecretKey("");
    setVerified(false);
  };

  useEffect(() => {
    resetForm();
  }, [open]);

  const verifyQA = async (value) => {
    if (value.length >= 8) {
      axios
        .post(`${dbUrl}/auth/qa`, {
          key: value,
        })
        .then((res) => {
          toast.success(QA_SUCCESS_MESSAGE);
          setVerified(true);
        })
        .catch((err) => {
          toast.error(
            `${(err.response && err.response.data.message) ?? QA_ERROR_PREFIX + err.message}`,
          );
          setVerified(false);
        });
    } else {
      setVerified(false);
    }
  };

  return (
    <Dialog open={open} maxWidth="xs">
      <DialogTitle>{desc}</DialogTitle>
      <DialogContent>
        {!hideQnn && (
          <Grid item xs={12} sm={12} md={12} mt={1}>
            <TextField
              size="small"
              fullWidth
              required
              value={qnn}
              onChange={(e) => {
                setQnn(e.target.value);
              }}
              label={QNN_LABEL}
              helperText={
                <span style={{ color: "transparent", visibility: "hidden" }}>
                  {ALIGNMENT_HELPER_TEXT}
                </span>
              }
            />
          </Grid>
        )}
        <Grid item xs={12} sm={12} md={12} mt={1}>
          <TextField
            size="small"
            fullWidth
            value={remark}
            onChange={(e) => {
              setRemark(e.target.value);
            }}
            label={REMARK_LABEL}
            helperText={
              <span style={{ color: "transparent", visibility: "hidden" }}>
                {ALIGNMENT_HELPER_TEXT}
              </span>
            }
          />
        </Grid>
        <Grid item xs={12} sm={12} md={12} mt={1}>
          <SecretKeyInput
            secretKey={secretKey}
            setSecretKey={setSecretKey}
            verifyQA={verifyQA}
          />
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            action(remark, qnn);
            setSecretKey("");
          }}
          color="warning"
          disabled={pending || qnn === null || remark === null || !verified}
        >
          {YES_BUTTON_LABEL}
        </Button>
        <Button
          onClick={() => {
            setOpen(false);
            setSecretKey("");
          }}
          disabled={pending}
        >
          {NO_BUTTON_LABEL}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

AlertModal.propTypes = {
  open: PropTypes.bool, // Whether the modal is open
  setOpen: PropTypes.func, // Function to toggle modal visibility
  action: PropTypes.func, // Function to perform on action (Yes button click)
  desc: PropTypes.string, // Description or title of the modal
  pending: PropTypes.bool, // Whether the action is pending/loading
  qnn: PropTypes.string, // Quality Notification Number
  setQnn: PropTypes.func, // Function to update QNN value
  remark: PropTypes.string, // Remark field value
  setRemark: PropTypes.func, // Function to update remark value
  hideQnn: PropTypes.bool, // Whether to hide the QNN field
};

export default AlertModal;
