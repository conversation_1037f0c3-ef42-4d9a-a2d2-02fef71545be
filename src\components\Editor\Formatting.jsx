const Formatting = ({ data }) => {
  if (data?.type === "header") {
    return <h1 className="text-lg font-bold capitalize">{data?.data?.text}</h1>;
  } else if (data?.type === "list") {
    if (data?.data?.style === "ordered") {
      return (
        <>
          <ol className="pl-5">
            {data?.data?.items?.map((options, index) => (
              <li key={options + index}>{`${index + +1}. ${options}`}</li>
            ))}
          </ol>
        </>
      );
    } else {
      return (
        <ul className="pl-5">
          {data?.data?.items?.map((options, index) => (
            <li key={options + index}>{options}</li>
          ))}
        </ul>
      );
    }
  } else if (data?.type === "paragraph") {
    return (
      <p
        style={{ marginBlock: ".2rem" }}
        dangerouslySetInnerHTML={{ __html: data?.data?.text }}
      ></p>
    );
  } else {
    return <h1>Invalid Data Type</h1>;
  }
};

export default Formatting;
