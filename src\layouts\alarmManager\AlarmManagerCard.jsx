import React, { useState, useEffect } from "react";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import {
  Box,
  InputLabel,
  MenuItem,
  Select,
  FormControl,
  TableRow,
  TableCell,
} from "@mui/material";
import { db } from "../../firebase";
import {
  companies,
  companyId_constant,
  machines,
  maintenance,
} from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";
import { toastMessageSuccess, toastMessageWarning } from "../../tools/toast";
import { CancelOutlined } from "@mui/icons-material";

export default function AlarmManagerCard({
  data,
  maintenanceData,
  machineData,
  page,
}) {
  const { currentMode } = useStateContext();
  //const dbrefMaintenance = db.collection(companies).doc(companyId_constant).collection(maintenance);
  //const dbrefAlarmsCollection = db.collection(companies).doc(companyId_constant).collection("Alarms");
  //const dbrefMachines = db.collection(companies).doc(companyId_constant).collection(machines);
  // const [maintenanceData, setMaintenanceData] = useState([]);
  const [maintenanceSelected, setMaintenanceSelected] = useState("");
  //const [machineData, setMachineData] = useState([]);
  const [machineSelected, setMachineSelected] = useState("");
  const [updateMode, setUpdateMode] = useState(false);

  useEffect(() => {
    // dbrefMaintenance.onSnapshot(snap => {
    //     const data = firebaseLooper(snap);
    //     setMaintenanceData(data?.reverse());
    // })

    // dbrefMachines.onSnapshot(snap => {
    //     const data = firebaseLooper(snap);
    //     setMachineData(data?.reverse());
    // })
    setUpdateMode(false);
  }, [page]);

  //

  const handleChangeMaintenance = (event) => {
    setMaintenanceSelected(event.target.value);
  };

  //
  const handleChangeMachine = (event) => {
    setMachineSelected(event.target.value);
  };

  //
  const handleUpdateAlarm = (data) => {
    //     if(maintenanceSelected && machineSelected){
    //     dbrefAlarmsCollection.doc(data.id).update({ "maintenanceId": maintenanceSelected, "mid": machineSelected }).then(() => {
    //         toastMessageSuccess({ message: "updated successfully" });
    //         setUpdateMode(false);
    //         setMachineSelected("");
    //         setMaintenanceSelected("");
    //         //handlePage()
    //     })
    //         .catch((e) => toastMessageWarning({ message: e }))
    // } else {
    //     toastMessageWarning({"message": "One or all fields are empty"})
    // }
  };
  return (
    <TableRow
      style={{ cursor: "pointer" }}
      className="hover:shadow-md hover:shadow-gray-400"
    >
      <TableCell
        sx={
          currentMode === "Dark"
            ? { color: "white", textTransform: "capitalize" }
            : { textTransform: "capitalize" }
        }
        style={{ padding: "10px" }}
        align="left"
      >
        {data.name}
      </TableCell>
      <TableCell
        sx={
          currentMode === "Dark"
            ? { color: "white", textTransform: "capitalize" }
            : { textTransform: "capitalize" }
        }
        style={{ padding: "10px" }}
        align="left"
      >
        <Box sx={{ minWidth: 150 }}>
          {updateMode ? (
            <FormControl fullWidth size="small">
              <InputLabel id="demo-simple-select-label">Machine </InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={machineSelected}
                label="Machine"
                onChange={handleChangeMachine}
              >
                {machineData?.map((data, index) => (
                  <MenuItem key={index} value={data.id}>
                    {data.title}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          ) : (
            <div>
              {" "}
              {machineData?.map((dataM) =>
                dataM.id === data.mid ? dataM.title : null,
              )}{" "}
            </div>
          )}
        </Box>
      </TableCell>

      <TableCell
        sx={
          currentMode === "Dark"
            ? { color: "white", textTransform: "capitalize" }
            : { textTransform: "capitalize" }
        }
        style={{ padding: "10px" }}
        align="left"
      >
        <Box sx={{ minWidth: 150 }}>
          {updateMode ? (
            <FormControl fullWidth size="small">
              <InputLabel id="demo-simple-select-label">Maintenance</InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={maintenanceSelected}
                label="Maintenance"
                onChange={handleChangeMaintenance}
              >
                {maintenanceData
                  ?.filter((mData) => mData?.mid === machineSelected)
                  ?.map((data, index) => (
                    <MenuItem key={index} value={data.id}>
                      {data.title}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
          ) : (
            <div>
              {" "}
              {maintenanceData?.map((dataM) =>
                dataM.id === data.maintenanceId ? dataM.title : null,
              )}{" "}
            </div>
          )}
        </Box>
      </TableCell>

      {updateMode ? (
        <TableCell
          sx={
            currentMode === "Dark"
              ? { color: "white", textTransform: "capitalize" }
              : { textTransform: "capitalize" }
          }
          style={{ padding: "10px" }}
          align="left"
        >
          <div className="flex">
            <ButtonBasic
              disabled={false}
              buttonTitle="Update"
              onClick={() => handleUpdateAlarm(data)}
            />
            <span
              className="hover:cursor-pointer hover:text-red-700"
              data-title="Cancel"
              onClick={() => setUpdateMode(false)}
            >
              <CancelOutlined sx={{ marginLeft: "2px", fontSize: "15px" }} />
            </span>
          </div>
        </TableCell>
      ) : (
        <TableCell
          sx={
            currentMode === "Dark"
              ? { color: "white", textTransform: "capitalize" }
              : { textTransform: "capitalize" }
          }
          style={{ padding: "10px" }}
          align="left"
        >
          <ButtonBasic buttonTitle="Edit" onClick={() => setUpdateMode(true)} />
        </TableCell>
      )}
    </TableRow>
  );
}
