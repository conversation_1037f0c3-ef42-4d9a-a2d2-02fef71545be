import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { addDataToCms } from "../functions/cms-infra.functions";
import { toastMessage } from "../../../../tools/toast";
import { useContext, useState } from "react";
import { CmsInfraContext } from "../../../../services/cms-infrastructure/cms-infra.context";

const AddCmsInfrastructure = () => {
  const { instruments } = useContext(CmsInfraContext);
  const [code, setCode] = useState("");

  async function handleOnSubmit(e) {
    e.preventDefault();

    const formData = { ref_id: "001", instrument_code: code };
    let active = false;

    Array.from(e.currentTarget.elements).forEach((field) => {
      if (!field.name) return;
      formData[field.name] = field.value;
    });

    addDataToCms(formData);
  }

  return (
    <div>
      <form onSubmit={handleOnSubmit}>
        <TextField
          sx={{ mb: 2 }}
          placeholder="Equipment Name"
          fullWidth
          variant="outlined"
          name="name"
        />
        <TextField
          sx={{ mb: 2 }}
          placeholder="Make / Model"
          fullWidth
          variant="outlined"
          name="make"
        />
        <TextField
          sx={{ mb: 2 }}
          placeholder="SI Number"
          fullWidth
          variant="outlined"
          name="si_num"
        />
        <TextField
          sx={{ mb: 2 }}
          placeholder="Certificate Number"
          fullWidth
          variant="outlined"
          name="certificate_no"
        />
        <TextField
          sx={{ mb: 2 }}
          placeholder="Validity"
          fullWidth
          variant="outlined"
          name="validity"
        />
        <FormControl sx={{ mb: 3 }} fullWidth>
          <InputLabel>Select Instrument</InputLabel>
          <Select
            label="Select Instrument"
            onChange={(e) => setCode(e.target.value)}
          >
            {instruments.map((item, i) => (
              <MenuItem value={item?.code_no} key={i}>
                {item?.code_no}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Button type="submit" variant="contained" fullWidth>
          Submit
        </Button>
      </form>
    </div>
  );
};

export default AddCmsInfrastructure;
