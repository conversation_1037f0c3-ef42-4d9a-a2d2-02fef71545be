import React, { useState, useEffect, useRef } from "react";
import EditorJS from "@editorjs/editorjs";
import Header from "@editorjs/header";
import List from "@editorjs/list";
import "./Editor.scss";

const INITIAL_DATA = () => {
  return {
    time: new Date().getTime(),
    blocks: [],
  };
};

const Editor = (props) => {
  const EDITOR_HOLDER_ID = props.id;
  const { editorData, setEditorData } = props;
  const ejInstance = useRef();

  useEffect(() => {
    if (!ejInstance.current) {
      initEditor();
    }
    return () => {
      ejInstance.current.destroy();
      ejInstance.current = null;
    };
  }, []);

  const initEditor = () => {
    const editor = new EditorJS({
      holder: EDITOR_HOLDER_ID,
      logLevel: "ERROR",
      data: editorData ? editorData : INITIAL_DATA,
      onReady: () => {
        ejInstance.current = editor;
      },
      onChange: async () => {
        let content = await ejInstance.current.save();
        setEditorData(content);
      },
      autofocus: true,
      tools: {
        header: Header,
        list: List,
      },
    });
  };

  return <div id={EDITOR_HOLDER_ID}></div>;
};

export default Editor;
