.adminPage {
  padding: 1.2rem;

  .adminPageInfoContainer {
    width: 100%;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    color: #344767;
    padding: 1.5rem 1.2rem;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .info {
      display: flex;
      flex-direction: column;
      h3 {
        font-size: 1.4rem;
        font-weight: 500;
        margin-bottom: 0.7rem;
      }
      p {
        font-size: 0.9rem;
        opacity: 0.9;
      }
    }

    .btn {
      .addAdminBtn {
        padding: 0.7rem 1.8rem;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
        outline: none;
        cursor: pointer;
        border-radius: 8px;
        border: none;
        background-color: rgba(20, 20, 94, 0.84);

        color: #fff;
        box-shadow:
          rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
          rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
        &:hover {
          opacity: 0.9;
        }
      }
    }
  }

  .adminsContainerSection {
    width: 100%;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    color: #344767;
    padding: 1.5rem 1.2rem;
    margin: 1.5rem 0;

    .adminsHeading {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 1.2rem;
      .info {
        margin-bottom: 0.4rem;
        h3 {
          font-size: 1.2rem;
          font-weight: 500;
          opacity: 0.9;
        }
        p {
          margin-top: 0.5rem;
          font-size: 0.9rem;
          opacity: 0.9;
        }
      }
    }

    .cardsContainer {
      //display: flex;
      // width: 100%;
      //flex-direction: column;
      // align-items: center;
      // justify-content: center;
      // flex-wrap: wrap;
      padding: 1rem;
      // @media (max-width: 500px) {
      //   padding: 0.5rem 0;
      // }

      .card {
        margin: 1rem;
        padding: 1rem;
        width: 265px;
        box-shadow: 0 0 28px 4px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        border-radius: 10px;
        @media (max-width: 500px) {
          width: 280px;
          margin: 0.5rem;
        }
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        .cardImg {
          margin-bottom: 0.5rem;
          width: 70px;
          height: 70px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            cursor: pointer;
          }
        }

        .cardTitle {
          margin: 0.4rem 0;
          h3 {
            text-align: center;
            font-size: 1rem;
            opacity: 0.6;
            text-decoration: underline;
            @media (max-width: 500px) {
              font-size: 0.9rem;
            }
          }
        }

        &:hover {
          transform: scale(1.1);
          color: white;
          background-color: rgba(20, 20, 94, 0.84);
        }
      }
    }
  }
}
