import { MenuItem, Select, Switch, TextField } from "@mui/material";
import {
  Button,
  Checkbox,
  FormControl,
  IconButton,
  InputLabel,
} from "@mui/material";
import React from "react";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CloseIcon from "@mui/icons-material/Close";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { companies, companyId_constant, fatReport } from "../../constants/data";
import { db } from "../../firebase";
import { toastMessageSuccess } from "../../tools/toast";
import { useAuth } from "../../hooks/AuthProvider";
import { updateUserOnEditDocument } from "../../utils/updateUserOnEditDocument";
import LoggingFunction from "../../components/CFR-Report/LoggingFunction";

const EditTable = ({
  data,
  fatId,
  handleClose,
  index,
  details,
  user,
  machineName,
}) => {
  const [deviation, setDeviation] = useState(data.dev);
  const [confirm, setConfirm] = useState(data.confirm);
  const [observation, setObservation] = useState(data.observation);
  const { reportId } = useParams();
  const { currentUser } = useAuth();
  // const databaseCollection = db.collection(companies).doc(companyId_constant)
  //   .collection(fatReport).doc(reportId).collection(`fatData`).doc(fatId).collection('table').doc(data.id)

  const handleSubmit = (e) => {
    e.preventDefault();
    const docdata = { dev: deviation, confirm, observation };
    // databaseCollection.update(docdata).then(() => {
    //   handleClose()
    //   LoggingFunction(
    //     machineName,
    //     details?.title,
    //     user?.fname + " " + user?.lname,
    //     "FAT Reports",
    //     `Test execution table updated of ${details?.title} from FAT Reports`,
    //   )
    //   toastMessageSuccess({ message: "Updated details successfully" })
    //   updateUserOnEditDocument({ userEmail: currentUser.email, docId: reportId, fatDataId: fatId })
    // })
  };
  return (
    <form onSubmit={handleSubmit}>
      <InputLabel>Observation</InputLabel>
      <TextField
        value={observation}
        onChange={(e) => setObservation(e.target.value)}
        style={{ marginBottom: "10px" }}
        variant="outlined"
        fullWidth
        placeholder="Enter Document Observation"
      />
      <InputLabel>Confirm</InputLabel>
      <FormControl fullWidth variant="outlined">
        <Select
          variant="outlined"
          onChange={(e) => setConfirm(e.target.value)}
          value={confirm}
        >
          <MenuItem value="Yes">YES</MenuItem>
          <MenuItem value="No">NO</MenuItem>
        </Select>
      </FormControl>

      <InputLabel>Deviation</InputLabel>
      <TextField
        value={deviation}
        onChange={(e) => setDeviation(e.target.value)}
        style={{ marginBottom: "10px" }}
        variant="outlined"
        fullWidth
        placeholder="Enter Document Desviation"
      />

      <div className="mt-10 flex justify-between">
        <Button
          onClick={handleClose}
          variant="contained"
          color="error"
          endIcon={<CloseIcon />}
        >
          Cancel{" "}
        </Button>
        <Button type="submit" variant="contained" endIcon={<AddIcon />}>
          Save Details
        </Button>
      </div>
    </form>
  );
};

export default EditTable;
