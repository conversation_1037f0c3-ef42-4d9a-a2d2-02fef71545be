import { Grid, MenuItem, Paper, Typography } from "@mui/material";
import React from "react";
import MyDropDown from "../../../components/ui/dropdown";
import { REQUEST_STATUS } from "./utils";
import PropTypes from "prop-types";

const RequestStatusFilters = ({ formData, setFormData }) => {
  const TITLE = "Request Status Filter Access";
  const USERS_ACCESS_LABEL = "USER'S ACCESS";
  const PLANNERS_ACCESS_LABEL = "PLANNER'S ACCESS";
  const PERFORMERS_ACCESS_LABEL = "PERFORMER'S ACCESS";
  const APPROVERS_ACCESS_LABEL = "APPROVER'S ACCESS";
  const ADMINS_ACCESS_LABEL = "ADMIN'S ACCESS";

  const USERS_ACCESS_NAME = "USERS_RSF_ACCESS";
  const PLANNERS_ACCESS_NAME = "PLANNERS_RSF_ACCESS";
  const PERFORMERS_ACCESS_NAME = "PERFORMERS_RSF_ACCESS";
  const APPROVERS_ACCESS_NAME = "APPROVERS_RSF_ACCESS";
  const ADMINS_ACCESS_NAME = "ADMINS_RSF_ACCESS";

  const handleDropdownChange = (e) => {
    const {
      target: { value, name },
    } = e;
    // const valueArray = parseStringArray(value);
    setFormData({
      ...formData,
      [name]: typeof value === "string" ? [value] : value,
    });
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {TITLE}
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <MyDropDown
            label={USERS_ACCESS_LABEL}
            name={USERS_ACCESS_NAME}
            value={formData.USERS_RSF_ACCESS ?? []}
            onChange={handleDropdownChange}
            multiple
            sx={{ minWidth: "14rem", width: "100%", maxWidth: "80rem" }}
            minWidth={"100%"}
          >
            {Object.keys(REQUEST_STATUS).map((key) => (
              <MenuItem key={key} value={REQUEST_STATUS[key]}>
                {key}
              </MenuItem>
            ))}
          </MyDropDown>
        </Grid>
        <Grid item xs={12}>
          <MyDropDown
            label={PLANNERS_ACCESS_LABEL}
            name={PLANNERS_ACCESS_NAME}
            value={formData.PLANNERS_RSF_ACCESS ?? []}
            onChange={handleDropdownChange}
            multiple
            sx={{ minWidth: "14rem", width: "100%", maxWidth: "80rem" }}
            minWidth={"100%"}
          >
            {Object.keys(REQUEST_STATUS).map((key) => (
              <MenuItem key={key} value={REQUEST_STATUS[key]}>
                {key}
              </MenuItem>
            ))}
          </MyDropDown>
        </Grid>
        <Grid item xs={12}>
          <MyDropDown
            label={PERFORMERS_ACCESS_LABEL}
            name={PERFORMERS_ACCESS_NAME}
            value={formData.PERFORMERS_RSF_ACCESS ?? []}
            onChange={handleDropdownChange}
            multiple
            sx={{ minWidth: "14rem", width: "100%", maxWidth: "80rem" }}
            minWidth={"100%"}
          >
            {Object.keys(REQUEST_STATUS).map((key) => (
              <MenuItem key={key} value={REQUEST_STATUS[key]}>
                {key}
              </MenuItem>
            ))}
          </MyDropDown>
        </Grid>
        <Grid item xs={12}>
          <MyDropDown
            label={APPROVERS_ACCESS_LABEL}
            name={APPROVERS_ACCESS_NAME}
            value={formData.APPROVERS_RSF_ACCESS ?? []}
            onChange={handleDropdownChange}
            multiple
            sx={{ minWidth: "14rem", width: "100%", maxWidth: "80rem" }}
            minWidth={"100%"}
          >
            {Object.keys(REQUEST_STATUS).map((key) => (
              <MenuItem key={key} value={REQUEST_STATUS[key]}>
                {key}
              </MenuItem>
            ))}
          </MyDropDown>
        </Grid>
        <Grid item xs={12}>
          <MyDropDown
            label={ADMINS_ACCESS_LABEL}
            name={ADMINS_ACCESS_NAME}
            value={formData.ADMINS_RSF_ACCESS ?? []}
            onChange={handleDropdownChange}
            multiple
            sx={{ minWidth: "14rem", width: "100%", maxWidth: "80rem" }}
            minWidth={"100%"}
          >
            {Object.keys(REQUEST_STATUS).map((key) => (
              <MenuItem key={key} value={REQUEST_STATUS[key]}>
                {key}
              </MenuItem>
            ))}
          </MyDropDown>
        </Grid>
      </Grid>
    </Paper>
  );
};
RequestStatusFilters.propTypes = {
  formData: PropTypes.object,
  setFormData: PropTypes.func,
};
export default RequestStatusFilters;
