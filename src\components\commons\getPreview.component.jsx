import { memo } from "react";

const fileFormats = {
  IMAGE: "image",
  VIDEO: "video",
  AUDIO: "audio",
  TEXT: "text",
  PDF: "application/pdf", // Added PDF format
};

const GetPreview = ({
  sourceUrl = "",
  textDesc = "",
  fileFormat = "",
  previewImageStyle = {},
  previewAudioStyle = {},
  previewVideoStyle = {},
  previewTextStyle = {},
  previewPdfStyle = {}, // Added PDF style prop
}) => {
  // Debug props
  console.log("GetPreview props:", { sourceUrl, fileFormat });

  switch (fileFormat) {
    case fileFormats.IMAGE:
      return (
        <img src={sourceUrl} alt="Preview image" style={previewImageStyle} />
      );

    case fileFormats.VIDEO:
      return (
        <video
          src={sourceUrl}
          alt="Preview video"
          style={previewVideoStyle}
          controls
        />
      );

    case fileFormats.AUDIO:
      return (
        <audio style={previewAudioStyle} controls>
          <source src={sourceUrl} type="audio/mpeg" />
          Your browser does not support the audio element.
        </audio>
      );

    case fileFormats.TEXT:
      return (
        <div style={previewTextStyle}>
          <span>{textDesc}</span>
        </div>
      );

    case fileFormats.PDF:
      return (
        <iframe
          src={sourceUrl}
          style={{ ...previewPdfStyle, width: "100%", height: "500px" }} // Default size, customizable via previewPdfStyle
          title="PDF Preview"
        />
      );

    default:
      return null;
  }
};

export default memo(GetPreview);
