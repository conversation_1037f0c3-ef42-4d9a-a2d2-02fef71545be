import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, Button } from "@mui/material";

const Filters = ({
  config,
  visibleColumns,
  handleFilterChange,
  toggleAdvancedFilterModal,
  toggleModal,
  handleCreate,
}) => {
  return (
    <Box
      sx={{
        display: "flex",
        gap: "1rem",
        marginBottom: "1rem",
        flexWrap: "wrap",
      }}
    >
      {/* Render filters only for visible columns */}
      {config
        .filter((col) => visibleColumns.includes(col.key) && col.filterable)
        .map((col) => (
          <TextField
            key={col.key}
            label={`Filter by ${col.label}`}
            variant="outlined"
            size="small"
            onChange={(e) => handleFilterChange(col.key, e.target.value)}
          />
        ))}

      {/* Buttons for advanced filters, column selection, and creating new rows */}
      <Button
        variant="contained"
        color="secondary"
        onClick={toggleAdvancedFilterModal}
        style={{ alignSelf: "flex-start" }}
      >
        Advanced Filters
      </Button>

      <Button
        variant="contained"
        color="primary"
        onClick={toggleModal}
        style={{ alignSelf: "flex-start" }}
      >
        Select Columns
      </Button>

      <Button
        variant="contained"
        color="primary"
        onClick={handleCreate}
        style={{ alignSelf: "flex-start" }}
      >
        Create
      </Button>
    </Box>
  );
};

export default Filters;
