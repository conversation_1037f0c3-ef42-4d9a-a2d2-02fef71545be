import axios from "axios";
import { dbUrl } from "../constants/db";

export async function getEnvData() {
  const response = {
    error: false,
    data: null,
  };
  try {
    if (dbUrl === undefined) {
      return response;
    }
    console.log("dbUrl", dbUrl);
    const res = await axios.get(`${dbUrl}/envdata`);
    if (res.data) {
      response.data = res.data;
    } else {
      response.error = true;
    }
  } catch (error) {
    response.error = true;
  }

  return response;
}
