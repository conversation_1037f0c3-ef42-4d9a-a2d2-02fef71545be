import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  <PERSON>alogTitle,
  IconButton,
  Slide,
  Typography,
} from "@mui/material";
import { EditSelectedComponent } from "./EditSelectedComponent";
import { Carousel } from "react-bootstrap";
import { forwardRef, useState, useEffect } from "react";
import "bootstrap/dist/css/bootstrap.min.css";
import { EastOutlined, WestOutlined } from "@mui/icons-material";
import { validateStep } from "./utils";
import { useAuth } from "../../hooks/AuthProvider";
import { toast } from "react-toastify";
import { ButtonBasicCancel } from "../buttons/Buttons";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export function PerformModal({
  rowData = {},
  currStep = 0,
  carousalData = [],
  handleSubmit = async (item, updatedItem) => {},
  setEnlarge = () => {},
  setEValue = () => {},
  open = false,
  handleClose = () => {},
  allSortKeys = [], // Add allSortKeys as a prop
}) {
  const { currentUser } = useAuth();
  const [isValid, setIsValid] = useState(true);

  useEffect(() => {
    if (open) {
      if (rowData.email !== currentUser.email) {
        toast.warn("This report is initiated by another user.");
        setIsValid(false);
        handleClose();
        return;
      }

      if (rowData.report_status === 2) {
        const getReportStatusText = (status) => {
          switch (status) {
            case 0:
              return "In Progress";
            case 1:
              return "Ready for Review";
            case 2:
              return "Reviewed";
            default:
              return "Unknown";
          }
        };

        toast.warn(
          `Report Status is in '${getReportStatusText(rowData.report_status)}' state.`,
        );
        setIsValid(false);
        handleClose();
        return;
      }

      setIsValid(true);
    }
  }, [
    open,
    rowData.email,
    rowData.report_status,
    currentUser.email,
    handleClose,
  ]);

  if (!isValid) {
    return null;
  }

  return (
    <Dialog
      open={open}
      TransitionComponent={Transition}
      keepMounted
      onClose={handleClose}
      aria-describedby="alert-dialog-slide-description"
      fullWidth
      maxWidth="lg"
    >
      <DialogTitle>
        <Box
          sx={{
            p: 2,
            borderRadius: 1,
            border: "1px solid",
            borderColor: "primary.main",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h6" component="span">
            {rowData.title}{" "}
            <span className="opacity-60 text-sm">
              {" ( " + rowData?.email.split("@")[0] + " )"}
            </span>
          </Typography>
          <Typography component="span">
            <span className="font-bold">
              {rowData?.report_status === 0 && (
                <span className="text-yellow-500">In Progress</span>
              )}
              {rowData?.report_status === 1 && (
                <span className="text-blue-500">Ready for Review</span>
              )}
              {rowData?.report_status === 2 && (
                <span className="text-green-500">Reviewed</span>
              )}
            </span>
          </Typography>
        </Box>
      </DialogTitle>
      <DialogContent>
        <Box>
          <StepsCarousel
            currStep={currStep}
            carousalData={carousalData}
            handleSubmit={handleSubmit}
            setEnlarge={setEnlarge}
            setEValue={setEValue}
            allSortKeys={allSortKeys} // Pass allSortKeys to StepsCarousel
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <ButtonBasicCancel buttonTitle="Close" onClick={handleClose} />
      </DialogActions>
    </Dialog>
  );
}

function StepsCarousel({
  currStep = 0,
  carousalData = [],
  handleSubmit = async (item, updatedItem) => {},
  setEnlarge = () => {},
  setEValue = () => {},
  allSortKeys = [], // Receive allSortKeys as prop
}) {
  // Create a map of sortKey to index for quick lookup
  const sortKeyToIndex = carousalData.reduce((acc, item, index) => {
    acc[item.sortKey] = index;
    return acc;
  }, {});

  // Initialize current index based on allSortKeys
  const [currentSortKey, setCurrentSortKey] = useState(
    allSortKeys.length > 0
      ? allSortKeys[Math.min(currStep, allSortKeys.length - 1)]
      : null,
  );

  const currentIndex =
    currentSortKey !== null ? sortKeyToIndex[currentSortKey] : 0;

  useEffect(() => {
    // Update currentSortKey when carousalData or allSortKeys changes
    if (allSortKeys.length > 0) {
      const initialSortKey =
        allSortKeys[Math.min(currStep, allSortKeys.length - 1)];
      setCurrentSortKey(initialSortKey);
    }
  }, [carousalData, allSortKeys, currStep]);

  const isCurrStepValid =
    carousalData &&
    carousalData.length > 0 &&
    currentIndex < carousalData.length &&
    validateStep(carousalData[currentIndex]).isValid;

  const isLastStep = currentIndex === carousalData.length - 1;
  const isFirstStep = currentIndex === 0;

  const handlePrev = () => {
    if (allSortKeys.length === 0) return;

    const currentKeyIndex = allSortKeys.indexOf(currentSortKey);
    if (currentKeyIndex > 0) {
      setCurrentSortKey(allSortKeys[currentKeyIndex - 1]);
    }
  };

  const handleNext = () => {
    if (allSortKeys.length === 0) return;

    const currentKeyIndex = allSortKeys.indexOf(currentSortKey);
    if (currentKeyIndex < allSortKeys.length - 1) {
      setCurrentSortKey(allSortKeys[currentKeyIndex + 1]);
    }
  };

  const handleSelect = (selectedIndex) => {
    if (allSortKeys.length === 0) return;

    setCurrentSortKey(allSortKeys[selectedIndex]);
  };

  return (
    <>
      <Carousel
        activeIndex={currentIndex}
        onSelect={handleSelect}
        controls={false}
        indicators={false}
        indicatorLabels={false}
        interval={null}
      >
        {carousalData.map((item, idx) => (
          <Carousel.Item key={item._id}>
            <EditSelectedComponent
              stepKey={item.sortKey} // Use sortKey instead of stepKey
              item={item}
              setEValue={setEValue}
              setEnlarge={setEnlarge}
              handleSubmit={handleSubmit}
            />
          </Carousel.Item>
        ))}
      </Carousel>
      <div className="d-flex justify-content-between align-items-center mt-2">
        <IconButton
          onClick={handlePrev}
          disabled={isFirstStep}
          sx={{
            color: isFirstStep ? "grey" : "primary.main",
          }}
        >
          <WestOutlined />
        </IconButton>
        <Typography component="span">
          {allSortKeys.indexOf(currentSortKey) + 1} / {allSortKeys.length}
        </Typography>
        <IconButton
          onClick={handleNext}
          disabled={!isCurrStepValid || isLastStep}
          sx={{
            color: !isCurrStepValid || isLastStep ? "grey" : "primary.main",
          }}
        >
          <EastOutlined />
        </IconButton>
      </div>
    </>
  );
}
