/* eslint-disable jsx-a11y/alt-text */
import CategoryIcon from "@mui/icons-material/Category";
import {useParams, useNavigate} from "react-router-dom";

import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  Tooltip,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  DialogActions,
  useStepContext,
  Button,
  Box,
} from "@mui/material";
import React, {useContext, useEffect, useState} from "react";
import Delete from "../../components/Delete/Delete";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import Paper from "@mui/material/Paper";
import "pure-react-carousel/dist/react-carousel.es.css";
import "./SubStepModal.scss";
import {DropzoneArea} from "material-ui-dropzone";
import EditStep from "./EditStep";
import InfoIcon from "@mui/icons-material/Info";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import DialogForSubStep from "./DialogForSubStep";
import {useStateContext} from "../../context/ContextProvider";
import {ButtonBasic} from "../../components/buttons/Buttons";
import {themeColors} from "../../infrastructure/theme";
import AddIcon from "@mui/icons-material/Add";
import axios from "axios";
import {dbConfig} from "../../infrastructure/db/db-config";
import {convertBase64} from "../../hooks/useBase64";
import {useMongoRefresh} from "../../services/mongo-refresh.context";
import {sharedCss} from "../../styles/sharedCss";
import {makeStyles} from "@mui/styles";
import {
  useCreateMachineCfr,
  useDeleteMachineCfr,
} from "../../hooks/cfr/machineCfrProvider";
import {useAuth} from "../../hooks/AuthProvider";
import {
  useHashtagsAllGetter,
  useHashtagsSetter,
} from "../../services3/hashtag/hashtagcontext";
import {HashtagContext} from "../../services2/hashtag/hashtag.context";
import {
  useStepsAllGetter,
  useStepsSetter,
} from "../../services3/machines/MachineContext2";
import ModalDiv from "./ModalDiv";
import {FaHashtag} from "react-icons/fa";
import {AddTask, PlaylistAdd, PlaylistAddCheck} from "@mui/icons-material";
import {classNames} from "@react-pdf-viewer/core";
import GetPreviewComponent from "../../components/commons/getPreview.component";
import RemoveIcon from "@mui/icons-material/Remove";
import {useCheckAccess} from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";

const useCustomStyles = makeStyles(theme => ({
  stepsConatiner: {
    padding: "0.5rem",
    backgroundColor: theme.palette.custom.backgroundThird,
    margin: "0.5rem",
    height: "55vh",
  },
  imageContainer: {
    backgroundColor: theme.palette.custom.backgroundThird,
    display: "flex",
    justifyContent: "center",
    height: "40vh",
    margin: "0.2rem 0.5rem",
  },
}));

const ShowPreview = ({type, url}) => {
  if (type === "image") {
    return <img src={url} />;
  }

  if (type === "video") {
    return (
      <div>
        <video src={url} />
      </div>
    );
  }
};

function GetPreview({format, url, desc}) {
  const {currentMode} = useStateContext();
  const fileFormatSuggestion = "Specify the format by clicking edit step";
  if (format === "image") {
    return (
      <img
        // width='450px'
        style={{maxHeight: "40vh", maxWidth: "650px"}}
        src={url}
      />
    );
    // return (
    //   <ImageMagnifier
    //     width={"200px"}
    //     src={url}
    //   />
    // )
  } else if (format === "video") {
    return (
      <video
        style={{width: "60%", maxHeight: "45vh", objectFit: "contain"}}
        controls
        src={url}
        alt="Step media"
      />
    );
  } else if (format === "audio") {
    return (
      <audio
        style={{marginTop: "15%", marginRight: "50px", marginBottom: "20px"}}
        controls>
        <source src={url} />
      </audio>
    );
  } else if (format === "text") {
    return (
      <div
        className="w-85 h-85 text-lg font-bold self-center capitalize"
        style={{wordBreak: "break-word"}}>
        <span
          data-title={fileFormatSuggestion}
          style={currentMode === "Dark" ? {color: "#fff"} : {}}>
          {desc}
        </span>
      </div>
    );
  }
  //bug:sl-205 fixed by this
  else if (!format) {
    return (
      <div className="w-40 h-85 animate-pulse self-center">
        <span data-title={fileFormatSuggestion}>File format is missing !</span>
      </div>
    );
  }

  return <h5>NO MEDIA</h5>;
}

function getColorType(key) {
  switch (key) {
    case "info":
      return "#1c9fe6";
    case "camera":
      return "#e6c81c";
    case "critical":
      return "#e61c3a";
    case "normal":
      return "#1ce663";
    default:
      return "#1c9fe6";
  }
}

const ManualItem = ({
  step,
  steps,
  machineName,
  name,
  moduleName,
  userName,
  mType,
  parent,
  sensorList,
  stepKey,
  manual_id,
  maintenance,
  setStepindex,
  stepindex,
  // openForModal,
  // setOpenForModal
  // hashArray,
}) => {
  //
  const {hashes} = useContext(HashtagContext);
  //

  const {currentUser} = useAuth();
  const deletetrainingstepcfr = useDeleteMachineCfr();
  const deletemaintenancestepcfr = useDeleteMachineCfr();

  const {mid} = useParams();

  const addtrainingsubstepcfr = useCreateMachineCfr();
  const addmaintenancesubstepcfr = useCreateMachineCfr();

  const [open, setOpen] = useState(false);
  const [openSubStep, setOpenSubStep] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [subSteps, setSubSteps] = useState([]);
  //const [newSteps, setNewSteps] = useState([]);
  const [testValueAll, setTestValueAll] = useState([]);
  //const [sensorValueAll, setSensorValueAll] = useState([]);

  const [title, setTitle] = useState("");
  const [sensorValue, setSensorValue] = useState("");
  const [sensor, setSensor] = useState([]);
  const [desc, setDesc] = useState("");
  const [type, setType] = useState("");
  const [format, setFormat] = useState("");
  // const [file, setFile] = useState(null);
  const [openDelete, setOpenDelete] = useState(false);
  const [sensorModal, setSensorModal] = useState(false);
  const {currentMode} = useStateContext();
  // const { progress, url } = useStorage(file);
  const [subStepCount, setSubStepCount] = useState(0);
  const [imageUrl, setImageUrl] = useState("");
  const [selectedFileForStorage, setSelectedFileForStorage] = useState("");
  const {refreshCount, setRefreshCount} = useMongoRefresh();
  const [isTitleExpanded, setIsTitleExpanded] = useState(false);
  const [isDescExpanded, setIsDescExpanded] = useState(false);
  const maxTitleLength = 20; // Adjust this value as needed
  const maxDescLength = 100; // Adjust this value as needed
  const [openForModal, setOpenForModal] = useState(false);
  const hashtagfunction = useHashtagsSetter();
  const hashtagArray = useHashtagsAllGetter();

  const hasStepGETAccess = useCheckAccess("stepdata", "GET");
  const hasStepPUTAccess = useCheckAccess("stepdata", "PUT");
  const hasStepDELETEAccess = useCheckAccess("stepdata", "DELETE");
  const hasSubStepGETAccess = useCheckAccess("sub-steps", "GET");
  const hasSubStepPOSTAccess = useCheckAccess("sub-steps", "POST");

  const toggleTitleReadMore = () => {
    setIsTitleExpanded(!isTitleExpanded);
  };

  const toggleDescReadMore = () => {
    setIsDescExpanded(!isDescExpanded);
  };

  const truncatedTitle =
    step.title.length > maxTitleLength
      ? step.title.substring(0, maxTitleLength) + "..."
      : step.title;
  const truncatedDesc =
    step.desc.length > maxDescLength
      ? step.desc.substring(0, maxDescLength) + "..."
      : step.desc;

  const getSubSteps = async () => {
    if (!hasSubStepGETAccess) return;
    try {
      const fullUrl =
        parent !== "Alarm SOP"
          ? `${dbConfig.url}/sub-steps/getFromStep/${step._id}`
          : `${dbConfig.url}/alarmSopSubStepData/getFromStep/${step._id}`;
      const response = await axios.get(fullUrl);
      const filteredSubSteps = response?.data?.data?.filter(
        x => x?.step_id === step?._id,
      );
      setSubSteps(filteredSubSteps);
      setSubStepCount(filteredSubSteps.length);
      // Update sub_steps availability if necessary
      updateSubStepsAvailability(
        step,
        !filteredSubSteps.length && step?.sub_steps ? false : null,
      );
    } catch (error) {
      console.error("Error fetching substeps:", error);
      toastMessage({message: "Failed to fetch substeps"});
    }
  };

  const updateSubStepsAvailability = async (step, sub_steps = null) => {
    if (!step || sub_steps === null) return;
    try {
      const localUrl =
        parent !== "Alarm SOP"
          ? `${dbConfig.url}/stepdata/${step._id}`
          : `${dbConfig.url}/alarmSopStepData/${step._id}`;
      await axios.put(localUrl, {
        ...step,
        sub_steps,
      });
    } catch (error) {
      console.error("Error updating sub_step:", error);
    }
  };

  // Fetch substeps when the component mounts or step changes
  useEffect(() => {
    if (hasSubStepGETAccess) {
      getSubSteps();
    }
  }, [step._id, refreshCount, hasSubStepGETAccess]);

  // Fetch hashtags only when necessary (e.g., when ModalDiv is opened)
  useEffect(() => {
    if (openForModal && parent === "Maintenance") {
      hashtagfunction(step);
    }
  }, [openForModal, step, parent, hashtagfunction]);
  // Update the substeps button handler to fetch substeps
  const handleSubStepsClick = async () => {
    if (hasSubStepGETAccess) {
      await getSubSteps(); // Fetch substeps when the button is clicked
      setOpenSubStep(true); // Open the substeps dialog after fetching
    }
  };

  const handleClose = () => {
    setOpen(false);
    setTitle("");
    setSensorValue("");
    setSensor([]); // Reset sensor array
    setDesc("");
    setType("");
    setFormat("");
    setImageUrl("");
    setSensorModal(false);
    setSelectedFileForStorage("");
  };

  // for substep
  // const handleSubmit = async (e) => {
  //   e.preventDefault();
  //   const data = {
  //     title,
  //     desc,
  //     step_id: step._id,
  //     index: subStepCount, // i.e subStepCount - 1 + 1
  //     // url: imageUrl ? imageUrl : "",
  //     type,
  //     format,
  //   };
  //   const date = new Date();
  //   const data2 = {
  //     activity: "substep added",
  //     dateTime: date,

  //     description: "a substep is added",
  //     machine: mid,
  //     module: "Training",
  //     username: currentUser.username,
  //   };

  //   const data3 = {
  //     activity: "substep added",
  //     dateTime: date,

  //     description: "a substep is added",
  //     machine: mid,
  //     module: "Maintenance",
  //     username: currentUser.username,
  //   };
  //   // console.log("SUB STEP", data);

  //   if (selectedFileForStorage) {
  //     let fd = new FormData();
  //     fd.append("image", selectedFileForStorage);
  //     var resTemp = await axios
  //       .post(`${dbConfig?.url_storage}/upload`, fd, {})
  //       .then((res1) => {
  //         console.log("storage:", res1);
  //         return res1;
  //       })
  //       .catch((err) => {
  //         console.log("storage error:", err);
  //       });
  //   }

  //   await axios
  //     .post(`${dbConfig.url}/sub-steps`, {
  //       ...data,
  //       url: selectedFileForStorage ? resTemp?.data?.data : "",
  //     })
  //     .then((response) => {
  //       if (parent == "Maintenance") {
  //         addmaintenancesubstepcfr(data3);
  //       } else {
  //         addtrainingsubstepcfr(data2);
  //       }
  //       toastMessageSuccess({ message: `Sub-Steps Added Successfully !` });
  //       setSubStepCount(subStepCount + 1);
  //       setOpen(false);
  //       setTitle("");
  //       setSensorValue("");
  //       setSensor([]); // Reset sensor array
  //       setDesc("");
  //       setImageUrl("");
  //       setSelectedFileForStorage("");
  //       setType("");
  //       setFormat("");
  //     })
  //     .catch((err) => {
  //       toastMessage({ message: err.message });
  //     });

  //   // To update sub_steps to true
  //   await updateSubStepsAvailability(
  //     step,
  //     step?.sub_steps || subStepCount ? null : true
  //   );
  // };

  const handleSubmit = async e => {
    e.preventDefault();
    const data = {
      title,
      desc,
      step_id: step._id,
      index: subStepCount,
      type,
      format,
    };
    const date = new Date();
    const data2 = {
      activity: "substep added",
      dateTime: date,
      description: "a substep is added",
      machine: mid,
      module: "Training",
      username: currentUser.username,
    };
    const data3 = {
      activity: "substep added",
      dateTime: date,
      description: "a substep is added",
      machine: mid,
      module: "Maintenance",
      username: currentUser.username,
    };

    let uploadedUrl = "";
    if (selectedFileForStorage) {
      let fd = new FormData();
      fd.append("image", selectedFileForStorage);
      try {
        const resTemp = await axios.post(`${dbConfig?.url_storage}/upload`, fd);
        uploadedUrl = resTemp?.data?.data;
      } catch (err) {
        console.error("Storage error:", err);
        toastMessage({message: "Failed to upload media"});
        return;
      }
    }

    const fullUrl =
      parent !== "Alarm SOP"
        ? `${dbConfig.url}/sub-steps`
        : `${dbConfig.url}/alarmSopSubStepData`;

    try {
      await axios.post(fullUrl, {
        ...data,
        url: uploadedUrl,
      });

      if (parent === "Maintenance") {
        addmaintenancesubstepcfr(data3);
      } else if (parent !== "Alarm SOP") {
        addtrainingsubstepcfr(data2);
      }

      toastMessageSuccess({message: `Sub-Step Added Successfully!`});
      setSubStepCount(subStepCount + 1); // Temporary increment for immediate UI update
      await getSubSteps(); // Refresh substeps to update subStepCount and subSteps
      setOpen(false);
      setTitle("");
      setSensorValue("");
      setSensor([]);
      setDesc("");
      setImageUrl("");
      setSelectedFileForStorage("");
      setType("");
      setFormat("");
    } catch (err) {
      console.error("Error adding substep:", err);
      toastMessage({message: err.message});
    }
  };

  const typesImages = ["image/png", "image/jpeg", "image/jpg"];
  const videoTypes = ["video/mp4", "video/mkv", "video/mov"];
  const audioTypes = ["audio/mp3", "audio/mpeg"];

  //
  // const sensorValueOpenFun = () => {
  //   se(true);
  // };

  const sensorValueCloseFun = () => {
    setSensorModal(false);
  };

  const prePopDataSensor = idx => {
    let temp = [...sensor];
    temp.splice(idx, 1);
    setSensor(temp);
  };
  //
  const menuItemTheme = {
    backgroundColor:
      currentMode === "Dark"
        ? themeColors.dark.primary
        : themeColors.light.primary,
  };

  function getPreview(url) {
    if (format === "image") {
      return <img width="450" src={url} />;
    } else if (format === "video") {
      return (
        <video
          style={{width: "90%", marginTop: "20px", marginBottom: "20px"}}
          controls
          src={url}
          alt="First slide"
        />
      );
    } else if (format === "audio") {
      return (
        <audio
          style={{
            marginTop: "15%",
            marginRight: "50px",
            marginBottom: "20px",
          }}
          controls>
          <source src={url} />
        </audio>
      );
    }
  }

  const handleChange = async loadedFiles => {
    let selectedFile = loadedFiles[0];
    const base64 = await convertBase64(selectedFile);
    console.log("Base64:", base64); // Debug the base64 string
    setSelectedFileForStorage(selectedFile);

    if (selectedFile) {
      if (type === "" || format === "") {
        toastMessage({
          message: "Please Select a Type / Format first to proceed",
        });
        return; // Exit early if type or format is missing
      }

      if (format === "image") {
        if (typesImages.includes(selectedFile.type)) {
          setImageUrl(base64);
        } else {
          toastMessage({message: "Please select an image file (png or jpg)"});
        }
      } else if (format === "video") {
        if (videoTypes.includes(selectedFile.type)) {
          setImageUrl(base64);
        } else {
          toastMessage({message: "Please select a video file (mp4 or mkv)"});
        }
      } else if (format === "audio") {
        if (audioTypes.includes(selectedFile.type)) {
          setImageUrl(base64);
        } else {
          toastMessage({message: "Please select an audio file (mp3)"});
        }
      }
    }
  };
  const commonCss = sharedCss();
  const customCss = useCustomStyles();
  const handleDelete = async () => {
    try {
      // Delete file from storage
      await axios.post(`${dbConfig.url_storage}/deleteImage`, {
        file_name: step?.url,
      });

      const date = new Date();
      const data2 = {
        activity: "step deleted",
        dateTime: date,
        description: "a step is deleted",
        machine: mid,
        module: "Training",
        username: currentUser.username,
      };
      const data3 = {
        activity: "step deleted",
        dateTime: date,
        description: "a step is deleted",
        machine: mid,
        module: "Maintenance",
        username: currentUser.username,
      };

      // Attempt to delete related hashtag data, ignoring 404 errors
      try {
        await axios.delete(`${dbConfig.url}/hashtags/stepdata/${step._id}`);
      } catch (hashtagError) {
        if (hashtagError.response?.status !== 404) {
          // Only throw non-404 errors
          throw hashtagError;
        }
        // 404 means no hashtags exist, so we can safely ignore it
        console.warn(`No hashtags found for stepdata ${step._id}`);
      }

      // Delete step data
      const fullUrl =
        parent !== "Alarm SOP"
          ? `${dbConfig.url}/stepdata/${step._id}`
          : `${dbConfig.url}/alarmSopStepData/${step._id}`;
      await axios.delete(fullUrl);

      if (parent === "Maintenance") {
        deletemaintenancestepcfr(data3);
      } else {
        deletetrainingstepcfr(data2);
      }

      toastMessageSuccess({message: `${step.title} deleted`});
      setOpenDelete(false);

      // Adjust stepindex based on the deleted step's position
      setStepindex(prevIndex => {
        if (steps.length === 1) {
          return 0;
        }
        if (stepKey === steps.length - 1) {
          const newIndex = steps.length - 2;
          return newIndex;
        }
        if (stepKey <= prevIndex) {
          const newIndex = Math.max(0, prevIndex - 1);
          return newIndex;
        }
        return prevIndex;
      });

      console.log("Triggering refresh with refreshCount:", refreshCount + 1);
      setRefreshCount(refreshCount + 1);
    } catch (error) {
      console.error("Error during deletion:", error);
      toastMessage({message: error.message});
    }
  };

  const handleDeleteMedia = () => {
    setImageUrl(null); // Clear the media state
  };

  const hashtag =
    Array.isArray(step?.hashtag) && step.hashtag.length > 0
      ? step.hashtag.map(tagId => {
          const tag = hashes.find(h => h._id === tagId);
          return tag ? `#${tag.title} ` : "";
        })
      : "No hashtags available";

  return (
    <div className={customCss.stepsConatiner} component={Paper}>
      <div className="bg-white rounded-lg shadow-md">
        {/* Step Header */}
        <div className="flex items-center justify-between bg-blue">
          {/* Left Side: Tooltip with IconButton */}
          <Tooltip title={step?.type} placement="top">
            <IconButton>
              <CategoryIcon
                style={{fontSize: "24px", color: getColorType(step?.type)}}
              />
            </IconButton>
          </Tooltip>

          {/* Centered Title and Description */}
          <div className="flex-1 text-center mx-4">
            {" "}
            {/* Added flex-1 and text-center */}
            <div className="text-xl uppercase font-bold">
              {stepKey + 1}.{step.title} :
            </div>
            {/* Description */}
            {step.format !== "text" && (
              <div className="text-sm text-black dark:text-black">
                {isDescExpanded ? step.desc : truncatedDesc}
                {step.desc.length > maxDescLength && (
                  <button
                    onClick={toggleDescReadMore}
                    className="text-blue-500 hover:text-blue-700 ml-2 focus:outline-none">
                    {isDescExpanded ? "Read Less" : "Read More"}
                  </button>
                )}
              </div>
            )}
          </div>

          {/* Right Side: Action Buttons */}
          <div className="flex items-center ">
            {subStepCount > 0 && (
              <>
                <div className="subStep">
                  <Tooltip title="Sub Steps" placement="top">
                    <IconButton
                      size="small"
                      variant="contained"
                      onClick={handleSubStepsClick}
                      disabled={!hasSubStepGETAccess}>
                      <PlaylistAddCheck />
                    </IconButton>
                  </Tooltip>
                </div>
              </>
            )}
            <Tooltip title="Add SubStep" placement="top">
              <IconButton
                variant="contained"
                color="secondary"
                size="small"
                onClick={() => setOpen(true)}
                disabled={!hasSubStepPOSTAccess}
                sx={{
                  color: !hasSubStepPOSTAccess ? "grey.500" : "secondary.main",
                }}>
                <PlaylistAdd />
              </IconButton>
            </Tooltip>
            {parent == "Maintenance" && (
              <Tooltip title={hashtag} placement="top">
                <IconButton
                  size="small"
                  variant="contained"
                  color="success"
                  onClick={() => setOpenForModal(true)}>
                  <FaHashtag />
                </IconButton>
              </Tooltip>
            )}
            <IconButton
              onClick={() => setOpenEdit(true)}
              disabled={!hasStepPUTAccess}
              sx={{
                color: !hasStepPUTAccess ? "grey.500" : "primary.main",
              }}>
              <EditIcon style={{fontSize: "20px"}} />
            </IconButton>

            <IconButton
              onClick={() => setOpenDelete(true)}
              disabled={!hasStepDELETEAccess}
              sx={{
                color: !hasStepDELETEAccess ? "grey.500" : "#f00",
              }}>
              <DeleteIcon style={{fontSize: "20px"}} />
            </IconButton>
          </div>
        </div>
      </div>

      {hasStepGETAccess ? (
        <div
          className="carouselInnerContent"
          style={{
            position: "relative", // Allow absolute positioning of sensor data
            display: "flex",
            flexDirection: "column", // Stack sensor data and media vertically
            gap: "1rem",
            padding: "1rem",
            backgroundColor:
              currentMode === "Dark"
                ? {backgroundColor: themeColors.dark.primary, color: "white"}
                : {},
            borderRadius: "8px",
          }}>
          {/* Sensor Data at Top-Left */}
          <div
            style={{
              position: "absolute",
              top: "10px",
              left: "10px",
              zIndex: 10, // Ensure it appears above other content
              backgroundColor:
                currentMode === "Dark"
                  ? {
                      backgroundColor: themeColors.dark.primary,
                      color: "white",
                    }
                  : {},
              padding: "0.5rem",
              borderRadius: "4px",
              maxWidth: "250px", // Limit width to avoid overlap
            }}>
            {typeof step.sensor === "object" && step.sensor.length > 0 ? (
              <div style={{fontSize: "12px"}}>
                {step.sensor.map(sData =>
                  sensorList?.map(tData =>
                    sData === tData._id ? (
                      <div key={tData.id} style={{marginBottom: "4px"}}>
                        <span style={{fontWeight: "bold"}}>{tData.tag}</span>:{" "}
                        {tData.value}
                      </div>
                    ) : null,
                  ),
                )}
              </div>
            ) : (
              <p
                style={{
                  fontSize: "12px",
                  color: currentMode === "Dark" ? "#ccc" : "#666",
                }}>
                No sensor data available.
              </p>
            )}
          </div>

          {/* Media Preview Centered */}
          <div
            className={customCss.imageContainer}
            style={{
              display: "flex",
              justifyContent: "center", // Center horizontally
              alignItems: "center", // Center vertically
              height: "40vh",
              width: "100%",
              backgroundColor:
                currentMode === "Dark"
                  ? {
                      backgroundColor: themeColors.dark.primary,
                      color: "white",
                    }
                  : {},
              borderRadius: "8px",
              overflow: "hidden", // Ensure content fits within bounds
            }}>
            <GetPreview
              format={step.format}
              url={`${dbConfig?.url_storage}/${step.url}`}
              desc={step.desc}
            />
          </div>
        </div>
      ) : (
        <NotAccessible />
      )}
      {/* delete Step */}

      <Dialog open={openDelete}>
        <Delete
          onClose={() => setOpenDelete(false)}
          onDelete={() => handleDelete()}
        />
      </Dialog>
      {/* end  delete Step */}
      {/* Adding a new MANUAL */}

      <Dialog open={open} fullWidth onClose={() => setOpen(false)}>
        <DialogTitle
          style={
            currentMode === "Dark"
              ? {backgroundColor: "#212B36", color: "white"}
              : {}
          }>
          Add Sub Step
        </DialogTitle>
        <DialogContent
          style={
            currentMode === "Dark"
              ? {backgroundColor: "#212B36", color: "white"}
              : {}
          }>
          <form onSubmit={handleSubmit}>
            <InputLabel style={{marginBottom: "10px"}}>
              Sub step Title
            </InputLabel>
            <TextField
              onChange={e => setTitle(e.target.value)}
              onBlur={() => setTitle(title?.trim())}
              value={title}
              style={{marginBottom: "10px"}}
              variant="outlined"
              fullWidth
              required
            />
            {/* <InputLabel style={{ marginBottom: "10px" }}>
              Sensory Value
            </InputLabel>
            <section className="flex">
              <Box sx={{ width: "100%", marginBottom: "10px" }}>
                <FormControl fullWidth>
                  <Select
                    value={sensorValue}
                    onChange={(e) => setSensorValue(e.target.value)}
                  >
                    {sensorList?.map((mData) => (
                      <MenuItem
                        key={mData.tag}
                        value={mData.tag}
                        sx={menuItemTheme}
                      >
                        {mData.tag}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
              <IconButton
                className="bg-red-700"
                onClick={() =>
                  sensorValue
                    ? setSensor([...sensor, sensorValue])
                    : toastMessageWarning({ message: "Missing sensor value" })
                }
              >
                <AddIcon className="text-green-800 "/>
              </IconButton>
            </section>
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                flexWrap: "wrap",
                marginTop: "5px",
                columnGap: "1rem",
              }}
            >
              {sensor?.map((data, idx) => (
                <div key={data + idx}>
                  <span className="font-bold">{idx + 1}. </span> {data}{" "}
                  <IconButton onClick={() => prePopDataSensor(idx)}>
                    <RemoveIcon className="text-red-700"/>
                  </IconButton>
                </div>
              ))}
            </div> */}
            <InputLabel style={{marginBottom: "10px"}}>
              SubStep Description
            </InputLabel>
            <TextField
              onChange={e => setDesc(e.target.value)}
              onBlur={() => setDesc(desc?.trim())}
              value={desc}
              style={{marginBottom: "10px"}}
              variant="outlined"
              fullWidth
              multiline
              rows={4}
              required
            />
            <InputLabel style={{marginBottom: "10px"}}>Format</InputLabel>
            <FormControl
              style={{marginBottom: "10px"}}
              required
              fullWidth
              variant="outlined">
              <Select onChange={e => setFormat(e.target.value)} required>
                <MenuItem value="image" sx={menuItemTheme}>
                  Image
                </MenuItem>
                <MenuItem value="video" sx={menuItemTheme}>
                  Video
                </MenuItem>
                <MenuItem value="audio" sx={menuItemTheme}>
                  Audio
                </MenuItem>
                <MenuItem value="text" sx={menuItemTheme}>
                  Text
                </MenuItem>
              </Select>
            </FormControl>
            <InputLabel style={{marginBottom: "10px"}}>Type</InputLabel>
            <FormControl
              style={{marginBottom: "10px"}}
              required
              fullWidth
              variant="outlined">
              <Select onChange={e => setType(e.target.value)} required>
                <MenuItem value="info" sx={menuItemTheme}>
                  Info
                </MenuItem>
                <MenuItem value="camera" sx={menuItemTheme}>
                  Camera
                </MenuItem>
                <MenuItem value="critical" sx={menuItemTheme}>
                  Critical
                </MenuItem>
                <MenuItem value="normal" sx={menuItemTheme}>
                  Normal
                </MenuItem>
              </Select>
            </FormControl>
            {format && format !== "text" && (
              <>
                <InputLabel style={{marginBottom: "10px"}}>Media</InputLabel>
                <DropzoneArea
                  showFileNames
                  onChange={handleChange}
                  dropzoneText="Drag and Drop / Click to ADD Media"
                  showAlerts={false}
                  filesLimit={1}
                  maxFileSize={50 * 1024 * 1024}
                  onDelete={handleDeleteMedia}
                  onAlert={message => {
                    if (message.includes("File is too big")) {
                      toastMessage({
                        message:
                          "File size exceeds 50MB. Please upload a smaller file.",
                      });
                    }
                  }}
                />
                <div style={{display: "flex", justifyContent: "center"}}>
                  {imageUrl ? (
                    <GetPreviewComponent
                      sourceUrl={imageUrl}
                      fileFormat={format}
                      previewImageStyle={{width: "450px", marginTop: "20px"}}
                      previewVideoStyle={{width: "75%", marginTop: "20px"}}
                      previewAudioStyle={{
                        marginTop: "15%",
                        marginRight: "50px",
                        marginBottom: "20px",
                      }}
                    />
                  ) : (
                    <img src="data:image/png;base64,..." alt="img" />
                  )}
                </div>
              </>
            )}
          </form>
        </DialogContent>
        <DialogActions
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            padding: "36px",
            backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
            color: currentMode === "Dark" ? "white" : "black",
          }}>
          <ButtonBasic
            buttonTitle="Cancel"
            width="30%"
            color="error"
            variant="contained"
            onClick={handleClose}
          />

          {title.trim() === "" ||
          type === "" ||
          format === "" ||
          desc.trim() === "" ||
          (format !== "text" && !imageUrl) ? (
            <ButtonBasic
              buttonTitle="Add Sub Step"
              width="30%"
              type="submit"
              color="primary"
              variant="contained"
              onClick={handleSubmit}
              disabled
            />
          ) : (
            <ButtonBasic
              buttonTitle="Add Sub Step"
              width="30%"
              type="submit"
              color="primary"
              variant="contained"
              onClick={handleSubmit}
            />
          )}
        </DialogActions>
      </Dialog>

      <DialogForSubStep
        parent={parent}
        open={openSubStep}
        close={() => setOpenSubStep(false)}
        step={step}
        subSteps={subSteps}
        sensorList={sensorList}
        onSubStepDelete={id => {
          setSubStepCount(subStepCount - 1);
          setRefreshCount(refreshCount + 1);
        }}
      />

      {/* Edit details of steps */}
      <EditStep
        collectionName="stepData"
        mType={mType}
        name={name}
        moduleName={moduleName}
        userName={userName}
        machineName={machineName}
        step={step}
        parent={parent}
        handleClose={() => setOpenEdit(false)}
        sensorList={sensorList}
        open={openEdit}
        setOpenEdit={setOpenEdit}
        hashtagArray2={hashtagArray}
      />

      {parent === "Maintenance" && (
        <ModalDiv
          manual_id={manual_id}
          open={openForModal}
          setOpen={setOpenForModal}
          maintenance={maintenance}
          machine={machineName}
          step={step}
        />
      )}
    </div>
  );
};

export default ManualItem;
