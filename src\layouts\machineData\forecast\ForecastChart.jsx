import React, { useRef, useEffect } from "react";
import * as d3 from "d3";
import { Card, CardContent, Typography, Box } from "@mui/material";

const ForecastChart = ({ timestamps, values, forecastValues }) => {
  const chartRef = useRef();

  useEffect(() => {
    if (!timestamps || !values || !forecastValues) return;

    // Clear previous chart
    d3.select(chartRef.current).selectAll("*").remove();

    // Get the width dynamically
    const containerWidth = chartRef.current.parentElement.clientWidth;
    const width = containerWidth - 40; // Compensating for padding
    const height = 250;
    const margin = { top: 20, right: 30, bottom: 30, left: 40 };

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left}, ${margin.top})`);

    // Parse timestamps
    const parsedTimestamps = timestamps.map((t) => new Date(t));

    // Find the last actual data index
    const lastActualIndex = values.findLastIndex((v) => v !== null);

    if (lastActualIndex === -1) return; // No actual data found

    // Construct new datasets ensuring forecast starts where actual ends
    const continuousTimestamps = [...timestamps];
    const continuousValues = [...values];
    const forecastOnlyValues = forecastValues.slice(lastActualIndex + 1); // Only take future values

    // Inject last actual data point at start of forecast
    if (forecastOnlyValues.length > 0) {
      forecastOnlyValues.unshift(values[lastActualIndex]); // Ensures smooth transition
      continuousTimestamps.splice(
        lastActualIndex + 1,
        0,
        timestamps[lastActualIndex],
      );
    }

    // Define scales
    const xScale = d3
      .scaleTime()
      .domain(d3.extent(parsedTimestamps))
      .range([0, width - margin.left - margin.right]);

    const yScale = d3
      .scaleLinear()
      .domain([
        d3.min([...values, ...forecastValues].filter((d) => d !== null)) - 5,
        d3.max([...values, ...forecastValues].filter((d) => d !== null)) + 5,
      ])
      .range([height - margin.top - margin.bottom, 0]);

    // Define line generator for actual data
    const lineActual = d3
      .line()
      .x((d, i) => xScale(parsedTimestamps[i]))
      .y((d) => yScale(d))
      .curve(d3.curveMonotoneX);

    // Define line generator for forecast data
    const lineForecast = d3
      .line()
      .x((d, i) => xScale(parsedTimestamps[lastActualIndex + i])) // Adjust index shift
      .y((d) => yScale(d))
      .curve(d3.curveMonotoneX);

    // Draw actual data line (solid black)
    svg
      .append("path")
      .datum(continuousValues.slice(0, lastActualIndex + 1))
      .attr("fill", "none")
      .attr("stroke", "#000")
      .attr("stroke-width", 2)
      .attr("d", lineActual);

    // Draw forecast data line (dashed, starting from last actual data point)
    svg
      .append("path")
      .datum(forecastOnlyValues)
      .attr("fill", "none")
      .attr("stroke", "#1976D2") // Blue color
      .attr("stroke-width", 2)
      .attr("stroke-dasharray", "5,5") // Dashed line for forecast
      .attr("d", lineForecast);

    // Add X axis
    svg
      .append("g")
      .attr("transform", `translate(0,${height - margin.top - margin.bottom})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat("%H:%M")));

    // Add Y axis
    svg.append("g").call(d3.axisLeft(yScale));
  }, [timestamps, values, forecastValues]);

  return (
    <Card sx={{ width: "100%", mt: 4 }}>
      <CardContent>
        <Typography variant="h6" align="center" gutterBottom>
          Machine Forecast Chart
        </Typography>
        <Box ref={chartRef} sx={{ width: "100%", height: "250px" }} />
      </CardContent>
    </Card>
  );
};

export default ForecastChart;
