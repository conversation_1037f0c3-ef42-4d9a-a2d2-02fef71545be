import { Box, Container, Typography } from "@mui/material";
import React from "react";
import ReportHead from "../Head/ReportHead";
import Pdf from "react-to-pdf";

const ref = React.createRef();

const FrontPage = () => {
  return (
    <>
      <Pdf targetRef={ref} filename="code-example.pdf">
        {({ toPdf }) => <button onClick={toPdf}>Generate Pdf</button>}
      </Pdf>
      <div ref={ref}>
        <Container sx={{ width: "100%", align: "center" }}>
          <Box
            sx={{ padding: 4, border: "1px solid black", marginTop: "10px" }}
          >
            <ReportHead />
            <br />
            <Typography sx={{ fontWeight: "bold" }} variant="h4" align="left">
              Lyophilization Systems India Pvt. Ltd.
              <br /> Hyderabad, India.
            </Typography>
            <br />
            <Typography sx={{ fontWeight: "bold" }} variant="h4" align="left">
              LYOPHILIZER <br />
              PLC and Software Qualification (FAT)
            </Typography>
            <br />
            <Typography sx={{ fontWeight: "bold" }} variant="h4" align="left">
              Customer
              <br />
              Gland Pharma Limited.
            </Typography>
            <Typography sx={{ fontWeight: "bold" }} variant="h6" align="left">
              Sy.No.143 to 148,150 & 151, D.P. Pally, <br />
              Near Gandimaisamma X Roads, Dundigal(M) <br />
              Medchal-Malkajgiri District, Hyderabad - 500043 <br />
              Telangana, India. <br />
            </Typography>
            <br />
            <Typography sx={{ fontWeight: "bold" }} variant="h5">
              Model No. Lyodryer 3S <br />
              Serial No. 2861
            </Typography>
          </Box>
        </Container>
      </div>
    </>
  );
};

export default FrontPage;
