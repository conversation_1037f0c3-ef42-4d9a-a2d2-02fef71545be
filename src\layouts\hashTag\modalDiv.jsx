import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  TextField,
  IconButton,
  Box,
  Typography,
  <PERSON>ton,
  <PERSON><PERSON>,
  Step,
  StepL<PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { useContext, useEffect, useState } from "react";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useStateContext } from "../../context/ContextProvider";
import { ModalContext } from "../../services2/hashtag/modal.context";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { dbConfig } from "../../infrastructure/db/db-config";
import axios from "axios";

// Styled components with dynamic theming and fixed card size
const StyledDialog = styled(Dialog)(({ theme, currentMode }) => ({
  "& .MuiDialog-paper": {
    borderRadius: "16px",
    maxWidth: "700px",
    width: "100%",
    backgroundColor: theme.palette.background.paper, // Neutral background
    color:
      currentMode === "Dark"
        ? theme.palette.text.primary
        : theme.palette.text.secondary,
    padding: theme.spacing(2),
  },
}));

const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.paper, // Neutral background
  minHeight: "500px",
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(3),
}));

const StyledCard = styled(Card)(
  ({ theme, currentColorLight, currentMode }) => ({
    borderRadius: "12px",
    backgroundColor: currentColorLight, // Apply currentColorLight to cards
    transition: "transform 0.2s, box-shadow 0.2s",
    cursor: "pointer",
    minHeight: "120px", // Fixed height for all cards
    height: "120px", // Ensure consistent height
    display: "flex",
    flexDirection: "column",
    "&:hover": {
      transform: "translateY(-4px)",
      boxShadow: theme.shadows[4],
    },
  }),
);

const StyledCardContent = styled(CardContent)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  height: "100%",
  overflow: "auto", // Handle overflow content
  padding: theme.spacing(2),
}));

const FormContainer = styled(Box)(({ theme, currentMode }) => ({
  padding: theme.spacing(3),
  backgroundColor:
    currentMode === "Dark"
      ? theme.palette.grey[800]
      : theme.palette.background.paper, // Neutral background
  borderRadius: "12px",
  boxShadow: theme.shadows[2],
}));

const HashtagList = styled("ul")(({ theme }) => ({
  listStyle: "none",
  padding: 0,
  margin: theme.spacing(2, 0),
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(1.5),
}));

const HashtagItem = styled("li")(({ theme, currentMode }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  padding: theme.spacing(1.5),
  borderRadius: "8px",
  backgroundColor:
    currentMode === "Dark" ? theme.palette.grey[900] : theme.palette.grey[100], // Neutral background
  border: `1px solid ${theme.palette.divider}`,
}));

const ModalDiv = ({ stepArray, open, setOpen, machines, mType, maint }) => {
  const {
    handleAddHashTag,
    maintenance,
    setMaintenance,
    hashtagAdded,
    setHA,
    maintType,
    setMT,
  } = useContext(ModalContext);
  const { currentMode, currentColorLight } = useStateContext();
  const [count, setCount] = useState(0);
  const [maintForMach, setMFM] = useState([]);
  const [selMachine, setSelMachine] = useState({});

  // Stepper steps
  const steps = [
    "Select Machine",
    "Select Maintenance Type",
    "Select Maintenance",
    "Add Hashtag to Step",
    "Select Step",
    "Add Step Hashtag",
  ];

  useEffect(() => {
    if (!open) {
      setMaintenance({});
      setCount(0);
    }
  }, [open, setMaintenance]);

  const handleMachineSelect = (target) => {
    setSelMachine(target);
    setCount(count + 1);
  };

  const handleMaintenanceSelect = (target) => {
    setMT(target);
    const machForMaint = maint.filter(
      (options) => options.data.mid === selMachine.mid,
    );
    const MFM = machForMaint.filter((options) => options.data.type === target);
    setMFM(MFM);
    setCount(count + 1);
  };

  const handleGoBack = () => {
    setCount(count - 1);
    if (count <= 3) setMaintenance({});
  };

  const handleClose = () => setOpen(false);

  return (
    <StyledDialog open={open} onClose={handleClose} currentMode={currentMode}>
      <DialogTitle sx={{ fontWeight: "bold", fontSize: "1.5rem" }}>
        Maintenance Workflow
      </DialogTitle>
      <Box sx={{ px: 3, mb: 2 }}>
        <Stepper activeStep={count} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Box>
      <StyledDialogContent>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          {count > 0 && (
            <ButtonBasic
              buttonTitle="Back"
              startIcon={<ArrowBackIcon />}
              onClick={handleGoBack}
            />
          )}
        </Box>
        {count === 0 && (
          <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Select a Machine
            </Typography>
            {machines.length === 0 ? (
              <EmptyData />
            ) : (
              <Grid container spacing={2}>
                {machines.map((options) => (
                  <Grid item xs={12} sm={6} key={options.mid}>
                    <StyledCard
                      onClick={() => handleMachineSelect(options)}
                      currentColorLight={currentColorLight}
                      currentMode={currentMode}
                    >
                      <StyledCardContent>
                        <Typography
                          variant="h6"
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                          }}
                        >
                          {options.data.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            display: "-webkit-box",
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: "vertical",
                          }}
                        >
                          {options.data.desc}
                        </Typography>
                      </StyledCardContent>
                    </StyledCard>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        )}
        {count === 1 && (
          <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Machine: {selMachine.data.title}
            </Typography>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Select Maintenance Type
            </Typography>
            <Grid container spacing={2}>
              {[
                "Calibration",
                "Machine Breakdown",
                "Routine",
                "Preventive",
              ].map((type, index) => (
                <Grid item xs={12} sm={6} key={index}>
                  <StyledCard
                    onClick={() => handleMaintenanceSelect(index)}
                    currentColorLight={currentColorLight}
                    currentMode={currentMode}
                  >
                    <StyledCardContent>
                      <Typography variant="h6">{type}</Typography>
                    </StyledCardContent>
                  </StyledCard>
                </Grid>
              ))}
            </Grid>
          </Box>
        )}
        {count >= 2 && (
          <Box>
            <Typography variant="h6" sx={{ mb: 1 }}>
              Machine: {selMachine.data.title}
            </Typography>
            <Typography variant="h6" sx={{ mb: 1 }}>
              Maintenance Type: {mType[maintType]}
            </Typography>
            {count === 2 && JSON.stringify(maintenance) === "{}" ? (
              <>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Select Maintenance
                </Typography>
                {maintForMach.length === 0 ? (
                  <EmptyData />
                ) : (
                  <Grid container spacing={2}>
                    {maintForMach.map((options) => (
                      <Grid item xs={12} sm={6} key={options.maintenance_id}>
                        <StyledCard
                          onClick={() => {
                            setMaintenance(options);
                            setCount(count + 1);
                          }}
                          currentColorLight={currentColorLight}
                          currentMode={currentMode}
                        >
                          <StyledCardContent>
                            <Typography
                              variant="h6"
                              sx={{
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                              }}
                            >
                              {options.data.title}
                            </Typography>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                display: "-webkit-box",
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: "vertical",
                              }}
                            >
                              {options.data.desc}
                            </Typography>
                          </StyledCardContent>
                        </StyledCard>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </>
            ) : (
              <>
                {count === 3 && (
                  <Box
                    sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}
                  >
                    <ButtonBasic
                      buttonTitle="Add Hashtag to Step"
                      onClick={() => setCount(count + 1)}
                    />
                  </Box>
                )}
                {count === 3 ? (
                  <AddData
                    data={maintenance}
                    addFun={handleAddHashTag}
                    setVal={setHA}
                    val={hashtagAdded}
                    currentColorLight={currentColorLight}
                    currentMode={currentMode}
                  />
                ) : (
                  <Steps
                    maintType={maintType}
                    stepArray={stepArray}
                    data={maintenance}
                    count={count}
                    setCount={setCount}
                    setOpen={setOpen}
                    currentColorLight={currentColorLight}
                    currentMode={currentMode}
                  />
                )}
              </>
            )}
          </Box>
        )}
      </StyledDialogContent>
    </StyledDialog>
  );
};

const EmptyData = () => (
  <Typography
    variant="h6"
    color="text.secondary"
    sx={{ textAlign: "center", py: 4 }}
  >
    No data available to proceed further.
  </Typography>
);

const AddData = ({
  data,
  addFun,
  setVal,
  val,
  currentColorLight,
  currentMode,
}) => {
  const { setOpen } = useContext(ModalContext);

  return (
    <Box>
      <Typography variant="h5" sx={{ mb: 1 }}>
        Maintenance: {data.data.title}
      </Typography>
      <StyledCard
        currentColorLight={currentColorLight}
        currentMode={currentMode}
        sx={{ mb: 3 }}
      >
        <StyledCardContent>
          <Typography
            variant="h6"
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {data.data.title}
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
            }}
          >
            {data.data.desc}
          </Typography>
        </StyledCardContent>
      </StyledCard>
      <FormContainer currentMode={currentMode}>
        <TextField
          fullWidth
          variant="outlined"
          value={val}
          onChange={(e) => setVal(e.target.value)}
          placeholder="Enter Hashtag"
          sx={{ mb: 2 }}
          error={val.trim() === "" && val.length > 0} // Highlight empty input
          helperText={
            val.trim() === "" && val.length > 0 ? "Hashtag cannot be empty" : ""
          }
        />
        <Box sx={{ display: "flex", gap: 2 }}>
          <ButtonBasic
            buttonTitle="SUBMIT"
            onClick={addFun}
            disabled={val.trim() === ""}
          />
          <ButtonBasic
            buttonTitle="SAVE AND CLOSE"
            onClick={() => setOpen(false)}
          />
        </Box>
      </FormContainer>
    </Box>
  );
};

const Steps = ({
  maintType,
  stepArray,
  data,
  count,
  setCount,
  setOpen,
  currentColor,
  currentMode,
}) => {
  const [stepArr, setStepArr] = useState([]);
  const { step, setStep } = useContext(ModalContext);

  useEffect(() => {
    const array = stepArray.filter(
      (options) => options?.manual_id === data.maintenance_id,
    );
    setStepArr([...array].sort((a, b) => a.index - b.index));
  }, [stepArray, data.maintenance_id]);

  return (
    <>
      <Typography variant="h5" sx={{ mb: 2 }}>
        Maintenance: {data.data.title}
      </Typography>
      {count === 4 && (
        <>
          {stepArr.length === 0 ? (
            <EmptyData />
          ) : (
            <Grid container spacing={2}>
              {stepArr.map((options) => (
                <Grid item xs={12} sm={6} key={options.id}>
                  <StyledCard
                    onClick={() => {
                      setStep(options);
                      setCount(count + 1);
                    }}
                    currentColor={currentColor}
                    currentMode={currentMode}
                  >
                    <StyledCardContent>
                      <Typography
                        variant="h6"
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {options.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          display: "-webkit-box",
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: "vertical",
                        }}
                      >
                        {options.desc}
                      </Typography>
                    </StyledCardContent>
                  </StyledCard>
                </Grid>
              ))}
            </Grid>
          )}
        </>
      )}
      {count === 5 && (
        <AddStepData
          step={step}
          setOpen={setOpen}
          maintType={maintType}
          currentColor={currentColor}
          currentMode={currentMode}
        />
      )}
    </>
  );
};

const AddStepData = ({
  step,
  setOpen,
  maintType,
  currentColor,
  currentMode,
}) => {
  const { addFun, val, setVal, hashtagArray, setHashtagArray } =
    useContext(ModalContext);

  const removeHashtag = (index) => {
    setHashtagArray(hashtagArray?.filter((_, i) => i !== index));
  };

  const handleButtonClick = () => {
    if (val?.trim() !== "") {
      setHashtagArray((prevState) => [...prevState, val]);
      setVal("");
    }
  };

  useEffect(() => {
    if (step?.hashtag?.length > 0) {
      const fetchHashtags = async () => {
        try {
          const promises = step.hashtag.map((item) =>
            axios.get(`${dbConfig.url}/hashtags/${item}`),
          );
          const responses = await Promise.all(promises);
          const titles = responses.map((res) => res.data.data.title);
          setHashtagArray(titles);
        } catch (error) {
          console.error("Error fetching hashtags:", error);
        }
      };
      fetchHashtags();
    }
  }, [step, setHashtagArray]);

  return (
    <Box>
      <Typography variant="h5" sx={{ mb: 1 }}>
        Step: {step.title}
      </Typography>
      <StyledCard
        currentColor={currentColor}
        currentMode={currentMode}
        sx={{ mb: 3 }}
      >
        <StyledCardContent>
          <Typography
            variant="h6"
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {step.title}
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
            }}
          >
            {step.desc}
          </Typography>
        </StyledCardContent>
      </StyledCard>
      <FormContainer currentMode={currentMode}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 2 }}>
          <TextField
            fullWidth
            variant="outlined"
            value={val}
            onChange={(e) => setVal(e.target.value)}
            placeholder="Enter Hashtag"
          />
          <IconButton onClick={handleButtonClick} color="primary">
            <AddIcon />
          </IconButton>
        </Box>
        <HashtagList>
          {hashtagArray?.map((item, index) => (
            <HashtagItem key={index} currentMode={currentMode}>
              <Typography variant="body1">{item}</Typography>
              <IconButton onClick={() => removeHashtag(index)} color="error">
                <DeleteIcon />
              </IconButton>
            </HashtagItem>
          ))}
        </HashtagList>
        <ButtonBasic buttonTitle="SUBMIT" onClick={addFun} />
      </FormContainer>
    </Box>
  );
};

export default ModalDiv;
