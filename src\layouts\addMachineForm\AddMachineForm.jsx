import React, { useEffect, useState } from "react";
import { alpha, styled } from "@mui/material/styles";
import {
  <PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>le,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputBase,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import "./addMachineForm.scss";
import { Link, useNavigate } from "react-router-dom";
import { db } from "../../firebase";
import {
  companies,
  companyId_constant,
  liveData,
  machines,
  maintenance,
  steps,
  training,
} from "../../constants/data";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { useAuth } from "../../hooks/AuthProvider";
import { useStateContext } from "../../context/ContextProvider";
import { firebaseLooper } from "../../tools/tool";
import { Box } from "@mui/system";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";
import { data } from "autoprefixer";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useCreateMachineCfr } from "../../hooks/cfr/machineCfrProvider";
import { useMachinesSetter } from "../../services3/machines/MachineContext2";
import { useUtils } from "../../hooks/UtilsProvider";
import NotAccessible from "../../components/not-accessible/not-accessible";
import { useCheckAccess } from "../../utils/useCheckAccess";

// const ROOT = db.collection(companies).doc(companyId_constant);

const BootstrapInput = styled(InputBase)(({ theme }) => ({
  "label + &": {
    marginTop: theme.spacing(0),
  },
  "& .MuiInputBase-input": {
    borderRadius: 4,
    position: "relative",
    backgroundColor: theme.palette.mode === "light" ? "#fcfcfb" : "#161C24",
    border: "1px solid #ced4da",
    fontSize: 14,
    color: theme.palette.mode === "light" ? "#344767" : "#fcfcfb",
    padding: "12px",
    transition: theme.transitions.create([
      "border-color",
      "background-color",
      "box-shadow",
    ]),
    "&:focus": {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main,
    },
  },
}));

const AddMachineForm = () => {
  const addmachinecfr = useCreateMachineCfr();
  const allMachinesSetter = useMachinesSetter();
  const { currentMode, currentColor } = useStateContext();
  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");
  const [location, setLocation] = useState("");
  const [equipmentId, setEquipmentId] = useState("");
  const [serialNo, setSerialNo] = useState("");
  const [model, setModel] = useState("");
  const [block, setBlock] = useState("");
  const [warranty, setWarranty] = useState("");
  const [status, setStatus] = useState("");
  const [machinesDetails, setMachines] = useState([]);
  const [newMachineId, setNewMachineId] = useState("");
  const [cloneMachineId, setCloneMachineId] = useState("");
  const [openClone, setOpenClone] = useState(false);
  const [machineDetails, setMachineDetails] = useState([]);
  const [wait, setWait] = useState(false);
  const [cloneComplete, setCloneComplete] = useState(false);
  const [mClone, setMClone] = useState(false);
  const history = useNavigate();

  const { currentUser } = useAuth();
  const { envData } = useUtils();

  useEffect(() => {}, []);

  const handledataClone = (e) => {
    const mid = e.target.value;
    setMClone(true);
  };
  //CREATE NEW MACHINE
  const checkForDuplicates = async (checkEquipmentId, checkSerialNo) => {
    try {
      const response = await axios.get(`${dbConfig.url}/machines`);

      const compareArray = response.data?.data.map(
        ({ equipmentId, serialNo }) => ({ equipmentId, serialNo }),
      );
      const isDuplicatePresent = compareArray.some(
        ({ equipmentId, serialNo }) =>
          equipmentId === checkEquipmentId || serialNo === checkSerialNo,
      );

      return isDuplicatePresent; // Returns true if duplicates exist
    } catch (error) {
      console.error("Error checking for duplicates:", error);
      return false;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Ensure currentUser is valid
    if (!currentUser || !currentUser.email) {
      toastMessage({
        message: "User authentication error. Please log in again.",
      });
      return;
    }

    // Check for duplicates
    const isDuplicate = await checkForDuplicates(equipmentId, serialNo);
    if (isDuplicate) {
      toastMessage({
        message:
          "A machine with the same Equipment ID or Serial No already exists!",
      });
      return;
    }

    if (mClone) {
      setWait(true);
    }

    const machineNew = {
      title: title.trim(),
      desc: desc.trim(),
      location: location.trim(),
      equipment_id: equipmentId.trim(),
      serial_no: serialNo.trim(),
      model: model.trim(),
      block: block.trim(),
      warranty: warranty.trim(),
      status: status.trim(),
      created_by: currentUser.email,
      created_at: new Date(), // Explicitly send current date
    };

    if (mClone) {
      // Handle cloning logic here
    } else {
      const date = new Date();
      const data = {
        activity: "machine created",
        dateTime: date,
        description: "a machine is created",
        machine: " ",
        module: "Machine",
        username: currentUser.username,
      };

      await axios
        .post(`${dbConfig.url}/machines`, machineNew)
        .then((res) => {
          toastMessageSuccess({ message: "Machine Created successfully!" });
          addmachinecfr({ ...data, machine: res.data.data._id });
          allMachinesSetter();
          history("/machines");
        })
        .catch((err) => {
          toastMessage({ message: err.message });
        });
    }
  };

  return (
    <>
      {useCheckAccess("machines", "POST") ? (
        <section className="addMachineForm">
          <div
            className="addMachineFormContainer"
            style={
              currentMode === "Dark"
                ? {
                    backgroundColor: "#161C24",
                    color: "white",
                    border: "1px solid white",
                  }
                : { border: "1px solid black" }
            }
          >
            <div
              style={{ display: "flex", justifyContent: "space-between" }}
              className="title"
            >
              <h3>New Machines</h3>
              {/* <Box
              component="form"
              sx={{
                '& > :not(style)': { width: '25ch' },
              }}
            >
              <InputLabel id="demo-simple-select-label">Clone from existing Machine</InputLabel>
              <Select onChange={handledataClone} variant='outlined' size="medium" fullWidth label="Clone from existing Machine">
                {machinesDetails.map((data) => (
                  <MenuItem key={data.id} value={data.id}>{data.title}</MenuItem>
                ))}
              </Select>
            </Box> */}
            </div>
            <div className="desc">
              <p>Add information about new machine</p>
            </div>
            {/* onSubmit={() => setOpenCloneModule(true)} */}
            <form onSubmit={handleSubmit} className="machineFormContainer">
              <div className="flex justify-between">
                <div className="labelFields w-5/12">
                  <InputLabel
                    style={
                      currentMode === "Dark"
                        ? {
                            color: "white",
                            marginBottom: "4px",
                            fontSize: "16px",
                          }
                        : { marginBottom: "4px", fontSize: "16px" }
                    }
                    htmlFor="machineName"
                  >
                    Machine Name
                  </InputLabel>
                  <BootstrapInput
                    onChange={(e) => setTitle(e.target.value)}
                    onBlur={() => setTitle(title?.trim())}
                    value={title}
                    id="machineName"
                    placeholder="Add machine name here ..."
                    required
                  />
                </div>
                <div className="labelFields w-5/12">
                  <InputLabel
                    style={
                      currentMode === "Dark"
                        ? {
                            color: "white",
                            marginBottom: "4px",
                            fontSize: "16px",
                          }
                        : { marginBottom: "4px", fontSize: "16px" }
                    }
                    htmlFor="equipment"
                  >
                    Equipment ID
                  </InputLabel>
                  <BootstrapInput
                    onChange={(e) => setEquipmentId(e.target.value)}
                    onBlur={() => setEquipmentId(equipmentId?.trim())}
                    value={equipmentId}
                    id="equipment"
                    placeholder="Add equipment ID here ..."
                    required
                  />
                </div>
              </div>

              <div className="flex justify-between">
                <div className="labelFields w-5/12">
                  <InputLabel
                    style={
                      currentMode === "Dark"
                        ? {
                            color: "white",
                            marginBottom: "4px",
                            fontSize: "16px",
                          }
                        : { marginBottom: "4px", fontSize: "16px" }
                    }
                    htmlFor="serialNo"
                  >
                    Serial No.
                  </InputLabel>
                  <BootstrapInput
                    onChange={(e) => setSerialNo(e.target.value)}
                    onBlur={() => setSerialNo(serialNo?.trim())}
                    value={serialNo}
                    id="equipment"
                    placeholder="Add serial no here ..."
                    required
                  />
                </div>
                <div className="labelFields w-5/12">
                  <InputLabel
                    style={
                      currentMode === "Dark"
                        ? {
                            color: "white",
                            marginBottom: "4px",
                            fontSize: "16px",
                          }
                        : { marginBottom: "4px", fontSize: "16px" }
                    }
                    htmlFor="model"
                  >
                    Model
                  </InputLabel>
                  <BootstrapInput
                    onChange={(e) => setModel(e.target.value)}
                    onBlur={() => setModel(model?.trim())}
                    value={model}
                    id="model"
                    placeholder="Add model here ..."
                    required
                  />
                </div>
              </div>

              <div className="flex justify-between">
                <div className="labelFields w-5/12">
                  <InputLabel
                    style={
                      currentMode === "Dark"
                        ? {
                            color: "white",
                            marginBottom: "4px",
                            fontSize: "16px",
                          }
                        : { marginBottom: "4px", fontSize: "16px" }
                    }
                    htmlFor="block"
                  >
                    Block
                  </InputLabel>
                  <BootstrapInput
                    onChange={(e) => setBlock(e.target.value)}
                    onBlur={() => setBlock(block?.trim())}
                    value={block}
                    id="block"
                    placeholder="Add block here ..."
                    required
                  />
                </div>

                <div className="labelFields w-5/12">
                  <InputLabel
                    style={
                      currentMode === "Dark"
                        ? {
                            color: "white",
                            marginBottom: "4px",
                            fontSize: "16px",
                          }
                        : { marginBottom: "4px", fontSize: "16px" }
                    }
                    htmlFor="machineLocation"
                  >
                    Machine Location
                  </InputLabel>
                  <BootstrapInput
                    onChange={(e) => setLocation(e.target.value)}
                    onBlur={(e) => setLocation(location?.trim())}
                    value={location}
                    id="machineLocation"
                    placeholder="Write machine location here...."
                    required
                  />
                </div>
              </div>

              <div className="flex justify-between">
                <div className="labelFields w-5/12">
                  <InputLabel
                    style={
                      currentMode === "Dark"
                        ? {
                            color: "white",
                            marginBottom: "4px",
                            fontSize: "16px",
                          }
                        : { marginBottom: "4px", fontSize: "16px" }
                    }
                    htmlFor="warranty"
                  >
                    Warranty
                  </InputLabel>
                  <BootstrapInput
                    onChange={(e) => setWarranty(e.target.value)}
                    onBlur={() => setWarranty(warranty?.trim())}
                    value={warranty}
                    id="warranty"
                    placeholder="Add warranty here ..."
                    required
                  />
                </div>
                <div className="labelFields w-5/12">
                  <InputLabel
                    style={
                      currentMode === "Dark"
                        ? {
                            color: "white",
                            marginBottom: "4px",
                            fontSize: "16px",
                          }
                        : { marginBottom: "4px", fontSize: "16px" }
                    }
                    htmlFor="status"
                  >
                    Status
                  </InputLabel>
                  <BootstrapInput
                    onChange={(e) => setStatus(e.target.value)}
                    onBlur={() => setStatus(status?.trim())}
                    value={status}
                    id="status"
                    placeholder="Add status here ..."
                    required
                  />
                </div>
              </div>
              <div className="labelFields">
                <InputLabel
                  style={
                    currentMode === "Dark"
                      ? {
                          color: "white",
                          marginBottom: "4px",
                          fontSize: "16px",
                        }
                      : { marginBottom: "4px", fontSize: "16px" }
                  }
                  htmlFor="machineDesc"
                >
                  Machine Description
                </InputLabel>
                <BootstrapInput
                  onChange={(e) => setDesc(e.target.value)}
                  onBlur={() => setDesc(desc?.trim())}
                  value={desc}
                  id="machineDesc"
                  placeholder="Write details about machine here ..."
                  required
                />
              </div>
              <div className=" mt-4 flex justify-between">
                <Link to="/machines">
                  {/* <button className="cancelBtn btn">Cancel </button> */}
                  <ButtonBasicCancel buttonTitle="Cancel" type="button" />
                </Link>
                {/* <button
                type="submit"
                className="createBtn btn"
                style={{ backgroundColor: currentColor }}
              >Create Machine</button> */}
                <ButtonBasic buttonTitle="Submit" type="submit" />
              </div>
            </form>
            {/* Confirm action on cloning the machine */}
            <Dialog open={wait} fullWidth>
              <DialogTitle>Please Wait .....</DialogTitle>
              <DialogContent>
                {!cloneComplete ? (
                  <LinearProgress variant="indeterminate" />
                ) : (
                  <>
                    <Typography variant="body1">
                      Cloning of Data Completed
                    </Typography>
                    <div>
                      <Button onClick={() => history("/machines")}>
                        Continue
                      </Button>
                    </div>
                  </>
                )}
                <br />
                <Alert
                  variant="filled"
                  severity={cloneComplete ? `success` : `warning`}
                >
                  <AlertTitle>
                    {cloneComplete
                      ? `Cloning has been completed . you may continue `
                      : `  Please Do not redirect while cloning. It may hamper the data cloning process`}
                  </AlertTitle>
                </Alert>
              </DialogContent>
            </Dialog>

            {/* On confirm - Tick mark all the possible cloning */}
            <Dialog open={openClone} fullWidth>
              <DialogTitle>Select Modules you want to clone :</DialogTitle>
              <DialogContent>{newMachineId}</DialogContent>
            </Dialog>
          </div>
        </section>
      ) : (
        <NotAccessible />
      )}
    </>
  );
};

export default AddMachineForm;
