import React, { useState, useMemo, useContext } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "../TableTempletes.css";

import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import TextField from "@mui/material/TextField";

import { useStateContext } from "../../../../context/ContextProvider";
import { db, storage } from "../../../../firebase";
import { companies, companyId_constant } from "../../../../constants/data";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../../../components/buttons/Buttons";

import { useStorageTablesFile } from "../../../../utils/useStorageTablesFile";
import { InputLabel, LinearProgress, Link, Typography } from "@mui/material";
import { Empty } from "antd";
import { FileDownload, ImageSearchOutlined } from "@mui/icons-material";
import {
  FileImageFilled,
  FilePdfFilled,
  PictureFilled,
} from "@ant-design/icons";
import { DeleteByUrl } from "../../../../utils/StorageOptions";
import { DropzoneArea } from "material-ui-dropzone";
import { FileManagerSelectorContext } from "../../../../services/fileManager/file-manager-select.context";
import FileSelector from "../../../FileSelector/screens/FileSelector";
import PreviewIcon from "@mui/icons-material/Preview";
import axios from "axios";
import { dbConfig } from "../../../../infrastructure/db/db-config";
import { useContentEditCount } from "../../../../services3/audits/ContentContext";

export default function Execution_Table_12_6_5({
  rowData,
  type,
  machineName,
  fatDataDocId,
  tableStaticData,
  useAt,
}) {
  const { currentMode, currentColorLight } = useStateContext();
  const [open, setOpen] = useState(false);
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");
  const [open2, setOpen2] = useState(false);

  const handleClickOpen2 = (data) => {
    //setRowDataSelected(data);
    setOpen2(true);
  };

  const handleClose2 = () => {
    setOpen2(false);
  };

  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useMemo(() => {
    // when theme changes then S.No array got reversed. So to stop that This Memonisation is used.
    setSortDirection("");
    //console.log("TableTemplates usememo")
  }, [currentMode]);

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#161c24" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            <TableRow
              sx={{
                border: theme.borderDesign,
                background: theme.backgroundColor,
              }}
            >
              <TableCell sx={{ border: theme.borderDesign }}>From</TableCell>
              <TableCell sx={{ border: theme.borderDesign }} align="center">
                To
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Acceptance Criteria
              </TableCell>

              <TableCell sx={{ border: theme.borderDesign }}>
                Observation (Pass / Fail)
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  sx={{
                    border: theme.borderDesign,
                    background: currentMode === "Dark" ? "#212b36" : "",
                  }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[2]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[3]}
                  </TableCell>
                </TableRow>
              ),
            )}

            {/* //bottom section of the table */}
            <TableRow
              onClick={() => handleClickOpen2()}
              sx={{
                border: theme.borderDesign,
              }}
            >
              <TableCell sx={{ border: theme.borderDesign }} colSpan={4}>
                <b> Remark :</b> {tableStaticData?.remark}
              </TableCell>
            </TableRow>

            <TableRow
              onClick={() => handleClickOpen2()}
              sx={{
                border: theme.borderDesign,
                background: theme.backgroundColor,
              }}
            >
              <TableCell
                sx={{ border: theme.borderDesign }}
                colSpan={2}
                align="center"
              >
                <b>DD ENTERPRISES</b>
              </TableCell>
              <TableCell
                sx={{ border: theme.borderDesign }}
                colSpan={2}
                align="center"
              >
                <b>AMNEAL ONCOLOGY PVT LTD</b>
              </TableCell>
            </TableRow>

            <TableRow
              onClick={() => handleClickOpen2()}
              sx={{
                border: theme.borderDesign,
                background: theme.backgroundColor,
              }}
            >
              <TableCell
                sx={{ border: theme.borderDesign }}
                colSpan={2}
                align="center"
              >
                <b>Checked by (Sign/Date)</b>
              </TableCell>
              <TableCell
                sx={{ border: theme.borderDesign }}
                colSpan={2}
                align="center"
              >
                <b>Verified By(Sign/Date)</b>
              </TableCell>
            </TableRow>

            <TableRow
              onClick={() => handleClickOpen2()}
              sx={{
                border: theme.borderDesign,
              }}
            >
              <TableCell
                sx={{ border: theme.borderDesign }}
                height="50vh"
                colSpan={2}
                rowSpan={2}
              >
                {tableStaticData?.checked_by}
                {tableStaticData?.checked_by ? "/" : ""}
                {tableStaticData?.checked_by_date}
              </TableCell>
              <TableCell
                sx={{ border: theme.borderDesign }}
                height="50vh"
                colSpan={2}
                rowSpan={2}
              >
                {tableStaticData?.verified_by}
                {tableStaticData?.verified_by ? "/" : ""}
                {tableStaticData?.verified_by_date}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
          <EditTableRow
            rowDataSelected={rowDataSelected}
            type={type}
            fatDataDocId={fatDataDocId}
            machineName={machineName}
            handleClose={handleClose}
          />
        </DialogContent>
      </Dialog>

      {/* //2. Dialog for extra info in Header, Remark and Footer of the table. First we create table
            // then we fill these enformation */}

      <Dialog
        maxWidth="lg"
        open={open2}
        onClose={handleClose2}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle id="alert-dialog-title">{"Table Data"}</DialogTitle>
          <TableExtraInfo
            rowData={rowData}
            type={type}
            fatDataDocId={fatDataDocId}
            machineName={machineName}
            handleClose={handleClose2}
            tableStaticData={tableStaticData}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}

////////////////// edit row need to be changed as above

function EditTableRow({
  rowDataSelected,
  tableType,
  type,
  machineName,
  fatDataDocId,
  handleClose,
}) {
  const { currentMode } = useStateContext();

  const [sNo, setSNo] = useState(rowDataSelected[0]);
  // const [tagNo, setTagNo] = useState(rowDataSelected[1]); //
  const [description, setDescription] = useState(rowDataSelected[1]);
  const [makeVendor, setMakeVendor] = useState(rowDataSelected[2]);
  const [typeIq, setTypeIq] = useState(rowDataSelected[3]); // type
  const [size, setSize] = useState(rowDataSelected[4]);
  const [documents, setDocuments] = useState(rowDataSelected[5]);
  const [result, setResult] = useState(rowDataSelected[6]); // t1
  const [urlIq1, setUrlIq1] = useState(rowDataSelected[7]); // 7
  const { fileUrl, changeFileUrl } = useContext(FileManagerSelectorContext);

  const [fileType, setFileType] = useState("");
  const [file, setFile] = useState(null);
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];
  const [fType, setFType] = useState("index" in rowDataSelected ? true : false);
  const [index, setIndex] = useState(rowDataSelected[9]);

  //
  const handleChangeImage = (file) => {
    let selectedFile = file[0];
    //console.log(file[0]) //(selectedFile?.size/1024))

    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        // delete last uploaded file via url if image changes
        if (url) {
          DeleteByUrl(url);
        }
        //
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };
  // const { currentColor, currentMode } = useStateContext();
  const { progress, url } = useStorageTablesFile(file);

  //
  const handleUpdateRow = async () => {
    if (fType && isNaN(parseInt(index))) {
      toastMessageWarning({ message: "The input of index should be a Number" });
      return;
    }
    let data = {
      fat_id: fatDataDocId,
      table_type: "Execution_Table_12_6_5",
      serial_no: sNo,
      desc: description,
      make_vendor: makeVendor,
      type: typeIq,
      size: size,
      documents: documents,
      result: result,
      index: index,
      url: fileUrl === null ? urlIq1 : fileUrl,
    };

    await axios
      .put(`${dbConfig.url}/generalTable`, data)
      .then((res) => {
        toastMessageSuccess({ message: "Row updated Successfully" });
      })
      .catch((err) => {});
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(type)
    //   .doc(fatDataDocId)
    //   .collection("table" + tableType)
    //   .doc(rowDataSelected[8]) // rowDataSelected[9] = id of the document
    //   .update(data)
    //   .then(() => {
    //     toastMessageSuccess({ message: "Row updated Successfully" });
    //     handleClose();
    //   })
    //   .catch((e) => {
    //     toastMessageWarning({ message: "Error ", e });
    //   });
  };

  const handleCancel = () => {
    if (url) {
      DeleteByUrl(url);
    }
    handleClose();
  };

  const handleDeleteDropZone = (url) => {
    DeleteByUrl(url); // to delete the file from storage
    setFile(null); // to remove the preview
  };

  return (
    <>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          <div className="flex justify-between py-2">
            <div className="w-1/12">
              <TextField
                label="S.No"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={sNo}
                onChange={(e) => setSNo(e.target.value)}
              />
            </div>

            <div className="w-2/12">
              <TextField
                label="Description"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
              />
            </div>
            <div className="w-1/12">
              <TextField
                label="Make/Vendor"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={makeVendor}
                onChange={(e) => setMakeVendor(e.target.value)}
              />
            </div>
            <div className="4/12">
              <TextField
                title={rowDataSelected[4]}
                label="Type"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={typeIq}
                onChange={(e) => setTypeIq(e.target.value)}
              />
            </div>
            <div className="w-1/12">
              <TextField
                label="Size"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={size}
                onChange={(e) => setSize(e.target.value)}
              />
            </div>
            <div className="w-1/12">
              <TextField
                label="Document"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={documents}
                onChange={(e) => setDocuments(e.target.value)}
              />
            </div>
            <div className="w-1/12">
              <TextField
                label="Result"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={result}
                onChange={(e) => setResult(e.target.value)}
              />
            </div>
            {fType && (
              <div className="w-2/12">
                <TextField
                  label="Index"
                  id="outlined-size-small"
                  defaultValue="Na"
                  size="small"
                  value={index}
                  onChange={(e) => setIndex(e.target.value)}
                />
              </div>
            )}
          </div>
        </DialogContentText>
        <FileSelector />
        <Typography sx={{ ml: "20px" }} align="left">
          {" "}
          <PreviewIcon /> Preview
        </Typography>
        <img width="250px" src={urlIq1} />
      </DialogContent>
      <DialogActions>
        <ButtonBasic buttonTitle="Update" onClick={handleUpdateRow} />
        <ButtonBasicCancel
          buttonTitle="Cancel &#x2716;"
          onClick={handleCancel}
        />
      </DialogActions>
    </>
  );
}

// Add/Edit extra info(header & footer) of the table (Header,Remark,Footer)
function TableExtraInfo({
  rowData,
  type,
  fatDataDocId,
  machineName,
  handleClose,
  tableStaticData,
}) {
  const { contentEditCount, setContentEditCount } = useContentEditCount();
  const [tagNo, setTagNo] = useState(tableStaticData?.tag_no);
  const [maxAllowableError, setMaxAllowableError] = useState(
    tableStaticData?.max_allowable_error,
  );
  const [dateOfVerification, setDateOfVerification] = useState(
    tableStaticData?.date_of_verification,
  );
  const [measuringRange, setMeasuringRange] = useState(
    tableStaticData?.measuring_range,
  );
  const [standardInstrumentUsed, setStandardInstrumentUsed] = useState(
    tableStaticData?.standard_instrument_used,
  );
  const [sNo, setSNo] = useState(tableStaticData?.s_no);
  const [calDoneOn, setCalDoneOn] = useState(tableStaticData?.cal_done_on);
  const [calDueOn, setCalDueOn] = useState(tableStaticData?.cal_due_on);

  const [remark, setRemark] = useState(tableStaticData?.remark);
  const [checkedBy, setCheckedBy] = useState(tableStaticData?.checked_by);
  const [checkedByDate, setCheckedByDate] = useState(
    tableStaticData?.checked_by_date,
  );
  const [verifiedBy, setVerifiedBy] = useState(tableStaticData?.verified_by);
  const [verifiedByDate, setVerifiedByDate] = useState(
    tableStaticData?.verified_by_date,
  );

  const handleAddTableExtraInfo = async () => {
    console.log("executionnnnnn:", rowData);
    const data = {
      mid: "",
      fat_id: fatDataDocId,
      table_type: type,
      table_title: rowData?.length ? rowData[0][4] : "", // index will change
      table_id: "", // remove this from backend also

      // "tag_no": tagNo,
      // "max_allowable_error": maxAllowableError,
      // "date_of_verification": dateOfVerification,
      // "measuring_range": measuringRange,
      // "standard_instrument_used": standardInstrumentUsed,
      // "s_no": sNo,
      // "cal_done_on": calDoneOn,
      // "cal_due_on": calDueOn,

      remark: remark,
      checked_by: checkedBy,
      checked_by_date: checkedByDate,
      verified_by: verifiedBy,
      verified_by_date: verifiedByDate,
    };

    if (tableStaticData?._id) {
      await axios
        .put(
          `${dbConfig.url}/generalTableStaticData/${tableStaticData?._id}`,
          data,
        )
        .then(() => {
          //   edittrainingcfr(data2);
          toastMessageSuccess({ message: `${type}has been updated` });
          handleClose();
          setContentEditCount(contentEditCount + 1);
        });
    } else {
      await axios
        .post(`${dbConfig.url}/generalTableStaticData`, data)
        .then(() => {
          //   edittrainingcfr(data2);
          toastMessageSuccess({ message: `${type}has been added` });
          handleClose();
          //setRefreshCount(refreshCount + 1);
        });
    }
  };
  return (
    <>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          {/* <div className="flex justify-between py-2">
                        <div className="w-5/12">
                            <TextField
                                label="Tag no."
                                id="outlined-size-small"
                                size="small"
                                value={tagNo}
                                onChange={(e) => setTagNo(e.target.value)}
                            />
                        </div>

                        <div className="w-5/12">
                            <TextField
                                label="Maximum allowable error"
                                id="outlined-size-small"
                                size="small"
                                value={maxAllowableError}
                                onChange={(e) => setMaxAllowableError(e.target.value)}
                            />
                        </div>
                    </div> */}

          {/* <div className="flex justify-between py-2">
                        <div className="w-5/12">
                            <TextField
                                label="Standard Instrument used"
                                id="outlined-size-small"
                                size="small"
                                value={standardInstrumentUsed}
                                onChange={(e) => setStandardInstrumentUsed(e.target.value)}
                            />
                        </div>

                        <div className="w-5/12">
                            <TextField
                                label="Sr No."
                                id="outlined-size-small"
                                size="small"
                                value={sNo}
                                onChange={(e) => setSNo(e.target.value)}
                            />
                        </div>
                    </div> */}

          {/* <div className="flex justify-between py-2">
                        <div className="w-5/12">
                            <TextField
                                label="Date of verification"
                                id="outlined-size-small"
                                size="small"
                                value={dateOfVerification}
                                onChange={(e) => setDateOfVerification(e.target.value)}
                            />
                        </div>

                        <div className="w-5/12">
                            <TextField
                                label="Measuring range"
                                id="outlined-size-small"
                                size="small"
                                value={measuringRange}
                                onChange={(e) => setMeasuringRange(e.target.value)}
                            />
                        </div>
                    </div> */}

          {/* <div className="flex justify-between py-2">
                        <div className="w-5/12">
                            <TextField
                                label="Done on"
                                id="outlined-size-small"
                                size="small"
                                value={calDoneOn}
                                onChange={(e) => setCalDoneOn(e.target.value)}
                            />
                        </div>

                        <div className="w-5/12">
                            <TextField
                                label="Due on"
                                id="outlined-size-small"
                                size="small"
                                value={calDueOn}
                                onChange={(e) => setCalDueOn(e.target.value)}
                            />
                        </div>
                    </div> */}

          <div className="flex justify-between py-2 ">
            <div className="w-full">
              <TextField
                sx={{ width: "50ch" }}
                minRows={2}
                variant="outlined"
                label="Remark"
                size="small"
                value={remark}
                onChange={(e) => setRemark(e.target.value)}
              />
            </div>
          </div>

          <div className="flex justify-between py-2 ">
            <div className="w-5/12">
              <TextField
                label="Checked by"
                id="outlined-size-small"
                size="small"
                value={checkedBy}
                onChange={(e) => setCheckedBy(e.target.value)}
              />
            </div>
            <div className="w-5/12">
              <TextField
                label="Checked on"
                id="outlined-size-small"
                size="small"
                value={checkedByDate}
                onChange={(e) => setCheckedByDate(e.target.value)}
              />
            </div>
          </div>

          <div className="flex justify-between py-2 ">
            <div className="w-5/12">
              <TextField
                label="Verified by"
                id="outlined-size-small"
                size="small"
                value={verifiedBy}
                onChange={(e) => setVerifiedBy(e.target.value)}
              />
            </div>
            <div className="w-5/12">
              <TextField
                label="Verified on"
                id="outlined-size-small"
                size="small"
                value={verifiedByDate}
                onChange={(e) => setVerifiedByDate(e.target.value)}
              />
            </div>
          </div>
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <ButtonBasic
          buttonTitle="Add/Update"
          onClick={() => handleAddTableExtraInfo()}
        />
        <ButtonBasicCancel
          buttonTitle="Cancel &#x2716;"
          onClick={() => handleClose()}
        />
      </DialogActions>
    </>
  );
}
