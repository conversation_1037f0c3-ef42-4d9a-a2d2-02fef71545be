import { useState, useCallback } from "react";
import { subDays } from "date-fns";

const useDateFilter = () => {
  // Initial date range: past 30 days to today
  const initialDateRange = [
    {
      startDate: subDays(new Date(), 30),
      endDate: new Date(),
      key: "selection",
    },
  ];

  const [date, setDate] = useState(initialDateRange);
  const [shadowOnDate, setShadowOnDate] = useState("");
  const [colorOnDate, setColorOnDate] = useState("");

  const dateFilter = useCallback((item) => {
    setDate([item?.selection]);
    if (item) {
      setShadowOnDate("0px 0px 2px #000");
      setColorOnDate("red");
    } else {
      setShadowOnDate("");
      setColorOnDate("");
    }
  }, []);

  // Function to reset the date filter to initial state
  const resetDateFilter = useCallback(() => {
    setDate(initialDateRange);
    setShadowOnDate("");
    setColorOnDate("");
  }, []);

  return {
    date,
    shadowOnDate,
    colorOnDate,
    dateFilter,
    resetDateFilter, // Expose the reset function
  };
};

export default useDateFilter;
