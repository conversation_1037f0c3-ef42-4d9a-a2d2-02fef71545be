import { Box, Button, Grid, Typography } from "@mui/material";
import React, { useState } from "react";
import EquimentUsedTable from "./equipment-used.table";
import * as XLSX from "xlsx";
import { useEffect } from "react";
import { db } from "../../../firebase";
import { firebaseLooper } from "../../../tools/tool";
import {
  cmsInfra,
  companies,
  companyId_constant,
} from "../../../constants/data";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";

const CalibrationDataPrint = ({ item, masterEquip, report }) => {
  const [results, setResults] = useState([]);

  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(cmsInfra)
    //   .doc("AIvh9SJkujEq9SxN9Us5")
    //   .collection("sap_report")
    //   .doc(report?.id)
    //   .collection("result")
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     console.log("Reports : ", data);
    //     setResults(data);
    //   });
  }, []);

  const printRef = React.useRef();

  const handleDownloadPdf = async () => {
    const element = printRef.current;
    const canvas = await html2canvas(element);
    const data = canvas.toDataURL("image/png");

    const pdf = new jsPDF();
    const imgProperties = pdf.getImageProperties(data);
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = (imgProperties.height * pdfWidth) / imgProperties.width;

    pdf.addImage(data, "PNG", 0, 0, pdfWidth, pdfHeight);
    pdf.save("print.pdf");
  };

  return (
    <>
      <button type="button" onClick={handleDownloadPdf}>
        Download as PDF
      </button>
      <div
        style={{
          padding: 10,
          // maxWidth: "60%"
        }}
        ref={printRef}
      >
        <div
          style={{
            marginBottom: 20,
            padding: 20,
            border: "1px solid black",
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Typography variant="h5" align="left">
            Calibration Data Sheet
          </Typography>
          <Typography variant="body1" align="left">
            <b>Date :</b> 28/OCT/2022{" "}
          </Typography>
        </div>
        <Box sx={{ border: "1px solid black", display: "flex" }}>
          {/* <Grid sx={{p: 2 , borderRight: '1px solid black',width: '50%'}}>
                <Typography>
                    Customer: 
                </Typography>
            </Grid> */}
          <Grid sx={{ width: "100%" }}>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">
                Cal Centrificate No. : {item[0]?.certificate_no}
              </Typography>
            </Box>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">
                Date of Calibration : {item[0]?.done_on}
              </Typography>
            </Box>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">
                Calibration Due on : {item[0]?.due_date}
              </Typography>
            </Box>
            <Box sx={{ p: 2 }}>
              <Typography variant="body2">
                Criticality : {report?.criticality}
              </Typography>
            </Box>
          </Grid>
        </Box>
        <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
          <Typography variant="body2">
            Details of Unit Under Calibration :{" "}
          </Typography>
        </Box>
        <Box
          sx={{
            border: "1px solid black",
            display: "flex",
            justifyContent: "",
            borderTop: "none",
          }}
        >
          <Grid sx={{ borderRight: "1px solid black", width: "50%" }}>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">
                Instrument Name: {item[0]?.code_no}
              </Typography>
            </Box>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">Make : {item[0]?.make}</Typography>
            </Box>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">Model: {item[0]?.make}</Typography>
            </Box>
            {/* <Box sx={{p: 2, borderBottom:'1px solid black'}}>
                  <Typography>
                   Serial Number: 
                </Typography>  
                </Box> */}
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">Range: {item[0]?.range}</Typography>
            </Box>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">Environmental Condition: </Typography>
            </Box>
            <Box sx={{ p: 2 }}>
              <Typography variant="body2">ID Number: 00</Typography>
            </Box>
          </Grid>
          <Grid sx={{ width: "50%" }}>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">
                Location : {item[0]?.location}
              </Typography>
            </Box>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">Accuracy : NONE</Typography>
            </Box>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">
                Operating Range : {item[0]?.range}
              </Typography>
            </Box>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">
                Equipment / Plant : {item[0]?.plant}
              </Typography>
            </Box>
            <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
              <Typography variant="body2">LC/Resolution : NONE</Typography>
            </Box>
          </Grid>
        </Box>
        <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
          <Typography variant="body2">
            Calibration Procedure Number : {report?.cal_p_no}{" "}
          </Typography>
        </Box>

        {/*Master Equipment used starts */}

        <Box>
          {/* 5 boxes */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              border: "1px solid black",
              borderTop: "none",
            }}
          >
            {/* Sr no0 */}
            <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
              <Typography variant="body2" align="center">
                Name{" "}
              </Typography>
            </Box>
            <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
              <Typography variant="body2" align="center">
                Make / Model{" "}
              </Typography>
            </Box>

            <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
              <Typography variant="body2" align="center">
                SI Number
              </Typography>
            </Box>
            <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
              <Typography variant="body2" align="center">
                Certificate Number
              </Typography>
            </Box>
            <Box sx={{ p: 1, width: `25%` }}>
              <Typography variant="body2" align="center">
                Validity
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* equipments mapping area */}
        {masterEquip.map((item, idx) => (
          <EquipmentTableItem masterEquip={item} />
        ))}

        {/* Table ends master equipment */}

        <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
          <Typography variant="body2">Result : </Typography>
        </Box>

        {/* Result custom table */}
        <Box>
          {/* 5 boxes */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              border: "1px solid black",
              borderTop: "none",
            }}
          >
            {/* Sr no0 */}
            <Box
              sx={{ p: 1, borderRight: "1px solid black", minWidth: "60px" }}
            >
              <Typography variant="body2" align="center">
                Sl No.{" "}
              </Typography>
            </Box>
            <Box sx={{ p: 1, borderRight: "1px solid black", width: "500px" }}>
              <Box sx={{ borderBottom: "1px solid black" }}>
                <Typography
                  variant="body2"
                  sx={{ mb: 5 }}
                  gutterBottom
                  align="center"
                >
                  Standard Values.{" "}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                <Box
                  sx={{ p: 1, borderRight: "1px solid black", width: "200px" }}
                >
                  <Typography variant="body2" align="center">
                    Readings{" "}
                  </Typography>
                </Box>
                <Box sx={{ p: 1, width: "200px" }}>
                  <Typography variant="body2" align="center">
                    {" "}
                    Equivalent Readings{" "}
                  </Typography>
                </Box>
              </Box>
            </Box>

            <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
              <Typography variant="body2" align="center">
                Observed Values <br /> before Adjustment
              </Typography>
            </Box>
            <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
              <Typography variant="body2" align="center">
                Observed Values <br /> after Adjustment{" "}
              </Typography>
            </Box>
            <Box sx={{ p: 1, width: `25%` }}>
              <Typography variant="body2" align="center">
                Observed Error in (+ / -)
              </Typography>
            </Box>
          </Box>
        </Box>

        {results?.map((item, idx) => (
          <DataItem idx={idx + 1} key={idx + item?.id} item={item} />
        ))}

        {/* Data Item  */}

        {/* 

          Remarks

        */}

        <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
          <Typography variant="body2" sx={{ mb: 10 }}>
            Remarks : {report?.remarks}
          </Typography>

          <Box sx={{ display: "flex" }}>
            <Typography sx={{ width: "45%" }} variant="body2">
              Caliberated by (Sign): {report?.calibrated_by}{" "}
            </Typography>
            <Typography variant="body2">Checked By(Mylan): </Typography>
          </Box>
        </Box>
        <Box sx={{ display: "flex", justifyContent: "flex-end", p: 3, mt: 3 }}>
          <Typography variant="body2">Page 1/1</Typography>
        </Box>
      </div>
    </>
  );
};

export default CalibrationDataPrint;

export const EquipmentTableItem = ({ masterEquip }) => {
  return (
    <Box>
      {/* 5 boxes */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          border: "1px solid black",
          borderTop: "none",
        }}
      >
        {/* Sr no0 */}
        <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
          <Typography variant="body2" align="center">
            {masterEquip?.name}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
          <Typography variant="body2" align="center">
            {" "}
            {masterEquip?.make}{" "}
          </Typography>
        </Box>

        <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
          <Typography variant="body2" align="center">
            {masterEquip?.si_num}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
          <Typography variant="body2" align="center">
            {masterEquip?.certificate_no}
          </Typography>
        </Box>
        <Box sx={{ p: 1, width: `25%` }}>
          <Typography variant="body2" align="center">
            {masterEquip?.validity}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export const DataItem = ({ item, idx }) => {
  return (
    <>
      {/* Content / data mapping goes here */}

      <Box>
        {/* 5 boxes */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderTop: "none",
          }}
        >
          {/* Sr no0 */}
          <Box sx={{ p: 1, borderRight: "1px solid black", minWidth: "60px" }}>
            <Typography variant="body2" align="center">
              {idx}
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "500px" }}>
            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <Box
                sx={{ p: 1, borderRight: "1px solid black", width: "200px" }}
              >
                <Typography align="center" variant="body2">
                  {item?.reading}{" "}
                </Typography>
              </Box>
              <Box sx={{ p: 1, width: "200px" }}>
                <Typography align="center" variant="body2">
                  {" "}
                  {item?.eq_reading}{" "}
                </Typography>
              </Box>
            </Box>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
            <Typography variant="body2" align="center">
              {" "}
              {item?.before}
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
            <Typography variant="body2" align="center">
              {item?.after}{" "}
            </Typography>
          </Box>
          <Box sx={{ p: 1, width: `25%` }}>
            <Typography variant="body2" align="center">
              {item?.error}
            </Typography>
          </Box>
        </Box>
      </Box>
    </>
  );
};
