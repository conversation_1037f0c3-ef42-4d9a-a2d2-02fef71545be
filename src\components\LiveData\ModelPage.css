button:hover {
  background-color: #4fb2e9;
}

body {
  margin: 0;
  padding: 0;

  overflow: hidden;

  box-sizing: border-box;
}
.layout {
  height: 100%;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.annotationModal {
  background-color: white;
  padding: 20px;
  border-radius: 4px;
  max-width: 400px;
  width: 100%;
}

.modal label {
  display: block;
  margin-bottom: 10px;
}

.modal input[type="text"],
.modal input[type="color"] {
  display: block;
  width: 100%;
  margin-top: 5px;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

#uploadBtn {
  font-size: 1.2rem;
  padding: 0.5rem 1rem;
  margin: 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  outline: none;
}
