import React, { useState, useEffect } from "react";
import DeleteIcon from "@mui/icons-material/Delete";
import { Link, useNavigate } from "react-router-dom";
import Box from "@mui/material/Box";
import { Typography, Button, Tooltip } from "@mui/material";
import { makeStyles } from "@mui/styles";
import { sharedCss } from "../src/styles/sharedCss";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { useAuth } from "./hooks/AuthProvider";

import {
  Dialog,
  TextField,
  Select,
  MenuItem,
  Card,
  IconButton,
  Pagination,
} from "@mui/material";
import EditRoundedIcon from "@mui/icons-material/EditRounded";
import Delete from "./components/Delete/Delete";
import { deleteData } from "./services2/issueModule/IssueModule.services";
import { styled } from "@mui/material/styles";
import {
  ButtonWithVisibilityIcon,
  ButtonBasic,
} from "./components/buttons/Buttons";
import { useStateContext } from "./context/ContextProvider";
import usePagination from "./usePagination";
import EditProject from "./components/Flow-Chart/Existing-Node/EditProject";
import PageHeader from "./components/commons/page-header.component";
import { v4 as uuidv4 } from "uuid";
import {
  useIssueModuleChangeCount,
  useIssueModuleProjectData,
} from "./services2/issueModule/IssueModule.context";
import { toastMessageSuccess } from "./tools/toast";
import { useMachines } from "./services/machines/MachineContext";
import axios from "axios";
import { dbConfig } from "./infrastructure/db/db-config";
import { useDeleteMachineCfr } from "./hooks/cfr/machineCfrProvider";
import NoDataComponent from "./components/commons/noData.component";
const StyledSelect = styled(Select)(({ theme }) => ({
  "& .MuiInputBase-input": {
    position: "relative",
    fontSize: 13,
    padding: "2px 12px",
  },
}));
const useStyles = makeStyles((theme) => ({
  issueModuleCard: {
    backgroundColor: theme.palette.custom.backgroundThird,
    color: theme.palette.custom.textColor,
    padding: "1.5rem",
    width: "25ch",
    minWidth: "20rem",
  },
  truncateText: {
    whiteSpace: "nowrap", // Prevent text from wrapping to a new line
    overflow: "hidden", // Hide any overflowing text
    textOverflow: "ellipsis", // Show an ellipsis (...) for truncated text
  },
}));

const useCustomStyles = makeStyles((theme) => ({
  hashtagsContainer: {
    // padding: "1rem",
    // borderRadius: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  mainBackground: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-start",
    padding: "18px 6px",
    marginBottom: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
  },
  hashtagsOuterContainer: {
    width: "100%",
  },
  hashtagsInnerContainer: {
    display: "flex",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  addButton: {
    width: "max-content",
  },
  hashtagsPageContainer: {
    // padding: "1rem",
    // border: "1px solid gainsboro",
  },
}));

const IssueModuleCard = (props) => {
  const { currentUser } = useAuth();

  const { data, userName, machinesData } = props;
  console.log("daaaaaa, ", data);
  const deleteissuecfr = useDeleteMachineCfr();
  const date2 = new Date();
  const data2 = {
    activity: "issue module  deleted",
    dateTime: date2,

    description: "an issue module is deleted",
    machine: data.mid,
    module: "Issue module",
    username: currentUser.username,
  };

  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [machineTitle, SetMachineTitle] = useState("");
  const [machine, SetMachine] = useState("");
  const { currentMode } = useStateContext();
  //const date = new Date(parseInt(data?.date));

  const dateString = data?.date;
  const date = new Date(dateString);

  const options = {
    weekday: "short", // Short weekday name (e.g., Thu)
    year: "numeric", // Full year (e.g., 1970)
    month: "short", // Short month name (e.g., Jan)
    day: "2-digit", // Day with leading zeros (e.g., 01)
  };

  const formattedDate = date.toLocaleString("en-US", options);
  console.log("data issue module ka", data); // Output: "Fri Jul 21 2023"

  const checkMachine = async () => {
    try {
      const machine = await axios.get(`${dbConfig.url}/machines/${data?.mid}`);
      console.log("machinn checking", machine);
      SetMachine(machine?.data?.data);
      SetMachineTitle(machine?.data?.data?.title);
      console.log("datanew", data);
    } catch (error) {
      console.log(error);
      SetMachineTitle("Machine Deleted");
    }
  };
  const { issueCount, setIssueCount } = useIssueModuleChangeCount();

  useEffect(() => {
    checkMachine();
  }, []);
  const classes = useStyles();
  return (
    <Card
      variant="outlined"
      className={`${classes.issueModuleCard} border-radius-inner`}
    >
      <Box style={{ paddingBlock: "1rem" }}>
        <Tooltip title={data?.name} arrow>
          <Typography variant="h6">
            {" "}
            {data?.name?.slice(0, 22)}{" "}
            {data?.name.length > 22 ? "..." : ""}{" "}
          </Typography>
        </Tooltip>
        <Typography variant="subtitle2">Edited on {formattedDate}</Typography>
        <Typography variant="subtitle2">{machineTitle}</Typography>
        <div>
          <Typography variant="subtitle2">{userName}</Typography>
        </div>
      </Box>
      <Box
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          //   marginTop:"1.5rem",
          // marginBottom: "1rem"
        }}
        id="22"
      >
        <div>
          <Link
            to={{
              pathname: `/edit/${data?._id}`, // Include id in the URL
              state: {
                id: data?._id,
                mid: data?.mid,
                name: data?.name,
                values: data?.values,
              },
            }}
          >
            <Button startIcon={<VisibilityIcon />} variant="contained">
              View
            </Button>
          </Link>
        </div>
        <div>
          <IconButton color="primary">
            <EditRoundedIcon
              onClick={() => {
                setOpenEdit(true);
              }}
            />
          </IconButton>
          <EditProject
            openEdit={openEdit}
            handleClose={() => setOpenEdit(false)}
            machinesData={machinesData}
            id={data?._id}
            name={data?.name}
            midprop={machine?._id}
            machine={machine}
            userName={userName}
            values={data?.values}
          />
          <IconButton color="error">
            <DeleteIcon
              onClick={() => {
                setOpenDel(true);
              }}
              className="project-delete"
            />
          </IconButton>
          <Dialog open={openDel}>
            <Delete
              onClose={() => setOpenDel(false)}
              onDelete={async () => {
                await deleteData("issueModule", data?._id)
                  .then(
                    () => {
                      setIssueCount(issueCount + 1);

                      deleteissuecfr(data2);
                      toastMessageSuccess({
                        message: `${data?.name.trim()} has been deleted successfully!`,
                      });
                    },
                    (error) => console.log(error),
                  )
                  .finally(setOpenDel(false));
              }}
            />
          </Dialog>
        </div>
      </Box>
    </Card>
  );
};

const Main = () => {
  const [tempCharts, setTempCharts] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredMachine, setFilteredMachine] = useState("");
  const { currentColorLight, currentMode } = useStateContext();
  const projectData = useIssueModuleProjectData();
  const history = useNavigate();
  const [page, setPage] = useState(1);
  const PER_PAGE = 6;
  const count = Math.ceil(projectData?.length / PER_PAGE);
  const _DATA = usePagination(projectData, PER_PAGE);
  const _DATA2 = usePagination(tempCharts, PER_PAGE);

  const [curPage, setCurPage] = useState(1);
  const { currentUser } = useAuth();
  const { machines } = useMachines();

  console.log("machines", machines);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // useEffect(() => {
  //   var filter, ul, li, a, i, txtValue;
  //   filter = searchTerm.toUpperCase();
  //   ul = document.getElementById("myUL");
  //   li = ul.getElementsByClassName("main-content");

  //   console.log("test",ul,li)
  //   for (i = 0; i < li.length; i++) {
  //     a = li[i];
  //     txtValue = a.textContent || a.innerText;
  //     if (txtValue.toUpperCase().indexOf(filter) > -1) {
  //       li[i].style.display = "";
  //     } else {
  //       li[i].style.display = "none";
  //     }
  //   }
  // }, [searchTerm]);

  const handleFilter = (value) => {
    setFilteredMachine(value);
    filter(value);
  };

  useEffect(() => {
    if (filteredMachine == "") {
      setTempCharts(_DATA.currentData());
    } else {
      setTempCharts(_DATA2.currentData());
    }
    console.log("tempcharts", tempCharts);
  }, [curPage, projectData, filteredMachine]);

  const filter = (filteredMachine) => {
    const filteredData = projectData.filter((chart) =>
      filteredMachine?._id ? chart?.mid === filteredMachine?._id : true,
    );
    setTempCharts(filteredData);
  };
  // useEffect(() => {
  //   const filteredData = projectData.filter((chart) =>
  //     filteredMachine?._id ? chart?.mid === filteredMachine?._id : true
  //   );
  //   setTempCharts(filteredData);
  // }, [filteredMachine, projectData, filteredMachine?.id]);

  const handleChange = (e, p) => {
    setPage(p);
    _DATA.jump(p);
    setCurPage(p);
  };
  const commonCss = sharedCss();
  const customCss = useCustomStyles();
  console.log("tempcharts", tempCharts);
  const filteredCharts = tempCharts?.filter((data) => {
    // Remove search by machine and username
    // const selectedMachine = machines?.filter((data2) => {
    //   if (data?.mid === data2?._id) return true;
    // });
    // const txtValue =
    //   currentUser?.fname +
    //   " " +
    //   currentUser?.lname +
    //   data?.name +
    //   selectedMachine[0]?.title +
    //   data?.date;
    const txtValueMatch = data?.name
      ?.toUpperCase()
      .includes(searchTerm.toUpperCase());
    return txtValueMatch;
  });
  console.log("filteredCharts", filteredCharts);
  return (
    <main className={customCss.hashtagsPageContainer}>
      <header
        className={`${commonCss?.headingContainer} border-radius-inner`}
        style={{ padding: "0.5rem 0rem 0.5rem 1rem" }}
      >
        <div>
          <Box>
            <Typography variant="h4">Issue Module</Typography>
            <Typography variant="h6">Projects & Issues</Typography>
          </Box>
          {/* <PageHeader title="Issue Module" subText="Projects & Issues" /> */}
        </div>
        <div className="action-container mr-4">
          <TextField
            label="Search"
            placeholder="Project name"
            size="small"
            value={searchTerm}
            onChange={handleSearch}
            style={{ width: "286px" }}
          />
          <div className="issue-machine">
            <StyledSelect
              value={filteredMachine}
              className="select-machine"
              name="machines"
              onChange={(e) => handleFilter(e.target.value)}
              renderValue={(selected) => {
                if (selected === "") return "ALL MACHINES";
                return (
                  (selected &&
                    typeof selected === "object" &&
                    !Array.isArray(selected) &&
                    selected?.title) ||
                  "ALL MACHINES"
                );
              }}
              displayEmpty
            >
              <MenuItem
                key={"ALL MACHINES"}
                className="option-machine"
                value={""}
              >
                All Machines
              </MenuItem>
              {Array.isArray(machines) &&
                machines.length > 0 &&
                machines.map((machine) => {
                  return (
                    <MenuItem
                      className="option-machine"
                      key={machine?.id}
                      value={machine}
                    >
                      {machine?.title}
                    </MenuItem>
                  );
                })}
            </StyledSelect>
          </div>
          <Button
            className={customCss.addButton}
            variant="contained"
            onClick={() => history("/flow-chart")}
          >
            Add Project
          </Button>
          <ButtonBasic
            buttonTitle={"Nodes"}
            onClick={() => history("/new-node")}
          />
        </div>
      </header>
      <div className={`${customCss.mainBackground} border-radius-inner`}>
        <div
          id="myUL"
          className="main-content flex-col w-100 rounded-xl"
          style={{ display: "flex", justifyContent: "space-between" }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              flexWrap: "wrap",
              gap: "1rem",
              marginBottom: "10px",
              width: "100%",
            }}
          >
            {/* Display No Data Message */}
            {!filteredCharts ||
            !Array.isArray(filteredCharts) ||
            filteredCharts.length === 0 ? (
              <div style={{ marginTop: "20px" }}>
                {/*
              <Typography variant="h6" style={{ textAlign: "center", marginTop: "20px" }}>
                No data
              </Typography>
              */}
                <NoDataComponent useAtTable={false} />
              </div>
            ) : (
              <Box
                style={{
                  display: "flex",
                  flexWrap: "wrap",
                  gap: "1rem",
                  padding: "0rem 0.8rem",
                }}
              >
                {filteredCharts &&
                  Array.isArray(filteredCharts) &&
                  filteredCharts.map((data, index) => (
                    <div key={index} className="issue-card">
                      <IssueModuleCard
                        data={data}
                        userName={data.userName}
                        machinesData={machines}
                      />
                    </div>
                  ))}
              </Box>
            )}
          </div>
          {/* Pagination */}
          {filteredCharts &&
            Array.isArray(filteredCharts) &&
            filteredCharts.length > 0 && (
              <Pagination
                count={count}
                page={page}
                onChange={handleChange}
                style={{
                  marginTop: "20px",
                  display: "flex",
                  justifyContent: "center",
                }}
              />
            )}
        </div>
      </div>
    </main>
  );
};

export default Main;
