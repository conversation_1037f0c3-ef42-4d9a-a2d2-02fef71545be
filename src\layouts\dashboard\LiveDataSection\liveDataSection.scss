.liveDataSection {
  width: 100%;
  background-color: #fff;
  box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  color: #344767;
  margin: 1.5rem 0;

  .liveDataSectionHeading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.2rem;
    margin-top: 2rem;

    .info {
      margin-bottom: 1rem;
      h3 {
        font-size: 1.2rem;
        font-weight: 500;
        opacity: 0.9;
      }
    }
    .selector {
      label {
        font-size: 0.9rem;
        color: #344767;
      }
      .css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input.MuiSelect-select {
        height: 0.8rem;
        color: #344767;
        font-size: 0.9rem;
      }
    }
  }

  .chartContainer {
    h2 {
      font-size: 0.9rem;
      opacity: 0.9;
      margin: 0 1.2rem;
    }
    .chart {
      margin: 2rem;
    }
  }
}
