import {
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableHead,
} from "@mui/material";
import PropTypes from "prop-types";

export const MyTableContainer = ({ children, ...props }) => {
  return (
    <TableContainer component={Paper}>
      <Table
        sx={{ minWidth: 650, width: "100%" }}
        size="small"
        stickyHeader
        aria-label="simple table"
        {...props}
      >
        {children}
      </Table>
    </TableContainer>
  );
};

export const MyTableHead = ({ children, ...props }) => {
  return <TableHead {...props}>{children}</TableHead>;
};

export const MyTableBody = ({ children, ...props }) => {
  return <TableBody {...props}>{children}</TableBody>;
};

MyTableContainer.propTypes = {
  children: PropTypes.node.isRequired, // Content inside the TableContainer
};

MyTableHead.propTypes = {
  children: PropTypes.node.isRequired, // Content inside the TableHead
};

MyTableBody.propTypes = {
  children: PropTypes.node.isRequired, // Content inside the TableBody
};
