import React from "react";
import "./adminForgotPass.scss";
import { alpha, styled } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import InputLabel from "@mui/material/InputLabel";
import { Link } from "react-router-dom";

const BootstrapInput = styled(InputBase)(({ theme }) => ({
  "label + &": {
    marginTop: theme.spacing(0),
  },
  "& .MuiInputBase-input": {
    borderRadius: 4,
    position: "relative",
    backgroundColor: theme.palette.mode === "light" ? "#fcfcfb" : "#2b2b2b",
    border: "1px solid #ced4da",
    fontSize: 14,
    color: "#344767",
    padding: "10px 12px",
    transition: theme.transitions.create([
      "border-color",
      "background-color",
      "box-shadow",
    ]),
    "&:focus": {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main,
    },
  },
}));

const AdminForgotPass = () => {
  return (
    <section className="adminForgotPassPage">
      <div className="logoContainer">
        <img
          src="https://cdn-images-1.medium.com/max/1200/1*w08ygePqBu1otQVZ60bPMg.png"
          alt=""
        />
      </div>
      <div className="illustrationContainer">
        <img
          src="https://trello.com/1/cards/61d54fffa9b18971bd552eb8/attachments/61d551e2b11cce43d1c2c6f5/download/Clip_web_design_by_Icons8.gif"
          alt=""
        />
      </div>

      <div className="formContainer">
        <div className="formTitle">LSI Admin</div>
        <div className="formDesc">
          Enter Your email address here to recover it.
        </div>

        <form className="form">
          <div className="labelFields">
            <InputLabel shrink htmlFor="adminEmail">
              Email Address
            </InputLabel>
            <BootstrapInput
              id="adminEmail"
              placeholder="eg. <EMAIL>"
            />
          </div>

          <div className="linkContainer">
            <Link to="/admin-login" className="link">
              <i className="ri-arrow-left-line"></i>
              Back to Login
            </Link>
          </div>
          <button>Submit</button>
        </form>
      </div>
    </section>
  );
};

export default AdminForgotPass;
