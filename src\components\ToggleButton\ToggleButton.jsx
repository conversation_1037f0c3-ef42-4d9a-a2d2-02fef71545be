import React from "react";
import "./ToggleButton.scss";

const ToggleButton = ({ label }) => {
  return (
    <div className="btncontainer">
      <p id="text"> {label}</p>
      <div className="toggle-switch">
        <input type="checkbox" className="checkbox" name={label} id={label} />
        <label className="label" htmlFor={label}>
          <span className="inner" />
          <span className="switch" />
        </label>
      </div>
    </div>
  );
};

export default ToggleButton;
