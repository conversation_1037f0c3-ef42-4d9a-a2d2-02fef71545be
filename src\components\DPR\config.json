[{"key": "Date", "label": "Date", "type": "date", "sortable": true, "filterable": true, "visible": true, "fieldType": "b"}, {"key": "Shift", "label": "Shift", "type": "text", "sortable": true, "filterable": true, "visible": true, "fieldType": "c"}, {"key": "Line", "label": "Line", "type": "text", "sortable": true, "filterable": true, "visible": true, "fieldType": "c"}, {"key": "Product Name", "label": "Product Name", "type": "text", "sortable": true, "filterable": true, "visible": true, "fieldType": "b"}, {"key": "B.NO", "label": "Batch Number", "type": "text", "sortable": true, "filterable": true, "visible": true, "fieldType": "a"}, {"key": "Pack Style", "label": "Pack Style", "type": "text", "sortable": false, "filterable": true, "visible": false, "fieldType": "c"}, {"key": "Running RPM", "label": "Running RPM", "type": "number", "sortable": true, "filterable": true, "visible": false, "fieldType": "b"}, {"key": "Total Shift Time", "label": "Total Shift Time", "type": "text", "sortable": true, "filterable": true, "visible": false, "fieldType": "a"}, {"key": "Actual Run Hrs", "label": "Actual Run Hours", "type": "text", "sortable": true, "filterable": true, "visible": false, "fieldType": "b"}, {"key": "Total Downtime", "label": "Total Downtime", "type": "text", "sortable": true, "filterable": true, "visible": false, "fieldType": "b"}, {"key": "Target *Do not Edit*", "label": "Target (Do not Edit)", "type": "number", "sortable": false, "filterable": true, "visible": false, "fieldType": "a"}, {"key": "Start Shipper (Nos)", "label": "Start Shipper (Nos)", "type": "number", "sortable": true, "filterable": true, "visible": false, "fieldType": "b"}, {"key": "End Shipper (Nos)", "label": "End Shipper (Nos)", "type": "number", "sortable": true, "filterable": true, "visible": false, "fieldType": "b"}, {"key": "QTY/ SHIPPER", "label": "Quantity per Shipper", "type": "number", "sortable": true, "filterable": true, "visible": false, "fieldType": "b"}, {"key": "Actual (Mns) *Do not Edit*", "label": "Actual (Minutes, Do not Edit)", "type": "number", "sortable": false, "filterable": true, "visible": false, "fieldType": "a"}, {"key": "Reason (Write in Detail)", "label": "Reason (Write in Detail)", "type": "text", "sortable": false, "filterable": true, "visible": false, "fieldType": "b"}, {"key": "Operator", "label": "Operator", "type": "text", "sortable": true, "filterable": true, "visible": false, "fieldType": "c"}, {"key": "Executive", "label": "Executive", "type": "text", "sortable": true, "filterable": true, "visible": false, "fieldType": "c"}, {"key": "INCHARGE", "label": "Incharge", "type": "text", "sortable": true, "filterable": true, "visible": false, "fieldType": "c"}, {"key": "CASUAL MANPOWER", "label": "Casual Manpower", "type": "number", "sortable": true, "filterable": true, "visible": false, "fieldType": "b"}, {"key": "Shift Instance", "label": "Shift Instance", "type": "text", "sortable": true, "filterable": true, "visible": false, "fieldType": "a"}, {"key": "CONCATENATE", "label": "Concatenate", "type": "text", "sortable": false, "filterable": true, "visible": false, "fieldType": "a"}, {"key": "Opt. Speed (For OEE Calculation)", "label": "Optimal Speed (For OEE Calculation)", "type": "number", "sortable": true, "filterable": true, "visible": false, "fieldType": "a"}, {"key": "Output as per Opt. Speed", "label": "Output as per Optimal Speed", "type": "number", "sortable": true, "filterable": true, "visible": false, "fieldType": "a"}, {"key": "Availability", "label": "Availability", "type": "percentage", "sortable": true, "filterable": true, "visible": false, "fieldType": "a"}, {"key": "Performance", "label": "Performance", "type": "percentage", "sortable": true, "filterable": true, "visible": false, "fieldType": "a"}, {"key": "Quality", "label": "Quality", "type": "percentage", "sortable": true, "filterable": true, "visible": false, "fieldType": "a"}, {"key": "OEE%", "label": "OEE%", "type": "percentage", "sortable": true, "filterable": true, "visible": false, "fieldType": "a"}]