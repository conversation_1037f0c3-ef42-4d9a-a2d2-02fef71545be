import React from "react";
import { AiFillCamera, AiFillShop } from "react-icons/ai";
import { AiFillVideoCamera } from "react-icons/ai";
import { FaBook, FaHashtag } from "react-icons/fa";
import { GoIssueOpened } from "react-icons/go";
import { RiToolsFill } from "react-icons/ri";
import { RiAlarmFill } from "react-icons/ri";
import { RiCalendar2Fill } from "react-icons/ri";
import { RiSettings4Fill } from "react-icons/ri";
import { RiAccountBoxFill } from "react-icons/ri";
import { VscFiles } from "react-icons/vsc";
import { VscReport } from "react-icons/vsc";
import { ImUsers } from "react-icons/im";
import { FaTasks } from "react-icons/fa";
import { BsFillFolderFill } from "react-icons/bs";
import ReceiptLongIcon from "@mui/icons-material/ReceiptLong";
import EventNoteIcon from "@mui/icons-material/EventNote";
import BarChartIcon from "@mui/icons-material/BarChart";
import { MdBatchPrediction } from "react-icons/md";
import { AccountTree, ErrorSharp } from "@mui/icons-material";
import ViewInArIcon from "@mui/icons-material/ViewInAr";
import DocumentScannerIcon from "@mui/icons-material/DocumentScanner";
import SpeedIcon from "@mui/icons-material/Speed";
import { TiSpanner } from "react-icons/ti";
import BuildIcon from "@mui/icons-material/Build";

export const MenuData = [
  {
    // listHeading: "Navigation",
    link: [
      {
        path: "/",
        text: "Dashboard",
        icon: (
          <AiFillShop
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
      {
        path: "/machines",
        text: "Machines",
        icon: (
          <RiToolsFill
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
      {
        path: "/reports",
        text: "Reports",
        icon: (
          <ReceiptLongIcon
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
      {
        path: "/file-manager",
        text: "Library",
        icon: (
          <BsFillFolderFill
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },

      // {
      //   path: "/smartScan",
      //   text: "Smart Scan",
      //   icon: <DocumentScannerIcon />,
      // },
      // {
      //   path: "/cms",
      //   text: "CMS",
      //   icon: <SpeedIcon />,
      // },
      {
        path: "/arviews",
        text: "Canvas",
        icon: (
          <AiFillCamera
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
      {
        path: "/dpr",
        text: "DPR",
        icon: (
          <ReceiptLongIcon
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
      {
        path: "/utilities",
        text: "Utilities",
        icon: (
          <VscFiles
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
    ],
  },

  {
    listHeading: "Overview",
    link: [
      {
        path: "/issues",
        text: "Issue Module",
        icon: (
          <AccountTree
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },

      {
        path: "/videocall",
        text: "Video Call",
        icon: (
          <AiFillVideoCamera
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
      {
        path: "/hashtag",
        text: "Hash Tags",
        icon: (
          <FaHashtag
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },

      // {
      //   path: "/alarms",
      //   text: "Alarms",
      //   icon: (
      //     <RiAlarmFill
      //       style={{
      //         width: "1.5rem",
      //         height: "2rem",
      //       }}
      //     />
      //   ),
      // },

      // {
      //   path: "/alarm-manager",
      //   text: "Alarm Manager",
      //   icon: (
      //     <RiAlarmFill
      //       style={{
      //         width: "1.5rem",
      //         height: "2rem",
      //       }}
      //     />
      //   ),
      // },
      {
        path: "/3dmodel",
        text: "3D View",
        icon: <ViewInArIcon />,
      },

      {
        path: "/smartScan",
        text: "Smart Scan",
        icon: (
          <DocumentScannerIcon
            style={{
              color: "#f29418",
            }}
          />
        ),
      },
      {
        path: "/cms",
        text: "CMS",
        icon: (
          <SpeedIcon
            style={{
              color: "#6300a1",
            }}
          />
        ),
      },
    ],
  },
  {
    listHeading: "User",
    link: [
      {
        path: "/users",
        text: "Users",
        icon: (
          <ImUsers
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
      {
        path: "/account",
        text: "Account",
        icon: (
          <RiAccountBoxFill
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
      {
        path: "/calendar",
        text: "Calendar",
        icon: (
          <RiCalendar2Fill
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
      // {
      // 	path: "/tasks",
      // 	text: "Tasks",
      // 	icon: <EventNoteIcon />,
      // },
      // {
      // 	path: "/notes",
      // 	text: "Notes",
      // 	icon: <EventNoteIcon />,
      // },
    ],
  },
  {
    listHeading: "Others",
    link: [
      {
        path: "/settings",
        text: "Settings",
        icon: (
          <RiSettings4Fill
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
      {
        path: "/configuration",
        text: "Configuration",
        icon: (
          <BuildIcon
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
      {
        path: "/audit-trail",
        text: "Audit Trail",
        icon: (
          <FaBook
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
    ],
  },
  {
    listHeading: "Help",
    link: [
      {
        path: "/user-manual",
        text: "User Manual",
        icon: (
          <VscFiles
            style={{
              width: "1.5rem",
              height: "2rem",
            }}
          />
        ),
      },
    ],
  },
];
