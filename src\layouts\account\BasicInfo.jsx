import { Typography } from "@mui/material";
import React from "react";
import { useStateContext } from "../../context/ContextProvider";
import InfoForm from "./InfoForm";
import { makeStyles } from "@mui/styles";
import { Box } from "@mui/material";

const useStyles = makeStyles((theme) => ({
  basicInfoContainer: {
    backgroundColor: theme.palette.custom.backgroundForth,
    color: theme.palette.custom.textColor,
    // padding: theme.spacing(2),
    display: "flex",
    flexDirection: "column",
    borderRadius: "10px",
    // marginBlock: "2rem",
  },
  title: {
    fontWeight: "bold",
    fontSize: "1.5rem",
  },
  darkTitle: {
    fontWeight: "bold",
    fontSize: "1.5rem",
    // marginBottom: theme.spacing(2),
    color: theme.palette.text.primary,
  },
}));

const BasicInfo = () => {
  const { currentMode, currentColorLight } = useStateContext();
  const classes = useStyles();
  return (
    <div className={classes.basicInfoContainer}>
      <InfoForm />
    </div>
  );
};

export default BasicInfo;
