import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from "@mui/material";
import React, { useState } from "react";
import {
  ButtonBasic,
  ButtonBasicCancel,
  SubmitButtons
} from "../../../components/buttons/Buttons";
import { useStateContext } from "../../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import { toastMessage, toastMessageSuccess } from "../../../tools/toast";
import { useMongoRefresh } from "../../../services/mongo-refresh.context";
import { useEditMachineCfr } from "../../../hooks/cfr/machineCfrProvider";
import { convertBase64 } from "../../../hooks/useBase64";
import GetPreviewComponent from "../../../components/commons/getPreview.component";
import { DropzoneArea } from "material-ui-dropzone";

const EditTraining = ({ mid, handleClose, data, machineName, name, userName }) => {
  const [title, setTitle] = useState(data.title);
  const [desc, setDesc] = useState(data.desc);
  const [period, setPeriod] = useState(data.period);
  const [type, setType] = useState(data.type);
  const [imageUrl, setImageUrl] = useState("");
  const [selectedFileForStorage, setSelectedFileForStorage] = useState(null);
  const [imageFileUrl, setImageFileUrl] = useState("");
  const [selectedImageForStorage, setSelectedImageForStorage] = useState(null);

  const typesPdf = ["application/pdf"];
  const typesImages = ["image/png", "image/jpeg", "image/jpg", "image/webp"];

  const { currentMode } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const edittrainingcfr = useEditMachineCfr();

  const handleChange = async (loadedFiles) => {
    const selectedFile = loadedFiles[0];
    const base64 = await convertBase64(selectedFile);
    setSelectedFileForStorage(selectedFile);

    if (typesPdf.includes(selectedFile.type)) {
      setImageUrl(base64);
    } else {
      toastMessage({ message: "Please select a PDF" });
    }
  };

  const handleImageChange = async (loadedFiles) => {
    const selectedFile = loadedFiles[0];
    if (!selectedFile) {
      setImageFileUrl("");
      setSelectedImageForStorage(null);
      return;
    }
    const base64 = await convertBase64(selectedFile);
    setSelectedImageForStorage(selectedFile);

    if (typesImages.includes(selectedFile.type)) {
      setImageFileUrl(base64);
    } else {
      toastMessage({ message: "Please select an image file (png, jpg, jpeg, webp)" });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (selectedFileForStorage && data.sop_url) {
      await axios.post(`${dbConfig?.url_storage}/deleteImage`, {
        file_name: data.sop_url,
      }).catch(console.error);
    }

    if (selectedImageForStorage && data.image_url) {
      await axios.post(`${dbConfig?.url_storage}/deleteImage`, {
        file_name: data.image_url,
      }).catch(console.error);
    }

    const dataSet = {
      title,
      desc,
      created_at: data.created_at,
      last_updated: new Date().toISOString(),
      mid: mid,
    };

    let resTemp = null;
    let resImageTemp = null;

    if (selectedFileForStorage) {
      const fd = new FormData();
      fd.append("image", selectedFileForStorage);
      resTemp = await axios.post(`${dbConfig?.url_storage}/upload`, fd).catch(console.error);
    }

    if (selectedImageForStorage) {
      const fd = new FormData();
      fd.append("image", selectedImageForStorage);
      resImageTemp = await axios.post(`${dbConfig?.url_storage}/upload`, fd).catch(console.error);
    }

    await axios
      .put(`${dbConfig.url}/training/${data._id}`, {
        ...dataSet,
        sop_url: selectedFileForStorage ? resTemp?.data?.data : data.sop_url,
        image_url: selectedImageForStorage ? resImageTemp?.data?.data : data.image_url || "",
      })
      .then(() => {
        edittrainingcfr({
          activity: "training edited",
          dateTime: new Date(),
          description: " training is edited",
          machine: mid,
          module: "Training",
          username: userName,
        });
        toastMessageSuccess({ message: `${title} has been updated` });
        handleClose();
        setRefreshCount(refreshCount + 1);
      })
      .catch((err) => {
        console.log("error:", err);
        toastMessage({ message: "Error while updating" });
      });
  };

  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Title</InputLabel>
      <TextField
        onChange={(e) => setTitle(e.target.value)}
        onBlur={() => setTitle(title?.trim())}
        value={title}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Description</InputLabel>
      <TextField
        onChange={(e) => setDesc(e.target.value)}
        onBlur={() => setDesc(desc?.trim())}
        value={desc}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{ marginBottom: "12px", maxHeight: "40vh", overflowY: "scroll" }}
      />

      <Tooltip title={data?.sop_url ? "Update with new SOP" : "Add a new SOP"} placement="top">
        <span> Add SOP</span>
      </Tooltip>
      <DropzoneArea
        acceptedFiles={typesPdf}
        showFileNames
        onChange={handleChange}
        dropzoneText="Drag and Drop / Click to ADD Media"
        showAlerts={false}
        filesLimit={1}
        maxFileSize={50 * 1024 * 1024}
        onDelete={() => handleChange([])}
      />
      {(imageUrl || data?.sop_url) && (
        <div className="my-2" style={{ display: "flex", justifyContent: "center" }}>
          <GetPreviewComponent
            sourceUrl={
              imageUrl.length > 0
                ? imageUrl
                : `${dbConfig?.url_storage}/${data?.sop_url || ""}`
            }
            fileFormat="pdf"
            previewPdfStyle={{ width: "450px", height: "500px" }}
          />
        </div>
      )}

      <Tooltip title={data?.image_url ? "Update with new Image" : "Add a new Image"} placement="top">
        <span> Add Image</span>
      </Tooltip>
      <DropzoneArea
        acceptedFiles={typesImages}
        showFileNames
        onChange={handleImageChange}
        dropzoneText="Drag and Drop / Click to ADD Image"
        showAlerts={false}
        filesLimit={1}
        maxFileSize={50 * 1024 * 1024}
        onDelete={() => handleImageChange([])}
      />
      {(imageFileUrl || data?.image_url) && (
        <div className="my-2" style={{ display: "flex", justifyContent: "center" }}>
          <GetPreviewComponent
            sourceUrl={
              imageFileUrl.length > 0
                ? imageFileUrl
                : `${dbConfig?.url_storage}/${data?.image_url || ""}`
            }
            fileFormat="image"
            previewImageStyle={{ width: "450px" }}
          />
        </div>
      )}

      <div className="p-2 mt-2 flex justify-between">
        <ButtonBasicCancel type="button" buttonTitle="Cancel" onClick={handleClose} />
        <SubmitButtons type="submit" buttonTitle="Submit" />
      </div>
    </form>
  );
};

export default EditTraining;
