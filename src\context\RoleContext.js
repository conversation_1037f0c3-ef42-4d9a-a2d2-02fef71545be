import React, { useContext, useState, useEffect } from "react";
import { auth } from "../firebase"; // Replace incorrect imports with 'auth'
import { companies, companyId_constant, users } from "../constants/data";
import { firebaseLooper } from "../tools/tool";

const RoleContext = React.createContext();
const RoleSeterContext = React.createContext();
//
export function useRole() {
  return useContext(RoleContext);
}

// export const userDetails = db
//   .collection(companies)
//   .doc(companyId_constant)
//   .collection(users);
// export const userDetails =  ""
export function RoleProvider({ children }) {
  const [role, setRole] = useState("");

  return <RoleContext.Provider value={role}>{children}</RoleContext.Provider>;
}
