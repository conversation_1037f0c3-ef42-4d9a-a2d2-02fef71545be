import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import {
  companies,
  companyId_constant,
  liveData,
  machines,
} from "../../constants/data";
// import { db, firestore } from "../../firebase";
// import firebase from "firebase";
import { firebaseLooper } from "../../tools/tool";
import Item from "./Item";
import "./machineData.scss";
import MachineDataHeader from "./MachineDataHeader";

import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import { Empty } from "antd";
import Paper from "@mui/material/Paper";
import Box from "@mui/material/Box";
import InputLabel from "@mui/material/InputLabel";
//import MenuItem from '@mui/material/MenuItem';
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import {
  // Box,
  Button,
  Dialog,
  DialogContent,
  //InputLabel,
  DialogTitle,
  DialogContentText,
  DialogActions,
  LinearProgress,
  MenuItem,
  //Select,
  //TextField,
  Typography,
} from "@mui/material";
import TextField from "@mui/material/TextField";
import AddIcon from "@mui/icons-material/Add";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
// import { useStorage } from "../../utils/useStorage";
import { Badge, CircularProgress, IconButton } from "@mui/material";
import { userDetails } from "../../context/RoleContext";
import { useAuth } from "../../hooks/AuthProvider";
import LoggingFunction from "../../components/CFR-Report/LoggingFunction";
import AnnotationChart from "./AnnotationChart";
import CloseIcon from "@mui/icons-material/Close";
import { useStateContext } from "../../context/ContextProvider";
import Checkbox from "@mui/material/Checkbox";
import AnnotationChartNew from "./AnnotationChartNew";
import { ButtonBasic } from "../../components/buttons/Buttons";
import NoDataComponent from "../../components/commons/noData.component";

const LiveData = () => {
  const { mid } = useParams();
  const [details, setDetails] = useState([]);
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");
  const [img_url, setImgUrl] = useState("");
  const [type, setType] = useState("");
  const [createdAt, setCreatedAt] = useState(new Date());
  const [show, setShow] = useState(null);
  const types = ["image/png", "image/jpeg", "image/jpg"];
  const [file, setFile] = useState(null);
  const [activeRole, setActiveRole] = useState(null);
  const [user, setUser] = useState([]);
  const [dataLoading, setDataLoading] = useState(true);
  const { currentUser } = useAuth();
  const [machineName, setMachineName] = useState("");
  const [color, setColor] = useState("");
  const [textColor, setTextColor] = useState("");
  const [processValues, setProcessValues] = useState([]);
  const [activeChartFromSelected, setActiveChartFromSelected] = useState(""); //
  const [activeChartsArrayFromSelected, setActiveChartsArrayFromSelected] =
    useState([]); // new implimentation
  const [showGraph, setShowGraph] = useState(false);
  const [showGraphNew, setShowGraphNew] = useState(false);
  const [searchChartKeyword, setSearchChartKeyword] = useState("");
  const { currentColor, currentMode, currentColorLight } = useStateContext();

  //
  const handleCloseGraph = () => {
    setShowGraph(false);
  };

  useEffect(() => {
    //Live Data
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(liveData)
    //   .where('mid', '==', mid)
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     data.sort(function (a, b) {
    //       return (a.type - b.type)
    //     })
    //     setDetails(data);
    //     setDataLoading(false)
    //   });
    //user role active status detail
    // userDetails.where("email", "==", currentUser.email).onSnapshot((snap) => {
    //   const data = firebaseLooper(snap);
    //   if (data[0].role === "trainee" || data[0].role === "operator") {
    //     setActiveRole(false);
    //   } else {
    //     setActiveRole(true);
    //   }
    // });
    // Machine Name
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(machines)
    //   .doc(mid)
    //   .onSnapshot((snap) => {
    //     const data = snap.data();
    //     setMachineName(data.title)
    //   })
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection("userData")
    //   .where("email", "==", currentUser.email)
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     setUser(data[0]);
    //   });
    // //processValue
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection('liveData2')
    //   .where("mid", "==", mid)
    //   .onSnapshot(snap => {
    //     const data = firebaseLooper(snap)
    //     setProcessValues(data)
    //   })
  }, []);

  const addLiveData = (e) => {
    e.preventDefault();
    // if (url === null) {
    //   return toastMessage({ message: `Please put an image` });
    // }
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(liveData)
    //   .add({ title, desc, color, textColor, url: url, createdAt, status: 'Active', mid, type, annotationData: [] })
    //   .then(() => {
    //     LoggingFunction(
    //       machineName,
    //       title,
    //       `${user?.fname} ${user?.lname}`,
    //       "Live data",
    //       `New Live Data is added`
    //     );
    //     toastMessageSuccess({ message: 'Successfully added to live data' });
    //     setTitle('');
    //     setDesc('');
    //     setType('');
    //     setFile(null);
    //     setShow(false);
    //   });
  };

  const handleChange = (e) => {
    let selectedFile = e.target.files[0];

    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG)",
        });
      }
    }
  };
  // const { currentColor, currentMode } = useStateContext();
  // const { progress, url } = useStorage(file);

  //
  const onChangeTitle = (title) => {
    setActiveChartFromSelected(title);
    setShowGraph(true);
  };
  //
  const handleCheckBox = (e) => {
    if (e.target.checked) {
      setActiveChartsArrayFromSelected([
        ...activeChartsArrayFromSelected,
        e.target.id,
      ]); // id is title
      //console.log("title and  event if", e.target.checked , ":" , [...activeChartsArrayFromSelected, e.target.id ] )
    } else {
      let temp = [...activeChartsArrayFromSelected];
      let temp2 = temp.filter((data) => data != e.target.id);
      setActiveChartsArrayFromSelected([...temp2]);
      //console.log("title and  event else", e.target.checked , ":" , [...temp2])
    }
  };

  return (
    <section className="machineDataViewPage">
      <MachineDataHeader />

      <div className="allMachineDataPreviewContainer">
        <div
          style={
            currentMode === "Dark"
              ? {
                  backgroundColor: "#212B36",
                  color: "white",
                  border: "1px solid white",
                }
              : { border: "1px solid" }
          }
          className="liveDataOuterContainer"
        >
          <div className="liveDataHeading">
            <div className="title">Live Data</div>

            <div className="pb-2">
              <Box
                sx={{
                  minWidth: 150,
                  mt: 1,
                  backgroundColor: currentMode === "Dark" ? "#555" : "#fff",
                  borderRadius: 1,
                }}
              >
                <FormControl fullWidth size="small" color="success">
                  <InputLabel id="demo-simple-select-label">
                    View Chart
                  </InputLabel>
                  <Select
                    labelId="demo-simple-select-label"
                    id="demo-simple-select"
                    label="View Chart"
                    value={activeChartFromSelected}
                    onChange={(e) => onChangeTitle(e.target.value)}
                  >
                    {processValues?.map((data, index) => (
                      <MenuItem
                        style={
                          currentMode === "Dark"
                            ? {
                                backgroundColor: "#212B36",
                                color: currentColorLight,
                              }
                            : {
                                backgroundColor: currentColorLight,
                                color: "#212B36",
                              }
                        }
                        key={index}
                        value={data.id}
                      >
                        {data.id}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </div>

            <div className="btn">
              {activeRole && (
                <button
                  onClick={() => setShow(true)}
                  type="button"
                  style={{ backgroundColor: currentColor }}
                  className="text-md opacity-0.9 text-white  hover:drop-shadow-xl rounded-md  p-2"
                >
                  Add Data <AddIcon />
                </button>
              )}
            </div>
          </div>

          <div className="liveDataContainer">
            <TableContainer
              style={
                currentMode === "Dark"
                  ? {
                      backgroundColor: "#212B36",
                      color: "white",
                      border: "1px solid white",
                    }
                  : { border: "1px solid black" }
              }
              component={Paper}
              className="table"
            >
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow>
                    <TableCell
                      style={
                        currentMode === "Dark"
                          ? { color: "white" }
                          : { color: "black" }
                      }
                      align="left"
                    >
                      Name
                    </TableCell>
                    <TableCell
                      style={
                        currentMode === "Dark"
                          ? { color: "white" }
                          : { color: "black" }
                      }
                      align="left"
                    >
                      Description
                    </TableCell>
                    <TableCell
                      style={
                        currentMode === "Dark"
                          ? { color: "white" }
                          : { color: "black" }
                      }
                      align="center"
                    >
                      Type
                    </TableCell>
                    <TableCell
                      style={
                        currentMode === "Dark"
                          ? { color: "white" }
                          : { color: "black" }
                      }
                      align="center"
                    >
                      Status
                    </TableCell>
                    <TableCell
                      style={
                        currentMode === "Dark"
                          ? { color: "white" }
                          : { color: "black" }
                      }
                      align="center"
                    >
                      Created At
                    </TableCell>
                    <TableCell
                      style={
                        currentMode === "Dark"
                          ? { color: "white" }
                          : { color: "black" }
                      }
                      align="center"
                    >
                      Actions
                    </TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {details.length > 0 ? (
                    details.map((data, index) => (
                      <>
                        {/* {console.log("dd",data.title)} */}
                        <Item
                          activeRole={activeRole}
                          key={index + data.id}
                          data={data}
                          machineName={machineName}
                          activeChartFromSelected={activeChartFromSelected}
                        />
                      </>
                    ))
                  ) : (
                    <>
                      {/*
                    <TableRow
                      sx={{
                        "&:last-child td, &:last-child th": {
                          border: 0,
                        },
                      }}
                    >
                      <TableCell
                        style={
                          currentMode === "Dark"
                            ? { color: "white", borderBottom: "none" }
                            : { color: "black", borderBottom: "none" }
                        }
                        align="center"
                        colSpan={6}
                      >
                        {dataLoading && <CircularProgress />}
                        {!dataLoading && (
                          <div className=" animate-pulse text-red-500">
                            No Data
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                    */}
                      <NoDataComponent />
                    </>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        </div>

        {/* //////////  NOT IN USE*/}
        {/* <div style={currentMode === 'Dark' ? { backgroundColor: '#212B36', padding: 0 } : { backgroundColor: currentColorLight, padding: 0 }} className='chartDataOuterContainer'>
          <div className='chartContainerFirst' style={currentMode === 'Dark' ? { backgroundColor: '#212B36', color: 'white', border: '1px solid white' } : { border: '1px solid black', backgroundColor: currentColorLight }} >
            <div className="chartDataHeading">
              <div className="title">Charts 1</div>
              <div className="searchBox">
                <Box sx={{ minWidth: 150, mt: 1, backgroundColor: currentMode === 'Dark' ? "#555" : "#fff", borderRadius: 1 }} >
                  <TextField
                    color='success'
                    value={searchChartKeyword}
                    onChange={(e) => setSearchChartKeyword(e.target.value)}
                    placeholder="Chart"
                    variant='outlined'
                    size='small'
                  />
                </Box>
                <i className="ri-search-line self-center pl-1" />
              </div>
              <ButtonBasic onClick={() => setShowGraphNew(true)} buttonTitle="show" title="Beta stage" />
            </div>


            <div className="chartDataContainer"  >
              <TableContainer component={Paper} className="table" style={currentMode === 'Dark' ? { backgroundColor: '#212B36', color: '#fff', border: '1px solid white' } : { border: '1px solid black' }}>
                <Table sx={{ minWidth: 650 }}>
                  <TableHead>
                    <TableRow  >
                      <TableCell style={currentMode === 'Dark' ? { color: 'white' } : { color: 'black' }} align="left">Title</TableCell>
                      <TableCell style={currentMode === 'Dark' ? { color: 'white' } : { color: 'black' }} align="left">Current value</TableCell>
                      <TableCell style={currentMode === 'Dark' ? { color: 'white' } : { color: 'black' }} align="center">Status</TableCell>
                      <TableCell style={currentMode === 'Dark' ? { color: 'white' } : { color: 'black' }} align="center">Selection</TableCell>
                    </TableRow>
                  </TableHead>

                  <TableBody>
                    {processValues.length > 0 ?
                      (processValues.filter((dataf) => dataf.id.toUpperCase().search(searchChartKeyword.toUpperCase()) >= 0)
                        .map((pData, index) =>
                          <>
                            <TableRow
                              key={index}
                              sx={{
                                '&:last-child td, &:last-child th': { border: 0 },
                              }}
                              className="hover:shadow-md  hover:cursor-pointer">
                              <TableCell style={currentMode === 'Dark' ? { color: 'white', borderBottom: 'none' } : { color: 'black', borderBottom: 'none' }} align="left">
                                {pData?.tag}
                              </TableCell>
                              <TableCell style={currentMode === 'Dark' ? { color: 'white', borderBottom: 'none' } : { color: 'black', borderBottom: 'none' }} align="left">
                                {parseInt(pData.value)?.toFixed(2)}
                              </TableCell>
                              <TableCell style={currentMode === 'Dark' ? { color: 'white', borderBottom: 'none' } : { color: 'black', borderBottom: 'none' }} align="center">
                                {firebase.firestore.Timestamp.now().seconds - pData.time?.seconds < 60 &&
                                  firebase.firestore.Timestamp.now().seconds - pData.time?.seconds > -1 ?
                                  <Badge color="success" variant="dot" /> : <Badge color="warning" variant="dot" />
                                }
                              </TableCell>
                              <TableCell style={currentMode === 'Dark' ? { color: 'white', borderBottom: 'none' } : { color: 'black', borderBottom: 'none' }} align="center">
                                <input className=' w-4 h-4 ' type='checkbox' id={pData.id} style={{ accentColor: currentColor }}
                                  onChange={(e) => handleCheckBox(e)}
                                />
                              </TableCell>
                            </TableRow>
                          </>

                        )
                      ) : (
                        <TableRow

                          sx={{
                            '&:last-child td, &:last-child th': {
                              border: 0,
                            },
                          }}
                        >
                          <TableCell
                            style={currentMode === 'Dark' ? { color: 'white', borderBottom: 'none' } : { color: 'black', borderBottom: 'none' }}
                            align="center"
                            colSpan={4}
                          >
                            {dataLoading && <CircularProgress />}
                            {!dataLoading && <div className=' animate-pulse text-red-500'>No Data</div>}
                          </TableCell>
                        </TableRow>
                      )}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          </div>
        </div> */}

        {/* // */}

        <Dialog fullWidth open={show}>
          <DialogContent
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#161C24", color: "white" }
                : {}
            }
          >
            <div>
              <Typography variant="h4" align="center">
                Add Details
              </Typography>

              <form onSubmit={addLiveData}>
                <DialogContent>
                  <InputLabel style={{ marginBottom: "10px" }}>
                    Title
                  </InputLabel>
                  <TextField
                    onChange={(e) => setTitle(e.target.value)}
                    onBlur={() => setTitle(title?.trim())}
                    style={{ marginBottom: "20px" }}
                    variant="outlined"
                    fullWidth
                    required
                    value={title}
                  />
                  <InputLabel style={{ marginBottom: "5px" }}>
                    Type [Service/Process]
                  </InputLabel>
                  <FormControl required variant="outlined" fullWidth>
                    <Select
                      required
                      value={type}
                      onChange={(e) => setType(e.target.value)}
                    >
                      <MenuItem
                        value={1}
                        style={
                          currentMode === "Dark"
                            ? { backgroundColor: "#161C24", color: "white" }
                            : {}
                        }
                      >
                        Service
                      </MenuItem>
                      <MenuItem
                        value={0}
                        style={
                          currentMode === "Dark"
                            ? { backgroundColor: "#161C24", color: "white" }
                            : {}
                        }
                      >
                        Process
                      </MenuItem>
                    </Select>
                  </FormControl>
                  <InputLabel
                    style={{ marginTop: "20px", marginBottom: "10px" }}
                  >
                    Description
                  </InputLabel>
                  <TextField
                    onChange={(e) => setDesc(e.target.value)}
                    onBlur={() => setDesc(desc?.trim())}
                    style={{ marginBottom: "20px" }}
                    variant="outlined"
                    rows={5}
                    multiline
                    fullWidth
                    required
                    value={desc}
                  />
                  <div
                    style={{
                      marginBottom: "20px",
                      display: "flex",
                      justifyContent: "space-between",
                    }}
                  >
                    <div>
                      <InputLabel style={{ marginBottom: "10px" }}>
                        Background Color
                      </InputLabel>
                      <input
                        required
                        type="color"
                        onChange={(e) => setColor(e.target.value)}
                      />
                      <div>{color}</div>
                    </div>
                    <div>
                      <InputLabel style={{ marginBottom: "10px" }}>
                        Text Color
                      </InputLabel>
                      <input
                        required
                        type="color"
                        onChange={(e) => setTextColor(e.target.value)}
                      />
                      <div>{textColor}</div>
                    </div>
                  </div>
                  <InputLabel style={{ marginBottom: "10px" }}>
                    Image
                  </InputLabel>
                  <input required type="file" onChange={handleChange} />
                  <div className="p-2 block"></div>
                </DialogContent>

                <div
                  style={{
                    marginTop: "10px",
                    display: "flex",
                    justifyContent: "space-between",
                    padding: "1.2rem",
                  }}
                >
                  <ButtonBasic
                    buttonTitle="Cancel"
                    onClick={() => setShow(false)}
                    variant="outlined"
                  />

                  <ButtonBasic
                    buttonTitle="Submit"
                    type="submit"
                    variant="outlined"
                  />
                </div>
              </form>
            </div>
          </DialogContent>
        </Dialog>
        {/* dialog for charts/graphs view :- */}
        <Dialog
          open={showGraph}
          onClose={handleCloseGraph}
          fullWidth
          maxWidth="xl"
        >
          <div className="flex justify-between p-3">
            <div className="font-bold text-xl  ">Live Data Graphs</div>
            <IconButton onClick={() => setShowGraph(false)}>
              <CloseIcon />
            </IconButton>
          </div>

          <div>
            <div className="px-2">
              <AnnotationChart
                processValues={processValues}
                activeChartFromSelected={activeChartFromSelected}
              />
            </div>
          </div>
        </Dialog>

        {/* dialog for MULTI and single charts/graphs view :- */}
        <Dialog
          open={showGraphNew}
          onClose={() => setShowGraphNew(false)}
          fullWidth
          maxWidth="xl"
        >
          <div
            className="flex justify-between p-3 shadow-sm shadow-slate-500"
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#262C2f", color: "white" }
                : { backgroundColor: currentColorLight }
            }
          >
            <div className="font-bold text-xl  ">Live Data Graphs</div>
            <IconButton onClick={() => setShowGraphNew(false)}>
              <CloseIcon />
            </IconButton>
          </div>

          <div>
            <div
              className="px-2"
              style={
                currentMode === "Dark"
                  ? { backgroundColor: "#444", color: "white" }
                  : { backgroundColor: "#444", color: "white" }
              }
            >
              <AnnotationChartNew
                // processValues={processValues}
                activeChartsArrayFromSelected={activeChartsArrayFromSelected}
                processValues={processValues}
              />
            </div>
          </div>
        </Dialog>
      </div>
    </section>
  );
};

export default LiveData;
