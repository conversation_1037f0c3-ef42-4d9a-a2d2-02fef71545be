import React, { useContext, useEffect, useState } from "react";
import { Typography } from "@mui/material";

import "./Alarms.scss";
import { DateRangePicker } from "react-date-range";
import { addDays } from "date-fns";
import BasicMenu from "../../components/menus/BasicMenu";
import Badge from "@mui/material/Badge";
import AddAlertIcon from "@mui/icons-material/AddAlert";
import Box from "@mui/material/Box";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import ACard from "./ACard";
import { useStateContext } from "../../context/ContextProvider";
import usePagination from "../../usePagination";
import { Pagination } from "@mui/material";
import PageHeader from "../../components/commons/page-header.component";
import { AlarmsContext } from "../../services2/alarms/alarms.context";
import { sharedCss } from "../../styles/sharedCss";
import { makeStyles } from "@mui/styles";

const useCustomStyles = makeStyles((theme) => ({
  machineContainer: {
    padding: "1rem",

    borderRadius: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  machinesOuterContainer: {
    width: "100%",
  },
  machinesInnerContainer: {
    display: "flex",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  addButton: {
    width: "max-content",
  },
  machinePageContainer: {
    padding: "1rem",
    border: "1px solid gainsboro",
  },
}));

export default function Alarms() {
  const customCss = useCustomStyles();

  const [shadowOnDate, setShadowOnDate] = useState("");
  const [colorOnDate, setColorOnDate] = useState("");
  const {
    alerts,
    alertsSorted,
    setAlertsSorted,
    allMachines,
    allMachinesWithAlertsCount,
  } = useContext(AlarmsContext);
  const { currentColorLight, currentMode } = useStateContext();
  const [date, setDate] = useState([
    {
      startDate: new Date(),
      endDate: addDays(new Date(), 7),
      key: "selection",
    },
  ]);
  const [selectedMachine, setSelectedMachine] = useState([]);
  const [machineHaveAlerts, setMachineHaveAlerts] = useState(true); //
  const [selectedMachineValue, setSelectedMachineValue] = useState(""); // to show selected name
  const [sortBy, setSortBy] = useState("none");
  const [sortDirection, setSortDirection] = useState("up");
  const [filterBy, setFilterBy] = useState("");
  const [page, setPage] = useState(1);
  const PER_PAGE = 6;
  const count = Math.ceil(alertsSorted.length / PER_PAGE);
  const _DATA = usePagination(alertsSorted, PER_PAGE);
  const [tempAlertsSorted, setTempAlertsSorted] = useState([]);
  const [curPage, setCurPage] = useState(1);
  const commonCss = sharedCss();
  useEffect(() => {
    setTempAlertsSorted(_DATA.currentData());
  }, [alertsSorted]);

  useEffect(() => {
    setTempAlertsSorted(_DATA.currentData());
  }, [curPage]);

  const dateFilter = (item) => {
    setDate([item.selection]);
    let tempAlert = [...alerts];
    let x = tempAlert.filter((data) => {
      if (
        data.time.toDate() <= item.selection.endDate &&
        data.time.toDate() >= item.selection.startDate
      ) {
        return data;
      }
    });

    setAlertsSorted([...x]);
    setShadowOnDate("0px 0px 2px #0fb");
    setColorOnDate("green");
  };
  //
  const cancelDates = () => {
    setAlertsSorted([...alerts]);
    setShadowOnDate("");
    setColorOnDate("");
  };
  //
  const handleChangeSelect = (event) => {
    setSelectedMachine([event.target.value]);
    setSelectedMachineValue(event.target.value);
    if (event.target.value == "all") {
      setSelectedMachine([]);
    }

    if (event.target.value != "all") {
      var temp = 0;
      alerts.map((data) => {
        if (data.mid == event.target.value.id) {
          temp += 1;
        }
      });
      if (temp == "0") {
        setMachineHaveAlerts(false);
      } else {
        setMachineHaveAlerts(true);
      }
    }
  };
  // direction up = increasing & down = decreasing
  const handleSortDirection = (direction) => {
    let temp = tempAlertsSorted;
    if (direction == "up") {
      temp.sort((a, b) => {
        return a.title?.toLowerCase() < b.title?.toLowerCase() ? 1 : -1;
      });
      setSortDirection("down");
    } else if (direction == "down") {
      temp.sort((a, b) => {
        return a.title?.toLowerCase() > b.title?.toLowerCase() ? 1 : -1;
      });
      setSortDirection("up");
    }
    setTempAlertsSorted([...temp]);
  };

  //
  const handleSort = (e) => {
    setSortBy(e.target.value);
    if (e.target.value == "name") {
      let temp = tempAlertsSorted;

      if (sortDirection == "up") {
        temp.sort((a, b) => {
          return a.name?.toLowerCase() > b.name?.toLowerCase() ? 1 : -1;
        });
      } else if (sortDirection == "down") {
        temp.sort((a, b) => {
          return a.name?.toLowerCase() < b.name?.toLowerCase() ? 1 : -1;
        });
      }

      setTempAlertsSorted([...temp]);
    } else if (e.target.value == "last_updated") {
      let temp = tempAlertsSorted;

      if (sortDirection == "up") {
        temp.sort((a, b) => {
          return a.time > b.time ? 1 : -1;
        });
      } else if (sortDirection == "down") {
        temp.sort((a, b) => {
          return a.time < b.time ? 1 : -1;
        });
      }

      setTempAlertsSorted([...temp]);
    } else if (e.target.value == "none") {
      setAlertsSorted([...alerts]);
    }
  };
  // filter by resolve and unresolve
  const handleFilter = (e) => {
    setFilterBy(e.target.value);
    if (e.target.value == "resolved") {
      let temp = [...alertsSorted];
      let filterd = temp.filter((data) => data.status === true);
      setTempAlertsSorted([...filterd]);
    } else if (e.target.value === "unresolved") {
      let temp = [...alertsSorted];
      let filterd = temp.filter((data) => data.status === false);
      setTempAlertsSorted([...filterd]);
    } else if (e.target.value === "none") {
      setAlertsSorted([...alerts]);
    }
  };

  const handleChange = (e, p) => {
    console.log(p);
    setPage(p);
    _DATA.jump(p);
    setCurPage(p);
  };

  return (
    <section className={customCss.machinePageContainer}>
      <header className={commonCss?.headingContainer}>
        <div>
          <div>
            <Box>
              <Typography variant="h4">Alarms</Typography>
              <Typography variant="subtitle1">
                Alarms & Major Warnings
              </Typography>
            </Box>
          </div>
        </div>
        <Box
          style={{
            display: "flex",
            flexDirection: "row",
            gap: "1rem",
            placeItems: "center",
          }}
        >
          <span className="self-center">
            {sortBy != "none" && (
              <>
                {sortDirection == "up" ? (
                  <i
                    className="ri-arrow-up-line hover:cursor-pointer hover:font-bold self-center"
                    onClick={() => handleSortDirection("up")}
                  ></i>
                ) : (
                  <i
                    className="ri-arrow-down-line hover:cursor-pointer hover:font-bold self-center"
                    onClick={() => handleSortDirection("down")}
                  ></i>
                )}
              </>
            )}
          </span>
          <span className="mr-4">
            <Box sx={{ minWidth: 180 }}>
              <FormControl fullWidth size="small">
                <InputLabel id="demo-simple-select-label">Sort</InputLabel>
                <Select
                  labelId="demo-simple-select-label"
                  id="demo-simple-select"
                  value={sortBy}
                  label="Sort"
                  onChange={(e) => handleSort(e)}
                >
                  <MenuItem value="none">None</MenuItem>
                  <MenuItem value="name">By Name</MenuItem>
                  <MenuItem value="last_updated">By Last Updated</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </span>
          <span className="mr-4">
            <FormControl sx={{ minWidth: 180 }} size="small" fullWidth>
              <InputLabel id="demo-simple-select-label">Machine</InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={selectedMachineValue}
                label="Machine"
                onChange={handleChangeSelect}
              >
                <MenuItem value="all">All Machines</MenuItem>
                {allMachinesWithAlertsCount?.map((data, index) => (
                  <MenuItem key={index} value={data}>
                    {" "}
                    {data.title}
                    {data.alertsCount > 0 && (
                      <Badge
                        badgeContent={
                          data.alertsCount > 0 ? data.alertsCount : null
                        }
                      >
                        <AddAlertIcon
                          className="text-red-500"
                          fontSize="small"
                        />
                      </Badge>
                    )}
                    {/* {data.title} {data.alertsCount > 0 ? data.alertsCount : null} */}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </span>
          <span className="mr-4">
            <Box sx={{ minWidth: 100 }}>
              <FormControl fullWidth size="small">
                <InputLabel id="demo-simple-select-label-filter">
                  Filter
                </InputLabel>
                <Select
                  labelId="demo-simple-select-label-filter"
                  id="demo-simple-select-filter"
                  value={filterBy}
                  label="Filter"
                  onChange={(e) => handleFilter(e)}
                >
                  <MenuItem value="none">None</MenuItem>
                  <MenuItem value="resolved">Resolved</MenuItem>
                  <MenuItem value="unresolved">Unresolved</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </span>
          {colorOnDate == "green" && (
            <span
              title="Cancel date selection"
              className="self-center hover:text-gray-500 hover:cursor-pointer"
              onClick={() => cancelDates()}
            >
              <i class="ri-close-circle-fill  " />
            </span>
          )}

          <span title="Filter by date" className="mr-4">
            <BasicMenu
              textShadow={shadowOnDate}
              color={colorOnDate}
              fontSize="1.5rem"
              items={
                <DateRangePicker
                  onChange={(item) => dateFilter(item)}
                  showSelectionPreview={true}
                  moveRangeOnFirstSelection={false}
                  months={1}
                  ranges={date}
                  direction="horizontal"
                />
              }
            />
          </span>
        </Box>
      </header>
      <main className="p-0 rounded-xl">
        <div className={commonCss?.generalBackground}>
          <div className="flex flex-col items-center py-10 px-10">
            <div className="flex flex-wrap" style={{ width: "100%" }}>
              {selectedMachine?.length > 0 ? (
                <>
                  {" "}
                  {machineHaveAlerts ? (
                    <>
                      {selectedMachine?.map((mData) =>
                        tempAlertsSorted.map((alertData) => (
                          <>
                            {mData.id === alertData.mid && (
                              <ACard
                                machineData={allMachines}
                                alerts={alertData}
                              />
                            )}
                          </>
                        )),
                      )}
                    </>
                  ) : (
                    <div className="flex  justify-center mt-1">No Data</div>
                  )}
                </>
              ) : (
                <div className="flex flex-wrap" style={{ width: "100%" }}>
                  {tempAlertsSorted?.map((alertData, index) => (
                    <ACard machineData={allMachines} alerts={alertData} />
                  ))}
                </div>
              )}
              {alertsSorted?.length == 0 && <div>No alram found...</div>}
            </div>
            <Pagination count={count} page={page} onChange={handleChange} />
          </div>
        </div>
      </main>
    </section>
  );
}
