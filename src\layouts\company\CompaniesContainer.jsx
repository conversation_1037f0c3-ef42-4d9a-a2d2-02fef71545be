import React, { useEffect, useState, useMemo } from "react";
import CompanyCard from "./CompanyCard";
import { db, auth } from "../../firebase"; // Replace 'storage' with 'auth' or correct export
import { firebaseLooper } from "../../tools/tool";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { DropzoneArea } from "material-ui-dropzone";
import { useStorage } from "../../hooks/useStorage";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import { companies } from "../../constants/data";
import Typography from "@mui/material/Typography";
import Pagination from "@mui/material/Pagination";
import Stack from "@mui/material/Stack";
import Box from "@mui/material/Box";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
//import {useCompanyIdSeter}  from '../../context/CompanyIdContext'.
import {
  useCompanyId,
  useCompanyIdSeter,
} from "../../context/CompanyIdContext";
import { useNavigate } from "react-router-dom";
import { useStateContext } from "../../context/ContextProvider";
import { themeColors } from "../../infrastructure/theme";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";

const CompaniesContainer = () => {
  const [companiesAll, setAllCompaniesAll] = useState([]);
  const [companiesInView, setCompaniesInView] = useState([]);
  const [companiesInViewContainerCount, setCompaniesInViewContainerCount] =
    useState(0); // number of pages
  const [page, setPage] = useState(1);
  //
  const [numOfCompanyInView, setNumOfCompanyInView] = useState(6);
  //
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState();
  const [description, setDescription] = useState();
  const [domain, setDomain] = useState();
  const [theme, setTheme] = useState();
  const [imageUrl, setImageUrl] = useState();
  const [newImageSelected, setNewImageSelected] = useState(false);
  const [file, setFile] = useState();
  const [docId, setDocId] = useState();
  const [searchKey, setSearchKey] = useState();
  const [searchedCompanies, setSearchedCompanies] = useState([]);
  const { currentColor, currentColorLight, currentMode } = useStateContext();

  const history = useNavigate();

  const companyIdSetterFun = useCompanyIdSeter();

  useEffect(() => {
    // db.collection(companies)
    //   .onSnapshot(snap => {
    //     const companyData = firebaseLooper(snap)
    //     let companyActivePageNum = window.localStorage.getItem('companyActivePageNum'); // for preventing pagination mismatch while editing 2/3
    //     let numOfCompanyInViewSession = window.localStorage.getItem('numOfCompanyInView');//
    //     setAllCompaniesAll(companyData);
    //     companyPageInfoContainerCounter(companyData.length, numOfCompanyInView);
    //     setCompaniesInView(companyData.slice(0, numOfCompanyInView));
    //     // for preventing pagination mismatch while editing 3/3
    //     if (companyActivePageNum != 1 && companyActivePageNum) {
    //       let companiesInViewContainerCountTemp = companyPageInfoContainerCounter(companyData.length, numOfCompanyInViewSession); //if it is last page
    //       if (companyActivePageNum == companiesInViewContainerCountTemp) {
    //         setCompaniesInView(companyData.slice(((companyActivePageNum - 1) * numOfCompanyInViewSession)));
    //         setNumOfCompanyInView(numOfCompanyInViewSession);
    //         setPage(companyActivePageNum);
    //         setNumOfCompanyInView(numOfCompanyInViewSession)
    //       }
    //       else { // if it's not the last page
    //         setCompaniesInView(companyData.slice(((companyActivePageNum - 1) * numOfCompanyInViewSession), ((companyActivePageNum) * numOfCompanyInViewSession)));
    //         setNumOfCompanyInView(numOfCompanyInViewSession);
    //         setPage(companyActivePageNum);
    //         setNumOfCompanyInView(numOfCompanyInViewSession)
    //       }
    //     }
    //   })
  }, []);
  //
  const handleChangeNumberOfCompanyInVew = (event) => {
    setNumOfCompanyInView(event.target.value);
    companyPageInfoContainerCounter(companiesAll?.length, event.target.value);
    setCompaniesInView(companiesAll.slice(0, event.target.value));
    window.localStorage.setItem("numOfCompanyInView", event.target.value); //for preventing pagination mismatch while editing 1.1/3
  };
  //
  const companyPageInfoContainerCounter = (
    companiesCount,
    numOfCompanyInViewProp,
  ) => {
    let temp = Math.ceil(companiesCount / numOfCompanyInViewProp);
    setCompaniesInViewContainerCount(temp);
    return temp;
  };
  //
  const handlePageChange = (e, value) => {
    window.localStorage.setItem("companyActivePageNum", value); // for preventing pagination mismatch while editing 1/3
    window.localStorage.setItem("numOfCompanyInView", numOfCompanyInView); //
    setPage(value);

    if (searchedCompanies.length > 0) {
      if (value == companiesInViewContainerCount) {
        setCompaniesInView(
          searchedCompanies?.slice((value - 1) * numOfCompanyInView),
        );
      } else {
        setCompaniesInView(
          searchedCompanies?.slice(
            (value - 1) * numOfCompanyInView,
            value * numOfCompanyInView,
          ),
        );
      }
    } else {
      if (value == companiesInViewContainerCount) {
        setCompaniesInView(
          companiesAll.slice((value - 1) * numOfCompanyInView),
        );
      } else {
        setCompaniesInView(
          companiesAll.slice(
            (value - 1) * numOfCompanyInView,
            value * numOfCompanyInView,
          ),
        );
      }
    }
  };
  //
  const handleCardView = (data) => {
    let promise = new Promise((resolve, reject) => {
      resolve();
    });
    promise.then(() => {
      //   db.collection("A_companyData")
      //     .doc(data.id)
      //     .collection("AR")
      //     .doc("temp")
      //     .get()
      //     .then((doc) => {
      //       //console.log("1:", doc.data())
      //       if (doc.data()) {
      //         companyIdSetterFun(data.id);
      //         history.replace("/");
      //         window.location.reload(false);
      //       } else {
      //         toastMessageWarning({
      //           message: "Company has limited or no Data...",
      //         });
      //         companyIdSetterFun(data.id);
      //         history.replace("/");
      //         window.location.reload(false);
      //       }
      //     })
      //     .catch((e) => console.log("2:", e));
    });
  };
  //
  const handleCardEdit = (data) => {
    setTitle(data.title);
    setDescription(data.desc);
    setDomain(data.domain);
    setTheme(data.theme);
    setImageUrl(data.url);
    setDocId(data.id);
    setOpen(true);
    setFile("");
  };

  const handleClose = () => {
    setOpen(false);
  };
  //
  const handleChange = (loadedFiles) => {
    let selectedFile = loadedFiles[0];
    if (selectedFile) {
      setFile(selectedFile);

      setNewImageSelected(true);
    } else {
      setNewImageSelected(false);
    }
  };

  const { progress, url } = useStorage(file, "companies_dp");

  //
  const handleSave = () => {
    if (title && domain && theme && description) {
      // db.collection(companies)
      //   .doc(docId)
      //   .update({
      //     title: title,
      //     theme: theme,
      //     domain: domain,
      //     desc: description,
      //     url: newImageSelected ? url : imageUrl,
      //   })
      //   .then(() => {
      //     setOpen(false);
      //     toastMessageSuccess({
      //       message: "successfully updated",
      //       type: "success",
      //     });
      //   });
    } else {
      // toastMessage({ message: "Missing field" });
    }
  };
  //To delete the file if selected i.e stored in the firebase storage
  const handleCancel = () => {
    if (newImageSelected) {
      var fileRef = auth.refFromURL(url); // Replace 'storage' with 'auth'
      fileRef
        .delete()
        .then(function () {
          // console.log("File Deleted");
          setOpen(false);
        })
        .catch(function (error) {
          console.log("file cancel:", error);
        });
    } else {
      setOpen(false);
    }
  };
  // search option
  const searchCompanies = (e) => {
    setSearchKey(e.target.value);
    //console.log("e", e.target.value)
    const temp = companiesAll;
    //console.log("companies:",temp)
    const temp2 = temp?.filter(
      (a) =>
        a.title?.toUpperCase().search(e.target.value.toUpperCase()) >= 0 || // # tag can be done by >= 0
        a.domain?.toUpperCase().search(e.target.value.toUpperCase()) >= 0,
    );
    //console.log("machineInView filterd:", temp2);
    setSearchedCompanies([...temp2]); // for pagination
    setCompaniesInView(temp2?.slice(0, numOfCompanyInView));
    companyPageInfoContainerCounter(temp2?.length, numOfCompanyInView);
  };

  return (
    <div
      className="companiesContainerSection"
      style={
        currentMode === "Dark"
          ? { backgroundColor: themeColors.dark.primary, color: "white" }
          : { backgroundColor: currentColorLight }
      }
    >
      <div className="companiesHeading">
        <div className="info">
          <h3>Companies</h3>
          {/* <p>List of all the active Companies.</p> */}
        </div>
        <div className="headingRightContainer">
          {/* search Box */}
          <Box
            component="form"
            sx={{
              paddingX: 1,
              "& > :not(style)": { width: "25ch" },
            }}
          >
            <TextField
              label="Search"
              id="outlined-size-small"
              //defaultValue="Name, Eqip id"
              placeholder="Name, domain"
              size="small"
              value={searchKey}
              onChange={(e) => searchCompanies(e)}
            />
          </Box>

          {/* item count box */}
          <Box sx={{ minWidth: 50, paddingLeft: 1 }}>
            <FormControl fullWidth size="small">
              <InputLabel id="demo-simple-select-label">Item</InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={numOfCompanyInView}
                label="Age"
                onChange={handleChangeNumberOfCompanyInVew}
              >
                <MenuItem value={6}>6</MenuItem>
                <MenuItem value={12}>12</MenuItem>
                <MenuItem value={18}>18</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </div>
      </div>

      {/* <div className='flex flex-col justify-between bg-red-500'> */}
      <div className="cardsContainer">
        {companiesInView
          ? companiesInView.map((data) => (
              <div>
                <CompanyCard
                  url={data.url}
                  mid={data.id}
                  title={data.title}
                  description={data.desc.slice(0, 15) + "..."}
                  domain={data.domain}
                  theme={data.theme}
                  onClickEdit={() => handleCardEdit(data)}
                  onClickView={() => handleCardView(data)}
                />
              </div>
            ))
          : null}
      </div>

      <div className="flex justify-center py-6">
        <Stack spacing={2}>
          <Pagination
            count={companiesInViewContainerCount}
            page={page}
            onChange={handlePageChange}
          />
        </Stack>
      </div>
      {/* </div> */}

      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Edit</DialogTitle>
        <DialogContent>
          <TextField
            margin="dense"
            id="title"
            label="Title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            fullWidth
            variant="standard"
          />
          <TextField
            margin="dense"
            id="desc"
            label="Description"
            type="text"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            fullWidth
            variant="standard"
          />
          <TextField
            margin="dense"
            id="domain"
            label="Domain"
            type="text"
            value={domain}
            onChange={(e) => setDomain(e.target.value)}
            fullWidth
            variant="standard"
          />
          <TextField
            margin="dense"
            id="theme"
            label="Theme"
            type="color"
            value={theme}
            onChange={(e) => setTheme(e.target.value)}
            fullWidth
            variant="standard"
          />
          <div className="text-gray-500 ">Image Section</div>
          <div className="flex justify-between bg-gray-300 p-2 rounded-sm mt-1">
            <div className=" w-1/2 justify-center justify-items-center items-center flex flex-col">
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) => handleChange(loadedFiles)}
                dropzoneText="Drag and Drop / Click to ADD Media"
                showAlerts={false}
                filesLimit={1}
              />

              {
                <div className="text-2xl text-gray-700 flex justify-end">
                  {file ? <p> {progress} % Uploaded</p> : null}
                </div>
              }
            </div>
            <img
              src={imageUrl}
              width="auto"
              className="max-w-xs max-h-40 self-center"
            ></img>
          </div>
        </DialogContent>
        <DialogActions>
          <ButtonBasicCancel
            buttonTitle="Cancel"
            onClick={() => handleCancel()}
          />
          <ButtonBasic buttonTitle="Update" onClick={() => handleSave()} />
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default CompaniesContainer;
