import React, {useEffect, useState} from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  MenuItem,
  Box,
} from "@mui/material";
import {dbConfig} from "../../../infrastructure/db/db-config";
import axios from "axios";
import {toastMessage, toastMessageSuccess} from "../../../tools/toast";
import {
  ButtonBasicCancel,
  SubmitButtons,
} from "../../../components/buttons/Buttons";
import {useStateContext} from "../../../context/ContextProvider";

const alarmTypes = [
  {label: "Process", value: 0},
  {label: "Service", value: 1},
  {label: "System", value: 2},
];

const EditAlarm = ({
  open,
  handleClose,
  alarm,
  onAlarmUpdated,
  maintenanceOfMachine,
  alarmSopData,
}) => {
  const {currentMode} = useStateContext();

  const [formData, setFormData] = useState({
    tag: "",
    desc: "",
    code: "",
    reason: "",
    action_taken: "",
    type: "",
    def_value: "",
    main_id: "",
    alarm_sop_id: "",
  });

  useEffect(() => {
    if (alarm) {
      setFormData({
        tag: alarm.tag || "",
        desc: alarm.desc || "",
        code: alarm.code || "",
        reason: alarm.reason || "",
        action_taken: alarm.action_taken || "",
        type: alarm.type ?? "",
        def_value: alarm.def_value || "",
        main_id: alarm.main_id || "",
        alarm_sop_id: alarm.alarm_sop_id || "",
      });
    }
  }, [alarm]);

  const handleChange = e => {
    setFormData(prev => ({...prev, [e.target.name]: e.target.value}));
  };

  const handleSubmit = async () => {
    try {
      const {type, ...formDataForDedicatedAlarmSop} = formData;
      const payload = formData.main_id
        ? formData
        : formDataForDedicatedAlarmSop;
      await axios.put(`${dbConfig.url}/alarms_new/${alarm._id}`, payload);
      toastMessageSuccess({message: "Alarm updated successfully"});
      handleClose();
      onAlarmUpdated();
    } catch (err) {
      console.error(err);
      toastMessage({message: "Failed to update alarm"});
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth="sm">
      <DialogTitle
        sx={{
          backgroundColor: currentMode === "Dark" ? "#212B36" : undefined,
          color: currentMode === "Dark" ? "#fff" : undefined,
        }}>
        Edit Alarm
      </DialogTitle>
      <DialogContent
        dividers
        sx={{
          backgroundColor: currentMode === "Dark" ? "#212B36" : undefined,
          color: currentMode === "Dark" ? "#fff" : undefined,
        }}>
        <Box
          component="form"
          sx={{display: "flex", flexDirection: "column", gap: 2}}>
          <TextField
            select
            fullWidth
            label={formData.main_id ? "Select Maintenance" : "Select Alarm SOP"}
            name={formData.main_id ? "main_id" : "alarm_sop_id"}
            value={formData.main_id ? formData.main_id : formData.alarm_sop_id}
            onChange={handleChange}>
            {(formData.main_id
              ? maintenanceOfMachine || []
              : alarmSopData || []
            )?.map(m => (
              <MenuItem key={m._id} value={m._id}>
                {m.title}
              </MenuItem>
            ))}
          </TextField>

          {[
            {label: "Tag", name: "tag"},
            {label: "Description", name: "desc"},
            {label: "Code", name: "code"},
            {label: "Reason", name: "reason"},
            {label: "Action Taken", name: "action_taken"},
            {label: "Default Value", name: "def_value"},
          ].map(field => (
            <TextField
              key={field.name}
              fullWidth
              label={field.label}
              name={field.name}
              value={formData[field.name]}
              onChange={handleChange}
            />
          ))}

          {formData.type && (
            <TextField
              select
              fullWidth
              margin="dense"
              label="Type"
              name="type"
              value={formData.type}
              onChange={handleChange}>
              {alarmTypes.map(type => (
                <MenuItem key={type.value} value={type.value}>
                  {type.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        </Box>
      </DialogContent>

      <Box sx={{p: 2, display: "flex", justifyContent: "space-between"}}>
        <ButtonBasicCancel
          buttonTitle="Cancel"
          type="button"
          onClick={handleClose}
        />
        <SubmitButtons
          buttonTitle="Update Alarm"
          type="button"
          onClick={handleSubmit}
        />
      </Box>
    </Dialog>
  );
};

export default EditAlarm;
