import {
  Avatar,
  Box,
  Button,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Menu,
  MenuItem,
  Switch,
  TableCell,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import axios from "axios";
import EditIcon from "@mui/icons-material/Edit";
import Delete from "./delete";
import { convertBase64 } from "./hooks/convertBase64";
import { toast } from "react-toastify";
import LockOpenIcon from "@mui/icons-material/LockOpen";
import LockOutlinedIcon from "@mui/icons-material/LockOutlined";
import { RemoveCircleOutline } from "@mui/icons-material";
import { IoCloudUploadOutline, IoTrashBin } from "react-icons/io5";
import AcceptRejectAlertModal from "./UI_Components/alert-ui";
import PropTypes from "prop-types";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useStateContext } from "../../context/ContextProvider";
import { commonRowStyle } from "../machineData/MaintenanceReportDataMain";
import { useCheckAccess } from "../../utils/useCheckAccess";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useUtils } from "../../hooks/UtilsProvider";

const MAX_FILE_SIZE = 2 * 1024 * 1024;

// For displaying role name in table (user?.role is numeric ID in DB)
const displayRoleName = (roleValue, roles) => {
  if (!roles || roles.length === 0) return roleValue || "";
  const roleObj = roles.find((r) => r.id === Number(roleValue));
  return roleObj
    ? roleObj.name.charAt(0).toUpperCase() + roleObj.name.slice(1)
    : roleValue || "";
};

// Helper to get role name for edit dropdown
const getRoleNameForEdit = (roleValue, roles) => {
  if (!roles || roles.length === 0) return "";
  if (typeof roleValue === "string" && /^\d+$/.test(roleValue)) {
    const found = roles.find((r) => r.id === Number(roleValue));
    return found ? found.name : "";
  }
  if (typeof roleValue === "number") {
    const found = roles.find((r) => r.id === roleValue);
    return found ? found.name : "";
  }
  if (typeof roleValue === "string") {
    const found = roles.find(
      (r) => r.name.toLowerCase() === roleValue.toLowerCase(),
    );
    return found ? found.name : roleValue;
  }
  return "";
};

const UserItem = ({ user, fetchAllUsers, isModelOpen }) => {
  const { envData, isFetching } = useUtils(); // Access envData and isFetching from useUtils
  const [open, setOpen] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [userData, setUserData] = useState({ ...user });
  const types = ["image/png", "image/jpeg", "image/jpg"];
  const [fileUrl, setFileUrl] = useState("");
  const [changeonuser, setChangeonuser] = useState(false);
  const currentUserTemp = sessionStorage.getItem("@user-creds");
  const [filesizeAlert, setFilesizeAlert] = useState(false);
  const currentUser = JSON.parse(currentUserTemp || "{}");
  const isAdmin = currentUser?.role === 5; // Assuming role 5 is admin
  const [signatureFileUrl, setSignatureFileUrl] = useState("");
  const [file, setFile] = useState(null); // profile picture file state
  const [signatureFile, setSignatureFile] = useState(null); // signature file state
  const [openConfirm, setOpenConfirm] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const { currentMode } = useStateContext();

  const hasUserPUTAccess = useCheckAccess("users", "PUT");
  const hasUserDELETEAccess = useCheckAccess("users", "DELETE");

  // Add a handler to toggle the `notify` state
  const handleNotifyToggle = () => {
    setUserData((prevUserData) => ({
      ...prevUserData,
      notify: !prevUserData.notify,
    }));
    setChangeonuser(true);
  };

  const reactivateUser = async () => {
    await axios
      .put(`${dbConfig.url}/users/${user._id}`, { attempt: 0 })
      .then(() => {
        toast.success("User Account Activated successfully!", {
          position: toast.POSITION.TOP_RIGHT,
        });
        fetchAllUsers();
      })
      .catch(() => {})
      .finally(() => {});
  };

  useEffect(() => {
    setUserData(user);
  }, [user, open]);

  const deactivateUser = async () => {
    await axios
      .put(`${dbConfig.url}/users/${user._id}`, { attempt: 6 })
      .then(() => {
        toast.success("User Account Deactivated Successfully!", {
          position: toast.POSITION.TOP_RIGHT,
        });
        fetchAllUsers();
      })
      .catch(() => {})
      .finally(() => {});
  };

  const handleChangeSignature = async (e) => {
    let selectedFile = e.target.files[0];
    const formData = new FormData();
    formData.append("image", file);
    setSignatureFile(selectedFile);
    if (selectedFile?.size <= MAX_FILE_SIZE) {
      setFilesizeAlert(false);
      const res = await axios.post(`${dbConfig.url}/upload`, formData )
      if (selectedFile && types.includes(selectedFile.type)) {
        setChangeonuser(true);
        setSignatureFileUrl(res?.data?.data);
        toast.success(`Signature Uploaded successfully`, {
          position: "top-right",
        });
      }
    } else {
      setFilesizeAlert(true);
      setSignatureFileUrl("");
    }
  };

  function openModal() {
    setFileUrl(`${dbConfig.url_storage}/images/${user?.avatar}` || "");
    setSignatureFileUrl(`${dbConfig.url_storage}/images/${user?.signature}` || "");
    setOpen(true);
  }

  function closeModal() {
    setChangeonuser(false);
    setFilesizeAlert(false);
    setSignatureFileUrl("");
    setOpen(false);
    setFileUrl("");
    setFile(null);
    setSignatureFile(null);
  }

  const handleSubmit = async () => {
    if (filesizeAlert) {
      toast.warning("File size should be less than 2 MB");
      return;
    }
    setChangeonuser(false);

    // Prepare changed fields only
    const changedFields = {};
    if ((userData.fname || "") !== (user.fname || ""))
      changedFields.fname = userData.fname;
    if ((userData.lname || "") !== (user.lname || ""))
      changedFields.lname = userData.lname;
    if ((userData.username || "") !== (user.username || ""))
      changedFields.username = userData.username;
    if ((userData.phone || "") !== (user.phone || ""))
      changedFields.phone = userData.phone?.toString();
    // Compare role by id
    const selectedRoleObj = envData.ROLES?.find(
      (r) => r.name.toLowerCase() === userData.role.toLowerCase(),
    );
    const currentRoleObj = envData.ROLES?.find(
      (r) => r.id === Number(user.role),
    );
    if (
      selectedRoleObj &&
      (!currentRoleObj ||
        selectedRoleObj.id !== currentRoleObj.id)
    ) {
      changedFields.role = selectedRoleObj.id; // ✅ This keeps it as number
    }
    if (!!userData.notify !== !!user.notify)
      changedFields.notify = !!userData.notify;
    if (
      (fileUrl === "" && user.avatar) ||
      (fileUrl?.length > 0 && fileUrl !== user.avatar)
    ) {
      changedFields.avatar = fileUrl;
    }
    if (
      (signatureFileUrl === "" && user.signature) ||
      (signatureFileUrl?.length > 0 && signatureFileUrl !== user.signature)
    ) {
      changedFields.signature = signatureFileUrl;
    }

    if (Object.keys(changedFields).length === 0) {
      toast.info("No changes to update.");
      return;
    }

    await axios
      .put(`${dbConfig.url}/users/${userData._id}`, changedFields)
      .then(() => {
        toast.success("User Updated successfully!", {
          position: toast.POSITION.TOP_RIGHT,
        });
      })
      .catch(() => {
        toast.error("Something went wrong");
      })
      .finally(() => {
        setFilesizeAlert(false);
        setSignatureFileUrl("");
        fetchAllUsers();
        closeModal();
      });
  };

  const handleDelete = async () => {
    await axios
      .delete(`${dbConfig.url}/users/${userData._id}`)
      .then(() => {
        toast.success("User Deleted successfully!", {
          position: toast.POSITION.TOP_RIGHT,
        });
        setOpenDelete(false);
      })
      .catch(() => {})
      .finally(() => {});
  };

  const handleChange = async (e) => {
    let selectedFile = e.target.files[0];
    setFile(selectedFile);
    if (selectedFile?.size <= MAX_FILE_SIZE) {
      setFilesizeAlert(false);
      setChangeonuser(true);
      const formData = new FormData();
      formData.append("image", selectedFile);
      const res = await axios.post(`${dbConfig.url}/upload`, formData );
      const base64 = res?.data?.data;
      if (selectedFile && types.includes(selectedFile.type)) {
        setFileUrl(base64);
        toast.success(`Profile Image Uploaded successfully`, {
          position: "top-right",
        });
      }
    } else {
      setFilesizeAlert(true);
      setFileUrl("");
      setChangeonuser(false);
    }
  };

  const handleRemoveUser = async () => {
    try {
      await axios.delete(`${dbConfig.url}/users/${userData._id}`);
      toast.success("User Deleted successfully!", {
        position: toast.POSITION.TOP_RIGHT,
      });
      fetchAllUsers();
    } catch (error) {
      console.error("Error deleting user:", error);
      toast.error("Failed to delete user. Please try again later.", {
        position: toast.POSITION.TOP_RIGHT,
      });
    }
  };

  const removeProfilePicture = () => {
    setFile(null);
    setFileUrl("");
    setChangeonuser(true);
    setUserData((prev) => ({
      ...prev,
      avatar: "",
    }));
  };

  const removeSignature = () => {
    setSignatureFile(null);
    setSignatureFileUrl("");
    setChangeonuser(true);
    setUserData((prev) => ({
      ...prev,
      signature: "",
    }));
  };

  const [anchorEl, setAnchorEl] = useState(null);
  const openMenu = Boolean(anchorEl);
  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <TableRow key={user?.id} sx={commonRowStyle}>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          {user?.fname + " " + user?.lname}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          <div
            style={{
              display: "flex",
              justifyContent: "flex-start",
              alignItems: "center",
            }}
          >
            <Avatar
              sx={{ width: 36, height: 36, marginRight: "16px" }}
              src={`${dbConfig?.url_storage}/images/${user?.avatar}`}
            />
            {user?.username || ""}
          </div>
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          {displayRoleName(user?.role, envData.ROLES)}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          {user?.email}
        </TableCell>
        {/* Conditionally render Actions only for Admin */}
        {isAdmin && (
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="center"
        >
          <IconButton
            aria-label="more"
            aria-controls={openMenu ? "action-menu" : undefined}
            aria-haspopup="true"
            aria-expanded={openMenu ? "true" : undefined}
            onClick={handleMenuClick}
          >
            <MoreVertIcon />
          </IconButton>
          <Menu
            id="action-menu"
            anchorEl={anchorEl}
            open={openMenu}
            onClose={handleClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
          >
            {user?.attempt >= 6 ? (
              <Tooltip title="User is deactivated. Click to activate" arrow>
                <MenuItem
                  onClick={() => {
                    setActionToConfirm("reactivate");
                    setOpenConfirm(true);
                    handleClose();
                  }}
                  disabled={!hasUserPUTAccess}
                >
                  <LockOutlinedIcon
                    sx={{ color: "red", mr: 1 }}
                    fontSize="small"
                  />
                  Deactivated
                </MenuItem>
              </Tooltip>
            ) : (
              <Tooltip title="User is activated. Click to deactivate" arrow>
                <MenuItem
                  onClick={() => {
                    setActionToConfirm("deactivate");
                    setOpenConfirm(true);
                    handleClose();
                  }}
                  disabled={
                    user?.email === currentUser?.email || !hasUserPUTAccess
                  }
                >
                  <LockOpenIcon
                    sx={{ color: "green", mr: 1 }}
                    fontSize="small"
                  />
                  Activated
                </MenuItem>
              </Tooltip>
            )}
            <MenuItem
              onClick={() => {
                openModal();
                handleClose();
              }}
              disabled={!hasUserPUTAccess}
            >
              <EditIcon sx={{ mr: 1 }} fontSize="small" />
              Edit
            </MenuItem>
            <MenuItem
              onClick={() => {
                setActionToConfirm("remove");
                setOpenConfirm(true);
                handleClose();
              }}
              disabled={
                user?.email === currentUser?.email || !hasUserDELETEAccess
              }
            >
              <RemoveCircleOutline
                sx={{ color: "red", mr: 1 }}
                fontSize="small"
              />
              Remove
            </MenuItem>
          </Menu>
        </TableCell>
        )}
      </TableRow>

      <Dialog open={openDelete} onClose={() => setOpenDelete(false)}>
        <Delete onClose={() => setOpenDelete(false)} onDelete={handleDelete} />
      </Dialog>
      <Dialog fullWidth open={open} onClose={closeModal}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            setOpenConfirm(true);
            setActionToConfirm("update");
          }}
        >
          <DialogTitle>Edit User</DialogTitle>
          <DialogContent>
            <div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-around",
                  gap: "24px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "0.5rem",
                  }}
                >
                  <Avatar
                    src={
                      fileUrl === ""
                        ? undefined
                        : fileUrl
                          ? fileUrl
                          : `${dbConfig?.url_storage}/images/${user?.avatar}` || undefined
                    }
                    alt="profile"
                    sx={{
                      width: "100px",
                      height: "100px",
                      display: "flex",
                      border: "1px dashed lightgray",
                    }}
                  />
                  <Typography
                    fontSize={12}
                    sx={{
                      width: "156px",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                      whiteSpace: "nowrap",
                      textAlign: "center",
                    }}
                  >
                    {file ? file.name : "Upload Profile Picture"}
                  </Typography>
                  <div
                    style={{
                      display: "flex",
                      gap: "24px",
                      width: "100%",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Button
                      sx={{
                        fontSize: "20px",
                        color: "blue",
                        mt: 1,
                        fontWeight: "bold",
                      }}
                    >
                      <label htmlFor="profilePic">
                        <IoCloudUploadOutline />
                      </label>
                    </Button>
                    <input
                      id="profilePic"
                      onChange={handleChange}
                      type="file"
                      accept="image/png, image/jpeg, image/jpg"
                      style={{
                        cursor: "pointer",
                        marginLeft: "80px",
                        display: "none",
                      }}
                    />
                    <Button
                      onClick={removeProfilePicture}
                      sx={{ color: "red", fontSize: "20px" }}
                    >
                      <IoTrashBin />
                    </Button>
                  </div>
                  {file && file.size > MAX_FILE_SIZE && (
                    <Typography
                      style={{
                        fontSize: ".8rem",
                        color: "red",
                        marginBottom: ".2rem",
                      }}
                    >
                      Max file size 2MB
                    </Typography>
                  )}
                </div>

                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "0.5rem",
                  }}
                >
                  <Avatar
                    src={
                      signatureFileUrl === ""
                        ? undefined
                        : signatureFileUrl
                          ? signatureFileUrl
                          : `${dbConfig?.url_storage}/images/${user?.signature}` || undefined
                    }
                    alt="signature"
                    sx={{
                      width: "100px",
                      height: "100px",
                      display: "flex",
                      border: "1px dashed lightgray",
                    }}
                  />
                  <Typography
                    fontSize={12}
                    sx={{
                      width: "156px",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                      whiteSpace: "nowrap",
                      textAlign: "center",
                    }}
                  >
                    {signatureFile ? signatureFile.name : "Upload Signature"}
                  </Typography>
                  <div
                    style={{
                      display: "flex",
                      gap: "24px",
                      width: "100%",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Button
                      sx={{
                        fontSize: "20px",
                        color: "blue",
                        mt: 1,
                        fontWeight: "bold",
                      }}
                    >
                      <label htmlFor="signaturePic">
                        <IoCloudUploadOutline />
                      </label>
                    </Button>
                    <input
                      id="signaturePic"
                      onChange={handleChangeSignature}
                      type="file"
                      accept="image/png, image/jpeg, image/jpg"
                      style={{
                        cursor: "pointer",
                        marginLeft: "80px",
                        display: "none",
                      }}
                    />
                    <Button
                      onClick={removeSignature}
                      sx={{ color: "red", fontSize: "20px" }}
                    >
                      <IoTrashBin />
                    </Button>
                  </div>
                  {signatureFile && signatureFile.size > MAX_FILE_SIZE && (
                    <Typography
                      style={{
                        fontSize: ".8rem",
                        color: "red",
                        marginBottom: ".2rem",
                      }}
                    >
                      Max file size 2MB
                    </Typography>
                  )}
                </div>
              </div>

              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr",
                  gap: "1rem",
                  margin: "15px",
                }}
              >
                <Box display="flex" alignItems="center" gap={1} mt={2}>
                  <Typography variant="body1">Email Notifications</Typography>
                  <Switch
                    checked={!!userData.notify}
                    onChange={handleNotifyToggle}
                    color="primary"
                  />
                </Box>
              </div>
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr",
                  gap: "1rem",
                  margin: "15px",
                }}
              >
                <TextField
                  label="First Name"
                  variant="outlined"
                  required
                  id="userFirstName"
                  value={userData?.fname || ""}
                  onChange={(e) => {
                    setUserData((prevUserData) => ({
                      ...prevUserData,
                      fname: e.target.value,
                    }));
                    setChangeonuser(true);
                  }}
                />
                <TextField
                  label="Last Name"
                  variant="outlined"
                  required
                  id="userLastName"
                  value={userData?.lname || ""}
                  onChange={(e) => {
                    setUserData((prevUserData) => ({
                      ...prevUserData,
                      lname: e.target.value,
                    }));
                    setChangeonuser(true);
                  }}
                />
                <TextField
                  label="ID"
                  variant="outlined"
                  id="userName"
                  required
                  value={userData?.username || ""}
                  onChange={(e) => {
                    setUserData((prevUserData) => ({
                      ...prevUserData,
                      username: e.target.value,
                    }));
                    setChangeonuser(true);
                  }}
                />
                <TextField
                  label="Select Role"
                  variant="outlined"
                  required
                  disabled={userData?.email === currentUser?.email}
                  value={getRoleNameForEdit(userData.role, envData.ROLES) || ""}
                  onChange={(e) => {
                    setUserData({ ...userData, role: e.target.value });
                    setChangeonuser(true);
                  }}
                  size="small"
                  fullWidth
                  select
                >
                  {envData.ROLES && !isFetching ? (
                    envData.ROLES.map((data) => (
                      <MenuItem key={data.id} value={data.name}>
                        {data.name.charAt(0).toUpperCase() + data.name.slice(1)}
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem value="" disabled>
                      Loading roles...
                    </MenuItem>
                  )}
                </TextField>
                <TextField
                  label="Email Address"
                  variant="outlined"
                  id="userEmail"
                  required
                  value={user?.email}
                  disabled
                />
              </div>

              {userData?.role === "admin" ? (
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Box>
                    {!user?.signature && signatureFileUrl === "" && (
                      <Box
                        style={{
                          width: "150px",
                          height: "100px",
                          border: "1px solid #ccc",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Typography variant="subtitle2">
                          <center>No Signature Uploaded</center>
                        </Typography>
                      </Box>
                    )}

                    {(user?.signature || signatureFileUrl !== "") && (
                      <div style={{ textAlign: "center" }}>
                        <img
                          src={signatureFileUrl || user?.signature}
                          alt="Signature Preview"
                          style={{
                            width: "150px",
                            height: "100px",
                            border: "1px solid #ababab",
                          }}
                        />
                        {signatureFileUrl !== "" && (
                          <Typography variant="subtitle2" color="red">
                            Click SUBMIT to save Signature
                          </Typography>
                        )}
                      </div>
                    )}
                  </Box>
                </div>
              ) : (
                ""
              )}
            </div>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              size="medium"
              color="success"
              type="submit"
              disabled={!changeonuser || isFetching}
            >
              Submit
            </Button>
            <Button
              color="error"
              variant="contained"
              size="medium"
              onClick={closeModal}
            >
              Cancel
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      <AcceptRejectAlertModal
        open={openConfirm}
        setOpen={setOpenConfirm}
        handleAccept={() => {
          if (actionToConfirm === "reactivate") {
            reactivateUser();
          } else if (actionToConfirm === "deactivate") {
            deactivateUser();
          } else if (actionToConfirm === "remove") {
            handleRemoveUser();
          } else if (actionToConfirm === "update") {
            handleSubmit();
          }
          setOpenConfirm(false);
          setActionToConfirm(null);
        }}
        handleReject={() => {
          setOpenConfirm(false);
          setOpen(false);
          setActionToConfirm(null);
        }}
        desc={`Confirm Action: ${actionToConfirm}`}
        pending={false}
        hideRemark={false}
        hideQnn={true}
      />
    </>
  );
};

UserItem.propTypes = {
  user: PropTypes.object,
  fetchAllUsers: PropTypes.func,
  isModelOpen: PropTypes.bool,
};

export default UserItem;
