import { FormControl, FormHelperText, InputLabel, Select } from "@mui/material";
import PropTypes from "prop-types";

MyDropDown.propTypes = {
  children: PropTypes.node.isRequired, // Dropdown options as children
  label: PropTypes.string, // Label for the dropdown
  helperText: PropTypes.string, // Helper text displayed below the dropdown
  onChange: PropTypes.func.isRequired, // Callback function for handling change events
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired, // The current value of the dropdown
  size: PropTypes.oneOf(["small", "medium"]), // Size of the dropdown, default is "small"
  minWidth: PropTypes.number, // Minimum width of the dropdown
};

MyDropDown.defaultProps = {
  label: "",
  helperText: "",
  size: "small",
  minWidth: 120,
  onChange: () => {}, // No-op by default
};

export default function MyDropDown({
  children,
  label = "",
  helperText = "",
  onChange = (e) => {},
  value,
  size = "small",
  minWidth = 120,
  ...props
}) {
  return (
    <FormControl sx={{ minWidth: minWidth }} size={size ?? "small"}>
      <InputLabel id={label}>{label}</InputLabel>
      <Select
        labelId={label}
        id={label}
        value={value}
        label={label}
        onChange={onChange}
        {...props}
      >
        {children}
      </Select>
      <FormHelperText>{helperText}</FormHelperText>
    </FormControl>
  );
}
