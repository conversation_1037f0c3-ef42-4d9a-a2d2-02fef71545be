import { TableHead, TableRow, TableCell } from "@mui/material";

const TableHeader = ({ columns, currentMode }) => {
  const headerStyle =
    currentMode === "Dark"
      ? { backgroundColor: "#212B36", color: "white" }
      : { backgroundColor: "#E0E0E0", color: "black" };

  return (
    <TableHead>
      <TableRow>
        {columns.map((column, index) => (
          <TableCell
            key={index}
            align={column.align || "left"}
            sx={{ width: column.width, height: "56px" }} // Apply width dynamically
            style={headerStyle}
          >
            {column.label}
            {column.filterComponent && column.filterComponent}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
};

export default TableHeader;
