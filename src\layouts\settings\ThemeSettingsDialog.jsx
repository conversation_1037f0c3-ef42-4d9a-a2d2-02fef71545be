import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Tooltip,
  Typography,
} from "@mui/material";
import { BsCheck } from "react-icons/bs";
import axios from "axios";

// Image imports
import blue from "../../assets/images/blue.png";
import dark from "../../assets/images/dark.png";
import maroon from "../../assets/images/maroon.png";
import orange from "../../assets/images/orange.png";
import green from "../../assets/images/green.png";
import lightblue from "../../assets/images/lightblue.png";
import palegreen from "../../assets/images/palegreen.png";
import navyblue from "../../assets/images/navyblue.png";

// Local imports
import { useStateContext } from "../../context/ContextProvider";
import { useAuth } from "../../hooks/AuthProvider";
import { themeColors } from "../../constants/dummy";
import { sharedCss } from "../../styles/sharedCss";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toastMessageSuccess, toastMessageWarning } from "../../tools/toast";
import { theme } from "antd";

const ThemeSettingsDialog = ({
  open = true,
  onCancel = () => {},
  onSave = () => {},
}) => {
  const {
    setColor = () => {},
    setMode = () => {},
    currentMode,
    currentColor,
  } = useStateContext();

  const [colortemp, setColortemp] = useState("");
  const [currentModetemp, setCurrentModetemp] = useState("");
  const [user, setUser] = useState(null);

  const commonCss = sharedCss();
  const { currentUser } = useAuth();

  const getUserDetails = async () => {
    try {
      console.log("Fetching user details...", currentUser);
      const id = currentUser._id;
      const response = await axios.get(`${dbConfig.url}/users/${id}`);
      setUser(response.data.data);
    } catch (error) {
      console.error("Failed to fetch user details:", error);
    }
  };

  useEffect(() => {
    getUserDetails();
    setColortemp(currentColor);
    setCurrentModetemp(currentMode);
  }, [currentColor, currentMode]);

  useEffect(() => {
    const divElement = document.getElementById("themeColorsDiv");
    if (!divElement) {
      return;
    }

    divElement.style.display = currentModetemp === "Dark" ? "none" : "block";
  }, [currentModetemp]);

  const setModetemp = (e) => {
    const value = e.target.value;
    setCurrentModetemp(value);
    sessionStorage.setItem("themeMode", value);
  };

  const setTempColor = (color) => {
    setColortemp(color);
    sessionStorage.setItem("colorMode", color);
  };

  const applyColorMode = () => {
    setMode(currentModetemp);
    setColor(colortemp);
  };

  const updateUserTheme = async (color, currentModetemp) => {
    try {
      if (!user) {
        toastMessageWarning({ message: "User not found!" });
        return;
      }

      const id = currentUser._id;
      await axios.put(`${dbConfig.url}/users/${id}`, { themecolor: color, thememode: currentModetemp });
      toastMessageSuccess({ message: "Theme Updated!" });
    } catch (error) {
      console.error("Failed to update user theme:", error);
      toastMessageWarning({
        message: "Failed to update theme. Please try again.",
      });
    } finally {
      onSave();
    }
  };

  const handleUpdateTheme = async () => {
    try {
      applyColorMode();
      await updateUserTheme(colortemp, currentModetemp);
    } catch (error) {
      console.error("Error updating theme:", error);
    } finally {
      onSave();
    }
  };

  const getPreviewImage = () => {
    if (currentModetemp === "Dark") {
      return <img src={dark} alt="Dark Theme Preview" />;
    }

    // Light mode images
    switch (colortemp) {
      case "#03C9D7":
        return (
          <img src={lightblue} loading="lazy" alt="Light Blue Theme Preview" />
        );
      case "#ff9100":
        return <img src={orange} loading="lazy" alt="Orange Theme Preview" />;
      case "#1E4DB7":
        return <img src={blue} loading="lazy" alt="Blue Theme Preview" />;
      case "#B80439":
        return <img src={maroon} loading="lazy" alt="Maroon Theme Preview" />;
      case "#0CB577":
        return <img src={green} loading="lazy" alt="Green Theme Preview" />;
      case "#486772":
        return (
          <img src={palegreen} loading="lazy" alt="Pale Green Theme Preview" />
        );
      case "#474E68":
        return (
          <img src={navyblue} loading="lazy" alt="Navy Blue Theme Preview" />
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onClose={onCancel} maxWidth="xl">
      <DialogTitle>
        <Typography variant="h4" component="h4">
          Theme settings
        </Typography>
      </DialogTitle>
      <DialogContent>
        <div
          className={`flex flex-row items-start ${currentModetemp === "Dark" ? "justify-start" : "justify-between"} ${currentModetemp === "Dark" ? "" : "mr-80"}`}
        >
          <div id="themeColorsDiv" style={{ alignSelf: "flex-start" }}>
            <p className="font-semibold text-xl">Theme Colors</p>
            <div className="flex gap-3">
              {themeColors.map((item) => (
                <Tooltip key={item.name} title={item.name} placement="top">
                  <div className="relative mt-2 cursor-pointer flex gap-5 items-center">
                    <button
                      type="button"
                      aria-label={`Select ${item.name} theme color`}
                      className="h-10 w-10 rounded-full cursor-pointer"
                      style={{ backgroundColor: item.color }}
                      onClick={() => setTempColor(item.color)}
                    >
                      <BsCheck
                        className={`ml-2 text-2xl text-white ${
                          item.color === colortemp ? "block" : "hidden"
                        }`}
                      />
                    </button>
                  </div>
                </Tooltip>
              ))}
            </div>
          </div>
          <div>
            <p className="font-semibold text-xl">Theme Option</p>
            <div
              className={`mt-2 ${
                currentMode === "Light"
                  ? "hover:bg-slate-200"
                  : "hover:bg-slate-700"
              } px-1 rounded-2xl`}
            >
              <input
                type="radio"
                id="light"
                name="theme"
                value="Light"
                className="cursor-pointer"
                onChange={setModetemp}
                checked={currentModetemp === "Light"}
              />
              <label htmlFor="light" className="ml-2 text-md cursor-pointer">
                Light
              </label>
            </div>

            <div
              className={`mt-2 ${
                currentMode === "Light"
                  ? "hover:bg-slate-200"
                  : "hover:bg-slate-700"
              } px-1 rounded-2xl`}
            >
              <input
                type="radio"
                id="dark"
                name="theme"
                value="Dark"
                onChange={setModetemp}
                className="cursor-pointer"
                checked={currentModetemp === "Dark"}
              />
              <label htmlFor="dark" className="ml-2 text-md cursor-pointer">
                Dark
              </label>
            </div>
          </div>
        </div>
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: "600",
              fontSize: "1.25rem",
              lineHeight: "1.75rem",
            }}
          >
            Preview
          </Typography>
        </Box>
        <div
          style={{
            marginTop: "1rem",
          }}
          className={commonCss.headingContainer}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              height: "45vh",
              width: "45vw",
            }}
          >
            {getPreviewImage()}
          </div>
        </div>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" color="error" onClick={onCancel}>
          Cancel
        </Button>
        <Button variant="contained" onClick={handleUpdateTheme}>
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ThemeSettingsDialog;
