import React from "react";
import { Mo<PERSON>, <PERSON>, Typography, TextField, Button } from "@mui/material";

const AdvancedFiltersModal = ({
  isOpen,
  toggleModal,
  config,
  handleFilterChange,
  applyFilters,
}) => {
  return (
    <Modal open={isOpen} onClose={toggleModal}>
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "80%",
          height: "70%",
          bgcolor: "background.paper",
          boxShadow: 24,
          p: 4,
          borderRadius: "8px",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Typography variant="h5" gutterBottom>
          Advanced Filters
        </Typography>
        <Box
          sx={{
            flex: 1,
            overflowY: "auto",
            display: "flex",
            flexWrap: "wrap",
            gap: 2,
          }}
        >
          {config.map((col) => (
            <TextField
              key={col.key}
              label={`Filter by ${col.label}`}
              variant="outlined"
              size="small"
              onChange={(e) => handleFilterChange(col.key, e.target.value)}
              style={{ flex: "1 1 30%" }}
            />
          ))}
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2,
            marginTop: 2,
          }}
        >
          <Button variant="contained" color="secondary" onClick={toggleModal}>
            Cancel
          </Button>
          <Button variant="contained" color="primary" onClick={applyFilters}>
            Apply
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default AdvancedFiltersModal;
