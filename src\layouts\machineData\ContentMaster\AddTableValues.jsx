import React from "react";
import {
  Button,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  TextField,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import { useState } from "react";
import { db } from "../../../firebase";
import { companies, companyId_constant } from "../../../constants/data";
import { toastMessageSuccess, toastMessageWarning } from "../../../tools/toast";
import { useParams } from "react-router-dom";
import RemoveIcon from "@mui/icons-material/Remove";
import { Box } from "@mui/system";

const AddTableValues = ({ collectionId, type, handleClose, index }) => {
  const [acceptance, setAcceptance] = useState("");
  const [check, setCheck] = useState("");
  const [tableType, setTableType] = useState("");
  const [pType, setPType] = useState("");
  const { docId } = useParams();
  const [sensor, setSensor] = useState([]);
  const [sensorValue, setSensorValue] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    const docdata = {
      acceptance,
      sensor,
      check,
      confirm: "",
      dev: "",
      index,
      observation: "",
      pType,
      tableType,
    };
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(type)
    //   .doc(collectionId)
    //   .collection('table')
    //   .add(docdata)
    //   .then(() => {
    //     handleClose()
    //     toastMessageSuccess({ message: "Added details for documentation Successfully !" })
    //   })
  };

  const prePopData = (idx) => {
    let temp = sensor;
    temp.splice(idx, 1);
    setSensor([...temp]);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Box
        sx={{ display: "grid", gap: 1, gridTemplateColumns: "repeat(2, 1fr)" }}
      >
        <Box sx={{}}>
          <InputLabel>Check Point</InputLabel>
          <TextField
            onChange={(e) => setCheck(e.target.value)}
            style={{ marginBottom: "10px" }}
            variant="outlined"
            fullWidth
            placeholder="Enter Document Title"
          />
        </Box>

        <Box sx={{}}>
          <InputLabel>Acceptance Criteria</InputLabel>
          <TextField
            onChange={(e) => setAcceptance(e.target.value)}
            style={{ marginBottom: "10px" }}
            variant="outlined"
            fullWidth
            placeholder="Enter Document Description"
          />
        </Box>

        <Box sx={{}}>
          <InputLabel style={{ marginBottom: "10px" }}>Sensor Value</InputLabel>
          <section style={{ marginBottom: "10px" }} className="flex">
            <TextField
              onChange={(e) => setSensorValue(e.target.value)}
              value={sensorValue}
              onBlur={() => setSensorValue(sensorValue.trim())}
              variant="outlined"
              fullWidth
              required
            />
            <IconButton
              className="bg-red-700"
              onClick={() =>
                sensorValue
                  ? setSensor([...sensor, sensorValue])
                  : toastMessageWarning({ message: "Missing sensor value" })
              }
            >
              <AddIcon />
            </IconButton>
          </section>
        </Box>

        <Box sx={{ maxHeight: "15vh", overflowY: "scroll" }}>
          {sensor?.map((data, idx) => (
            <Box key={data + idx}>
              <span className="font-bold">{idx + 1}.</span> {data}{" "}
              <span>
                <IconButton onClick={() => prePopData(idx)}>
                  <RemoveIcon />
                </IconButton>
              </span>
            </Box>
          ))}
        </Box>

        <Box sx={{}}>
          <InputLabel htmlFor="type">Type</InputLabel>
          <Select
            name="type"
            value={tableType}
            placeholder="Select Type"
            onChange={(e) => setTableType(e.target.value)}
            variant="outlined"
            fullWidth
            style={{ marginBottom: "10px" }}
            required
          >
            <MenuItem value="auto">Auto</MenuItem>
            <MenuItem value="manual">Manual</MenuItem>
          </Select>
        </Box>

        <Box sx={{}}>
          <InputLabel htmlFor="pType">Process Type</InputLabel>
          <Select
            name="pType"
            value={pType}
            placeholder="Select Process Type"
            onChange={(e) => setPType(e.target.value)}
            variant="outlined"
            fullWidth
            style={{ marginBottom: "10px" }}
            required
          >
            <MenuItem value="process">Process</MenuItem>
            <MenuItem value="set_point">Set Point</MenuItem>
          </Select>
        </Box>
      </Box>

      <div className="mt-10 flex justify-between">
        <Button
          onClick={handleClose}
          variant="contained"
          color="error"
          endIcon={<CloseIcon />}
        >
          Cancel{" "}
        </Button>
        <Button type="submit" variant="contained" endIcon={<AddIcon />}>
          ADD Table Row{" "}
        </Button>
      </div>
    </form>
  );
};

export default AddTableValues;
