import { But<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ield } from "@mui/material";
import React, { useState } from "react";
import { companies, companyId_constant } from "../../../constants/data";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { toastMessage, toastMessageSuccess } from "../../../tools/toast";
import { useStateContext } from "../../../context/ContextProvider";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../../components/buttons/Buttons";
import { themeColors } from "../../../infrastructure/theme";
import { dbConfig } from "../../../infrastructure/db/db-config";
import axios from "axios";
import { useEditMachineCfr } from "../../../hooks/cfr/machineCfrProvider";
import { useAuth } from "../../../hooks/AuthProvider";
import { useContentSetter } from "../../../services3/audits/ContentContext";

const EditDoc = ({
  mid,
  id,
  handleClose,
  data,
  type,
  machineName,
  dataTitle,
  userName,
}) => {
  const [title, setTitle] = useState(data?.title);
  const [desc, setDesc] = useState(data?.desc);
  const [index, setIndex] = useState(data?.index);
  const { currentMode } = useStateContext();
  const editauditseriescontentcfr = useEditMachineCfr();
  const editsatseriescontentcfr = useEditMachineCfr();
  const handleContent = useContentSetter();
  const { currentUser } = useAuth();

  const dark = {
    background: "#212B36",
    color: "#fff",
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    let date = new Date();

    const data2 = {
      activity: "content edited",
      dateTime: date,

      description: "content is edited inside audit",
      machine: mid,
      module: "AUDIT",
      username: currentUser.username,
    };
    const data3 = {
      activity: "content edited",
      dateTime: date,

      description: "content is edited inside sat",
      machine: mid,
      module: "SAT",
      username: currentUser.username,
    };
    const numerical = parseInt(index);
    if (isNaN(numerical)) {
      toastMessage({ message: "Index must be a numerical value!" });
      return;
    }
    const dataSet = {
      title,
      createdAt: new Date(),
      lastUpdated: new Date(),
      mid: mid,
      index: numerical,
    };

    axios
      .put(`${dbConfig.url}/fatdatas/${data?._id}`, {
        ...dataSet,
      })
      .then(() => {
        if (type == "SAT") {
          editsatseriescontentcfr(data3);
        } else {
          editauditseriescontentcfr(data2);
        }
        handleContent(id);
        handleClose();
        toastMessageSuccess({ message: "Updated successfully" });
      });

    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(`${type.toLowerCase()}Data`)
    //     .doc(data.id).update(dataSet).then(() => {
    //         LoggingFunction(
    //             machineName,
    //             dataTitle,
    //             userName,
    //             type,
    //             `${dataTitle} is updated`
    //         )
    //         handleClose();
    //         toastMessageSuccess({ message: 'Updated successfully' })
    //     })
  };

  const indexSetter = (inputtxt) => {
    const anyNum = new RegExp("^[0-9]+$");
    if (inputtxt?.match(anyNum)) {
      setIndex(parseInt(inputtxt));
    } else {
      if (inputtxt === "") {
        setIndex("");
      }
    }
  };
  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Title</InputLabel>
      <TextField
        onChange={(e) => setTitle(e.target.value)}
        onBlur={() => setTitle(title?.trim())}
        value={title}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      {/* <InputLabel style={{ marginBottom: '10px' }}>Description</InputLabel>
            <TextField
                onChange={(e) => setDesc(e.target.value)}
                onBlur={() => setDesc(desc?.trim())}
                value={desc}
                required
                variant='outlined'
                fullWidth
                multiline
                style={{ marginBottom: '12px' }} /> */}
      <InputLabel style={{ marginBottom: "10px" }}>Index</InputLabel>
      <TextField
        // className= { currentMode === "Dark" ? ' text-white p-4 rounded-md border-2 border-gray-500 w-full mb-4' :
        // ' text-gray-700 p-4 rounded-md border-2 border-gray-700 w-full mb-4'}
        //style={currentMode === "Dark" ? {backgroundColor: themeColors.dark.secordary } : {}}
        onChange={(e) => indexSetter(e.target.value)}
        onBlur={() => setIndex(index)}
        value={index}
        fullWidth
        required
      />
      <div className="p-2 mt-2 flex justify-between">
        {/* <Button color="success" onClick={handleClose} variant='contained'>Cancel</Button> */}
        <ButtonBasicCancel
          buttonTitle="Cancel"
          type="button"
          onClick={handleClose}
        />
        {/* <Button color="error" type="submit" variant='contained'>Submit</Button> */}
        <ButtonBasic buttonTitle="Submit" type="submit" />
      </div>
    </form>
  );
};

export default EditDoc;
