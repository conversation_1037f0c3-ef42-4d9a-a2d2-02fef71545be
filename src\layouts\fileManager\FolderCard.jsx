import { useState } from "react";
import { Link } from "react-router-dom";
import folderImg from "../../assets/images/folder.png";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import {
  Box,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputLabel,
  Menu,
  MenuItem,
  TextField,
  Typography,
} from "@mui/material";
import RemoveCircleIcon from "@mui/icons-material/RemoveCircle";
import Delete from "../../components/Delete/Delete";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useStateContext } from "../../context/ContextProvider";
import { ButtonBasic } from "../../components/buttons/Buttons";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { sharedCss } from "../../styles/sharedCss";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import { useCheckAccess } from "../../utils/useCheckAccess";

const FolderCard = ({ grid, folder, readOnly }) => {
  const [open, setOpen] = useState(false);
  const [name, setName] = useState(folder.name); // Initialize with folder.name
  const [openRename, setOpenRename] = useState(false);
  const [openDel, setOpenDel] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null); // State for menu anchor
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  const hasFolderDELETEAccess = useCheckAccess("folders", "DELETE");
  const hasFolderPUTAccess = useCheckAccess("folders", "PUT");

  const createdAt = folder.created_at;
  const createdDate = new Date(createdAt);
  const formattedDate = createdDate.toLocaleDateString("en-US", {
    weekday: "short",
    year: "numeric",
    month: "short",
    day: "numeric",
  });

  const style = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: 400,
    bgcolor: "background.paper",
    boxShadow: 24,
    border: "none",
    borderRadius: "8px",
    p: 4,
  };

  const handleSave = async () => {
    if (name.trim() === "") {
      toastMessage({ message: "Input cannot be empty!" });
      return;
    }
    await axios
      .put(`${dbConfig.url}/folders/${folder._id}`, { ...folder, name: name })
      .then(() => {
        toastMessageSuccess({ message: "Folder renamed successfully!" });
        setOpenRename(false);
        setRefreshCount(refreshCount + 1);
      });
  };

  const handleDelete = async () => {
    await axios.delete(`${dbConfig.url}/folders/${folder._id}`).then(() => {
      toastMessage({ message: "Folder deleted successfully!" });
      setOpenDel(false);
      setRefreshCount(refreshCount + 1);
    });
  };

  // Reset name to original folder.name when closing the dialog
  const handleCloseRename = () => {
    setName(folder.name); // Reset to original name
    setOpenRename(false);
  };

  // Menu handlers
  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDeleteOption = () => {
    setOpenDel(true);
    handleMenuClose();
  };

  const commonCss = sharedCss();

  return (
    <div
      style={
        grid
          ? {
              width: "23%",
              margin: "1% 2% 1% 0%",
              borderRadius: "10px",
              overflow: "hidden",
            }
          : {
              justifyContent: "center",
              alignItems: "center",
              borderBottom:
                currentMode === "Dark"
                  ? "1px solid white"
                  : "0.05rem solid #e0e0e0",
            }
      }
    >
      <section
        className={`folderCard ${commonCss.backgroundLight2} ${
          currentMode === "Dark" ? "hover:bg-slate-800" : "hover:bg-slate-100"
        }`}
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          padding: 0,
        }}
      >
        {grid ? (
          <div
            style={{
              width: "100%",
              display: "flex",
              alignItems: "center",
              padding: "0 0 0 1rem",
            }}
            className="folderBtnContainer"
          >
            {/* Folder Name */}
            <Link
              to={{
                pathname: `/file-manager/folder/${folder?._id}`,
                state: { folder: folder },
              }}
              className={commonCss.folderLink}
              style={{
                flex: grid ? "0 1 auto" : 1,
                paddingLeft: !grid ? "1rem" : undefined,
              }}
            >
              <div
                className="folderInfoContainer"
                style={{ display: "flex", alignItems: "center" }}
              >
                <div
                  className="folderImgContainer"
                  style={{
                    width: "30px",
                    height: "30px",
                    overflow: "hidden",
                  }}
                >
                  <img
                    style={{
                      width: "30px",
                      height: "30px",
                      objectFit: "contain",
                      display: "block",
                    }}
                    src={folderImg}
                    alt="Folder icon"
                  />
                </div>
                <div
                  style={{
                    marginLeft: "0.5rem",
                    textAlign: "left",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    maxWidth: "150px",
                  }}
                  className="folderTitleContainer"
                >
                  {folder?.name}
                </div>
              </div>
            </Link>

            {/* Menu Options */}
            <div
              style={{
                flex: "0 0 auto",
                marginLeft: "auto",
                paddingRight: "0.5rem",
              }}
            >
              {!readOnly && (
                <>
                  <IconButton
                    onClick={handleMenuClick}
                    color="default"
                    variant="contained"
                    aria-label="Folder options"
                  >
                    <MoreVertIcon />
                  </IconButton>
                  <Menu
                    anchorEl={anchorEl}
                    open={Boolean(anchorEl)}
                    onClose={handleMenuClose}
                  >
                    <MenuItem
                      onClick={handleDeleteOption}
                      disabled={!hasFolderDELETEAccess}
                    >
                      <DeleteIcon
                        style={{
                          fontSize: "20px",
                          color: "#f00",
                          marginRight: "8px",
                        }}
                      />
                      Delete
                    </MenuItem>
                    <MenuItem
                      onClick={() => {
                        handleMenuClose();
                        setOpenRename(true);
                      }}
                      disabled={!hasFolderPUTAccess}
                    >
                      <EditIcon
                        style={{
                          fontSize: "20px",
                          marginRight: "8px",
                        }}
                      />
                      Edit
                    </MenuItem>
                  </Menu>
                </>
              )}
            </div>
          </div>
        ) : (
          <>
            {/* Name Column */}
            <Link
              to={{
                pathname: `/file-manager/folder/${folder?._id}`,
                state: { folder: folder },
              }}
              className={commonCss.folderLink}
              style={{
                flex: grid ? "0 1 auto" : 1,
                paddingLeft: !grid ? "1rem" : undefined,
              }}
            >
              <div
                className="folderInfoContainer"
                style={{ display: "flex", alignItems: "center" }}
              >
                <div
                  className="folderImgContainer"
                  style={{
                    width: "25px",
                    height: "25px",
                    overflow: "hidden",
                  }}
                >
                  <img
                    style={{
                      width: "25px",
                      height: "25px",
                      objectFit: "contain",
                      display: "block",
                    }}
                    src={folderImg}
                    alt="Folder icon"
                  />
                </div>
                <div
                  style={{
                    justifyContent: "flex-start",
                    marginLeft: "0.5rem",
                  }}
                  className="folderTitleContainer"
                >
                  {folder?.name}
                </div>
              </div>
            </Link>

            {/* Owner Column */}
            <div
              style={{ flex: 1.5, display: "flex", justifyContent: "center" }}
            >
              <Typography variant="body2">{folder?.creator}</Typography>
            </div>

            {/* Created At Column */}
            <div style={{ flex: 1, display: "flex", justifyContent: "center" }}>
              <Typography variant="body2">{formattedDate}</Typography>
            </div>

            {/* Actions Column */}
            <div
              style={{
                flex: 1,
                display: "flex",
                justifyContent: "flex-end",
                paddingRight: "1rem",
              }}
            >
              {!readOnly && (
                <>
                  <IconButton
                    onClick={handleMenuClick}
                    color="default"
                    variant="contained"
                    aria-label="Folder options"
                  >
                    <MoreVertIcon />
                  </IconButton>
                  <Menu
                    anchorEl={anchorEl}
                    open={Boolean(anchorEl)}
                    onClose={handleMenuClose}
                  >
                    <MenuItem
                      onClick={handleDeleteOption}
                      disabled={!hasFolderDELETEAccess}
                    >
                      <DeleteIcon
                        style={{
                          fontSize: "20px",
                          color: "#f00",
                          marginRight: "8px",
                        }}
                      />
                      Delete
                    </MenuItem>
                    <MenuItem
                      onClick={() => {
                        handleMenuClose();
                        setOpenRename(true);
                      }}
                      disabled={!hasFolderPUTAccess}
                    >
                      <EditIcon
                        style={{
                          fontSize: "20px",
                          marginRight: "8px",
                        }}
                      />
                      Edit
                    </MenuItem>
                  </Menu>
                </>
              )}
            </div>
          </>
        )}

        <Dialog open={openRename} onClose={handleCloseRename}>
          <DialogContent>
            <div className="w-full">
              <Typography
                sx={
                  currentMode === "Dark"
                    ? { color: "white" }
                    : { color: "black" }
                }
                gutterBottom
              >
                Folder Name:
              </Typography>
              <TextField
                size="small"
                type="text"
                fullWidth
                placeholder="eg. File 1"
                required
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>
          </DialogContent>
          <DialogActions>
            <Button
              size="medium"
              variant="contained"
              color="error"
              onClick={handleCloseRename}
            >
              Close
            </Button>
            <ButtonBasic
              onClick={() => handleSave()}
              style={{ marginLeft: "20px" }}
              buttonTitle="Update"
            />
          </DialogActions>
        </Dialog>
        <Dialog onClose={() => setOpenDel(false)} open={openDel}>
          <Delete onClose={() => setOpenDel(false)} onDelete={handleDelete} />
        </Dialog>
      </section>
    </div>
  );
};

export default FolderCard;
