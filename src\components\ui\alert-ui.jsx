import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>le,
  <PERSON>rid,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { dbUrl } from "../../constants/db";
import axios from "axios";
import { Close } from "@mui/icons-material";
import SecretKeyInput from "./SecretKeyInput";
import { Box } from "@mui/system";
import { toast } from "react-toastify";
import { validateQnnPattern } from "../../utils";
import PropTypes from "prop-types";

const AcceptRejectAlertModal = ({
  open = false,
  setOpen = (open) => {},
  handleAccept = (qnn, remark) => {},
  handleReject = (qnn, remark) => {},
  desc = "field",
  pending = false,
  hideRemark = false,
  hideQnn = false,
  AcceptToolTip = "",
  RejectToolTip = "",
  AcceptBtnName = "Accept",
  RejectBtnName = "Reject",
  hideRejectBtn = false,
  isQnnMandatory = true,
  isRemarksMandatory = true,
  bodyContent = null,
}) => {
  // Constants for hardcoded strings
  const QA_VERIFIED_SUCCESS_MSG = "QA Key verified successfully";
  const QA_VERIFICATION_FAILED_MSG = "QA Verification Failed";
  const REMARKS_ERROR_MSG = "Remarks should be less than 100 characters";
  const REQUIRED_FIELDS_TOOLTIP = "Please Enter all the required fields";
  const QUALITY_NOTIFICATION_LABEL = "Quality Notification Number";
  const REMARKS_LABEL = "Remarks";
  const CLOSE_TOOLTIP = "Close";

  const [secretKey, setSecretKey] = useState("");
  const [verified, setVerified] = useState(false);
  const [form, setForm] = useState({
    qnn: "",
    remark: "",
  });

  const resetForm = () => {
    setForm({
      qnn: "",
      remark: "",
    });
    setSecretKey("");
    setVerified(false);
  };

  useEffect(() => {
    resetForm();
  }, [open]);

  useEffect(() => {
    setVerified(false);
  }, [secretKey]);

  const verifyQA = async (value) => {
    if (value.length >= 8) {
      axios
        .post(`${dbUrl}/auth/qa`, {
          key: value,
        })
        .then((res) => {
          toast.success(QA_VERIFIED_SUCCESS_MSG);
          setVerified(true);
        })
        .catch((err) => {
          toast.error(
            `${(err.response && err.response.data.message) ?? QA_VERIFICATION_FAILED_MSG + " " + err.message}`,
          );
          setVerified(false);
        });
    } else {
      setVerified(false);
    }
  };

  const {
    text: qnnHelperText,
    isValid: isValidQNN,
    style: qnnStyles,
  } = validateQnnPattern(form.qnn);

  const validateForm = () => {
    if (
      !hideQnn &&
      isQnnMandatory &&
      (!isValidQNN || form.qnn.length <= 0 || form.qnn.length > 100)
    ) {
      return false;
    }
    if (
      !hideRemark &&
      isRemarksMandatory &&
      (form.remark.length <= 0 || form.remark.length > 100)
    ) {
      return false;
    }
    return true;
  };

  const isFormValid = validateForm();

  const isBtnDisabled = pending || !isFormValid || !verified;
  console.log(qnnHelperText);

  return (
    <Dialog open={open}>
      <Box sx={{ px: 4, pt: 1, pb: 2.5, width: "24rem" }}>
        <DialogTitle
          sx={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            alignItems: "flex-start",
            position: "relative",
          }}
        >
          {typeof desc === "string" ? (
            <Typography variant="h6">{desc}</Typography>
          ) : (
            desc
          )}
          <Tooltip title={CLOSE_TOOLTIP} placement="top">
            <Button
              onClick={() => setOpen(false)}
              sx={{
                position: "absolute",
                right: "-1.75rem",
                top: "5%",
                "&:hover": {
                  backgroundColor: "#dd0101 ",
                  "& *": {
                    color: "white",
                  },
                },
              }}
            >
              <Close color="error" />
            </Button>
          </Tooltip>
        </DialogTitle>
        <DialogContent>
          {bodyContent && <Box sx={{ py: 2 }}>{bodyContent}</Box>}
          {!hideQnn && (
            <Grid item xs={12} sm={12} md={12} mt={1}>
              <TextField
                size="small"
                fullWidth
                required={isQnnMandatory}
                value={form.qnn}
                onChange={(e) => {
                  setForm({
                    ...form,
                    qnn: e.target.value.toUpperCase().trimStart(),
                  });
                }}
                label={QUALITY_NOTIFICATION_LABEL}
                error={!isValidQNN}
                helperText={
                  <span style={qnnStyles}>{qnnHelperText}&nbsp;</span>
                }
              />
            </Grid>
          )}
          {!hideRemark && (
            <Grid item xs={12} sm={12} md={12} mt={1}>
              <TextField
                size="small"
                fullWidth
                required={isRemarksMandatory}
                value={form.remark}
                onChange={(e) => {
                  setForm({ ...form, remark: e.target.value.trimStart() });
                }}
                label={REMARKS_LABEL}
                error={form.remark.length > 100}
                helperText={form.remark.length > 100 ? REMARKS_ERROR_MSG : " "}
              />
            </Grid>
          )}
          <Grid item xs={12} sm={12} md={12} mt={1}>
            <SecretKeyInput
              secretKey={secretKey}
              setSecretKey={setSecretKey}
              verifyQA={verifyQA}
            />
          </Grid>
        </DialogContent>
        <DialogActions>
          <Tooltip
            title={isBtnDisabled ? REQUIRED_FIELDS_TOOLTIP : AcceptToolTip}
          >
            <Box>
              <Button
                onClick={() => {
                  handleAccept(form.qnn, form.remark);
                  setSecretKey("");
                }}
                color="success"
                disabled={isBtnDisabled}
                variant="contained"
              >
                {AcceptBtnName}
              </Button>
            </Box>
          </Tooltip>
          {!hideRejectBtn && (
            <Tooltip
              title={isBtnDisabled ? REQUIRED_FIELDS_TOOLTIP : RejectToolTip}
            >
              <Box>
                <Button
                  onClick={() => {
                    handleReject(form.qnn, form.remark);
                    setSecretKey("");
                  }}
                  color="warning"
                  disabled={isBtnDisabled}
                  variant="contained"
                >
                  {RejectBtnName}
                </Button>
              </Box>
            </Tooltip>
          )}
        </DialogActions>
      </Box>
    </Dialog>
  );
};
AcceptRejectAlertModal.propTypes = {
  open: PropTypes.bool, // Whether the dialog is open
  setOpen: PropTypes.func, // Function to set dialog state
  handleAccept: PropTypes.func, // Function to handle accept action
  handleReject: PropTypes.func, // Function to handle reject action
  desc: PropTypes.oneOfType([PropTypes.string, PropTypes.node]), // Description text or a React node
  pending: PropTypes.bool, // Whether an action is pending
  hideRemark: PropTypes.bool, // Whether to hide the remarks field
  hideQnn: PropTypes.bool, // Whether to hide the QNN field
  AcceptToolTip: PropTypes.string, // Tooltip text for the Accept button
  RejectToolTip: PropTypes.string, // Tooltip text for the Reject button
  AcceptBtnName: PropTypes.string, // Name for the Accept button
  RejectBtnName: PropTypes.string, // Name for the Reject button
  hideRejectBtn: PropTypes.bool, // Whether to hide the Reject button
  isQnnMandatory: PropTypes.bool, // Whether QNN is mandatory
  isRemarksMandatory: PropTypes.bool, // Whether remarks are mandatory
  bodyContent: PropTypes.node, // Additional content to render in the dialog
};

export default AcceptRejectAlertModal;
