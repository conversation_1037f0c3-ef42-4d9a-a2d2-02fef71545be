import React, { createContext, useEffect, useState } from "react";
import getMaintenance from "./maintenance.services";

export const MaintenanceContext = createContext([]);

const MaintenanceProvider = ({ children }) => {
  const [maintenance, setMaintenance] = useState([]);

  const handeData = (data) => {
    setMaintenance(data);
  };

  useEffect(() => {
    getMaintenance({ handleEvent: handeData });
  }, []);

  console.log(maintenance);

  return (
    <MaintenanceContext.Provider value={maintenance}>
      {children}
    </MaintenanceContext.Provider>
  );
};

export default MaintenanceProvider;
