import React from "react";
import { Box, TextField, But<PERSON> } from "@mui/material";
import PropTypes from "prop-types";

const DEFAULT_LABEL = "Search";
const DEFAULT_PLACEHOLDER = "Name, ID, Location or Equipment";
const PDF_BUTTON_LABEL = "PDF Download";

const SearchField = ({
  label = DEFAULT_LABEL,
  placeholder = DEFAULT_PLACEHOLDER,
  search,
  setSearch,
  handlePDFDownload,
  showPDFDownload = false,
}) => {
  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          my: 1,
        }}
      >
        <TextField
          label={label}
          placeholder={placeholder}
          sx={{
            maxWidth: "30rem",
          }}
          size="small"
          value={search}
          onChange={(e) => setSearch(e.target.value.trimStart())}
          fullWidth
        />
        &nbsp;
        {showPDFDownload ? (
          <Button
            variant="outlined"
            color="inherit"
            sx={{
              textTransform: "none",
            }}
            onClick={handlePDFDownload}
          >
            {PDF_BUTTON_LABEL}
          </Button>
        ) : (
          ""
        )}
      </Box>
    </Box>
  );
};

SearchField.propTypes = {
  label: PropTypes.string, // Label for the search field
  placeholder: PropTypes.string, // Placeholder text for the search input
  search: PropTypes.string.isRequired, // Current search value
  setSearch: PropTypes.func.isRequired, // Function to update the search value
  handlePDFDownload: PropTypes.func, // Function to handle PDF download
  showPDFDownload: PropTypes.bool, // Determines if the PDF download button should be displayed
};

SearchField.defaultProps = {
  label: DEFAULT_LABEL, // Default label
  placeholder: DEFAULT_PLACEHOLDER, // Default placeholder text
  handlePDFDownload: () => {}, // Default is a no-op function
  showPDFDownload: false, // Default is to not show the PDF download button
};

export default SearchField;
