import React from "react";
import {
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Box,
} from "@mui/material";

const ForecastConfidenceScore = ({ values, forecastValues }) => {
  const calculateConfidenceScore = (values, forecastValues) => {
    if (!values || !forecastValues || values.length < 5) return 0;

    const validForecasts = forecastValues.filter((val) => val !== null);
    const confidence = validForecasts.length / forecastValues.length;

    return Math.min(100, (confidence * 100).toFixed(2));
  };

  const confidenceScore = calculateConfidenceScore(values, forecastValues);

  const getColor = () => {
    if (confidenceScore > 70) return "success";
    if (confidenceScore > 40) return "warning";
    return "error";
  };

  return (
    <Card sx={{ p: 1.5, height: "150px", textAlign: "center", mt: 4 }}>
      <CardContent sx={{ p: 0 }}>
        <Typography variant="body1" align="center" sx={{ fontWeight: "bold" }}>
          Forecast Confidence
        </Typography>
        <Box display="flex" flexDirection="column" alignItems="center" mt={1}>
          <Typography variant="h6" sx={{ fontWeight: "bold", mb: 0.5 }}>
            {confidenceScore}%
          </Typography>
          <CircularProgress
            variant="determinate"
            value={confidenceScore}
            color={getColor()}
            size={50}
            thickness={4}
          />
        </Box>
      </CardContent>
    </Card>
  );
};

export default ForecastConfidenceScore;
