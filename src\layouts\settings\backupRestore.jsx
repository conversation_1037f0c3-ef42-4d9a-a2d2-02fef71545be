import {
  Avatar,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Tooltip,
} from "@mui/material";
import axios from "axios";
import React, { useEffect, useState } from "react";
import { dbConfig } from "../../infrastructure/db/db-config";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { FlakyOutlined, Restore } from "@mui/icons-material";
import moment from "moment";
import { FolderIcon } from "lucide-react";
import { toast } from "react-toastify";

async function fetchBackupList() {
  try {
    const response = await axios.get(`${dbConfig.url}/db-backup/list`);
    return response.data;
  } catch (error) {
    console.error("Error fetching backup list:", error);
    toast.error("Error fetching backup list");
    return null;
  }
}

async function createBackup() {
  try {
    const response = await axios.post(`${dbConfig.url}/db-backup`);
    toast.success("Backup created successfully");
    return response.data;
  } catch (error) {
    toast.error("Error creating backup");
    return null;
  }
}

async function restoreBackup(backupName) {
  try {
    const response = await axios.post(`${dbConfig.url}/db-backup/restore`, {
      backupFolder: backupName,
    });
    toast.success("Backup restored successfully");
    return response.data;
  } catch (error) {
    toast.error("Error restoring backup");
    return null;
  }
}

export function BackupRestore() {
  const [open, setOpen] = useState(false);
  const [backupList, setBackupList] = useState([]);
  const [restoreInProgress, setRestoreInProgress] = useState(false);

  const fetchData = async () => {
    const data = await fetchBackupList();
    if (data) {
      setBackupList(data);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <ButtonBasic
        variant={"contained"}
        onClick={() => setOpen(!open)}
        buttonTitle={"Backup & Restore"}
      />
      <Dialog
        open={open}
        onClose={() => setOpen(!open)}
        fullWidth
        maxWidth="lg"
      >
        <DialogTitle>
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <span>Backup & Restore</span>
            <Button
              onClick={() => createBackup().then(() => fetchData())}
              color="primary"
            >
              Backup
            </Button>
          </div>
        </DialogTitle>
        <DialogContent sx={{ minHeight: "50vh" }}>
          <Box className="userListTableContainer">
            <Grid item xs={12} md={4}>
              <List>
                {backupList.length > 0 &&
                  backupList.map((l, i) => {
                    return (
                      <Item
                        key={i}
                        data={l}
                        index={i}
                        restoreInProgress={restoreInProgress}
                        setRestoreInProgress={setRestoreInProgress}
                      />
                    );
                  })}
              </List>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              // Handle backup logic here
              setOpen(false);
            }}
          >
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

const Item = ({ data, index, restoreInProgress, setRestoreInProgress }) => {
  return (
    <ListItem
      secondaryAction={
        <Box sx={{ display: "flex", gap: "1rem" }}>
          <IconButton
            edge="end"
            disabled={restoreInProgress}
            onClick={async () => {
              setRestoreInProgress(true);
              await restoreBackup(data);
              setRestoreInProgress(false);
            }}
          >
            <Tooltip>
              {restoreInProgress ? <CircularProgress /> : <Restore />}
            </Tooltip>
          </IconButton>
          {/* <IconButton edge="end" aria-label={STRINGS.CHECK} disabled={isLoading} onClick={() => handleCheckRestoredFile(data)}>
                    <Tooltip title={STRINGS.CHECK}>
                      <FlakyOutlined color='success' />
                    </Tooltip>
                  </IconButton> */}
        </Box>
      }
    >
      {" "}
      {index + 1}.
      <ListItemAvatar>
        <Avatar>
          <Tooltip
          // title={moment(parseInt(data?.split('c')[0])).startOf('day').fromNow().includes('hours') ? `Backup taken today at ${moment(parseInt(data?.split('c')[0])).format('h:m:s')}` : ""}
          >
            <FolderIcon
            // color={moment(parseInt(data?.split('c')[0])).startOf('day').fromNow().includes('hours') ? 'success' : ""}
            />
          </Tooltip>
        </Avatar>
      </ListItemAvatar>
      <ListItemText
        primary={moment(parseInt(data?.split("c")[0])).format("M-D-Y:h:m:s")}
        secondary={data}
      />
    </ListItem>
  );
};
