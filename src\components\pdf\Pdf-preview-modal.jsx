import React from "react";
import {
  Page,
  Text,
  Document,
  StyleSheet,
  View,
  PDFDownloadLink,
  PDFViewer,
} from "@react-pdf/renderer";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from "@mui/material";
import PropTypes from "prop-types";
import { formatDateTime } from "../Utils/timeUtils";

// Constants
const FOOTER_TEXT =
  "This document is computer generated and does not require any signature or stamp for validation";
const PRINTED_BY_LABEL = "Printed By: ";
const DATE_TIME_LABEL = "Date & Time: ";
const DEFAULT_FILE_NAME_PREFIX = "pdf-";
const DEFAULT_MODEL_TITLE = "Instrument List";
const LOADING_TEXT = "Loading...";
const DOWNLOAD_TEXT = "Download";
const CLOSE_BUTTON_TEXT = "Close";

const styles = StyleSheet.create({
  page: {
    padding: 20,
    flexDirection: "column",
    justifyContent: "space-between",
  },
  content: {
    marginBottom: 20,
  },
  footer: {
    fontSize: 10,
    textAlign: "center",
    marginTop: "auto",
  },
});

const MyPDFDocument = ({ children, currentUser }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      <View style={styles.content}>{children}</View>
      <Text style={styles.footer}>{FOOTER_TEXT}</Text>
      <Text style={styles.footer}>
        {PRINTED_BY_LABEL}
        {currentUser.email}
        {"\n"}
        {DATE_TIME_LABEL}
        {new Date().toLocaleString()}
      </Text>
    </Page>
  </Document>
);

const MyPDFPreview = ({
  openModal = false,
  setOpenModal,
  children,
  fileName = `${DEFAULT_FILE_NAME_PREFIX}${formatDateTime(new Date())}.pdf`,
  modelTitle = DEFAULT_MODEL_TITLE,
}) => {
  const currentUser = JSON.parse(sessionStorage.getItem("@user-creds"));

  return (
    <Dialog open={openModal} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Typography fontWeight={600} variant="h5">
          {modelTitle}
        </Typography>
      </DialogTitle>
      <DialogContent style={{ padding: "2rem" }} fullWidth>
        <PDFViewer width="100%" height="720px">
          <MyPDFDocument currentUser={currentUser}>{children}</MyPDFDocument>
        </PDFViewer>
      </DialogContent>
      <DialogActions>
        <Button
          type="submit"
          variant="contained"
          color="success"
          sx={{ margin: ".5rem 1rem " }}
        >
          <PDFDownloadLink
            document={
              <MyPDFDocument currentUser={currentUser}>
                {children}
              </MyPDFDocument>
            }
            fileName={fileName}
          >
            {({ blob, url, loading, error }) =>
              loading ? LOADING_TEXT : DOWNLOAD_TEXT
            }
          </PDFDownloadLink>
        </Button>
        <Button
          variant="contained"
          color="error"
          onClick={() => setOpenModal(false)}
        >
          {CLOSE_BUTTON_TEXT}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

MyPDFPreview.propTypes = {
  openModal: PropTypes.bool,
  setOpenModal: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  fileName: PropTypes.string,
  modelTitle: PropTypes.string,
};

MyPDFPreview.defaultProps = {
  openModal: false,
  fileName: `pdf-${formatDateTime(new Date())}.pdf`,
  modelTitle: "Instrument List",
};

MyPDFDocument.propTypes = {
  children: PropTypes.node.isRequired,
  currentUser: PropTypes.shape({
    email: PropTypes.string.isRequired,
  }).isRequired,
};

export default MyPDFPreview;
