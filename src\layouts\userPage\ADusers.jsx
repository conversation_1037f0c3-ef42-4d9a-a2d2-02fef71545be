import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import React from "react";
import axios from "axios";
import { useEffect, useState } from "react";
// import { useMongoRefresh } from "@/services/mongo-refresh.context";
// import { dbUrl } from "../../constants/db";
import { makeStyles } from "@mui/styles";
const useStyles = makeStyles((theme) => ({
  container: {
    maxHeight: "84vh",
  },
  tableHeaderCell: {
    // backgroundColor: "lightgrey !important",
    // color: "black  !important",
    fontWeight: "bold !important",
    fontSize: "0.9rem !important",
    minWidth: "6rem !important",
  },
  tableCell: {},
}));
function ADusers() {
  //here is the code for getting the data
  // axios.get(`${dbUrl}/users/getaduser`).then((res)=>{
  //   setUsers(res?.data);
  // }).catch((err)=>{
  //
  // })

  const [users, setUsers] = useState([]);
  const classes = useStyles();
  useEffect(() => {
    axios
      .get(`/users/getaduser`)
      .then((res) => {
        setUsers(res?.data);
      })
      .catch((err) => {
        //
      });
  }, []);

  //code ends

  return (
    <TableContainer component={Paper} className={classes.container}>
      <Table stickyHeader aria-label="responsive table">
        <TableHead>
          <TableRow>
            <TableCell className={classes.tableHeaderCell} align="left">
              Username
            </TableCell>
            <TableCell className={classes.tableHeaderCell} align="left">
              Full name
            </TableCell>
            {/* <TableCell align="left">Active Status</TableCell> */}
            <TableCell className={classes.tableHeaderCell} align="left">
              Email
            </TableCell>
            <TableCell className={classes.tableHeaderCell} align="left">
              Created At teteytee
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {users?.map((user, index) => (
            <TableRow
              key={index + "add_users" + user.username}
              className={classes.tableRow}
            >
              <TableCell align="left">{user?.username}</TableCell>
              <TableCell align="left">{user?.cn}</TableCell>
              <TableCell align="left">{user?.userPrincipalName}</TableCell>
              <TableCell align="left">{`${user?.whenCreated?.substring(6, 8)}/${user?.whenCreated?.substring(4, 6)}/${user?.whenCreated?.substring(0, 4)}`}</TableCell>
              {/* user?.whenCreated */}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}

export default ADusers;
