const FolderPreviewContainer = () => {
  return (
    <div className="folderPreviewContainer">
      <div className="infoHeaderContainer">
        <div className="title">
          <div className="icon">
            <i className="ri-file-text-line"></i>
          </div>
          <h4>File Preview</h4>
        </div>
      </div>

      <hr />

      <div className="filePreviewInfoContainer">
        <div className="fileIcon">
          <img
            src="https://cdn.iconscout.com/icon/free/png-256/folder-2631188-2177200.png"
            alt=""
          />
        </div>
        <div className="fileTitle">Licence agreement on inc of waterfall</div>
        <div className="fileDesc">24.5Mb , 12:30pm </div>
      </div>

      <hr />

      <div className="fileDescContainer">
        <div className="fileDescTitle">Folder Description</div>
        <div className="fileDesc">
          Lorem, ipsum dolor sit amet consectetur adipisicing elit. emporibus
          vel adipisicing.
        </div>
      </div>

      <hr />

      <div className="fileBtnContainer">
        <div className="btns">
          <div className="editBtn btn">
            <i className="ri-pencil-line"></i>
          </div>
          <div className="deleteBtn btn">
            <i className="ri-delete-bin-6-line"></i>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FolderPreviewContainer;
