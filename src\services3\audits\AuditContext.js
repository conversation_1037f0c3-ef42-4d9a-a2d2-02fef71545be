import axios from "axios";
import React, { createContext, useEffect, useState, useContext } from "react";
import { dbConfig } from "../../infrastructure/db/db-config";

export const AuditGetterContext = createContext([]);
export const AuditSetterContext = createContext();
export const EditAuditCountContext = createContext();
export const EditCountContext = createContext();

export function useAuditGetter() {
  return useContext(AuditGetterContext);
}
export function useAuditSetter() {
  return useContext(AuditSetterContext);
}
export function useAuditEditCountSetter() {
  return useContext(EditAuditCountContext);
}
export function useAuditEditCount() {
  return useContext(EditCountContext);
}

const AuditProvider = ({ children }) => {
  const [series, setSeries] = useState([]);
  const [auditEditCount, setAuditEditCount] = useState(0);

  const handleAudit = async (type) => {
    await axios
      .get(
        `${dbConfig.url}/${
          type === "AUDIT" ? "FAT"?.toLowerCase() : type?.toLowerCase()
        }lists`,
      ) // FAT changed to AUDIT
      .then((data) => {
        setSeries(data?.data);
        console.log("serilized.jsx: data:", data?.data);
      })
      .catch((e) => {
        console.log("error Serilized.jsx : fatlists not found error", e);
      });
  };
  const handleEditCount = () => {
    console.log("handlerun");
    setAuditEditCount(auditEditCount + 1);
  };
  return (
    <AuditGetterContext.Provider value={series}>
      <AuditSetterContext.Provider value={handleAudit}>
        <EditAuditCountContext.Provider value={handleEditCount}>
          <EditCountContext.Provider value={auditEditCount}>
            {children}
          </EditCountContext.Provider>
        </EditAuditCountContext.Provider>
      </AuditSetterContext.Provider>
    </AuditGetterContext.Provider>
  );
};
export default AuditProvider;
