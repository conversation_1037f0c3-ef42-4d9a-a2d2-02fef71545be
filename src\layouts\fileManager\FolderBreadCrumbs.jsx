import React from "react";
import { Breadcrumbs, <PERSON> } from "@mui/material";
import { ROOT_FOLDER } from "../../hooks/useFolder";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import { NavLink } from "react-router-dom";

export default function FolderBreadCrumbs({ currentFolder }) {
  let path = currentFolder === ROOT_FOLDER ? [] : [ROOT_FOLDER];
  if (currentFolder) path = [...path, ...currentFolder.path];
  return (
    <div role="presentation">
      <Breadcrumbs
        aria-label="breadcrumb"
        separator={<NavigateNextIcon fontSize="small" />}
      >
        {path.map((folder) => (
          <NavLink
            underline="hover"
            color="inherit"
            to={
              folder.id ? `/file-manager/folder/${folder.id}` : "/file-manager"
            }
          >
            {folder.name}
          </NavLink>
        ))}
        {currentFolder && (
          <Link
            underline="none"
            style={{
              textTransform: "capitalize",
              fontWeight: "500",
            }}
          >
            {currentFolder.name}
          </Link>
        )}
      </Breadcrumbs>
    </div>
  );
}
