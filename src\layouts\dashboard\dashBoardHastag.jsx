import {
  Autocomplete,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField,
  Box,
  Typography,
  Divider,
  CircularProgress,
  Tooltip,
  Paper,
  cardActionAreaClasses,
  Card,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import SearchIcon from "@mui/icons-material/Search";
import { makeStyles } from "@mui/styles";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMaintenanceInfoSeter } from "../../context/MaintenanceContext";
import { useNavigate } from "react-router-dom";
import { useStateContext } from "../../context/ContextProvider";

const useStyles = makeStyles((theme) => ({
  hashtags: {
    position: "fixed",
    right: "2.5rem",
    bottom: "2.5rem",
    zIndex: 1200,
  },
  hashtagButton: {
    minWidth: "56px",
    width: "56px",
    height: "56px",
    borderRadius: "50%",
    fontSize: "4.5rem",
    boxShadow: "0 6px 24px rgba(0,0,0,0.25)",
    background: theme.palette.custom.backgroundForth,
    color: "#fff",
    transition: "background 0.2s, width 0.2s, height 0.2s, min-width 0.2s, padding 0.2s, transform 0.2s",
    padding: 8,
    "&:hover": {
      background: theme.palette.primary.dark,
      minWidth: "70px",
      width: "70px",
      height: "70px",
      padding: 13,
      transform: "scale(1.12)", // 80/56 ≈ 1.43
    },
  },
  dialogPaper: {
    width: "100%",
    maxWidth: 400,
    borderRadius: 16,
    background: theme.palette.background.paper,
    boxShadow: "0 8px 32px rgba(0,0,0,0.18)",
    margin: 0,
  },
  dialogTitle: {
    padding: "1rem 1.5rem 0.75rem 1.5rem",
    background: theme.palette.background.default,
    borderBottom: "1px solid #ececec",
  },
  dialogContent: {
    margin: 0,
    padding: "1.25rem 1.5rem 1rem 1.5rem",
    background: theme.palette.background.paper,
    minHeight: 300,
    maxHeight: 480,
    overflow: "hidden",
    display: "flex",
    flexDirection: "column",
    gap: "1rem",
  },
  searchSection: {
    display: "flex",
    alignItems: "center",
    gap: "0.75rem",
    background: "#f7f7fa",
    borderRadius: 10,
    padding: "0.6rem 1rem",
    marginBottom: "0.5rem",
  },
  searchInput: {
    flex: 1,
  },
  hashtagListSection: {
    flex: 1,
    overflowY: "auto",
    marginTop: "0.5rem",
    marginBottom: "0.5rem",
    minHeight: 160,
    maxHeight: 320,
    paddingRight: 4,
  },
  errorText: {
    color: theme.palette.error.main,
    marginBottom: "0.5rem",
    fontSize: "0.95rem",
    fontWeight: 500,
  },
  dialogActions: {
    padding: "1rem 1.5rem",
    borderTop: "1px solid #ececec",
    background: theme.palette.background.default,
    justifyContent: "flex-end",
  },
}));

function DashBoardHastag({ currentMode }) {
  const [hashes, setHashes] = useState([]);
  const [hashArray, setHashArray] = useState([]);
  const [findBy, setFindBy] = useState("");
  const [inputFindBy, setInputFindBy] = useState("");
  const [tempHash, setTempHash] = useState([]);
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const classes = useStyles();

  const handleHashtagData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const apiUrl = `${dbConfig.url}/hashtags`;
      const headers = { "Content-Type": "application/json" };
      const [hashtagResponse, maintenanceResponse] = await Promise.all([
        axios.get(`${dbConfig.url}/hashtags`, { headers }),
        axios.get(`${dbConfig.url}/maintenance`, { headers }),
      ]);
      const hashtagData = hashtagResponse?.data?.data || [];
      const maintenanceData = maintenanceResponse?.data?.data || [];
      const enrichedData = hashtagData.map((hashtag) => {
        const maintenance = maintenanceData.find(
          (m) => m._id === hashtag.maintenance_id,
        );
        return {
          ...hashtag,
          visit: maintenance?.visit ?? 0,
          manualTitle: maintenance?.title || "",
        };
      });
      setHashes(enrichedData);
      setTempHash(enrichedData);
    } catch (error) {
      setError("Error fetching hashtag data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const uniqueHashtags = [
      ...new Set(hashes.map((option) => option.title)),
    ].filter(Boolean);
    setHashArray(uniqueHashtags);
  }, [hashes]);

  const handleClickOpen = () => {
    setOpen(true);
    handleHashtagData();
  };

  const handleClose = () => setOpen(false);

  const handleTagSubmit = (e) => {
    e.preventDefault();
  };

  const handleTagOnChange = (target) => {
    setInputFindBy(target);
    setFindBy(target);
    const filtered = hashes.filter((option) =>
      option.title?.toLowerCase().includes(target.toLowerCase()),
    );
    setTempHash(target ? filtered : hashes);
  };

  return (
    <div className={classes.hashtags}>
      <Tooltip title="Search Hashtags" placement="left">
        <Button
          className={classes.hashtagButton}
          onClick={handleClickOpen}
          disableRipple
          disableElevation
          style={{
            minWidth: 56,
            width: 56,
            height: 56,
            padding: 8,
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            boxShadow: "0 6px 24px rgba(0,0,0,0.25)",
            transition: "background 0.2s, width 0.2s, height 0.2s, min-width 0.2s, padding 0.2s, transform 0.2s",
            background: "primary.main",
          }}
          sx={{
            bgcolor: "primary.main",
            "&:hover": {
              bgcolor: "primary.dark",
              "& span": {
                color: "#fff",
              },
            },
          }}
        >
          <span
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "50%",
              height: "50%",
              fontSize: "2.5rem",
              lineHeight: 1,
              fontWeight: 700,
              textAlign: "center",
              borderRadius: "50%",
              background: "transparent",
              margin: 0,
              padding: 0,
              color: "#fff",
            }}
          >
            #
          </span>
        </Button>
      </Tooltip>
      <Dialog
        open={open}
        onClose={handleClose}
        PaperProps={{ className: classes.dialogPaper }}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle className={classes.dialogTitle}>
          <Typography variant="subtitle1" fontWeight={700} fontSize="1rem">
            Quick Hashtag Search
          </Typography>
        </DialogTitle>
        <DialogContent className={classes.dialogContent}>
          {error && (
            <Typography className={classes.errorText}>{error}</Typography>
          )}
          <Box className={classes.searchSection}>
            <Autocomplete
              size="small"
              inputValue={inputFindBy}
              value={findBy}
              onInputChange={(event, newInputValue) =>
                handleTagOnChange(newInputValue)
              }
              id="hashtag-search"
              options={hashArray}
              className={classes.searchInput}
              loading={isLoading}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Search hashtags"
                  placeholder="Type to search..."
                  variant="outlined"
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {isLoading ? (
                          <CircularProgress color="inherit" size={18} />
                        ) : null}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                />
              )}
            />
            <IconButton
              type="submit"
              color="primary"
              sx={{
                backgroundColor: (theme) => theme.palette.primary.main,
                color: "white",
                "&:hover": {
                  backgroundColor: (theme) => theme.palette.primary.dark,
                },
              }}
              onClick={handleTagSubmit}
            >
              <SearchIcon />
            </IconButton>
          </Box>
          <Divider />
          <Box className={classes.hashtagListSection}>
            <DialogBodyHastags tempHash={tempHash} isLoading={isLoading} />
          </Box>
        </DialogContent>
        <DialogActions className={classes.dialogActions}>
          <Button
            onClick={handleClose}
            variant="contained"
            color="error"
            sx={{ minWidth: "80px", fontSize: "0.9rem", py: 0.5 }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

DashBoardHastag.propTypes = {
  currentMode: PropTypes.string.isRequired,
};

export default DashBoardHastag;

export function DialogBodyHastags({ tempHash }) {
  const { currentMode, currentColorLight } = useStateContext();
  const navigate = useNavigate();
  const maintenanceInfoSetter = useMaintenanceInfoSeter();

  const handleGoTo = async (hash, target) => {
    await axios
      .get(`${dbConfig?.url}/maintenance/${target}`)
      .then((response) => {
        return response?.data.data;
      })
      .then((data) => {
        maintenanceInfoSetter(hash);
        console.log("maintenanceinfosetter payload from hastag:", hash);
        navigate(`maintenance/${data?.mid}`);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  return (
    <Box
      sx={{
        background: currentMode === "Dark" ? "#181F2A" : "#f9f9fb",
        height: "100%",
        minHeight: 160,
        maxHeight: 320,
        overflowY: "auto",
        px: 0,
        borderRadius: 2,
      }}
    >
      <Box sx={{ px: 1, py: 1 }}>
        {tempHash.length === 0 ? (
          <Typography
            align="center"
            sx={{ mt: 2, color: "text.secondary", fontSize: "0.98rem" }}
          >
            No hashtags found.
          </Typography>
        ) : (
          tempHash.map((options, index) =>
            options.title !== "" ? (
              <Card
                sx={{
                  mb: 1.5,
                  borderRadius: "10px",
                  boxShadow: 1,
                  transition: "box-shadow 0.2s, border 0.2s",
                  "&:hover": { boxShadow: 4, border: "1px solid #1976d2" },
                  background: currentMode === "Dark" ? "#232B3B" : "#fff",
                  cursor: "pointer",
                  px: 1.5,
                  py: 1.2,
                  display: "flex",
                  flexDirection: "column",
                  gap: 0.2,
                }}
                key={options.maintenance_id + "" + index}
                onClick={() => handleGoTo(options, options.maintenance_id)}
              >
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 700,
                    fontSize: "1.02rem",
                    color: currentMode === "Dark" ? "#fff" : "#222",
                    mb: 0.2,
                  }}
                >
                  {options.title}
                </Typography>
                <Typography
                  sx={{
                    fontWeight: 500,
                    fontSize: "0.95rem",
                    color: currentMode === "Dark" ? "#b0b8c1" : "#555",
                  }}
                >
                  {options.manualTitle}
                </Typography>
              </Card>
            ) : null,
          )
        )}
      </Box>
    </Box>
  );
}
