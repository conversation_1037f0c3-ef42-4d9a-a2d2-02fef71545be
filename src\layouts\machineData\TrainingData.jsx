import React, { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  CircularProgress,
  Typography,
  Button,
  TextField,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import AddTraining from "./AddTraining";
import TrainingItem from "./TrainingItem";
import MachineDataHeader from "./MachineDataHeader";
import { useRole } from "../../context/RoleContext";
import { useAuth } from "../../hooks/AuthProvider";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { sharedCss } from "../../styles/sharedCss";
import TableHeader from "./TableHeader";
import NoDataComponent from "../../components/commons/noData.component";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";
import axios from "axios";
import { useStateContext } from "../../context/ContextProvider";

const TrainingData = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { mid } = useParams();
  const [details, setDetails] = useState([]);
  const [machineName, setMachineName] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [role, setRole] = useState("");
  const { currentUser } = useAuth();
  const { currentColor, currentMode } = useStateContext();
  const [dataLoading, setDataLoading] = useState(true);
  const { refreshCount } = useMongoRefresh();
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  const hasTrainingPOSTAccess = useCheckAccess("training", "POST");
  const hasTrainingGETAccess = useCheckAccess("training", "GET");

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const getAllTraining = async () => {
    try {
      const response = await axios.get(
        `${dbConfig.url}/training/getFromMachine/${mid}`
      );
      const trainingData = response?.data?.data || [];
      setDetails(trainingData);
    } catch (error) {
      console.error("ERROR IN TRAINING:", error.message);
      setDetails([]);
    } finally {
      setDataLoading(false);
    }
  };

  const fetchCurrentMachine = async () => {
    await axios.get(`${dbConfig.url}/machines/${mid}`).then((response) => {
      setMachineName(response.data?.data?.title);
    });
  };

  const commonCss = sharedCss();

  useEffect(() => {
    getAllTraining();
    fetchCurrentMachine();
  }, [refreshCount]);

  const handleOnChangeSearchTerm = (e) => {
    setSearchTerm(e.target.value);
    setPage(0); // Reset page
  };

  const filteredDetails = useMemo(() => {
    return details.filter((data) => {
      const matchesMid = data.mid === mid;
      const matchesSearch = data.title
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase());
      return matchesMid && matchesSearch;
    });
  }, [details, mid, searchTerm]);

  const lastFilteredDetailsItem = filteredDetails.length;

  const paginatedDetails = useMemo(() => {
    return filteredDetails.slice(
      page * rowsPerPage,
      page * rowsPerPage + rowsPerPage
    );
  }, [filteredDetails, page, rowsPerPage]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <section>
      <MachineDataHeader />

      <div>
        <div
          className={`${commonCss.sectionContainer} ${commonCss.backgroundLight} border-radius-outer`}
        >
          <div className={`${commonCss.tableLable}`}>
            <Typography sx={{ ml: 2 }} fontWeight="bold" variant="h6">
              Training
            </Typography>
            <div
              className={commonCss.tableRightContent}
              style={{ display: "flex", alignItems: "center", gap: "1rem" }}
            >
              <div>
                <TextField
                  label="Search"
                  placeholder="Search by title"
                  value={searchTerm}
                  onChange={handleOnChangeSearchTerm}
                  size="small"
                  className={`${commonCss.searchBox} ${commonCss.inputAlignmentFix}`}
                  InputProps={{
                    startAdornment: (
                      <IconButton>
                        <SearchIcon />
                      </IconButton>
                    ),
                  }}
                />
              </div>

              <div>
                {role !== "trainee" && (
                  <Button
                    variant="contained"
                    onClick={() => setIsOpen(true)}
                    disabled={!hasTrainingPOSTAccess}
                  >
                    Add Training Manual
                  </Button>
                )}
              </div>
            </div>
          </div>

          {hasTrainingGETAccess ? (
            <div className="liveDataContainer">
              <TableContainer
                className="table border-radius-inner"
                component={Paper}
                sx={commonOuterContainerStyle}
              >
                <Table sx={{ minWidth: 650 }}>
                  <TableHeader
                    currentMode={currentMode}
                    columns={[
                      { label: "Title", align: "left" },
                      { label: "Description", align: "left" },
                      { label: "Actions", align: "center" },
                    ]}
                  />
                  <TableBody>
                    {dataLoading ? (
                      <TableRow>
                        <TableCell colSpan={3} align="center">
                          <CircularProgress />
                        </TableCell>
                      </TableRow>
                    ) : paginatedDetails.length > 0 ? (
                      paginatedDetails.map((data, index) => (
                        <TrainingItem
                          role={role}
                          data={data}
                          key={data._id + index}
                          mid={mid}
                          machineName={machineName}
                          lastItem={
                            lastFilteredDetailsItem - 1 ===
                            page * rowsPerPage + index
                          }
                        />
                      ))
                    ) : (
                      <NoDataComponent />
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              {lastFilteredDetailsItem > 0 && (
                <TablePagination
                  component="div"
                  count={lastFilteredDetailsItem}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  className={commonCss.tablePagination}
                />
              )}
            </div>
          ) : (
            <NotAccessible />
          )}
        </div>
      </div>

      <Dialog open={isOpen} fullWidth>
        <DialogTitle
          style={
            currentMode === "Dark"
              ? { backgroundColor: "#212B36", color: "white" }
              : {}
          }
        >
          Add Training Module
        </DialogTitle>
        <DialogContent
          style={
            currentMode === "Dark"
              ? { backgroundColor: "#212B36", color: "white" }
              : {}
          }
        >
          <AddTraining
            mid={mid}
            machineName={machineName}
            handleClose={() => setIsOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default TrainingData;
