import React, { useState, useRef, useEffect } from "react";
import { Suspense } from "react";
import { Link } from "react-router-dom";
import * as THREE from "three";
import { Canvas, useFrame, useThree } from "@react-three/fiber";
import { OrbitControls, Text, Box, Html, useGLTF } from "@react-three/drei";
import { Vector3, Quaternion } from "three";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import "./ModelPage.css";
import Modal from "./Modal";
import NotAccessible from "../not-accessible/not-accessible";
import { sharedCss } from "../../styles/sharedCss";
import {
  Button,
  Card,
  ListItem,
  Paper,
  FormControlLabel,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  List,
  CardContent,
  Typography,
} from "@mui/material";
import { useStateContext } from "../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { useCheckAccess } from "../../utils/useCheckAccess";

const Annotation = React.memo(
  ({ position, text, rotation, normal, color, value }) => {
    const textRef = useRef();
    const { camera } = useThree();

    useFrame(() => {
      if (textRef.current) {
        textRef.current.quaternion.copy(camera.quaternion);
        const normalVector = new Vector3(normal.x, normal.y, normal.z);
        const textPosition = new Vector3(
          position.x,
          position.y,
          position.z,
        ).add(normalVector.clone().multiplyScalar(0.3));
        textRef.current.position.copy(textPosition);
      }
    });

    return (
      <Text
        rotation={rotation}
        position={position}
        fontSize={0.25}
        anchorX="center"
        anchorY="middle"
        color={color}
        ref={textRef}
      >
        {`${text + " : " + value}`}
      </Text>
    );
  },
);

const Marker = React.memo(
  ({ position, normal, onPointerOver, onPointerOut }) => {
    const normalVector = new Vector3(normal.x, normal.y, normal.z);
    const hoverPosition = new Vector3(
      position?.x,
      position?.y,
      position?.z,
    )?.add(normalVector.clone().multiplyScalar(0.03));
    const quaternion = new Quaternion()?.setFromUnitVectors(
      new Vector3(0, 0, 1),
      normal,
    );

    return (
      <mesh
        position={hoverPosition}
        quaternion={quaternion}
        onPointerOver={onPointerOver}
        onPointerOut={onPointerOut}
      >
        <circleBufferGeometry args={[0.1, 32]} />
        <meshBasicMaterial color="red" side={THREE.DoubleSide} />
      </mesh>
    );
  },
);

function Model({ url, handleModelDoubleClick }) {
  const gltf = useGLTF(url, true);
  // setModelData(gltf);
  const boundingBox = new THREE.Box3().setFromObject(gltf.scene);
  const size = boundingBox.getSize(new THREE.Vector3());
  const xOffset = size.x * -0.3;
  const yOffset = size.y * -0.5;
  const zOffset = size.z * -0.2;

  return (
    <group
      position={[xOffset, yOffset, zOffset]}
      onDoubleClick={handleModelDoubleClick}
    >
      {/* 3. Render the scene directly—always defined once useGLTF has loaded */}
      <primitive object={gltf.scene} dispose={null} />
    </group>
  );
}

function ModelView() {
  const [modelUrl, setModelUrl] = useState(null);
  const inputRef = useRef();
  const [markers, setMarkers] = useState([]);
  const [showMarkers, setShowMarkers] = useState(true);
  const [showAllannottaion, setShowAllannottaion] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalData, setModalData] = useState({ point: null, normal: null });
  const [showAxisHelper, setShowAxisHelper] = useState(false);
  const { viewModelId, setViewModelId } = useStateContext();
  const orbitControlsRef = useRef();

  const hasModelGETAccess = useCheckAccess("model", "GET");
  const hasModelPUTAccess = useCheckAccess("model", "PUT");

  const handleModelDoubleClick = (event) => {
    event.stopPropagation();
    const point = event.point.clone();
    const normal = event.face.normal.clone();

    setModalData({ point, normal });
    setModalVisible(true);
  };

  useEffect(() => {
    const getRecentModel = async () => {
      try {
        const response = await axios.get(
          `${dbConfig.url}/model/${viewModelId}`,
        );
        const modelData = response?.data?.data;
        // Construct the complete URL for the model
        const completeModelUrl = `${dbConfig.url_storage}/${modelData?.url}`;
        setModelUrl(completeModelUrl);
        setMarkers(modelData?.markers || []);
      } catch (error) {
        console.error("Error fetching model:", error);
        toastMessage({ message: "Failed to load model data" });
      }
    };

    if (viewModelId) {
      getRecentModel();
    }
  }, [viewModelId]);

  const firebaseUpdate = (markers) => {
    const markersArray = JSON.parse(JSON.stringify(markers));
    const updateModel = async () => {
      try {
        const response = await axios.get(
          `${dbConfig.url}/model/${viewModelId}`,
        );
        const existingDocument = response.data.data;

        const updatedDocument = {
          desc: existingDocument.desc || "",
          mid: existingDocument.mid || "",
          url: existingDocument.url || "", // Store the original filename
          name: existingDocument.name || "",
          markers: markersArray,
        };

        await axios.put(
          `${dbConfig.url}/model/${viewModelId}`,
          updatedDocument,
        );
        toastMessageSuccess({ message: "Updated project successfully!" });
      } catch (error) {
        console.error("Error updating document:", error);
        toastMessage({ message: "Failed to update project" });
      }
    };

    updateModel();
  };

  const handleModalSubmit = (text, color, selectedSensor) => {
    setMarkers((prevMarkers) => {
      if (!Array.isArray(prevMarkers)) {
        prevMarkers = [];
      }

      const newMarker = {
        position: modalData.point,
        normal: modalData.normal,
        text: text || selectedSensor.tag,
        color,
        sensor: selectedSensor._id,
        value: selectedSensor.value || "",
        visible: false,
      };

      const updatedMarkers = [...prevMarkers, newMarker];
      firebaseUpdate(updatedMarkers);
      return updatedMarkers;
    });
    setModalVisible(false);
  };

  const deleteAnnotation = (index) => {
    const newAnnotations = [...markers];
    newAnnotations.splice(index, 1);
    setMarkers(newAnnotations);
    firebaseUpdate(newAnnotations);
  };

  const toggleAnnotation = React.useCallback((value) => {
    setMarkers((prevMarkers) =>
      prevMarkers?.map((marker) => ({ ...marker, visible: value })),
    );
  }, []);

  const toggleMarkerVisibility = React.useCallback((index, isVisible) => {
    setMarkers((prevMarkers) =>
      prevMarkers?.map((marker, i) =>
        i === index ? { ...marker, visible: isVisible } : marker,
      ),
    );
  }, []);

  const panToMarker = React.useCallback((position) => {
    if (orbitControlsRef?.current) {
      // Leaving this here for potential future debugging
      if (process.env.NODE_ENV === "development") {
        console.log(
          JSON.stringify(
            {
              "orbitControlsRef?.current.target":
                orbitControlsRef?.current.target,
              "orbitControlsRef?.current.object":
                orbitControlsRef?.current.object,
            },
            null,
            2,
          ),
        );
      }

      const distance = 2.5;
      const normalVector = new Vector3(0, 0, 1).applyQuaternion(
        new Quaternion().setFromUnitVectors(
          new Vector3(0, 0, 1),
          new Vector3(position?.x, position?.y, position?.z)?.normalize(),
        ),
      );
      const cameraPosition = new Vector3(
        position.x,
        position.y,
        position.z,
      )?.add(normalVector.clone().multiplyScalar(distance));

      /*
      Apparently, commenting out 
      the below 3 lines fixes the bug mentioned here: https://arizonsystems.atlassian.net/browse/AS-146
      orbitControlsRef?.current.target.set(
        position?.x,
        position?.y,
        position?.z
      );
      If the above 3 lines of code are needed,
      they can be uncommented. 
      A onMouseDown event handler can be added
      to set the orbitControlsRef?.current.target to 0, 0, 0.
      orbitControlsRef?.current.target.set(
        0,
        0,
        0
      );
      */

      orbitControlsRef?.current?.object?.position.set(
        cameraPosition?.x,
        cameraPosition?.y,
        cameraPosition?.z,
      );
      orbitControlsRef.current.update();
    }
  }, []);

  const commonCss = sharedCss();
  return hasModelGETAccess ? (
    <div>
      <div
        className={commonCss.backgroundLight}
        style={{
          width: "100%",
          height: `calc(100vh - 8rem)`,
          overflow: "hidden",
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          justifyContent: "center",
        }}
      >
        {modelUrl && (
          <Canvas
            style={{ width: "100vw", height: "100vh" }}
            toneMappingExposure={1}
            toneMapping="Linear"
            camera={{ position: [0, 0, 10], fov: 20 }}
          >
            <Suspense fallback={null}>
              <pointLight position={[5, 10, -10]} intensity={0.8} />
              <ambientLight intensity={3} color={"white"} />
              <spotLight
                position={[3, 6, 10]}
                intensity={1}
                angle={Math.PI / 4}
                penumbra={0.1}
                color={"white"}
              />
              <directionalLight position={[1, 1, 1]} intensity={1} />
              <spotLight
                position={[3, 6, 10]}
                intensity={1}
                angle={0.3}
                color={"white"}
              />
              <pointLight position={[0, 0, -10]} intensity={1} />

              <Model
                handleModelDoubleClick={handleModelDoubleClick}
                url={modelUrl}
              />

              {showMarkers &&
                markers?.map((marker, index) => {
                  const quaternion = new Quaternion().setFromUnitVectors(
                    new Vector3(0, 0, 1),
                    marker?.normal,
                  );
                  const textPosition = new Vector3(
                    marker?.position.x,
                    marker?.position.y,
                    marker?.position.z,
                  ).add(new Vector3(0, 0.15, 0.2));

                  return (
                    <React.Fragment key={index}>
                      <Marker
                        position={marker?.position}
                        normal={marker?.normal}
                        onPointerOver={() =>
                          toggleMarkerVisibility(index, true)
                        }
                        onPointerOut={() =>
                          toggleMarkerVisibility(index, false)
                        }
                      />
                      {marker?.text && marker?.visible && (
                        <Annotation
                          color={marker?.color}
                          position={textPosition}
                          text={marker?.text}
                          normal={marker?.normal}
                          value={marker?.value}
                        />
                      )}
                    </React.Fragment>
                  );
                })}
              <OrbitControls ref={orbitControlsRef} />
            </Suspense>
            {showAxisHelper && <axesHelper args={[5]} position={[0, 0, 0]} />}
          </Canvas>
        )}
        <Modal
          setMarkers={setMarkers}
          visible={modalVisible}
          onSubmit={handleModalSubmit}
          onCancel={() => setModalVisible(false)}
        />
      </div>
      <div
        style={{
          marginTop: "1rem",
          fontSize: "medium",
          zIndex: "1",
          display: "flex",
          flexDirection: "column",
          gap: ".5rem",
        }}
      >
        <Card style={{ display: "flex", justifyContent: "space-around" }}>
          <Link to="/3dmodel">
            <Button style={{ marginBlock: ".5rem" }} variant="contained">
              Back
            </Button>
          </Link>
          <FormControlLabel
            control={
              <Checkbox
                size="small"
                checked={showMarkers}
                onChange={() => setShowMarkers((prev) => !prev)}
              />
            }
            label="Marker"
            labelPlacement="end"
          />
          <FormControlLabel
            control={
              <Checkbox
                size="small"
                checked={showAxisHelper}
                onChange={() => setShowAxisHelper((prev) => !prev)}
              />
            }
            label="Axis Helper"
            labelPlacement="end"
          />
          <FormControlLabel
            control={
              <Checkbox
                size="small"
                checked={showAllannottaion}
                onChange={(e) => {
                  const isChecked = e.target.checked;
                  setShowAllannottaion(isChecked);
                  toggleAnnotation(isChecked);
                }}
              />
            }
            label="Annotations"
            labelPlacement="end"
          />
          <Button
            style={{ marginBlock: ".5rem" }}
            onClick={() => firebaseUpdate(markers)}
            variant="contained"
            disabled={!hasModelPUTAccess}
          >
            Save
          </Button>
        </Card>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell align="left">No</TableCell>
                <TableCell align="left">Sensor</TableCell>
                <TableCell align="center">Value</TableCell>
                <TableCell align="center">Marker</TableCell>
                <TableCell align="center">Action</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {markers?.map((x, index) => (
                <TableRow key={index}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{x?.text}</TableCell>
                  <TableCell align="center">{x?.value}</TableCell>
                  <TableCell
                    align="center"
                    // Pans to marker only if showMarkers is true ( annotation markers are visible )
                    onClick={() => showMarkers && panToMarker(x?.position)}
                    style={{
                      cursor: `${showMarkers ? "pointer" : "default"}`,
                      color: "#f05f40",
                      fontWeight: "bold",
                    }}
                  >
                    {x?.text}({x?.value})
                  </TableCell>
                  <TableCell align="center">
                    <IconButton
                      color="secondary"
                      onClick={() => deleteAnnotation(index)}
                    >
                      <DeleteIcon color="error" fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
    </div>
  ) : (
    <NotAccessible style={{ height: "80vh" }} />
  );
}

export default ModelView;
