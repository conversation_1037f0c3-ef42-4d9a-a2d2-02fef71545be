import React from "react";
import { <PERSON><PERSON>, <PERSON>, CardHeader, CardContent, Typography } from "@mui/material";

const DashboardGrid = ({ oeeData }) => {
  // Calculate Availability in percentage
  const calculateAvailability = () => {
    const { plannedProductionTime, stopTime } = oeeData;
    const runTime = plannedProductionTime - stopTime;
    return ((runTime / plannedProductionTime) * 100).toFixed(2);
  };

  // Calculate Performance in percentage
  const calculatePerformance = () => {
    const { idealCycleTime, totalCount, runTime } = oeeData;
    const theoreticalMaxOutput = (runTime * 60) / idealCycleTime; // Convert runTime to seconds
    return ((totalCount / theoreticalMaxOutput) * 100).toFixed(2);
  };

  // Calculate Quality in percentage
  const calculateQuality = () => {
    const { goodCount, totalCount } = oeeData;
    return ((goodCount / totalCount) * 100).toFixed(2);
  };

  // Calculate Overall OEE in percentage
  const calculateOverallOEE = () => {
    const availability = parseFloat(calculateAvailability()) / 100;
    const performance = parseFloat(calculatePerformance()) / 100;
    const quality = parseFloat(calculateQuality()) / 100;
    return (availability * performance * quality * 100).toFixed(2);
  };

  return (
    <Grid container spacing={2}>
      {/* Overall OEE Card */}
      <Grid item xs={12} sm={6} md={3}>
        <Card
          sx={{
            height: "300px",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            textAlign: "center",
          }}
        >
          <CardHeader
            title="Overall OEE"
            sx={{ backgroundColor: "#2196F3", color: "white", width: "100%" }}
          />
          <CardContent>
            <Typography variant="h5">{calculateOverallOEE()}%</Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Availability Card */}
      <Grid item xs={12} sm={6} md={3}>
        <Card
          sx={{
            height: "300px",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            textAlign: "center",
          }}
        >
          <CardHeader
            title="Availability"
            sx={{ backgroundColor: "#4CAF50", color: "white", width: "100%" }}
          />
          <CardContent>
            <Typography variant="h5">{calculateAvailability()}%</Typography>
            <Typography align="left" variant="body1">
              Planned Runtime:{" "}
              {(oeeData?.plannedProductionTime / 60).toFixed(2)} hours
            </Typography>
            <Typography align="left" variant="body1">
              Actual Runtime: {(oeeData?.runTime / 60).toFixed(2)} hours
            </Typography>
            <Typography align="left" variant="body1">
              Unplanned Downtime: {(oeeData?.stopTime / 60).toFixed(2)} hours
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Performance Card */}
      <Grid item xs={12} sm={6} md={3}>
        <Card
          sx={{
            height: "300px",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            textAlign: "center",
          }}
        >
          <CardHeader
            title="Performance"
            sx={{ backgroundColor: "#FFC107", color: "white", width: "100%" }}
          />
          <CardContent>
            <Typography variant="h5">{calculatePerformance()}%</Typography>
            <Typography align="left" variant="body1">
              Planned Quantity: {oeeData?.totalCount}
            </Typography>
            <Typography align="left" variant="body1">
              Actual Quantity: {oeeData?.goodCount}
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Quality Card */}
      <Grid item xs={12} sm={6} md={3}>
        <Card
          sx={{
            height: "300px",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            textAlign: "center",
          }}
        >
          <CardHeader
            title="Quality"
            sx={{ backgroundColor: "#FF5722", color: "white", width: "100%" }}
          />
          <CardContent>
            <Typography variant="h5">{calculateQuality()}%</Typography>
            <Typography align="left" variant="body1">
              Actual Quantity: {oeeData?.goodCount}
            </Typography>
            <Typography align="left" variant="body1">
              Rejected Quantity: {oeeData?.totalCount - oeeData?.goodCount}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default DashboardGrid;
