:root {
  --theme-color: #6366f1; /* Modern indigo */
  --green-color: #10b981; /* Modern emerald */
  --green-hover: #059669; /* Darker emerald */
  --bg-dark: #111827; /* Dark gray/slate */
  --bg-light: #f9fafb; /* Light gray */
  --text-light: #f3f4f6; /* Light text */
  --text-dark: #1f2937; /* Dark text */
  --accent: #8b5cf6; /* Purple accent */
  --border-dark: #374151; /* Dark border */
  --header-bg: #0f172a; /* Darker header */
  --toolbar-bg: #1e293b; /* Slate toolbar */
  --panel-bg: #1e293b; /* Slate panel */
}

/* Main container and layout */
.whiteboard-container {
  background: var(--bg-dark);
  color: var(--text-light);
  min-height: 100vh;
  /* Add flex and column to ensure header stays at top and content fills below */
  display: flex;
  flex-direction: column;
}

.whiteboard-header {
  background: var(--header-bg);
  color: var(--text-light);
  border-bottom: 2px solid var(--border-dark);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* Fix header height for proper layout calculation */
  min-height: 64px;
  height: 64px;
  box-sizing: border-box;
}

.whiteboard-header h1 {
  color: var(--text-light);
  margin: 0;
  font-size: 2rem;
}

.header-button {
  background: var(--accent);
  color: var(--text-light);
  border: none;
  border-radius: 4px;
  padding: 8px 18px;
  font-weight: 600;
  margin-left: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: background 0.2s;
}

.whiteboard-content {
  display: flex;
  background: var(--bg-dark);
  align-items: stretch;
  min-height: 0;
  /* allow to shrink */
  /* Use flex-grow to fill remaining space instead of fixed height */
  flex: 1 1 0; /* changed from flex: 1 1 auto; to fill more space */
  box-sizing: border-box;
  overflow: hidden;
}

.whiteboard-nav {
  display: flex;
  align-items: center;
}

.whiteboard-tabs {
  display: flex;
  align-items: center;
  gap: 4px;
}

.whiteboard-tab {
  background: var(--toolbar-bg);
  color: var(--text-light);
  padding: 6px 16px;
  border-radius: 6px;
  margin: 0 2px;
  cursor: pointer;
  border: 1px solid transparent;
  font-weight: 500;
  transition:
    background 0.2s,
    color 0.2s,
    border 0.2s;
}

.whiteboard-tab.active {
  background: var(--accent);
  color: var(--text-light);
  border: 1.5px solid var(--accent);
}

.new-board-button {
  background: #2d3240;
  color: #fff;
  border: 1px solid #444;
  border-radius: 6px;
  margin-left: 8px;
  cursor: pointer;
  padding: 6px 10px;
  display: flex;
  align-items: center;
  transition: background 0.2s;
}

.toolbar {
  background: var(--toolbar-bg);
  border-right: 2px solid var(--border-dark);
  padding: 12px 0;
  min-width: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* Fix: make toolbar scrollable if content overflows */
  max-height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
}

.tool-button {
  background: var(--bg-dark);
  color: var(--text-light);
  border: 2px solid transparent;
  border-radius: 8px;
  margin: 6px 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition:
    background 0.2s,
    color 0.2s,
    border 0.2s;
}

.tool-button.active {
  background: var(--accent);
  color: var(--text-light);
  border: 2px solid var(--accent);
}

.color-picker-tool {
  margin: 12px 0;
  width: 36px;
  height: 36px;
  border: 2px solid #444;
  border-radius: 6px;
  background: #23272f;
  cursor: pointer;
}

.canvas-container {
  position: relative;
  z-index: 0;
  background: #fff;
  border: 2px solid #444;
  border-radius: 8px;
  margin: 30px 24px 24px 24px; /* increased margin for more space */
  flex: 1 1 0;
  min-width: 0;
  min-height: 0;
  height: 100%; /* increased height by reducing margin subtraction */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.drawing-canvas {
  display: block;
  background: #fff;
  position: relative;
  z-index: 1;
  border-radius: 8px;
  width: 100% !important;
  height: 100% !important;
  min-height: 0;
  min-width: 0;
  box-sizing: border-box;
  flex: 1 1 auto;
}

.object-action-icons {
  position: absolute;
  z-index: 10;
  display: flex;
  gap: 8px;
  pointer-events: none;
}

.object-action-btn {
  pointer-events: auto;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition:
    background 0.2s,
    border 0.2s;
}

.object-action-btn.delete svg {
  color: #d00;
}
.object-action-btn.copy svg {
  color: #007bff;
}

.options-panel {
  background: var(--panel-bg);
  color: var(--text-light);
  border-left: 2px solid var(--border-dark);
  min-width: 300px;
  max-width: 300px;
  width: 300px;
  flex: 0 0 300px;
  /* Prevent shrinking/growing on tool change */
  padding: 16px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.12);
  z-index: 2;
  margin: 16px 16px 16px 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 400px;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  font-family: inherit;
  gap: 16px;
  /* Ensure content stays inside and is scrollable if needed */
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  max-height: 100%;
  /* Prevent child elements from overflowing horizontally */
  word-break: break-word;
}

/* Tool panel theme */
.tool-panel {
  background: var(--bg-dark);
  color: var(--text-light);
  border-radius: 8px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid var(--border-dark);
}

.tool-panel h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 1.1rem;
  color: #fff;
}

.tool-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.text-options .option-row,
.shape-properties .property-row,
.size-slider,
.color-picker {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
  max-width: 100%;
}

.text-options label,
.shape-properties span,
.size-slider label,
.color-picker label {
  min-width: 70px;
  font-size: 0.9rem;
  color: var(--text-light);
}

.text-options input[type="number"],
.text-options select,
.shape-properties input[type="color"],
.size-slider input[type="range"] {
  background: var(--bg-dark);
  color: var(--text-light);
  border: 1px solid var(--border-dark);
  border-radius: 4px;
  padding: 2px 6px;
  max-width: calc(100% - 75px);
  font-size: 0.9rem;
}

.shape-options-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.shape-option-btn {
  background: var(--bg-dark);
  color: var(--text-light);
  border: 1.5px solid var(--border-dark);
  border-radius: 6px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition:
    background 0.2s,
    color 0.2s,
    border 0.2s;
}

.shape-option-btn.active {
  background: var(--accent);
  color: var(--text-light);
  border: 1.5px solid var(--accent);
}

.color-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-preview div {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid var(--border-dark);
}

.preset-colors {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.preset-colors div {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid var(--border-dark);
  cursor: pointer;
}

.text-input-overlay {
  position: absolute;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #ccc;
  padding: 2px 4px;
  min-width: 40px;
  outline: none;
  z-index: 1002;
  pointer-events: auto;
  font-size: inherit;
  font-family: inherit;
}

.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-dialog {
  background: var(--bg-dark);
  color: var(--text-light);
  border-radius: 10px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.18);
  padding: 32px 40px;
  min-width: 320px;
}

.confirm-dialog-content p {
  margin-bottom: 24px;
}

.confirm-dialog-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
}

.confirm-btn,
.cancel-btn {
  background: var(--accent);
  color: var(--text-light);
  border: none;
  border-radius: 4px;
  padding: 8px 18px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.cancel-btn {
  background: var(--border-dark);
  color: var(--text-light);
}

/* Responsive fix for small screens */
@media (max-width: 900px) {
  .whiteboard-content {
    flex-direction: column;
    min-height: unset;
    height: auto;
  }
  .canvas-container,
  .options-panel {
    margin: 8px 8px 8px 8px;
    border-radius: 8px;
    border-right: 2px solid var(--border-dark);
    border-left: 2px solid var(--border-dark);
    min-height: 0;
    height: auto;
  }
  .canvas-container {
    margin-right: 10px;
    height: 500px; /* increased from 300px */
  }
  .options-panel {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    margin-bottom: 16px;
  }
}
