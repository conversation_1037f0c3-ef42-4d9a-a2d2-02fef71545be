import { <PERSON>, Button, Grid, Typography } from "@mui/material";
import React, { useState } from "react";
import * as XLSX from "xlsx";

const OperatingRangesForm = () => {
  const [excelFile, setExcelFile] = useState(null);
  const [excelFileError, setExcelFileError] = useState(null);

  // submit
  const [excelData, setExcelData] = useState(null);
  // it will contain array of objects

  // handle File
  const fileType = [
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ];

  const handleFile = (e) => {
    let selectedFile = e.target.files[0];
    if (selectedFile) {
      // console.log(selectedFile.type);
      if (selectedFile && fileType.includes(selectedFile.type)) {
        let reader = new FileReader();
        reader.readAsArrayBuffer(selectedFile);
        reader.onload = (e) => {
          setExcelFileError(null);
          setExcelFile(e.target.result);
        };
      } else {
        setExcelFileError("Please select only excel file types");
        setExcelFile(null);
      }
    } else {
      console.log("plz select your file");
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (excelFile !== null) {
      const workbook = XLSX.read(excelFile, { type: "buffer" });
      const worksheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[worksheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);
      setExcelData(data);
    } else {
      setExcelData(null);
    }
  };

  return (
    <div>
      <form className="form-group" autoComplete="off" onSubmit={handleSubmit}>
        <label>
          <h5>Upload Excel file</h5>
        </label>
        <br></br>
        <input
          type="file"
          className="form-control"
          onChange={handleFile}
          required
        ></input>
        {excelFileError && (
          <div className="text-red-500" style={{ marginTop: 5 + "px" }}>
            {excelFileError}
          </div>
        )}
        <Button
          type="submit"
          variant="contained"
          color="primary"
          style={{ marginTop: 5 + "px" }}
        >
          Submit
        </Button>
      </form>
      <Box sx={{ border: "1px solid black", p: 1 }}>
        <Typography align="center">
          <b> OPERATING RANGE OF INSTRUMENTS</b>{" "}
        </Typography>
      </Box>

      <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
        <Typography>QMS reference No </Typography>
      </Box>

      <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
        <Typography>Result </Typography>
      </Box>

      {/* Result custom table */}
      <Box>
        {/* 5 boxes */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderTop: "none",
          }}
        >
          {/* Sr no0 */}
          <Box sx={{ p: 1, borderRight: "1px solid black", minWidth: "60px" }}>
            <Typography align="center">Sl No. </Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "350px" }}>
            <Typography align="center">Instrument ID </Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "350px" }}>
            <Typography align="center">Overall Range </Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "500px" }}>
            <Box sx={{ borderBottom: "1px solid black" }}>
              <br />
              <Typography align="center"> Operating Range </Typography>
              <br />
            </Box>

            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <Box
                sx={{ p: 1, borderRight: "1px solid black", width: "140px" }}
              >
                <Typography>Vendor Recommendation </Typography>
              </Box>
              <Box
                sx={{ p: 1, borderRight: "1px solid black", width: "140px" }}
              >
                <Typography> Process Requirement </Typography>
              </Box>
              <Box
                sx={{ p: 1, borderRight: "1px solid black", width: "140px" }}
              >
                <Typography> Validation/ Qualification Requirement </Typography>
              </Box>
              <Box sx={{ p: 1, width: "140px" }}>
                <Typography> Operational SOP Requirement </Typography>
              </Box>
            </Box>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "350px" }}>
            <Typography align="center">Finalized Operating Range </Typography>
          </Box>
        </Box>
      </Box>

      {/* Content / data mapping goes here */}
      {excelData?.map((item, idx) => (
        <TableRow key={item?.sl_no + idx} item={item} />
      ))}
    </div>
  );
};

export default OperatingRangesForm;

const TableRow = ({ item }) => {
  return (
    <>
      <Box>
        {/* 5 boxes */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderTop: "none",
          }}
        >
          {/* Sr no0 */}
          <Box sx={{ p: 1, borderRight: "1px solid black", minWidth: "60px" }}>
            <Typography align="center">{item?.sl_no}</Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "350px" }}>
            <Typography align="center">{item?.instrument_id}</Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "350px" }}>
            <Typography align="center">{item?.overall_range}</Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "500px" }}>
            {/* <Box>
              <Typography align="center"> {item?.instrument_id}</Typography>
            </Box> */}

            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <Box
                sx={{ p: 1, borderRight: "1px solid black", width: "125px" }}
              >
                <Typography>{item?.vendor_recommendation}</Typography>
              </Box>
              <Box
                sx={{ p: 1, borderRight: "1px solid black", width: "125px" }}
              >
                <Typography>{item?.process_req}</Typography>
              </Box>
              <Box
                sx={{ p: 1, borderRight: "1px solid black", width: "125px" }}
              >
                <Typography>{item?.op_req}</Typography>
              </Box>
              <Box sx={{ p: 1, width: "125px" }}>
                <Typography>{item?.process_req}</Typography>
              </Box>
            </Box>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "350px" }}>
            <Typography align="center">{item?.final_range}</Typography>
          </Box>
        </Box>
      </Box>
    </>
  );
};
