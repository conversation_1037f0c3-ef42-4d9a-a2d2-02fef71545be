// import React from 'react'
// import DeleteIcon from '@mui/icons-material/Delete';
// import EditIcon from '@mui/icons-material/Edit';
// import { ButtonBasic, ButtonBasicCancel } from "../../components/buttons/Buttons";
// import axios from "axios";
// import { dbConfig } from "../../infrastructure/db/db-config";
// import { Dialog, DialogContent, DialogTitle, IconButton, InputLabel, TextField } from "@mui/material";
// import { useState, useEffect, useContext } from "react";
// import { useStateContext } from "../../context/ContextProvider";

// function EditHashtag(openEdit,options,object) {
//     const [openDel, setOpenDel] = useState(false);
// 	const [setOpenEdit] = useState(openEdit);
// 	const [hashtitle,setHashtagtitle]=useState();
// 	const [hashtag, setHashtag] = useState("");
//     const { currentMode } = useStateContext()
//     const handleSubmit=async(options)=>{
// 		console.log(object)
//         setOpenEdit(false)
// 	}
//   return (
//     <form>
//         <Dialog open={openEdit} fullWidth>
// 							<DialogTitle
// 							style={
// 							currentMode === "Dark"
// 							? { backgroundColor: "#212B36", color: "white" }
// 							: {}
// 							}>
// 							Edit Hashtag
// 							</DialogTitle>
// 							<DialogContent
// 								style={
// 								currentMode === "Dark"
// 								? { backgroundColor: "#212B36", color: "white" }
// 								: {}
// 								}>
// 								<InputLabel style={{ marginBottom: "10px" }}>Hashtag</InputLabel>
// 								<TextField
// 									onChange={(e) => setHashtagtitle(e.target.value)}
// 									style={{ marginBottom: "10px" }}
// 									variant='outlined'
// 									// value={hashtag}
// 									fullWidth
// 									multiline
// 								/>
// 								{console.log("women options",options)}
// 								<div className='p-2 mt-2 flex justify-between'>
// 							<ButtonBasicCancel
// 							buttonTitle='Cancel'
// 							type='button'
// 							onClick={() => setOpenEdit(false)}
// 							variant='outlined'
// 							/>
// 							<ButtonBasic buttonTitle='Submit' type='submit' variant='outlined' onClick={handleSubmit(options)}/>
// 						</div>
// 							</DialogContent>
// 		</Dialog>
//     </form>
//   )
// }

// export default EditHashtag
