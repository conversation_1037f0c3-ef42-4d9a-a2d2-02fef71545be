import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  TableCell,
  TableRow,
} from "@mui/material";
import { useAuth } from "../../../hooks/AuthProvider";

import EditDoc from "./EditDoc";
import DocumentationData from "../DocumentationData";
import ReportData from "../ReportData";
import { db } from "../../../firebase";
import { companies, companyId_constant } from "../../../constants/data";
import { toastMessage } from "../../../tools/toast";
import Delete from "../../../components/Delete/Delete";
import { Link } from "react-router-dom";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { useStateContext } from "../../../context/ContextProvider";
import { themeColors } from "../../../infrastructure/theme";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import { useDeleteMachineCfr } from "../../../hooks/cfr/machineCfrProvider";
import { useContentSetter } from "../../../services3/audits/ContentContext";

const DocItem = ({
  mid,
  data,
  type,
  machineName,
  idx,
  id,
  user,
  fatSeriesId,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openReport, setOpenReport] = useState(false);
  const { currentMode } = useStateContext();
  const handleContent = useContentSetter();
  const menuItemTheme = {
    backgroundColor:
      currentMode === "Dark"
        ? themeColors.dark.primary
        : themeColors.light.primary,
  };
  const deleteauditseriescontentcfr = useDeleteMachineCfr();
  const deletesatseriescontentcfr = useDeleteMachineCfr();

  const { currentUser } = useAuth();
  //comment by ankit
  let date = new Date();
  const data2 = {
    activity: "content deleted",
    dateTime: date,

    description: "content is deleted inside audit",
    machine: mid,
    module: "AUDIT",
    username: currentUser.username,
  };
  const data3 = {
    activity: "content deleted",
    dateTime: date,

    description: "content is deleted inside sat",
    machine: mid,
    module: "SAT",
    username: currentUser.username,
  };
  function deleteData() {
    axios.delete(`${dbConfig.url}/fatdatas/${data?._id}`).then(() => {
      if (type == "SAT") {
        deletesatseriescontentcfr(data3);
      } else {
        deleteauditseriescontentcfr(data2);
      }
      handleContent(id);
      toastMessage({ message: "Deleted data successfully !" });
    });
    // db.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(`${type.toLowerCase()}Data`)
    // 	.doc(data.id)
    // 	.delete()
    // 	.then(() => {
    // 		LoggingFunction(
    // 			machineName,
    // 			data.title,
    // 			`${user?.fname} ${user?.lname}`,
    // 			type,
    // 			`${data.title} is deleted from ${type} module`
    // 		)
    // 		toastMessage({ message: "Deleted data successfully !" });
    // 	});
  }

  return (
    <>
      <TableRow
        sx={{
          "&:last-child td, &:last-child th": { border: 0 },
        }}
      >
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { borderBottom: "none" }
          }
          align="left"
        >
          {data.index}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { borderBottom: "none" }
          }
          align="left"
        >
          <b>{data.title} </b>
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          <div className="dataBtns">
            <IconButton onClick={() => setOpenEdit(true)}>
              <EditIcon color="primary" style={{ fontSize: "20px" }} />
            </IconButton>
            <IconButton onClick={() => setOpenDel(true)}>
              <DeleteIcon style={{ fontSize: "20px", color: "#f00" }} />
            </IconButton>
            <IconButton size="small">
              {type === "FAT" ? (
                <Link to={`/${mid}/docDetailsFat/${data._id}`}>
                  <ChevronRightIcon
                    style={
                      currentMode === "Dark"
                        ? { fontSize: "20px", color: "white" }
                        : { fontSize: "20px" }
                    }
                  />
                </Link>
              ) : (
                <Link to={`/${mid}/docDetailsSat/${data._id}`}>
                  <ChevronRightIcon
                    style={
                      currentMode === "Dark"
                        ? { fontSize: "20px", color: "white" }
                        : { fontSize: "20px" }
                    }
                  />
                </Link>
              )}
            </IconButton>
          </div>
        </TableCell>
      </TableRow>
      <>
        {isOpen && (
          <TableRow
            sx={{
              "&:last-child td, &:last-child th": {
                border: 0,
              },
            }}
          >
            <TableCell
              className="subData"
              style={{ borderBottom: "none" }}
              align="center"
              colSpan={4}
            >
              <div>
                <DocumentationData type="FAT" docId={data._id} fatSeriesId />
              </div>
            </TableCell>
          </TableRow>
        )}
      </>
      <>
        {openReport && (
          <TableRow
            sx={{
              "&:last-child td, &:last-child th": {
                border: 0,
              },
            }}
          >
            <TableCell
              className="subData"
              style={{ borderBottom: "none" }}
              align="center"
              colSpan={4}
            >
              <div>
                <ReportData type="FATReport" docId={data.id} />
              </div>
            </TableCell>
          </TableRow>
        )}
      </>
      <Dialog open={openDel}>
        <Delete onClose={() => setOpenDel(false)} onDelete={deleteData} />
      </Dialog>

      <Dialog open={openEdit} fullWidth>
        <DialogTitle sx={menuItemTheme}>
          Edit Documentation - [{data.title}]{" "}
        </DialogTitle>
        <DialogContent sx={menuItemTheme}>
          <EditDoc
            handleClose={() => setOpenEdit(false)}
            mid={data.mid}
            id={id}
            data={data}
            type={type}
            machineName={machineName}
            dataTitle={data.title}
            userName={`${user?.fname} ${user?.lname}`}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DocItem;
