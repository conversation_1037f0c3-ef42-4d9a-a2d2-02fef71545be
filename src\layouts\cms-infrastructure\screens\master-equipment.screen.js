import React, { useEffect, useMemo, useState } from "react";

import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { useContext } from "react";
import { CmsInfraContext } from "../../../services/cms-infrastructure/cms-infra.context";
import MasterEquipmentRow from "../components/master-equipment-row";
import { useStateContext } from "../../../context/ContextProvider";
import { ButtonBasic } from "../../../components/buttons/Buttons";
import SapMasterForm from "../forms/cms-master.form";
import SapIntimationForm from "../forms/sap-intimation.form";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
} from "@mui/material";
import AddCmsInfrastructure from "../crud/add-forms/add-cms-infra.form";
import ReportItem from "../components/report-item.component";
import AddCmsReportForm from "../crud/add-forms/add-report.form";
import OperatingRangesForm from "../forms/operating-ranges.form";
import NoDataComponent from "../../../components/commons/noData.component";
import CommonDropDownComponent from "../../../components/commons/dropDown.component";

export default function MasterEquipmentScreen() {
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const { cmsData, reports } = useContext(CmsInfraContext);
  const [searchKeyWordBatch, setSearchKeywordBatch] = useState("");
  const [selectedPage, setSelectedPage] = useState("instrument");
  const [open, setOpen] = useState(false);
  const [openRep, setOpenRep] = useState(false);

  const filterCmsData = (data) => {
    return (
      data.name?.toUpperCase().search(searchKeyWordBatch.toUpperCase()) >= 0 ||
      data.make?.toUpperCase().search(searchKeyWordBatch.toUpperCase()) >= 0
    );
  };

  const isFilteredCmsDataAvailable = useMemo(
    () => cmsData?.filter(filterCmsData)?.length,
    [searchKeyWordBatch],
  );

  return (
    <>
      {/* <section style={currentMode === 'Dark' ? { backgroundColor: '#161C24', color: 'white' } : { backgroundColor: currentColorLight }} className="machineContainer"> */}
      <section className="userPage">
        {/* <div className='flex justify-between'> */}
        <div
          style={
            currentMode === "Dark"
              ? {
                  backgroundColor: "#161C24",
                  color: "white",
                  border: "1px solid white",
                }
              : { border: "1px solid black" }
          }
          className="userPageInfoContainer"
        >
          <div className="info">
            <h3>Calibration Management System</h3>
            <p>Equipments & SAP </p>
          </div>

          <div>
            {/*
            <FormControl>
              <InputLabel>Select Page</InputLabel>
              <Select
                value={selectedPage}
                onChange={(e) => setSelectedPage(e.target.value)}
                sx={{ width: "200px" }}
                fullWidth
                label="Select Page"
              > 
              <MenuItem value="instrument">Instrument Master</MenuItem>
                <MenuItem value="equip">Equipment Master</MenuItem>
                <MenuItem value="opr">Operating Ranges</MenuItem>
                <MenuItem value="report">Calibration Reports</MenuItem>
              </Select>
            </FormControl>
            */}
            <CommonDropDownComponent
              dropDownContainerStyle={{ width: "225px" }}
              dropDownLabel={"Select Page"}
              dropDownSize={"large"}
              handleChange={(e) => setSelectedPage(e.target.value)}
              menuData={[
                {
                  id: "instrument",
                  label: "Instrument Master",
                  value: "instrument",
                },
                { id: "equip", label: "Equipment Master", value: "equip" },
                { id: "opr", label: "Operating Ranges", value: "opr" },
                { id: "report", label: "Calibration Reports", value: "report" },
              ]}
              menuItemDisplay={"label"}
              menuItemValue={"value"}
              menuValue={selectedPage}
              menuValueDefault={selectedPage}
            />
          </div>
        </div>

        <Dialog open={open} onClose={() => setOpen(false)}>
          <DialogTitle>Add Equipment Data</DialogTitle>
          <DialogContent>
            <AddCmsInfrastructure />
          </DialogContent>
        </Dialog>
        {selectedPage === "instrument" && <SapMasterForm />}
        {/* ///////// */}

        {/* {selectedPage === "report" && <CalibrationReportTable/> } */}

        {selectedPage === "equip" && (
          <>
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                padding: "20px",
              }}
            >
              <div>
                <ButtonBasic
                  onClick={() => setOpen(true)}
                  buttonTitle="Add Equipment"
                />
              </div>
            </div>
            <div className="userListTableContainer">
              <TableContainer
                component={Paper}
                className="table"
                style={
                  currentMode === "Dark"
                    ? { border: "1px solid white" }
                    : { border: "1px solid black" }
                }
              >
                <Table
                  style={
                    currentMode === "Dark"
                      ? { backgroundColor: "#161C24", borderRadius: "10px" }
                      : { borderRadius: "10px" }
                  }
                  sx={{ minWidth: 650 }}
                >
                  <TableHead>
                    <TableRow>
                      <TableCell align="left">Equipment Name</TableCell>
                      <TableCell align="left">Make / Model </TableCell>
                      <TableCell align="left">SI Number</TableCell>
                      <TableCell align="left">Certificate Number</TableCell>
                      <TableCell align="left">Validity</TableCell>
                      <TableCell align="left">Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {cmsData?.filter(filterCmsData).map((batch, index) => (
                      <MasterEquipmentRow
                        key={index + batch?.id}
                        item={batch}
                        searchKeyWordBatch={searchKeyWordBatch}
                      />
                    ))}
                    {!isFilteredCmsDataAvailable && (
                      <>
                        <NoDataComponent cellColSpan={7} />
                      </>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          </>
        )}

        {/* Calibration Report */}
        {selectedPage === "report" && (
          <>
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                padding: "20px",
              }}
            >
              <div>
                <ButtonBasic
                  onClick={() => setOpenRep(true)}
                  buttonTitle="Create a new Report"
                />
              </div>
            </div>
            <Dialog open={openRep} onClose={() => setOpenRep(false)}>
              <DialogTitle>Add Report Data</DialogTitle>
              <DialogContent>
                <AddCmsReportForm />
              </DialogContent>
            </Dialog>
            <div className="userListTableContainer">
              <TableContainer
                component={Paper}
                className="table"
                style={
                  currentMode === "Dark"
                    ? { border: "1px solid white" }
                    : { border: "1px solid black" }
                }
              >
                <Table
                  style={
                    currentMode === "Dark"
                      ? { backgroundColor: "#161C24", borderRadius: "10px" }
                      : { borderRadius: "10px" }
                  }
                  sx={{ minWidth: 650 }}
                >
                  <TableHead>
                    <TableRow>
                      <TableCell align="left">
                        Calibration Procedure Number
                      </TableCell>
                      <TableCell align="left">Calibrated By </TableCell>
                      <TableCell align="left">Created By</TableCell>
                      <TableCell align="left">Criticality</TableCell>
                      <TableCell align="left">Instrument Calibrated</TableCell>
                      <TableCell align="left">Remarks</TableCell>
                      <TableCell align="left">Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {reports?.map((batch, index) => (
                      <ReportItem item={batch} key={index + batch.id} />
                    ))}
                    {!reports?.length && (
                      <>
                        <NoDataComponent cellColSpan={7} />
                      </>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          </>
        )}

        {selectedPage === "opr" && <OperatingRangesForm />}
      </section>
    </>
  );
}
