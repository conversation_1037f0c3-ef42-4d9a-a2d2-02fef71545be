import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Dialog,
  DialogActions,
  DialogContent,
  Di<PERSON>Title,
  <PERSON>ack,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import AcceptRejectAlertModal from "../../components/ui/alert-ui";
import PropTypes from "prop-types";

const TITLE_TEXT = "Edit Audit Trail Configuration";
const ACTIVITY_LABEL = "Activity Name";
const DESCRIPTION_LABEL = "Description";
const DESCRIPTION_PLACEHOLDER = "MultiLine with rows: 2 and rowsMax: 4";
const SUBMIT_BUTTON_TEXT = "Submit";
const LOADING_TEXT = "Loading...";
const CANCEL_BUTTON_TEXT = "Cancel";
const ALERT_DESCRIPTION = "Enter passkey to edit CFR Configuration";
const ACCEPT_BUTTON_TEXT = "Yes";
const REJECT_BUTTON_TEXT = "No";
const ACCEPT_TOOLTIP = "Edit Tolerance";

const FormInput = ({ label, value, onChange, helperText, name, ...props }) => {
  return (
    <TextField
      size="small"
      fullWidth
      placeholder={label}
      name={name}
      label={label}
      value={value}
      onChange={onChange}
      helperText={helperText}
      inputProps={{ maxLength: 20 }}
      {...props}
    />
  );
};

const INITIAL_DATA = {
  unit: "",
};

const ConfigEditModal = ({
  openModal = false,
  setOpenModal = () => {},
  data = INITIAL_DATA,
  forEdit = false,
  updateConfig,
}) => {
  const [formState, setFormState] = useState({});
  const [loading, setLoading] = useState(false);
  const [openConfirm, setOpenConfirm] = useState(false);

  useEffect(() => {
    setFormState(data);
  }, [data]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormState({ ...formState, [name]: value.trimStart() });
  };

  const handleSubmit = async (e) => {
    await updateConfig(formState);
    setFormState({});
    setOpenModal(false);
    setLoading(false);
  };

  return (
    <>
      <Dialog open={openModal} maxWidth="sm" fullWidth>
        <form>
          <DialogTitle>
            <Typography fontWeight={600} variant="h5">
              {TITLE_TEXT}
            </Typography>
          </DialogTitle>
          <DialogContent style={{ padding: "2rem" }} fullWidth>
            <Stack spacing={2} container>
              <FormInput
                name="activity"
                label={ACTIVITY_LABEL}
                required
                onChange={handleChange}
                value={formState.activity}
              />
              <TextField
                name="description"
                label={DESCRIPTION_LABEL}
                placeholder={DESCRIPTION_PLACEHOLDER}
                multiline
                rows={2}
                maxRows={4}
                onChange={handleChange}
                value={formState.description}
              />
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button
              disabled={loading}
              variant="contained"
              color="success"
              sx={{ margin: ".5rem 1rem " }}
              onClick={() => setOpenConfirm(true)}
            >
              {loading ? LOADING_TEXT : SUBMIT_BUTTON_TEXT}
            </Button>
            <Button
              variant="contained"
              color="error"
              onClick={() => {
                setOpenModal(false);
                setFormState(data);
              }}
            >
              {CANCEL_BUTTON_TEXT}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      <AcceptRejectAlertModal
        open={openConfirm}
        setOpen={setOpenConfirm}
        desc={ALERT_DESCRIPTION}
        pending={loading}
        handleAccept={(qnn, remark) => {
          handleSubmit().then(() => {
            setOpenConfirm(false);
          });
        }}
        handleReject={(qnn, remark) => {
          setOpenConfirm(false);
        }}
        AcceptBtnName={ACCEPT_BUTTON_TEXT}
        RejectBtnName={REJECT_BUTTON_TEXT}
        AcceptToolTip={ACCEPT_TOOLTIP}
        hideQnn
        hideRemark
        key={forEdit + formState.unit}
      />
    </>
  );
};

FormInput.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  helperText: PropTypes.string,
  name: PropTypes.string.isRequired,
};

ConfigEditModal.propTypes = {
  openModal: PropTypes.bool,
  setOpenModal: PropTypes.func,
  data: PropTypes.shape({
    unit: PropTypes.string,
  }),
  forEdit: PropTypes.bool,
  updateConfig: PropTypes.func.isRequired,
};

export default ConfigEditModal;
