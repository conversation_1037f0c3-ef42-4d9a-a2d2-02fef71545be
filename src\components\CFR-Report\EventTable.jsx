import React from "react";
import moment from "moment-timezone";

import {
  Paper,
  CircularProgress,
  TableContainer,
  Table,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@mui/material";
import { useStateContext } from "../../context/ContextProvider";
const indianTimeZone = "Asia/Kolkata";

function EventTable(props) {
  const { machineDetails, data, filteredModuleSet, load } = props;

  const { currentMode } = useStateContext();
  console.log("testnew2", machineDetails);
  let reportItems = [];

  if (data.length > 0) {
    reportItems = data?.filter(
      (item) => item.machine?.trim() === machineDetails?.trim(),
    );
  }
  console.log("reportItems", reportItems);
  reportItems = reportItems
    .slice()
    .sort((a, b) => {
      let a_date = a.dateTime.seconds;
      let b_date = b.dateTime.seconds;
      if (a_date < b_date) {
        return 1;
      }
      if (a_date > b_date) {
        return -1;
      }
      return 0;
    })
    .reverse();

  return data.length > 0 ? (
    <TableContainer component={Paper} className="tableContainer">
      <Table className="insideTable">
        <TableHead>
          <TableRow>
            <TableCell align="left">Date/Time</TableCell>
            <TableCell align="left">Activity</TableCell>
            <TableCell align="left">Username</TableCell>
            <TableCell align="left">Module</TableCell>
            <TableCell align="left">Description</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {filteredModuleSet &&
            reportItems.map((item, idx) => {
              if (filteredModuleSet.has(item.module.trim()))
                return (
                  <TableRow key={`${item.dateTime} - ${item.dateTime}`}>
                    <TableCell
                      sx={currentMode === "Dark" ? { color: "white" } : {}}
                      style={
                        idx === reportItems.length - 1 && currentMode === "Dark"
                          ? { borderBottom: "1px solid white" }
                          : {}
                      }
                      align="left"
                    >
                      {" "}
                      {
                        /* {`${moment
                        .utc(item.dateTime)
                        .format("YYYY-MM-DD")} at ${moment
                        .utc(item?.dateTime)
                        .format("HH:mm:ss ")}` */
                        `${moment.tz(item.dateTime, indianTimeZone).format("YYYY-MM-DD [at] HH:mm:ss")}`
                      }
                    </TableCell>
                    <TableCell
                      sx={currentMode === "Dark" ? { color: "white" } : {}}
                      style={
                        idx === reportItems.length - 1 && currentMode === "Dark"
                          ? { borderBottom: "1px solid white" }
                          : {}
                      }
                      align="left"
                    >
                      {item.activity}
                    </TableCell>
                    <TableCell
                      sx={currentMode === "Dark" ? { color: "white" } : {}}
                      style={
                        idx === reportItems.length - 1 && currentMode === "Dark"
                          ? { borderBottom: "1px solid white" }
                          : {}
                      }
                      align="left"
                    >
                      {item.username}
                    </TableCell>
                    <TableCell
                      sx={currentMode === "Dark" ? { color: "white" } : {}}
                      style={
                        idx === reportItems.length - 1 && currentMode === "Dark"
                          ? { borderBottom: "1px solid white" }
                          : {}
                      }
                      align="left"
                    >
                      {item.module}
                    </TableCell>
                    <TableCell
                      sx={currentMode === "Dark" ? { color: "white" } : {}}
                      style={
                        idx === reportItems.length - 1 && currentMode === "Dark"
                          ? { borderBottom: "1px solid white" }
                          : {}
                      }
                      align="left"
                    >
                      {item.description}
                    </TableCell>
                  </TableRow>
                );
            })}
        </TableBody>
      </Table>
    </TableContainer>
  ) : (
    <div className="h-32 w-full flex justify-center items-center">
      {load ? (
        <CircularProgress />
      ) : (
        <div style={currentMode === "Dark" ? { color: "white" } : {}}>
          No Data
        </div>
      )}
    </div>
  );
}

export default EventTable;
