import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { LoadingContextNonFunctionalComp } from "./LoadingProvider";
import { toast } from "react-toastify";

export const getAllUserCount = async (setUserCount, setLoading = () => {}) => {
  LoadingContextNonFunctionalComp.setIsLoading(true);
  setLoading(true);
  try {
    const res = await axios.get(`http://localhost:8000/api/v1/users/count`);
    setUserCount(res?.data);
    toast.success("User count fetched successfully.");
  } catch (err) {
    toast.error(
      "Failed to fetch user count: " + (err.message || "Unknown error"),
    );
  } finally {
    setLoading(false);
    LoadingContextNonFunctionalComp.setIsLoading(false);
  }
};

export const getAllLDAPUsers = async (setLdapUsers, setLoading = () => {}) => {
  LoadingContextNonFunctionalComp.setIsLoading(true);
  setLoading(true);
  try {
    const resp = await axios.get(`${dbConfig?.url}/auth/ldapusers`);
    const users = resp.data;
    setLdapUsers(users);
    console.log("resp.status:- ", resp.status);
    if (resp.status === 200) {
      toast.success("LDAP users fetched successfully.");
    } else {
      toast.error(
        "Failed to fetch LDAP users: Unexpected response status " + resp.status,
      );
    }
  } catch (error) {
    toast.error(
      "Failed to fetch LDAP users: " + (error.message || "Unknown error"),
    );
  } finally {
    setLoading(false);
    LoadingContextNonFunctionalComp.setIsLoading(false);
  }
};

export const getAllLDAPUsersUsingConfig = async (config, setLdapUsers) => {
  console.log("config:- ", config);
  try {
    console.log("auth/ldapusers hitted");
    const resp = await axios.post(`${dbConfig?.url}/auth/ldapusers`, {
      config: config,
    });
    console.log("resp:- ", resp);
    const users = resp.data;
    console.log("users from backend ldap:- ", users);
    setLdapUsers(users);
    if (resp.status === 200) {
      toast.success("LDAP users fetched successfully.");
    } else {
      toast.error(
        "Failed to fetch LDAP users: Unexpected response status " + resp.status,
      );
    }
  } catch (error) {
    // Handle Axios error with structured backend response
    console.error("Error fetching LDAP users:", error);
    const errorMessage =
      error.response?.data?.message || error.message || "Unknown error";
    toast.error("Failed to fetch LDAP users: " + errorMessage);
    throw new Error(errorMessage); // Throw a new error with the message for LdapConfigForm to catch
  }
};
