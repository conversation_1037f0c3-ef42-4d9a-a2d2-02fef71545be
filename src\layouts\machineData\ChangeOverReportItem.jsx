import React, { useState, useEffect } from "react";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import {
  TableCell,
  TableRow,
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  Box,
  Chip,
  CircularProgress,
  Tooltip,
  Menu,
  MenuItem as MuiMenuItem,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import Delete from "../../components/Delete/Delete";
import { useStateContext } from "../../context/ContextProvider";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { useMaintenanceInfo } from "../../context/MaintenanceContext";
import { useUser } from "../../context/UserContext";
import moment from "moment";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import EditChangeOverReport from "./EditMaintenance/EditChangeOverReport";
import SelectedComponent from "../../components/reports/SelectedComponent";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { PictureAsPdf } from "@mui/icons-material";
import NoDataComponent from "../../components/commons/noData.component";
import { commonRowStyle } from "./MaintenanceReportDataMain";
import AutoAwesomeIcon from "@mui/icons-material/AutoAwesome";
import ReportAnalyseAndChatByAiMain from "./AiSection/ReportAnalyseAndChatByAiMain";
import { PerformModal } from "../../components/reports/PerformModal";
import BuildIcon from "@mui/icons-material/Build";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";

const ChangeOverReportItem = ({
  data,
  machineData,
  stepData,
  setChangeOverReportDataAll,
  refreshChangeOverReports,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openAiDialog, setOpenAiDialog] = useState(false);
  const [openPerformModal, setOpenPerformModal] = useState(false); // State for PerformModal
  const [carousalData, setCarousalData] = useState([]);
  const [allSortKeys, setAllSortKeys] = useState([]);
  const { currentMode } = useStateContext();
  const [enlarge, setEnlarge] = useState(false);
  const [eValue, setEValue] = useState("");
  const [remarks, setRemarks] = useState("");
  const [selectedStep, setSelectedStep] = useState(0);
  const maintenanceInfoFromContext = useMaintenanceInfo();
  const user = useUser();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const [reset, setReset] = useState(0);
  const [pdfGenrationActive, setPdfGenrationActive] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const openMenu = Boolean(anchorEl);

  const hasChangeOverReportPUTAccess = useCheckAccess(
    "changeOverReport",
    "PUT",
  );
  const hasChangeOverReportDELETEAccess = useCheckAccess(
    "changeOverReport",
    "DELETE",
  );

  const hasChangeOverReportStepGETAccess = useCheckAccess(
    "changeOverReportSteps",
    "GET",
  );

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  useEffect(() => {
    if (!data?._id) return;

    axios
      .get(`${dbConfig.url}/changeOverReportSteps/getFromReport/${data?._id}`)
      .then((res) => {
        let dataNow = res?.data?.data;
        // Sort dataNow by sortKey ascending
        if (Array.isArray(dataNow)) {
          dataNow = [...dataNow].sort(
            (a, b) => (a.sortKey ?? 0) - (b.sortKey ?? 0),
          );
        }
        setCarousalData(dataNow);
        // Extract sortKey from each step, sort, and store in allSortKeys (ascending)
        const sortKeys = Array.isArray(dataNow)
          ? dataNow
              .map((step) => step.sortKey)
              .filter((k) => k !== undefined && k !== null)
          : [];
        setAllSortKeys(sortKeys);
        console.log("changeover report step data from mongo : data:", dataNow);
      })
      .catch((e) => {
        console.log("error changeover report step data from mongo : data:", e);
      });
  }, [data, refreshCount]);

  useEffect(() => {
    setRemarks(carousalData[selectedStep]?.remarks || "");
  }, [selectedStep, carousalData]);

  const machineName = machineData.filter(
    (machine) => machine?.id === data?.mid,
  )[0]?.title;

  const handleDownloadPDF = async () => {
    setPdfGenrationActive(true);
    try {
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const res = await axios.get(
        `${dbConfig.url}/changeOverReportSteps/pdf/export?manual_id=${
          data?._id
        }&timezone=${encodeURIComponent(timeZone)}`,
      );
      console.log("PDF response:", res);
      console.log("timezone:", timeZone);
      window.open(`${dbConfig.url}/${res.data.data}`, "_blank");
    } catch (err) {
      console.error(err);
      toastMessage({
        message: err?.response?.data?.message ?? "Error generating PDF",
      });
    } finally {
      setPdfGenrationActive(false);
    }
  };

  async function deleteData() {
    await axios
      .delete(`${dbConfig.url}/changeOverReport/${data?._id}`)
      .then(() => {
        setOpenDel(false);
        toastMessage({ message: `Deleted ${data?.title} successfully!` });
        setRefreshCount(refreshCount + 1);
        setChangeOverReportDataAll((prev) =>
          prev.filter((item) => item._id !== data._id),
        );
      })
      .catch(() => {
        toastMessage({ message: "Failed to delete" });
      });
  }

  const uOrA = async (updatedItem) => {
    var manualId;
    const document = {
      ...updatedItem,
      date: new Date(updatedItem.date),
      updated_at: new Date().toISOString(),
      sub_steps:
        updatedItem.sub_steps !== undefined ? updatedItem.sub_steps : false,
    };
    await axios
      .put(`${dbConfig.url}/changeOverReportSteps/${updatedItem._id}`, document)
      .then((res) => {
        manualId = res.data.data.manual_id;
        toastMessageSuccess({ message: "Updated Remarks Successfully" });
        refreshChangeOverReports();
        setCarousalData((prev) =>
          prev.map((item) =>
            item._id === updatedItem._id ? { ...item, ...updatedItem } : item,
          ),
        );
        setReset(reset + 1);
      })
      .catch((err) => {
        console.error("Error updating remarks:", err);
      });

    // Check all steps' remarks for this report
    try {
      const stepsRes = await axios.get(
        `${dbConfig.url}/changeOverReportSteps/getFromReport/${manualId}`,
      );
      const steps = stepsRes?.data?.data || [];
      const allFilled =
        steps.length > 0 &&
        steps.every(
          (step) =>
            step.remarks !== "" &&
            step.remarks !== null &&
            step.remarks !== "NA",
        );
      if (allFilled) {
        await axios
          .put(`${dbConfig.url}/changeOverReport/${manualId}`, {
            report_status: 1,
          })
          .then(() => {
            toastMessageSuccess({
              message: "Report Status Updated Successfully",
            });
            refreshChangeOverReports();
            setRefreshCount(refreshCount + 1);
          })
          .catch((err) => {
            console.error("Error updating report status:", err);
          });
      }
    } catch (err) {
      console.error("Error checking all step remarks:", err);
    }
  };

  return (
    <>
      <TableRow sx={commonRowStyle}>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          <b className="capitalize">{data?.title}</b>
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          {moment(data?.date).format("DD MMM YYYY")}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          <b className="capitalize" data-title={data?.email}>
            {data?.email.split("@")[0]}
          </b>
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="left"
        >
          {data?.remarks}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="center"
        >
          {(() => {
            const status = data?.report_status;
            switch (status) {
              case 0:
                return (
                  <span className="font-bold text-yellow-500">In Progress</span>
                );
              case 1:
                return (
                  <span className="font-bold text-blue-500">
                    Ready for Review
                  </span>
                );
              case 2:
                return (
                  <span className="font-bold text-green-500">Reviewed</span>
                );
              default:
                return <span className="font-bold text-gray-500">Unknown</span>;
            }
          })()}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { borderBottom: "1px solid #e0e0e0" }
          }
          align="center"
        >
          <div className="dataBtns">
            <IconButton
              onClick={() => setOpenEdit(true)}
              disabled={!hasChangeOverReportPUTAccess}
              sx={{
                color: !hasChangeOverReportPUTAccess
                  ? "grey.500"
                  : "primary.main",
              }}
            >
              <EditIcon style={{ fontSize: "20px" }} />
            </IconButton>

            <IconButton onClick={handleMenuOpen}>
              <MoreVertIcon
                style={{
                  fontSize: "20px",
                  color: currentMode === "Dark" ? "#fff" : "#000",
                }}
              />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={openMenu}
              onClose={handleMenuClose}
              PaperProps={{
                style: {
                  backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
                  color: currentMode === "Dark" ? "#fff" : "#000",
                },
              }}
            >
              <MuiMenuItem
                onClick={() => {
                  setOpenDel(true);
                  handleMenuClose();
                }}
                disabled={!hasChangeOverReportDELETEAccess}
              >
                <DeleteIcon
                  style={{
                    fontSize: "20px",
                    color: "#f00",
                    marginRight: "8px",
                  }}
                />
                Delete
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenAiDialog(true);
                  handleMenuClose();
                }}
              >
                <AutoAwesomeIcon
                  style={{
                    fontSize: "20px",
                    color: "forestgreen",
                    marginRight: "8px",
                  }}
                />
                AI Chat and Analysis
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  handleDownloadPDF();
                }}
                disabled={
                  pdfGenrationActive || !hasChangeOverReportStepGETAccess
                }
              >
                {pdfGenrationActive ? (
                  <CircularProgress size={20} style={{ marginRight: "8px" }} />
                ) : (
                  <PictureAsPdf
                    style={{
                      fontSize: "20px",
                      color: "red",
                      marginRight: "8px",
                    }}
                  />
                )}
                {pdfGenrationActive ? "Generating PDF..." : "Download PDF"}
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenPerformModal(true);
                  handleMenuClose();
                }}
              >
                <BuildIcon
                  style={{
                    fontSize: "20px",
                    color: "#2196f3",
                    marginRight: "8px",
                  }}
                />
                Perform
              </MuiMenuItem>
            </Menu>
            <IconButton
              onClick={() => setIsOpen(!isOpen)}
              style={currentMode === "Dark" ? { color: "white" } : {}}
            >
              {isOpen ? (
                <ExpandLessIcon style={{ fontSize: "20px" }} />
              ) : (
                <ExpandMoreIcon style={{ fontSize: "20px" }} />
              )}
            </IconButton>
          </div>
        </TableCell>
      </TableRow>

      {isOpen &&
        (carousalData?.length > 0 ? (
          <TableRow sx={{ "&:last-child td, &:last-child th": { border: 0 } }}>
            <TableCell colSpan={6}>
              <Box
                sx={{
                  padding: 1,
                  backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
                  border:
                    currentMode === "Dark"
                      ? "1px solid white"
                      : "1px solid black",
                }}
              >
                <Box sx={{ display: "flex", gap: "20px" }}>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      flexWrap: "wrap",
                      gap: 1,
                      maxHeight: "300px",
                      overflowY: "auto",
                    }}
                  >
                    {carousalData?.length > 0 &&
                    hasChangeOverReportStepGETAccess
                      ? carousalData.map((item, idx) => (
                          <Chip
                            key={idx}
                            label={`Step ${idx + 1}`}
                            onClick={() => setSelectedStep(idx)}
                            color={selectedStep === idx ? "primary" : "default"}
                            sx={{
                              borderRadius: "50%",
                              width: "50px",
                              height: "50px",
                              fontSize: "12px",
                              backgroundColor:
                                item.remarks === null ||
                                item.remarks === "NA" ||
                                item.remarks === ""
                                  ? "#E78895"
                                  : "#74E291",
                              "& .MuiChip-label": {
                                padding: "0",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                              },
                            }}
                          />
                        ))
                      : null}
                  </Box>
                  <Box sx={{ flexGrow: 1 }}>
                    {hasChangeOverReportStepGETAccess ? (
                      carousalData[selectedStep] && (
                        <SelectedComponent
                          index={selectedStep}
                          key={carousalData[selectedStep]._id}
                          item={carousalData[selectedStep]}
                          setRemarks={setRemarks}
                          setEValue={setEValue}
                          setEnlarge={setEnlarge}
                          remarks={remarks}
                          uOrA={uOrA}
                        />
                      )
                    ) : (
                      <NotAccessible />
                    )}
                  </Box>
                </Box>
              </Box>
            </TableCell>
          </TableRow>
        ) : hasChangeOverReportStepGETAccess ? (
          <NoDataComponent
            noDataMessage={"No Steps Available"}
            paddText={true}
            padding={"12px"}
          />
        ) : (
          <NotAccessible />
        ))}

      <Dialog open={openDel}>
        <Delete onClose={() => setOpenDel(false)} onDelete={deleteData} />
      </Dialog>
      <Dialog open={openEdit} fullWidth>
        <DialogTitle>Edit Report [{data?.title}]</DialogTitle>
        <DialogContent>
          <EditChangeOverReport
            handleClose={() => setOpenEdit(false)}
            mid={data.mid}
            data={data}
            userName={`${user?.fname} ${user?.lname}`}
            reportName={data.title}
            machineName={machineName}
            refreshChangeOverReports={refreshChangeOverReports}
          />
        </DialogContent>
      </Dialog>
      <Dialog
        open={enlarge}
        onClose={() => setEnlarge(false)}
        fullWidth
        maxWidth="lg"
      >
        <DialogTitle>Enlarged Image</DialogTitle>
        <DialogContent>
          <img
            src={`${dbConfig.url_storage}/${eValue}`}
            alt="Enlarged"
            style={{ width: "100%", height: "100%" }}
          />
        </DialogContent>
      </Dialog>
      <Dialog open={openAiDialog} fullWidth maxWidth="lg">
        <DialogTitle>
          Chat and Analyze [{data?.title}]
          <Tooltip title="AI Chat and Analyze">
            <AutoAwesomeIcon
              className="animate-pulse"
              style={{ fontSize: "30px", color: "#00f" }}
            />
          </Tooltip>
        </DialogTitle>
        <DialogContent>
          <ReportAnalyseAndChatByAiMain
            handleClose={() => setOpenAiDialog(false)}
            mid={data.mid}
            data={data}
            userName={`${user?.fname} ${user?.lname}`}
            reportName={data.title}
            machineName={machineName}
          />
        </DialogContent>
      </Dialog>
      <PerformModal
        rowData={data}
        currStep={selectedStep}
        carousalData={carousalData}
        setEValue={setEValue}
        setEnlarge={setEnlarge}
        handleSubmit={async (item, updatedItem) => {
          await uOrA(updatedItem);
        }}
        open={openPerformModal}
        handleClose={() => setOpenPerformModal(false)}
        allSortKeys={allSortKeys} // Add this prop
      />
    </>
  );
};

export default ChangeOverReportItem;
