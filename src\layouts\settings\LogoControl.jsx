import axios from "axios";
import React, { useState } from "react";
import { dbConfig } from "../../infrastructure/db/db-config";
import { Box, Button, FormHelperText, Typography } from "@mui/material";
import { DropzoneArea } from "material-ui-dropzone";
import { convertBase64 } from "../../hooks/useBase64";
import { toastMessageSuccess } from "../../tools/toast";
import { useLogo } from "../../context/LogoContext";

const LogoControl = () => {
  const { handleUpdateLogo, handleUpdateLogoForPdfTemplates, logoImageData } =
    useLogo();
  const [asset, setAsset] = useState({
    id: logoImageData?._id ?? logoImageData?.id,
    image_url: "",
    imageFile: null,
  });
  const typesImages = ["image/png", "image/jpeg", "image/jpg"];
  const [key, setKey] = useState(0);

  const handleUploadLogo = async () => {
    try {
      const uploadResult = await handleUpdateLogo({
        id: asset.id,
        image_url: asset.image_url,
      });
      const uploadPdfTemplateLogoResult = await handleUpdateLogoForPdfTemplates(
        asset.imageFile,
      );

      toastMessageSuccess({ message: "Logo updated" });
      setAsset({ ...asset, image_url: null, imageFile: null });
      setKey((prev) => prev + 1);
      return { uploadResult, uploadPdfTemplateLogoResult };
    } catch (error) {
      console.error(error);
      return null;
    }
  };

  const handleChangeLogo = async (e) => {
    try {
      if (!e?.length) {
        return null;
      }

      const base64Logo = await convertBase64(e[0]);
      setAsset({ ...asset, image_url: base64Logo, imageFile: e[0] });
    } catch (error) {
      console.error(error);
    }
  };

  const handleDelete = () =>
    setAsset({ ...asset, image_url: null, imageFile: null });

  return (
    <Box component={"div"} sx={{ marginTop: "2rem" }}>
      <Typography variant={"h5"} component={"h5"}>
        {"Set Logo"}
      </Typography>

      {/* Dropzone */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <DropzoneArea
          key={key}
          onChange={handleChangeLogo}
          onDrop={handleChangeLogo}
          onDelete={handleDelete}
          filesLimit={1}
          maxFileSize={1 * 1024 * 1024}
          dropzoneText="Drag & Drop or Click to Add File"
          showFileNames
          disabled={false}
          getDropRejectMessage={() => "File size exceeds 1MB!"}
          acceptedFiles={typesImages}
          sx={{ borderRadius: 1 }}
        />
        <FormHelperText sx={{ mt: 1 }}>
          Max file size: 1MB. Supported formats: {"PNG, JPG"}
        </FormHelperText>
      </Box>
      <Button
        variant={"contained"}
        onClick={handleUploadLogo}
        disabled={!asset.image_url}
      >
        {"Update logo"}
      </Button>
    </Box>
  );
};

export default LogoControl;
