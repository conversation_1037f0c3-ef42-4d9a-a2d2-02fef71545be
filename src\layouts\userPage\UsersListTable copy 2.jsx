// import {
//   Paper,
//   Table,
//   TableBody,
//   TableCell,
//   TableContainer,
//   TableHead,
//   Box,
//   TableRow,
//   Typography,
//   Skeleton,
// } from "@mui/material";
// import React from "react";
// import { useEffect, useState } from "react";
// import UserItem from "./UserItem";
// import { AuthProvider } from "./hooks/AuthProvider";
// import { makeStyles } from "@mui/styles";
// import { v4 as uuidv4 } from "uuid";
// import { CfrContextProvider } from "./hooks/CfrProvider";
// import Pagination from './UI_Components/pagination';
// import SearchField from './UI_Components/search-field';
// import PropTypes from 'prop-types';
// const useStyles = makeStyles(() => ({
//   container: {
//     maxHeight: "84vh",
//   },
//   tableHeaderCell: {
//     // backgroundColor: "#f8f9fa !important",
//     // color: "black  !important",
//     fontWeight: "bold !important",
//     fontSize: "0.9rem !important",
//     minWidth: "6rem !important",
//   },
//   tableCell: {},
// }));
// export const columnCode = [
//   "fname",
//   // "username",
//   "username",
//   "user_role",
//   "email",
//   // "contact",
//   // "password_valid_for",
//   "actions",
// ];

// const UserListTable = ({ allUsersList, fetchAllUsers, loading, setloading, isModelOpen }) => {
//   //here is the code for getting the data
//   const currentUserTemp = localStorage.getItem("@user-creds");

//   const currentUser = JSON.parse(currentUserTemp);
//   // eslint-disable-next-line no-unused-vars
//   const [passwordToggel, setPasswordToggel] = useState(false);
//   const [pagination, setPagination] = useState({
//     currentPage: 1,
//     itemsPerPage: 6
//   })
//   const [search, setSearch] = useState("");

//   const [sortedData, setSortedData] = useState([]);

//   const [currentItems, setCurrentItems] = useState([]);

//   useEffect(() => {
//     setSortedData(allUsersList.filter((user) => {
//       return (user.username && user.username.toLowerCase().includes(search.toLowerCase())) ||
//         // (user.lname && user.lname.toLowerCase().includes(search.toLowerCase())) ||
//         (user.email && user.email.toLowerCase().includes(search.toLowerCase())) ||
//         (user.role && user.role.toLowerCase().includes(search.toLowerCase()))
//     }));
//     setPagination(prev => ({ ...prev, currentPage: 1 }))
//   }, [search, allUsersList,])

//   useEffect(() => {
//     updateCurrentItems(sortedData, setCurrentItems);
//   }, [sortedData, pagination])

//   const updateCurrentItems = (users, _setCurrItems) => {
//     const indexOfLastItem = pagination.currentPage * pagination.itemsPerPage;
//     const indexOfFirstItem = indexOfLastItem - pagination.itemsPerPage;
//     _setCurrItems(users.slice(indexOfFirstItem, indexOfLastItem))
//   }

//   const classes = useStyles();
//   useEffect(() => {
//     fetchAllUsers();
//     setloading(false);
//   }, []);

//   return (
//     <Box boxShadow={1} padding={2}>
//       <SearchField
//         showPDFDownload={false}
//         label='Search'
//         search={search}
//         setSearch={setSearch}
//         placeholder='Name, ID, Role'
//       />
//       <Box>
//         <TableContainer component={Paper} sx={{ height: '50%', overflowY: 'auto' }}>
//           <Table aria-label="responsive table">
//             <TableHead sx={{
//               borderBottom: "2px solid lightslategray"
//             }}>
//               <TableRow>
//                 <TableCell className={classes.tableHeaderCell} align="left">
//                   Name
//                 </TableCell>
//                 <TableCell className={classes.tableHeaderCell} align="left">
//                   ID
//                 </TableCell>
//                 <TableCell className={classes.tableHeaderCell} align="left">
//                   Role
//                 </TableCell>
//                 <TableCell className={classes.tableHeaderCell} align="left">
//                   Email
//                 </TableCell>
//                 {/* <TableCell
//                   align="left"
//                 >
//                   Password Valid For
//                 </TableCell> */}
//                 {(currentUser?.role === "admin" && !isModelOpen) && (
//                   <TableCell className={classes.tableHeaderCell} align="center">
//                     Actions
//                   </TableCell>
//                 )}
//               </TableRow>
//             </TableHead>
//             <TableBody>
//               {loading &&
//                 Array.from({ length: 7 }).map((_, index) => (
//                   <TableRow
//                     key={index + "u_l_t" + "table_row_a"}
//                     className={classes.tableRow}
//                   >
//                     {columnCode?.map((column, colIndex) => (
//                       <TableCell key={colIndex} align="center">
//                         <Skeleton variant="text" />
//                       </TableCell>
//                     ))}
//                   </TableRow>
//                 ))}
//               {!loading && currentItems?.length > 0 ? (
//                 currentItems?.map((user, index) => (
//                   <AuthProvider key={index}>
//                     <CfrContextProvider>
//                       <UserItem
//                         isModelOpen={isModelOpen}
//                         fetchAllUsers={fetchAllUsers}
//                         key={uuidv4()}
//                         user={user}
//                         passwordToggel={passwordToggel}
//                       />
//                     </CfrContextProvider>
//                   </AuthProvider>
//                 ))
//               ) : !loading ? (
//                 <TableRow>
//                   <TableCell colSpan={7} align="center">
//                     <Typography
//                       variant="h5"
//                       color="textSecondary"
//                       fontWeight="bold"
//                     >
//                       No Data
//                     </Typography>
//                   </TableCell>
//                 </TableRow>
//               ) : null}
//             </TableBody>
//           </Table>
//         </TableContainer>
//       </Box>
//       <Box sx={{
//         px: 3,
//         py: 1,
//         border: '1px solid #e0e0e0',
//         display: 'flex',
//         justifyContent: 'center',
//       }}>
//         <Pagination totalRows={sortedData.length} pagination={pagination} setPagination={setPagination} />
//       </Box>
//     </Box>
//   );
// };

// UserListTable.propTypes = {
//   allUsersList: PropTypes.arrayOf(
//     PropTypes.shape({
//       _id: PropTypes.string.isRequired, // Assuming each row has an _id field
//       // Add other properties from row object if required
//     })
//   ).isRequired,
//   fetchAllUsers: PropTypes.func.isRequired,
//   loading: PropTypes.bool.isRequired,
//   setloading: PropTypes.func.isRequired,
//   isModelOpen: PropTypes.bool.isRequired
// };
// export default UserListTable;

import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  Box,
  TableRow,
  Typography,
  Skeleton,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import UserItem from "./UserItem copy";
import { makeStyles } from "@mui/styles";
import Pagination from "./UI_Components/pagination";
import SearchField from "./UI_Components/search-field";
import PropTypes from "prop-types";
import { ResetTvRounded } from "@mui/icons-material";

const useStyles = makeStyles(() => ({
  container: {
    all: "unset",
    maxHeight: "84vh",
  },
  tableHeaderCell: {
    all: "unset",
    // backgroundColor: "#f8f9fa !important",
    // color: "black  !important",
    fontWeight: "bold !important",
    fontSize: "0.9rem !important",
    minWidth: "6rem !important",
  },
  tableCell: { all: "unset" },
}));

const columnCode = ["fname", "username", "user_role", "email", "actions"];

const UserListTable = ({
  allUsersList,
  fetchAllUsers,
  loading,
  setloading,
  isModelOpen,
}) => {
  const [passwordToggel, setPasswordToggel] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    itemsPerPage: 6,
  });
  const [search, setSearch] = useState("");
  const [sortedData, setSortedData] = useState([]);
  const [currentItems, setCurrentItems] = useState([]);

  useEffect(() => {
    setSortedData(
      allUsersList.filter(
        (user) =>
          user.username?.toLowerCase().includes(search.toLowerCase()) ||
          user.email?.toLowerCase().includes(search.toLowerCase()) ||
          user.role?.toLowerCase().includes(search.toLowerCase()),
      ),
    );
    setPagination((prev) => ({ ...prev, currentPage: 1 }));
  }, [search, allUsersList]);

  useEffect(() => {
    const indexOfLastItem = pagination.currentPage * pagination.itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - pagination.itemsPerPage;
    setCurrentItems(sortedData.slice(indexOfFirstItem, indexOfLastItem));
  }, [sortedData, pagination]);

  const classes = useStyles();
  useEffect(() => {
    fetchAllUsers();
    setloading(false);
  }, []);

  return (
    <Box boxShadow={1} padding={2}>
      <SearchField
        showPDFDownload={false}
        label="Search"
        search={search}
        setSearch={setSearch}
        placeholder="Name, ID, Role"
      />
      <Box>
        <TableContainer
          component={Paper}
          sx={{ height: "50%", overflowY: "auto" }}
        >
          <Table aria-label="responsive table">
            <TableHead sx={{ borderBottom: "2px solid lightslategray" }}>
              <TableRow>
                <TableCell className={classes.tableHeaderCell} align="left">
                  Name
                </TableCell>
                <TableCell className={classes.tableHeaderCell} align="left">
                  ID
                </TableCell>
                <TableCell className={classes.tableHeaderCell} align="left">
                  Role
                </TableCell>
                <TableCell className={classes.tableHeaderCell} align="left">
                  Email
                </TableCell>
                <TableCell className={classes.tableHeaderCell} align="center">
                  Actions
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading &&
                Array.from({ length: 7 }).map((_, index) => (
                  <TableRow key={index}>
                    {columnCode.map((column, colIndex) => (
                      <TableCell key={colIndex} align="center">
                        <Skeleton variant="text" />
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              {!loading && currentItems.length > 0 ? (
                currentItems.map((user, index) => (
                  <UserItem
                    key={user._id || index}
                    isModelOpen={isModelOpen}
                    fetchAllUsers={fetchAllUsers}
                    user={user}
                    passwordToggel={passwordToggel}
                  />
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    <Typography
                      variant="h5"
                      color="textSecondary"
                      fontWeight="bold"
                    >
                      No Data
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
      <Box
        sx={{
          px: 3,
          py: 1,
          border: "1px solid #e0e0e0",
          display: "flex",
          justifyContent: "center",
        }}
      >
        <Pagination
          totalRows={sortedData.length}
          pagination={pagination}
          setPagination={setPagination}
        />
      </Box>
    </Box>
  );
};

UserListTable.propTypes = {
  allUsersList: PropTypes.arrayOf(
    PropTypes.shape({
      _id: PropTypes.string.isRequired,
    }),
  ).isRequired,
  fetchAllUsers: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
  setloading: PropTypes.func.isRequired,
  isModelOpen: PropTypes.bool.isRequired,
};

export default UserListTable;
