.ManualDataSection {
  width: 100%;
  // box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  color: #344767;

  .addBtn {
    // padding: 0.7rem 1.8rem;
    font-size: 0.8rem;
    font-weight: bold;
    // text-transform: uppercase;
    outline: none;
    margin: 0 5px;
    cursor: pointer;
    border-radius: 8px;
    border: none;
    // background-image: linear-gradient(
    // 	310deg,
    // 	rgb(33, 82, 255),
    // 	rgb(33, 212, 253)
    // );
    // color: #fff;
    // box-shadow: rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
    // 	rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
    // &:hover {
    // 	opacity: 0.85;
    // }
  }
  .ManualDataContainer {
    width: 100%;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    // margin: 0.5rem 0 1rem 0;
    .manualDataHeading {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 1.2rem;
        font-weight: 500;
      }
      .steps {
        font-size: 1.2rem;
        font-weight: 500;
      }
    }
  }

  .carouselContainer {
    width: 100%;
    min-height: 40vh; ////
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    // margin-top: 0.1rem;
    outline: none !important;
    .carousel {
      width: 100%;
      min-height: 100%;
      box-shadow:
        0px 2px 1px -1px rgba(0, 0, 0, 0.2),
        0px 1px 1px 0px rgba(0, 0, 0, 0.14),
        0px 1px 3px 0px rgba(0, 0, 0, 0.12);
      overflow: hidden !important;
      outline: none !important;
      .carouselInner {
        outline: none !important;
        .carouselInnerHeaderContainer {
          width: 100%;
          // height: fit-content;
          box-shadow:
            0px 2px 1px -1px rgba(0, 0, 0, 0.2),
            0px 1px 1px 0px rgba(0, 0, 0, 0.14),
            0px 1px 3px 0px rgba(0, 0, 0, 0.12);
          background-color: #fff;
          padding: 1.5rem 1.2rem;
        }
        .carouselInnerContent {
          // margin-top: 2rem;
          width: 100%;
          // height: 100%;
          min-height: 700px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;

          padding: 1.5rem 1.2rem;
          .imageContainer {
            width: 100%;
            height: 100% !important;
            display: flex;
            align-items: center;
            justify-content: center;
            // padding: 1.5rem 0;
            overflow: hidden !important;

            .imgContainer {
              width: 70%;
              height: 97%;
            }

            video {
              border-radius: 20px;
              width: 80%;
              height: 97%;
            }
          }
          .subBtnsContainer {
            width: 100%;
            margin-top: 2rem;
            .subStep {
              padding: 0.55rem 1.5rem;
              font-size: 0.8rem;
              font-weight: bold;
              text-transform: uppercase;
              outline: none;
              cursor: pointer;
              border: none;
              box-shadow:
                rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
                rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
              margin-left: 1rem;
              &:hover {
                opacity: 0.85;
              }
            }
          }
        }
      }
    }
  }
}
