import React from "react";
import { Box, Typography, Icon<PERSON>utton } from "@mui/material";
import { ArrowDownward, <PERSON>Up<PERSON> } from "@material-ui/icons";
import { useStateContext } from "../../context/ContextProvider";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";

// Default values as a constant
const DEFAULT_HEADER_TEXT = {
  name: "Name",
  creator: "Owner",
  created_at: "Created At",
  actions: "Actions",
};

const DEFAULT_SORT_DIRECTION = {
  name: null,
  created_at: null,
};

const FileFolderHeader = ({
  grid = false,
  headerText = {},
  sortDirection = {},
  dispatchSort = () => {},
}) => {
  const { currentMode } = useStateContext();
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  // Merge default and provided values
  const mergedHeaderText = { ...DEFAULT_HEADER_TEXT, ...headerText };
  const mergedSortDirection = { ...DEFAULT_SORT_DIRECTION, ...sortDirection };

  // Early return if grid is true
  if (grid) {
    return null;
  }

  // Determine background color based on mode
  const bgColor = currentMode === "Dark" ? "bg-gray-900" : "bg-gray-100";

  // Helper function to handle sort toggling
  const handleSort = (type) => {
    const currentDirection = mergedSortDirection[type];
    const newDirection = !currentDirection;
    dispatchSort({ type, sortDirection: newDirection });
  };

  return (
    <Box
      data-testid="file-folder-header"
      sx={{
        ...commonOuterContainerStyle,
        mx: 4,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        bgcolor: bgColor,
        borderRadius: "5px",
      }}
    >
      <Box
        sx={{
          marginLeft: "32px",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          gap: 2,
        }}
      >
        <Typography>{mergedHeaderText.name}</Typography>
        <IconButton
          onClick={() => handleSort("name")}
          aria-label="Sort by name"
        >
          {mergedSortDirection.name ? <ArrowUpward /> : <ArrowDownward />}
        </IconButton>
      </Box>

      {/* Owner Column */}
      <Box sx={{ marginLeft: "32px" }}>
        <Typography>{mergedHeaderText.creator}</Typography>
      </Box>

      {/* Created At Column */}
      <Box
        sx={{
          marginLeft: "32px",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          gap: 2,
        }}
      >
        <Typography>{mergedHeaderText.created_at}</Typography>
        <IconButton
          onClick={() => handleSort("created_at")}
          aria-label="Sort by creation date"
        >
          {mergedSortDirection.created_at ? <ArrowUpward /> : <ArrowDownward />}
        </IconButton>
      </Box>

      {/* Actions Column */}
      <Box sx={{ paddingRight: "32px" }}>
        <Typography>{mergedHeaderText.actions}</Typography>
      </Box>
    </Box>
  );
};

export default React.memo(FileFolderHeader);
