import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardHeader,
  CardContent,
  <PERSON><PERSON>,
  <PERSON>b,
  Con<PERSON>er,
  <PERSON><PERSON>,
  Divider,
} from "@mui/material";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "react-chartjs-2";
import DashboardGrid from "./DashboardGrid";
import DownTimeKPI from "./DowntimeKPI";
import MachineDataHeader from "../machineData/MachineDataHeader";
import MaintenanceTable from "../../components/oee/maintenance-table";
import DashboardTable from "./DashboardTable";
import DownTimeTable from "./DowntimeTable";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

const TimelineReports = ({ oeeData }) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState("manufacturing");
  const [refresh, setRefresh] = useState(1);

  const handleOpen = () => {
    setDialogOpen(true);
  };

  const handleClose = () => {
    setDialogOpen(false);
  };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };
  const printToPdf = () => {
    const inputElement = document.getElementById("print-content"); // Get the element to print

    if (!inputElement) {
      console.error("Print content not found.");
      return;
    }

    html2canvas(inputElement, {
      scale: 2, // Increase scale for better resolution
      useCORS: true, // Use CORS to handle cross-origin images
      logging: true, // Enable logging for debugging purposes
      scrollY: -window.scrollY, // Adjust for any current page scroll
      windowWidth: document.documentElement.offsetWidth, // Use the document's actual width
      windowHeight: document.documentElement.offsetHeight, // Use the document's actual height
    }).then((canvas) => {
      const imgData = canvas.toDataURL("image/jpeg", 1.0); // Get image data as JPEG
      const pdfWidth = 210; // A4 width in mm
      const pdfHeight = 295; // A4 height in mm
      const imgWidth = pdfWidth - 20; // Add padding by reducing the imgWidth
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      const margin = 10; // Margin of 10mm on each side
      let position = 0;

      const doc = new jsPDF("p", "mm", "a4"); // Initialize jsPDF

      // Check if the content needs to be split into multiple pages
      if (imgHeight > pdfHeight) {
        // Add content to the first page with padding
        doc.addImage(
          imgData,
          "JPEG",
          margin,
          position + margin,
          imgWidth,
          imgHeight,
        );
        heightLeft -= pdfHeight;

        // Handle content that spans multiple pages
        while (heightLeft >= 0) {
          position = heightLeft - imgHeight;
          doc.addPage();
          doc.addImage(
            imgData,
            "JPEG",
            margin,
            position + margin - pdfHeight,
            imgWidth,
            imgHeight,
          );
          heightLeft -= pdfHeight;
        }
      } else {
        // If the content fits on one page, just add it with padding
        doc.addImage(imgData, "JPEG", margin, margin, imgWidth, imgHeight);
      }

      // Save the PDF
      doc.save(`${oeeData?._id}.pdf`);
    });
  };

  if (!oeeData) {
    return (
      <>
        <MachineDataHeader />
        <Typography>No OEE Data Available</Typography>;
      </>
    );
  }

  // Destructure properties with null checks
  const {
    plannedProductionTime,
    stopTime,
    idealCycleTime,
    totalCount,
    runTime,
    goodCount,
    maintenanceRecords,
    operatorInfo,
    performanceTargets,
    changeoverTimes,
    qualityMetrics,
  } = oeeData || {};

  const refreshThem = () => {
    setRefresh(refresh + 1);
  };

  // Calculate Availability in percentage with null checks
  const availability =
    runTime && plannedProductionTime
      ? ((runTime - stopTime) / plannedProductionTime) * 100
      : 0;
  const availabilityFormatted = availability.toFixed(2);

  // Calculate Performance in percentage with null checks
  const performance =
    idealCycleTime && totalCount && runTime
      ? ((idealCycleTime * totalCount) / runTime) * 100
      : 0;
  const performanceFormatted = performance.toFixed(2);

  // Calculate Quality in percentage with null checks
  const quality = goodCount && totalCount ? (goodCount / totalCount) * 100 : 0;
  const qualityFormatted = quality.toFixed(2);

  // Calculate Overall OEE with null checks
  const overallOEE =
    availability && performance && quality
      ? (
          (availability / 100) *
          (performance / 100) *
          (quality / 100) *
          100
        ).toFixed(2)
      : 0;

  // Revised calculation functions
  const calculateAvailability = () => {
    if (!oeeData || !oeeData.plannedProductionTime || !oeeData.stopTime)
      return "0";
    const { plannedProductionTime, stopTime } = oeeData;
    const runTime = plannedProductionTime - stopTime;
    return ((runTime / plannedProductionTime) * 100).toFixed(2);
  };

  const calculatePerformance = () => {
    if (
      !oeeData ||
      !oeeData.idealCycleTime ||
      !oeeData.totalCount ||
      !oeeData.runTime
    )
      return "0";
    const { idealCycleTime, totalCount, runTime } = oeeData;
    const theoreticalMaxOutput = (runTime * 60) / idealCycleTime;
    return ((totalCount / theoreticalMaxOutput) * 100).toFixed(2);
  };

  const calculateQuality = () => {
    if (!oeeData || !oeeData.goodCount || !oeeData.totalCount) return "0";
    const { goodCount, totalCount } = oeeData;
    return ((goodCount / totalCount) * 100).toFixed(2);
  };

  const calculateOverallOEE = () => {
    const availability = parseFloat(calculateAvailability()) / 100;
    const performance = parseFloat(calculatePerformance()) / 100;
    const quality = parseFloat(calculateQuality()) / 100;
    return (availability * performance * quality * 100).toFixed(2);
  };
  // Data for OEE Overall Doughnut Chart
  const overallOeeData = {
    labels: ["Availability", "Performance", "Quality"],
    datasets: [
      {
        data: [
          calculateAvailability(),
          calculatePerformance(),
          calculateQuality(),
        ],
        backgroundColor: ["#4CAF50", "#FF5722", "#FFC107"],
      },
    ],
  };

  // Data for Availability Bar Chart
  const availabilityData = {
    labels: ["Planned Runtime", "Actual Runtime", "Planned Downtime"],
    datasets: [
      {
        label: "Availability",
        data: [plannedProductionTime || 0, runTime || 0, stopTime || 0],
        backgroundColor: ["#2196F3", "#4CAF50", "#FF5722"],
      },
    ],
  };

  // Data for Performance Bar Chart
  const performanceData = {
    labels: ["Planned Quantity", "Actual Quantity"],
    datasets: [
      {
        label: "Performance",
        data: [totalCount || 0, goodCount || 0],
        backgroundColor: ["#2196F3", "#4CAF50"],
      },
    ],
  };

  // Data for Quality Bar Chart
  const qualityData = {
    labels: ["Actual Quantity", "Rejected Quantity"],
    datasets: [
      {
        label: "Quality",
        data: [goodCount || 0, totalCount - goodCount || 0],
        backgroundColor: ["#4CAF50", "#FF5722"],
      },
    ],
  };

  return (
    <div>
      <Typography mt={5} variant="h6">
        (OEE) Machine Timeline Dashboard
      </Typography>
      <Container>
        <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
          <Tabs value={selectedTab} onChange={handleTabChange}>
            <Tab label="Manufacturing KPI" value="manufacturing" />
            <Tab label="Downtime KPI" value="downtime" />
            <Tab label="Print" value="print" />
          </Tabs>
        </Box>

        {selectedTab === "manufacturing" && (
          <>
            <DashboardGrid oeeData={oeeData} />
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Card elevation={0}>
                  <CardHeader title="Availability" />
                  <CardContent>
                    <Bar data={availabilityData} />
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card elevation={0}>
                  <CardHeader title="Performance" />
                  <CardContent>
                    <Bar data={performanceData} />
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card elevation={0}>
                  <CardHeader title="Quality" />
                  <CardContent>
                    <Bar data={qualityData} />
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card elevation={0}>
                  <CardHeader title="OEE Overall" />
                  <CardContent>
                    <Doughnut data={overallOeeData} />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* <Card elevation={0} sx={{ marginTop: 2 }}>
              <CardHeader title="Operator Information" />
              <CardContent>
                
                <Typography variant="body1">
                  Operator ID: {operatorInfo?.operatorId}
                </Typography>
                <Typography variant="body1">
                  Operator Name: {operatorInfo?.operatorName}
                </Typography>
              </CardContent>
            </Card> */}

            {/* <Card elevation={0} sx={{ marginTop: 2 }}>
              <CardHeader title="Performance Targets" />
              <CardContent>
            
                <Typography variant="body1">
                  Target Cycle Time: {performanceTargets?.targetCycleTime}
                </Typography>
                <Typography variant="body1">
                  Target Output: {performanceTargets?.targetOutput}
                </Typography>
                <Typography variant="body1">
                  Changeover Times: {changeoverTimes}
                </Typography>
              </CardContent>
            </Card> */}

            {/* <Card elevation={0} sx={{ marginTop: 2 }}>
              <CardHeader title="Quality Metrics" />
              <CardContent>
              
                <Typography variant="body1">
                  Quality Metrics: {qualityMetrics}
                </Typography>
              </CardContent>
            </Card> */}
          </>
        )}
        {selectedTab === "downtime" && (
          <>
            <DownTimeKPI
              type="reports"
              refreshThem={refreshThem}
              oeeData={oeeData}
            />
          </>
        )}
        {selectedTab === "print" && (
          <div>
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                marginBottom: "20px",
              }}
            >
              <Button
                onClick={printToPdf}
                variant="outlined"
                color="inherit"
                size="small"
              >
                Print
              </Button>
            </div>
            <div style={{ width: "1080px" }} id="print-content">
              <MaintenanceTable maintenanceRecords={maintenanceRecords} />
              <div>
                <br />
                <Divider />
                <br />
              </div>
              {oeeData && <DashboardTable oeeData={oeeData} />}
              <div>
                <br />
                <Divider />
                <br />
              </div>
              {oeeData && <DownTimeTable oeeData={oeeData} />}
              <div>
                <br />
                <Divider />
                <br />
              </div>
            </div>
          </div>
        )}
      </Container>
    </div>
  );
};

export default TimelineReports;
