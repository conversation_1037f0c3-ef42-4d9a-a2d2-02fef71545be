import {
  Box,
  Dialog,
  DialogContent,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import React, { useContext, useEffect, useState } from "react";
import {
  cmsInfra,
  companies,
  companyId_constant,
} from "../../../constants/data";
import { db } from "../../../firebase";
import { CmsInfraContext } from "../../../services/cms-infrastructure/cms-infra.context";
import { firebaseLooper } from "../../../tools/tool";
import CalibrationDataForm from "./calibration-data.form";
import BlindsClosedIcon from "@mui/icons-material/BlindsClosed";

const SapIntimationForm = () => {
  const { cmsData } = useContext(CmsInfraContext);
  const [cmsId, setCmsId] = useState("");
  const [masterData, setMasterData] = useState([]);

  return (
    <div>
      <Box sx={{ p: 1 }}>
        <FormControl variant="outlined" fullWidth>
          <InputLabel>Select CMS</InputLabel>
          <Select label="Select CMS" onChange={(e) => setCmsId(e.target.value)}>
            {cmsData.map((item, idx) => (
              <MenuItem key={idx + item?.id} value={item?.id}>
                {item?.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      <Box sx={{ border: "1px solid black", p: 1 }}>
        <Typography sx={{ fontSize: "12px" }} align="center">
          Intimation For Incorporation / Replacing / Obsoleting / Updating
          Instrument in SAP{" "}
        </Typography>
      </Box>

      {/*Master Equipment used starts */}

      {/* Table ends master equipment */}

      <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
        <Typography sx={{ fontSize: "12px" }}>
          Change Control / Incident report / Break down / PM order
          reference/Change request/QMS element:{" "}
        </Typography>
      </Box>

      {/* Result custom table */}
      <Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderTop: "none",
            alignItems: { lg: "normal", md: "center", sm: "center" },
            flexWrap: { lg: "nowrap", md: "wrap", sm: "wrap" },
          }}
        >
          {/* Sr no0 */}
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Instrument Code No{" "}
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Equipment Sort Field
            </Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Instrument Desc.& range{" "}
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Make
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Location
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Equipment/ Plant
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Frequency
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Least Count
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Unit of Measure
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Error Claimed
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Plan number
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Instrument discarded
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography align="center" sx={{ fontSize: "12px" }}>
              Remarks
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Calibration Form
            </Typography>
          </Box>
        </Box>
      </Box>
      {/* Data Mapping  */}
      {masterData?.map((item, idx) => (
        <IntimationItem item={item} />
      ))}
    </div>
  );
};

export default SapIntimationForm;

const IntimationItem = ({ item, eqItem }) => {
  const [open, setOpen] = useState(false);
  const { cmsData } = useContext(CmsInfraContext);

  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          border: "1px solid black",
          borderTop: "none",
        }}
      >
        {/* Sr no0 */}
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}> {item?.code_no} </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>{item?.sort_field}</Typography>
        </Box>

        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>{item?.desc_range} </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>{item?.make} </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>{item?.location}</Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>{item?.plant}</Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>{item?.frequency}</Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>{item?.least_count}</Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>{item?.unit}</Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>{item?.error}</Typography>
        </Box>

        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>{item?.plan_no}</Typography>
        </Box>

        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>
            {item?.inst_discarded}
          </Typography>
        </Box>

        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }}>{item?.remarks}</Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            <BlindsClosedIcon onClick={() => setOpen(true)} />
          </Typography>
        </Box>
      </Box>
      <Dialog
        maxWidth="xl"
        onClose={() => setOpen(false)}
        open={open}
        fullWidth
      >
        <DialogContent>
          <CalibrationDataForm
            masterEquip={cmsData?.filter(
              (eq) => item?.code_no == eq?.instrument_code,
            )}
            item={item}
          />
        </DialogContent>
      </Dialog>
    </Box>
  );
};
