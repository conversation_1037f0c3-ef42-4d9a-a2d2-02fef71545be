import React from "react";
import { OTPublisher } from "opentok-react";
import { Box, IconButton, Tooltip, Alert, Button } from "@mui/material";
import MicIcon from "@mui/icons-material/Mic";
import MicOffIcon from "@mui/icons-material/MicOff";
import VideocamIcon from "@mui/icons-material/Videocam";
import VideocamOffIcon from "@mui/icons-material/VideocamOff";
import ScreenShareIcon from "@mui/icons-material/ScreenShare";
import StopScreenShareIcon from "@mui/icons-material/StopScreenShare";
import RefreshIcon from "@mui/icons-material/Refresh";

class Publisher extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      error: null,
      audio: true,
      video: true,
      videoSource: "camera",
      publishScreen: false,
      retryCount: 0,
    };
  }

  componentDidMount() {
    // Check device permissions on mount
    this.checkDevicePermissions();
  }

  checkDevicePermissions = async () => {
    try {
      // Try to get user media to check permissions
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: true,
      });

      // If successful, stop all tracks
      stream.getTracks().forEach((track) => track.stop());
    } catch (err) {
      console.error("Permission check failed:", err);
      // Set a more user-friendly error message
      this.handleDeviceError(err);
    }
  };

  handleDeviceError = (err) => {
    let errorMessage = "Failed to access camera or microphone.";

    if (err.name === "NotReadableError") {
      errorMessage =
        "Camera or microphone is already in use by another application. Please close other applications that might be using your camera/mic.";
    } else if (
      err.name === "NotAllowedError" ||
      err.name === "PermissionDeniedError"
    ) {
      errorMessage =
        "Camera or microphone permission denied. Please allow access in your browser settings.";
    } else if (err.name === "NotFoundError") {
      errorMessage =
        "No camera or microphone found. Please connect a device and try again.";
    } else if (err.name === "OverconstrainedError") {
      errorMessage =
        "The requested camera/microphone settings are not supported by your device.";
    }

    this.setState({ error: errorMessage });
  };

  setAudio = (audio) => {
    this.setState({ audio });
  };

  setVideo = (video) => {
    this.setState({ video });
  };

  toggleScreenshare = () => {
    this.setState((state) => ({
      publishScreen: !state.publishScreen,
    }));
  };

  changeVideoSource = (videoSource) => {
    this.state.videoSource !== "camera"
      ? this.setState({ videoSource: "camera" })
      : this.setState({ videoSource: "screen" });
  };

  onError = (err) => {
    console.error("Publisher error:", err);
    this.handleDeviceError(err);
  };

  retryConnection = () => {
    this.setState((prevState) => ({
      error: null,
      retryCount: prevState.retryCount + 1,
    }));

    // Force a re-render of the publisher
    this.checkDevicePermissions();
  };

  openDeviceSettings = () => {
    // For Chrome
    if (navigator.userAgent.indexOf("Chrome") !== -1) {
      window.open("chrome://settings/content/camera");
    }
    // For Firefox
    else if (navigator.userAgent.indexOf("Firefox") !== -1) {
      window.open("about:preferences#privacy");
    }
    // For Safari and others, just show an alert
    else {
      alert(
        "Please open your browser settings and ensure camera and microphone permissions are granted for this site.",
      );
    }
  };

  render() {
    const { publishScreen, retryCount } = this.state;

    return (
      <Box className="publisher">
        {this.state.error ? (
          <Box className="errorMessageContainer">
            <Alert
              severity="error"
              action={
                <Button
                  color="inherit"
                  size="small"
                  startIcon={<RefreshIcon />}
                  onClick={this.retryConnection}
                >
                  Retry
                </Button>
              }
              sx={{ mb: 2 }}
            >
              {this.state.error}
            </Alert>
            <Button
              variant="outlined"
              color="primary"
              onClick={this.openDeviceSettings}
              sx={{ mb: 1 }}
            >
              Open Browser Settings
            </Button>
          </Box>
        ) : (
          <>
            <OTPublisher
              key={`publisher-${retryCount}`}
              properties={{
                publishAudio: this.state.audio,
                publishVideo: this.state.video,
                videoSource:
                  this.state.videoSource === "screen" ? "screen" : undefined,
                width: "100%",
                height: "100%",
                style: { buttonDisplayMode: "off" },
              }}
              onError={this.onError}
            />

            <Box className="controlsContainer">
              <Tooltip title={this.state.audio ? "Mute Audio" : "Unmute Audio"}>
                <IconButton
                  onClick={() => this.setAudio(!this.state.audio)}
                  className={`controlButton ${!this.state.audio ? "disabled" : ""}`}
                >
                  {this.state.audio ? <MicIcon /> : <MicOffIcon />}
                </IconButton>
              </Tooltip>

              <Tooltip
                title={this.state.video ? "Turn Off Camera" : "Turn On Camera"}
              >
                <IconButton
                  onClick={() => this.setVideo(!this.state.video)}
                  className={`controlButton ${!this.state.video ? "disabled" : ""}`}
                >
                  {this.state.video ? <VideocamIcon /> : <VideocamOffIcon />}
                </IconButton>
              </Tooltip>

              <Tooltip
                title={
                  this.state.videoSource === "camera"
                    ? "Share Screen"
                    : "Stop Sharing"
                }
              >
                <IconButton
                  onClick={this.changeVideoSource}
                  className={`controlButton ${this.state.videoSource !== "camera" ? "active" : ""}`}
                >
                  {this.state.videoSource === "camera" ? (
                    <ScreenShareIcon />
                  ) : (
                    <StopScreenShareIcon />
                  )}
                </IconButton>
              </Tooltip>
            </Box>
          </>
        )}

        {publishScreen && (
          <OTPublisher
            properties={{
              width: 800,
              height: 350,
              publishAudio: this.state.audio,
              publishVideo: this.state.video,
              videoSource: "screen",
              showControls: true,
            }}
          />
        )}
      </Box>
    );
  }
}
export default Publisher;
