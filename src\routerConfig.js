import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom"; // Use Routes for react-router-dom v6+
import RegistrationPortal from "./layouts/registration/Registration";
import AddUser from "./pages/addUser";
import TestingPage from "./pages/testing";
import Whiteboard from "./components/whiteboard/Whiteboard";
import CompanyIdPage from "./pages/companyIdPage";
import ForgotPasswordPage from "./pages/forgotPasswordPage";
import FATApproval from "./layouts/FATApproval/FATApproval";
import AdminLoginPage from "./pages/adminLoginPage";
import LoginPage from "./pages/loginPage";
import AdminForgotPasswordPage from "./pages/adminForgotPasswordPage";
import LoginMongoScreen from "./layouts/auth-mongo/login-mongo.screen";
import PrintReportsPage from "./pages/PrintReports";
import PrintFatSingle from "./layouts/machineData/printForMachineData/PrintFatSingle";
import PrintSatSingle from "./layouts/machineData/printForMachineData/PrintSatSingle";
import PrivateRoutesForLsiAdminMain from "./route/PrivateRoutesForLsiAdminMain";
import PrivateRouteMain from "./route/PrivateRouteMain";
import FlowChartType1 from "./components/Flow-Chart/Type1/index";
import ExistingFlowChart from "./components/Flow-Chart/Existing-Node/type-1";
import NewNode from "./components/New-Node/index";
import CreateNewNode from "./components/New-Node/create-node";
import AddUsers from "./layouts/FATApproval/AddUsers";

export const router = (
  <BrowserRouter>
    <Routes>
      <Route path="/user-invitation/:id" element={<RegistrationPortal />} />
      <Route
        path="/add-user/b59083f7-5871-4ab5-996b-020090486c53"
        element={<AddUser />}
      />
      <Route path="/testing" element={<TestingPage />} />
      <Route path="/whiteboard" element={<Whiteboard />} />
      <Route path="/company-id" element={<CompanyIdPage />} />
      <Route path="/forgot-password" element={<ForgotPasswordPage />} />
      <Route
        path="/fat-approval/:companyId/:fid/:approvalId/:eid"
        element={<FATApproval />}
      />
      <Route path="/admin-login" element={<AdminLoginPage />} />
      <Route path="/login" element={<LoginPage />} />
      <Route
        path="/admin-forgot-password"
        element={<AdminForgotPasswordPage />}
      />
      <Route path="/login-mongo" element={<LoginMongoScreen />} />
      <Route
        path="/fat-reports-print/:reportId"
        element={<PrintReportsPage />}
      />
      <Route path="/:mid/printFatSingle/:docId" element={<PrintFatSingle />} />
      <Route path="/:mid/printSatSingle/:docId" element={<PrintSatSingle />} />
      <Route path="/flow-chart" element={<FlowChartType1 />} />
      <Route path="/edit" element={<ExistingFlowChart />} />
      <Route path="/new-node" element={<NewNode />} />
      <Route path="/create-node" element={<CreateNewNode />} />
      <Route path="/:fid/:fatId/add-users-fat" element={<AddUsers />} />
      <Route path="*" element={<PrivateRoutesForLsiAdminMain />} />
      <Route path="/" element={<PrivateRouteMain />} />
    </Routes>
  </BrowserRouter>
);

export default router;
