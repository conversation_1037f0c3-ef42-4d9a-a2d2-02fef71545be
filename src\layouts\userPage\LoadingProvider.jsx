import PropTypes from "prop-types";
import React, { createContext, useState, useContext } from "react";

const LoadingContext = createContext();
export const LoadingContextNonFunctionalComp = {};

export const useLoading = () => useContext(LoadingContext);

export const LoadingProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);

  LoadingContextNonFunctionalComp.isLoading = isLoading;
  LoadingContextNonFunctionalComp.setIsLoading = setIsLoading;

  return (
    <LoadingContext.Provider value={[isLoading, setIsLoading]}>
      {children}
    </LoadingContext.Provider>
  );
};

LoadingProvider.propTypes = {
  children: PropTypes.node,
};
