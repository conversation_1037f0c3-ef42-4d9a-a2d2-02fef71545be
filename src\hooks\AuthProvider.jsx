import React from "react";
import { useContext, useState, useEffect } from "react";
import axios from "axios";
import { dbConfig } from "../infrastructure/db/db-config";
import { useNavigate } from "react-router";
import { toast } from "react-toastify";
import dayjs from "dayjs";
import moment from "moment";
import { makeStyles } from "@mui/styles";
import { Box, Button, Dialog, Typography } from "@mui/material";
import "react-toastify/dist/ReactToastify.css";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import OutlinedInput from "@mui/material/OutlinedInput";
import { DialogActions, FormControl } from "@mui/material";
// import { useLoading } from "./LoadingProvider";
import BigNumber from "bignumber.js";
import { useUtils } from "./UtilsProvider";
import userRoles from "../constants/UserRole";
import PropTypes from "prop-types";
import { MODULES_NAMES_TO_ROUTES } from "../layouts/env/modules-access/utils";

const defaultPassword = "default@123";
const AuthCurrentuserContext = React.createContext();
const AuthLoginContext = React.createContext();
const AuthLogoutContext = React.createContext();
const SidbarConext = React.createContext();
const LDAPDefaultPwdLockDays = 7;

export function useSidbarConext() {
  return useContext(SidbarConext);
}
export function useAuthCurrentuser() {
  return useContext(AuthCurrentuserContext);
}
export function useAuthlogin() {
  return useContext(AuthLoginContext);
}
export function useAuthLogout() {
  return useContext(AuthLogoutContext);
}
// function checkPasswordExpiry(pwdExpiresIn, pwdExpireWarn) {
//   console.log("Password Expiry Check:", { pwdExpiresIn, pwdExpireWarn });

//   if (pwdExpireWarn) {
//     window.alert(`Your password is going to expire in ${pwdExpiresIn} days`);
//     return `Your password is going to expire in ${pwdExpiresIn} days`;
//   }
//   return null;
// }

function checkPasswordExpiry(ldapTimestamp) {
  const LDAP_EPOCH_DIFFERENCE = 11644473600000;
  const LDAPDefaultPwdLockDays = 90; // Define this as per your policy

  // Convert LDAP time to Unix timestamp (milliseconds)
  const ldapInMilliseconds =
    Number(ldapTimestamp) / 10000 - LDAP_EPOCH_DIFFERENCE;
  const ldapDate = dayjs(ldapInMilliseconds);
  const today = dayjs();
  const differenceInDays = today.diff(ldapDate, "day");

  console.log(
    "DIFF:",
    differenceInDays,
    "LDAP:",
    ldapDate.format("DD/MM/YYYY"),
    "TODAY:",
    today.format("DD/MM/YYYY"),
  );
  if (differenceInDays > LDAPDefaultPwdLockDays) {
    window.alert(
      `Your password has expired. It was last changed on ${ldapDate.format("DD/MM/YYYY")}.`,
    );
    return null;
  } else if (
    differenceInDays > 0 &&
    differenceInDays <= LDAPDefaultPwdLockDays
  ) {
    const daysLeft = LDAPDefaultPwdLockDays - differenceInDays;
    window.alert(
      `Your password will expire in ${daysLeft} day(s), on ${ldapDate.add(LDAPDefaultPwdLockDays, "day").format("DD/MM/YYYY")}.`,
    );
    return null;
  } else {
    return null;
  }
}

const useStyles = makeStyles(() => ({
  passwordContainer: {
    backgroundColor: "#f5f5f5",
    color: "black",
    borderRadius: "10px",
    display: "flex",
    flexDirection: "column",
    position: "relative",
    alignContent: "center",
  },
  changePasswordTitle: {
    fontSize: "1.5rem",
  },
}));

const AuthContext = React.createContext();

export const useAuth = () => {
  return useContext(AuthContext);
};

export function AuthProvider({ children }) {
  // Constants
  const DEFAULT_PASSWORD = "default@123";
  const PASSWORD_MISMATCH_WARNING =
    "Your password doesnt match ! Please try again";
  const PASSWORD_CHANGE_SUCCESS =
    "Your password has been changed successfully!";
  const PASSWORD_VALIDATION_WARNING =
    "Password must be 8 characters long, contains a special character, and one letter.";
  const SHORT_PASSWORD_WARNING = "Short Password(min 8 char.)";
  const ALPHANUMERIC_WARNING = "At least 1 alphanumeric char";
  const SPECIAL_CHAR_WARNING = "At least 1 special char";
  const LOGIN_SUCCESS = "Login Successful";
  const LOGIN_FAILED = "Login Failed";
  const PASSWORD_EXPIRED_ALERT = "Your password has expired, please change it";
  const PASSWORD_EXPIRY_WARNING = "Your password is going to expire in";
  const ALREADY_LOGGED_IN_CONFIRMATION =
    "You are already logged in on another device/browser. By logging in here, you will be logged out of the other device/browser. Click OK to continue.";
  const ENV_SETTINGS_ERROR =
    "Something went wrong while getting environment settings";
  const PASSWORDS_DO_NOT_MATCH = "Passwords does not match";
  const SOMETHING_WENT_WRONG = "Something went wrong";

  // eslint-disable-next-line no-unused-vars
  // const [isLoading, setIsLoading] = useLoading();
  const { envData, isFetching } = useUtils();
  console.log("envData", envData, isFetching);
  // eslint-disable-next-line no-unused-vars
  const [loginItem, setLoginItem] = useState(
    sessionStorage.getItem("@user-creds"),
  );
  const [currentUser, setCurrentUser] = useState(""); // {email: "<EMAIL>" }
  const [loading, setLoading] = useState(true);
  // eslint-disable-next-line no-unused-vars
  const [currentUserId, setCurrentUserId] = useState(null);
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    try {
      const storedSidebarState = sessionStorage.getItem("sidebarOpen");
      return storedSidebarState !== null
        ? JSON.parse(storedSidebarState)
        : true; // Default to true if not set
    } catch (error) {
      console.error("Error parsing sidebarOpen from sessionStorage:", error);
      return true; // Fallback to true in case of error
    }
  });

  useEffect(() => {
    // Ensure sidebarOpen is always a valid boolean
    if (typeof sidebarOpen !== "boolean") {
      console.warn("Invalid sidebarOpen value detected. Resetting to true.");
      setSidebarOpen(true);
    }
  }, [sidebarOpen]);

  const navigate = useNavigate();
  const classes = useStyles();
  const [openChangePass, setOpenChangePass] = useState(false);
  const [passwordChange, setPasswordChange] = useState("");
  const [passwordissame, setPasswordisSame] = useState(false);
  const [isValid, setIsValid] = useState(false);

  const [confirmPass, setConfirmPass] = useState("");

  const update = async (data) => {
    const id = currentUserId;

    if (passwordChange !== confirmPass) {
      toast.warning(PASSWORD_MISMATCH_WARNING, {
        position: toast.POSITION.TOP_RIGHT,
      });
    } else {
      if (isValid === "") {
        const updateddate = moment();
        // setIsLoading(true);
        await axios
          .post(`${dbConfig.url}/users/${id}/change_password`, {
            new_password: data,
            current_password: DEFAULT_PASSWORD,
            password_created: moment(updateddate).valueOf(),
          })
          .then(() => {
            toast.success(PASSWORD_CHANGE_SUCCESS, {
              position: toast.POSITION.TOP_RIGHT,
            });
            setOpenChangePass(false);
          })
          .catch((err) => {
            toast.warning(err.response.data, {
              position: toast.POSITION.TOP_RIGHT,
            });
          })
          .finally(() => {
            // setIsLoading(false);
          });
      } else {
        toast.warning(PASSWORD_VALIDATION_WARNING, {
          position: toast.POSITION.TOP_RIGHT,
        });
        setPasswordChange("");
        setConfirmPass("");

        return;
      }
    }

    setConfirmPass("");
  };

  function validatePassword(password) {
    const alphanumericRegex = /^(?=.*[a-zA-Z])(?=.*\d).+$/;
    const specialCharRegex = /[!@#$%^&*(),.?":{}|<>]/;
    if (password?.length < 8) {
      return SHORT_PASSWORD_WARNING;
    } else if (!alphanumericRegex.test(password)) {
      return ALPHANUMERIC_WARNING;
    } else if (!specialCharRegex.test(password)) {
      return SPECIAL_CHAR_WARNING;
    } else {
      return "";
    }
  }
  const handlepasswordchange = (e) => {
    const isValidpaswword = validatePassword(e.target.value);
    setIsValid(isValidpaswword);
    setPasswordChange(e.target.value);
    setPasswordisSame(e.target.value === confirmPass);
  };
  const confirmPassword = (e) => {
    setConfirmPass(e.target.value);
    setPasswordisSame(e.target.value === passwordChange);
    //
  };

  const [showNewPassword, setShowNewPassword] = useState(false);

  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const login = async (email, password) => {
    // setIsLoading(true); // Start loading
    try {
      const checkIfAlreadyLoggedInRes = await axios.post(
        `${dbConfig.url}/auth/check`,
        {
          email: email,
          password: password,
        },
      );

      const checkIfAlreadyLoggedIn =
        checkIfAlreadyLoggedInRes.data.data.isLoggedIn;

      if (checkIfAlreadyLoggedIn) {
        const ok = window.confirm(ALREADY_LOGGED_IN_CONFIRMATION);

        if (!ok) {
          return;
        }
      }

      const res = await axios.post(`${dbConfig.url}/auth`, {
        email: email,
        password: password,
      });

      console.log("Login response:", res.data.data);

      // Store user data in session storage
      const ldapUserData = {
        email: res.data.data.user.email,
        fname: res.data.data.user.fname,
        lname: res.data.data.user.lname,
        role: res.data.data.user?.role,
        username: res.data.data.user.username,
        avatar: res.data.data.user.avatar,
        signature: res.data.data.user.signature,
        attempt: res.data.data.user.attempt,
        themecolor: res.data.data.user.themecolor,
        _id: res.data.data.user._id,
      };
      window.sessionStorage.setItem(
        "@user-token",
        JSON.stringify(res.data.data.token),
      );
      window.sessionStorage.setItem(
        "@user-creds",
        JSON.stringify(ldapUserData),
      );
      setLoginItem(window.sessionStorage.getItem("@user-creds"));

      toast.success(LOGIN_SUCCESS, {
        position: toast.POSITION.TOP_RIGHT,
      });

      if (res.data.data.ldapUserAccount) {
        const pwdLastSet = res.data.data.ldapUserAccount.pwdLastSet;
        const today = dayjs();
        window.sessionStorage.setItem(
          "@user-ldap-creds",
          JSON.stringify(res.data.data.ldapUserAccount),
        );

        const toast_message = checkPasswordExpiry(
          pwdLastSet,
          today.valueOf().toString(),
        );
        if (
          toast_message &&
          toast_message !== "Password has been updated today"
        ) {
          toast.success(toast_message, {
            position: toast.POSITION.TOP_RIGHT,
          });
        }
      }

      // if (res.data.data && res.data.data.ldapUserAccount) {
      //   const pwdLastSet = res.data.data.ldapUserAccount.pwdLastSet;
      //   // const pwdLastSet =  dayjs(*************);
      //   const today = dayjs();
      //   window.sessionStorage.setItem(
      //     "@user-ldap-creds",
      //     JSON.stringify(res.data.data.ldapUserAccount)
      //   );
      //   // const pwdLastSetDiff = today.diff(pwdLastSet, 'day');
      //   // console.log(pwdLastSetDiff, pwdLastSet, today.valueOf(), 'pwdLastSetDiff')

      //   const toast_message = checkPasswordExpiry(pwdLastSet, today.valueOf().toString())
      //   console.log("LDAP Comment: ", toast_message);
      //   if (toast_message && toast_message !== "Password has been updated today") {
      //     toast.success(toast_message, {
      //       position: toast.POSITION.TOP_RIGHT,
      //     });
      //   }
      //   // if (pwdLastSetDiff <= LDAPDefaultPwdLockDays) {
      //   //   toast.success(`Your password will expire in ${pwdLastSetDiff} days`, {
      //   //     position: toast.POSITION.TOP_RIGHT,
      //   //   });
      //   // }
      // }

      if (res.data.data && res.data.data.ldapUserAccount) {
        const { pwdExpiresIn, pwdExpireWarn } = res.data.data.ldapUserAccount;
        window.sessionStorage.setItem(
          "@user-ldap-creds",
          JSON.stringify(res.data.data.ldapUserAccount),
        );

        const toast_message = checkPasswordExpiry(pwdExpiresIn, pwdExpireWarn);
        if (toast_message) {
          toast.success(toast_message, {
            position: toast.POSITION.TOP_RIGHT,
          });
        }
      }
      // Redirect and refresh
      const envRes = await axios.get(`${dbConfig.url}/envdata`).catch((err) => {
        console.log("Requesting URL:", `${dbConfig.url}/envdata`);
        toast.error(err.response?.data?.message ?? ENV_SETTINGS_ERROR);
      });
      const envData = envRes.data;
      const ROLE = res.data.data.user.role;
      const RoutesValues = Object.values(MODULES_NAMES_TO_ROUTES)
        .filter((item) => item.length > 1)
        .map((item) => item.join(","));
      const RoutesChecker = {
        USERS_ACCESS: (envData.USERS_ACCESS ?? []).filter(
          (item) => !(RoutesValues.includes(item) || item.length === 0),
        ),
        PLANNERS_ACCESS: (envData.PLANNERS_ACCESS ?? []).filter(
          (item) => !(RoutesValues.includes(item) || item.length === 0),
        ),
        ADMINS_ACCESS: (envData.ADMINS_ACCESS ?? []).filter(
          (item) => !(RoutesValues.includes(item) || item.length === 0),
        ),
        PERFORMERS_ACCESS: (envData.PERFORMERS_ACCESS ?? []).filter(
          (item) => !(RoutesValues.includes(item) || item.length === 0),
        ),
        APPROVERS_ACCESS: (envData.APPROVERS_ACCESS ?? []).filter(
          (item) => !(RoutesValues.includes(item) || item.length === 0),
        ),
      };

      let redirectUrl = "/";
      if (ROLE === userRoles.admin) {
        redirectUrl = RoutesChecker.ADMINS_ACCESS[0] ?? "/";
      } else if (ROLE === userRoles.approver) {
        redirectUrl = RoutesChecker.APPROVERS_ACCESS[0] ?? "/";
      } else if (ROLE === userRoles.performer) {
        redirectUrl = RoutesChecker.PERFORMERS_ACCESS[0] ?? "/";
      } else if (ROLE === userRoles.planner) {
        redirectUrl = RoutesChecker.PLANNERS_ACCESS[0] ?? "/";
      } else if (ROLE === userRoles.user) {
        redirectUrl = RoutesChecker.USERS_ACCESS[0] ?? "/";
      }

      window.location.href = redirectUrl; // Redirect and refresh the browser
    } catch (err) {
      toast.error(
        `${(err.response && err.response.data.message) ?? LOGIN_FAILED + err.message}`,
      );
    } finally {
      // setIsLoading(false);
    }
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const logout = async () => {
    try {
      console.log("Calling logout");
      await axios
        .put(`${dbConfig.url}/auth/logout`, {
          email: currentUser.email, // Only send the email
        })
        .then(() => {
          console.log("Logout successful");
          // Clear session storage
          setCurrentUser("");
          sessionStorage.removeItem("@user-creds");
          sessionStorage.removeItem("@user-token");
          sessionStorage.removeItem("@user-ldap-creds");
          sessionStorage.removeItem("@block");
          console.log("Removed tokens, navigating to login");
          toast.success("Logout successful");
          navigate("/login-mongo");
        })
        .catch((err) => {
          console.error("Logout error:", err);
          toast.error(err.response?.data?.message ?? SOMETHING_WENT_WRONG);
        });
    } catch (err) {
      console.error("Logout error:", err);
      toast.error(err.response?.data?.message ?? SOMETHING_WENT_WRONG);
    }
  };

  useEffect(() => {
    setLoading(false);

    if (
      sessionStorage.getItem("@user-creds") &&
      sessionStorage.getItem("@user-token")
    ) {
      let currentUserTemp = sessionStorage.getItem("@user-creds");
      setCurrentUser(JSON.parse(currentUserTemp));
    } else {
      navigate("/login-mongo");
    }
  }, []);

  return (
    <AuthContext.Provider
      value={{ setCurrentUser, login, logout, currentUser }}
    >
      <AuthCurrentuserContext.Provider value={currentUser}>
        <AuthLoginContext.Provider value={login}>
          <AuthLogoutContext.Provider value={logout}>
            {!loading && children}
          </AuthLogoutContext.Provider>
        </AuthLoginContext.Provider>
      </AuthCurrentuserContext.Provider>
    </AuthContext.Provider>
    // {/* dialogue for change password */}
    // <Dialog width="xl" open={openChangePass}>
    //   <div style={{ borderRadius: "5px" }}>
    //     <div
    //       style={{
    //         background: "lightgrey",
    //         padding: "1rem",
    //         borderRadius: "5px 5px 0px 0px",
    //       }}
    //     >
    //       <Typography className={classes.changePasswordTitle} variant="h5">
    //         Change Password
    //       </Typography>
    //     </div>

    //     <div
    //       className={classes.passwordContainer}
    //       style={{
    //         display: "flex",
    //         flexDirection: "column",
    //         alignItems: "center",
    //       }}
    //     >
    //       {" "}
    //       <Box
    //         style={{
    //           display: "flex",
    //           flexDirection: "row",
    //           gap: "1rem",
    //           alignItems: "center",
    //           backgroundColor: "#f5f5f5",
    //           padding: "2rem",
    //         }}
    //       >
    //         <div
    //           style={{
    //             display: "flex",
    //             flexDirection: "column",
    //             alignItems: "flex-end",
    //             gap: "2.2rem",
    //             position: "",
    //           }}
    //         >
    //           <Typography>New Password</Typography>
    //           <Typography>Confirm Password</Typography>
    //         </div>
    //         <div
    //           style={{
    //             display: "flex",
    //             flexDirection: "column",
    //             gap: "1.5rem",
    //           }}
    //         >
    //           <FormControl sx={{ width: "100%" }} variant="outlined">
    //             <OutlinedInput
    //               placeholder="New Password"
    //               id="outlined-adornment-password"
    //               type={showNewPassword ? "text" : "password"}
    //               endAdornment={
    //                 <InputAdornment position="end">
    //                   <IconButton
    //                     aria-label="toggle password visibility"
    //                     onClick={() => setShowNewPassword(!showNewPassword)}
    //                     onMouseDown={handleMouseDownPassword}
    //                     edge="end"
    //                   >
    //                     {showNewPassword ? <VisibilityOff /> : <Visibility />}
    //                   </IconButton>
    //                 </InputAdornment>
    //               }
    //               onChange={(e) => handlepasswordchange(e)}
    //               size="small"
    //               className={classes.textfieldStyle}
    //               style={{ backgroundColor: "white", borderRadius: "5px" }}
    //               fullWidth
    //             />
    //           </FormControl>

    //           <FormControl sx={{ width: "100%" }} variant="outlined">
    //             <OutlinedInput
    //               placeholder="Confirm Password"
    //               id="outlined-adornment-password"
    //               type={showConfirmPassword ? "text" : "password"}
    //               endAdornment={
    //                 <InputAdornment position="end">
    //                   <IconButton
    //                     aria-label="toggle password visibility"
    //                     onClick={() =>
    //                       setShowConfirmPassword(!showConfirmPassword)
    //                     }
    //                     onMouseDown={handleMouseDownPassword}
    //                     edge="end"
    //                   >
    //                     {showConfirmPassword ? (
    //                       <VisibilityOff />
    //                     ) : (
    //                       <Visibility />
    //                     )}
    //                   </IconButton>
    //                 </InputAdornment>
    //               }
    //               onChange={(e) => confirmPassword(e)}
    //               size="small"
    //               error={!passwordissame && confirmPass?.length > 0}
    //               className={classes.textfieldStyle}
    //               style={{ backgroundColor: "white", borderRadius: "5px" }}
    //               fullWidth
    //               helperText={
    //                 !passwordissame && confirmPass?.length > 0
    //                   ? PASSWORDS_DO_NOT_MATCH
    //                   : ""
    //               }
    //             />
    //           </FormControl>
    //         </div>
    //       </Box>
    //     </div>
    //   </div>
    //   <DialogActions>
    //     <Button
    //       variant="contained"
    //       color="primary"
    //       disabled={confirmPass?.length <= 1 && passwordChange <= 1}
    //       onClick={() => update(passwordChange)}
    //       sx={{ marginBottom: "1rem" }}
    //     >
    //       Update Password
    //     </Button>
    //     <Button
    //       color="error"
    //       variant="contained"
    //       onClick={() => setOpenChangePass(false)}
    //       sx={{ marginBottom: "1rem" }}
    //     >
    //       Close
    //     </Button>
    //   </DialogActions>
    // </Dialog>
  );
}
AuthProvider.propTypes = {
  children: PropTypes.node,
};
