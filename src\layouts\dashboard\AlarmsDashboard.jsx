import React, { useEffect, useState } from "react";
import { companies, companyId_constant } from "../../constants/data";
import { useStateContext } from "../../context/ContextProvider";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import NotificationsIcon from "@mui/icons-material/Notifications";

const AlarmsDashboard = () => {
  const [alarms, setAlarms] = useState([]);
  const { currentMode, currentColorLight } = useStateContext();

  useEffect(() => {
    // db.collection(companies).doc(companyId_constant).collection('Alerts').onSnapshot(snap => {
    //   const data = firebaseLooper(snap)
    //   setAlarms(data)
    // })
  }, []);

  return (
    <div
      className="p-4"
      style={
        currentMode === "Dark"
          ? { backgroundColor: "#161C24", color: "white" }
          : { backgroundColor: currentColorLight }
      }
    >
      <div>
        <h3 className="text-xl mb-3">Alerts</h3>
      </div>
      {alarms.map((data) => (
        <div className="mb-2 p-3">
          <div>
            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <b>
                Name: <span style={{ color: "lightgreen" }}>{data.name}</span>
              </b>

              <div>
                <NotificationsIcon color="warning" />
              </div>
            </div>
          </div>
          <div>
            Expected Value : <span>{data.evalue}</span>
          </div>
          <div>
            Tolerance : <span>{data.tolerance}</span>
          </div>
          <div>
            Value : <span>{data.value}</span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default AlarmsDashboard;
