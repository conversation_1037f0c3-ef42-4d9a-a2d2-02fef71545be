import React from "react";
import {
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Box,
} from "@mui/material";

const ProcessCapability = ({ values, USL, LSL }) => {
  if (!values || values.length < 5) return null;

  // Calculate Mean and Standard Deviation
  const mean = values.reduce((a, b) => a + b, 0) / values.length;
  const stdDev = Math.sqrt(
    values.map((x) => Math.pow(x - mean, 2)).reduce((a, b) => a + b, 0) /
      values.length,
  );

  // Process Capability Index Calculation
  const Cp = (USL - LSL) / (6 * stdDev); // Capability
  const Cpk = Math.min(
    (USL - mean) / (3 * stdDev),
    (mean - LSL) / (3 * stdDev),
  ); // Performance

  // Determine progress color
  const getColor = (value) => {
    if (value < 1) return "error"; // Poor
    if (value < 1.33) return "warning"; // Marginal
    return "success"; // Good
  };

  return (
    <Card sx={{ width: "100%", mt: 4, p: 2 }}>
      <CardContent>
        <Typography variant="h6" align="center" gutterBottom>
          Process Capability Index (Cp, Cpk)
        </Typography>

        <Box sx={{ textAlign: "center", mb: 2 }}>
          <Typography variant="body1">Cp: {Cp.toFixed(2)}</Typography>
          <LinearProgress
            variant="determinate"
            value={Math.min(Cp * 100, 100)}
            color={getColor(Cp)}
            sx={{ height: 8, mt: 1 }}
          />
        </Box>

        <Box sx={{ textAlign: "center" }}>
          <Typography variant="body1">Cpk: {Cpk.toFixed(2)}</Typography>
          <LinearProgress
            variant="determinate"
            value={Math.min(Cpk * 100, 100)}
            color={getColor(Cpk)}
            sx={{ height: 8, mt: 1 }}
          />
        </Box>
      </CardContent>
    </Card>
  );
};

export default ProcessCapability;
