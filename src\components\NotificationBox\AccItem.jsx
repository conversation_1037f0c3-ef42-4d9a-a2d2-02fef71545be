import React, { useEffect, useState } from "react";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";

const AccItem = ({ callId }) => {
  // const dbRef = db
  //   .collection(companies)
  //   .doc(companyId_constant)
  //   .collection("CallLogData")
  //   .doc(`${callId}`);
  const [details, setDetails] = useState([]);
  useEffect(() => {
    // dbRef.get().then((snap) => {
    //   const data = snap.data();
    //   console.log(data);
    //   setDetails(data);
    // });
  }, []);
  return (
    <>
      <div>
        <span style={{ color: "lightgreen" }}>Manual : </span>
        {details?.manual_name} |{" "}
        {details?.time?.toDate().toString().substring(0, 15)}
      </div>
      <hr className="mt-2 mb-2" />
      <div>
        <span style={{ color: "lightgreen" }}>Step :</span> {details?.step} |{" "}
        {details?.manual}
      </div>
    </>
  );
};

export default AccItem;
