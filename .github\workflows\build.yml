name: Build

on:
  push:
    branches:
      - "**main**"
      - "**master**"
      - "**dev**"
      - "**develop**"
      - "**qa**"
      - "**staging**"
      - "**v[0-9]*"
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - "**main**"
      - "**master**"
      - "**dev**"
      - "**develop**"
      - "**qa**"
      - "**staging**"
      - "**v[0-9]*"

jobs:
  sonarqube:
    name: SonarQube
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: SonarQube Scan
        uses: SonarSource/sonarqube-scan-action@v5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
