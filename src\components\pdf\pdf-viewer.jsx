import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Worker } from "@react-pdf-viewer/core";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";
import { Box } from "@mui/material";
import { toast } from "react-toastify";
import PropTypes from "prop-types";

const PDF_CONTENT_TYPE = "application/pdf";
const PDF_WORKER_URL =
  "https://unpkg.com/pdfjs-dist@3.4.120/build/pdf.worker.min.js";

const base64toBlob = (data) => {
  // Cut the prefix `data:application/pdf;base64` from the raw base 64
  try {
    const base64WithoutPrefix = data.substr(
      `data:${PDF_CONTENT_TYPE};base64,`.length,
    );

    const bytes = atob(base64WithoutPrefix);
    let length = bytes.length;
    let out = new Uint8Array(length);

    while (length--) {
      out[length] = bytes.charCodeAt(length);
    }

    return new Blob([out], { type: PDF_CONTENT_TYPE });
  } catch (error) {
    // console.log(error)
    toast.error(error.message);
    return null;
  }
};

const PdfRawViewer = ({ pdfUrl }) => {
  const [url, setUrl] = useState(null);
  const defaultLayoutPluginInstance = defaultLayoutPlugin();

  useEffect(() => {
    try {
      if (pdfUrl && pdfUrl !== null) {
        const previewUrl = URL.createObjectURL(base64toBlob(pdfUrl));
        setUrl(previewUrl);
      }
    } catch (error) {
      setUrl(null);
    }
  }, [pdfUrl]);
  return (
    <Box
      sx={{
        width: "100%",
        height: "100vh",
        overflow: "hidden",
      }}
    >
      {url && url !== null && (
        <Worker workerUrl={PDF_WORKER_URL}>
          <Viewer fileUrl={url} plugins={[defaultLayoutPluginInstance]} />
        </Worker>
      )}
    </Box>
  );
};

PdfRawViewer.propTypes = {
  pdfUrl: PropTypes.string.isRequired,
};

export default PdfRawViewer;
