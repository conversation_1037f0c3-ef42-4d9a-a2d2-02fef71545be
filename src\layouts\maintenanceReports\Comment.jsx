import React from "react";
import {
  companies,
  companyId_constant,
  maintenanceReport,
} from "../../constants/data";
import { DeleteFilled, EditOutlined } from "@ant-design/icons";
import {
  Dialog,
  DialogContent,
  DialogActions,
  DialogTitle,
  Button,
  TextField,
} from "@mui/material";
import "./MaintenanceReport.css";
import { db } from "../../firebase";
import LoggingFunction from "../../components/CFR-Report/LoggingFunction";
import { useStateContext } from "../../context/ContextProvider";

export default function Comment(props) {
  const { userName, reportName, machineName } = props;
  const [open, setOpen] = React.useState(false);
  const [comment, setComment] = React.useState(props.comment);
  const { currentMode } = useStateContext();

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const commentUpdate = () => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(maintenanceReport)
    //     .doc(props.maintenanceReportId)
    //     .collection('stepData').doc(props.id).set({ 'comment': comment }, { merge: true })
    LoggingFunction(
      machineName,
      reportName,
      userName,
      "Maintenance Report",
      `${props.comment} of ${reportName} is updated`,
    );
    handleClose();
  };
  const commentDelete = () => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(maintenanceReport)
    //   .doc(props.maintenanceReportId)
    //   .collection("stepData")
    //   .doc(props.id)
    //   .set({ comment: null }, { merge: true });
    LoggingFunction(
      machineName,
      reportName,
      userName,
      "Maintenance Report",
      `${props.comment} is deleted from ${reportName}`,
    );
  };
  return (
    <>
      <div className="p-3">
        <div
          className="flex items-center justify-between mt-2 carouselInnerHeaderContainer"
          style={{
            backgroundColor: currentMode === "Dark" ? "#161C24" : "#fff",
            color: currentMode === "Dark" ? "#fff" : "#000",
          }}
        >
          <div className="left flex items-center">
            <div className="text-xl uppercase font-bold">Comment:</div>
            <div className="text-sm ml-4 capitalize">{props.comment}</div>
          </div>

          <div className="right flex items-center justify-center">
            <Button onClick={handleClickOpen}>
              <EditOutlined className="text-xl" />
            </Button>
            <div className="ml-2"></div>
            <Button onClick={commentDelete}>
              <DeleteFilled className="text-xl text-red-600" />
            </Button>
          </div>
        </div>
      </div>
      <Dialog open={open} onClose={handleClose} fullWidth="true" maxWidth="sm">
        <DialogTitle>Edit Comment</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Comment"
            type="text"
            fullWidth
            variant="standard"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button color="primary" variant="contained" onClick={commentUpdate}>
            Update
          </Button>
          <Button color="inherit" variant="contained" onClick={handleClose}>
            cancel
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
