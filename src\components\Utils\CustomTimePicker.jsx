import React, { useState, useCallback, useRef, useEffect } from "react";
import { useStateContext } from "../../context/ContextProvider";
import { Clock } from "lucide-react";
import styles from "./CustomTimePicker.module.css";

// Global state to track which picker is currently open
let activePickerId = null;

const generateOptions = (start, end) =>
  Array.from({ length: end - start + 1 }, (_, i) =>
    String(i + start).padStart(2, "0"),
  );

// Pre-generate options to avoid recreating them on each render
const hourOptions = generateOptions(1, 12);
const minuteOptions = generateOptions(0, 59);
const meridiemOptions = ["AM", "PM"];

const CustomTimePicker = ({ onTimeChange, initialValue }) => {
  const [hour, setHour] = useState("");
  const [minute, setMinute] = useState("");
  const [meridiem, setMeridiem] = useState("PM");
  const [showPicker, setShowPicker] = useState(false);
  const [finalTime, setFinalTime] = useState("");
  const [pickerId] = useState(
    () => `picker-${Math.random().toString(36).substr(2, 9)}`,
  );

  const containerRef = useRef(null);
  const pickerRef = useRef(null);
  const hourRef = useRef(null);
  const minuteRef = useRef(null);
  const meridiemRef = useRef(null);

  const { currentColor, currentMode } = useStateContext() || {
    currentColor: "#1976d2",
    currentMode: "Light",
  };

  // Toggle picker and manage global active picker state
  const togglePicker = useCallback(() => {
    if (showPicker) {
      setShowPicker(false);
      activePickerId = null;
    } else {
      // Close any other open picker
      if (activePickerId && activePickerId !== pickerId) {
        // Dispatch a custom event to close other pickers
        window.dispatchEvent(
          new CustomEvent("closePickers", { detail: { except: pickerId } }),
        );
      }
      setShowPicker(true);
      activePickerId = pickerId;
    }
  }, [showPicker, pickerId]);

  // Listen for close events from other pickers
  useEffect(() => {
    const handleCloseEvent = (e) => {
      if (e.detail.except !== pickerId && showPicker) {
        setShowPicker(false);
      }
    };

    window.addEventListener("closePickers", handleCloseEvent);
    return () => {
      window.removeEventListener("closePickers", handleCloseEvent);
    };
  }, [pickerId, showPicker]);

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target) &&
        showPicker
      ) {
        setShowPicker(false);
        activePickerId = null;
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showPicker]);

  const handleHourClick = useCallback((h) => {
    setHour(h);
  }, []);

  const handleMinuteClick = useCallback((m) => {
    setMinute(m);
  }, []);

  const handleMeridiemClick = useCallback((m) => {
    setMeridiem(m);
  }, []);

  useEffect(() => {
    if (initialValue) {
      try {
        // Handle the special case for "12:00 AM"
        const [time, period] = initialValue.split(" ");
        if (time && period) {
          const [initHour, initMinute] = time.split(":");

          // Ensure we have valid numbers before setting state
          if (!isNaN(parseInt(initHour)) && !isNaN(parseInt(initMinute))) {
            setHour(initHour);
            setMinute(initMinute);
            setMeridiem(period);
            setFinalTime(initialValue);
          } else {
            // Default values if parsing fails
            setHour("12");
            setMinute("00");
            setMeridiem("AM");
            setFinalTime("12:00 AM");
          }
        } else {
          // Default values if splitting fails
          setHour("12");
          setMinute("00");
          setMeridiem("AM");
          setFinalTime("12:00 AM");
        }
      } catch (error) {
        // Fallback to default values
        setHour("12");
        setMinute("00");
        setMeridiem("AM");
        setFinalTime("12:00 AM");
      }
    }
  }, [initialValue]);

  // Optimize scrolling by using requestAnimationFrame
  const scrollToSelected = useCallback(() => {
    requestAnimationFrame(() => {
      // Scroll hour into view
      if (hourRef.current) {
        const selectedHourEl = hourRef.current.querySelector(
          `.${styles.selected}`,
        );
        if (selectedHourEl) {
          selectedHourEl.scrollIntoView({ block: "center" });
        }
      }

      // Scroll minute into view
      if (minuteRef.current) {
        const selectedMinuteEl = minuteRef.current.querySelector(
          `.${styles.selected}`,
        );
        if (selectedMinuteEl) {
          selectedMinuteEl.scrollIntoView({ block: "center" });
        }
      }

      // Scroll meridiem into view
      if (meridiemRef.current) {
        const selectedMeridiemEl = meridiemRef.current.querySelector(
          `.${styles.selected}`,
        );
        if (selectedMeridiemEl) {
          selectedMeridiemEl.scrollIntoView({ block: "center" });
        }
      }
    });
  }, []);

  // Ensure the picker is visible in the viewport when opened
  const ensurePickerVisible = useCallback(() => {
    if (!pickerRef.current) return;

    requestAnimationFrame(() => {
      // Get picker position
      const pickerRect = pickerRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;

      // Check if picker bottom is outside viewport
      if (pickerRect.bottom > viewportHeight) {
        // Calculate how much we need to scroll to show the entire picker
        const scrollNeeded = pickerRect.bottom - viewportHeight + 20; // 20px extra padding

        // Scroll the window
        window.scrollBy({
          top: scrollNeeded,
          behavior: "auto", // Use 'auto' instead of 'smooth' for better performance
        });
      }
    });
  }, []);

  useEffect(() => {
    if (showPicker) {
      // Use requestAnimationFrame for smoother performance
      requestAnimationFrame(() => {
        ensurePickerVisible();
        // Use a short delay to ensure the DOM is fully rendered
        setTimeout(scrollToSelected, 10);
      });
    }
  }, [showPicker, ensurePickerVisible, scrollToSelected]);

  // Optimize scrolling when values change
  useEffect(() => {
    if (showPicker) {
      // Use requestAnimationFrame for smoother performance
      requestAnimationFrame(scrollToSelected);
    }
  }, [hour, minute, meridiem, showPicker, scrollToSelected]);

  const handleConfirm = useCallback(() => {
    if (hour && minute && meridiem) {
      // Special handling for 12 AM/PM
      const formattedHour = hour === "12" ? hour : hour.padStart(2, "0");
      const formattedMinute = minute.padStart(2, "0");
      const time = `${formattedHour}:${formattedMinute} ${meridiem}`;
      setFinalTime(time);
      setShowPicker(false);
      onTimeChange?.(time);
    }
  }, [hour, minute, meridiem, onTimeChange]);

  const reset = useCallback(() => {
    setShowPicker(false);
    setHour(initialValue ? hour : "");
    setMinute(initialValue ? minute : "");
    setMeridiem(initialValue ? meridiem : "PM");
    setFinalTime(initialValue || "");
  }, [initialValue, hour, minute, meridiem]);

  return (
    <div className={styles.container} ref={containerRef}>
      <button
        className={`${styles.inputButton} ${showPicker ? styles.active : ""}`}
        onClick={togglePicker}
        style={{
          backgroundColor: showPicker ? currentColor : "transparent",
          color: showPicker ? "#fff" : currentMode === "Dark" ? "#fff" : "#000",
          borderColor: showPicker ? currentColor : "gray",
        }}
      >
        <span
          className={finalTime ? styles.selectedText : styles.placeholderText}
        >
          {finalTime || "Enter time"}
        </span>
        <Clock size={21} className={styles.icon} />
      </button>

      {showPicker && (
        <div
          className={styles.pickerDropdown}
          style={{
            backgroundColor: currentMode === "Dark" ? "#333" : "#fff",
            borderColor: currentColor,
          }}
          ref={pickerRef}
        >
          <div className={styles.timePickerContainer}>
            {/* Hour Column */}
            <div className={styles.timePickerColumn}>
              <div className={styles.columnLabel}>Hour</div>
              <div className={styles.columnScroller} ref={hourRef}>
                {hourOptions.map((h) => (
                  <button
                    key={h}
                    className={`${styles.optionButton} ${hour === h ? styles.selected : ""}`}
                    style={{
                      backgroundColor:
                        hour === h ? currentColor : "transparent",
                      color:
                        hour === h
                          ? "white"
                          : currentMode === "Dark"
                            ? "#fff"
                            : "#000",
                    }}
                    onClick={() => handleHourClick(h)}
                  >
                    {h}
                  </button>
                ))}
              </div>
            </div>

            {/* Minute Column */}
            <div className={styles.timePickerColumn}>
              <div className={styles.columnLabel}>Minute</div>
              <div className={styles.columnScroller} ref={minuteRef}>
                {minuteOptions.map((m) => (
                  <button
                    key={m}
                    className={`${styles.optionButton} ${minute === m ? styles.selected : ""}`}
                    style={{
                      backgroundColor:
                        minute === m ? currentColor : "transparent",
                      color:
                        minute === m
                          ? "white"
                          : currentMode === "Dark"
                            ? "#fff"
                            : "#000",
                    }}
                    onClick={() => handleMinuteClick(m)}
                  >
                    {m}
                  </button>
                ))}
              </div>
            </div>

            {/* AM/PM Column */}
            <div className={styles.timePickerColumn}>
              <div className={styles.columnLabel}>AM/PM</div>
              <div className={styles.columnScroller} ref={meridiemRef}>
                {meridiemOptions.map((m) => (
                  <button
                    key={m}
                    className={`${styles.optionButton} ${meridiem === m ? styles.selected : ""}`}
                    style={{
                      backgroundColor:
                        meridiem === m ? currentColor : "transparent",
                      color:
                        meridiem === m
                          ? "white"
                          : currentMode === "Dark"
                            ? "#fff"
                            : "#000",
                    }}
                    onClick={() => handleMeridiemClick(m)}
                  >
                    {m}
                  </button>
                ))}
              </div>
            </div>
          </div>

          <div className={styles.footer}>
            <button
              className={styles.cancelButton}
              style={{ color: currentColor }}
              onClick={reset}
            >
              Cancel
            </button>
            <button
              className={styles.okButton}
              style={{ backgroundColor: currentColor }}
              onClick={handleConfirm}
            >
              OK
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomTimePicker;
