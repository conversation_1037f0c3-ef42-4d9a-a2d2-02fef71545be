import { Box, Button, CircularProgress } from "@mui/material";
import React from "react";
import PropTypes from "prop-types";

const MyButtonWithLoader = ({
  onClick,
  isLoading = false,
  disabled = false,
  forEdit = false,
  autoFocus = true,
  color = "success",
  variant = "contained",
  sx = { position: "relative" },
  children = "Submit",
  ...props
}) => {
  return (
    <Button
      variant={variant}
      color={color}
      onClick={onClick}
      autoFocus={autoFocus}
      disabled={disabled || isLoading}
      sx={sx}
      {...props}
    >
      {children}
      {isLoading && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <CircularProgress size="1.25rem" color="warning" />
        </Box>
      )}
    </Button>
  );
};

MyButtonWithLoader.propTypes = {
  onClick: PropTypes.func.isRequired, // Function to handle button click
  isLoading: PropTypes.bool, // Loading state to show loader
  disabled: PropTypes.bool, // Disable the button
  forEdit: PropTypes.bool, // Custom flag for edit state
  autoFocus: PropTypes.bool, // Auto-focus behavior
  color: PropTypes.oneOf([
    "primary",
    "secondary",
    "success",
    "error",
    "info",
    "warning",
  ]), // Button color
  variant: PropTypes.oneOf(["contained", "outlined", "text"]), // Button variant
  sx: PropTypes.object, // MUI styling object for button
  children: PropTypes.node, // Button content or label
};

export default MyButtonWithLoader;
