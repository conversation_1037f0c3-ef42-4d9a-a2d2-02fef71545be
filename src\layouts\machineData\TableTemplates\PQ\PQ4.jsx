import React, { useState, useMemo, useContext } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "../TableTempletes.css";

import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import TextField from "@mui/material/TextField";
import { useContentEditCount } from "../../../../services3/audits/ContentContext";

import { useStateContext } from "../../../../context/ContextProvider";
import { db, storage } from "../../../../firebase";
import { companies, companyId_constant } from "../../../../constants/data";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../../../components/buttons/Buttons";
import axios from "axios";
import { dbConfig } from "../../../../infrastructure/db/db-config";
import { useStorageTablesFile } from "../../../../utils/useStorageTablesFile";
import { InputLabel, LinearProgress, Link, Typography } from "@mui/material";
import { Empty } from "antd";
import { FileDownload, ImageSearchOutlined } from "@mui/icons-material";
import {
  FileImageFilled,
  FilePdfFilled,
  PictureFilled,
} from "@ant-design/icons";
import { DeleteByUrl } from "../../../../utils/StorageOptions";
import { DropzoneArea } from "material-ui-dropzone";
import { FileManagerSelectorContext } from "../../../../services/fileManager/file-manager-select.context";
import FileSelector from "../../../FileSelector/screens/FileSelector";
import PreviewIcon from "@mui/icons-material/Preview";
import { v4 as uuidv4 } from "uuid";

export default function PQ4({
  rowData,
  type,
  machineName,
  fatDataDocId,
  tableStaticData,
  useAt,
}) {
  const { currentMode, currentColorLight } = useStateContext();
  const [open, setOpen] = useState(false);
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");
  const [open2, setOpen2] = useState(false);

  //console.log("from PQ4 tableStaticData:",tableStaticData)

  const handleClickOpen2 = (data) => {
    //setRowDataSelected(data);
    setOpen2(true);
  };

  const handleClose2 = () => {
    setOpen2(false);
  };
  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useMemo(() => {
    // when theme changes then S.No array got reversed. So to stop that This Memonisation is used.
    setSortDirection("");
    //console.log("TableTemplates usememo")
  }, [currentMode]);

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#161c24" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            onClick={() => handleClickOpen2()}
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            <TableRow
              sx={{
                border: theme.borderDesign,
                background: theme.backgroundColor,
              }}
            >
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={3}>
                <span className="pr-1">S No.</span>
                {/* For serial number :-*/}
                {/* {useAt !== "tableMain" ? (
                            <>
                              {sortDirection === "down" || sortDirection === "" ? (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("up")}
                                >
                                  &#x21E9;{" "}
                                </span>
                              ) : (
                                <span
                                  className={theme.arrow}
                                  onClick={() => setSortDirection("down")}
                                >
                                  &#x21E7;{" "}
                                </span>
                              )}{" "}
                            </>
                          ) : null} */}
              </TableCell>
              <TableCell
                sx={{ border: theme.borderDesign }}
                // colSpan={6}
                rowSpan={2}
                align="center"
              >
                Parameters
              </TableCell>
              <TableCell
                sx={{ border: theme.borderDesign }}
                // colSpan={6}
                rowSpan={2}
                align="center"
              >
                Limits
              </TableCell>
              <TableCell
                sx={{ border: theme.borderDesign }}
                align="center"
                colSpan={3}
              >
                Observation, B. No. {tableStaticData?.observation_batch_no}
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell sx={{ border: theme.borderDesign }} align="center">
                At 40 RPM
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} align="center">
                At 45 RPM
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} align="center">
                At 50 RPM
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  key={uuidv4()}
                  sx={{
                    border: theme.borderDesign,
                    background: currentMode === "Dark" ? "#212b36" : "",
                  }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[2]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[3]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[4]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[5]}
                  </TableCell>
                </TableRow>
              ),
            )}
            {/* //footer section */}
            {/* <TableRow
                            onClick={() => handleClickOpen2()}
                            sx={{
                                border: theme.borderDesign,
                            }}
                        >
                            <TableCell sx={{ border: theme.borderDesign }} >
                                Pre compression force
                            </TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} colSpan={5} align="center">
                                {tableStaticData?.pre_compression_force}
                               
                            </TableCell>
                        </TableRow>

                        <TableRow
                            onClick={() => handleClickOpen2()}
                            sx={{
                                border: theme.borderDesign,
                            }}
                        >
                            <TableCell sx={{ border: theme.borderDesign }} >
                                Main compression force
                            </TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} colSpan={5} align="center">
                                {tableStaticData?.main_compression_force}
                                
                            </TableCell>
                        </TableRow>

                        <TableRow
                            onClick={() => handleClickOpen2()}
                            sx={{
                                border: theme.borderDesign,
                            }}
                        >
                            <TableCell sx={{ border: theme.borderDesign }} >
                                Feeder Speed
                            </TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} colSpan={5} align="center">
                                {tableStaticData?.feeder_speed}
                               
                            </TableCell>
                        </TableRow> */}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
          <EditTableRow
            rowDataSelected={rowDataSelected}
            type={type}
            fatDataDocId={fatDataDocId}
            machineName={machineName}
            handleClose={handleClose}
          />
        </DialogContent>
      </Dialog>

      <Dialog
        maxWidth="lg"
        open={open2}
        onClose={handleClose2}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle id="alert-dialog-title">{"Table Data"}</DialogTitle>
          <TableExtraInfo
            rowData={rowData}
            type={type}
            fatDataDocId={fatDataDocId}
            machineName={machineName}
            handleClose={handleClose2}
            tableStaticData={tableStaticData}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}

////////////////// edit row need to be changed as above

function EditTableRow({
  rowDataSelected,
  tableType,
  type,
  machineName,
  fatDataDocId,
  handleClose,
}) {
  const { currentMode } = useStateContext();
  const { contentEditCount, setContentEditCount } = useContentEditCount(); // for reRendering
  // console.log("pq3 rowDataSelected edit:",rowDataSelected);

  const [serial_no, setSerial_no] = useState(rowDataSelected[0]);
  const [parameter, setParameter] = useState(rowDataSelected[1]);
  const [limits, setLimits] = useState(rowDataSelected[2]);
  const [rpm40, setRpm40] = useState(rowDataSelected[3]);
  const [rpm45, setRpm45] = useState(rowDataSelected[4]);
  const [rpm50, setRpm50] = useState(rowDataSelected[5]);
  const [id, setId] = useState(rowDataSelected[6]); //

  const [urlIq1, setUrlIq1] = useState(rowDataSelected[6]); // not used for now
  const { fileUrl, changeFileUrl } = useContext(FileManagerSelectorContext);

  const [fileType, setFileType] = useState("");
  const [file, setFile] = useState(null);
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];
  const [fType, setFType] = useState("index" in rowDataSelected ? true : false);
  const [index, setIndex] = useState(rowDataSelected[9]);

  //
  const handleChangeImage = (file) => {
    let selectedFile = file[0];
    //console.log(file[0]) //(selectedFile?.size/1024))

    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        // delete last uploaded file via url if image changes
        if (url) {
          DeleteByUrl(url);
        }
        //
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };
  // const { currentColor, currentMode } = useStateContext();
  const { progress, url } = useStorageTablesFile(file);

  //
  const handleUpdateRow = async () => {
    if (fType && isNaN(parseInt(index))) {
      toastMessageWarning({ message: "The input of index should be a Number" });
      return;
    }
    let data = {
      serial_no,
      parameter,
      limits,
      rpm40,
      rpm45,
      rpm50,
      url: fileUrl === null ? urlIq1 : fileUrl,
    };

    await axios
      .put(`${dbConfig.url}/generalTable/${id}`, data)
      .then((res) => {
        setContentEditCount(contentEditCount + 1);
        toastMessageSuccess({ message: "Row updated Successfully" });
      })
      .catch((err) => {});
  };

  const handleCancel = () => {
    if (url) {
      DeleteByUrl(url);
    }
    handleClose();
  };

  const handleDeleteDropZone = (url) => {
    DeleteByUrl(url); // to delete the file from storage
    setFile(null); // to remove the preview
  };

  return (
    <>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          <div className="flex justify-between py-2">
            <div className="w-1/12">
              <TextField
                label="S No."
                id="outlined-size-small"
                //defaultValue="Na"
                size="small"
                value={serial_no}
                onChange={(e) => setSerial_no(e.target.value)}
              />
            </div>
            <div className="w-2/12">
              <TextField
                label="Parameter"
                id="outlined-size-small"
                //defaultValue="Na"
                size="small"
                value={parameter}
                onChange={(e) => setParameter(e.target.value)}
              />
            </div>

            <div className="w-2/12">
              <TextField
                label="Limits"
                id="outlined-size-small"
                //defaultValue="Na"
                size="small"
                value={limits}
                onChange={(e) => setLimits(e.target.value)}
              />
            </div>

            <div className="2/12">
              <TextField
                //title={min10}
                label="At 40 RPM"
                id="outlined-size-small"
                //defaultValue="Na"
                size="small"
                value={rpm40}
                onChange={(e) => setRpm40(e.target.value)}
              />
            </div>
            <div className="w-2/12">
              <TextField
                label="At 45 RPM"
                id="outlined-size-small"
                //defaultValue="Na"
                size="small"
                value={rpm45}
                onChange={(e) => setRpm45(e.target.value)}
              />
            </div>
            <div className="w-2/12">
              <TextField
                label="At 50 RPM"
                id="outlined-size-small"
                //defaultValue="Na"
                size="small"
                value={rpm50}
                onChange={(e) => setRpm50(e.target.value)}
              />
            </div>

            {/* {fType && (
                            <div className="w-2/12">
                                <TextField
                                    label="Index"
                                    id="outlined-size-small"
                                    defaultValue="Na"
                                    size="small"
                                    value={index}
                                    onChange={(e) => setIndex(e.target.value)}
                                />
                            </div>
                        )} */}
          </div>
        </DialogContentText>
        <FileSelector />
        <Typography sx={{ ml: "20px" }} align="left">
          {" "}
          <PreviewIcon /> Preview
        </Typography>
        <img width="250px" src={urlIq1} />
      </DialogContent>
      <DialogActions>
        <ButtonBasic buttonTitle="Update" onClick={handleUpdateRow} />
        <ButtonBasicCancel
          buttonTitle="Cancel &#x2716;"
          onClick={handleCancel}
        />
      </DialogActions>
    </>
  );
}

// Fo header and footer
function TableExtraInfo({
  rowData,
  type,
  fatDataDocId,
  machineName,
  handleClose,
  tableStaticData,
}) {
  const { contentEditCount, setContentEditCount } = useContentEditCount(); // for reRendering

  const [observation_batch_no, setObservation_batch_no] = useState(
    tableStaticData?.observation_batch_no,
  );
  // const [main_compression_force, setMain_compression_force] = useState(tableStaticData?.main_compression_force);
  // const [feeder_speed, setFeeder_speed] = useState(tableStaticData?.feeder_speed);

  const handleAddTableExtraInfo = async () => {
    //console.log("tableStaticData pq3:", tableStaticData);
    //console.log("rowDataaa:", rowData);
    const data = {
      mid: "",
      fat_id: fatDataDocId,
      table_type: "PQ4", //type,
      table_title: rowData?.length ? rowData[0][7] : "", //⭐ index will change // Check the function for index
      table_id: "", // remove this from backend also

      observation_batch_no,
    };

    if (tableStaticData?._id) {
      await axios
        .put(
          `${dbConfig.url}/generalTableStaticData/${tableStaticData?._id}`,
          data,
        )
        .then(() => {
          //   edittrainingcfr(data2);
          toastMessageSuccess({ message: `${type}has been updated` });
          handleClose();
          setContentEditCount(contentEditCount + 1);
        });
    } else {
      await axios
        .post(`${dbConfig.url}/generalTableStaticData`, data)
        .then(() => {
          //   edittrainingcfr(data2);
          toastMessageSuccess({ message: `${type}has been added` });
          handleClose();
          setContentEditCount(contentEditCount + 1);
          //setRefreshCount(refreshCount + 1);
        });
    }
  };
  return (
    <>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          <div className="flex justify-between py-2">
            <div className="w-12/12">
              <TextField
                label="Observation Batch No."
                id="outlined-size-small"
                size="small"
                value={observation_batch_no}
                onChange={(e) => setObservation_batch_no(e.target.value)}
              />
            </div>

            {/* <div className="w-3/12">
                            <TextField
                                label="Main Compression Force"
                                id="outlined-size-small"
                                size="small"
                                value={main_compression_force}
                                onChange={(e) => setMain_compression_force(e.target.value)}
                            />
                        </div>

                        <div className="w-3/12">
                            <TextField
                                label="Feeder Speed"
                                id="outlined-size-small"
                                size="small"
                                value={feeder_speed}
                                onChange={(e) => setFeeder_speed(e.target.value)}
                            />
                        </div> */}
          </div>
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <ButtonBasic
          buttonTitle="Add/Update"
          onClick={() => handleAddTableExtraInfo()}
        />
        <ButtonBasicCancel
          buttonTitle="Cancel &#x2716;"
          onClick={() => handleClose()}
        />
      </DialogActions>
    </>
  );
}
