import React, {useState, useEffect} from "react";
import {FormControl, InputLabel, MenuItem, Select} from "@mui/material";
import TextField from "@mui/material/TextField";
import {useAuth} from "../../hooks/AuthProvider";
import {toastMessage, toastMessageSuccess} from "../../tools/toast";
import {useStateContext} from "../../context/ContextProvider";
import {
  ButtonBasic,
  ButtonBasicCancel,
  SubmitButtons,
} from "../../components/buttons/Buttons";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider";
import {DatePicker} from "@mui/x-date-pickers/DatePicker";
import axios from "axios";
import {dbConfig} from "../../infrastructure/db/db-config";
import {useMongoRefresh} from "../../services/mongo-refresh.context";
import {useCreateMachineCfr} from "../../hooks/cfr/machineCfrProvider";
import moment from "moment";
import dayjs from "dayjs";
import {DropzoneArea} from "material-ui-dropzone";
import {convertBase64} from "../../hooks/useBase64";
import GetPreviewComponent from "../../components/commons/getPreview.component";

const AddMaintenance = ({
  mid,
  handleClose,
  machineName,
  handleSubmitSuccess,
  useAt,
}) => {
  const addmaintainancecfr = useCreateMachineCfr();
  const {currentUser} = useAuth();
  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");
  const [period, setPeriod] = useState("Daily");
  const [type, setType] = useState("");
  const {refreshCount, setRefreshCount} = useMongoRefresh();
  const [frequencies, setFrequencies] = useState([
    "Daily",
    "Every 1 Month",
    "Every 3 Months",
    "Every 6 Months",
    "Every 1 Year",
    "Every 2 Years",
    "Every 3 Years",
    "Every 5 Years",
  ]);
  const [last_done, setLastDone] = useState(dayjs());
  const [status, setStatus] = useState(false);
  const [dueDate, setDueDate] = useState(dayjs());
  const {currentMode} = useStateContext();

  const [imageUrl, setImageUrl] = useState("");
  const [selectedImageForStorage, setSelectedImageForStorage] = useState(null);

  const handleImageChange = async loadedFiles => {
    if (!loadedFiles || loadedFiles.length === 0) {
      setSelectedImageForStorage(null);
      setImageUrl("");
      return;
    }
    const selectedFile = loadedFiles[0];
    const base64 = await convertBase64(selectedFile);
    setSelectedImageForStorage(selectedFile);
    setImageUrl(base64);
  };

  function validateDate(date) {
    return date && dayjs(date).isValid();
  }

  const handleSubmit = async e => {
    e.preventDefault();

    if (!validateDate(last_done) || !validateDate(dueDate)) {
      toastMessage({message: "Invalid Date"});
      return;
    }

    let imageUploadRes = null;
    if (selectedImageForStorage) {
      let fd = new FormData();
      fd.append("image", selectedImageForStorage);
      try {
        imageUploadRes = await axios.post(
          `${dbConfig?.url_storage}/upload`,
          fd,
        );
      } catch (err) {
        console.log("Image upload error:", err);
        toastMessage({message: "Image upload failed"});
        return;
      }
    }

    const date = new Date();
    const data2 = {
      activity: "Maintenance added",
      dateTime: date,
      description: "a Maintenance is added",
      machine: mid,
      module: "Maintenance",
      username: currentUser.username,
    };

    const data = {
      title,
      desc: desc,
      cycle_time: period,
      created_at: new Date(),
      last_done: dayjs(last_done).toDate(),
      mid: mid,
      type: type,
      issue_id: "",
      status,
      due_date: dayjs(dueDate).toDate(),
      image_url: selectedImageForStorage ? imageUploadRes?.data?.data : "",
    };

    const alarmSopData = {
      title,
      desc,
      created_at: new Date(),
      last_done: dayjs(last_done).toDate(),
      status,
      image_url: selectedImageForStorage ? imageUploadRes?.data?.data : "",
      mid,
    };

    axios
      .post(
        `${dbConfig.url}/${useAt !== "AlarmSop" ? "maintenance" : "alarmSop"}`,
        useAt !== "AlarmSop" ? data : alarmSopData,
      )
      .then(response => {
        addmaintainancecfr(data2);
        toastMessageSuccess({message: `${data.title} Added Successfully`});
        if (useAt === "AlarmSop") {
          handleSubmitSuccess();
        }
        handleClose();
        setRefreshCount(refreshCount + 1);
      })
      .catch(err => {
        toastMessage({message: err.message});
      });
  };

  useEffect(() => {
    if (!validateDate(last_done)) return;

    const handleDueDate = days => {
      const due_date = dayjs(last_done).add(days, "day");
      setDueDate(due_date);
    };

    switch (period) {
      case frequencies[0]:
        handleDueDate(1);
        break;
      case frequencies[1]:
        handleDueDate(30);
        break;
      case frequencies[2]:
        handleDueDate(90);
        break;
      case frequencies[3]:
        handleDueDate(180);
        break;
      case frequencies[4]:
        handleDueDate(360);
        break;
      case frequencies[5]:
        handleDueDate(720);
        break;
      case frequencies[6]:
        handleDueDate(1080);
        break;
      case frequencies[7]:
        handleDueDate(1800);
        break;
      default:
        handleDueDate(1);
        break;
    }
  }, [period, last_done, frequencies]);

  return (
    <form
      onSubmit={handleSubmit}
      style={
        currentMode === "Dark"
          ? {backgroundColor: "#212B36", color: "white"}
          : {}
      }>
      <InputLabel className="mb-1">Title</InputLabel>
      <TextField
        onChange={e => setTitle(e.target.value)}
        onBlur={() => setTitle(title?.trim())}
        value={title}
        required
        variant="outlined"
        fullWidth
        style={{marginBottom: "12px"}}
      />

      <InputLabel className="mb-1">Description</InputLabel>
      <TextField
        onChange={e => setDesc(e.target.value)}
        onBlur={e => setDesc(desc?.trim())}
        value={desc}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{marginBottom: "12px"}}
      />

      {useAt !== "AlarmSop" && (
        <>
          {" "}
          <InputLabel className="mb-1">Select Type</InputLabel>
          <FormControl
            style={{marginBottom: "12px"}}
            required
            variant="outlined"
            fullWidth>
            <Select
              style={
                currentMode === "Dark"
                  ? {backgroundColor: "#212B36", color: "white"}
                  : {}
              }
              required
              value={type}
              onChange={e => setType(e.target.value)}>
              {useAt === "MaintenanceDataCalibration" && (
                <MenuItem value={0}>Calibration</MenuItem>
              )}
              {useAt === "LineClearance" && (
                <MenuItem value={5}>Line Clearance</MenuItem>
              )}
              {useAt === "GembaSteps" && <MenuItem value={4}>Gemba</MenuItem>}
              {useAt != "MaintenanceDataCalibration" &&
                useAt != "LineClearance" &&
                useAt != "GembaSteps" && (
                  <MenuItem value={1}>Machine Breakdown</MenuItem>
                )}
              {useAt != "MaintenanceDataCalibration" &&
                useAt != "LineClearance" &&
                useAt != "GembaSteps" && <MenuItem value={2}>Routine</MenuItem>}
              {useAt != "MaintenanceDataCalibration" &&
                useAt != "LineClearance" &&
                useAt != "GembaSteps" && (
                  <MenuItem value={3}>Preventive</MenuItem>
                )}
            </Select>
          </FormControl>
        </>
      )}

      {useAt !== "AlarmSop" && (
        <>
          {" "}
          <InputLabel
            className="mb-1"
            style={{display: type == 1 ? "none" : " "}}>
            Select Period
          </InputLabel>
          <FormControl
            style={{marginBottom: "12px", display: type == 1 ? "none" : " "}}
            required
            variant="outlined"
            fullWidth>
            <Select
              required
              value={period}
              onChange={e => setPeriod(e.target.value)}>
              {frequencies.map(title => (
                <MenuItem
                  value={title}
                  key={title}
                  style={
                    currentMode === "Dark"
                      ? {backgroundColor: "#212B36", color: "white"}
                      : {}
                  }>
                  {title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </>
      )}

      <InputLabel className="mb-1">Select Last Done</InputLabel>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          value={last_done}
          onChange={newValue => setLastDone(newValue)}
          renderInput={params => (
            <TextField
              fullWidth
              required
              style={{marginBottom: "12px"}}
              {...params}
            />
          )}
        />
      </LocalizationProvider>

      {useAt !== "AlarmSop" && (
        <>
          {" "}
          <InputLabel className="mb-1">Select Due Date</InputLabel>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              value={dueDate}
              onChange={newValue => setDueDate(newValue)}
              renderInput={params => (
                <TextField
                  fullWidth
                  required
                  style={{marginBottom: "12px"}}
                  {...params}
                />
              )}
            />
          </LocalizationProvider>
        </>
      )}

      <InputLabel className="mb-1">Select Status</InputLabel>
      <FormControl
        style={{marginBottom: "12px"}}
        required
        variant="outlined"
        fullWidth>
        <Select value={status} onChange={e => setStatus(e.target.value)}>
          <MenuItem value={true}>ON</MenuItem>
          <MenuItem value={false}>OFF</MenuItem>
        </Select>
      </FormControl>

      <InputLabel className="mb-1">Add Image</InputLabel>
      <DropzoneArea
        acceptedFiles={["image/*"]}
        showFileNames={true}
        onChange={loadedFiles => handleImageChange(loadedFiles)}
        dropzoneText="Drag and Drop / Click to ADD Image"
        showAlerts={false}
        filesLimit={1}
        maxFileSize={10 * 1024 * 1024}
        onDelete={() => handleImageChange([])}
      />
      {imageUrl && (
        <div
          className="my-2"
          style={{display: "flex", justifyContent: "center"}}>
          <GetPreviewComponent
            sourceUrl={imageUrl}
            fileFormat="image"
            previewImageStyle={{width: "450px"}}
          />
        </div>
      )}

      <div className="p-2 mt-2 flex justify-between">
        <ButtonBasicCancel
          buttonTitle="Cancel"
          type="button"
          onClick={handleClose}
        />
        <SubmitButtons buttonTitle="Submit" type="submit" />
      </div>
    </form>
  );
};

export default AddMaintenance;
