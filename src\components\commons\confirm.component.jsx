import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Select,
  InputLabel,
  MenuItem,
  FormControl,
  Typography,
  Box,
  TextField,
} from "@mui/material";

export const Confirm = ({
  dialogOpen = false,
  dialogTitle = "",
  dialogBodyContent = "",
  dialogConfirmContent = "",
  dialogCancelContent = "",
  useDropDownInDialog = false,
  useTextInputInDialog = false,
  textInputLabel = "",
  textInputStyle = {},
  textInputSize = "small",
  fileName = "",
  projectPlaceholder = "",
  handleTextBlur = () => {},
  handleTextChange = () => {},
  dropDownContainerStyle = {},
  dropDownSize = "small",
  dropDownLabel = "",
  handleClick = () => {},
  handleChange = () => {},
  handleBlur = () => {},
  handleConfirm = () => {},
  handleClose = () => {},
  dropdownData = [],
  dropDownDataValue = "",
  dropDownDataValueDefault = "",
  dropDownDataDisplayKey = "",
  dropDownDataValueKey = "",
}) => {
  return (
    <Dialog open={dialogOpen} fullWidth>
      <DialogTitle>{dialogTitle}</DialogTitle>
      <DialogContent>
        <Box>
          <Typography>{dialogBodyContent}</Typography>
          {!!textInputLabel && (
            <InputLabel id={`${textInputLabel?.toString()}-id-`}>
              {textInputLabel}
            </InputLabel>
          )}
          {useTextInputInDialog && (
            <TextField
              fullWidth
              variant="outlined"
              value={fileName || ""}
              size={textInputSize}
              sx={textInputStyle}
              placeholder={projectPlaceholder}
              onChange={handleTextChange}
              onBlur={handleTextBlur}
            />
          )}
          {useDropDownInDialog && (
            <FormControl
              style={dropDownContainerStyle}
              size={dropDownSize}
              variant={"outlined"}
            >
              {!!dropDownLabel && (
                <InputLabel id={`${dropDownLabel?.toString()}-id-`}>
                  {dropDownLabel}
                </InputLabel>
              )}
              <Select
                onClick={handleClick}
                onChange={handleChange}
                onBlur={handleBlur}
                labelId={`${dropDownLabel?.toString()}-id-`}
                label={dropDownLabel?.toString()}
                value={dropDownDataValue ?? ""}
                defaultValue={dropDownDataValueDefault}
                fullWidth
              >
                {dropdownData &&
                  Array.isArray(dropdownData) &&
                  dropdownData?.map((dropdownData, index) => (
                    <MenuItem
                      key={dropdownData?.id ?? dropdownData?._id ?? index}
                      value={dropdownData[dropDownDataValueKey]}
                    >
                      {dropdownData[dropDownDataDisplayKey]}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button variant="contained" color="error" onClick={handleClose}>
          {dialogCancelContent}
        </Button>
        <Button variant="contained" color="primary" onClick={handleConfirm}>
          {dialogConfirmContent}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
