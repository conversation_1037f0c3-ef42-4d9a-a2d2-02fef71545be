.contentViewPage {
  width: 100%;
  min-height: 90vh;
  text-align: left;
  .allContentPreviewContainer {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 100%;

    .contentPageHeading {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0.5rem 0;
      padding: 1.5rem 1.2rem;
      width: 100%;
      // background-color: #fff;
      box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      color: #344767;
      .contentHeading_left {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .contentHeadingInfoLeft {
          display: flex;
          flex-direction: column;
          // justify-content: flex-start;
          // align-items: center;
          .contentHeadingTitle {
            font-size: 18px;
            font-weight: 500;
          }
        }
      }
      .contentHeading_right {
        .contentHeadingInfoRight {
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          align-items: center;
          text-align: left;
        }
      }
    }
    .contentPageMain {
      margin: 1rem 0;
      padding: 1rem 1.2rem;
      width: 100%;
      // background-color: #fff;
      box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      color: #344767;

      .contentMainContainer {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        //
        .height90Vh {
          height: 90vh;
          width: 100%;
        }
        .content_sub-section {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          margin-bottom: 16px;
          width: 100%;
          .contentMainTitle {
            margin-bottom: 16px;
            font-size: 24px;
            font-weight: 700;
            width: fit-content;
            border-bottom: 1px solid #344767;
          }
          .contentMainSubTitle {
            margin-bottom: 8px;
            font-size: 18px;
            font-weight: 700;
          }
          .title {
            font-size: 14px;
            font-weight: 500;
            align-items: center;
          }
          .table {
            margin-bottom: 32px;
            .table_superHeader {
              width: 100%;
              text-align: center;
              font-size: 18px;
              font-weight: 600;
              padding: 16px 0;
              text-transform: uppercase;
              border-bottom: none !important;
            }
          }
        }
      }
    }
    .faBtn {
      position: fixed;
      bottom: 64px;
      right: 150px;

      background: #00274e;
      border-radius: 50%;
      z-index: 2;
      &:hover {
        background: #003972;
      }
    }
    .faBtnLeft {
      position: fixed;
      bottom: 64px;
      right: 64px;
      background: #00274e;
      border-radius: 50%;
      z-index: 2;
      &:hover {
        background: #003972;
      }
    }
    .faBtnRight {
      position: fixed;
      bottom: 64px;
      right: 236px;
      background: #00274e;
      border-radius: 50%;
      z-index: 2;
      &:hover {
        background: #003972;
      }
    }
    .fabDrawer {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      position: fixed;
      bottom: 64px;
      right: 195px;
      height: 56px;
      padding: 0px 25px 0px 8px;
      background: #eee;
      box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      button {
        height: 100%;
        padding: 0;
        i {
          font-size: 18px;
        }
      }
    }
  }
}
