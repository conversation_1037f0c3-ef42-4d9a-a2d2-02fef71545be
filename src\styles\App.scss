@import url("https://fonts.googleapis.com/css?family=Roboto&display=swap");

@import "~react-pro-sidebar/dist/scss/styles.scss";
body {
  margin: 0;
  height: 100vh;
  color: #353535;
  font-family: "<PERSON>o", sans-serif;
}

img {
  pointer-events: none;
  user-select: none;
}

#root {
  height: 100%;
}

.app {
  height: 100%;
  display: flex;
  position: relative;

  main {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
  }
}

.d-block {
  display: block;
}
.d-none {
  display: none;
}

.type-1 {
  background: #fff;
  padding: 10px;
  margin-bottom: 10px;
  padding-bottom: 20px;
  border-radius: 10px;
  p {
    margin-top: -10px;
  }
}

.node-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: left;
  background: #fff;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
}

.save-node {
  margin-top: 20px;
  margin-bottom: 20px;
}

.node-save {
  background: rgb(21, 185, 29);
  text-decoration: none;
  padding: 15px;
  padding-right: 25px;
  padding-left: 25px;
  color: white;
  border-radius: 5px;
}

// ----------------- Header -----------------

.top-nav {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  padding: 2rem 1.2rem;
  background: #fff;
  border-radius: 10px;
  margin-bottom: 1rem;
  box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
}

.tab-container {
  flex-basis: fit-content;
  display: flex;
  gap: 16px;
  padding: 0.25rem;
}

// _____ Tabs ________

.tab {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  color: #fff !important;
  text-decoration: none;
  padding: 10px 18px !important;
  border-radius: 8px;
  height: fit-content;
  width: fit-content;
  font-size: 13.5px;
  cursor: pointer;
  opacity: 1;
}

.tab-text {
  font-size: 13px;
  font-weight: bold;
  padding-left: 8px;
  inline-size: fit-content;
  white-space: nowrap;
}

// ___________________

// _____ Search Bar _____

.search-bar {
  flex-basis: 500px;
}

.search-bar input {
  background: white;
  padding: 7px 28px;
  font-size: 14px;
  border-radius: 8px;
  outline: none;
  box-shadow: 0px 4px 5px 0px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 0, 0, 0.1);
  width: calc(100% - 36px);
}
// ____________________

// _____ Add Element ________

.add-project,
.add-node,
.add-field {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  background: #fff;
  text-decoration: none;
  padding: 7px 24px;
  color: #ff7a00;
  border-radius: 8px;
  border: 1px solid #ff7a00;
  outline: none;
  text-decoration: none;
  cursor: pointer;
  height: fit-content;
  width: fit-content;
  box-sizing: border-box;
}

.add-project:hover,
.add-node:hover,
.add-field:hover {
  // background: #ff7a00 !important;
  color: #fff !important;
  background-image: linear-gradient(310deg, #ff7a00, #ffb470);
  box-shadow:
    rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
    rgba(0, 0, 0, 0.7) 0px 2px 4px -1px;
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

// _________________________

// -------------- End of Header --------------

// ---------- Edit Existing Project ----------

.dndflow {
  position: relative;
  display: flex;
  gap: 16px;
  height: 73.5vh;
}

.main-container {
  display: flex;
  flex-direction: column;
  header {
    margin-bottom: 36px;
  }
}

.header-right {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.export-pdf,
.export-json {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  padding: 7px 24px;
  border-radius: 8px;
  text-transform: uppercase;
  color: #000;
  cursor: pointer;
  outline: none;
  border: none;
}

.export-json:hover,
.export-pdf:hover {
  color: #fff;
  opacity: 0.85;
}

.export-pdf:hover {
  background-image: linear-gradient(310deg, #ff0000, #ff9999);
  box-shadow:
    rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
    rgba(0, 0, 0, 0.7) 0px 2px 4px -1px;
}

.export-json:hover {
  background-image: linear-gradient(310deg, #fbb034, #ffdd00);
  box-shadow:
    rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
    rgba(0, 0, 0, 0.7) 0px 2px 4px -1px;
}

// ____________ Aside ______________

.project-sidebar {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  width: 324px;
  background: #fff;
  text-align: center;
  box-shadow: rgba(0, 0, 0, 0.1) 0rem 1.25rem 1.6875rem 0rem;
  border-radius: 10px;
  overflow-y: auto;
}
.project-sidebar > div {
  width: 100%;
  padding: 16px 24px;
}

.description {
  font-size: 17px;
  font-weight: 500;
  opacity: 0.9;
  margin: 24px 0px;
  text-align: center;
}

.checkboxWrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 64px;
  margin-bottom: 20px;
  font-size: 15px;
  font-weight: 500;
}

.edge-input,
.node-input {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  label {
    font-size: 15px;
    font-weight: 500;
  }
  input,
  select {
    background: #dedede;
    border: none;
    outline: none;
    padding: 2px 8px;
    border-radius: 4px;
    width: 50%;
    font-size: 14px;
  }
}
.node-input-textarea {
  flex-wrap: wrap !important;
  textarea {
    background: #dedede;
    padding: 2px 8px;
    border-radius: 5px;
    border: none;
    outline: none;
    width: 100%;
  }
}

.node-position {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  margin-bottom: 20px;
  label {
    font-size: 15px;
    b {
      font-weight: 600;
    }
  }
}

.image-node {
  img {
    box-shadow: rgba(0, 0, 0, 0.4) 0rem 1.25rem 1.6875rem 0rem;
    height: 240px;
    margin-bottom: 36px;
  }
}

// ------- Toggle -------

.sidebar-content {
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
}

.sidebar-content div {
  width: calc(100% - 16px);
}

.toggle-btn {
  cursor: pointer;
  width: 40px !important;
  height: 40px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  padding: 0px;
}

.toggle-sidebar {
  position: absolute;
  padding: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0;
  right: 0;
  width: 41.5px !important;
  height: 41.5px !important;
  border: 1px solid #eee;
  z-index: 100;
  margin: 12px;
}

.node-sidebar.toggle-sidebar {
  left: 0 !important;
}

.toggle-sidebar div {
  padding: 0px !important;
  display: flex;
  justify-content: center;
  background-image: linear-gradient(310deg, #2152ff, #21d4fd);
  color: #fff;
  box-shadow:
    rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
    rgba(0, 0, 0, 0.7) 0px 2px 4px -1px;
}

.toggle-sidebar div:not(:first-of-type) {
  display: none;
}

// --- End of Toggle ----

// _________ End of Aside __________

// _________ Background _____________

.react-flow__controls-button {
  background: #fff !important;
}

// ______ End of Background _________

// ---------- End of Edit Existing Project ----------

// ----------------- Check Box ----------------------

.switch {
  position: relative;
  display: inline-block;
  width: 36px !important;
  height: 20px;
  margin: 0;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #2196f3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(16px);
  -ms-transform: translateX(16px);
  transform: translateX(16px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

// ---------------- End of Check Box ----------------

// ----------------- Project Main ----------------------

.project-dashboard {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  text-align: left;
  width: 100%;
  margin-bottom: 64px;
  padding: 2rem 1.5rem;
  border-radius: 12px;
  color: #344767;
  .title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    align-self: flex-start;
    font-size: 20px;
    font-weight: 500;
    opacity: 0.9;
    margin-bottom: 36px;
  }
}

.action-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  gap: 16px;
  font-size: 16px;
  .issue-machine {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    .select-machine {
      background: transparent;
      border: none;
      outline: none;
      padding: 8px;
      font-weight: bold;
      font-size: 14px;
      width: 200px;
      .option-machine {
        padding: 8px;
      }
    }
  }
}

.main-content {
  display: flex;
  align-items: center;
  justify-content: center !important;
  flex-flow: row wrap;
  width: 100%;
  padding-bottom: 24px;
  list-style-type: none;
  //smooth loading
  animation: fadeInAnimation ease 1s;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;

  @keyframes fadeInAnimation {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  .main-content-grid {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    border-radius: 16px !important;
    margin: 0.75rem;

    .project-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      gap: 16px;
      height: 100%;
      width: 100%;
      text-decoration: none;

      .content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        .title {
          text-transform: capitalize;
          text-decoration: none;
          font-size: 1.3rem;
          font-weight: 500;
          margin-bottom: 8px;
        }
        .project-date {
          font-size: 0.9rem;
          font-weight: 400;
          opacity: 0.85;
          letter-spacing: 1px;
        }
        .machine {
          font-size: 0.9rem;
          font-weight: 400;
          opacity: 0.85;
        }
        .user-info {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          .user-name {
            font-size: 0.9rem;
            font-weight: 400;
            opacity: 0.95;
            text-transform: capitalize;
          }
        }
      }
      .action-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-top: 1rem;
        .actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: nowrap;
          gap: 8px;
        }
        .project-delete {
          color: #f00;
          cursor: pointer;
          opacity: 0.7;
          font-size: 25px !important;
        }
        .project-delete:hover {
          opacity: 1;
        }
      }
    }
  }
}

// -------------- End of Project Main -------------------

// ----------------- Node Main ----------------------
.main-content-list {
  width: 100%;

  .tableContainer {
    // border-radius: 0.75rem !important;
    .insideTable {
      // border-radius: 0.75rem !important;
      min-width: 650 !important;
      width: 100% !important;
    }
  }
}
.delete-btn {
  color: red;
  background: #fff;
  padding: 0;
  span.tab-text {
    font-size: 13px;
    font-weight: 400;
    padding: 0;
  }
}

// -------------- End of Node Main -------------------

// ------------------- New Node Main ---------------------

.create-new-node {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  background: #fff;
  width: 100%;
  margin: 16px 0 36px 0;
  padding: 30px;
  box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  box-sizing: border-box;
}
.project-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  border-radius: 10px !important;
  margin-bottom: 24px;

  .project-details-heading {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    box-sizing: border-box;
    text-transform: capitalize;
    color: rgba(0, 0, 0, 0.85);
    text-align: left;
    h1 {
      font-size: 20px;
      font-weight: 800;
    }
    p {
      font-size: 14px !important;
      font-weight: 600;
      opacity: 0.6;
    }
  }
  .action-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    font-size: 16px;
  }
}

.create-node-content {
  width: 100%;
  margin-top: 12px;
}

// -------------- End of New Node Main -------------------

// -------------- Node Sidebar -------------------
.dndflow {
  .codeNodeMain {
    text-align: left !important;
    width: calc(100% - 8px);
    font-size: 13px;
    margin-top: 8px;
    background: #eee !important;
  }

  .reactflow-wrapper {
    flex-grow: 1;
    height: 100%;
    box-shadow: -4px 15px 40px -30px rgba(0, 0, 0, 0.5);
  }

  .code,
  .code2,
  .code3,
  .code4,
  .code5,
  .code6,
  .code8,
  .code10,
  .code11,
  .code12,
  .dndnode,
  .image,
  .custom,
  .custom-2,
  .only-image,
  .textfield2,
  .textfield3,
  .textarea2,
  .textarea3,
  .image3,
  .image2field1,
  .area2field1,
  .area2image1 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 24px;
    padding: 12px 8px;
    background: #fff;
    height: auto;
    font-weight: 500;
    font-size: 14px;
    border: 1px solid #2ac2ba;
    border-radius: 8px;
    cursor: grab !important;
    box-shadow: -4px 24px 24px -24px rgba(0, 0, 0, 0.75);
    min-width: 255px !important;

    p {
      text-align: center;
    }

    img {
      display: block;
      margin-top: 8px;
      width: calc(100% - 12px);
      height: 120px;
      border-radius: 8px;
    }

    textarea,
    .node_textarea {
      margin-top: 8px;
      background: #d3f3fa;
      width: calc(100% - 12px);
      border-radius: 8px;
      outline: none;
      padding: 12px;
      pointer-events: none;
      font-size: 13px;
      overflow: hidden;
    }
    &.circle {
      border-color: #0041d0;
      border-radius: 50%;
      height: 100px;
      width: 100px;
      margin-left: 60px;
      background: #fff;
    }

    &.square {
      height: 100px;
      width: 100px;
      background: #fff;
    }

    &.output {
      border-color: #ff0072 !important;
      box-shadow: -4px 24px 24px -18px rgba(0, 0, 0, 0.75);
    }

    &.custom {
      border-color: #ff0072;
    }
  }

  .image,
  .custom,
  .custom-2 {
    border: 1px solid #0779a7;
  }

  .only-image,
  .textfield2,
  .textfield3,
  .textarea2,
  .textarea3,
  .image3,
  .image2field1,
  .area2field1,
  .area2image1 {
    border: 1px solid #ff0072;
  }
  .dndnode {
    border: 1px solid #1a192b;
  }
  .textfield2 {
    border: 1px solid #1be7d6;
  }
  .textarea3,
  .image3,
  .image2field1,
  .area2field1,
  .area2image1 {
    border: 1px solid #2ac2ba;
  }
}
// ---------- End of Node Sidebar ----------------

// --------------------- Popup ---------------------------

.popup-content {
  max-width: 550px;
  border-radius: 8px;
  padding: 0px !important;

  .modal {
    padding: 16px 24px;
    padding-top: 0px;
    border-radius: 8px;

    .header {
      width: 100%;
      font-size: 20px;
      text-align: left;
      padding: 0px;
      box-sizing: border-box;
    }
    .content {
      width: 100%;

      .input-field-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        text-align: left;
        gap: 8px;
        width: 100%;
        margin-bottom: 12px;
        label {
          color: rgba(0, 0, 0, 0.54);
          font-size: 16px;
          font-weight: 400;
        }
      }

      .node-wrapper-field-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 16px;
        width: 100%;

        .node-wrapper-field {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 64px;
          width: 242px;
          text-align: center;
          border: 1px solid #aaa;
          border-radius: 7px;
          box-sizing: border-box;
          cursor: pointer;
          &:hover {
            box-shadow: 0 0 3px 1px #aaa;
          }
          span {
            padding-left: 16px;
          }
        }
      }
    }
    .actions {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      text-align: center;
      width: 100%;
      padding: 10px 5px;
      margin-top: 16px;
    }
  }
}

.none {
  display: none;
}

// ------------------ End of Popup -----------------------
.m1 {
  margin: 1rem;
}
