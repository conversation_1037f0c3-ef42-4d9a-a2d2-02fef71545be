import "./liveDataSection.scss";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import { useEffect, useState } from "react";
import { FormControl } from "@mui/material";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Line } from "react-chartjs-2";
import { db } from "../../../firebase";
import {
  companies,
  companyId_constant,
  liveEvents,
} from "../../../constants/data";
import { firebaseLooper } from "../../../tools/tool";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
);

export const options = {
  responsive: true,
  plugins: {
    title: {
      display: true,
      text: "Live Data",
    },
  },
};

const labels = ["January", "February", "March", "April", "May", "June", "July"];

export const data = {
  labels,
  datasets: [
    {
      label: "Dataset 2",
      data: ["23", "83", "54", "48", "19", "44", "89"],
      borderColor: "rgb(53, 162, 235)",
      backgroundColor: "rgba(53, 162, 235, 0.5)",
    },
  ],
};

const LiveDataSection = () => {
  const [chartvalue, setChartValue] = useState("");
  const [selectedEvents, setSelectedEvents] = useState([]);
  const [lineData, setLineData] = useState([]);
  const [mainData, setMainData] = useState([]);
  const [liveDataEvents, setLiveDataEvents] = useState([]);

  const companyIdd = !companyId_constant
    ? window.localStorage.companyId
    : companyId_constant;

  useEffect(() => {
    // db.collection(companies).doc(companyIdd).collection(liveEvents).onSnapshot(snap => {
    //   const data = firebaseLooper(snap)
    //   setLiveDataEvents(data)
    // })
  }, []);

  const handleChange = (event) => {
    setChartValue(event.target.value);
    let chartValue = event.target.value;
    // db.collection(companies).doc(companyId_constant).collection(liveEvents).doc(chartValue).onSnapshot(snap => {
    //   const data = snap.data();
    //   let labelPoints = []
    //   let dataPoints = []
    //   setSelectedEvents(data?.previous)
    //   // console.log(data.previous)

    //             for(let i in data?.previous){

    //               dataPoints.push(data?.previous[i].value)
    //               labelPoints.push(data?.previous[i].time)

    //             }
    //             // console.log(dataPoints)
    //             setLineData(dataPoints)
    //             setMainData(labelPoints)
    // })
  };

  return (
    <section className="liveDataSection">
      <div className="liveDataSectionHeading">
        <div className="info">
          <h3>Live Data</h3>
        </div>
        <div className="selector">
          <FormControl style={{ width: "200px" }}>
            <InputLabel id="dataChart">Select Data</InputLabel>
            <Select
              id="chart"
              labelId="dataChart"
              value={chartvalue}
              onChange={handleChange}
              style={{ marginBottom: ".4rem" }}
              label="Select Data"
            >
              {liveDataEvents?.map((data) => (
                <MenuItem value={data?.id}>{data?.id}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </div>
      </div>

      <div className="chartContainer">
        <div className="chart">
          <Line
            options={options}
            data={{
              labels: [...mainData],
              datasets: [
                {
                  label: "Live Data Points",
                  data: [...lineData],
                  borderColor: "rgb(53, 162, 235)",
                  backgroundColor: "rgba(53, 162, 235, 0.5)",
                },
              ],
            }}
          />
        </div>
      </div>
    </section>
  );
};

export default LiveDataSection;
