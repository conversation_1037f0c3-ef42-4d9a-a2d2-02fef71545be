import React, { useEffect, useState } from "react";
import { Box, Button, TextField, Tooltip } from "@mui/material";
const ServerConfigDialog = () => {
  const envIP = process.env.REACT_APP_IP ?? "localhost";
  const [ip, setIp] = useState(envIP);

  useEffect(() => {
    var ipTemp = window.localStorage.getItem("server_ip");
    setIp(ipTemp);
  }, []);
  const handleIp = () => {
    window.localStorage.setItem("server_ip", ip);
    window.location.reload();
  };
  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "spaceAround",
          justifyItem: "center",
        }}
      >
        <Tooltip title=" Eg: 127.0.0.1">
          <TextField
            size="small"
            value={ip}
            onChange={(e) => setIp(e.target.value)}
            label="Ip"
          ></TextField>
        </Tooltip>
        &nbsp;
        <Button size="small" variant="contained" onClick={handleIp}>
          Set
        </Button>
      </Box>
    </>
  );
};

export default ServerConfigDialog;
