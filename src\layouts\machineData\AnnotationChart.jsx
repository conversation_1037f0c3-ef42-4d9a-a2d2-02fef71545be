import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { Line } from "react-chartjs-2";
import { Chart as ChartJS, registerables } from "chart.js";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import Box from "@mui/material/Box";
import Skeleton from "@mui/material/Skeleton";

export default function AnnotationChart({
  processValues,
  activeChartFromSelected,
}) {
  ChartJS.register(...registerables);
  const [chartData, setChartData] = useState([]);
  const [time, setTime] = useState([]);
  const [value, setValue] = useState("");

  const [activeChart, setActiveChart] = useState("");
  const [chartState, setChartState] = useState("");
  const [updateChartSelectorValue, setUpdateChartSelectorValue] = useState("");
  const [lastUpdaterSelectorValue, setLastUpdateSelectorValue] = useState("");

  useEffect(() => {
    if (activeChartFromSelected != "") {
      showChart(activeChartFromSelected);
      setActiveChart(activeChartFromSelected);
      // console.log("activeChartFromSelected anotationChart:", activeChartFromSelected)
    }
  }, []);

  const showChart = (chartTitle) => {
    if (chartTitle != "") {
      //console.log("chart")
      // db.collection(companies)
      //   .doc(companyId_constant)
      //   .collection("liveData2")
      //   .doc(chartTitle)
      //   .onSnapshot((snap) => {
      //     const data = snap.data();
      //     let previous = [];
      //     let time = [];
      //     for (let index = 0; index < data.previous.length; index++) {
      //       const element = data.previous[index];
      //       time.push(element.time?.slice(0, 16));
      //       previous.push(parseFloat(element.value).toFixed(2));
      //     }
      //     // console.log('anotation:',time)
      //     //console.log('anotation value:',previous)
      //     setChartData(previous);
      //     setTime(time);
      //     let stateValue = stateSetter({ time, previous });
      //     setChartState(stateValue);
      //   });
    }
  };

  const stateSetter = ({ time, previous }) => {
    return {
      labels: time,
      datasets: [
        {
          label: "Value",
          fill: false,
          lineTension: 0.5,
          backgroundColor: "rgba(75,192,192,1)",
          borderColor: "rgba(0,0,0,1)",
          borderWidth: 2,
          data: previous,
        },
      ],
    };
  };

  const updateChart = (e) => {
    setUpdateChartSelectorValue(e.target.value);

    // db.collection('mqtt').doc(activeChart).onSnapshot((snap) => {
    //   const data = snap.data()
    //   let newprevious = []
    //   let newtime = []
    //   for (let index = 0; index < data.previous.length; index++) {
    //     const element = data.previous[index];
    //     newtime.push(element.time?.slice(0, 16))
    //     newprevious.push(element.value)

    //   }

    //   let previous = newprevious.slice(0, e.target.value); // for parameter
    //   let time = newtime.slice(0, e.target.value);         // for parameter
    //   //console.log(newPreviousValuesForUpdate)
    //   setChartData(previous);
    //   setTime(time);
    //   let stateValue = stateSetter({ time, previous });
    //   setChartState(stateValue);
    // })
    e.preventDefault();
  };
  const lastUpdateChart = (e) => {
    setLastUpdateSelectorValue(e.target.value);

    // db.collection("mqtt")
    //   .doc(activeChart)
    //   .onSnapshot((snap) => {
    //     const data = snap.data();
    //     let newprevious = [];
    //     let newtime = [];
    //     for (let index = 0; index < data.previous.length; index++) {
    //       const element = data.previous[index];
    //       newtime.push(element.time?.slice(0, 16));
    //       newprevious.push(element.value);
    //     }

    //     let previous = newprevious.slice(e.target.value, newprevious.length); // for parameter
    //     let time = newtime.slice(e.target.value, newtime.length); // for parameter

    //     setChartData(previous);
    //     setTime(time);
    //     let stateValue = stateSetter({ time, previous });
    //     setChartState(stateValue);
    //   });
    e.preventDefault();
  };

  const reset = () => {
    if (activeChart != "") {
      showChart(activeChart);
      setUpdateChartSelectorValue(null);
      setLastUpdateSelectorValue(null);

      // db.collection('mqtt').doc(activeChart).onSnapshot((snap) => {
      //   const data = snap.data()
      //   let newprevious = []
      //   let newtime = []
      //   for (let index = 0; index < data.previous.length; index++) {
      //     const element = data.previous[index];
      //     newtime.push(element.time?.slice(0, 16))
      //     newprevious.push(element.value)

      //   }

      //   let previous = newprevious; // for parameter
      //   let time = newtime;         // for parameter

      //   setChartData(previous);
      //   setTime(time);
      //   let stateValue = stateSetter({ time, previous });
      //   setChartState(stateValue);

      // })
    }
  };

  const onChangeTitle = (title) => {
    setActiveChart(title);
    showChart(title);
  };
  return (
    <div>
      {/* {processValues?.map((data) =>
      <div className="font-bold text-xl text-green-500  underline"> */}

      {/* <span onClick={() => onClickTitle(data.id) }>
          {data.id}
        </span> */}
      <div className="pb-2">
        <FormControl size="small" variant="outlined" fullWidth>
          <InputLabel>Select Chart</InputLabel>
          <Select
            label="Sort By (First)"
            value={activeChart}
            onChange={(e) => onChangeTitle(e.target.value)}
          >
            {processValues?.map((data, index) => (
              <MenuItem key={index} value={data.id}>
                {data.id}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>
      {/* </div>
       )} */}

      {activeChart && chartState != "" && (
        <div>
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <FormControl
              size="small"
              variant="outlined"
              style={{ marginRight: "20px" }}
              fullWidth
            >
              <InputLabel>Sort By (First)</InputLabel>
              <Select
                value={updateChartSelectorValue}
                label="Sort By (First)"
                onChange={(e) => updateChart(e)}
              >
                <MenuItem value={20}>First 20 values</MenuItem>
                <MenuItem value={40}>First 40 values</MenuItem>
                <MenuItem value={60}>First 60 values</MenuItem>
                <MenuItem value={80}>First 80 values</MenuItem>
              </Select>
            </FormControl>
            <FormControl
              size="small"
              variant="outlined"
              style={{ marginRight: "20px" }}
              fullWidth
            >
              <InputLabel>Sort By (After)</InputLabel>
              <Select
                value={lastUpdaterSelectorValue}
                label="Sort By (After)"
                onChange={(e) => lastUpdateChart(e)}
              >
                <MenuItem value={20}>After 20 values</MenuItem>
                <MenuItem value={40}>After 40 values</MenuItem>
                <MenuItem value={60}>After 60 values</MenuItem>
                <MenuItem value={80}>After 80 values</MenuItem>
              </Select>
            </FormControl>
            <Button onClick={reset}>Reset</Button>
          </div>
          <div className="h-1/3  flex content-center justify-center self-center">
            <div className="w-4/5">
              <Line
                data={chartState}
                options={{
                  title: {
                    display: true,
                    text: activeChart,
                    fontSize: 5,
                  },
                  legend: {
                    display: true,
                    position: "right",
                  },
                }}
              />
            </div>
          </div>
        </div>
      )}

      {!(activeChart && chartState != "") && (
        <div className="flex justify-center content-center mb-1">
          <Box sx={{ width: 300 }}>
            <Skeleton />
            <Skeleton animation="wave" />
            <Skeleton animation={false} />
          </Box>
        </div>
      )}
    </div>
  );
}
