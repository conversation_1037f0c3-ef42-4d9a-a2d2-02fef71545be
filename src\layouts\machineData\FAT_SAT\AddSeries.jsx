import React, { useState, useEffectz, useEffect } from "react";
import { Button, InputLabel, TextField, Select, MenuItem } from "@mui/material";
import { db } from "../../../firebase";
import { companies, companyId_constant } from "../../../constants/data";
import { firebaseLooper } from "../../../tools/tool";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { useAuth } from "../../../hooks/AuthProvider";
import { toastMessageSuccess, toastMessageWarning } from "../../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
  SubmitButtons,
} from "../../../components/buttons/Buttons";
import { useStateContext } from "../../../context/ContextProvider";
import { themeColors } from "../../../infrastructure/theme";
import axios from "axios";
import { useCreateMachineCfr } from "../../../hooks/cfr/machineCfrProvider";
import { dbConfig } from "../../../infrastructure/db/db-config";
import { useAuditSetter } from "../../../services3/audits/AuditContext";

const AddDoc = ({ handleClose, type, machineName, index, mid }) => {
  const [protocol_no, setProtocol_No] = useState("");
  const [description, setDescription] = useState("");
  const [status, setStatus] = useState("");
  const [user, setUser] = useState([]);
  const { currentUser } = useAuth();
  const { currentMode } = useStateContext();
  const addauditseriescfr = useCreateMachineCfr();
  const addsatseriescfr = useCreateMachineCfr();
  const handleAudit = useAuditSetter();
  console.log("type", type);
  const menuItemTheme = {
    backgroundColor:
      currentMode === "Dark"
        ? themeColors.dark.primary
        : themeColors.light.primary,
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = {
      index,
      protocol_no,
      description,
      status,
      mid,
      rev_no: "00",
    };
    let date = new Date();
    const data2 = {
      activity: "audit series added",
      dateTime: date,

      description: "an audit series is added",
      machine: mid,
      module: "AUDIT",
      username: currentUser.username,
    };

    const data3 = {
      activity: "sat series added",
      dateTime: date,

      description: "a sat series is added",
      machine: mid,
      module: "SAT",
      username: currentUser.username,
    };
    // db.collection(companies).doc(companyId_constant)
    //     .collection(`${type.toLowerCase()}List`).add(data).then(() => {
    //         // LoggingFunction(
    //         //     machineName,
    //         //     protocol_no,
    //         //     `${user?.fname} ${user?.lname}`,
    //         //     type,
    //         //     `${protocol_no} series is added to ${type} module`
    //         // )
    //         handleClose();
    //         toastMessageSuccess({ message: "Added successfully" })
    //     })
    //axios
    //   .post(`${dbConfig.url}/fatlists`, data)
    //   .then(() => {
    //     //window.location.reload();
    //     if (type == "SAT") {
    //       addsatseriescfr(data3);
    //     } else {
    //       addauditseriescfr(data2);
    //     }
    // db.collection(companies).doc(companyId_constant)
    //     .collection(`${type.toLowerCase()}List`).add(data).then(() => {
    //         // LoggingFunction(
    //         //     machineName,
    //         //     protocol_no,
    //         //     `${user?.fname} ${user?.lname}`,
    //         //     type,
    //         //     `${protocol_no} series is added to ${type} module`
    //         // )
    //         handleClose();
    //         toastMessageSuccess({ message: "Added successfully" })
    //     })
    axios
      .post(
        `${dbConfig.url}/${
          type === "AUDIT" ? "FAT"?.toLowerCase() : type?.toLowerCase()
        }lists`,
        data,
      )
      .then(() => {
        //window.location.reload();
        if (type == "SAT") {
          addsatseriescfr(data3);
        } else {
          // AUDIT i.e fat
          addauditseriescfr(data2);
        }
        handleAudit(type);
        handleClose();
        toastMessageSuccess({ message: "Added successfully" });
      })
      .catch((e) => {
        toastMessageWarning({ message: "Something went wrong" });
        console.log("error fatlist add failed :", e);
      });
  };

  // useEffect(() => {
  //     db.collection(companies)
  //         .doc(companyId_constant)
  //         .collection("userData")
  //         .where("email", "==", currentUser.email)
  //         .onSnapshot((snap) => {
  //             const data = firebaseLooper(snap);
  //             setUser(data[0]);
  //         });
  // }, []);

  return (
    <form
      onSubmit={handleSubmit}
      style={
        currentMode === "Dark"
          ? { backgroundColor: "#212B36", color: "white" }
          : {}
      }
    >
      <InputLabel style={{ marginBottom: "10px" }}>Protocol No.</InputLabel>
      <TextField
        onChange={(e) => setProtocol_No(e.target.value)}
        value={protocol_no}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Description</InputLabel>
      <TextField
        onChange={(e) => setDescription(e.target.value)}
        value={description}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Status</InputLabel>
      <Select
        onChange={(e) => setStatus(e.target.value)}
        value={status}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: " 12 px" }}
      >
        <MenuItem value="completed" sx={menuItemTheme}>
          Completed
        </MenuItem>
        <MenuItem value="not completed" sx={menuItemTheme}>
          Not Completed
        </MenuItem>
        <MenuItem value="pending" sx={menuItemTheme}>
          Pending
        </MenuItem>
      </Select>
      <div className="p-2 mt-2 flex justify-between">
        {/* <Button onClick={handleClose} variant='outlined'>Cancel</Button> */}
        <ButtonBasicCancel
          type="button"
          buttonTitle="Cancel"
          onClick={handleClose}
        />
        {/* <Button type="submit" variant='outlined'>Submit</Button> */}
        <SubmitButtons type="submit" buttonTitle="Submit" />
      </div>
    </form>
  );
};

export default AddDoc;
