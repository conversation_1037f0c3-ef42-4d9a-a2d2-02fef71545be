import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>l,
  <PERSON>u,
  Menu<PERSON>tem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import { Box } from "@mui/system";
import React, { useState } from "react";
import ClearIcon from "@mui/icons-material/Clear";
import CheckIcon from "@mui/icons-material/Check";
import { toastMessageSuccess } from "../../tools/toast";

const EditAnnotation = ({
  mqttList,
  item,
  mode,
  setAnnotations,
  annotations,
  mainMid,
}) => {
  const [currentAnnotation, setCurrentAnnotation] = useState(item);
  const [color, setColor] = useState(currentAnnotation?.data?.color);

  const prePopData = (data) => {
    setAnnotations((pre) => pre.filter((dataItem) => dataItem !== data));
  };

  const confirmAnnotation = () => {
    let tempAnn = [...annotations];
    for (let x in annotations) {
      if (annotations[x].geometry.x === currentAnnotation.geometry.x) {
        tempAnn[x].data.text = currentAnnotation.data.text;
        tempAnn[x].data.mqtt_id = currentAnnotation.data.mqtt_id;
        tempAnn[x].data.color = color;
      }
    }
    setAnnotations(tempAnn);
    toastMessageSuccess({
      message: "Annotation updated! Please Save before leaving",
    });
  };

  return (
    <div>
      <Box
        sx={{
          border: "1px solid #CFD2CF",
          p: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          m: 2,
        }}
      >
        {mode ? (
          <Typography>
            {item.data.text} -{" "}
            <span style={{ color: "#A2B5BB" }} className={`text-sm italic`}>
              ({mqttList?.find((data) => data._id === item.data.mqtt_id)?.tag} :{" "}
              {mqttList?.find((data) => data._id === item.data.mqtt_id)?.value}{" "}
              )
            </span>{" "}
          </Typography>
        ) : (
          <>
            <div style={{ display: "flex", flexDirection: "column" }}>
              <InputLabel>Annotation Process value</InputLabel>
              <Select
                style={{ marginBottom: "10px" }}
                name="Annotation Tag"
                size="small"
                variant="outlined"
                placeholder="Annotation Tag"
                onChange={(e) =>
                  setCurrentAnnotation({
                    ...currentAnnotation,
                    data: {
                      ...currentAnnotation.data,
                      mqtt_id: e.target.value,
                    },
                  })
                }
                value={currentAnnotation?.data?.mqtt_id}
              >
                {mqttList
                  ?.filter((item) => item?.mid === mainMid)
                  .map((data) => (
                    <MenuItem value={data._id}>{data?.tag}</MenuItem>
                  ))}
              </Select>
              <InputLabel>Annotation Tag</InputLabel>
              <TextField
                name="Annotation Tag"
                size="small"
                variant="outlined"
                placeholder="Annotation Tag"
                onChange={(e) =>
                  setCurrentAnnotation({
                    ...currentAnnotation,
                    data: {
                      ...currentAnnotation.data,
                      text: e.target?.value,
                    },
                  })
                }
                value={currentAnnotation?.data?.text}
              />
              <div className="mr-5" style={{ display: "flex" }}>
                <Box sx={{ mr: 2, align: "center" }}>
                  <p className="text-sm">BG Color</p>
                  <input
                    value={color.background_color}
                    required
                    type="color"
                    onChange={(e) =>
                      setColor({ ...color, background_color: e.target.value })
                    }
                  />
                </Box>
                <Box sx={{ mr: 2, align: "center" }}>
                  <p className="text-sm"> Text</p>
                  <input
                    value={color.text_color}
                    required
                    type="color"
                    onChange={(e) =>
                      setColor({ ...color, text_color: e.target.value })
                    }
                  />
                </Box>
                <Box sx={{ mr: 2, align: "center" }}>
                  <p className="text-sm">Border</p>
                  <input
                    value={color.border_color}
                    required
                    type="color"
                    onChange={(e) =>
                      setColor({ ...color, border_color: e.target.value })
                    }
                  />
                </Box>
              </div>
            </div>
          </>
        )}
        {/* <Typography>
          <ShowMqttValue mqttId={item.data.mqttId} />
        </Typography> */}
        {!mode && (
          <div>
            <IconButton onClick={() => confirmAnnotation()}>
              <p className="text-xs">Confirm</p> <CheckIcon />
            </IconButton>
            <IconButton onClick={() => prePopData(item)}>
              <ClearIcon />
            </IconButton>
          </div>
        )}
      </Box>
    </div>
  );
};

export default EditAnnotation;
