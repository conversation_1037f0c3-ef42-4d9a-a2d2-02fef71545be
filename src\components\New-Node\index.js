import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import Popup from "reactjs-popup";
import { toastMessage, toastMessageWarning } from "../../tools/toast";
import {
  addData,
  deleteData,
} from "../../services2/issueModule/IssueModule.services";
import Delete from "../Delete/Delete";
import { ButtonBasic } from "../buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import {
  Button,
  TextField,
  TableContainer,
  Table,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  Paper,
  IconButton,
  Dialog,
  Box,
  Typography,
} from "@mui/material";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import DeleteIcon from "@mui/icons-material/Delete";
// import { db } from "../../firebase";
// import { companies, companyId_constant } from "../../constants/data";
// import { firebaseLooper } from "../../tools/tool";
import "../../styles/App.scss";
import { toastMessageSuccess } from "../../tools/toast";
import {
  useIssueModuleChangeCount,
  useIssueModuleNodeData,
} from "../../services2/issueModule/IssueModule.context";
import PageHeader from "../commons/page-header.component";
import { makeStyles } from "@mui/styles";
import { sharedCss } from "../../styles/sharedCss";
import CommonNodeModal from "./CommonNodeModal";

const useCustomStyles = makeStyles((theme) => ({
  issueCardContainerContainer: {
    padding: "1rem",
    borderRadius: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  addButton: {
    width: "fit-content",
    whiteSpace: "nowrap",
  },
  issueModulePageContainer: {
    // padding: "1rem",
    // border: "1px solid gainsboro",
    height: "fit-content",
  },
}));

function NodeRow({ data }) {
  const [openDel, setOpenDel] = useState(false);
  const { issueCount, setIssueCount } = useIssueModuleChangeCount();

  function lengthOfNode(node) {
    return Object.values(node).filter((elem) => elem === true).length;
  }
  const noOfFields = lengthOfNode(data);
  return (
    <TableRow className="table-nodes">
      <TableCell style={{ textTransform: "capitalize" }} align="left">
        {data?.name}
      </TableCell>
      <TableCell style={{ textTransform: "capitalize" }} align="left">
        {data?.desc}
      </TableCell>
      <TableCell style={{ textTransform: "capitalize" }} align="center">
        {noOfFields}
      </TableCell>
      <TableCell align="center">
        <IconButton color="error">
          <DeleteIcon
            style={{ fontSize: "22px" }}
            onClick={() => {
              setOpenDel(true);
            }}
          />
        </IconButton>
        {console.log("Data being passed to Link:", data)}
        <IconButton
          component={Link}
          to={{
            pathname: `/create-node/${data?._id}`,
            state: data,
          }}
          onClick={() => console.log("Link clicked with data:", data)}
        >
          <KeyboardArrowRightIcon
            style={{ color: "green", fontSize: "30px" }}
          />
        </IconButton>
        <Dialog open={openDel}>
          <Delete
            onClose={() => setOpenDel(false)}
            onDelete={async () => {
              await deleteData("custom_nodes", data?._id).then(
                () => {
                  setIssueCount(issueCount + 1);

                  setOpenDel(false);
                  toastMessageSuccess({
                    message: "Node Data has been deleted successfully!",
                  });
                },
                (error) => console.log(error),
              );
            }}
          />
        </Dialog>
      </TableCell>
    </TableRow>
  );
}

const NewNode = () => {
  const [nodeName, setNodeName] = useState("");
  const [nodeDesc, setNodeDesc] = useState("");

  // Open create node modal
  const [openCreateNewNode, setOpenCreateNewNode] = useState(false);

  const { currentMode, currentColorLight } = useStateContext();
  const history = useNavigate();
  const nodes = useIssueModuleNodeData();
  const customCss = useCustomStyles();
  const commonCss = sharedCss();
  const { issueCount, setIssueCount } = useIssueModuleChangeCount();
  const addNode = async (data) => {
    await addData("custom_nodes", data).then(() => {
      setIssueCount(issueCount + 1);
      toastMessageSuccess({
        message: "Successfully added new Node",
      });
    });
  };

  const handleOpenCreateNode = () => setOpenCreateNewNode(true);
  const handleCloseCreateNode = () => setOpenCreateNewNode(false);

  const handleNodeNameChange = (evt) =>
    setNodeName(evt.target.value.trimStart());

  const handleNodeDescriptionChange = (evt) =>
    setNodeDesc(evt.target.value.trimStart());

  const handleCancel = () => {
    setNodeName("");
    setNodeDesc("");
    handleCloseCreateNode();
  };

  const handleSubmit = async () => {
    try {
      if (nodeName === "") {
        toastMessageWarning({
          message: "Please provide name of the node",
        });
        return;
      }
      if (nodeDesc === "") {
        toastMessageWarning({
          message: "Please provide description of the node",
        });
        return;
      }
      let data = {
        name: `${nodeName}`,
        desc: `${nodeDesc}`,
        text_field: false,
        text_field2: false,
        text_field3: false,
        text_area: false,
        text_area2: false,
        text_area3: false,
        image: false,
        image2: false,
        image3: false,
        code: false,
        code2: false,
        code3: false,
      };
      await addNode(data);
      setNodeName("");
      setNodeDesc("");
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error(error);
      }
      return toastMessage({
        message: error?.message ?? "An error occured while saving the node.",
      });
    } finally {
      handleCloseCreateNode();
    }
  };

  return (
    <main className={customCss.issueModulePageContainer}>
      <header
        className={commonCss?.headingContainer}
        style={{ padding: "1rem" }}
      >
        <Box>
          <Typography variant="h4">Nodes</Typography>
        </Box>
        <div className="action-container mr-4">
          {/*
          <Popup
            trigger={
              <ButtonBasic
                buttonTitle={"New Node"}
                onClick={() => {
                  setNodeName(""); // Reset when opening
                  setNodeDesc(""); // Reset when opening
                }}
              />
            }
            modal
            nested
          >
            {(close) => {
              return (
                <div
                  className="modal"
                  style={
                    currentMode === "Dark"
                      ? { backgroundColor: "#212B36", color: "white" }
                      : {}
                  }
                >
                  <div
                    className="header"
                    style={currentMode === "Dark" ? { color: "white" } : {}}
                  >
                    {" "}
                    Create Node{" "}
                  </div>
                  <div className="content">
                    <div className="input-field-container">
                      <label
                        style={currentMode === "Dark" ? { color: "white" } : {}}
                      >
                        Name of Node:
                      </label>
                      <TextField
                        fullWidth
                        variant="outlined"
                        type="text"
                        placeholder="Name of the Node"
                        value={nodeName}
                        onChange={(evt) => setNodeName(evt.target.value.trimStart())}
                      />
                    </div>
                    <div className="input-field-container">
                      <label
                        style={currentMode === "Dark" ? { color: "white" } : {}}
                      >
                        Description of Node:
                      </label>
                      <TextField
                        fullWidth
                        variant="outlined"
                        type="text"
                        placeholder="Description of the Node"
                        value={nodeDesc}
                        onChange={(evt) => setNodeDesc(evt.target.value.trimStart())}
                      />
                    </div>
                  </div>
                  <div className="actions">
                    <Button variant="contained" color="error" onClick={close}>
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      color="success"
                      disabled={nodeName === "" || nodeDesc === ""}
                      onClick={() => {
                        if (nodeName === "") {
                          toastMessageWarning({
                            message: "Please provide name of the node",
                          });
                          return;
                        }
                        if (nodeDesc === "") {
                          toastMessageWarning({
                            message: "Please provide description of the node",
                          });
                          return;
                        }
                        let data = {
                          name: `${nodeName}`,
                          desc: `${nodeDesc}`,
                          text_field: false,
                          text_field2: false,
                          text_field3: false,
                          text_area: false,
                          text_area2: false,
                          text_area3: false,
                          image: false,
                          image2: false,
                          image3: false,
                          code: false,
                          code2: false,
                          code3: false,
                        };
                        addNode(data);
                        setNodeName(""); // Reset on submit
                        setNodeDesc(""); // Reset on submit
                        close();
                      }}
                    >
                      Add Node
                    </Button>
                  </div>
                </div>
              );
            }}
          </Popup>
          */}
          <ButtonBasic
            buttonTitle={"New Node"}
            onClick={handleOpenCreateNode}
          />
          <CommonNodeModal
            openDialog={openCreateNewNode}
            dialogTitleText={"Create Node"}
            textName={nodeName}
            textNameInputStyle={{
              marginBottom: "4vh",
            }}
            textNameInputLabel={"Name of Node:"}
            textNameInputPlaceholder={"Name of the Node"}
            handleTextNameChange={handleNodeNameChange}
            textDescription={nodeDesc}
            textDescriptionInputStyle={{
              marginBottom: "0.5vh",
            }}
            textDescriptionInputLabel={"Description of the Node:"}
            textDescriptionInputPlaceholder={"Description of the Node"}
            handleTextDescriptionChange={handleNodeDescriptionChange}
            buttonCloseText={"Cancel"}
            handleClose={handleCancel}
            isSubmitButtonDisabled={nodeName === "" || nodeDesc === ""}
            buttonSubmitText={"Add Node"}
            handleSubmit={handleSubmit}
          />
          <ButtonBasic
            buttonTitle={"Projects"}
            onClick={() => history("/issues")}
          />
        </div>
      </header>
      <div className={customCss.issueCardContainerContainer}>
        <Typography variant="h6" sx={{ marginBottom: "12px" }}>
          List of Nodes
        </Typography>
        <TableContainer component={Paper}>
          <Table id="node-table">
            <TableHead>
              <TableRow>
                <TableCell style={{ textTransform: "capitalize" }} align="left">
                  Node Name
                </TableCell>
                <TableCell style={{ textTransform: "capitalize" }} align="left">
                  Node Description
                </TableCell>
                <TableCell
                  style={{ textTransform: "capitalize" }}
                  align="center"
                >
                  Field Number
                </TableCell>
                <TableCell
                  style={{ textTransform: "capitalize" }}
                  align="center"
                >
                  Actions
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {nodes?.map((node) => {
                return <NodeRow data={node} />;
              })}
              <TableRow className="no-click">
                <TableCell align="left">Text Node</TableCell>
                <TableCell align="left">Default Node with Text</TableCell>
                <TableCell align="center">1</TableCell>
                <TableCell align="center">
                  <IconButton disabled color="error">
                    <DeleteIcon style={{ fontSize: "22px" }} />
                  </IconButton>
                </TableCell>
              </TableRow>
              <TableRow className="no-click">
                <TableCell align="left">Image Node</TableCell>
                <TableCell align="left">
                  Default Node with Image and Text
                </TableCell>
                <TableCell align="center">1</TableCell>
                <TableCell align="center">
                  <IconButton disabled color="error">
                    <DeleteIcon style={{ fontSize: "22px" }} />
                  </IconButton>
                </TableCell>
              </TableRow>
              <TableRow className="no-click">
                <TableCell align="left">Code Node</TableCell>
                <TableCell align="left">
                  Default Node with Code and Text
                </TableCell>
                <TableCell align="center">1</TableCell>
                <TableCell align="center">
                  <IconButton disabled color="error">
                    <DeleteIcon style={{ fontSize: "22px" }} />
                  </IconButton>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </div>
    </main>
  );
};

export default NewNode;
