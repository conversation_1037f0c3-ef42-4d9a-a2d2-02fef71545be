import React, { useState } from "react";
import NotificationsIcon from "@mui/icons-material/Notifications";
import { useNavigate, useLocation } from "react-router-dom";
import { useMaintenanceInfoSeter } from "../../context/MaintenanceContext";
import { useStateContext } from "../../context/ContextProvider";
import { Card } from "@mui/material";
import { makeStyles } from "@mui/styles";
import moment from "moment";

const useStyles = makeStyles((theme) => ({
  cardRoot: {
    backgroundColor:
      theme.palette.mode === "dark" ? "#212B36" : theme.palette.background.paper,
    color: theme.palette.mode === "dark" ? "#fff" : "#000",
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    display: "flex",
    alignItems: "center",
    cursor: "pointer",
    gap: "1rem",
    "&:hover": {
      boxShadow: theme.palette.mode === "dark"
        ? "0 4px 8px -2px rgba(0,0,0,0.95)"
        : "0 4px 8px -2px rgba(0,0,0,0.40)",
    },
  },
  icon: {
    fontSize: 30,
    borderRadius: 4,
  },
  title: {
    color: theme.palette.mode === "dark" ? "#fff" : "#000",
    fontWeight: 500,
    marginBottom: 4,
  },
  label: {
    fontWeight: "bold",
    fontSize: "0.875rem",
    marginRight: 4,
  },
  text: {
    fontSize: "0.875rem",
  },
}));

export default function NotificationItem({ dataM, alerts }) {
  const [showAlerts, setShowAlerts] = useState(true);
  const navigate = useNavigate();
  const maintenanceInfoSetter = useMaintenanceInfoSeter();
  const location = useLocation();
  const { currentMode } = useStateContext(); // can be removed if unused elsewhere
  const pathName = location.pathname;
  const id = dataM._id;

  const classes = useStyles();

  const handleMaintenanceInfoSetter = async (data) => {
    return new Promise((resolve) => {
      resolve(maintenanceInfoSetter({ ...data, maintenance_id: data?.main_id }));
    });
  };

  const handleRedirect = async (data) => {
    if (pathName === `/maintenance/${data.mid}`) {
      maintenanceInfoSetter({ ...data, maintenance_id: data?.main_id });
    } else {
      maintenanceInfoSetter({ ...data, maintenance_id: data?.main_id });
      await handleMaintenanceInfoSetter(data);
      navigate(data?.type === 0 ? `calibration/${data?.mid}` : `maintenance/${data?.mid}`);
    }
  };

  return (
    <>
      {showAlerts &&
        alerts.map((data) =>
          id === data.mid ? (
            <li key={data._id} onClick={() => handleRedirect(data)}>
              <Card variant="outlined" className={classes.cardRoot}>
                <div style={{ display: "flex", alignItems: "center" }}>
                  <NotificationsIcon
                    className={classes.icon}
                    style={{
                      color:
                        data?.def_value === data?.value ? "#D82148" : "green",
                    }}
                  />
                </div>
                <div>
                  <div className={classes.title}>
                    <span>{data?.tag}</span> :{" "}
                    {moment(data?.time).format("MMMM Do YYYY, h:mm:ss")}
                  </div>
                  <div>
                    <span className={classes.label}>Value:</span>
                    <span className={classes.text}>{data?.value}</span>
                  </div>
                  <div className={classes.text}>{dataM.title}</div>
                </div>
              </Card>
            </li>
          ) : null
        )}
    </>
  );
}
