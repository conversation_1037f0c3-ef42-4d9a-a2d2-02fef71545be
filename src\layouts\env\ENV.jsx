import React, { useEffect, useState } from "react";
import {
  TextField,
  Button,
  Grid,
  Typography,
  Box,
  Paper,
  Card,
  Switch,
  FormControlLabel,
  MenuItem,
} from "@mui/material";
import axios from "axios";
import { toast } from "react-toastify";
import bugout from "./logs";
import TestForm from "./run-tests";
import MyDropDown from "../../components/ui/dropdown";
import ColorPickerInput from "../../components/ui/color-picker-input";
import { useUtils } from "../../hooks/UtilsProvider";
import ModulesAccess from "./modules-access/modules-access";
import RequestStatusFilters from "./request-status-filters";
import { dbConfig } from "../../infrastructure/db/db-config";
import RolesManager from "./RolesManager";

const ENV = () => {
  const { envData } = useUtils();
  const [formData, setFormData] = useState(null);

  /**
   * Handles changes to input fields by updating the formData state.
   * @param {Event} e - The event object from the input change event.
   */

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  /**
   * Updates the formData state with the new value of a switch.
   * Converts the switch state into "Y" (checked) or "N" (unchecked).
   * @param {string} name - The name of the form field to update.
   * @returns {Function} A function that takes an event and updates the state.
   */

  const handleSwitchChange = (name) => (event) => {
    setFormData({ ...formData, [name]: event.target.checked ? "Y" : "N" });
  };

  /**
   * Fetches environment data from the server and updates the formData state.
   * Displays an error message if the data cannot be retrieved.
   */

  const getEnvData = async () => {
    if (dbConfig.url === undefined) {
      return;
    }
    const response = await axios.get(`${dbConfig.url}/envdata`);
    if (response.data) {
      setFormData(response.data);
    } else {
      toast.error("Error getting environment settings, please contact admin");
    }
  };

  /**
   * Submits the form data to update environment settings.
   * Prevents default form submission behavior.
   * Sends a PUT request to update the data on the server.
   * Displays a success message upon successful update or an error message upon failure.
   * @param {Event} e - The event object from the form submission event.
   */

  const handleSubmit = async (e) => {
    e.preventDefault();

    axios
      .put(`${dbConfig.url}/envdata/update`, formData)
      .then(() => {
        toast.success("Saved Successfully");
      })
      .catch((error) => {
        console.log(error);
        toast.error(error?.message);
      });
  };

  useEffect(() => {
    getEnvData();
  }, []);

  /**
   * Makes a POST request to /seed/migrate to migrate the database.
   * Shows a success or error message based on the response.
   */
  const handleMigrateDB = async () => {
    axios.post(`${dbConfig.url}/seed/migrate`).then(
      (res) => {
        toast(res.data.message);
      },
      (err) => {
        toast(err.data.message);
      },
    );
  };

  /**
   * Downloads the combined counts data in CSV format.
   *
   * Makes a GET request to /envdata/count_csv and handles the response as a Blob.
   * Creates a link element to trigger the download of the file, named "combined_counts.csv".
   * If the download is successful, the file is saved to the user's default download location.
   * If there's an error, logs an error message to the console.
   */
  const downloadCSVCounts = async () => {
    try {
      const response = await axios.get(`${dbConfig.url}/envdata/count_csv`, {
        responseType: "blob", // Important: Handle the response as a Blob
      });

      // Create a blob from the response data
      const blob = new Blob([response.data], { type: "text/csv" });

      // Create a link element to trigger the download
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "combined_counts.csv"); // Set the filename
      document.body.appendChild(link);
      link.click();
      link.remove(); // Clean up the DOM

      // Optional: Revoke the blob URL after some time (cleanup)
      setTimeout(() => window.URL.revokeObjectURL(url), 100);
    } catch (error) {
      console.error("Error downloading the CSV file:", error);
    }
  };

  /**
   * Downloads log files in a zip archive.
   *
   * Makes a GET request to /envdata/export-logs and handles the response as a Blob.
   * Creates a link element to trigger the download of the file, named "logs.zip".
   * If the download is successful, the file is saved to the user's default download location.
   * If there's an error, logs an error message to the console and alerts the user.
   */
  const handleDownloadLogs = async () => {
    try {
      const response = await axios.get(`${dbConfig.url}/envdata/export-logs`, {
        responseType: "blob", // Ensure it handles binary data
      });

      const blob = new Blob([response.data], { type: "application/zip" });
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download =
        response.headers["content-disposition"]?.split("filename=")[1] ||
        "logs.zip";
      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error("Error downloading log files:", error);
      alert("There was an error downloading the log files. Please try again.");
    }
  };

  /**
   * Downloads the environment configuration in Excel (.xlsx) format.
   *
   * Makes a GET request to /envdata/export-config and handles the response as a Blob.
   * Creates a link element to trigger the download of the file, named "config_export.xlsx".
   * If the download is successful, the file is saved to the user's default download location.
   * If there's an error, logs an error message to the console and alerts the user.
   */
  const downloadConfig = async () => {
    try {
      // Request to get the Excel file from the backend
      const response = await axios.get(
        `${dbConfig.url}/envdata/export-config`,
        {
          responseType: "blob", // Important: Handle the response as a Blob
        },
      );

      // Create a Blob from the response data
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // Create a link element and set it to download the file
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = "config_export.xlsx"; // Set the file name
      document.body.appendChild(link);
      link.click();

      // Clean up the link after download
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error("Error downloading the Excel file:", error);
      alert("There was an error downloading the Excel file. Please try again.");
    }
  };

  /**
   * Handles button click events to trigger data migration based on the specified field.
   * If the field's value in formData is "Y", it determines the appropriate endpoint
   * and makes a POST request to migrate data. Displays success or error messages
   * based on the result.
   *
   * @param {string} field - The name of the field to check in formData.
   *                         Determines which data migration to perform.
   */

  const handleButtonClick = async (field) => {
    if (formData[field] === "Y") {
      try {
        let endpoint = "";
        switch (field) {
          case "SEED":
            endpoint = "/seed";
            break;
          case "SEED_USER":
            endpoint = "/seed/seed-user";
            break;
          case "SEED_BLOCK":
            endpoint = "/seed/seed-block";
            break;
          default:
            break;
        }

        if (endpoint) {
          await axios.post(`${dbConfig.url}${endpoint}`);
          toast(` Migrated data successfully`);
        }
      } catch (error) {
        console.error(`Error Migrating data:`, error);
        toast.error(`Error Migrating data: `, error);
      }
    } else {
      toast.warning(`This migration is not available`);
    }
  };

  return (
    <Grid container>
      <Grid item xs={12}>
        <Card
          elevation={3}
          style={{ marginBottom: "1rem", padding: "1rem 1rem" }}
          sx={{ bgcolor: envData.CARD_BG_COLOR }}
        >
          <Box>
            <Typography variant="h4" sx={{ margin: "1rem" }}>
              Configuration Settings
            </Typography>
          </Box>
        </Card>
        {formData !== null && (
          <Box>
            <Box sx={{ maxWidth: 1840, p: 3 }}>
              <form onSubmit={handleSubmit}>
                {/* SMTP Configuration */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    SMTP Configuration
                  </Typography>
                  <Grid container spacing={2}>
                    {/* SMTP OAuth Toggle */}
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.SMTP_OAUTH === "Y"}
                            onChange={handleSwitchChange("SMTP_OAUTH")}
                            name="SMTP_OAUTH"
                            color="primary"
                          />
                        }
                        label="Enable OAuth for SMTP"
                      />
                    </Grid>

                    {/* Regular SMTP fields */}
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="SMTP Host"
                        name="SMTP_HOST"
                        value={formData.SMTP_HOST}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="SMTP Port"
                        name="SMTP_PORT"
                        value={formData.SMTP_PORT}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="SMTP Email"
                        name="SMTP_EMAIL"
                        value={formData.SMTP_EMAIL}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="email"
                      />
                    </Grid>
                    {(formData.SMTP_OAUTH === undefined ||
                      formData.SMTP_OAUTH === "N") && (
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="SMTP Password"
                          name="SMTP_PASS"
                          value={formData.SMTP_PASS}
                          onChange={handleChange}
                          variant="outlined"
                          size="small"
                          type="password"
                        />
                      </Grid>
                    )}

                    {/* OAuth-related fields: only shown if SMTP_OAUTH is 'Y' */}
                    {formData.SMTP_OAUTH === "Y" && (
                      <>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="OAuth Client ID"
                            name="OAUTH_CLIENT_ID"
                            value={formData.OAUTH_CLIENT_ID}
                            onChange={handleChange}
                            variant="outlined"
                            size="small"
                            type="text"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="OAuth Client Secret"
                            name="OAUTH_CLIENT_SECRET"
                            value={formData.OAUTH_CLIENT_SECRET}
                            onChange={handleChange}
                            variant="outlined"
                            size="small"
                            type="password"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="OAuth Refresh Token"
                            name="OAUTH_REFRESH_TOKEN"
                            value={formData.OAUTH_REFRESH_TOKEN}
                            onChange={handleChange}
                            variant="outlined"
                            size="small"
                            type="text"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="OAuth Access Token"
                            name="OAUTH_ACCESS_TOKEN"
                            value={formData.OAUTH_ACCESS_TOKEN}
                            onChange={handleChange}
                            variant="outlined"
                            size="small"
                            type="text"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="OAuth Tenant ID"
                            name="OAUTH_TENANT_ID"
                            value={formData.OAUTH_TENANT_ID}
                            onChange={handleChange}
                            variant="outlined"
                            size="small"
                            type="text"
                          />
                        </Grid>
                      </>
                    )}
                  </Grid>
                </Paper>

                {/* Database Configuration */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Database Configuration
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Database URL"
                        name="DATABASE_URL"
                        value={formData.DATABASE_URL}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Server Host"
                        name="SERVER_HOST"
                        value={formData.SERVER_HOST}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>

                    {/* Existing Server user credentials */}
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Server User"
                        name="SERVER_USER"
                        value={formData.SERVER_USER}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Server Password"
                        name="SERVER_PASSWORD"
                        value={formData.SERVER_PASSWORD}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="password"
                      />
                    </Grid>

                    {/* New fields for SA Username and Password */}
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="SA Username"
                        name="SA_USERNAME"
                        value={formData.SA_USERNAME}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="SA Password"
                        name="SA_PASSWORD"
                        value={formData.SA_PASSWORD}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="password"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Server DB Name"
                        name="SERVER_DB_NAME"
                        value={formData.SERVER_DB_NAME}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Server DB Backup Path"
                        name="SERVER_DB_BACKUP_PATH"
                        value={formData.SERVER_DB_BACKUP_PATH}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Button
                        variant={
                          formData.SEED_BLOCK === "Y" ? "contained" : "outlined"
                        }
                        color="primary"
                        onClick={() => handleMigrateDB()}
                        sx={{ width: "20%", minHeight: "20px" }}
                      >
                        Migrate Database Schema
                      </Button>
                    </Grid>
                    <Grid item xs={12}>
                      <span>
                        Periodic backup can only be enabled if SA credentials
                        are provided
                      </span>
                    </Grid>
                    {/* Enable Periodic Backup (Only enabled if SA credentials are provided) */}
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.BACKUP_ENABLED === "Y"}
                            onChange={handleSwitchChange("BACKUP_ENABLED")}
                            name="BACKUP_ENABLED"
                            color="primary"
                            disabled={
                              !formData.SA_USERNAME || !formData.SA_PASSWORD
                            }
                          />
                        }
                        label="Enable Periodic Backup"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Backup Interval in day's"
                        name="BACKUP_INTERVAL_DAYS"
                        value={formData.BACKUP_INTERVAL_DAYS}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Number of backups to retain"
                        name="BACKUP_RETAIN_COUNT"
                        value={formData.BACKUP_RETAIN_COUNT}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Backup Hour of the day (0 - 23)"
                        name="BACKUP_TIME_HOUR"
                        value={formData.BACKUP_TIME_HOUR}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Backup Minute of the day (0 - 59)"
                        name="BACKUP_TIME_MINUTE"
                        value={formData.BACKUP_TIME_MINUTE}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                  </Grid>
                </Paper>

                {/* LDAP Configuration */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    LDAP Configuration
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="LDAP URL"
                        name="LDAP_URL"
                        value={formData.LDAP_URL}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="LDAP LOGON NAME"
                        name="LDAP_LOGON_NAME"
                        value={formData.LDAP_LOGON_NAME}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="LDAP BASE DN"
                        name="LDAP_BASE_ON"
                        value={formData.LDAP_BASE_ON}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="LDAP GROUP NAME"
                        name="LDAP_GROUP_NAME"
                        value={formData.LDAP_GROUP_NAME}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="OU"
                        name="OUNAME"
                        value={formData.OUNAME}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="LDAP password expiry warning - number of days"
                        name="PWD_EXPIRE_WARN_DAYS"
                        value={formData.PWD_EXPIRE_WARN_DAYS}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                  </Grid>
                </Paper>

                {/* Super Admin Credentials */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Super Admin Credentials
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Super Admin Username"
                        name="SUPER_ADMIN_USERNAME"
                        value={formData.SUPER_ADMIN_USERNAME}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Super Admin Password"
                        name="SUPER_ADMIN_PASSWORD"
                        value={formData.SUPER_ADMIN_PASSWORD}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="password"
                      />
                    </Grid>
                  </Grid>
                </Paper>

                {/* Other Settings */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    PDF Settings
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Maximum number of rows in PDF"
                        name="MAX_PDF_LIMIT"
                        value={formData.MAX_PDF_LIMIT}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                  </Grid>
                </Paper>

                <ModulesAccess
                  formData={formData}
                  setFormData={setFormData}
                  roles={formData.ROLES}
                />

                {/* <RequestStatusFilters
                  formData={formData}
                  setFormData={setFormData}
                  handleChange={handleChange}
                /> */}

                {/* Other Settings */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Watch Tower
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.MAINTENANCE === "Y"}
                            onChange={handleSwitchChange("MAINTENANCE")}
                            name="MAINTENANCE"
                            color="primary"
                          />
                        }
                        label="Enable Maintenance Mode "
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.ENABLE_WATCH_TOWER === "Y"}
                            onChange={handleSwitchChange("ENABLE_WATCH_TOWER")}
                            name="ENABLE_WATCH_TOWER"
                            color="primary"
                          />
                        }
                        label="Enable Watch Tower"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="LOG_ROCKET_ID"
                        name="LOG_ROCKET_ID"
                        value={formData.LOG_ROCKET_ID}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="NEW_RELIC_APP_NAME"
                        name="NEW_RELIC_APP_NAME"
                        value={formData.NEW_RELIC_APP_NAME}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="NEW_RELIC_LICENSE_KEY"
                        name="NEW_RELIC_LICENSE_KEY"
                        value={formData.NEW_RELIC_LICENSE_KEY}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="NEW_RELIC_LOG_LEVEL"
                        name="NEW_RELIC_LOG_LEVEL"
                        value={formData.NEW_RELIC_LOG_LEVEL}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={
                              formData.NEW_RELIC_DISTRIBUTED_TRACING === "Y"
                            }
                            onChange={handleSwitchChange(
                              "NEW_RELIC_DISTRIBUTED_TRACING",
                            )}
                            name="NEW_RELIC_DISTRIBUTED_TRACING"
                            color="primary"
                          />
                        }
                        label="Enable NEW_RELIC_DISTRIBUTED_TRACING"
                      />
                    </Grid>
                  </Grid>
                </Paper>
                {/* /* Other Settings */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Other Settings
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.MAINTENANCE === "Y"}
                            onChange={handleSwitchChange("MAINTENANCE")}
                            name="MAINTENANCE"
                            color="primary"
                          />
                        }
                        label="Enable Maintenance Mode "
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.ENABLE_CACHE === "Y"}
                            onChange={handleSwitchChange("ENABLE_CACHE")}
                            name="ENABLE_CACHE"
                            color="primary"
                          />
                        }
                        label="Enable Caching "
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Redis Host"
                        name="REDIS_HOST"
                        value={formData.REDIS_HOST}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Redis Port"
                        name="REDIS_PORT"
                        value={formData.REDIS_PORT}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Redis User"
                        name="REDIS_USER"
                        value={formData.REDIS_USER}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Redis Password"
                        name="REDIS_PASSWORD"
                        value={formData.REDIS_PASSWORD}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="password"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Unit Name"
                        name="UNIT_NAME"
                        value={formData.UNIT_NAME}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Unit Logo"
                        name="UNIT_LOGO"
                        value={formData.UNIT_LOGO}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="File Storage Path"
                        name="STORAGE_DIRECTORY"
                        value={formData.STORAGE_DIRECTORY}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="text"
                      />
                    </Grid>
                    {process.env.NODE_ENV === "development" && (
                      <>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Auth Type  (Dev Feature)"
                            name="AUTH_TYPE"
                            value={formData.AUTH_TYPE}
                            onChange={handleChange}
                            variant="outlined"
                            size="small"
                            type="text"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Node Environment  (Dev Feature)"
                            name="NODE_ENV"
                            value={formData.NODE_ENV}
                            onChange={handleChange}
                            variant="outlined"
                            size="small"
                            type="text"
                          />
                        </Grid>
                      </>
                    )}

                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="To Email Support"
                        name="TO_EMAIL_SUPPORT"
                        value={formData.TO_EMAIL_SUPPORT}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="email"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="From Email Support"
                        name="FROM_EMAIL_SUPPORT"
                        value={formData.FROM_EMAIL_SUPPORT}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="email"
                      />
                    </Grid>
                    {process.env.NODE_ENV === "development" && (
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Password Encryption Secret  (Dev Feature)"
                          name="JWT_SECRET"
                          value={formData.JWT_SECRET}
                          onChange={handleChange}
                          variant="outlined"
                          size="small"
                          type="password"
                        />
                      </Grid>
                    )}
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="QA Verification Key"
                        name="QA_KEY"
                        value={formData.QA_KEY}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="password"
                        error={formData.QA_KEY.length < 5}
                        helperText={
                          formData.QA_KEY.length < 5
                            ? "QA Key should be minimum 5 characters."
                            : ""
                        }
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Login Lock Attempts"
                        name="LOGIN_LOCK_ATTEMPTS"
                        value={formData.LOGIN_LOCK_ATTEMPTS}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                  </Grid>
                </Paper>
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Notification Settings
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.NOTIFY === "Y"}
                            onChange={handleSwitchChange("NOTIFY")}
                            name="NOTIFY"
                            color="primary"
                          />
                        }
                        label="Enable Notification"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.SEND_EMAIL_CONTACT_ADMIN === "Y"}
                            onChange={handleSwitchChange(
                              "SEND_EMAIL_CONTACT_ADMIN",
                            )}
                            name="SEND_EMAIL_CONTACT_ADMIN"
                            color="primary"
                          />
                        }
                        label="Send Email for contact admin requests"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.CRON_DAILY === "Y"}
                            onChange={handleSwitchChange("CRON_DAILY")}
                            name="CRON_DAILY"
                            color="primary"
                          />
                        }
                        label="Enable Daily Notification"
                      />
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Daily Hour(24 hours)"
                        name="DAILY_HOUR"
                        value={formData.DAILY_HOUR}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Daily Minute"
                        name="DAILY_MINUTE"
                        value={formData.DAILY_MINUTE}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}></Grid>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.CRON_WEEKLY === "Y"}
                            onChange={handleSwitchChange("CRON_WEEKLY")}
                            name="CRON_WEEKLY"
                            color="primary"
                          />
                        }
                        label="Enable Weekly Notification"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Weekly Day(sunday[0]-saturday[6])"
                        name="WEEKLY_DAY"
                        value={formData.WEEKLY_DAY}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Weekly Hour(24 hours)"
                        name="WEEKLY_HOUR"
                        value={formData.WEEKLY_HOUR}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Weekly Minute"
                        name="WEEKLY_MINUTE"
                        value={formData.WEEKLY_MINUTE}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.CRON_MONTHLY === "Y"}
                            onChange={handleSwitchChange("CRON_MONTHLY")}
                            name="CRON_MONTHLY"
                            color="primary"
                          />
                        }
                        label="Enable Monthly Notification"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Monthly Day"
                        name="MONTHLY_DAY"
                        value={formData.MONTHLY_DAY}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Monthly Hour(24 hours)"
                        name="MONTHLY_HOUR"
                        value={formData.MONTHLY_HOUR}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Monthly Minute"
                        name="MONTHLY_MINUTE"
                        value={formData.MONTHLY_MINUTE}
                        onChange={handleChange}
                        variant="outlined"
                        size="small"
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom>
                        Notification Settings(Dev)
                      </Typography>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.TEST_CRON_DAILY === "Y"}
                            onChange={handleSwitchChange("TEST_CRON_DAILY")}
                            name="TEST_CRON_DAILY"
                            color="primary"
                          />
                        }
                        label="Enable Test Daily Notification"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.TEST_CRON_WEEKLY === "Y"}
                            onChange={handleSwitchChange("TEST_CRON_WEEKLY")}
                            name="TEST_CRON_WEEKLY"
                            color="primary"
                          />
                        }
                        label="Enable Test Weekly Notification"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.TEST_CRON_MONTHLY === "Y"}
                            onChange={handleSwitchChange("TEST_CRON_MONTHLY")}
                            name="TEST_CRON_MONTHLY"
                            color="primary"
                          />
                        }
                        label="Enable Test Monthly Notification"
                      />
                    </Grid>

                    {/* Add more other settings fields as needed */}
                  </Grid>
                </Paper>

                {/* Seed Settings */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Database Default Data Settings
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Button
                        variant={
                          formData.SEED === "Y" ? "contained" : "outlined"
                        }
                        color="primary"
                        onClick={() => handleButtonClick("SEED")}
                        sx={{ width: "20%", minHeight: "20px" }}
                      >
                        Add Default Units & Tolerances
                      </Button>
                    </Grid>
                    {process.env.NODE_ENV === "development" && (
                      <>
                        <Grid item xs={12}>
                          <Button
                            variant={
                              formData.SEED_USER === "Y"
                                ? "contained"
                                : "outlined"
                            }
                            color="primary"
                            onClick={() => handleButtonClick("SEED_USER")}
                            sx={{ width: "20%", minHeight: "20px" }}
                          >
                            Add Default Users (Dev Feature)
                          </Button>
                        </Grid>
                      </>
                    )}
                    <Grid item xs={12}>
                      <Button
                        variant={
                          formData.SEED_BLOCK === "Y" ? "contained" : "outlined"
                        }
                        color="primary"
                        onClick={() => handleButtonClick("SEED_BLOCK")}
                        sx={{ width: "20%", minHeight: "20px" }}
                      >
                        Add Default Blocks
                      </Button>
                    </Grid>
                  </Grid>
                </Paper>

                {/* Log Settings */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Logs & Events Settings
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Button
                        variant="outlined"
                        color="primary"
                        onClick={() => {
                          bugout.downloadLog();
                        }}
                        sx={{ width: "20%", minHeight: "20px" }}
                      >
                        Download Frontend Logs
                      </Button>
                    </Grid>

                    <Grid item xs={12}>
                      <Button
                        variant="outlined"
                        color="primary"
                        onClick={handleDownloadLogs}
                        sx={{ width: "20%", minHeight: "20px" }}
                      >
                        Download Backend Logs
                      </Button>
                    </Grid>

                    <Grid item xs={12}>
                      <Button
                        variant="outlined"
                        color="primary"
                        onClick={downloadCSVCounts}
                        sx={{ width: "20%", minHeight: "20px" }}
                      >
                        Download CSV counts
                      </Button>
                    </Grid>

                    <Grid item xs={12}>
                      <Button
                        variant="outlined"
                        color="primary"
                        onClick={downloadConfig}
                        sx={{ width: "20%", minHeight: "20px" }}
                      >
                        Download configuration excel
                      </Button>
                    </Grid>
                  </Grid>
                </Paper>

                {/* Log Settings */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Test configuration
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TestForm />
                    </Grid>
                  </Grid>
                </Paper>

                {/* Roles Manager */}
                <RolesManager
                  roles={formData.ROLES}
                  setRoles={(newRoles) =>
                    setFormData({ ...formData, ROLES: newRoles })
                  }
                />

                {/* Buttons Section */}
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    gap: 2,
                    marginTop: 2,
                  }}
                >
                  <Button type="submit" variant="contained" color="primary">
                    Save
                  </Button>
                </Box>
              </form>
            </Box>
          </Box>
        )}
      </Grid>
    </Grid>
  );
};

export default ENV;
