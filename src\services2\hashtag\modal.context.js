import axios from "axios";
import { createContext, useState, useContext } from "react";
import { useNavigate } from "react-router-dom";
import { companies, companyId_constant } from "../../constants/data";
import { useMaintenanceInfoSeter } from "../../context/MaintenanceContext";
import { db } from "../../firebase";
import { dbConfig } from "../../infrastructure/db/db-config";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import { HashtagContext } from "./hashtag.context";
import { useMongoRefresh } from "../../services/mongo-refresh.context";

export const ModalContext = createContext();

const ModalProvider = ({ children }) => {
  const { refreshHashtagData } = useContext(HashtagContext);
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const [hashtagAdded, setHA] = useState("");
  const [maintType, setMT] = useState(-1);
  const { open, setOpen } = useContext(HashtagContext);
  const [maintenance, setMaintenance] = useState({});
  const [val, setVal] = useState("");
  const [step, setStep] = useState({});
  const [hashtagArray, setHashtagArray] = useState([]);
  const handleAddHashTag = async (e) => {
    e.preventDefault();
    if (hashtagAdded.trim() === "") {
      toastMessage({ message: "Hashtag cannot be empty!" });
      setHA("");
      return;
    }

    try {
      // Check for existing hashtags to avoid duplicates
      const response = await axios.get(`${dbConfig.url}/hashtags`, {
        params: { maintenance_id: maintenance.maintenance_id, type: maintType },
      });
      const existingHashtags = response.data.data.map((h) =>
        h.title.toLowerCase(),
      );
      if (existingHashtags.includes(hashtagAdded.trim().toLowerCase())) {
        toastMessage({
          message: "Hashtag already exists for this maintenance!",
        });
        setHA("");
        return;
      }

      // Post new hashtag
      await axios.post(`${dbConfig.url}/hashtags`, {
        maintenance_id: maintenance.maintenance_id,
        title: hashtagAdded.trim(),
        type: maintType,
        // step_id is omitted for maintenance hashtags
      });

      toastMessageSuccess({ message: "Hashtag successfully added!" });
      refreshHashtagData();
      setHA("");
      setOpen(false);
    } catch (error) {
      console.error("Error adding hashtag:", error);
      toastMessage({ message: "Failed to add hashtag. Please try again." });
    }
  };

  const addFun = async (e) => {
    e.preventDefault();

    if (!hashtagArray || hashtagArray.length === 0) {
      toastMessage({ message: "Please add at least one hashtag" });
      return;
    }

    try {
      console.log("hashtagArray:", hashtagArray); // Debug input hashtags
      const hashtagPromises = hashtagArray.map((hash) =>
        axios.post(`${dbConfig.url}/hashtags`, {
          maintenance_id: step.manual_id,
          title: hash,
          step_id: step._id,
          type: maintType,
        }),
      );

      const hashtagResponses = await Promise.all(hashtagPromises);
      const newHashtagIds = hashtagResponses
        .map((response) => response.data.data._id)
        .filter((id) => id != null);
      console.log("newHashtagIds:", newHashtagIds); // Debug hashtag IDs

      if (newHashtagIds.length === 0) {
        toastMessage({
          message: "No valid hashtags were created. Please try again.",
        });
        return;
      }

      const existingHashtags = (step.hashtag || []).filter((id) => id != null);
      console.log("existingHashtags:", existingHashtags); // Debug existing hashtags

      const updatedStepData = {
        ...step,
        hashtag: [...existingHashtags, ...newHashtagIds],
      };
      console.log("updatedStepData:", updatedStepData); // Debug final step data

      await axios.put(`${dbConfig.url}/stepdata/${step._id}`, updatedStepData);

      toastMessageSuccess({ message: "Hashtags added successfully!" });
      refreshHashtagData();
      setHashtagArray([]);
      setVal("");
      setOpen(false);
      setRefreshCount(refreshCount + 1);
    } catch (error) {
      console.error("Error adding hashtags:", error);
      toastMessage({ message: "Failed to add hashtags. Please try again." });
    }
  };

  return (
    <ModalContext.Provider
      value={{
        handleAddHashTag,
        hashtagAdded,
        setHA,
        maintenance,
        setMaintenance,
        step,
        setStep,
        addFun,
        val,
        setVal,
        maintType,
        setMT,
        hashtagArray,
        setHashtagArray,
      }}
    >
      {children}
    </ModalContext.Provider>
  );
};

export default ModalProvider;
