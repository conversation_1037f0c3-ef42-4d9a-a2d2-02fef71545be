import { Link } from "react-router-dom";
import { ButtonBasic } from "../../components/buttons/Buttons";
import PageHeader from "../../components/commons/page-header.component";
import { useStateContext } from "../../context/ContextProvider";
import { sharedCss } from "../../styles/sharedCss";
import { makeStyles } from "@mui/styles";
import Button from "@mui/material/Button";
import Box from "@mui/material/Box";
import { Typography } from "@mui/material";

const useCustomStyles = makeStyles((theme) => ({
  userContainer: {
    padding: "1rem",
    borderRadius: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  usersOuterContainer: {
    width: "100%",
  },
  usersInnerContainer: {
    display: "flex",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  addButton: {
    width: "max-content",
  },
  userPageContainer: {
    padding: "1rem",
    border: "1px solid gainsboro",
  },
}));

const UserInfoContainer = () => {
  const { currentMode, currentColorLight } = useStateContext();
  const commonCss = sharedCss();
  const customCss = useCustomStyles();
  return (
    <header
      className={commonCss.headingContainer}
      style={{ display: "flex", alignItems: "center" }}
    >
      <div>
        <Box sx={{ padding: "0.5rem 0.75rem" }}>
          <Typography variant="h4">User Management</Typography>
          <Typography variant="h6">List of all Admins & Users</Typography>
        </Box>
        {/* <PageHeader
					title='User Management'
					subText='List of all Admins & Users'
				/> */}
      </div>
      <div className="mr-6">
        <Link to="/add-user">
          <Button className={customCss.addButton} variant="contained">
            Add New User
          </Button>
        </Link>
      </div>
    </header>
  );
};

export default UserInfoContainer;
