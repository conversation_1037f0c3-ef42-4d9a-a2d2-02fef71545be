import React from "react";
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { useRef, useEffect, useState } from "react";
import { companies, companyId_constant, machines } from "../../constants/data";
import { db, storage } from "../../firebase";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";

const Add3dModel = ({
  onClose,
  setMachineData,
  machinesData,
  getRecentModel,
}) => {
  const [modelName, setModelName] = useState("");
  const [modelDescription, setModelDescription] = useState("");
  const [machineId, setMachineId] = useState("");
  const [modelUrl, setModelUrl] = useState(null);
  const inputRef = useRef();
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState(null);

  // Validation state for input errors
  const [nameError, setNameError] = useState(false);
  const [descriptionError, setDescriptionError] = useState(false);

  const addData = async (
    modelName,
    modelDescription,
    machineId,
    selectedFile,
    setUploadProgress,
    modelUrl,
  ) => {
    // Trim inputs before submission
    const trimmedName = modelName.trim();
    const trimmedDescription = modelDescription.trim();

    if (!trimmedName || !trimmedDescription) {
      toastMessageWarning({
        message: "Title and Description cannot be empty or just spaces",
      });
      return;
    }

    if (selectedFile && modelUrl) {
      const testmodel = {
        created_at: new Date(),
        desc: trimmedDescription,
        mid: machineId,
        url: modelUrl, // Use the URL from storage
        name: trimmedName,
      };

      await axios
        .post(`${dbConfig.url}/model`, testmodel)
        .then((res) => {
          toastMessageSuccess({ message: "Model Created successfully !" });
          onClose();
          getRecentModel();
        })
        .catch((err) => {
          toastMessage({ message: "Failed to create model" });
        });
    } else {
      toastMessageWarning({
        message: "No valid file selected or upload failed",
      });
    }
  };

  const handleUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      console.log(file.size, "file size");
      if (file.size > 30 * 1024 * 1024) {
        // 30 MB limit
        toastMessageWarning({ message: "Model Size must be less than 30 MB" });
        event.target.value = ""; // Reset the input field
        setSelectedFile(null);
        setModelUrl(null);
        return;
      }

      try {
        // Upload file to storage
        const fd = new FormData();
        fd.append("image", file);
        const res = await axios.post(`${dbConfig?.url_storage}/upload`, fd, {
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total,
            );
            setUploadProgress(percentCompleted);
          },
        });

        if (res?.data?.data) {
          setModelUrl(res.data.data); // Store the URL from the storage response
          setSelectedFile(file); // Store the file for validation
          toastMessageSuccess({ message: "3D Model uploaded successfully!" });
          console.log("File uploaded, URL:", res.data.data);
        } else {
          throw new Error("No URL returned from storage");
        }
      } catch (error) {
        console.error("Error uploading file:", error);
        toastMessageWarning({ message: "Failed to upload 3D model" });
        event.target.value = ""; // Reset the input field
        setSelectedFile(null);
        setModelUrl(null);
      }
    }
  };

  // Handle modelName change
  const handleModelNameChange = (e) => {
    const value = e.target.value;
    setModelName(value);
    // Check for leading space
    if (value.startsWith(" ")) {
      setNameError(true);
    } else {
      setNameError(false);
    }
  };

  // Handle modelDescription change
  const handleModelDescriptionChange = (e) => {
    const value = e.target.value;
    setModelDescription(value);
    // Check for leading space
    if (value.startsWith(" ")) {
      setDescriptionError(true);
    } else {
      setDescriptionError(false);
    }
  };

  return (
    <Box>
      <TextField
        required
        sx={{ mb: 3, mt: 3 }}
        label="Title"
        fullWidth
        placeholder="eg: Vaccum PID"
        value={modelName}
        onChange={handleModelNameChange}
        error={nameError}
        helperText={nameError ? "Title cannot start with a space" : ""}
      />

      <TextField
        required
        sx={{ mb: 3 }}
        fullWidth
        rows={4}
        multiline
        value={modelDescription}
        onChange={handleModelDescriptionChange}
        label="Description"
        placeholder="eg: This is the 3D Model of a Motor"
        error={descriptionError}
        helperText={
          descriptionError ? "Description cannot start with a space" : ""
        }
      />

      <FormControl fullWidth>
        <InputLabel>Machine</InputLabel>
        <Select
          required
          label="Machine"
          sx={{ mb: 3 }}
          onChange={(e) => setMachineId(e.target.value)}
          value={machineId}
        >
          {machinesData?.map((data) => (
            <MenuItem key={data?._id} value={data?._id}>
              {data?.title}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <InputLabel sx={{ mb: 1 }}>3D Model</InputLabel>
      <input
        style={{ marginBottom: "20px" }}
        ref={inputRef}
        type="file"
        accept=".glb,.gltf"
        onChange={handleUpload}
      />
      {uploadProgress > 0 && uploadProgress < 100 && (
        <LinearProgress variant="determinate" value={uploadProgress} />
      )}

      <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
        <Button
          variant="contained"
          onClick={() => {
            onClose();
            getRecentModel();
          }}
          color="error"
        >
          Cancel
        </Button>
        <Button
          onClick={() => {
            addData(
              modelName,
              modelDescription,
              machineId,
              selectedFile,
              setUploadProgress,
              modelUrl,
            );
          }}
          disabled={
            !selectedFile ||
            !modelName.trim() ||
            !modelDescription.trim() ||
            !machineId ||
            nameError ||
            descriptionError
          }
          variant="contained"
        >
          Add Model
        </Button>
      </Box>
    </Box>
  );
};

export default Add3dModel;
