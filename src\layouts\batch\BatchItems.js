import React, { useState, useEffect } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { useStateContext } from "../../context/ContextProvider";
import Carousel from "react-material-ui-carousel";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import {
  batch,
  companies,
  companyId_constant,
  media,
} from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { ExpandMore } from "@mui/icons-material";
import { Dialog, DialogContent, DialogTitle, IconButton } from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import EditBatch from "./EditBatch";

export default function BatchDataItems({ batchData, searchKeyWordBatch }) {
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const [batchActiveMedias, setBatchActiveMedias] = useState([]);
  const [itemsExpanded, setItemExpanded] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);

  // useEffect(() => {
  //     db.collection(companies).doc(companyId_constant).collection(batch).doc(batchData?.id).collection(media)
  //         .onSnapshot((snap) => {
  //             const data = firebaseLooper(snap);
  //             setBatchActiveMedias(data);
  //             console.log("batch media: ", data)
  //         })
  // }, [])

  //
  const handelActionOpenClose = () => {
    if (!itemsExpanded) {
      // db.collection(companies).doc(companyId_constant).collection(batch).doc(batchData?.id).collection(media)
      //     .onSnapshot((snap) => {
      //         const data = firebaseLooper(snap);
      //         setBatchActiveMedias(data);
      //         setItemExpanded(true)
      //         console.log("batch media: ", data)
      //     })
    } else {
      setItemExpanded(false);
      //setBatchActiveMedias([]);
    }
  };

  return (
    <>
      <TableRow
        key={batchData.id}
        // sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
        style={{
          cursor: "pointer",
          borderBottom:
            currentMode === "Dark" ? ".5px solid #444" : ".5px solid #ddd",
        }}
        className={
          !itemsExpanded
            ? "hover:shadow-md hover:shadow-gray-400"
            : "shadow-inner"
        }
      >
        <TableCell
          style={{ borderBottom: "none", textTransform: "capitalize" }}
          align="left"
        >
          {batchData?.product_name}
        </TableCell>

        <TableCell
          style={{ borderBottom: "none", textTransform: "capitalize" }}
          align="left"
        >
          {batchData?.batch_no}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="left">
          {batchData?.count}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="left">
          {batchData?.date?.toDate().toString().substring(0, 15)}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="left">
          {batchData?.email}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="left">
          <IconButton onClick={() => handelActionOpenClose()}>
            {itemsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>

          <IconButton onClick={() => setOpenEdit(true)}>
            <EditIcon style={{ fontSize: "20px", color: "#00f" }} />
          </IconButton>
        </TableCell>
      </TableRow>

      {itemsExpanded && (
        <TableRow>
          <TableCell colSpan={6}>
            <div className=" flex justify-center w-full">
              <div className="  w-3/4   ">
                <>
                  <Carousel
                    // index= {index}
                    NextIcon={<NavigateNextIcon />}
                    PrevIcon={<NavigateBeforeIcon />}
                    fullHeightHover={false}
                    cycleNavigation={true}
                    navButtonsProps={{
                      // Change the colors and radius of the actual buttons. THIS STYLES BOTH BUTTONS
                      style: {
                        backgroundColor: "#666",
                        color: "#fff",
                        borderRadius: "50%",
                        padding: ".75rem",
                        margin: "0 1.5rem",
                      },
                    }}
                    indicators={true}
                    autoPlay={true}
                    animation="slide"
                    duration="800"
                    navButtonsAlwaysVisible={true}
                    style={currentMode === "Dark" ? { background: "#168" } : {}}
                  >
                    {batchActiveMedias
                      ?.sort((a, b) => a.index - b.index)
                      ?.map((data, index) => (
                        <div
                          key={index}
                          className=" flex justify-center h-96  py-2"
                        >
                          <img src={data?.url} width="300" height="300" />
                        </div>
                      ))}
                  </Carousel>
                </>
              </div>
            </div>
          </TableCell>
        </TableRow>
      )}

      {/* //edit */}
      <Dialog open={openEdit} fullWidth>
        <DialogTitle>Edit Batch - [{batchData?.product_name}]</DialogTitle>
        <DialogContent>
          <EditBatch handleClose={() => setOpenEdit(false)} data={batchData} />
        </DialogContent>
      </Dialog>
    </>
  );
}
