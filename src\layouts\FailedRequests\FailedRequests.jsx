import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
} from "@mui/material";
import { getFailedRequests, deleteFailedRequest } from "./../../services/db";

import axios from "axios";

const FILTER_METHOD_LABEL = "Method";
const ALL_METHODS = "All";
const GET_METHOD = "GET";
const POST_METHOD = "POST";
const PUT_METHOD = "PUT";
const DELETE_METHOD = "DELETE";
const PATCH_METHOD = "PATCH";
const COLUMN_ID = "ID";
const COLUMN_METHOD = "Method";
const COLUMN_URL = "URL";
const COLUMN_DATA = "Data";
const COLUMN_TIMESTAMP = "Timestamp";
const COLUMN_ACTIONS = "Actions";
const RETRY_BUTTON_TEXT = "Retry";
const RETRY_ERROR_MESSAGE = "Failed to retry request:";

const FailedRequestsTable = () => {
  const [requests, setRequests] = useState([]);
  const [filteredRequests, setFilteredRequests] = useState([]);
  const [methodFilter, setMethodFilter] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  useEffect(() => {
    async function fetchData() {
      const data = await getFailedRequests();
      setRequests(data);
      setFilteredRequests(data);
    }

    fetchData();
  }, []);

  const handleFilterChange = (event) => {
    const method = event.target.value;
    setMethodFilter(method);

    const filtered = method
      ? requests.filter(
          (request) => request.method.toLowerCase() === method.toLowerCase(),
        )
      : requests;

    setFilteredRequests(filtered);
    setPage(0); // Reset to first page on filter change
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0); // Reset to first page when rows per page changes
  };

  const handleRetry = async (request) => {
    try {
      await axios({
        method: request.method,
        url: request.url,
        data: request.data,
        headers: request.headers,
      });

      // On successful retry, remove the request from IndexedDB
      await deleteFailedRequest(request.id);
      setFilteredRequests(filteredRequests.filter((r) => r.id !== request.id));
    } catch (error) {
      console.error(RETRY_ERROR_MESSAGE, error);
      // Optionally, you can show a notification or alert the user that the retry failed.
    }
  };

  return (
    <Paper>
      <FormControl sx={{ m: 2, minWidth: 120 }}>
        <InputLabel id="filter-method-label">{FILTER_METHOD_LABEL}</InputLabel>
        <Select
          labelId="filter-method-label"
          value={methodFilter}
          label={FILTER_METHOD_LABEL}
          onChange={handleFilterChange}
        >
          <MenuItem value="">{ALL_METHODS}</MenuItem>
          <MenuItem value={GET_METHOD}>{GET_METHOD}</MenuItem>
          <MenuItem value={POST_METHOD}>{POST_METHOD}</MenuItem>
          <MenuItem value={PUT_METHOD}>{PUT_METHOD}</MenuItem>
          <MenuItem value={DELETE_METHOD}>{DELETE_METHOD}</MenuItem>
          <MenuItem value={PATCH_METHOD}>{PATCH_METHOD}</MenuItem>
        </Select>
      </FormControl>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{COLUMN_ID}</TableCell>
              <TableCell>{COLUMN_METHOD}</TableCell>
              <TableCell>{COLUMN_URL}</TableCell>
              <TableCell>{COLUMN_DATA}</TableCell>
              <TableCell>{COLUMN_TIMESTAMP}</TableCell>
              <TableCell>{COLUMN_ACTIONS}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredRequests
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((request) => (
                <TableRow key={request.id}>
                  <TableCell>{request.id}</TableCell>
                  <TableCell>{request.method}</TableCell>
                  <TableCell>{request.url}</TableCell>
                  <TableCell>{JSON.stringify(request.data)}</TableCell>
                  <TableCell>
                    {new Date(request.timestamp).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => handleRetry(request)}
                    >
                      {RETRY_BUTTON_TEXT}
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        component="div"
        count={filteredRequests.length}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[5, 10, 25]}
      />
    </Paper>
  );
};

export default FailedRequestsTable;
