import { <PERSON>, Button, Grid, Typography } from "@mui/material";
import React, { useState } from "react";
import EquimentUsedTable from "./equipment-used.table";
import * as XLSX from "xlsx";

const CalibrationDataForm = ({ item, masterEquip }) => {
  const [excelFile, setExcelFile] = useState(null);
  const [excelFileError, setExcelFileError] = useState(null);

  // submit
  const [excelData, setExcelData] = useState(null);
  // it will contain array of objects

  // handle File
  const fileType = [
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ];
  const handleFile = (e) => {
    let selectedFile = e.target.files[0];
    if (selectedFile) {
      // console.log(selectedFile.type);
      if (selectedFile && fileType.includes(selectedFile.type)) {
        let reader = new FileReader();
        reader.readAsArrayBuffer(selectedFile);
        reader.onload = (e) => {
          setExcelFileError(null);
          setExcelFile(e.target.result);
        };
      } else {
        setExcelFileError("Please select only excel file types");
        setExcelFile(null);
      }
    } else {
      console.log("plz select your file");
    }
  };

  // submit function
  const handleSubmit = (e) => {
    e.preventDefault();
    if (excelFile !== null) {
      const workbook = XLSX.read(excelFile, { type: "buffer" });
      const worksheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[worksheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);
      setExcelData(data);
    } else {
      setExcelData(null);
    }
  };

  return (
    <div>
      {/* Excel data to table */}
      {/* <form className='form-group' autoComplete="off"
        onSubmit={handleSubmit}>
          <label><h5>Upload Excel file</h5></label>
          <br></br>
          <input type='file' className='form-control'
          onChange={handleFile} required></input>                  
          {excelFileError&&<div className='text-red-500'
          style={{marginTop:5+'px'}}>{excelFileError}</div>}
          <Button type='submit' variant="contained" color="primary"
          style={{marginTop:5+'px'}}>Submit</Button>
        </form> */}

      <Box sx={{ border: "1px solid black", display: "flex" }}>
        {/* <Grid sx={{p: 2 , borderRight: '1px solid black',width: '50%'}}>
                <Typography>
                    Customer: 
                </Typography>
            </Grid> */}
        <Grid sx={{ width: "100%" }}>
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">
              Cal Centrificate No. : {item?.certificate_no}
            </Typography>
          </Box>
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">
              Date of Calibration : {item?.done_on}
            </Typography>
          </Box>
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">
              Calibration Due on : {item?.due_date}
            </Typography>
          </Box>
          <Box sx={{ p: 2 }}>
            <Typography variant="body2">Criticality : Medium</Typography>
          </Box>
        </Grid>
      </Box>
      <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
        <Typography variant="body2">
          Details of Unit Under Calibration :{" "}
        </Typography>
      </Box>
      <Box
        sx={{
          border: "1px solid black",
          display: "flex",
          justifyContent: "",
          borderTop: "none",
        }}
      >
        <Grid sx={{ borderRight: "1px solid black", width: "50%" }}>
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">
              Instrument Name: {item?.code_no}
            </Typography>
          </Box>
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">Make : {item?.make}</Typography>
          </Box>
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">Model: {item?.make}</Typography>
          </Box>
          {/* <Box sx={{p: 2, borderBottom:'1px solid black'}}>
                  <Typography>
                   Serial Number: 
                </Typography>  
                </Box> */}
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">Range: {item?.range}</Typography>
          </Box>

          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">Environmental Condition: </Typography>
          </Box>

          <Box sx={{ p: 2 }}>
            <Typography variant="body2">ID Number:</Typography>
          </Box>
        </Grid>
        <Grid sx={{ width: "50%" }}>
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">Location : {item?.location}</Typography>
          </Box>
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">Accuracy :</Typography>
          </Box>
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">
              Operating Range : {item?.range}
            </Typography>
          </Box>
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">
              Equipment / Plant : {item?.plant}
            </Typography>
          </Box>
          <Box sx={{ p: 2, borderBottom: "1px solid black" }}>
            <Typography variant="body2">LC/Resolution :</Typography>
          </Box>
        </Grid>
      </Box>
      <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
        <Typography variant="body2">Calibration Procedure Number : </Typography>
      </Box>
      <Box>
        {/* 5 boxes */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderTop: "none",
          }}
        >
          {/* Sr no0 */}
          <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
            <Typography variant="body2" align="center">
              Name{" "}
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
            <Typography variant="body2" align="center">
              Make / Model{" "}
            </Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
            <Typography variant="body2" align="center">
              SI Number
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
            <Typography variant="body2" align="center">
              Certificate Number
            </Typography>
          </Box>
          <Box sx={{ p: 1, width: `25%` }}>
            <Typography variant="body2" align="center">
              Validity
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* equipments mapping area */}
      {masterEquip.map((item, idx) => (
        <EquipmentTableItem masterEquip={item} />
      ))}

      {/* Table ends master equipment */}

      <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
        <Typography variant="body2">Result : </Typography>
      </Box>

      {/* Result custom table */}
      <Box>
        {/* 5 boxes */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderTop: "none",
          }}
        >
          {/* Sr no0 */}
          <Box sx={{ p: 1, borderRight: "1px solid black", minWidth: "60px" }}>
            <Typography variant="body2" align="center">
              Sl No.{" "}
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "500px" }}>
            <Box sx={{ borderBottom: "1px solid black" }}>
              <Typography
                variant="body2"
                sx={{ mb: 5 }}
                gutterBottom
                align="center"
              >
                Standard Values.{" "}
              </Typography>
            </Box>

            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <Box
                sx={{ p: 1, borderRight: "1px solid black", width: "200px" }}
              >
                <Typography variant="body2">Readings </Typography>
              </Box>
              <Box sx={{ p: 1, width: "200px" }}>
                <Typography variant="body2"> Equivalent Readings </Typography>
              </Box>
            </Box>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
            <Typography variant="body2" align="center">
              Observed Values <br /> before Adjustment
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
            <Typography variant="body2" align="center">
              Observed Values <br /> after Adjustment{" "}
            </Typography>
          </Box>
          <Box sx={{ p: 1, width: `25%` }}>
            <Typography variant="body2" align="center">
              Observed Error in (+ / -)
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Data Item  */}
      <DataItem />
      <DataItem />
      <DataItem />
      <DataItem />
      <DataItem />

      {/* 

          Remarks

        */}

      <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
        <Typography variant="body2" sx={{ mb: 10 }}>
          Remarks : {item?.remarks}
        </Typography>

        <Box sx={{ display: "flex" }}>
          <Typography sx={{ width: "45%" }} variant="body2">
            Calibrated by (Sign):{" "}
          </Typography>
          <Typography variant="body2">Checked By(Mylan): </Typography>
        </Box>
      </Box>
    </div>
  );
};

export default CalibrationDataForm;

export const EquipmentTableItem = ({ masterEquip }) => {
  return (
    <Box>
      {/* 5 boxes */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          border: "1px solid black",
          borderTop: "none",
        }}
      >
        {/* Sr no0 */}
        <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
          <Typography variant="body2" align="center">
            {masterEquip?.name}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
          <Typography variant="body2" align="center">
            {" "}
            {masterEquip?.make}{" "}
          </Typography>
        </Box>

        <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
          <Typography variant="body2" align="center">
            {masterEquip?.si_num}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
          <Typography variant="body2" align="center">
            {masterEquip?.certificate_no}
          </Typography>
        </Box>
        <Box sx={{ p: 1, width: `25%` }}>
          <Typography variant="body2" align="center">
            {masterEquip?.validity}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export const DataItem = () => {
  return (
    <>
      {/* Content / data mapping goes here */}

      <Box>
        {/* 5 boxes */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderTop: "none",
          }}
        >
          {/* Sr no0 */}
          <Box sx={{ p: 1, borderRight: "1px solid black", minWidth: "60px" }}>
            <Typography align="center">1 </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "500px" }}>
            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <Box
                sx={{ p: 1, borderRight: "1px solid black", width: "200px" }}
              >
                <Typography> </Typography>
              </Box>
              <Box sx={{ p: 1, width: "200px" }}>
                <Typography> </Typography>
              </Box>
            </Box>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
            <Typography align="center"> </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: `25%` }}>
            <Typography align="center"> </Typography>
          </Box>
          <Box sx={{ p: 1, width: `25%` }}>
            <Typography align="center"></Typography>
          </Box>
        </Box>
      </Box>
    </>
  );
};
