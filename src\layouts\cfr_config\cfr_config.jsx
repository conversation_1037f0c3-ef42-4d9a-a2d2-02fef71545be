import React, { useEffect, useState } from "react";
import {
  Table<PERSON>ontainer,
  TableRow,
  Table,
  TableHead,
  TableBody,
  TableCell,
  Button,
  Grid,
  Typography,
  Box,
  Paper,
  Card,
} from "@mui/material";
import axios from "axios";
import { toast } from "react-toastify";
import { dbUrl } from "../../constants/db";

import ConfigEditModal from "./cfr_config_edit";
import { useUtils } from "../../hooks/UtilsProvider";
import PropTypes from "prop-types";

const TITLE = "Audit Trail Configuration Settings";
const ERROR_GETTING_SETTINGS =
  "Error getting environment settings, please contact admin";
const SAVED_SUCCESSFULLY = "Saved Successfully";
const ERROR_WHILE_SAVING = "Error while saving";
const NO_COLUMN = "No";
const ACTIVITY_COLUMN = "Activity";
const DESCRIPTION_COLUMN = "Description";
const ACTION_COLUMN = "Action";
const EDIT_BUTTON_TEXT = "Edit";

const CFRConfig = () => {
  const { envData } = useUtils();
  const [formData, setFormData] = useState(null);
  const [itemEdit, setItemEdit] = useState(null);
  const [itemEditIndex, setItemEditIndex] = useState(null);
  const [editModalOpen, setEditModalOpen] = useState(false);

  const getCFRConfigData = async () => {
    const response = await axios.get(`${dbUrl}/cfrconfig`);
    if (response.data) {
      setFormData(response.data);
    } else {
      toast.error(ERROR_GETTING_SETTINGS);
    }
  };

  const handleSubmit = async (item) => {
    const payload = [...formData];
    payload[itemEditIndex] = item;
    axios
      .put(`${dbUrl}/cfrconfig/update`, payload)
      .then(() => {
        getCFRConfigData();
        toast(SAVED_SUCCESSFULLY);
      })
      .catch(() => {
        toast(ERROR_WHILE_SAVING);
      });
  };

  useEffect(() => {
    getCFRConfigData();
  }, []);

  const handleEditItem = (item, index) => {
    setItemEditIndex(index);
    setItemEdit(item);
    setEditModalOpen(true);
  };

  return (
    <>
      <Grid container>
        <Grid item xs={12}>
          <Card
            elevation={3}
            style={{ marginBottom: "1rem", padding: "1rem 1rem" }}
          >
            <Box>
              <Typography variant="h4" sx={{ margin: "1rem" }}>
                {TITLE}
              </Typography>
            </Box>
          </Card>
          {formData !== null && (
            <Box>
              <Box sx={{ maxWidth: 1840, p: 3 }}>
                <TableContainer component={Paper}>
                  <Table>
                    {/* Table Header */}
                    <TableHead>
                      <TableRow>
                        <TableCell>{NO_COLUMN}</TableCell>
                        <TableCell>{ACTIVITY_COLUMN}</TableCell>
                        <TableCell>{DESCRIPTION_COLUMN}</TableCell>
                        <TableCell>{ACTION_COLUMN}</TableCell>
                      </TableRow>
                    </TableHead>
                    {/* Table Body */}
                    <TableBody>
                      {formData.map((item, index) => (
                        <TableRow key={item._id}>
                          <TableCell>{index + 1}</TableCell>
                          <TableCell>{item.activity}</TableCell>
                          <TableCell>{item.description}</TableCell>
                          <TableCell>
                            <Button
                              variant="outlined"
                              color="secondary"
                              onClick={() => handleEditItem(item, index)}
                            >
                              {EDIT_BUTTON_TEXT}
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            </Box>
          )}
        </Grid>
      </Grid>
      <ConfigEditModal
        openModal={editModalOpen}
        setOpenModal={setEditModalOpen}
        data={itemEdit ?? undefined}
        key={`units-form-${itemEdit?._id}`}
        updateConfig={handleSubmit}
      />
    </>
  );
};

ConfigEditModal.propTypes = {
  openModal: PropTypes.bool.isRequired,
  setOpenModal: PropTypes.func.isRequired,
  data: PropTypes.shape({
    unit: PropTypes.string,
    activity: PropTypes.string,
    description: PropTypes.string,
  }),
  updateConfig: PropTypes.func.isRequired,
};

CFRConfig.propTypes = {};

export default CFRConfig;
