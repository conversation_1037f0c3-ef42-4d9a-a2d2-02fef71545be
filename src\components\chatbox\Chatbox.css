.chat {
  flex: 0.65;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.chat__header {
  padding: 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid lightgray;
}

.chat__headerInfo {
  flex: 1;
  padding-left: 15px;
}

.chat__headerInfo > h3 {
  font-weight: 500;
  margin-bottom: 3px;
}

.chat__headerInfo > p {
  color: gray;
}

.chat__headerRight {
  display: flex;
  min-width: 100px;
  justify-content: space-between;
}

.chat__body {
  flex: 1;
  background-repeat: repeat;
  background-position: center;
  padding: 30px;
  overflow: scroll;
  overflow-y: auto;
}

.chat__name {
  position: absolute;
  top: -15px;
  font-weight: 800;
  font-size: x-small;
}

.chat__receiver {
  margin-left: auto;
  background-color: #f7902f !important;
}

.chat__message {
  overflow-y: auto;
  position: relative;
  font-size: 16px;
  padding: 10px;
  background-color: rgb(235, 235, 235);
  border-radius: 10px;
  margin-bottom: 30px;
  width: fit-content;
  padding: 1.125em 1.5em;
  border-radius: 1rem;
  max-width: 30em;
}

.chat__timestamp {
  margin-left: 10px;
  font-size: xx-small;
}

.chat__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 62px;
  border-top: 10x solid lightgray;
}

.chat__footer > form {
  flex: 1;
  display: flex;
}

.chat__messageText {
  word-wrap: break-word;
}

.chat__footer > form > input {
  flex: 1;
  border-radius: 30px;
  padding: 10px;
  border: none;
}

.chat__footer > form > button {
  border: none;
  cursor: pointer;
}

.chat__footer > .MuiSvgIcon-root {
  padding: 10px;
}

.chat__emojiPicker {
  z-index: 99;
  bottom: 120px;
  position: fixed;
}
