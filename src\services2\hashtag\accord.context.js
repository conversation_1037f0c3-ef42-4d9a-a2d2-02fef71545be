import { createContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import { companies, companyId_constant } from "../../constants/data";
import { useMaintenanceInfoSeter } from "../../context/MaintenanceContext";
import { toastMessageWarning } from "../../tools/toast";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";

export const AccordContext = createContext();

const AccordProvider = ({ children }) => {
  const maintenanceInfoSetter = useMaintenanceInfoSeter();
  const history = useNavigate();

  const maintenanceData = async (hash, target) => {
    try {
      console.log(`Fetching maintenance for ID: ${target}`);
      const response = await axios.get(
        `${dbConfig?.url}/maintenance/${target}`,
      );
      console.log("Maintenance API response:", response.data);
      return response.data;
    } catch (error) {
      console.error(
        "Maintenance API error:",
        error.response?.data || error.message,
      );
      if (error.response?.status === 404) {
        toastMessageWarning({ message: `Maintenance ID ${target} not found` });
      } else {
        toastMessageWarning({ message: "Failed to fetch maintenance data" });
      }
      throw error;
    }
  };

  const handleGoTo = async (hash, target) => {
    console.log("handleGoTo called with hash:", hash, "target:", target);
    if (!target) {
      console.error("Missing maintenance ID");
      toastMessageWarning({ message: "Invalid maintenance ID" });
      return;
    }
    // if (!hash?.step_id) {
    //   console.error("Missing step_id in hash:", hash);
    //   toastMessageWarning({ message: "Step ID is required for redirection" });
    //   return;
    // }

    try {
      const data = await maintenanceData(hash, target);
      const maintenanceId = data?.data?.mid || data?.mid;
      console.log("Extracted maintenanceId:", maintenanceId);
      if (maintenanceId) {
        maintenanceInfoSetter(hash);
        history(`/maintenance/${maintenanceId}`);
        console.log("Redirecting to maintenance:", maintenanceId);
      } else {
        console.warn("Maintenance ID not found in response:", data);
        toastMessageWarning({ message: "Maintenance is not available" });
      }
    } catch (error) {
      console.error("handleGoTo error:", error);
    }
  };

  return (
    <AccordContext.Provider value={{ handleGoTo }}>
      {children}
    </AccordContext.Provider>
  );
};

export default AccordProvider;
