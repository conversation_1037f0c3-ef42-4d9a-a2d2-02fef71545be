.settingSection {
  .infoContainer {
    width: 100%;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    color: #344767;
    padding: 1.5rem 1.2rem;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .info {
      display: flex;
      flex-direction: column;
      h3 {
        font-size: 1.4rem;
        font-weight: 500;
        margin-bottom: 0.7rem;
      }
      p {
        font-size: 0.9rem;
        opacity: 0.9;
      }
    }

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      .btns {
        padding: 0.7rem 1.8rem;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
        outline: none;
        cursor: pointer;
        border-radius: 8px;
        border: none;
        color: #fff;
        box-shadow:
          rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
          rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
        &:hover {
          opacity: 0.85;
        }
      }
      .mycontainer {
        position: absolute;
        top: 0;
        right: 0;
      }
      .mobileApp {
        background-image: linear-gradient(
          310deg,
          rgb(33, 82, 255),
          rgb(33, 212, 253)
        );
      }
      .glassApp {
        margin-left: 2rem;
        background-image: linear-gradient(
          to right top,
          #393977,
          #2e3069,
          #23275b,
          #191e4d,
          #0e1640
        );
      }
    }
  }
  .container {
    display: flex;
    justify-content: space-between;
  }

  .left-div,
  .right-div {
    width: 50%;
  }
  .apkFormContainer {
    display: flex;
    align-items: right;
    justify-content: right;
    margin-top: 2.5rem;

    .glassFormContainer {
      margin-left: 2rem;
    }

    .mobileFormContainer,
    .glassFormContainer {
      width: 50%;
      box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      // color: #344767;
      .title {
        padding: 0 1.2rem;
        padding-top: 1.5rem;
        margin-bottom: 0.4rem;
        h3 {
          text-transform: uppercase;
          font-size: 1.1rem;
          font-weight: 600;
          opacity: 0.98;
        }
      }

      .desc {
        padding: 0 1.2rem;
        p {
          font-size: 0.9rem;
          opacity: 0.7;
        }
      }

      .formInner {
        padding: 0 1.2rem;
        width: 100%;

        .labelFields {
          margin: 1.5rem 0.5rem;
          label {
            font-size: 1.2rem;
            font-weight: 500;
            // color: #344767;
            margin-bottom: 0;
          }
          .MuiInputBase-root {
            width: 100%;
          }

          .MuiAutocomplete-input {
            font-size: 0.9rem;
            // color: #344767;
            opacity: 0.8;
          }
          .MuiDropzoneArea-textContainer {
            p {
              font-size: 1.1rem;
              font-weight: 500;
            }
            .MuiDropzoneArea-icon {
              color: #344767;
            }
          }
        }

        .buttons {
          margin: 1rem 0;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .createBtn {
            background-image: linear-gradient(
              to right top,
              #393977,
              #2e3069,
              #23275b,
              #191e4d,
              #0e1640
            );
            color: #fff;
            padding: 0.7rem 3.2rem;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
            outline: none;
            cursor: pointer;
            border-radius: 8px;
            border: none;
            box-shadow:
              rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
              rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
            &:hover {
              opacity: 0.85;
            }
          }
        }
      }
    }
  }
}
