import React, { useState } from "react";
import "./addCompany.scss";

import { companies } from "../../constants/data";
import { alpha, styled } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import InputLabel from "@mui/material/InputLabel";
import { Link } from "react-router-dom";
import { Box, TextField } from "@mui/material";
import Autocomplete, { createFilterOptions } from "@mui/material/Autocomplete";
import ColorPicker from "./ColorPicker";
import { db } from "../../firebase";
import { DropzoneArea } from "material-ui-dropzone";
import { useStorage } from "../../hooks/useStorage";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import { message } from "antd";
import { useNavigate } from "react-router-dom";
import { useStateContext } from "../../context/ContextProvider";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";
import { useEffect } from "react";

const AddCompany = () => {
  const [companyName, setCompanyName] = useState();
  const [companyDomain, setCompanyDomain] = useState();
  const [theme, setTheme] = useState();
  const [description, setDescription] = useState();
  const [file, setFile] = useState();
  const history = useNavigate();
  const { currentColorLight, currentMode } = useStateContext();
  const [companyID, setCompanyID] = useState();
  const [IDs, setIDs] = useState([]);

  const { progress, url } = useStorage(file, "companies_dp");

  useEffect(() => {
    // let arr = []
    // db.collection(companies).get().then(snap => snap.forEach(data => {
    //   arr = [...arr, data.id]
    // }))
    // .then(() => setIDs(arr))
  }, []);
  //

  // console.log(IDs)
  const handleChange = (loadedFiles) => {
    let selectedFile = loadedFiles[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  };

  //
  const addCompany = () => {
    // console.log()
    if (IDs.includes(companyID)) {
      toastMessageWarning({ message: "A company with same ID exists!" });
      return;
    }
    console.log(
      companyName,
      companyDomain,
      theme,
      file,
      url,
      description,
      companyID,
    );
    if (
      companyName &&
      companyDomain &&
      theme &&
      file &&
      url &&
      description &&
      companyID
    ) {
      //   db.collection(companies).doc(companyID).set({
      //   title:companyName,
      //   theme:theme,
      //   domain:companyDomain,
      //   desc:description,
      //   url:url,
      //   })
      //   .then(() => {
      //     setCompanyName("");
      //     setCompanyDomain("");
      //     setFile("");
      //     setTheme("");
      //     setDescription("");
      //     history('/companies');
      //     toastMessageSuccess({message:"successfully Added",type:"success"});
      //   }
      // )
    } else {
      // toastMessage({message:'Missing field'})
    }
  };
  return (
    <section className="addCompanyForm">
      <div
        className="addCompanyFormContainer"
        style={
          currentMode === "Dark"
            ? { backgroundColor: "#161C24", color: "white" }
            : { backgroundColor: currentColorLight }
        }
      >
        <div className="title">
          <h3>Add New Company</h3>
        </div>
        <div className="desc">
          <p>Mandatory information</p>
        </div>

        <div className="comapnyFormContainer">
          <Box
            component="form"
            noValidate
            sx={{
              display: "grid",
              gridTemplateColumns: { sm: "1fr 1fr" },
              gap: 2,
            }}
          >
            <div className="labelFields">
              <TextField
                label="Company Name"
                id="companyName"
                placeholder="eg. xyz company"
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                onBlur={() => setCompanyName(companyName?.trim())}
                size="small"
                fullWidth
              />
            </div>

            <div className="labelFields">
              <TextField
                label="Company ID"
                id="companyID"
                placeholder="eg. Arizon12345"
                value={companyID}
                onChange={(e) => setCompanyID(e.target.value)}
                onBlur={() => setCompanyID(companyID?.trim())}
                size="small"
                fullWidth
              />
            </div>

            <div className="labelFields">
              <TextField
                label="Company Domain"
                id="companyDomain"
                placeholder="eg. arizonSystems.com"
                value={companyDomain}
                onChange={(e) => setCompanyDomain(e.target.value)}
                onBlur={() => setCompanyDomain(companyDomain?.trim())}
                size="small"
                fullWidth
              />
            </div>

            <div className="labelFields">
              <TextField
                label="Company Description"
                id="companyDesc"
                placeholder="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                onBlur={() => setDescription(description.trim())}
                size="small"
                fullWidth
              />
            </div>

            <div className="labelFields">
              <TextField
                label="Select color"
                type="color"
                value={theme}
                onChange={(e) => setTheme(e.target.value)}
                size="small"
                fullWidth
              />
            </div>

            <div className="labelFields">
              <InputLabel shrink htmlFor="companyLogo">
                Select Logo
              </InputLabel>
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) => handleChange(loadedFiles)}
                dropzoneText="Drag and Drop / Click to ADD Media"
                acceptedFiles={["image/jpeg", "image/png", "image/bmp"]}
                showAlerts={false}
                filesLimit={1}
              />
              {file ? <p> {progress} % Uploaded</p> : null}
            </div>
          </Box>

          <div className="flex justify-between">
            <Link to="/companies">
              <ButtonBasicCancel buttonTitle="Cancel" />
            </Link>
            {/* <button className="createBtn btn" onClick={()=> addCompany()} disabled={file ? false : true}>Add Company</button> */}
            <ButtonBasic
              buttonTitle="Add Company"
              onClick={() => addCompany()}
              disabled={file ? false : true}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default AddCompany;
