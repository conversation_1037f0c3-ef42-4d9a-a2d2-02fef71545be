import React, { useState } from "react";
import "./forgotPassword.scss";

import { alpha, styled } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import InputLabel from "@mui/material/InputLabel";
import { Link } from "react-router-dom";
import { useAuth } from "../../hooks/AuthProvider";

const BootstrapInput = styled(InputBase)(({ theme }) => ({
  "label + &": {
    marginTop: theme.spacing(0),
  },
  "& .MuiInputBase-input": {
    borderRadius: 4,
    position: "relative",
    backgroundColor: theme.palette.mode === "light" ? "#fcfcfb" : "#2b2b2b",
    border: "1px solid #ced4da",
    fontSize: 14,
    color: "#344767",
    padding: "10px 12px",
    transition: theme.transitions.create([
      "border-color",
      "background-color",
      "box-shadow",
    ]),
    "&:focus": {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main,
    },
  },
}));

const ForgotPassword = () => {
  const { resetPassword } = useAuth();
  const [email, setEmail] = useState("");

  return (
    <section className="forgotPassword">
      <div className="logoContainer ">
        <img
          src="https://firebasestorage.googleapis.com/v0/b/lyodatatest.appspot.com/o/arizon%2Farizon.webp?alt=media&token=e1e0804e-4387-4779-bfed-6d31dcb876cf"
          alt=""
        />
      </div>

      <div className="illustrationContainer">
        <img
          src="https://image.makewebeasy.net/makeweb/0/6UkjyKdfh/LYO/lyo1.png"
          alt=""
        />
      </div>

      <div className="formContainer">
        <div className="formTitle">Forgot Password?</div>
        <div className="formDesc">
          Enter Your email address here to recover it.
        </div>

        <form className="form">
          <div className="labelFields">
            <InputLabel shrink htmlFor="userEmail">
              Email Address
            </InputLabel>
            <BootstrapInput
              onChange={(e) => setEmail(e.target?.value)}
              id="userEmail"
              placeholder="eg. <EMAIL>"
            />
          </div>

          <div className="linkContainer">
            <Link to="/login" className="link">
              <i className="ri-arrow-left-line"></i>
              Back to Login
            </Link>
          </div>
          <button
            type="button"
            onClick={() => {
              resetPassword(email);
              console.log(email);
            }}
          >
            Submit
          </button>
        </form>
      </div>
    </section>
  );
};

export default ForgotPassword;
