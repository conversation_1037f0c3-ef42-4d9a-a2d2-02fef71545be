import React, { useEffect, useState } from "react";
import { TextField } from "@mui/material";
import { Button, IconButton } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DeleteForeverIcon from "@mui/icons-material/DeleteForever";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableContainer from "@mui/material/TableContainer";
import Paper from "@mui/material/Paper";
import { db } from "../../../firebase";
import { companies, companyId_constant } from "../../../constants/data";
import { firebaseLooper } from "../../../tools/tool";
import { toastMessageSuccess } from "../../../tools/toast";
import DynamicInput from "./DynamicInput";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { useAuth } from "../../../hooks/AuthProvider";
import { useStateContext } from "../../../context/ContextProvider";

function DynamicTable({
  docId,
  type,
  dynamicTable,
  tableName,
  dynamicTablesDocId,
  reportName,
  machineName,
}) {
  const [columnsNumber, setColumnsNumber] = useState(dynamicTable[0].length);
  const [inputTableValue, setInputTableValue] = useState("");
  const [editTableName, setEditTableName] = useState(false);
  const [openEdit, setOpenEdit] = useState(
    Array(dynamicTable.length).fill(false),
  );
  const [editRow, setEditRow] = useState([]);
  const [editTile, setEditTile] = useState({});
  const { currentUser } = useAuth();
  const [user, setUser] = useState([]);
  const { currentMode } = useStateContext();

  const moduleName = type === "fatData" ? "FAT" : "SAT";

  const handleEdit = (index) => {
    setEditRow(dynamicTable[index]);
    const array = openEdit.map((value, idx) =>
      idx === index ? !value : value,
    );
    setOpenEdit(array);
  };

  const updateTableName = (name) => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(type)
    //     .where("docId", "==", docId)
    //     .onSnapshot((snap) => {
    //         snap.forEach((snap) => {
    //             db.collection(companies)
    //                 .doc(companyId_constant)
    //                 .collection(type)
    //                 .doc(snap.id)
    //                 .collection("dynamic_table")
    //                 .doc(dynamicTablesDocId)
    //                 .update({ tableName: name })
    //                 .then(() => {
    //                     LoggingFunction(
    //                         machineName,
    //                         reportName,
    //                         `${user?.fname} ${user?.lname}`,
    //                         moduleName,
    //                         `Custom Table(${tableName}) is changed to from ${name} inside ${reportName}`
    //                     )
    //                     toastMessageSuccess({
    //                         message: `Table Name has been updated successfully to ${name}`,
    //                     },
    //                         (error) => console.log(error)
    //                     )
    //                 }
    //                 )
    //         });
    //     });
  };

  const updateTable = (value) => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(type)
    //     .where("docId", "==", docId)
    //     .onSnapshot((snap) => {
    //         snap.forEach((snap) => {
    //             db.collection(companies)
    //                 .doc(companyId_constant)
    //                 .collection(type)
    //                 .doc(snap.id)
    //                 .collection("dynamic_table")
    //                 .doc(dynamicTablesDocId)
    //                 .update({ dynamicTable: JSON.stringify(value) })
    //                 .then(() => {
    //                     LoggingFunction(
    //                         machineName,
    //                         reportName,
    //                         `${user?.fname} ${user?.lname}`,
    //                         moduleName,
    //                         `${tableName}) is updated`
    //                     )
    //                     toastMessageSuccess({
    //                         message: `Table Content has been updated successfully`,
    //                     },
    //                         (error) => console.log(error)
    //                     )
    //                 }
    //                 )
    //         });
    //     });
  };

  const deleteTable = () => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(type)
    //     .where("docId", "==", docId)
    //     .onSnapshot((snap) => {
    //         snap.forEach((snap) => {
    //             db.collection(companies)
    //                 .doc(companyId_constant)
    //                 .collection(type)
    //                 .doc(snap.id)
    //                 .collection("dynamic_table")
    //                 .doc(dynamicTablesDocId)
    //                 .delete()
    //                 .then(() => {
    //                     LoggingFunction(
    //                         machineName,
    //                         reportName,
    //                         `${user?.fname} ${user?.lname}`,
    //                         moduleName,
    //                         `Custom Table(${tableName}) is deleted from ${reportName}`
    //                     )
    //                     toastMessageSuccess({
    //                         message: `Table  has been deleted successfully`,
    //                     },
    //                         (error) => console.log(error)
    //                     );
    //                 }
    //                 )
    //         });
    //     });
  };

  const handleSave = (index) => {
    const array = dynamicTable.map((value, idx) => {
      return idx === index ? editRow : value;
    });
    updateTable(array);
    handleEdit(index);
  };

  const handleDeleteRow = (index) => {
    const array = dynamicTable.filter((value, idx) => {
      return idx !== index;
    });
    updateTable(array);
  };

  const handleDeleteColumn = (index) => {
    let array = dynamicTable;
    array = array.map((row) => {
      row = row.filter((value, idx) => {
        return idx !== index;
      });
      return row;
    });
    setColumnsNumber((prevValue) => prevValue - 1);
    updateTable(array);
  };

  const addRow = () => {
    const newRow = Array.apply(null, { length: columnsNumber }).fill({
      id: "",
      value: "data",
    });
    newRow.map((value, idx) => {
      value = {
        ...value,
        id: idx,
      };
      return value;
    });
    let array = [...dynamicTable, newRow];
    updateTable(array);
  };

  const addColumns = (where, position) => {
    let array = dynamicTable;
    var newArray = [];
    if (where === "before") {
      array.forEach((row, index) => {
        var requiredArray = [];
        row.forEach((tile, idx) => {
          if (idx === position) {
            if (index === 0) {
              requiredArray.push({ id: 0, value: "Empty Head" });
              requiredArray.push(tile);
            } else {
              requiredArray.push({ id: 0, value: "Empty data" });
              requiredArray.push(tile);
            }
          } else requiredArray.push(tile);
        });
        newArray.push(requiredArray);
      });
    } else if (where === "after") {
      newArray = array.map((row, idx) => {
        if (idx === 0)
          row.push({ id: parseInt(columnsNumber), value: "Empty Head" });
        else row.push({ id: parseInt(columnsNumber), value: "Empty data" });
        return row;
      });
    }
    newArray = newArray.map((row) => {
      row.map((tile, idx) => {
        tile = {
          ...tile,
          id: idx,
        };
        return tile;
      });
      return row;
    });
    setColumnsNumber(parseInt(columnsNumber) + 1);
    updateTable(newArray);
  };

  useEffect(() => {
    if (JSON.stringify(editRow) === "[]") {
      const array = Array.apply(null, { length: columnsNumber }).fill({
        id: "",
        value: "",
      });
      array[editTile.id] = editTile;
      setEditRow(array);
    } else {
      const array = editRow.map((elements) => elements);
      array[editTile.id] = editTile;
      setEditRow(array);
    }
  }, [editTile]);
  useEffect(() => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection("userData")
    //     .where("email", "==", currentUser.email)
    //     .onSnapshot((snap) => {
    //         const data = firebaseLooper(snap);
    //         setUser(data[0]);
    //     });
  }, []);

  return (
    <>
      <div className="flex justify-between items-center mt-4 mb-2">
        <div className="table_title items-baseline flex">
          <h4 className="font-bold uppercase underline mr-8 mb-4">
            {tableName}
          </h4>
          <Button onClick={() => setEditTableName(!editTableName)}>
            <i className="ri-pencil-fill"></i>
          </Button>
        </div>
        <Button
          onClick={() => deleteTable()}
          color="error"
          size="small"
          endIcon={<DeleteForeverIcon />}
          variant="contained"
        >
          {" "}
          Delete Table{" "}
        </Button>
      </div>
      {editTableName && (
        <div className="my-6">
          <TextField
            onChange={(e) => setInputTableValue(e.target.value)}
            style={{ marginBottom: "10px" }}
            variant="outlined"
            fullWidth
            placeholder={`Enter New Table Name Data`}
          />
          <div className="mt-2 flex justify-center">
            <Button
              variant="contained"
              onClick={() => {
                updateTableName(inputTableValue, dynamicTablesDocId);
                setEditTableName(!editTableName);
              }}
              size="small"
              endIcon={<AddIcon />}
            >
              {" "}
              Save{" "}
            </Button>
          </div>
        </div>
      )}
      <TableContainer component={Paper} className="table mb-8">
        <Table
          sx={{
            minWidth: 650,
            width: "100%",
            border:
              currentMode === "Dark" ? "1px solid white" : "1px solid black",
          }}
        >
          <TableHead>
            <TableRow
              style={{
                backgroundColor: currentMode === "Dark" ? "#161C24" : "#D9D9D9",
                fontWeight: "bold",
                color: currentMode === "Dark" ? "white" : "black",
                textTransform: "uppercase",
              }}
            >
              {dynamicTable[0]?.map((data, idx) => (
                <TableCell
                  key={data.id}
                  style={{
                    border:
                      currentMode === "Dark"
                        ? "1px solid white"
                        : "1px solid black",
                  }}
                  align="left"
                >
                  <span className="mr-2">{data.value}</span>
                  <IconButton
                    size="small"
                    sx={{ height: "32px", width: "32px" }}
                    onClick={() => addColumns("before", idx)}
                  >
                    <i className="ri-add-line text-green-500 font-bold text-xl"></i>
                  </IconButton>
                  <IconButton
                    size="small"
                    sx={{ height: "32px", width: "32px" }}
                    onClick={() => handleDeleteColumn(idx)}
                  >
                    <i className="ri-delete-bin-6-fill text-red-700 text-sm"></i>
                  </IconButton>
                </TableCell>
              ))}
              <TableCell
                style={{
                  border:
                    currentMode === "Dark"
                      ? "1px solid white"
                      : "1px solid black",
                }}
                align="right"
              >
                <Button onClick={() => handleEdit(0)}>
                  <i className="ri-pencil-fill"></i>
                </Button>
                <Button onClick={() => addColumns("after")}>
                  <i className="ri-add-line text-green-500 font-bold text-xl"></i>
                </Button>
              </TableCell>
            </TableRow>
            {openEdit[0] && (
              <TableRow className="my-6 mx-auto">
                <TableCell colSpan={columnsNumber}>
                  {dynamicTable[0].map((val, column_idx) => (
                    <DynamicInput
                      key={column_idx}
                      headerValue={val.value}
                      prevValue={val.value}
                      column_idx={column_idx}
                      setTile={setEditTile}
                    />
                  ))}
                  <div className="mt-2 mb-4 flex justify-center">
                    <Button
                      variant="contained"
                      onClick={() => handleSave(0)}
                      size="small"
                      endIcon={<AddIcon />}
                    >
                      {" "}
                      Save{" "}
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableHead>
          <TableBody>
            {dynamicTable.map((row, index) => {
              if (index !== 0) {
                return (
                  <React.Fragment key={index}>
                    <TableRow
                      sx={{
                        backgroundColor:
                          currentMode === "Dark" ? "#212B36" : "#fff",
                        color: currentMode === "Dark" ? "white" : "black",
                        textTransform: "capitalize",
                      }}
                    >
                      {row.map((cellData, cellIdx) => (
                        <TableCell
                          style={{
                            border:
                              currentMode === "Dark"
                                ? "1px solid white"
                                : "1px solid black",
                          }}
                          key={cellIdx}
                          align="left"
                        >
                          {cellData.value}
                        </TableCell>
                      ))}
                      <TableCell
                        style={{
                          border:
                            currentMode === "Dark"
                              ? "1px solid white"
                              : "1px solid black",
                        }}
                        align="right"
                      >
                        <Button onClick={() => handleEdit(index)}>
                          <i className="ri-pencil-fill"></i>
                        </Button>
                        <Button onClick={() => handleDeleteRow(index)}>
                          <i className="ri-delete-bin-6-fill text-red-700"></i>
                        </Button>
                      </TableCell>
                    </TableRow>
                    {openEdit[index] && (
                      <TableRow className="my-6">
                        <TableCell colSpan={columnsNumber}>
                          {dynamicTable[index].map((val, column_idx) => (
                            <DynamicInput
                              key={column_idx}
                              headerValue={dynamicTable[0][column_idx].value}
                              prevValue={val.value}
                              column_idx={column_idx}
                              setTile={setEditTile}
                            />
                          ))}
                          <div className="mt-2 mb-4 flex justify-center">
                            <Button
                              variant="contained"
                              onClick={() => handleSave(index)}
                              size="small"
                              endIcon={<AddIcon />}
                            >
                              {" "}
                              Save{" "}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                );
              }
            })}
          </TableBody>
        </Table>
      </TableContainer>
      <div className="mt-1 flex justify-center">
        <Button
          onClick={() => {
            setOpenEdit([...openEdit, false]);
            addRow();
          }}
          variant="contained"
          color="warning"
          size="small"
          endIcon={<AddIcon />}
        >
          Add Row
        </Button>
      </div>
    </>
  );
}

export default DynamicTable;
