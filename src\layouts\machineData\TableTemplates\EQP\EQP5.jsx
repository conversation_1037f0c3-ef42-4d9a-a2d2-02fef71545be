import React, { useState, useMemo, useContext } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "../TableTempletes.css";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import TextField from "@mui/material/TextField";
import { useStateContext } from "../../../../context/ContextProvider";
import { db } from "../../../../firebase";
import { companies, companyId_constant } from "../../../../constants/data";
import {
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../../tools/toast";
import { ButtonBasic } from "../../../../components/buttons/Buttons";
import { DeleteByUrl } from "../../../../utils/StorageOptions";
import { FilePdfFilled, PictureFilled } from "@ant-design/icons";
import { toastMessage } from "../../../../tools/toast";
import { InputLabel, LinearProgress, Typography } from "@mui/material";
import { DropzoneArea } from "material-ui-dropzone";
import { FileDownload } from "@mui/icons-material";
import { Empty } from "antd";
import { useStorageTablesFile } from "../../../../utils/useStorageTablesFile";
import FileSelector from "../../../FileSelector/screens/FileSelector";
import PreviewIcon from "@mui/icons-material/Preview";
import { FileManagerSelectorContext } from "../../../../services/fileManager/file-manager-select.context";

const EQP5 = ({ rowData, type, machineName, fatDataDocId, useAt }) => {
  const [open, setOpen] = useState(false);
  const { currentMode, currentColorLight } = useStateContext();
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#161c24" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }}>
                Test Result
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={3}>
                PASS FAIL
              </TableCell>
            </TableRow>
            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }}>
                Company's Name
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>Name</TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Signature
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>Date</TableCell>
              {useAt !== "tableMain" && (
                <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                  Image
                </TableCell>
              )}
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  sx={{
                    border: theme.borderDesign,
                    background: currentMode === "Dark" ? "#212b36" : "",
                  }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[2]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[3]}
                  </TableCell>
                  {useAt !== "tableMain" && (
                    <TableCell
                      sx={{ border: theme.borderDesign }}
                      align="center"
                    >
                      {data[4] === null ? (
                        "Add One"
                      ) : (
                        <>
                          {data[4]?.includes(".pdf") ? (
                            <FilePdfFilled
                              className="text-red-700"
                              onContextMenu={() =>
                                (window.location.href = data[4])
                              }
                              title="Press right click to open file"
                            />
                          ) : (
                            <PictureFilled
                              onContextMenu={() =>
                                (window.location.href = data[4])
                              }
                              title="Press right click to open file"
                            />
                          )}
                        </>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
          <EditTableRow
            rowDataSelected={rowDataSelected}
            type={type}
            fatDataDocId={fatDataDocId}
            machineName={machineName}
            handleClose={handleClose}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

const EditTableRow = ({
  rowDataSelected,
  type,
  machineName,
  fatDataDocId,
  handleClose,
}) => {
  // console.log(rowDataSelected, tableType)
  const { currentMode } = useStateContext();
  const [comapnyName, setComapnyName] = useState(rowDataSelected[0]);
  const [name, setName] = useState(rowDataSelected[1]);
  const [signature, setSignature] = useState(rowDataSelected[2]);
  const [date, setDate] = useState(rowDataSelected[3]);
  const [urlData, setUrlData] = useState(rowDataSelected[4]);
  const [fileType, setFileType] = useState("");
  const [file, setFile] = useState(null);
  const { progress, url } = useStorageTablesFile(file);
  const { fileUrl, changeFileUrl } = useContext(FileManagerSelectorContext);
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];
  const [index, setIndex] = useState(rowDataSelected[6]);
  const [fType, setFType] = useState("index" in rowDataSelected ? true : false);

  const handleChangeImage = (file) => {
    let selectedFile = file[0];
    if (selectedFile) {
      if (fType && types.includes(selectedFile.type)) {
        if (url) {
          DeleteByUrl(url);
        }
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };

  const handleUpdateRow = () => {
    if (fType && isNaN(parseInt(index))) {
      toastMessageWarning({ message: "The input of index should be a Number" });
      return;
    }
    let data = {
      comapnyName: comapnyName,
      name: name,
      signature: signature,
      date: date,
      index: index,
      url: fileUrl === null ? urlData : fileUrl,
    };
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(type)
    //   .doc(fatDataDocId)
    //   .collection("tableEQP5")
    //   .doc(rowDataSelected[5])
    //   .update(fType ? {...data, index} : data)
    //   .then(() => {
    //     toastMessageSuccess({ message: "Row updated Successfully" });
    //     handleClose();
    //   })
    //   .catch((e) => {
    //     toastMessageWarning({ message: "Error ", e });
    //   });
  };

  const handleCancel = () => {
    if (url) {
      DeleteByUrl(url);
    }
    handleClose();
  };
  const handleDeleteDropZone = (url) => {
    DeleteByUrl(url);
    setFile(null);
  };

  return (
    <>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          <div className="flex justify-between py-2">
            <div className="w-2/12">
              <TextField
                label="Company's Name"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={comapnyName}
                onChange={(e) => setComapnyName(e.target.value)}
              />
            </div>
            <div className="w-2/12">
              <TextField
                fullWidth
                label="Name"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>
            <div className="w-2/12">
              <TextField
                label="Signature"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={signature}
                onChange={(e) => setSignature(e.target.value)}
              />
            </div>
            <div className="w-2/12">
              <TextField
                label="Date"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={date}
                onChange={(e) => setDate(e.target.value)}
              />
            </div>
            {/* {fType */}
            {(url?.includes(".pdf") ||
              fileUrl?.includes(".pdf") ||
              urlData?.includes(".pdf")) && (
              <div className="w-2/12">
                <TextField
                  label="Index"
                  id="outlined-size-small"
                  defaultValue="Na"
                  size="small"
                  value={index}
                  onChange={(e) => setIndex(e.target.value)}
                />
              </div>
            )}
          </div>
        </DialogContentText>
        <FileSelector setFType={setFType} />
        <Typography sx={{ ml: "20px" }} align="left">
          {" "}
          <PreviewIcon /> Preview
        </Typography>
        <img width="250px" src={urlData} />
      </DialogContent>
      <DialogActions>
        <ButtonBasic buttonTitle="Update" onClick={handleUpdateRow} />
      </DialogActions>
    </>
  );
};

export default EQP5;
