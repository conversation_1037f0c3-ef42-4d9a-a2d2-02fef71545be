import {
  <PERSON>ton,
  Checkbox,
  Dialog,
  DialogContent,
  DialogTitle,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import React, { useState } from "react";
import { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { v4 as uuidv4 } from "uuid";
import {
  companies,
  companyId_constant,
  fatReport,
  machines,
} from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import "../machineData/contentPage.scss";
import DataTableItem from "./DataTableItem";
import ApprovalTableItem from "../machineData/ApprovalTable/ApprovalTable";
import EditReports from "./EditReports";
import ReportsTableForPrint from "./print/ReportsTableForPrint";
import { useAuth } from "../../hooks/AuthProvider";
import { useStateContext } from "../../context/ContextProvider";
import { Edit } from "@mui/icons-material";
import Formatting from "../../components/Editor/Formatting";
import { T1, T2, T3, T4 } from "../machineData/TableTemplates/TableTemplates";
import IQ1 from "../machineData/TableTemplates/iq/IQ1";
import IQ2 from "../machineData/TableTemplates/iq/IQ2";
import IQ3 from "../machineData/TableTemplates/iq/IQ3";
import IQ4 from "../machineData/TableTemplates/iq/IQ4";
import IQ5 from "../machineData/TableTemplates/iq/IQ5";
import IQ7 from "../machineData/TableTemplates/iq/IQ7";
import IQ9 from "../machineData/TableTemplates/iq/IQ9";
import IQ8 from "../machineData/TableTemplates/iq/IQ8";
import IQ10 from "../machineData/TableTemplates/iq/IQ10";
import IQ11 from "../machineData/TableTemplates/iq/IQ11";
import IQ6 from "../machineData/TableTemplates/iq/IQ6";
import MFG1 from "../machineData/TableTemplates/manufacturing/MFG1";
import EQP1 from "../machineData/TableTemplates/EQP/EQP1";
import EQP2 from "../machineData/TableTemplates/EQP/EQP2";
import EQP3 from "../machineData/TableTemplates/EQP/EQP3";
import EQP4 from "../machineData/TableTemplates/EQP/EQP4";
import EQP5 from "../machineData/TableTemplates/EQP/EQP5";
import OQ1 from "../machineData/TableTemplates/OQ/OQ1";
import OQ2 from "../machineData/TableTemplates/OQ/OQ2";
import OQ3 from "../machineData/TableTemplates/OQ/OQ3";
import OQ4 from "../machineData/TableTemplates/OQ/OQ4";
import OQ5 from "../machineData/TableTemplates/OQ/OQ5";
import OQ6 from "../machineData/TableTemplates/OQ/OQ6";
import OQ7 from "../machineData/TableTemplates/OQ/OQ7";
import OQ8 from "../machineData/TableTemplates/OQ/OQ8";
import OQLayout from "../OQ-layouts/OQLayout";
import NonConfirmity from "../Non-conformity/NonConfirmity";
import IQ12 from "../machineData/TableTemplates/iq/IQ12";
import IQ13 from "../machineData/TableTemplates/iq/IQ13";
import IQ14 from "../machineData/TableTemplates/iq/IQ14";
import OQ11 from "../machineData/TableTemplates/OQ/OQ11";
import OQ10 from "../machineData/TableTemplates/OQ/OQ10";
import OQ9 from "../machineData/TableTemplates/OQ/OQ9";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import PQ1 from "../machineData/TableTemplates/PQ/PQ1";
import PQ2 from "../machineData/TableTemplates/PQ/PQ2";
import PQ3 from "../machineData/TableTemplates/PQ/PQ3";
import PQ4 from "../machineData/TableTemplates/PQ/PQ4";
import PQ5 from "../machineData/TableTemplates/PQ/PQ5";
import { useContentEditCount } from "../../services3/audits/ContentContext";
import PostApprovalGilPq from "../machineData/TableTemplates/Post_Approval/Post_ApprovalGilPq";

const ReportsTable = ({
  fatDataId,
  fatData,
  type,
  machineName,
  user,
  details,
  approvalTableType,
  //reportId
}) => {
  const { reportId } = useParams(); //ID of reports from which the fatData has to be fetched
  const { contentEditCount, setContentEditCount } = useContentEditCount();

  console.log("approvaltableType:", approvalTableType);

  // const databaseCollection = db
  //   .collection(companies)
  //   .doc(companyId_constant)
  //   .collection(fatReport)
  //   .doc(reportId)
  //   .collection(`fatData`)
  //   .doc(fatDataId);

  const [reportTableDetails, setReportTableDetails] = useState([]);
  const [approvalTable, setApprovalTable] = useState([]);
  const [postApprovalTable, setPostApprovalTable] = useState([]);
  const [table1, setTable1] = useState([]);
  const [table2, setTable2] = useState([]);
  const [table3, setTable3] = useState([]);
  const [table4, setTable4] = useState([]);
  const [iq1, setIq1] = useState();
  const [iq2, setIq2] = useState();
  const [iq3, setIq3] = useState();
  const [iq4, setIq4] = useState();
  const [iq5, setIq5] = useState();
  const [iq6, setIq6] = useState();
  const [iq7, setIq7] = useState();
  const [iq8, setIq8] = useState();
  const [iq9, setIq9] = useState();
  const [iq10, setIq10] = useState();
  const [iq11, setIq11] = useState();
  const [iq12, setIq12] = useState();
  const [iq13, setIq13] = useState();
  const [iq14, setIq14] = useState();
  const [mfg1, setMfg1] = useState();

  const [eqp1, setEQP1] = useState();
  const [eqp2, setEQP2] = useState();
  const [eqp3, setEQP3] = useState();
  const [eqp4, setEQP4] = useState();
  const [eqp5, setEQP5] = useState();

  const [oq1, setOq1] = useState();
  const [oq2, setOq2] = useState();
  const [oq3, setOq3] = useState();
  const [oq4, setOq4] = useState();
  const [oq5, setOq5] = useState();
  const [oq6, setOq6] = useState();
  const [oq7, setOq7] = useState();
  const [oq8, setOq8] = useState();
  const [oq9, setOq9] = useState();
  const [oq10, setOq10] = useState();
  const [oq11, setOq11] = useState();

  const [pq1, setPq1] = useState(); // test1
  const [pq2, setPq2] = useState([]);
  const [pq2_staticData, setPq2_staticData] = useState([]);
  const [pq3, setPq3] = useState([]);
  const [pq3_staticData, setPq3_staticData] = useState([]);
  const [pq4, setPq4] = useState([]);
  const [pq4_staticData, setPq4_staticData] = useState([]);
  const [pq5, setPq5] = useState([]);
  //const [postApproval, setPostApproval] = useState([]);

  useEffect(() => {
    //PQ start
    axios
      .post(`${dbConfig.url}/general-table-report/findsome`, {
        fat_id: fatDataId,
        table_type: "PQ1",
      })
      .then((res) => {
        console.log("PQ1 data get:", res?.data);
        console.log("FatData id:", fatDataId);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            temp.push(dataM["serial_no"]);
            temp.push(dataM["speed_of_machine"]);
            temp.push(dataM["speed_in_rpm"]);
            //temp.push(dataM["url"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPq1(tablesArray);
      })
      .catch((e) => {
        console.log("error PQ1 data get:", e);
      });

    //PQ2
    axios
      .post(`${dbConfig.url}/general-table-report/findsome`, {
        fat_id: fatDataId,
        table_type: "PQ2",
      })
      .then((res) => {
        console.log("PQ2 data get:", res?.data);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"], //
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            // temp.push(dataM["serial_no"]);
            temp.push(dataM["parameter"]);
            temp.push(dataM["acceptance_criteria"]);
            temp.push(dataM["initial"]);
            temp.push(dataM["min10"]);
            temp.push(dataM["min20"]);
            temp.push(dataM["min30"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPq2(tablesArray);
        //
        axios
          .post(`${dbConfig.url}/general-table_static-data-report/findsome`, {
            fat_id: fatDataId,
            // table_title: res?.data[0]["table_title"],
          })
          .then((res2) => {
            console.log("res222:", res2?.data);
            setPq2_staticData(res2?.data);
          })
          .catch((e) => {
            console.log("error Pq2_staticData get:", e);
          });
        //
      })
      .catch((e) => {
        console.log("error PQ2 data get:", e);
      });

    //PQ3
    axios
      .post(`${dbConfig.url}/general-table-report/findsome`, {
        fat_id: fatDataId,
        table_type: "PQ3",
      })
      .then((res) => {
        console.log("PQ3 data get:", res?.data);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"], //
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            // temp.push(dataM["serial_no"]);
            temp.push(dataM["parameter"]);
            temp.push(dataM["specification"]);
            temp.push(dataM["rpm10"]);
            temp.push(dataM["rpm20"]);
            temp.push(dataM["rpm40"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPq3(tablesArray);
        //
        axios
          .post(`${dbConfig.url}/general-table_static-data-report/findsome`, {
            fat_id: fatDataId,
            // table_title: res?.data[0]["table_title"],
          })
          .then((res2) => {
            console.log("res222:", res2?.data);
            setPq3_staticData(res2?.data);
          })
          .catch((e) => {
            console.log("error Pq3_staticData get:", e);
          });
        //
      })
      .catch((e) => {
        console.log("error PQ3 data get:", e);
      });

    //PQ4
    axios
      .post(`${dbConfig.url}/general-table-report/findsome`, {
        fat_id: fatDataId,
        table_type: "PQ4",
      })
      .then((res) => {
        console.log("PQ4 data get:", res?.data);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"], //
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            temp.push(dataM["serial_no"]); // it has serial no
            temp.push(dataM["parameter"]);
            temp.push(dataM["limits"]);
            temp.push(dataM["rpm40"]);
            temp.push(dataM["rpm45"]);
            temp.push(dataM["rpm50"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPq4(tablesArray);
        //
        axios
          .post(`${dbConfig.url}/general-table_static-data-report/findsome`, {
            fat_id: fatDataId,
            // table_title: res?.data[0]["table_title"],
          })
          .then((res2) => {
            console.log("res222:", res2?.data);
            setPq4_staticData(res2?.data);
          })
          .catch((e) => {
            console.log("error Pq4_staticData get:", e);
          });
        //
      })
      .catch((e) => {
        console.log("error PQ4 data get:", e);
      });

    //PQ5
    axios
      .post(`${dbConfig.url}/general-table-report/findsome`, {
        fat_id: fatDataId,
        table_type: "PQ5",
      })
      .then((res) => {
        console.log("PQ5 data get:", res?.data);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"], //
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            temp.push(dataM["serial_no"]); // it has serial no
            temp.push(dataM["eqp_name"]);
            temp.push(dataM["eqp_id"]);
            temp.push(dataM["make"]);
            temp.push(dataM["test_param"]);
            temp.push(dataM["design_range"]);
            temp.push(dataM["qualification_range"]);
            temp.push(dataM["product_qualification_range"]);
            temp.push(dataM["tolerance"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPq5(tablesArray);
        //
        // axios.post(`${dbConfig.url}/generalTableStaticData/findsome`, {
        //   fat_id: docId,
        //   // table_title: res?.data[0]["table_title"],
        // }).then((res2) => {
        //   console.log("res222:", res2?.data)
        //   setPq5_staticData(res2?.data)
        // }).catch((e) => {
        //   console.log("error Pq5_staticData get:", e)
        // })
        //
      })
      .catch((e) => {
        console.log("error PQ5 data get:", e);
      });

    // Post Approval
    // It has implementation as other tables only.  But only API is diffrent

    axios
      .post(`${dbConfig.url}/general-table-report/findsome_report_approval`, {
        // fat_id: fatDataId,
        fat_report_id: reportId,
        table_type: "PostApproval",
      })
      .then((res) => {
        console.log("Post Approval data get:", res?.data);
        console.log("FatData id:", fatDataId);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            // temp.push(dataM["serial_no"]);
            temp.push(dataM["prep_by_tech_transfer"]);
            temp.push(dataM["rev_by_tech_transfer"]);
            temp.push(dataM["production"]);
            temp.push(dataM["engineering"]);
            temp.push(dataM["rev_by_qa"]);
            temp.push(dataM["approved_by_qa"]);
            //temp.push(dataM["url"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPostApprovalTable(tablesArray);
      })
      .catch((e) => {
        console.log("error Post Approval data get:", e);
      });

    //   databaseCollection
    //     .collection("table") //every data from collection table will be fetched (excution table)
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       setReportTableDetails(data);
    //     });

    //   //tabletypes data
    //   //t1
    //   databaseCollection
    //     .collection("table1") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["tag_no"]); //
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["make_vendor"]); //
    //             temp.push(dataM["details"]); //
    //             temp.push(dataM["size"]); //
    //             temp.push(dataM["documents"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setTable1(tablesArray);
    //     });
    //   //t2
    //   databaseCollection
    //     .collection("table2") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["tag_no"]); //
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["type_size"]); //
    //             temp.push(dataM["documents"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setTable2(tablesArray);
    //     });
    //   //t3
    //   databaseCollection
    //     .collection("table3") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["drawing_title"]); //
    //             temp.push(dataM["drawing_no"]); //
    //             temp.push(dataM["compliance"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setTable3(tablesArray);
    //     });

    //   //t4
    //   databaseCollection
    //     .collection("table4") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["tag_no"]); //
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["make_vendor"]); //
    //             temp.push(dataM["details"]); //
    //             temp.push(dataM["doc_avail"]); //
    //             temp.push(dataM["sr_avail"]); //
    //             temp.push(dataM["type"]); // document type
    //             temp.push(dataM["result"]);
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setTable4(tablesArray);
    //     });

    //   // IQ1
    //   databaseCollection
    //     .collection("tableIQ1") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["make_vendor"]); //
    //             temp.push(dataM["type"]); //
    //             temp.push(dataM["size"]); //
    //             temp.push(dataM["documents"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq1(tablesArray);
    //     });

    //   // IQ2
    //   databaseCollection
    //     .collection("tableIQ2") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["tag_no"]); //
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["make_vendor"]); //
    //             temp.push(dataM["type"]); //
    //             temp.push(dataM["size"]); //
    //             temp.push(dataM["documents"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq2(tablesArray);
    //     });

    //   // IQ3
    //   databaseCollection
    //     .collection("tableIQ3") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["tag_no"]); //
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["make_vendor"]); //
    //             temp.push(dataM["model"]); //
    //             temp.push(dataM["type"]); //
    //             temp.push(dataM["size"]); //
    //             temp.push(dataM["documents"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq3(tablesArray);
    //     });

    //   // IQ4
    //   databaseCollection
    //     .collection("tableIQ4") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["tag_no"]); //
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["make_vendor"]); //
    //             temp.push(dataM["model"]); //
    //             temp.push(dataM["type"]); //
    //             temp.push(dataM["rating"]); // IQ3:Size
    //             temp.push(dataM["documents"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq4(tablesArray);
    //     });

    //   // IQ5
    //   databaseCollection
    //     .collection("tableIQ5") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["make_vendor"]); //
    //             temp.push(dataM["model"]); //
    //             temp.push(dataM["type"]); //
    //             temp.push(dataM["rating"]); // IQ3:Size
    //             temp.push(dataM["documents"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq5(tablesArray);
    //     });

    //   // IQ6
    //   databaseCollection
    //     .collection("tableIQ6") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["tag_no"]); //
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["make_vendor"]); //
    //             temp.push(dataM["model"]); //
    //             temp.push(dataM["calibration_certificate_number"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq6(tablesArray);
    //     });

    //   // IQ7
    //   databaseCollection
    //     .collection("tableIQ7") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["module_type"]); //
    //             temp.push(dataM["position"]); //
    //             temp.push(dataM["application"]); //
    //             temp.push(dataM["calibration_certificate_number"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq7(tablesArray);
    //     });

    //   // IQ8
    //   databaseCollection
    //     .collection("tableIQ8") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["tag_no"]); //
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["make_vendor"]); //
    //             temp.push(dataM["model"]); //
    //             temp.push(dataM["test_report_no"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq8(tablesArray);
    //     });

    //   // IQ9
    //   databaseCollection
    //     .collection("tableIQ9") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["tag_no"]); //
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["make_vendor"]); //
    //             temp.push(dataM["model"]); //
    //             temp.push(dataM["type"]); //
    //             temp.push(dataM["calibration_certificate_number"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq9(tablesArray);
    //     });

    //   // IQ10
    //   databaseCollection
    //     .collection("tableIQ10") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["software_name"]); // order is important
    //             temp.push(dataM["make"]); //
    //             temp.push(dataM["version"]); //
    //             temp.push(dataM["application"]); //
    //             temp.push(dataM["remarks"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq10(tablesArray);
    //     });

    //   // IQ11
    //   databaseCollection
    //     .collection("tableIQ11") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]); // order is important
    //             temp.push(dataM["tag_no"]); //
    //             temp.push(dataM["desc"]); //
    //             temp.push(dataM["make"]); //
    //             temp.push(dataM["model"]); //
    //             temp.push(dataM["range"]); //
    //             //temp.push(dataM['rating']); // IQ3:Size
    //             temp.push(dataM["documents"]); //
    //             temp.push(dataM["result"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq11(tablesArray);
    //     });

    //   // IQ12
    //   databaseCollection
    //     .collection("tableIQ12") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["drawing_title"]);
    //             temp.push(dataM["drawing_no"]);
    //             temp.push(dataM["compliance"]);
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq12(tablesArray);
    //     });

    //   // IQ13
    //   databaseCollection
    //     .collection("tableIQ13") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["s_no"]);
    //             temp.push(dataM["tag_no"]);
    //             temp.push(dataM["desc"]);
    //             temp.push(dataM["model_vendor"]);
    //             temp.push(dataM["model_s_no"]);
    //             temp.push(dataM["typeTable"]);
    //             temp.push(dataM["size"]);
    //             temp.push(dataM["docs"]);
    //             temp.push(dataM["result"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]);

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq13(tablesArray);
    //     });

    //   // IQ14
    //   databaseCollection
    //     .collection("tableIQ14") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["s_no"]);
    //             temp.push(dataM["desc"]);
    //             temp.push(dataM["model_vendor"]);
    //             temp.push(dataM["model_s_no"]);
    //             temp.push(dataM["typeTable"]);
    //             temp.push(dataM["docs"]);
    //             temp.push(dataM["result"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]);

    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setIq14(tablesArray);
    //     });

    //   // MFG1
    //   databaseCollection
    //     .collection("tableMFG1") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["desc"]); // order is important
    //             temp.push(dataM["document_number"]); //
    //             temp.push(dataM["yes_no"]); //
    //             temp.push(dataM["url"]); //
    //             temp.push(dataM["id"]); ////
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setMfg1(tablesArray);
    //     });

    //   // EQP1
    //   databaseCollection
    //     .collection("tableEQP1") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["desc"]);
    //             temp.push(dataM["protocol_number"]);
    //             temp.push(dataM["compliance"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setEQP1(tablesArray);
    //     });

    //   // EQP2
    //   databaseCollection
    //     .collection("tableEQP2") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["desc"]);
    //             temp.push(dataM["page_number"]);
    //             temp.push(dataM["compliance"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setEQP2(tablesArray);
    //     });

    //   // EQP3
    //   databaseCollection
    //     .collection("tableEQP3") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["doc"]);
    //             temp.push(dataM["test_report_number"]);
    //             temp.push(dataM["compliance"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setEQP3(tablesArray);
    //     });

    //   //EQP4
    //   databaseCollection
    //     .collection("tableEQP4") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["s_no"]);
    //             temp.push(dataM["qualification_document_name"]);
    //             temp.push(dataM["page_number"]);
    //             temp.push(dataM["nature_of_deviation"]);
    //             temp.push(dataM["major"]);
    //             temp.push(dataM["minor"]);
    //             temp.push(dataM["action_taken"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setEQP4(tablesArray);
    //     });

    //   // EQP5
    //   databaseCollection
    //     .collection("tableEQP5") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["comapnyName"]);
    //             temp.push(dataM["name"]);
    //             temp.push(dataM["signature"]);
    //             temp.push(dataM["date"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setEQP5(tablesArray);
    //     });

    //   // OQ1
    //   databaseCollection
    //     .collection("tableOQ1") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]);
    //             temp.push(dataM["desc"]);
    //             temp.push(dataM["desc_sec"]); // new addition
    //             temp.push(dataM["mod_number"]);
    //             temp.push(dataM["ch_number"]);
    //             temp.push(dataM["signal"]);
    //             temp.push(dataM["result"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setOq1(tablesArray);
    //     });

    //   // OQ2
    //   databaseCollection
    //     .collection("tableOQ2") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]);
    //             temp.push(dataM["desc"]);
    //             temp.push(dataM["performance_parameters"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setOq2(tablesArray);
    //     });

    //   // OQ3
    //   databaseCollection
    //     .collection("tableOQ3") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["serial_no"]);
    //             temp.push(dataM["desc"]);
    //             temp.push(dataM["required"]);
    //             temp.push(dataM["actuals"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setOq3(tablesArray);
    //     });

    //   // OQ4
    //   databaseCollection
    //     .collection("tableOQ4") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["desc"]);
    //             temp.push(dataM["general_condition"]);
    //             temp.push(dataM["actual_condition"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setOq4(tablesArray);
    //     });

    //   // OQ5
    //   databaseCollection
    //     .collection("tableOQ5") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["desc"]);
    //             temp.push(dataM["acceptance_criteria"]);
    //             temp.push(dataM["observation"]);
    //             temp.push(dataM["pass_fail"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setOq5(tablesArray);
    //     });

    //   // OQ6
    //   databaseCollection
    //     .collection("tableOQ6") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["check_point"]);
    //             temp.push(dataM["acceptance_criteria"]);
    //             temp.push(dataM["observation"]);
    //             temp.push(dataM["pass_fail"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setOq6(tablesArray);
    //     });

    //   // OQ7
    //   databaseCollection
    //     .collection("tableOQ7") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["check_point"]);
    //             temp.push(dataM["acceptance_criteria"]);
    //             temp.push(dataM["min"]);
    //             temp.push(dataM["max"]);
    //             temp.push(dataM["avg"]);
    //             temp.push(dataM["pass_fail"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setOq7(tablesArray);
    //     });

    //   // OQ8
    //   databaseCollection
    //     .collection("tableOQ8") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["temprature_set_point"]);
    //             temp.push(dataM["acceptance_criteria"]);
    //             temp.push(dataM["average_time"]);
    //             temp.push(dataM["variation"]);
    //             temp.push(dataM["status"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]);
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setOq8(tablesArray);
    //     });

    //   // OQ9
    //   databaseCollection
    //     .collection("tableOQ9") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["selfSP"]);
    //             temp.push(dataM["acceptance"]);
    //             temp.push(dataM["shelfNo"]);
    //             temp.push(dataM["shelfAverage"]);
    //             temp.push(dataM["allShelvesAverage"]); // ok/ Not ok
    //             temp.push(dataM["deviation"]);
    //             temp.push(dataM["passFail"]); //
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setOq9(tablesArray);
    //     });

    //   // OQ10
    //   databaseCollection
    //     .collection("tableOQ10") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["checkPoint"]);
    //             temp.push(dataM["results"]);
    //             temp.push(dataM["confirm"]);
    //             temp.push(dataM["deviation"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setOq10(tablesArray);
    //     });

    //   // OQ11
    //   databaseCollection
    //     .collection("tableOQ11") // table
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       const tablesArray = []; // array of tables
    //       fatData?.table_list?.map((titleData) => {
    //         let filterdData = data?.filter(
    //           (fdata) => fdata?.table_title === titleData?.table_title
    //         );
    //         if (filterdData?.length > 0) {
    //           let dataSorted = filterdData.sort(
    //             (a, b) => a["serial_no"] - b["serial_no"]
    //           );
    //           const tempArr2d = [];
    //           dataSorted.map((dataM) => {
    //             const temp = [];
    //             temp.push(dataM["recipe"]);
    //             temp.push(dataM["range"]);
    //             temp.push(dataM["value"]);
    //             temp.push(dataM["acceptanceCriteria"]);
    //             temp.push(dataM["chamberLead"]);
    //             temp.push(dataM["confirm"]);
    //             temp.push(dataM["url"]);
    //             temp.push(dataM["id"]);
    //             temp.push(dataM["table_title"]); //
    //             tempArr2d.push(temp);
    //           });
    //           tablesArray.push(tempArr2d);
    //         }
    //       });
    //       setOq11(tablesArray);
    //     });

    //   // tabletype data end
    //   databaseCollection
    //     .collection("approval") //Approval table for only 1 section - Pre approvals
    //     .onSnapshot((snap) => {
    //       const data = firebaseLooper(snap);
    //       setApprovalTable(data);
    //     });
  }, [contentEditCount]);

  const filterTableArrayBasedOnTitleForPq1 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        speed_of_machine: mData[1],
        "speed_in_rpm:": mData[2],
        id: mData[3],
        table_title: mData[5],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq2 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        parameter: mData[0],
        acceptance_criteria: mData[1],
        initial: mData[2],
        min10: mData[3],
        min20: mData[4],
        min30: mData[5],
        id: mData[6],
        table_title: mData[8],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq3 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        parameter: mData[0],
        specification: mData[1],
        rpm10: mData[2],
        rpm20: mData[3],
        rpm40: mData[4],
        id: mData[5],
        table_title: mData[7],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq4 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        parameter: mData[1],
        limits: mData[2],
        rpm40: mData[3],
        rpm45: mData[4],
        rpm50: mData[5],
        id: mData[6],
        table_title: mData[8],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        eqp_name: mData[1],
        eqp_id: mData[2],
        make: mData[3],
        test_param: mData[4],
        design_range: mData[5],
        qualification_range: mData[6],
        product_qualification_range: mData[7],
        tolerance: mData[8],
        id: mData[9],
        table_title: mData[11],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPostApprovalTable = (tData) => {
    // not that much needed in Approvals, but to keep the flow we use this
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        prep_by_tech_transfer: mData[0],
        rev_by_tech_transfer: mData[1],
        production: mData[2],
        engineering: mData[3],
        rev_by_qa: mData[4],
        approved_by_qa: mData[5],
        id: mData[6],
        table_title: mData[8],
      });
    });
    console.log("fdd:", tempTableData);
    return tempTableData;
  };

  if (reportTableDetails.length > 0) {
    return (
      <>
        <div className="content_sub-section">
          <div className="table_title">
            <h4 className="font-bold underline mr-3 mb-6">TEST EXECUTION</h4>
          </div>
          <TableContainer component={Paper} className="table">
            <Table sx={{ minWidth: 650, width: "100%" }}>
              <TableHead>
                <TableRow
                  style={{
                    backgroundColor: "#D9D9D9",
                    border: "1px solid black",
                    fontWeight: "bold",
                  }}
                >
                  <TableCell
                    style={{ border: "1px solid black", color: "black" }}
                    align="left"
                  >
                    Check Point
                  </TableCell>
                  <TableCell
                    style={{ border: "1px solid black", color: "black" }}
                    align="center"
                  >
                    Observation
                  </TableCell>
                  <TableCell
                    style={{ border: "1px solid black", color: "black" }}
                    align="center"
                  >
                    Acceptance Criteria
                  </TableCell>
                  <TableCell
                    style={{ border: "1px solid black", color: "black" }}
                    align="center"
                  >
                    Confirm YES/NO
                  </TableCell>
                  <TableCell
                    style={{ border: "1px solid black", color: "black" }}
                    align="center"
                  >
                    Deviation
                  </TableCell>
                  {type === "preview" && (
                    <TableCell
                      style={{ border: "1px solid black", color: "black" }}
                      align="center"
                    >
                      Actions
                    </TableCell>
                  )}
                </TableRow>
              </TableHead>

              <TableBody>
                {reportTableDetails.map((data) => (
                  <DataTableItem
                    fatId={fatDataId}
                    type={type}
                    key={data.id}
                    data={data}
                    details={details}
                    user={user}
                    machineName={machineName}
                  />
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </div>
      </>
    );
  } else if (
    table1?.length > 0 ||
    table2?.length > 0 ||
    table3?.length > 0 ||
    table4?.length > 0 ||
    iq1?.length > 0 ||
    iq2?.length > 0 ||
    iq3?.length > 0 ||
    iq4?.length > 0 ||
    iq5?.length > 0 ||
    iq6?.length > 0 ||
    iq7?.length > 0 ||
    iq8?.length > 0 ||
    iq9?.length > 0 ||
    iq10?.length > 0 ||
    iq11?.length > 0 ||
    iq10?.length > 0 ||
    iq11?.length > 0 ||
    iq12?.length > 0 ||
    mfg1?.length > 0 ||
    eqp1?.length > 0 ||
    eqp2?.length > 0 ||
    eqp3?.length > 0 ||
    eqp4?.length > 0 ||
    eqp5?.length > 0 ||
    oq1?.length > 0 ||
    oq2?.length > 0 ||
    oq3?.length > 0 ||
    oq4?.length > 0 ||
    oq5?.length > 0 ||
    oq6?.length > 0 ||
    oq7?.length > 0 ||
    oq8?.length > 0 ||
    oq9?.length > 0 ||
    oq10?.length > 0 ||
    oq11?.length > 0 ||
    pq1?.length > 0 ||
    pq2?.length > 0 ||
    pq3?.length > 0 ||
    pq4?.length > 0 ||
    pq5?.length > 0 ||
    postApprovalTable?.length > 0 ||
    approvalTableType
  ) {
    return (
      <div>
        {/* PQ start */}
        {pq1?.length > 0 && (
          <>
            {
              // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

              // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
              // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
              // Then we group them and again make them array of array
              Object.values(
                Object?.groupBy(
                  [...filterTableArrayBasedOnTitleForPq1(pq1)],
                  ({ table_title }) => table_title,
                ),
              )?.map((tData) => (
                <>
                  {console.log("tdata:", tData?.map(Object?.values))}
                  <div>
                    {/* tableTitle */}
                    {
                      tData?.map(Object?.values)[0][
                        tData?.map(Object?.values)[0]?.length - 1
                      ]
                    }
                  </div>
                  <PQ1
                    rowData={tData?.map(Object?.values)}
                    tableStaticData={pq1?.length ? pq1[0] : {}}
                    type={type}
                    machineName={machineName}
                    fatDataDocId={details.id}
                  />
                  <br />
                </>
              ))
            }
          </>
        )}

        {pq2?.length > 0 && (
          <>
            {
              // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

              // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
              // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
              // Then we group them and again make them array of array
              Object.values(
                Object?.groupBy(
                  [...filterTableArrayBasedOnTitleForPq2(pq2)],
                  ({ table_title }) => table_title,
                ),
              )?.map((tData) => (
                <>
                  {console.log("tdata:", tData?.map(Object?.values))}
                  <div>
                    {/* tableTitle */}
                    {
                      tData?.map(Object?.values)[0][
                        tData?.map(Object?.values)[0]?.length - 1
                      ]
                    }
                  </div>
                  <PQ2
                    key={uuidv4()}
                    rowData={tData?.map(Object?.values)}
                    tableStaticData={
                      pq2_staticData?.length
                        ? pq2_staticData?.filter(
                            (fData) =>
                              fData?.table_title ==
                              tData?.map(Object?.values)[0][
                                tData?.map(Object?.values)[0]?.length - 1
                              ],
                          )[0]
                        : {}
                    }
                    type={type}
                    machineName={machineName}
                    fatDataDocId={details.id}
                  />
                  <br />
                </>
              ))
            }
          </>
        )}

        {pq3?.length > 0 && (
          <>
            {
              // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

              // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
              // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
              // Then we group them and again make them array of array
              Object.values(
                Object?.groupBy(
                  [...filterTableArrayBasedOnTitleForPq3(pq3)],
                  ({ table_title }) => table_title,
                ),
              )?.map((tData) => (
                <>
                  {console.log("tdata:", tData?.map(Object?.values))}
                  <div>
                    {/* tableTitle */}
                    {
                      tData?.map(Object?.values)[0][
                        tData?.map(Object?.values)[0]?.length - 1
                      ]
                    }
                  </div>
                  <PQ3
                    key={uuidv4()}
                    rowData={tData?.map(Object?.values)}
                    tableStaticData={
                      pq3_staticData?.length
                        ? pq3_staticData?.filter(
                            (fData) =>
                              fData?.table_title ==
                              tData?.map(Object?.values)[0][
                                tData?.map(Object?.values)[0]?.length - 1
                              ],
                          )[0]
                        : {}
                    }
                    type={type}
                    machineName={machineName}
                    fatDataDocId={details.id}
                  />
                  <br />
                </>
              ))
            }
          </>
        )}

        {pq4?.length > 0 && (
          <>
            {
              // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

              // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
              // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
              // Then we group them and again make them array of array
              Object.values(
                Object?.groupBy(
                  [...filterTableArrayBasedOnTitleForPq4(pq4)],
                  ({ table_title }) => table_title,
                ),
              )?.map((tData) => (
                <>
                  {console.log("tdata:", tData?.map(Object?.values))}
                  <div>
                    {/* tableTitle */}
                    {
                      tData?.map(Object?.values)[0][
                        tData?.map(Object?.values)[0]?.length - 1
                      ]
                    }
                  </div>
                  <PQ4
                    key={uuidv4()}
                    rowData={tData?.map(Object?.values)}
                    tableStaticData={
                      pq4_staticData?.length
                        ? pq4_staticData?.filter(
                            (fData) =>
                              fData?.table_title ==
                              tData?.map(Object?.values)[0][
                                tData?.map(Object?.values)[0]?.length - 1
                              ],
                          )[0]
                        : {}
                    }
                    type={type}
                    machineName={machineName}
                    fatDataDocId={details.id}
                  />
                  <br />
                </>
              ))
            }
          </>
        )}

        {pq5?.length > 0 && (
          <>
            {
              // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

              // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
              // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
              // Then we group them and again make them array of array
              Object.values(
                Object?.groupBy(
                  [...filterTableArrayBasedOnTitleForPq5(pq5)],
                  ({ table_title }) => table_title,
                ),
              )?.map((tData) => (
                <>
                  {console.log("tdata:", tData?.map(Object?.values))}
                  <div>
                    {/* tableTitle */}
                    {
                      tData?.map(Object?.values)[0][
                        tData?.map(Object?.values)[0]?.length - 1
                      ]
                    }
                  </div>
                  <PQ5
                    key={uuidv4()}
                    rowData={tData?.map(Object?.values)}
                    // tableStaticData={pq5_staticData?.length ?
                    //   pq5_staticData?.filter((fData) => fData?.table_title ==
                    //     tData?.map(Object?.values)[0][tData?.map(Object?.values)[0]?.length - 1])[0]
                    //   : {}}
                    type={type}
                    machineName={machineName}
                    fatDataDocId={details.id}
                    useAt={"report"}
                  />
                  <br />
                </>
              ))
            }
          </>
        )}

        {/* {postApproval?.length > 0 && ( */}
        {/* { (!(Object.values(Object?.groupBy([...filterTableArrayBasedOnTitleForPostApprovalTable(postApprovalTable)], ({ table_title }) => table_title)))?.length
         && approvalTableType === 7)
         && 
         <>
         <div className="content_sub-section">
          <p>
         <PostApprovalGilPq
          key={uuidv4()}
          rowData={[{}]}
          // tableStaticData={pq5_staticData?.length ?
          //   pq5_staticData?.filter((fData) => fData?.table_title ==
          //     tData?.map(Object?.values)[0][tData?.map(Object?.values)[0]?.length - 1])[0]
          //   : {}}
          type={type}
          machineName={machineName}
          fatDataDocId={details.id}
          useAt={"report"}
        />
        </p>
        </div>
        </>
        } */}

        {/* {postApprovalTable?.length > 0 && ( */}
        {approvalTableType === 7 && (
          <>
            {
              // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

              // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
              // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
              // Then we group them and again make them array of array
              Object.values(
                Object?.groupBy(
                  [
                    ...filterTableArrayBasedOnTitleForPostApprovalTable(
                      postApprovalTable,
                    ),
                  ],
                  ({ table_title }) => table_title,
                ),
              )?.map((tData) => (
                <>
                  {console.log("tdataa:", tData?.map(Object?.values))}
                  <div>
                    {/* tableTitle */}
                    {
                      tData?.map(Object?.values)[0][
                        tData?.map(Object?.values)[0]?.length - 1
                      ]
                    }
                  </div>
                  <PostApprovalGilPq
                    key={uuidv4()}
                    rowData={tData?.map(Object?.values)}
                    // tableStaticData={pq5_staticData?.length ?
                    //   pq5_staticData?.filter((fData) => fData?.table_title ==
                    //     tData?.map(Object?.values)[0][tData?.map(Object?.values)[0]?.length - 1])[0]
                    //   : {}}
                    type={type}
                    machineName={machineName}
                    fatDataDocId={details.id}
                    useAt={"report"}
                  />
                  <br />
                </>
              ))
            }
          </>
        )}

        {table1?.length > 0 && (
          <>
            {table1?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <T1
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {table2?.length > 0 && (
          <>
            {table2?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <T2
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {table3?.length > 0 && (
          <>
            {table3?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <T3
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {table4?.length > 0 && (
          <>
            {table4?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <T4
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq1?.length > 0 && (
          <>
            {iq1?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ1
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq2?.length > 0 && (
          <>
            {iq2?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ2
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq3?.length > 0 && (
          <>
            {iq3?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ3
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq4?.length > 0 && (
          <>
            {iq4?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ4
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq5?.length > 0 && (
          <>
            {iq5?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ5
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq6?.length > 0 && (
          <>
            {iq6?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ6
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq7?.length > 0 && (
          <>
            {iq7?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ7
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq8?.length > 0 && (
          <>
            {iq8?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ8
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq9?.length > 0 && (
          <>
            {iq9?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ9
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq10?.length > 0 && (
          <>
            {iq10?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ10
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq11?.length > 0 && (
          <>
            {iq11?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ11
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq12?.length > 0 && (
          <>
            {iq12?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ12
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq13?.length > 0 && (
          <>
            {iq13?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ13
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq13?.length > 0 && (
          <>
            {iq13?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ13
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {iq14?.length > 0 && (
          <>
            {iq14?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <IQ14
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {mfg1?.length > 0 && (
          <>
            {mfg1?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <MFG1
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {eqp1?.length > 0 && (
          <>
            {eqp1?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <EQP1
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {eqp2?.length > 0 && (
          <>
            {eqp2?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <EQP2
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {eqp3?.length > 0 && (
          <>
            {eqp3?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <EQP3
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {eqp4?.length > 0 && (
          <>
            {eqp4?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <EQP4
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {eqp5?.length > 0 && (
          <>
            {eqp5?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <EQP5
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {oq1?.length > 0 && (
          <>
            {oq1?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <OQ1
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {oq2?.length > 0 && (
          <>
            {oq2?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <OQ2
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {oq3?.length > 0 && (
          <>
            {oq3?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <OQ3
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {oq4?.length > 0 && (
          <>
            {oq4?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <OQ4
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {oq5?.length > 0 && (
          <>
            {oq5?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <OQ5
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {oq6?.length > 0 && (
          <>
            {oq6?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <OQ6
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {oq7?.length > 0 && (
          <>
            {oq7?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <OQ7
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {oq8?.length > 0 && (
          <>
            {oq8?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <OQ8
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {oq9?.length > 0 && (
          <>
            {oq9?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <OQ9
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {oq10?.length > 0 && (
          <>
            {oq10?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <OQ10
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}

        {oq11?.length > 0 && (
          <>
            {oq11?.map((tData) => (
              <div className="content_sub-section">
                <div className="table_title">
                  {/* tableTitle */}
                  <h4 className="font-bold underline mr-3 mb-6">
                    {" "}
                    {tData[0][tData[0]?.length - 1]}{" "}
                  </h4>
                </div>
                <OQ11
                  rowData={tData}
                  type={type}
                  machineName={machineName}
                  fatDataDocId={fatData.id}
                  useAt="reportItem"
                />
                <br />
              </div>
            ))}
          </>
        )}
      </div>
    );
  } else if (approvalTable) {
    return (
      <>
        {approvalTable?.length > 0 && (
          <div className="content_sub-section">
            <div className="table_title"></div>
            <TableContainer component={Paper} className="table">
              <Table sx={{ minWidth: 650, width: "100%" }}>
                <TableHead>
                  <TableRow
                    style={{
                      color: "white",
                      backgroundColor: "#D9D9D9",
                      border: "1px solid black",
                      fontWeight: "bold",
                    }}
                  >
                    <TableCell
                      colSpan={4}
                      style={{ border: "1px solid black", color: "black" }}
                      align="center"
                    >
                      VENDOR
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableHead>
                  <TableRow
                    style={{
                      color: "white",
                      backgroundColor: "#D9D9D9",
                      border: "1px solid black",
                      fontWeight: "bold",
                    }}
                  >
                    <TableCell
                      style={{ border: "1px solid black", color: "black" }}
                      align="left"
                    >
                      Name{" "}
                    </TableCell>
                    <TableCell
                      style={{ border: "1px solid black", color: "black" }}
                      align="left"
                    >
                      Email
                    </TableCell>
                    <TableCell
                      style={{ border: "1px solid black", color: "black" }}
                      align="left"
                    >
                      Approval Status
                    </TableCell>
                    <TableCell
                      style={{ border: "1px solid black", color: "black" }}
                      align="center"
                    >
                      Date
                    </TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {approvalTable
                    ?.filter((data) => data?.type === "vendor")
                    .map((data) => (
                      <ApprovalTableItem key={data.id} data={data} />
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
            <br />
            <TableContainer component={Paper} className="table">
              <Table sx={{ minWidth: 650, width: "100%" }}>
                <TableHead>
                  <TableRow
                    style={{
                      color: "white",
                      backgroundColor: "#D9D9D9",
                      border: "1px solid black",
                      fontWeight: "bold",
                    }}
                  >
                    <TableCell
                      colSpan={4}
                      style={{ border: "1px solid black", color: "black" }}
                      align="center"
                    >
                      CUSTOMER
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableHead>
                  <TableRow
                    style={{
                      color: "white",
                      backgroundColor: "#D9D9D9",
                      border: "1px solid black",
                      fontWeight: "bold",
                    }}
                  >
                    <TableCell
                      style={{ border: "1px solid black", color: "black" }}
                      align="left"
                    >
                      Name{" "}
                    </TableCell>
                    <TableCell
                      style={{ border: "1px solid black", color: "black" }}
                      align="left"
                    >
                      Email
                    </TableCell>
                    <TableCell
                      style={{ border: "1px solid black", color: "black" }}
                      align="left"
                    >
                      Approval Status
                    </TableCell>
                    <TableCell
                      style={{ border: "1px solid black", color: "black" }}
                      align="center"
                    >
                      Date
                    </TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {approvalTable
                    ?.filter((data) => data?.type === "customer")
                    .map((data) => (
                      <ApprovalTableItem key={data.id} data={data} />
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        )}
      </>
    );
  }
};

////////////////////

const ReportItem = ({ type, data }) => {
  // data is one fatData
  const { reportId } = useParams(); //ID of reports from which the fatData has to be fetched
  const history = useNavigate();

  const [editOpen, setEditOpen] = useState(false);
  const { currentUser } = useAuth();
  const [user, setUser] = useState([]);
  const [machineName, setMachineName] = useState("");
  // const [data, setData] = useState({})//
  const { currentMode } = useStateContext();

  useEffect(() => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection("userData")
    //     .where("email", "==", currentUser.email)
    //     .onSnapshot((snap) => {
    //         const data = firebaseLooper(snap);
    //         setUser(data[0]);
    //     });
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(machines)
    //     .doc(data.mid)
    //     .onSnapshot((snap) => {
    //         const data = snap.data();
    //         setMachineName(data.title)
    //     })
  }, []);

  const background = {
    background: currentMode === "Dark" ? "#161C24" : "",
    color: currentMode === "Dark" ? "#fff" : "",
    border: currentMode === "Dark" ? "2px solid #fff" : "",
  };

  return (
    <section key={`Reports-${data._id}`} className="contentViewPage min-h-0">
      <div className="allContentPreviewContainer">
        <main className="contentPageMain " style={background}>
          <div className="contentMainContainer">
            <div className="content_sub-section">
              <header className="flex justify-between w-full">
                <div className="capitalize font-bold text-xl">
                  {data?.title}
                </div>
                {/* {data?.type === 0 && ( */}
                {data?.type === 7 && (
                  <Button
                    onClick={() => {
                      history(`/${reportId}/${data?._id}/add-users-fat`);
                    }}
                    variant="outlined"
                    color="info"
                  >
                    {" "}
                    Add Users to Document
                  </Button>
                )}
              </header>

              <div className="italic font-medium">
                {/* " {data?.desc} " */}
                {data?.desc?.blocks?.length ? (
                  data?.desc.blocks?.map((options) => (
                    <>
                      {options && (
                        <Formatting key={options.id} data={options} />
                      )}
                    </>
                  ))
                ) : (
                  <></>
                )}
              </div>
            </div>

            {(data?.type === 6 || data?.type === 2 || data?.type === 5) && (
              <>
                {data?.objective && (
                  <div className="content_sub-section">
                    <div className="content_subtitle flex">
                      <h4 className="font-bold underline mr-3"> OBJECTIVE:</h4>
                      <p>{data?.objective}</p>
                    </div>
                  </div>
                )}

                {data?.method && (
                  <div className="content_sub-section">
                    <div className="content_subtitle flex">
                      <h4 className="font-bold underline mr-3"> METHOD:</h4>
                      <p>{data?.method}</p>
                    </div>
                  </div>
                )}

                {data?.pre && (
                  <div className="content_sub-section">
                    <div className="content_subtitle">
                      <h4 className="font-bold underline mr-3 mb-2">
                        {" "}
                        PREREQUISITES:
                      </h4>
                      {data.pre.map((point, idx) => (
                        <p className="mb-2" key={idx}>
                          <span className="font-bold ">{idx + 1}.</span> {point}
                        </p>
                      ))}
                    </div>
                  </div>
                )}

                {data?.procedure && (
                  <div className="content_sub-section">
                    <div className="content_subtitle">
                      <h4 className="font-bold underline mr-3 mb-2">
                        {" "}
                        TEST PROCEDURE:
                      </h4>
                      {data.procedure.map((point, idx) => (
                        <p className="mb-1" key={idx}>
                          <span className="font-bold ">{idx + 1}.</span> {point}
                        </p>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}

            {/* //DEVIATION, IMPACT AND CORRECTIVE ACTION SHEET */}
            {data?.type === 5 && <OQLayout />}
            {/* // */}

            {/* //DEVIATION, IMPACT AND CORRECTIVE ACTION SHEET */}
            {data?.type === 6 && <NonConfirmity type={type} docId={data.id} />}
            {/* // */}

            {type === "preview" && (
              <ReportsTable
                approvalTableType={data?.type}
                reportId={reportId}
                type={type}
                fatDataId={data._id}
                fatData={data}
                details={data}
                user={user}
                machineName={machineName}
              />
            )}

            {/* new component for print */}
            {type === "print" && (
              <ReportsTableForPrint type={type} fatDataId={data.id} />
            )}

            {(data?.type === 6 || data?.type === 2 || data?.type === 5) && (
              <div className="content_sub-section">
                <div className="content_subtitle">
                  <h4 className="font-bold  mr-3"> TEST SUMMARY:</h4>
                  <div className="flex items-center justify-start gap-x-4">
                    Document Complies
                    <span>
                      <Checkbox
                        checked={data?.summary}
                        disabled
                        sx={{
                          "&.Mui-disabled": {
                            color: "#2196f3",
                          },
                        }}
                      />
                      Yes
                    </span>
                    <span>
                      <Checkbox
                        checked={!data?.summary}
                        disabled
                        sx={{
                          "&.Mui-disabled": {
                            color: "#2196f3",
                          },
                        }}
                      />
                      No
                    </span>
                  </div>
                </div>
                {type === "preview" && (
                  <div className="">
                    <Edit
                      className=" hover:cursor-pointer hover:text-red-300 hover:shadow-md "
                      onClick={() => setEditOpen(true)}
                    />
                  </div>
                )}
              </div>
            )}

            {data.comment &&
            (data?.type === 6 || data?.type === 2 || data?.type === 5) ? (
              <div className="content_sub-section ">
                <div className="content_subtitle flex">
                  <h4 className="font-bold  mr-3"> COMMENT:</h4>
                  <p>{data?.comment}</p>
                </div>
                {type === "preview" && (
                  <div className="">
                    <Edit
                      className=" hover:cursor-pointer hover:text-red-300 hover:shadow-md "
                      onClick={() => setEditOpen(true)}
                    />
                  </div>
                )}
              </div>
            ) : (
              ""
            )}
          </div>
        </main>
        {/* //dialog for comment and summery update */}
        <Dialog maxWidth="md" fullWidth open={editOpen}>
          <DialogTitle>Edit Report</DialogTitle>
          <DialogContent>
            <EditReports
              details={data}
              user={user}
              machineName={machineName}
              handleClose={() => setEditOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>
      {/* <div className='flex justify-items-center  place-content-center '>***</div> */}
    </section>
  );
};

export default ReportItem;
