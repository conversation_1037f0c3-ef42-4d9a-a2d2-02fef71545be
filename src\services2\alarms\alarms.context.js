import { createContext, useEffect, useState } from "react";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";

export const AlarmsContext = createContext();

const AlarmsProvider = ({ children }) => {
  // const dbref = db.collection(companies).doc(companyId_constant).collection('Alerts');
  // const dbRefMachines = db.collection(companies).doc(companyId_constant).collection('machineData');
  const [alerts, setAlerts] = useState([]);
  const [alertsSorted, setAlertsSorted] = useState([]);
  const [allMachines, setAllMachines] = useState([]);
  const [allMachinesWithAlertsCount, setAllMachinesWithAlertsCount] = useState(
    [],
  );

  useEffect(() => {
    let allMachinesTemp = [];
    let alertTemp = [];

    let promise = new Promise((resolve, reject) => {
      resolve();
    });

    // Only commented out, new implementation required

    // promise.then(() => {
    //     dbRefMachines.onSnapshot(snap => {
    //         const data = firebaseLooper(snap)
    //         setAllMachines(data?.reverse());
    //         allMachinesTemp = data;
    //     })
    // })
    //     .then(() => {
    //         return new Promise((resolve, reject) => {
    //             dbref.onSnapshot(snap => {
    //                 const data = firebaseLooper(snap)
    //                 setAlerts(data);
    //                 setAlertsSorted(data);
    //                 alertTemp = data;
    //                 resolve();
    //             })
    //         })
    //             .then(() => {

    //                 let allMachinesWithAlertsCountTemp = [];
    //                 allMachinesTemp?.map((mData) => {
    //                     let count = 0;

    //                     alertTemp.map((aData) => {
    //                         if (mData.id === aData.mid) {
    //                             count = count + 1;
    //                         }
    //                     })
    //                     allMachinesWithAlertsCountTemp = [{ ...mData, 'alertsCount': count }, ...allMachinesWithAlertsCountTemp];
    //                 })
    //                 setAllMachinesWithAlertsCount([...allMachinesWithAlertsCountTemp?.reverse()]);
    //             })
    //     })
  }, []);

  return (
    <AlarmsContext.Provider
      value={{
        alerts,
        setAlerts,
        alertsSorted,
        setAlertsSorted,
        allMachines,
        setAllMachines,
        allMachinesWithAlertsCount,
        setAllMachinesWithAlertsCount,
      }}
    >
      {children}
    </AlarmsContext.Provider>
  );
};

export default AlarmsProvider;
