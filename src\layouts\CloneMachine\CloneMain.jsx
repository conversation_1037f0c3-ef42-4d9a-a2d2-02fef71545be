import React, { useState } from "react";
import CloneMachine from "./CloneMachine";
import FATClone from "./FATClone";
import LiveDataClone from "./LiveDataClone";
import MaintenanaceClone from "./MaintenanceClone";
import TrainingClone from "./TrainingClone";

const CloneMain = ({ machineDetails }) => {
  const [newMachineId, setNewMachineId] = useState(null);

  return (
    <div>
      <CloneMachine
        setNewMachineId={setNewMachineId}
        machineDetails={machineDetails}
      />
      {newMachineId && (
        <>
          <LiveDataClone
            newMachineId={newMachineId}
            machineDetails={machineDetails}
          />
          <MaintenanaceClone
            newMachineId={newMachineId}
            machineDetails={machineDetails}
          />
          <TrainingClone
            newMachineId={newMachineId}
            machineDetails={machineDetails}
          />
          <FATClone
            newMachineId={newMachineId}
            machineDetails={machineDetails}
          />
        </>
      )}
    </div>
  );
};

export default CloneMain;
