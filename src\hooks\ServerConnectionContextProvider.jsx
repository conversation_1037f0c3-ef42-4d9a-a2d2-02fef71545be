import PropTypes from "prop-types";
import { createContext, useContext, useEffect, useState } from "react";

// const IpSetterContext = createContext();
// const IpGetterContext = createContext();
// const PortSetterContext = createContext();
// const PortGetterContext = createContext();

const ServerConnectionContext = createContext();

// export function useIpSetter () {
//     return useContext(IpSetterContext);
// }
// export function useIpGetter () {
//     return useContext(IpGetterContext);
// }

// export function usePortSetter () {
//     return useContext(PortSetterContext);
// }
// export function usePortGetter () {
//     return useContext(PortGetterContext);
// }

export function useServerConnection() {
  return useContext(ServerConnectionContext);
}

export function ServerConnectionContextProvider({ children }) {
  const defaultport = localStorage.getItem("server_port");
  const defaultip = localStorage.getItem("server_ip");
  const [ip, setIp] = useState(
    defaultip ? defaultip : window.location.hostname,
  );
  const [port, setPort] = useState(defaultport ? defaultport : "5000");

  //
  function setIpHandler(ipAddress) {
    let ip = ipAddress;
    window.localStorage.setItem("server_ip", ip);
    //
    setIp(ip);
  }

  function setPortHandler(portNumber) {
    let port = portNumber;
    localStorage.setItem("server_port", port);
    //
    setPort(port);
  }

  useEffect(() => {
    let serverIp = window.localStorage.getItem("server_ip");
    let serverPort = window.localStorage.getItem("server_port");
    if (serverIp && serverPort) {
      //
      setIp(serverIp);
      setPort(serverPort);
    }
  }, []);

  return (
    <ServerConnectionContext.Provider
      value={[ip, setIpHandler, port, setPortHandler]}
    >
      {children}
    </ServerConnectionContext.Provider>
  );
}
ServerConnectionContextProvider.propTypes = {
  children: PropTypes.node,
};
