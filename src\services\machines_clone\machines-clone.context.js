// FETCH MACHINE TABLE DATA  - FAT LIST  |  FAT DATA  | FAT Tables list

import { createContext, useEffect, useState } from "react";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";

const fatTablesList = {
  table: "table",
  table1: "table1",
  table2: "table2",
  table3: "table3",
  table4: "table4",
  tableIQ1: "tableIQ1",
  tableIQ2: "tableIQ2",
  tableIQ3: "tableIQ3",
  tableIQ4: "tableIQ4",
  tableIQ5: "tableIQ5",
  tableIQ6: "tableIQ6",
  tableIQ7: "tableIQ7",
  tableIQ8: "tableIQ8",
  tableIQ9: "tableIQ9",
  tableIQ10: "tableIQ10",
  tableIQ11: "tableIQ11",
  tableIQ12: "tableIQ12",
  tableIQ13: "tableIQ13",
  tableIQ14: "tableIQ14",
  tableOQ1: "tableOQ1",
  tableOQ2: "tableOQ2",
  tableOQ3: "tableOQ3",
  tableOQ4: "tableOQ4",
  tableOQ5: "tableOQ5",
  tableOQ6: "tableOQ6",
  tableOQ7: "tableOQ7",
  tableOQ8: "tableOQ8",
  tableOQ9: "tableOQ9",
  tableOQ10: "tableOQ10",
  tableOQ11: "tableOQ11",
  tableMFG1: "tableMFG1",
  tableEQP1: "tableEQP1",
  tableEQP2: "tableEQP2",
  tableEQP3: "tableEQP3",
  tableEQP4: "tableEQP4",
  tableEQP5: "tableEQP5",
};

// const ROOT = db.collection(companies).doc(companyId_constant);

export const CloneFATTablesContext = createContext();

export const CloneFatTableProvider = ({ children }) => {
  const [fatList, setFatList] = useState([]);
  const [fatData, setFatData] = useState([]);
  const [fatTables, setFatTables] = useState([]);
  const [fatActive, setFatActive] = useState(false);

  useEffect(() => {
    cloneFatList();
    cloneFatData();
    cloneFatTables();

    console.log("FAT TABLES INSIDE USE EFFECT", fatTables);
  }, [fatActive]);

  const cloneFatList = () => {
    // ROOT.collection("fatList").onSnapshot((snap) => {
    //   const data = firebaseLooper(snap);
    //   setFatList(data);
    // });
  };

  const cloneFatData = () => {
    // ROOT.collection("fatData").onSnapshot((snap) => {
    //   const data = firebaseLooper(snap);
    //   setFatData(data);
    //   setFatActive(true)
    // });
  };

  const cloneFatTables = () => {
    //Temporary table arrays are created
    let table = [];
    let table1 = [];
    let table2 = [];
    let table3 = [];
    let table4 = [];
    let tableIQ1 = [];
    let tableIQ2 = [];
    let tableIQ3 = [];
    let tableIQ4 = [];
    let tableIQ5 = [];
    let tableIQ6 = [];
    let tableIQ7 = [];
    let tableIQ8 = [];
    let tableIQ9 = [];
    let tableIQ10 = [];
    let tableIQ11 = [];
    let tableIQ12 = [];
    let tableIQ13 = [];
    let tableIQ14 = [];
    let tableOQ1 = [];
    let tableOQ2 = [];
    let tableOQ3 = [];
    let tableOQ4 = [];
    let tableOQ5 = [];
    let tableOQ6 = [];
    let tableOQ7 = [];
    let tableOQ8 = [];
    let tableOQ9 = [];
    let tableOQ10 = [];
    let tableOQ11 = [];
    let tableMFG1 = [];
    let tableEQP1 = [];
    let tableEQP2 = [];
    let tableEQP3 = [];
    let tableEQP4 = [];
    let tableEQP5 = [];

    for (let i in fatData) {
      // const MAIN = ROOT.collection("fatData").doc(fatData[i].id);

      // MAIN.collection(fatTablesList.table).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   table.push(...tableData, { fatMainId: fatData[i].id });
      // });

      // MAIN.collection(fatTablesList.table1).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   table1.push(...tableData, { fatMainId: fatData[i].id });
      // });

      // MAIN.collection(fatTablesList.table2).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   table2.push(...tableData, { fatMainId: fatData[i].id });
      // });

      // MAIN.collection(fatTablesList.table3).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   table3.push(...tableData, { fatMainId: fatData[i].id });
      // });

      // MAIN.collection(fatTablesList.table4).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   table4.push(...tableData, { fatMainId: fatData[i].id });
      // });

      // //FETCH IQ 1 to 14
      // MAIN.collection(fatTablesList.tableIQ1).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ1.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ2).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ2.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ3).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ3.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ4).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ4.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ5).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ5.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ6).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ6.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ7).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ7.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ8).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ8.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ9).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ9.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ10).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ10.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ11).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ11.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ12).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ12.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ13).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ13.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableIQ14).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableIQ14.push(...tableData, { fatMainId: fatData[i].id });
      // });

      // //OQ 1 to 11
      // MAIN.collection(fatTablesList.tableOQ1).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableOQ1.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableOQ2).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableOQ2.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableOQ3).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableOQ3.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableOQ4).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableOQ4.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableOQ5).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableOQ5.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableOQ6).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableOQ6.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableOQ7).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableOQ7.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableOQ8).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableOQ8.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableOQ9).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableOQ9.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableOQ10).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableOQ10.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableOQ11).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableOQ11.push(...tableData, { fatMainId: fatData[i].id });
      // });

      // //EQP 1 to 5
      // MAIN.collection(fatTablesList.tableEQP1).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableEQP1.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableEQP2).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableEQP2.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableEQP3).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableEQP3.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableEQP4).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableEQP4.push(...tableData, { fatMainId: fatData[i].id });
      // });
      // MAIN.collection(fatTablesList.tableEQP5).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableEQP5.push(...tableData, { fatMainId: fatData[i].id });
      // });

      // //MFG1
      // MAIN.collection(fatTablesList.tableMFG1).onSnapshot((snap) => {
      //   const tableData = firebaseLooper(snap);
      //   tableMFG1.push(...tableData, { fatMainId: fatData[i].id });
      // });

      setFatTables({
        table,
        table1,
        table2,
        table3,
        table4,
        tableIQ1,
        tableIQ2,
        tableIQ3,
        tableIQ4,
        tableIQ5,
        tableIQ6,
        tableIQ7,
        tableIQ8,
        tableIQ9,
        tableIQ10,
        tableIQ11,
        tableIQ12,
        tableIQ13,
        tableIQ14,
        tableOQ1,
        tableOQ2,
        tableOQ3,
        tableOQ4,
        tableOQ5,
        tableOQ6,
        tableOQ7,
        tableOQ8,
        tableOQ9,
        tableOQ10,
        tableOQ11,
        tableMFG1,
        tableEQP1,
        tableEQP2,
        tableEQP3,
        tableEQP4,
        tableEQP5,
      });
    }
  };

  return (
    <CloneFATTablesContext.Provider value={{ fatList, fatData, fatTables }}>
      {children}
    </CloneFATTablesContext.Provider>
  );
};
