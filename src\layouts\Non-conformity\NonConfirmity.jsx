// outer structure css => contentPage.scss
import React, { useState, useEffect } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { useStateContext } from "../../context/ContextProvider";
import { db } from "../../firebase";
import { companies, companyId_constant } from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";

export default function NonConfirmity({ type, docId }) {
  const { currentMode } = useStateContext();
  const [tableNonConformityCheckedBy, setTableNonConformityCheckedBy] =
    useState([]);

  useEffect(() => {
    //tabletypes data
    // db.collection(companies)
    // .doc(companyId_constant)
    // .collection(type)
    // .doc(docId)
    // .collection("tableNon_conformity_checked_by")  // table
    // .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     let dataSorted = data.sort((a, b) => a['serial_no'] - b['serial_no']);
    //     const tempArr2d = [];
    //     dataSorted.map((dataM) => {
    //         const temp = [];
    //        // temp.push(dataM['serial_no']); // order is important
    //         temp.push(dataM['company_name']); //
    //         temp.push(dataM['name']); //
    //         temp.push(dataM['signatures']); //
    //         temp.push(dataM['date']); //
    //         temp.push(dataM['url']); //
    //         temp.push(dataM['id']); ////
    //         tempArr2d.push(temp)
    //     })
    //     setTableNonConformityCheckedBy(tempArr2d);
    //     console.log("tableNonConformityCheckedBy data: ", tempArr2d);
    //     //window.localStorage.setItem('table1',JSON.stringify(tempArr2d))
    // });
  }, []);

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#000" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };
  return (
    <div className="content_sub-section">
      <div className="content_subtitle flex mb-4">
        <h4 className="font-bold  mr-3"> Non-Conformity record:</h4>
      </div>

      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          {/* <TableHead sx={{ border: theme.borderDesign, background: theme.backgroundColor }}>
                        </TableHead> */}

          <TableBody>
            <TableRow>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={2}>
                Number Of Non-Conformity Records attached? _____________
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell sx={{ border: theme.borderDesign }}>
                {" "}
                Checked by
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}> Date </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>

      <h4 className="font-bold  mr-3 my-4"> Test Summary </h4>

      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          {/* <TableHead sx={{ border: theme.borderDesign, background: theme.backgroundColor }}>
                        </TableHead> */}

          <TableBody>
            <TableRow>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={5}>
                <div className="flex justify-between w-6/12">
                  <div>TEST RESULTS</div>
                  <div>
                    <input type="Checkbox" /> PASS{" "}
                  </div>
                  <div>
                    <input type="Checkbox" /> FAIL{" "}
                  </div>
                </div>
              </TableCell>
            </TableRow>
            <TableRow
              sx={{
                border: theme.borderDesign,
                background: theme.backgroundColor,
              }}
            >
              <TableCell sx={{ border: theme.borderDesign }}> </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                {" "}
                COMPANY'S NAME{" "}
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}> NAME</TableCell>
              <TableCell sx={{ border: theme.borderDesign }} width="10%">
                {" "}
                SIGNATURE{" "}
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}> DATE </TableCell>
            </TableRow>

            {tableNonConformityCheckedBy?.length > 0 ? (
              tableNonConformityCheckedBy?.map((tdata) => (
                <TableRow>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {" "}
                    Checked by{" "}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {" "}
                    {tdata[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {" "}
                    {tdata[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {" "}
                    {tdata[2]}{" "}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {" "}
                    {tdata[3]}{" "}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell sx={{ border: theme.borderDesign }}>
                  {" "}
                  Checked by{" "}
                </TableCell>
                <TableCell sx={{ border: theme.borderDesign }}> </TableCell>
                <TableCell sx={{ border: theme.borderDesign }}> </TableCell>
                <TableCell sx={{ border: theme.borderDesign }}> </TableCell>
                <TableCell sx={{ border: theme.borderDesign }}> </TableCell>
              </TableRow>
            )}

            <TableRow>
              <TableCell sx={{ border: theme.borderDesign }}>
                {" "}
                Approved by{" "}
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}> </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}> </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}> </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}> </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
}
