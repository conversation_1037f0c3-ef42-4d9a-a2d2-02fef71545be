// this is old machine code, worked fine // delete this component after testing
import { Pa<PERSON><PERSON>, Stack } from "@mui/material";
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { companies, companyId_constant, machines } from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import "./Machine.scss";
import MachineItem from "./MachineItem";
import Box from "@mui/material/Box";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import { useStateContext } from "../../context/ContextProvider";
import AddIcon from "@mui/icons-material/Add";
import {
  Button,
  InputLabel,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
} from "@mui/material";
import { toastMessageSuccess } from "../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";
import { themeColors } from "../../infrastructure/theme";
import PageHeader from "../../components/commons/page-header.component";
import { v4 as uuidv4 } from "uuid";
import { useMachinesAllGetter } from "../../services3/machines/MachineContext2";
import CommonDropDownComponent from "../../components/commons/dropDown.component";

const Machine = () => {
  //const [allMachinesData, setAllMachinesData] = useState([]);
  const allMachinesData = useMachinesAllGetter();
  const [machinesInView, setMachinesInView] = useState([]);

  const [machinesInViewContainerCount, setMachinesInViewContainerCount] =
    useState(0); // number of pages
  const [page, setPage] = useState(1);
  const [numberOfMachineInView, setNumberOfMachinesInView] = useState(6);

  const [sortBy, setSortBy] = useState("none");
  const [sortDirection, setSortDirection] = useState("up");
  const [searchKey, setSearchKey] = useState("");
  const [searchedMachines, setSearchedMachines] = useState([]);
  const { currentColor, currentMode, currentColorLight } = useStateContext();

  //u.m.2
  const [title, setTitle] = useState();
  const [desc, setDesc] = useState();
  const [location, setLocation] = useState();
  const [equipmentId, setEquipmentId] = useState();
  const [serialNo, setSerialNo] = useState();
  const [model, setModel] = useState();
  const [block, setBlock] = useState();
  const [warranty, setWarranty] = useState();
  const [status, setStatus] = useState();

  const [openEdit, setOpenEdit] = useState(false);
  const [machineId, setMachineId] = useState();

  //

  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(machines)
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     let machineActivePageNum = window.localStorage.getItem(
    //       "machineActivePageNum"
    //     ); // for preventing pagination mismatch while editing 2/3
    //     let numOfMachineInViewSession =
    //       window.localStorage.getItem("numOfMachineInView"); //
    //     setAllMachinesData(data?.reverse());
    //     setMachinesInView(data.slice(0, numberOfMachineInView));
    //     machinesPageInfoContainerCounter(data.length, numberOfMachineInView);
    //     // for preventing pagination mismatch while editing 3/3
    //     if (machineActivePageNum != 1 && machineActivePageNum) {
    //       let machineInViewContainerCountTemp =
    //         machinesPageInfoContainerCounter(
    //           data.length,
    //           numOfMachineInViewSession
    //         ); //if it is last page
    //       if (machineActivePageNum == machineInViewContainerCountTemp) {
    //         setMachinesInView(
    //           data.slice((machineActivePageNum - 1) * numOfMachineInViewSession)
    //         );
    //         setNumberOfMachinesInView(numOfMachineInViewSession);
    //         setPage(machineActivePageNum);
    //         setNumberOfMachinesInView(numOfMachineInViewSession);
    //       } else {
    //         // if it's not the last page
    //         setMachinesInView(
    //           data.slice(
    //             (machineActivePageNum - 1) * numOfMachineInViewSession,
    //             machineActivePageNum * numOfMachineInViewSession
    //           )
    //         );
    //         setNumberOfMachinesInView(numOfMachineInViewSession);
    //         setPage(machineActivePageNum);
    //         setNumberOfMachinesInView(numOfMachineInViewSession);
    //       }
    //     }
    //   });
  }, []);

  //u.m.3
  const handleUpdateMachine = (e) => {
    e.preventDefault();
    const dataSet = {
      title,
      desc,
      location,
      equipmentId,
      serialNo,
      model,
      block,
      warranty,
      status,
      createdAt: new Date(),
      lastUpdated: new Date(),
    };

    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(machines)
    //   .doc(machineId)
    //   .update(dataSet)
    //   .then(() => {
    //     setOpenEdit(false);
    //     toastMessageSuccess({ message: "successfully updated" });
    //   })
    //   .catch((e) => alert("update failed: ", e));
  };

  //u.m.4
  const handleOnClikEditMachine = (machine) => {
    setTitle(machine.title);
    setDesc(machine.desc);
    setLocation(machine.location);
    setEquipmentId(machine.equipmentId);
    setSerialNo(machine.serialNo);
    setModel(machine.model);
    setBlock(machine.block);
    setWarranty(machine.warranty);
    setStatus(machine.status);
    setMachineId(machine.id);
    setOpenEdit(true);
  };

  //
  const machinesPageInfoContainerCounter = (
    allMachinesCount,
    numberOfMachineInViewProp,
  ) => {
    var temp = Math.ceil(allMachinesCount / numberOfMachineInViewProp);
    setMachinesInViewContainerCount(temp);
    return temp;
  };
  //
  const handlePageChange = (event, value) => {
    window.localStorage.setItem("machineActivePageNum", value); // for preventing pagination mismatch while editing 1/3
    window.localStorage.setItem("numOfMachineInView", numberOfMachineInView); //
    setPage(value);

    if (searchedMachines.length > 0) {
      if (value == machinesInViewContainerCount) {
        setMachinesInView(
          searchedMachines.slice((value - 1) * numberOfMachineInView),
        );
      } else {
        setMachinesInView(
          searchedMachines.slice(
            (value - 1) * numberOfMachineInView,
            value * numberOfMachineInView,
          ),
        );
      }
    } else {
      if (value == machinesInViewContainerCount) {
        setMachinesInView(
          allMachinesData.slice((value - 1) * numberOfMachineInView),
        );
      } else {
        setMachinesInView(
          allMachinesData.slice(
            (value - 1) * numberOfMachineInView,
            value * numberOfMachineInView,
          ),
        );
      }
    }
  };
  //

  const handleNumberOfMachineInView = (e) => {
    window.localStorage.setItem("numOfMachineInView", e.target.value); //for preventing pagination mismatch while editing 1.1/3
    setNumberOfMachinesInView(e.target.value);
    machinesPageInfoContainerCounter(allMachinesData?.length, e.target.value);
    setMachinesInView(allMachinesData.slice(0, e.target.value));
  };
  //
  const handleSort = (e) => {
    setSortBy(e.target.value);

    if (e.target.value == "name") {
      //console.log("Before sorted:", machinesInView)
      let temp = machinesInView;

      if (sortDirection == "up") {
        temp.sort((a, b) => {
          return a.title?.toLowerCase() > b.title?.toLowerCase() ? 1 : -1;
        });
      } else if (sortDirection == "down") {
        temp.sort((a, b) => {
          return a.title?.toLowerCase() < b.title?.toLowerCase() ? 1 : -1;
        });
      }

      setMachinesInView([...temp]);
      // console.log("sorted:", temp)
    } else if (e.target.value == "equip_id") {
      //console.log("Before sorted:", machinesInView)
      let temp = machinesInView;

      if (sortDirection == "up") {
        temp.sort((a, b) => {
          return a.equipmentId?.toLowerCase() > b.equipmentId?.toLowerCase()
            ? 1
            : -1;
        });
      } else if (sortDirection == "down") {
        temp.sort((a, b) => {
          return a.equipmentId?.toLowerCase() < b.equipmentId?.toLowerCase()
            ? 1
            : -1;
        });
      }

      setMachinesInView([...temp]);
      // console.log("sorted:", temp)
    } else if (e.target.value == "model") {
      //console.log("Before sorted:", machinesInView)
      let temp = machinesInView;

      if (sortDirection == "up") {
        temp.sort((a, b) => {
          return a.model?.toLowerCase() > b.model?.toLowerCase() ? 1 : -1;
        });
      } else if (sortDirection == "down") {
        temp.sort((a, b) => {
          return a.model?.toLowerCase() < b.model?.toLowerCase() ? 1 : -1;
        });
      }

      setMachinesInView([...temp]);
      // console.log("sorted:", temp)
    } else if (e.target.value == "last_updated") {
      //console.log("Before sorted:", machinesInView)
      let temp = machinesInView;

      if (sortDirection == "up") {
        temp.sort((a, b) => {
          return a.lastUpdated > b.lastUpdated ? 1 : -1;
        });
      } else if (sortDirection == "down") {
        temp.sort((a, b) => {
          return a.lastUpdated < b.lastUpdated ? 1 : -1;
        });
      }

      setMachinesInView([...temp]);
      // console.log("sorted:", temp)
    } else if (e.target.value == "none") {
      setMachinesInView(allMachinesData.slice(0, numberOfMachineInView));
      //setSortDirection('none');
    }
  };
  // direction up = increasing & down = decreasing
  const handleSortDirection = (direction) => {
    let temp = machinesInView;
    if (direction == "up") {
      temp.sort((a, b) => {
        return a.title?.toLowerCase() < b.title?.toLowerCase() ? 1 : -1;
      });
      setSortDirection("down");
    } else if (direction == "down") {
      temp.sort((a, b) => {
        return a.title?.toLowerCase() > b.title?.toLowerCase() ? 1 : -1;
      });
      setSortDirection("up");
    }
    setMachinesInView([...temp]);
  };
  //
  const searchMachines = (e) => {
    setSearchKey(e.target.value);
    //console.log("e", e.target.value)
    const temp = allMachinesData;
    //console.log("machineInView:",temp)
    const temp2 = temp?.filter(
      (a) =>
        a.title?.toUpperCase().search(e.target.value.toUpperCase()) >= 0 || //  for dictionary search use (=== 0)
        a.equipmentId?.toUpperCase().search(e.target.value.toUpperCase()) >= 0,
    );
    //console.log("machineInView filterd:", temp2);
    setSearchedMachines([...temp2]); // this data is to handle pagination after searching
    setMachinesInView(temp2?.slice(0, numberOfMachineInView));
    machinesPageInfoContainerCounter(temp2?.length, numberOfMachineInView);
  };

  return (
    <section
      style={
        currentMode === "Dark"
          ? { backgroundColor: "#161C24", color: "white" }
          : { backgroundColor: currentColorLight }
      }
      className="machineContainer"
    >
      <div className="flex justify-between">
        <PageHeader title="Machines" subText="List of all Machines & Details" />

        <div className="flex p-1 py-6">
          <div className="px-1">
            <Box
              component="form"
              sx={{
                "& > :not(style)": { width: "25ch" },
              }}
            >
              <TextField
                label="Search"
                id="outlined-size-small"
                //defaultValue="Name, Eqip id"
                placeholder="Name, EQP ID"
                size="small"
                value={searchKey}
                onChange={(e) => searchMachines(e)}
              />
            </Box>
          </div>

          <div className="flex">
            {sortBy != "none" && (
              <>
                {sortDirection == "up" ? (
                  <i
                    className="ri-arrow-up-line hover:cursor-pointer hover:font-bold self-center"
                    onClick={() => handleSortDirection("up")}
                  ></i>
                ) : (
                  <i
                    className="ri-arrow-down-line hover:cursor-pointer hover:font-bold self-center"
                    onClick={() => handleSortDirection("down")}
                  ></i>
                )}
              </>
            )}
          </div>

          <div className="px-1">
            {/*
            <Box sx={{ minWidth: 100 }}>
              <FormControl fullWidth size="small">
                <InputLabel id="demo-simple-select-label">Sort</InputLabel>
                <Select
                  labelId="demo-simple-select-label"
                  id="demo-simple-select"
                  value={sortBy}
                  label="Sort"
                  onChange={(e) => handleSort(e)}
                >
                  <MenuItem value="none">None</MenuItem>
                  <MenuItem value="name">By Name</MenuItem>
                  <MenuItem value="model">By Model</MenuItem>
                  <MenuItem value="last_updated">By Last Updated</MenuItem>
                  <MenuItem value="equip_id">By Equipment</MenuItem>
                </Select>
              </FormControl>
            </Box>
            */}
            <CommonDropDownComponent
              dropDownContainerStyle={{ minWidth: "100px" }}
              dropDownLabel={"Sort"}
              menuValue={sortBy}
              menuValueDefault={sortBy}
              menuItemDisplay={"label"}
              menuItemValue={"value"}
              menuData={[
                { id: "none", label: "None", value: "none" },
                { id: "name", label: "By Name", value: "name" },
                { id: "model", label: "By Model", value: "model" },
                {
                  id: "last_updated",
                  label: "By Last Updated",
                  value: "last_updated",
                },
                { id: "equip_id", label: "By Equipment", value: "equip_id" },
              ]}
              handleChange={handleSort}
            />
          </div>

          <div className="px-1">
            {/*
            <Box sx={{ minWidth: 50 }}>
              <FormControl fullWidth size="small">
                <InputLabel id="demo-simple-select-label">Items</InputLabel>
                <Select
                  labelId="demo-simple-select-label"
                  id="demo-simple-select"
                  value={numberOfMachineInView}
                  label="Items"
                  onChange={(e) => handleNumberOfMachineInView(e)}
                >
                  <MenuItem value={6}>6</MenuItem>
                  <MenuItem value={12}>12</MenuItem>
                  <MenuItem value={18}>18</MenuItem>
                </Select>
              </FormControl>
            </Box>
            */}
            <CommonDropDownComponent
              dropDownContainerStyle={{ minWidth: "50px" }}
              dropDownLabel={"Items"}
              menuValue={numberOfMachineInView}
              menuValueDefault={numberOfMachineInView}
              menuItemDisplay={"label"}
              menuItemValue={"value"}
              menuData={[
                { id: "6", label: "6", value: 6 },
                { id: "12", label: "12", value: 12 },
                { id: "18", label: "18", value: 18 },
              ]}
              handleChange={handleNumberOfMachineInView}
            />
          </div>
          <div className="px-1 pr-4">
            <Link to="/add-machine" className="formLink ">
              {/* <button className='addButton'>Add Machine</button> */}
              <ButtonBasic
                type="button"
                //style={{ backgroundColor: currentColor }}
                //className="text-md opacity-0.9 text-white  hover:drop-shadow-xl rounded-md  p-2"
                buttonTitle="Add Machine "
              />
            </Link>
          </div>
        </div>
      </div>

      <div className="machinesOuterContainer ">
        <div className="machinesInnerContainer ">
          {machinesInView.map((machineData, index) => (
            <MachineItem
              machine={machineData}
              key={uuidv4()}
              onClickEdit={() => handleOnClikEditMachine(machineData)}
            />
          ))}
        </div>
        <div className="flex justify-center">
          <Stack spacing={2}>
            <Pagination
              count={machinesInViewContainerCount}
              page={page}
              onChange={handlePageChange}
            />
          </Stack>
        </div>
      </div>

      {/* //u.m.1 This edit dialog box should be here only, [because of pagination] */}
      <Dialog open={openEdit} fullWidth>
        <DialogTitle
          style={
            currentMode === "Dark"
              ? { backgroundColor: themeColors.dark.secordary }
              : { backgroundColor: "white" }
          }
        >
          Edit Machines - [{title}]
        </DialogTitle>
        <DialogContent
          style={
            currentMode === "Dark"
              ? { backgroundColor: themeColors.dark.secordary }
              : { backgroundColor: "white" }
          }
        >
          <form onSubmit={handleUpdateMachine}>
            <InputLabel style={{ marginBottom: "10px" }}>Title</InputLabel>
            <TextField
              onChange={(e) => setTitle(e.target.value)}
              value={title}
              onBlur={() => setTitle(title?.trim())}
              required
              variant="outlined"
              fullWidth
              style={{ marginBottom: "12px" }}
            />

            <InputLabel style={{ marginBottom: "10px" }}>Location</InputLabel>
            <TextField
              onChange={(e) => setLocation(e.target.value)}
              value={location}
              onBlur={() => setLocation(location?.trim())}
              required
              variant="outlined"
              fullWidth
              style={{ marginBottom: "12px" }}
            />

            <InputLabel style={{ marginBottom: "10px" }}>
              Equipment ID
            </InputLabel>
            <TextField
              onChange={(e) => setEquipmentId(e.target.value)}
              value={equipmentId}
              onBlur={() => setEquipmentId(equipmentId?.trim())}
              required
              variant="outlined"
              fullWidth
              style={{ marginBottom: "12px" }}
            />
            <InputLabel style={{ marginBottom: "10px" }}>Serial No</InputLabel>
            <TextField
              onChange={(e) => setSerialNo(e.target.value)}
              value={serialNo}
              onBlur={() => setSerialNo(serialNo?.trim())}
              required
              variant="outlined"
              fullWidth
              style={{ marginBottom: "12px" }}
            />
            <InputLabel style={{ marginBottom: "10px" }}>Model</InputLabel>
            <TextField
              onChange={(e) => setModel(e.target.value)}
              value={model}
              onBlur={() => setModel(model?.trim())}
              required
              variant="outlined"
              fullWidth
              style={{ marginBottom: "12px" }}
            />

            <InputLabel style={{ marginBottom: "10px" }}>Warranty</InputLabel>
            <TextField
              onChange={(e) => setWarranty(e.target.value)}
              value={warranty}
              onBlur={() => setWarranty(warranty?.trim())}
              required
              variant="outlined"
              fullWidth
              style={{ marginBottom: "12px" }}
            />

            <InputLabel style={{ marginBottom: "10px" }}>Block</InputLabel>
            <TextField
              onChange={(e) => setBlock(e.target.value)}
              value={block}
              onBlur={() => setBlock(block?.trim())}
              required
              variant="outlined"
              fullWidth
              style={{ marginBottom: "12px" }}
            />

            <InputLabel style={{ marginBottom: "10px" }}>Status</InputLabel>
            <TextField
              onChange={(e) => setStatus(e.target.value)}
              value={status}
              onBlur={() => setStatus(status?.trim())}
              required
              variant="outlined"
              fullWidth
              style={{ marginBottom: "12px" }}
            />
            <InputLabel style={{ marginBottom: "10px" }}>
              Description
            </InputLabel>
            <TextField
              onChange={(e) => setDesc(e.target.value)}
              value={desc}
              onBlur={() => setDesc(desc?.trim())}
              required
              variant="outlined"
              fullWidth
              multiline
              style={{ marginBottom: "12px" }}
            />
            <div fullWidth className="py-2 mt-2 flex justify-between">
              {/* <Button color="success" onClick={() => setOpenEdit(false)} variant='contained'>Cancel</Button> */}
              <ButtonBasicCancel
                buttonTitle="Cancel"
                type="button"
                onClick={() => setOpenEdit(false)}
              />
              {/* <Button color="error" type="submit" variant='contained'>Submit</Button> */}
              <ButtonBasic buttonTitle="Submit" type="submit" />
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* // */}
    </section>
  );
};

export default Machine;
