import React from "react";
import {
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
} from "@mui/material";

const DownTimeTable = ({ oeeData }) => {
  return (
    <div>
      <Typography variant="h6" gutterBottom>
        Downtime Data Table
      </Typography>
      <Table
        sx={{ border: "1px solid rgba(224, 224, 224, 1)" }}
        variant="outlined"
      >
        <TableHead>
          <TableRow>
            <TableCell sx={{ border: "1px solid rgba(224, 224, 224, 1)" }}>
              Reason
            </TableCell>
            <TableCell sx={{ border: "1px solid rgba(224, 224, 224, 1)" }}>
              Criticality
            </TableCell>
            <TableCell sx={{ border: "1px solid rgba(224, 224, 224, 1)" }}>
              Description
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {oeeData?.downtimeReasons.map((event, index) => (
            <TableRow
              key={index}
              sx={{ "& > *": { border: "1px solid rgba(224, 224, 224, 1)" } }}
            >
              <TableCell>{event.reason}</TableCell>
              <TableCell>{event.criticality}</TableCell>
              <TableCell>{event.description}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default DownTimeTable;
