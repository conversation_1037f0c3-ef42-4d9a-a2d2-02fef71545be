import React from "react";
import LinearProgress from "@mui/material/LinearProgress";
import Box from "@mui/material/Box";
import { Typography } from "@mui/material";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";

const CustomProgressBar = ({ stepData }) => {
  const totalSteps = stepData.length;
  const stepWidth = 100 / totalSteps;

  return (
    <Box m={2} display="flex" alignItems="center">
      {stepData.map((step, index) => (
        <div
          key={index}
          style={{
            position: "relative",
            width: `${stepWidth}%`,
            marginRight: "4px",
          }}
        >
          <FiberManualRecordIcon
            // color={step.comment ? 'success' : 'disabled'}
            fontSize="large"
            style={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              backgroundColor: "white",
            }}
          />
          <Typography
            variant="subtitle1"
            style={{ textAlign: "center", marginTop: "8px" }}
          >
            {index + 1}
          </Typography>
          <LinearProgress
            variant="determinate"
            value={step.comment ? 100 : 0}
            sx={{
              bgcolor: step.comment ? "#65B741" : "#808080",
            }}
          />
        </div>
      ))}
    </Box>
  );
};

export default CustomProgressBar;
