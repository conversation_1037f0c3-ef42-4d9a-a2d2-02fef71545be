/* eslint-disable react/jsx-no-target-blank */
import {
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  LinearProgress,
  TableCell,
  TableRow,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { companies, companyId_constant, fatReport } from "../../constants/data";
import { db } from "../../firebase";
import EditIcon from "@mui/icons-material/Edit";
import EditTable from "./EditTable";
import VideoCameraBackIcon from "@mui/icons-material/VideoCameraBack";
import ImageIcon from "@mui/icons-material/Image";
import ArticleIcon from "@mui/icons-material/Article";
import AudioFileIcon from "@mui/icons-material/AudioFile";
import { Button, TextField, Typography } from "@mui/material";
import { useStorage } from "../../utils/useStorage";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { toastMessageSuccess, toastMessage } from "../../tools/toast";
import LoggingFunction from "../../components/CFR-Report/LoggingFunction";

const DataTableItem = ({ fatId, data, type, details, user, machineName }) => {
  const { reportId } = useParams(); //ID of reports from which the fatData has to be fetched
  const [openVideo, setOpenVideo] = useState(false);
  const [open, setOpen] = useState(false);
  const [openImage, setOpenImage] = useState(false);
  const [openAudio, setOpenAudio] = useState(false);
  const [openText, setOpenText] = useState(false);
  const [videoFiles, setVideoFiles] = useState(
    data.videos ? [...data.videos] : [],
  );
  const [audioFiles, setAudioFiles] = useState(
    data.audioFiles ? [...data.audios] : [],
  );
  const [imageFiles, setImages] = useState(data.images ? [...data.images] : []);
  const [textFiles, setTextFiles] = useState(
    data.textFiles ? [...data.textFiles] : [],
  );
  const [mediaProofTitle, setMediaProofTitle] = useState("");
  //   const databaseCollection = db
  //     .collection(companies)
  //     .doc(companyId_constant)
  //     .collection(fatReport)
  //     .doc(reportId)
  //     .collection(`fatData`)
  //     .doc(fatId)
  //     .collection("table")
  //     .doc(data.id);

  // useEffect(() => {
  // 	setImages(data.images)
  // 	setVideoFiles(data.videos)
  // 	setAudioFiles(data.audios)
  // 	setTextFiles(data.textFiles)

  // }, [videoFiles, audioFiles, textFiles, imageFiles, data])

  const [file, setFile] = useState(null);

  const handleMediaChange = (e) => {
    let selectedFile = e.target.files[0];
    setFile(selectedFile);
  };

  const { progress, url } = useStorage(file);

  const checkIfValid = (a, b) => {
    if (a.trim() === "" || b.trim() === "") {
      toastMessage({ message: "Empty inputs are invalid!" });
      return true;
    }
    return false;
  };

  const submitVideoDetails = (e) => {
    const dataValues = { mediaTitle: mediaProofTitle, videoUrl: url };
    if (checkIfValid(mediaProofTitle, url)) return;
    // databaseCollection.update({ videos: [...videoFiles, dataValues] }).then(() => {
    // 	setFile(null)
    // 	setMediaProofTitle('')
    // 	LoggingFunction(
    // 		machineName,
    // 		details?.title,
    // 		user?.fname + " " + user?.lname,
    // 		"FAT Reports",
    // 		`Video is added in test execution of ${details?.title}`,
    // 	)
    // 	toastMessageSuccess({ message: 'Media added successfully !' })
    // })
  };

  const submitAudioDetails = (e) => {
    const dataValues = { mediaTitle: mediaProofTitle, audioUrl: url };
    if (checkIfValid(mediaProofTitle, url)) return;
    // databaseCollection.update({ audios: [...audioFiles, dataValues] }).then(() => {
    // 	setFile(null)
    // 	setMediaProofTitle('')
    // 	LoggingFunction(
    // 		machineName,
    // 		details?.title,
    // 		user?.fname + " " + user?.lname,
    // 		"FAT Reports",
    // 		`Audio is added in test execution of ${details?.title}`,
    // 	)
    // 	toastMessageSuccess({ message: 'Media added successfully !' })
    // })
  };

  const submitImageDetails = (e) => {
    const dataValues = { mediaTitle: mediaProofTitle, imageUrl: url };
    if (checkIfValid(mediaProofTitle, url)) return;
    // databaseCollection.update({ images: [...imageFiles, dataValues] }).then(() => {
    // 	setFile(null)
    // 	setMediaProofTitle('')
    // 	LoggingFunction(
    // 		machineName,
    // 		details?.title,
    // 		user?.fname + " " + user?.lname,
    // 		"FAT Reports",
    // 		`Image is added in test execution of ${details?.title}`,
    // 	)
    // 	toastMessageSuccess({ message: 'Media added successfully !' })
    // })
  };

  const submitTextDetails = (e) => {
    const dataValues = { mediaTitle: mediaProofTitle, textUrl: url };
    if (checkIfValid(mediaProofTitle, url)) return;
    // databaseCollection.update({ textFiles: [...textFiles, dataValues] }).then(() => {
    // 	setFile(null)
    // 	setMediaProofTitle('')
    // 	LoggingFunction(
    // 		machineName,
    // 		details?.title,
    // 		user?.fname + " " + user?.lname,
    // 		"FAT Reports",
    // 		`Text is added in test execution of ${details?.title}`,
    // 	)
    // 	toastMessageSuccess({ message: 'Media added successfully !' })
    // })
  };

  return (
    <>
      <TableRow
        sx={{
          "&:last-child td, &:last-child th": { border: 0 },
        }}
        style={{ cursor: "pointer" }}
      >
        <TableCell style={{ border: "1px solid black" }} align="left">
          {data.check}
        </TableCell>
        <TableCell style={{ border: "1px solid black" }} align="center">
          {data.observation}
        </TableCell>
        <TableCell style={{ border: "1px solid black" }} align="center">
          {data.acceptance}
        </TableCell>
        <TableCell style={{ border: "1px solid black" }} align="center">
          {data.confirm}
        </TableCell>
        <TableCell style={{ border: "1px solid black" }} align="center">
          {data.dev}
        </TableCell>
        {type === "preview" && (
          <TableCell align="center">
            {/* <IconButton onClick={() => setOpenText(true)}>
					<ArticleIcon/>
					</IconButton> */}
            {data.process_active && (
              <div className="flex justify-center">
                <div className=" w-2 h-2 rounded-md bg-red-700 animate-ping"></div>
              </div>
            )}
            {/* <IconButton>
						<CircularProgress />
					</IconButton> */}
            <IconButton onClick={() => setOpenAudio(true)}>
              <AudioFileIcon />
            </IconButton>
            <IconButton onClick={() => setOpenImage(true)}>
              <ImageIcon />
            </IconButton>
            {/* <IconButton onClick={() => setOpenVideo(true)}>
					<VideoCameraBackIcon/>
					</IconButton> */}
            <IconButton onClick={() => setOpen(true)}>
              <EditIcon />
            </IconButton>
          </TableCell>
        )}
      </TableRow>
      <Dialog open={open} fullWidth maxWidth="lg">
        <DialogTitle>Edit Details</DialogTitle>
        <DialogContent>
          <EditTable
            data={data}
            fatId={fatId}
            handleClose={() => setOpen(false)}
            details={details}
            user={user}
            machineName={machineName}
          />
        </DialogContent>
      </Dialog>
      <Dialog
        open={openVideo}
        fullWidth
        maxWidth="md"
        onClose={() => setOpenVideo(!openVideo)}
      >
        <DialogTitle>Video</DialogTitle>
        <DialogContent>
          <div>
            <TextField
              value={mediaProofTitle}
              style={{ marginBottom: "20px" }}
              onChange={(e) => setMediaProofTitle(e.target.value)}
              placeholder="Enter Video Title"
              fullWidth
              variant="outlined"
            />
            <input onChange={handleMediaChange} type="file" />
          </div>
          <h3>{progress} %</h3>
          <LinearProgress variant="determinate" value={progress} />

          {data.videos &&
            videoFiles.map((item, idx) => (
              <div
                style={{
                  padding: 10,
                  marginTop: "10px",
                  border: "1px solid #764AF1",
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <div style={{ display: "flex", justifyContent: "normal" }}>
                  <VideoCameraBackIcon />
                  <Typography variant="body1" style={{ marginLeft: "20px" }}>
                    {item.mediaTitle}
                  </Typography>
                </div>
                <a href={item.videoUrl} target="_blank">
                  <>
                    <FileDownloadIcon />
                  </>
                </a>
              </div>
            ))}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={submitVideoDetails}
            variant="contained"
            color="primary"
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openAudio}
        onClose={() => setOpenAudio(false)}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>Audio Files</DialogTitle>
        <DialogContent>
          <div>
            <TextField
              value={mediaProofTitle}
              style={{ marginBottom: "20px" }}
              onChange={(e) => setMediaProofTitle(e.target.value)}
              placeholder="Enter Audio Title"
              fullWidth
              variant="outlined"
            />
            <input onChange={handleMediaChange} type="file" />
          </div>
          <h3>{progress} %</h3>
          <LinearProgress variant="determinate" value={progress} />

          {data.audios &&
            data?.audios.map((item, idx) => (
              <div
                style={{
                  padding: 10,
                  marginTop: "10px",
                  border: "1px solid #764AF1",
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <div style={{ display: "flex", justifyContent: "normal" }}>
                  <AudioFileIcon />
                  <Typography variant="body1" style={{ marginLeft: "20px" }}>
                    {item.mediaTitle}
                  </Typography>
                </div>
                <a href={item.audioUrl} target="_blank">
                  <>
                    <FileDownloadIcon />
                  </>
                </a>
              </div>
            ))}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={submitAudioDetails}
            variant="contained"
            color="primary"
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openImage}
        onClose={() => setOpenImage(false)}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>Image Files</DialogTitle>
        <DialogContent>
          <div>
            <TextField
              value={mediaProofTitle}
              style={{ marginBottom: "20px" }}
              onChange={(e) => setMediaProofTitle(e.target.value)}
              placeholder="Enter Image Title"
              fullWidth
              variant="outlined"
            />
            <input onChange={handleMediaChange} type="file" />
          </div>
          <h3>{progress} %</h3>
          <LinearProgress variant="determinate" value={progress} />

          {data.images?.map((item, idx) => (
            <div
              style={{
                padding: 10,
                marginTop: "10px",
                border: "1px solid #764AF1",
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <div style={{ display: "flex", justifyContent: "normal" }}>
                <ImageIcon />
                <Typography variant="body1" style={{ marginLeft: "20px" }}>
                  {item.mediaTitle}
                </Typography>
              </div>
              <a href={item.imageUrl} target="_blank">
                <>
                  <FileDownloadIcon />
                </>
              </a>
            </div>
          ))}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={submitImageDetails}
            variant="contained"
            color="primary"
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openText}
        onClose={() => setOpenText(false)}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>Text Files/Documents</DialogTitle>
        <DialogContent>
          <div>
            <TextField
              value={mediaProofTitle}
              style={{ marginBottom: "20px" }}
              onChange={(e) => setMediaProofTitle(e.target.value)}
              placeholder="Enter Document Title"
              fullWidth
              variant="outlined"
            />
            <input onChange={handleMediaChange} type="file" />
          </div>
          <h3>{progress} %</h3>
          <LinearProgress variant="determinate" value={progress} />

          {data.textFiles &&
            textFiles?.map((item, idx) => (
              <div
                style={{
                  padding: 10,
                  marginTop: "10px",
                  border: "1px solid #764AF1",
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <div style={{ display: "flex", justifyContent: "normal" }}>
                  <ArticleIcon />
                  <Typography variant="body1" style={{ marginLeft: "20px" }}>
                    {item.mediaTitle}
                  </Typography>
                </div>
                <a href={item.textUrl} target="_blank">
                  <>
                    <FileDownloadIcon />
                  </>
                </a>
              </div>
            ))}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={submitTextDetails}
            variant="contained"
            color="primary"
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
export default DataTableItem;
