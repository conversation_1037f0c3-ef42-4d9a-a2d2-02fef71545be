import React from "react";
//import ButtonMoreOptions from '../../components/buttons/ButtonMoreOptions'
import { CommonButtons } from "../../components/buttons/Buttons";
import { useNavigate, useLocation } from "react-router-dom";
import { useMaintenanceInfoSeter } from "../../context/MaintenanceContext";

export default function AlarmCard({ alerts, machineData }) {
  const history = useNavigate();
  const maintenanceInfoSetter = useMaintenanceInfoSeter();
  const location = useLocation();
  const pathName = location.pathname;

  //
  const handleRedirect = (data) => {
    // console.log("notification pathName:", pathName)
    if (pathName == "/maintenance/" + data.mid) {
      maintenanceInfoSetter(data);
    } else {
      maintenanceInfoSetter(data);
      history.replace("/maintenance" + "/" + data.mid);
    }
  };

  return (
    <>
      {
        <div className="singleAlarmContainer hover:shadow-md flex flex-col justify-between">
          <div className=" h-3/4 rounded-lg flex">
            <div className="w-1/4 rounded-lg p-1 flex flex-col justify-center">
              <div className="self-center">
                <i class="ri-notification-4-line text-red-500 text-2xl "></i>
              </div>
            </div>
            <div className="w-3/4  rounded-lg p-1">
              <span className="font-bold ">{alerts.name} : </span>
              <span>{parseFloat(alerts.value).toFixed(2)}</span>
              <div className="flex">
                <span>
                  <i className="ri-timer-2-fill self-center mr-1"></i>
                </span>
                {alerts?.time?.toDate().toString().substring(0, 15)}
              </div>
              <div>
                {machineData?.map((data) =>
                  data.id == alerts.mid ? data.title : null,
                )}
              </div>
            </div>
          </div>
          <div className=" h-1/4 rounded-lg justify-between flex p-1">
            <CommonButtons buttonTitle="Acknowledge" />
            <CommonButtons
              onClick={() => handleRedirect(alerts)}
              buttonTitle="Resolve"
            />
          </div>
        </div>
      }
    </>
  );
}
