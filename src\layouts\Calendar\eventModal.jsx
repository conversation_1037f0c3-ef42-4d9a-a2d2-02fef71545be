import { Download } from "@mui/icons-material";
import {
  <PERSON>,
  <PERSON>ton,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  TextField,
} from "@mui/material";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import axios from "axios";
import React from "react";
import { toast } from "react-toastify";
import { dbConfig } from "../../infrastructure/db/db-config";
import { PictureAsPdf } from "@material-ui/icons";

const TYPE_TITLE = {
  0: "Calibration",
  1: "Machine Breakdown",
  2: "Routine",
  3: "Preventive",
  4: "Gemba",
  5: "Line Clearance",
};

/**
 * A modal that displays a dialog with a close button
 * @param {{
 * open: boolean,
 * setOpen: (open: boolean) => void,
 * selectedModuleTitle: string,
 * selectedModule: "preventiveMaintenance"|"routineMaintenance"|"maintenanceReport"| "changeoverReport"| "trainingReport",
 * eventsCount: number | string,
 * selectedDate: Date |null,
 * selectedEvent: {
 *  _id: string,
 *  title: string,
 *  type: number,
 *  lastdone: string,
 *  date: string,
 * }[],
 * }} props
 * @returns {React.ReactElement}
 */
export function EventModal({
  open,
  setOpen,
  selectedModuleTitle,
  selectedModule,
  eventsCount,
  selectedDate,
  selectedEvent,
}) {
  const [generatingPDF, setGeneratingPDF] = React.useState(false);
  const [selectedEventState, setSelectedEventState] =
    React.useState(selectedEvent);

  React.useEffect(() => {
    setSelectedEventState(selectedEvent);
  }, [selectedEvent]);

  const handleDownloadPDF = async (_id) => {
    if (!_id) {
      toast.warn("Id is Required.");
      return;
    }

    const reportPath =
      selectedModule === "maintenanceReport"
        ? "mainReportStepData"
        : selectedModule === "changeoverReport"
          ? "changeOverReportSteps"
          : "manualReportSteps";

    try {
      setGeneratingPDF(true);
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const res = await axios.get(
        `${dbConfig.url}/${reportPath}/pdf/export?manual_id=${_id}&timezone=${encodeURIComponent(timeZone)}`,
      );

      if (res?.data?.data) {
        console.log("PDF response:", res);
        console.log("timezone:", timeZone);
        window.open(`${dbConfig.url}/${res.data.data}`, "_blank");
      } else {
        toast.error("Failed to Generate PDF.");
      }
    } catch (err) {
      console.error(err);
      toast.error("An error occurred while generating PDF.");
    } finally {
      setGeneratingPDF(false);
    }
  };

  const forReport = [
    "maintenanceReport",
    "changeoverReport",
    "trainingReport",
  ].includes(selectedModule);

  return (
    <Dialog open={open} onClose={() => setOpen(false)}>
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: "40px",
        }}
      >
        <h5>
          {selectedModuleTitle}:&nbsp;&nbsp;
          <span
            style={{
              color:
                selectedModule === "preventiveMaintenance" ||
                selectedModule === "routineMaintenance"
                  ? "red"
                  : "green",
            }}
          >
            {eventsCount}
          </span>
        </h5>
        <p>
          <span
            style={{
              fontWeight: "bold",
            }}
          >
            Date:&nbsp;&nbsp;
          </span>
          {selectedDate?.toLocaleDateString()}
        </p>
      </DialogTitle>
      <DialogContent>
        <TableContainer>
          <Box>
            <TextField
              label="Search"
              variant="outlined"
              size="small"
              sx={{ my: "10px", width: "100%" }}
              onChange={(e) => {
                const searchValue = e.target.value.toLowerCase();
                const filteredEvents = selectedEvent.filter(
                  (event) =>
                    event.title.toLowerCase().includes(searchValue) ||
                    TYPE_TITLE[event.type].toLowerCase().includes(searchValue),
                );
                setSelectedEventState(filteredEvents);
              }}
            />
          </Box>
          <Table sx={{ border: 1 }}>
            <TableHead sx={{ backgroundColor: "#acadac" }}>
              <TableRow>
                <TableCell sx={{ border: 1 }}>
                  <strong>Title</strong>
                </TableCell>
                {selectedModule === "maintenanceReport" && (
                  <TableCell sx={{ border: 1 }}>
                    <strong>Type</strong>
                  </TableCell>
                )}
                {forReport && (
                  <TableCell sx={{ border: 1 }}>
                    <strong>Actions</strong>
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {selectedEventState &&
                selectedEventState.map((data, index) => (
                  <TableRow key={index}>
                    <TableCell sx={{ border: 1 }}>{data.title}</TableCell>
                    {selectedModule === "maintenanceReport" && (
                      <TableCell sx={{ border: 1 }}>
                        {TYPE_TITLE[data.type] ?? data.type}
                      </TableCell>
                    )}
                    {forReport && (
                      <TableCell align="center" sx={{ border: 1 }}>
                        <Tooltip title="Download PDF">
                          <IconButton
                            onClick={() => handleDownloadPDF(data._id)}
                            disabled={generatingPDF}
                          >
                            {generatingPDF ? (
                              <CircularProgress size="20px" />
                            ) : (
                              <PictureAsPdf
                                style={{ fontSize: "20px", color: "red" }}
                              />
                            )}
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions>
        <Button
          color="error"
          variant="contained"
          onClick={() => setOpen(false)}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}
