// NOTE :  Action sheet data is stored in seprate col in firebase. But not seems final way to store.
// We can keep data in more optimised way
// check pdf : Protocol Number: 2738-135-00
// integration note: Des<PERSON> need to update, currently not adding while post request.

// This is adding content in FAT.

import React, { useState, useEffect } from "react";
import { Button, InputLabel, MenuItem, Select, TextField } from "@mui/material";
import { companies, companyId_constant } from "../../../constants/data";
import { db } from "../../../firebase";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { useAuth } from "../../../hooks/AuthProvider";
import { firebaseLooper } from "../../../tools/tool";
import { toastMessageSuccess, toastMessageWarning } from "../../../tools/toast";
import { useStateContext } from "../../../context/ContextProvider";
import { themeColors } from "../../../infrastructure/theme";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import { useCreateMachineCfr } from "../../../hooks/cfr/machineCfrProvider";
import { useContentSetter } from "../../../services3/audits/ContentContext";

const AddDoc = ({ mid, handleClose, type, machineName, id, index }) => {
  const handleContent = useContentSetter();

  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");
  const [user, setUser] = useState([]);
  const { currentUser } = useAuth();
  // const [dataType, setDataType] = useState('')khh
  const [templateType, setTemplateType] = useState();
  const { currentMode } = useStateContext();
  const menuItemTheme = {
    backgroundColor:
      currentMode === "Dark"
        ? themeColors.dark.primary
        : themeColors.light.primary,
  };
  const addauditseriescontentcfr = useCreateMachineCfr();
  const addsatseriescontentcfr = useCreateMachineCfr();

  const handleSubmit = (e) => {
    console.log("type and aid", type, ":", id);
    e.preventDefault();
    let date = new Date();
    const data2 = {
      activity: "content added",
      dateTime: date,

      description: "content is added inside audit",
      machine: mid,
      module: "AUDIT",
      username: currentUser.username,
    };
    const data3 = {
      activity: "content added",
      dateTime: date,

      description: "content is added inside sat",
      machine: mid,
      module: "SAT",
      username: currentUser.username,
    };

    const data = {
      title,
      type: templateType,
      createdAt: date.toLocaleString,
      lastUpdated: date.toLocaleString,
      mid: mid,
      index,
    }; // desc need to update with new one having option of block,italic etc
    data[type === "FAT" || "AUDIT" ? "fid" : "sid"] = id;

    axios
      .post(`${dbConfig.url}/fatdatas`, data)
      .then((res) => {
        //window.location.reload();
        if (type == "SAT") {
          addsatseriescontentcfr(data3);
        } else {
          addauditseriescontentcfr(data2);
        }
        handleContent(id);
        console.log("handle function called");
        handleClose();
        toastMessageSuccess({ message: "Added successfully" });
      })
      .catch((e) => {
        toastMessageWarning({ message: "Something went wrong" });
        console.log("Fat add fail: ", e);
      });

    // db.collection(companies).doc(companyId_constant)
    // .collection(`${type.toLowerCase()}Data`)
    // .add(data).then((docRef) => {

    //     // LoggingFunction(
    //     //     machineName,
    //     //     title,
    //     //     `${user?.fname} ${user?.lname}`,
    //     //     type,
    //     //     `${title} is added to ${type} module`
    //     // )

    //     const obj = {
    //         title: "",
    //         deviation_no: "",
    //         protocol_ref: "",
    //         page_no: "",
    //         item_no: "",
    //         desc: "",
    //         impact: "",
    //         tester: "",
    //         sign_tester: "",
    //         action: "",
    //         eng_approval: "",
    //         sign_eng: "",
    //         result: "",
    //         valid_approval: "",
    //         sign_valid: "",
    //         cust_approval: "",
    //         sign_cust: ""
    //     }

    //     if(templateType == 5) {db.collection(companies).doc(companyId_constant)
    //     .collection(`${type.toLowerCase()}Data`)
    //     .doc(docRef.id).collection('actionSheet').add(obj)}
    //     handleClose()
    //     toastMessageSuccess({ message: "Added successfully" })
    // })
  };

  //  comment by ankit
  // useEffect(() => {
  //     db.collection(companies)
  //         .doc(companyId_constant)
  //         .collection("userData")
  //         .where("email", "==", currentUser.email)
  //         .onSnapshot((snap) => {
  //             const data = firebaseLooper(snap);
  //             setUser(data[0]);
  //         });
  // }, []);

  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Title</InputLabel>
      <TextField
        onChange={(e) => setTitle(e.target.value)}
        onBlur={() => setTitle(title?.trim())}
        value={title}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />

      <InputLabel style={{ marginBottom: "10px" }}>Description</InputLabel>
      <TextField
        onChange={(e) => setDesc(e.target.value)}
        onBlur={() => setDesc(desc?.trim())}
        value={desc}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{ marginBottom: "12px" }}
      />

      {/* <InputLabel style={{ marginBottom: '10px' }}>Type</InputLabel>
            <Select
                onChange={(e) => setDataType(e.target.value)}
                onBlur={() => setDataType(desc?.trim())}
                value={dataType}
                required
                variant='outlined'
                fullWidth
                style={{ marginBottom: '12px' }} >

                <MenuItem value="manual">Manual</MenuItem>
                <MenuItem value="auto">Auto</MenuItem>
            </Select> */}

      <InputLabel style={{ marginBottom: "10px" }}>Template Type</InputLabel>
      <Select
        onChange={(e) => setTemplateType(e.target.value)}
        value={templateType}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      >
        <MenuItem value={0} sx={menuItemTheme}>
          Approval
        </MenuItem>
        <MenuItem value={1} sx={menuItemTheme}>
          Purpose
        </MenuItem>
        <MenuItem value={2} sx={menuItemTheme}>
          Body
        </MenuItem>
        <MenuItem value={5} sx={menuItemTheme}>
          Deviation,Impact and Corrective Action Sheet
        </MenuItem>
        <MenuItem value={6} sx={menuItemTheme}>
          Non Conformity
        </MenuItem>
        <MenuItem value={7} sx={menuItemTheme}>
          Post Approval
        </MenuItem>
      </Select>

      <div className="p-2 mt-2 flex justify-between">
        <Button onClick={handleClose} color="error" variant="contained">
          Cancel
        </Button>
        <Button type="submit" color="primary" variant="contained">
          Submit
        </Button>
      </div>
    </form>
  );
};

export default AddDoc;
