import React, { useState, useMemo } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "../TableTempletes.css";

import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import TextField from "@mui/material/TextField";

import { useStateContext } from "../../../../context/ContextProvider";
import { db, storage } from "../../../../firebase";
import { companies, companyId_constant } from "../../../../constants/data";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../../../components/buttons/Buttons";

import { useStorageTablesFile } from "../../../../utils/useStorageTablesFile";
import { InputLabel, LinearProgress, Link } from "@mui/material";
import { Empty } from "antd";
import { FileDownload, ImageSearchOutlined } from "@mui/icons-material";
import {
  FileImageFilled,
  FilePdfFilled,
  PictureFilled,
} from "@ant-design/icons";
import { DeleteByUrl } from "../../../../utils/StorageOptions";
import { DropzoneArea } from "material-ui-dropzone";

export default function NonConformityCheckedBy({
  rowData,
  type,
  machineName,
  fatDataDocId,
  useAt,
}) {
  const { currentMode, currentColorLight } = useStateContext();
  const [open, setOpen] = useState(false);
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");

  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useMemo(() => {
    // when theme changes then S.No array got reversed. So to stop that This Memonisation is used.
    setSortDirection("");
    //console.log("TableTemplates usememo")
  }, [currentMode]);

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#161c24" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow> */}

            <TableRow sx={{ border: theme.borderDesign }}>
              {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}><span className='pr-1'>S.No</span>
                                {useAt !== 'tableMain' ? <>
                                    {(sortDirection === 'down' || sortDirection === '') ? <span className={theme.arrow} onClick={() => setSortDirection('up')}>&#x21E9; </span>
                                        : <span className={theme.arrow} onClick={() => setSortDirection('down')}>&#x21E7; </span>
                                    } </>
                                    : null
                                }
                            </TableCell> */}
              {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Tag No</TableCell> */}
              {/* <TableCell sx={{ border: theme.borderDesign }} colSpan={3} align='center'>Requirements</TableCell> */}
              {/* <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}> Signal on/off </TableCell>
                            <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Result</TableCell> */}
              {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>Image</TableCell> } */}
              {/* </TableRow> */}

              {/* <TableRow sx={{ border: theme.borderDesign }}> */}

              <TableCell sx={{ border: theme.borderDesign }}>
                {" "}
                COMPANY'S NAME{" "}
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}> NAME</TableCell>
              <TableCell sx={{ border: theme.borderDesign }} width="15%">
                {" "}
                SIGNATURE{" "}
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}> DATE </TableCell>
              {/* <TableCell sx={{ border: theme.borderDesign }}>Model/ Serial no</TableCell>
                            <TableCell sx={{ border: theme.borderDesign }}>Range</TableCell>   */}
              {/* <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>   */}

              {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} >Image</TableCell> }           */}

              {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  sx={{
                    border: theme.borderDesign,
                    background: currentMode === "Dark" ? "#212b36" : "",
                  }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    <image src={data[4]} alt={data[2]}></image>
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[3]}
                  </TableCell>
                  {/* <TableCell sx={{ border: theme.borderDesign }} >{data[4]}</TableCell>
                                <TableCell sx={{ border: theme.borderDesign }} >{data[5]}</TableCell> */}
                  {/* <TableCell sx={{ border: theme.borderDesign }} >{data[6]}</TableCell>
                                <TableCell sx={{ border: theme.borderDesign }} >{data[7]}</TableCell> */}
                  {/* { useAt !== "tableMain" && <TableCell sx={{ border: theme.borderDesign }} align='center' >{data[4] === null ? "Add One" :
                                    <>
                                        {data[4]?.includes('.pdf') ?
                                            <FilePdfFilled className='text-red-700' onContextMenu={() => window.location.href = data[4]} title="Press right click to open file" />
                                            :
                                            <PictureFilled onContextMenu={() => window.location.href = data[4]} title="Press right click to open file" />
                                        }
                                    </>
                                }
                                </TableCell>
                                 } */}
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
          <EditTableRow
            rowDataSelected={rowDataSelected}
            tableType={"NonConfirmityCheckedBy"}
            type={type}
            fatDataDocId={fatDataDocId}
            machineName={machineName}
            handleClose={handleClose}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}

//////////////////

function EditTableRow({
  rowDataSelected,
  tableType,
  type,
  machineName,
  fatDataDocId,
  handleClose,
}) {
  const { currentMode } = useStateContext();

  const [companyName, setCompanyName] = useState(rowDataSelected[0]);
  // const [tagNo, setTagNo] = useState(rowDataSelected[1]); //
  const [name, setName] = useState(rowDataSelected[1]);
  // const [make, setMake] = useState(rowDataSelected[3]);
  // const [model, setModel] = useState(rowDataSelected[4]);
  const [signature, setSignature] = useState(rowDataSelected[2]); // type
  const [date, setDate] = useState(rowDataSelected[3]);
  //const [testReportNo, setTestReportNo] = useState(rowDataSelected[6]);
  // const [chNumber, setChNumber] = useState(rowDataSelected[3]);
  // const [signal, setSignal] = useState(rowDataSelected[4]);
  // const [result, setResult] = useState(rowDataSelected[5]);
  const [urlData, setUrlData] = useState(rowDataSelected[4]);

  const [fileType, setFileType] = useState("");
  const [file, setFile] = useState(null);
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];

  //
  const handleChangeImage = (file) => {
    let selectedFile = file[0];
    //console.log(file[0]) //(selectedFile?.size/1024))

    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        // delete last uploaded file via url if image changes
        if (url) {
          DeleteByUrl(url);
        }
        //
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };
  // const { currentColor, currentMode } = useStateContext();
  const { progress, url } = useStorageTablesFile(file);

  //
  const handleUpdateRow = () => {
    let data = {
      company_name: companyName,
      name: name,
      signature: signature,
      date: date,
      url: url === null ? urlData : url,
    };
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(type)
    //     .doc(fatDataDocId)
    //     .collection("table" + tableType)
    //     .doc(rowDataSelected[5]) // rowDataSelected[9] = id of the document
    //     .update(data)
    //     .then(() => {
    //         toastMessageSuccess({ message: "Row updated Successfully" });
    //         handleClose();
    //     }).catch((e) => {
    //         toastMessageWarning({ message: "Error ", e });
    //         console.log("NonConformityCheckedBy", e)
    //     })
  };

  const handleCancel = () => {
    if (url) {
      DeleteByUrl(url);
    }
    handleClose();
  };

  const handleDeleteDropZone = (url) => {
    DeleteByUrl(url); // to delete the file from storage
    setFile(null); // to remove the preview
  };

  return (
    <>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          <div className="flex justify-between py-2">
            <div className="w-1/12">
              <TextField
                label="Company's Name"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
              />
            </div>

            {/* <div className='w-1/12'>
                            <TextField
                                label="Tag No"
                                id="outlined-size-small"
                                defaultValue="Na"
                                size="small"
                                value={tagNo}
                                onChange={(e) => setTagNo(e.target.value)}
                            />
                        </div> */}

            <div className="w-2/12">
              <TextField
                label="Name"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>

            {/* <div className='w-1/12'>
                            <TextField
                                label="MOD no"
                                id="outlined-size-small"
                                defaultValue="Na"
                                size="small"
                                value={modNumber}
                                onChange={(e) => setModNumber(e.target.value)}
                            />
                        </div>

                        <div className='w-1/12'>
                            <TextField
                                label="CH no"
                                id="outlined-size-small"
                                defaultValue="Na"
                                size="small"
                                value={chNumber}
                                onChange={(e) => setChNumber(e.target.value)}
                            />
                        </div>

                        <div className='w-1/12'>
                            <TextField
                                label="Signal"
                                id="outlined-size-small"
                                defaultValue="Na"
                                size="small"
                                value={signal}
                                onChange={(e) => setSignal(e.target.value)}
                            />
                        </div> */}

            <div className="4/12">
              <TextField
                label="Signatures"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={signature}
                onChange={(e) => setSignature(e.target.value)}
              />
            </div>

            <div className="w-1/12">
              <TextField
                label="Date"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={date}
                onChange={(e) => setDate(e.target.value)}
              />
            </div>
          </div>
          {/* image section */}
          <div className="flex justify-center">
            <div
              className={
                currentMode === "Dark"
                  ? "w-2/6 bg-gray-700 rounded-sm p-1 shadow-md"
                  : "w-2/6  bg-gray-100 rounded-sm p-1 shadow-md"
              }
            >
              <InputLabel style={{ marginBottom: "10px" }}>Media</InputLabel>
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) => handleChangeImage(loadedFiles)}
                dropzoneText="Drag and Drop / Click to ADD Media. MaxFileSize : 5Mb"
                showAlerts={false}
                filesLimit={1}
                maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
                onDelete={() => handleDeleteDropZone(url)}
                dropzoneClass={
                  currentMode === "Dark"
                    ? "dropZoneClassDark"
                    : "dropZoneClassLight"
                }
              />

              <div className="p-2 block">
                <LinearProgress
                  style={{ marginBottom: "20px" }}
                  variant="determinate"
                  value={progress}
                />
                <div className="text-2xl text-gray-400 flex justify-end ">
                  <p> {progress} % Uploaded</p>
                </div>
                {!url ? ( // url when we upload
                  <>
                    {url ? (
                      <img alt="" src={url} />
                    ) : (
                      <>
                        <a target="_blank" href={urlData} rel="noreferrer">
                          {" "}
                          <img alt="" src={urlData} />{" "}
                        </a>

                        {urlData?.includes(".pdf") && (
                          <a
                            target="_blank"
                            href={urlData}
                            data-title="download"
                            rel="noreferrer"
                          >
                            <FileDownload className=" animate-bounce bg-slate-400 shadow-md rounded-sm hover:bg-slate-500" />
                            Pdf
                          </a>
                        )}
                      </>
                    )}
                  </>
                ) : (
                  <Empty
                    description={<span>Please Wait for Preview ...</span>}
                  />
                )}
              </div>
            </div>
          </div>
          {/* image section close */}
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <ButtonBasic buttonTitle="Update" onClick={handleUpdateRow} />
        <ButtonBasicCancel
          buttonTitle="Cancel &#x2716;"
          onClick={handleCancel}
        />
      </DialogActions>
    </>
  );
}
