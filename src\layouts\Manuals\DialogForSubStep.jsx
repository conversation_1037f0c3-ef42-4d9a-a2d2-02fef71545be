import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
} from "@mui/material";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import {
  CarouselProvider,
  Slider,
  Slide,
  ButtonBack,
  ButtonNext,
} from "pure-react-carousel";
import SubStepItem from "./SubStepItem";
import { useStateContext } from "../../context/ContextProvider";
import { Button } from "@mui/material";
import { themeColors } from "../../infrastructure/theme";

const DialogForSubStep = ({
  step,
  parent,
  open,
  close,
  subSteps,
  sensorList,
  onSubStepDelete = (id) => {},
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isMediaPreviewOpen, setIsMediaPreviewOpen] = useState(null);
  const { currentMode } = useStateContext();

  const setNaturalSlideHeight = () => {
    if (isEditing) {
      return isMediaPreviewOpen?.type !== "text" &&
        isMediaPreviewOpen?.type !== "audio"
        ? 120
        : isMediaPreviewOpen?.type === "audio"
          ? 90
          : 60;
    }
    return 40;
  };

  return (
    <div>
      <Dialog open={open} maxWidth="lg" fullWidth onClose={close}>
        <DialogTitle
          style={
            currentMode === "Dark"
              ? {
                  backgroundColor: themeColors.dark.primary,
                  color: "white",
                  borderRadius: "5px 5px 0 0",
                }
              : { borderRadius: "5px 5px 0 0" }
          }
        >
          Sub Steps
        </DialogTitle>
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { backgroundColor: themeColors.dark.primary }
              : {}
          }
        >
          {subSteps?.length === 0 && <div>No Substeps Added</div>}

          <CarouselProvider
            naturalSlideWidth={100}
            naturalSlideHeight={setNaturalSlideHeight()}
            totalSlides={subSteps?.length}
            dragEnabled={false}
            touchEnabled={false}
          >
            {!isEditing && (
              <div
                style={{
                  position: "absolute",
                  marginTop: "20px",
                  top: "200px",
                  width: "100%",
                  display: "flex",
                  justifyContent: "space-between",
                  zIndex: "1000",
                }}
              >
                <ButtonBack
                  aria-label="Previous slide"
                  className="w-12 h-12 bg-gray-500 rounded-3xl hover:bg-gray-400 cursor-pointer flex items-center justify-center"
                  style={{ marginLeft: "20px" }}
                >
                  <NavigateBeforeIcon className="text-white" />
                </ButtonBack>
                <ButtonNext
                  aria-label="Next slide"
                  className="w-12 h-12 bg-gray-500 rounded-3xl hover:bg-gray-400 cursor-pointer flex items-center justify-center"
                  style={{ marginRight: "20px" }}
                >
                  <NavigateNextIcon className="text-white" />
                </ButtonNext>
              </div>
            )}

            <Slider
              className="outline-0"
              style={{
                outline: "none",
                zIndex: "100",
                marginTop: "24px",
              }}
            >
              {subSteps
                ?.sort((a, b) => a.index - b.index)
                .map((data, index) => (
                  <Slide index={index} className="outline-0" key={index}>
                    <SubStepItem
                      parent={parent}
                      closeItem={close}
                      data={data}
                      step={step}
                      sensorList={sensorList}
                      setIsEditing={setIsEditing}
                      setIsMediaPreviewOpen={setIsMediaPreviewOpen}
                      onSubStepDelete={onSubStepDelete}
                    />
                  </Slide>
                ))}
            </Slider>
          </CarouselProvider>
        </DialogContent>

        <DialogActions
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: "36px",
            backgroundColor:
              currentMode === "Dark" ? themeColors.dark.primary : "#fff",
            color: currentMode === "Dark" ? "white" : "black",
          }}
        >
          <Button
            width="30%"
            color="error"
            variant="contained"
            style={{ padding: "6px 128px" }}
            onClick={close}
          >
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default DialogForSubStep;
