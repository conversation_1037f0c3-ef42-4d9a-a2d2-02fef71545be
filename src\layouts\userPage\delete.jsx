import React from "react";
// import { useStateContext } from '../../context/ContextProvider'
import { Box, Button, Typography } from "@mui/material";

import PropTypes from "prop-types";
// import STRINGS from '../../constants';

const Delete = ({ onDelete, onClose }) => {
  //   const { currentMode } = useStateContext()

  return (
    <div style={{ padding: "20px" }}>
      <div className="w-full  max-w-lg p-5 relative mx-auto my-auto rounded-xl shadow-lg">
        <div className="">
          <div className="text-center p-5 flex-auto justify-center">
            <br />
            <Typography sx={{ fontWeight: "500" }} variant="h5">
              Are you sure
            </Typography>
            <Typography variant="body1">confirm delete</Typography>
            <p className="text-sm px-8"></p>
          </div>
          <Box
            style={{
              display: "flex",
              flexDirection: "row-reverse",
              gap: "1rem",
            }}
          >
            <Button
              onClick={() => onDelete()}
              variant="contained"
              color="error"
            >
              delete
            </Button>
            <Button variant="outlined" color="error" onClick={() => onClose()}>
              cancel
            </Button>
          </Box>
        </div>
      </div>
    </div>
  );
};

Delete.propTypes = {
  onDelete: PropTypes.func.isRequired, // Callback function to handle delete action
  onClose: PropTypes.func.isRequired, // Callback function to handle cancel action
};

export default Delete;
