import React from "react";
import {
  <PERSON>,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Collapse,
  Box,
  Pagination,
  Badge,
  Tooltip,
} from "@mui/material";
import {
  Audiotrack, // Icon for audio
  Videocam, // Icon for video
  Image, // Icon for image
  TextFields, // Icon for text
  Info, // Icon for info
  CameraAlt, // Icon for camera
  Assignment, // Icon for normal
  Warning, // Icon for critical
} from "@mui/icons-material";
import SOPSubStepsTable from "./SOPSubStepsTable";
import { Typography } from "@mui/material";

// Step validation: A step is primarily defined by its title.
const isStepValid = (step) => !!step.title;

// Validate if a step has all required data (title, desc, url)
const hasAllStepData = (step) => {
  return !!step.title && !!step.desc && !!step.url;
};

// Icon mappings for format
export const formatIcons = {
  audio: <Audiotrack sx={{ color: "#2B6CB0" }} />,
  video: <Videocam sx={{ color: "#2B6CB0" }} />,
  image: <Image sx={{ color: "#2B6CB0" }} />,
  text: <TextFields sx={{ color: "#2B6CB0" }} />,
};

// Icon mappings for type
export const typeIcons = {
  info: <Info sx={{ color: "#2B6CB0" }} />,
  camera: <CameraAlt sx={{ color: "#2B6CB0" }} />,
  normal: <Assignment sx={{ color: "#2B6CB0" }} />,
  critical: <Warning sx={{ color: "#D32F2F" }} />, // Red for critical to highlight importance
};

const SOPStepsTable = ({
  manuals = [],
  page = 1,
  perPage = 5,
  expandedManual,
  onExpandManual,
  expandedStep,
  onExpandStep,
  subStepPage,
  onSubStepPageChange,
  stepPageCount,
  onStepPageChange,
  catName,
  localObjectUrlMap,
}) => {
  const paged = (arr, page, perPage) =>
    arr.slice((page - 1) * perPage, page * perPage);

  // Define alternating colors for manuals
  const getRowBackgroundColor = (index) => {
    return index % 2 === 0 ? "#E8F0FE" : "#F5F7FA"; // Soft blue for even, light gray for odd
  };

  return (
    <>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              Index
            </TableCell>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              Manual Title
            </TableCell>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              Manual Description
            </TableCell>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              Created At
            </TableCell>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              Last Updated
            </TableCell>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              Machine ID (mid)
            </TableCell>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              Steps
            </TableCell>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            ></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {manuals.map((manual, idx) => (
            <React.Fragment key={manual._id || idx}>
              <TableRow
                sx={{
                  "&:last-child td, &:last-child th": { border: 0 },
                  borderBottom: "1px solid #e0e0e0",
                  "&:hover": { bgcolor: "#f5f5f5" },
                  cursor: "pointer",
                }}
              >
                <TableCell>{idx + 1}</TableCell>
                <TableCell>{manual.title}</TableCell>
                <TableCell>{manual.desc}</TableCell>
                <TableCell>
                  {manual.createdAt
                    ? new Date(manual.createdAt).toLocaleString()
                    : ""}
                </TableCell>
                <TableCell>
                  {manual.lastUpdated
                    ? new Date(manual.lastUpdated).toLocaleString()
                    : ""}
                </TableCell>
                <TableCell>{manual.mid}</TableCell>
                <TableCell sx={{ textAlign: "center" }}>
                  {manual.steps?.length > 0 ? (
                    <Badge
                      badgeContent={manual.steps.length}
                      sx={{
                        "& .MuiBadge-badge": {
                          backgroundColor: "#2B6CB0",
                          color: "white",
                          fontSize: "0.75rem",
                          minWidth: "24px",
                          height: "24px",
                          borderRadius: "50%",
                          cursor: "pointer",
                          display: "flex",
                        },
                      }}
                      onClick={() => onExpandManual(idx)}
                    />
                  ) : (
                    0
                  )}
                </TableCell>
                <TableCell></TableCell>
              </TableRow>
              <TableRow>
                <TableCell
                  colSpan={8}
                  sx={{
                    p: 0,
                    border: 0,
                    backgroundColor: "#DDF6D2", // Soft green for steps section
                  }}
                >
                  <Collapse
                    in={expandedManual === idx}
                    timeout="auto"
                    unmountOnExit
                  >
                    <Box sx={{ p: 2 }}>
                      <Typography
                        variant="h6"
                        sx={{ mb: 2, fontWeight: "bold", color: "#2B6CB0" }}
                      >
                        Steps
                      </Typography>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell
                              sx={{
                                fontWeight: "bold",
                                backgroundColor: "rgb(224, 224, 224)",
                              }}
                            >
                              Step Title
                            </TableCell>
                            <TableCell
                              sx={{
                                fontWeight: "bold",
                                backgroundColor: "rgb(224, 224, 224)",
                              }}
                            >
                              Step Description
                            </TableCell>
                            <TableCell
                              sx={{
                                fontWeight: "bold",
                                backgroundColor: "rgb(224, 224, 224)",
                              }}
                            >
                              URL
                            </TableCell>
                            <TableCell
                              sx={{
                                fontWeight: "bold",
                                backgroundColor: "rgb(224, 224, 224)",
                              }}
                            >
                              Format
                            </TableCell>
                            <TableCell
                              sx={{
                                fontWeight: "bold",
                                backgroundColor: "rgb(224, 224, 224)",
                              }}
                            >
                              Type
                            </TableCell>
                            <TableCell
                              sx={{
                                fontWeight: "bold",
                                backgroundColor: "rgb(224, 224, 224)",
                              }}
                            >
                              SubSteps
                            </TableCell>
                            <TableCell
                              sx={{
                                fontWeight: "bold",
                                backgroundColor: "rgb(224, 224, 224)",
                              }}
                            ></TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {(manual.steps || []).map((step, stepIdx) => (
                            <React.Fragment key={step._id || stepIdx}>
                              <TableRow
                                sx={{
                                  "&:last-child td, &:last-child th": {
                                    border: 0,
                                  },
                                  borderBottom: "1px solid #e0e0e0",
                                  "&:hover": { bgcolor: "#f5f5f5" },
                                  cursor: "pointer",
                                  border: hasAllStepData(step)
                                    ? "1px solid #E6F4EA"
                                    : "1px solid #F87171", // Red border if invalid
                                }}
                              >
                                <TableCell>{step.title}</TableCell>
                                <TableCell>{step.desc}</TableCell>
                                <TableCell>{step.url}</TableCell>
                                <TableCell>
                                  {step.format ? (
                                    <Tooltip
                                      title={
                                        step.format.charAt(0).toUpperCase() +
                                        step.format.slice(1)
                                      }
                                    >
                                      {formatIcons[step.format] || step.format}
                                    </Tooltip>
                                  ) : (
                                    "-"
                                  )}
                                </TableCell>
                                <TableCell>
                                  {step.type ? (
                                    <Tooltip
                                      title={
                                        step.type.charAt(0).toUpperCase() +
                                        step.type.slice(1)
                                      }
                                    >
                                      {typeIcons[step.type] || step.type}
                                    </Tooltip>
                                  ) : (
                                    "-"
                                  )}
                                </TableCell>
                                <TableCell sx={{ textAlign: "center" }}>
                                  {step.subSteps?.length > 0 ? (
                                    <Badge
                                      badgeContent={step.subSteps.length}
                                      sx={{
                                        "& .MuiBadge-badge": {
                                          backgroundColor: "#2B6CB0",
                                          color: "white",
                                          fontSize: "0.75rem",
                                          minWidth: "24px",
                                          height: "24px",
                                          borderRadius: "50%",
                                          cursor: "pointer",
                                        },
                                      }}
                                      onClick={() => onExpandStep(idx, stepIdx)}
                                    />
                                  ) : (
                                    0
                                  )}
                                </TableCell>
                                <TableCell></TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell
                                  colSpan={8}
                                  sx={{
                                    p: 0,
                                    m: 1,
                                    border: 0,
                                    backgroundColor: "#ECFAE5",
                                  }}
                                >
                                  <Collapse
                                    in={expandedStep[idx] === stepIdx}
                                    timeout="auto"
                                    unmountOnExit
                                  >
                                    <Box sx={{ p: 2 }}>
                                      <Typography
                                        variant="h6"
                                        sx={{
                                          mb: 2,
                                          fontWeight: "bold",
                                          color: "#2B6CB0",
                                        }}
                                      >
                                        Sub-Steps
                                      </Typography>
                                      <SOPSubStepsTable
                                        steps={step.subSteps || []}
                                      />
                                    </Box>
                                  </Collapse>
                                </TableCell>
                              </TableRow>
                            </React.Fragment>
                          ))}
                        </TableBody>
                      </Table>
                    </Box>
                  </Collapse>
                </TableCell>
              </TableRow>
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
      {manuals.length > perPage && (
        <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
          <Pagination
            count={stepPageCount}
            page={page}
            onChange={(_, value) => onStepPageChange(value)}
            size="small"
            sx={{ "& .MuiPaginationItem-root": { color: "#2B6CB0" } }}
          />
        </Box>
      )}
    </>
  );
};

export default SOPStepsTable;
