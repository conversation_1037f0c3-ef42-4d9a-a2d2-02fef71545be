import React, { useState, useEffect } from "react";
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import {
  companies,
  companyId_constant,
  liveData,
} from "../../../constants/data";
import { db } from "../../../firebase";
import { firebaseLooper } from "../../../tools/tool";
import { useStorage } from "../../../hooks/useStorage";
import { toastMessage, toastMessageSuccess } from "../../../tools/toast";
import { useAuth } from "../../../hooks/AuthProvider";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";

const EditLiveData = ({ mid, handleClose, data, machineName }) => {
  const [title, setTitle] = useState(data.title);
  const [desc, setDesc] = useState(data.desc);
  const [type, setType] = useState(data.type);
  const types = ["image/png", "image/jpeg", "image/jpg"];
  const [file, setFile] = useState(null);
  const [user, setUser] = useState([]);
  const { currentUser } = useAuth();
  const [color, setColor] = useState(data.color);
  const [textColor, setTextColor] = useState(data.textColor);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (url === null) {
      const dataSet = {
        title,
        desc,
        color,
        textColor,
        status: "Active",
        mid,
        type,
      };
      // db.collection(companies)
      //   .doc(companyId_constant)
      //   .collection(liveData)
      //   .doc(data.id)
      //   .update(dataSet)
      //   .then(() => {
      //     LoggingFunction(
      //       machineName,
      //       data.title,
      //       `${user?.fname} ${user?.lname}`,
      //       "Live data",
      //       `${data.title} is updated`
      //     )
      //     handleClose();
      //   });
    } else {
      const dataSet = { title, desc, url: url, status: "Active", mid, type };
      // db.collection(companies)
      //   .doc(companyId_constant)
      //   .collection(liveData)
      //   .doc(data.id)
      //   .update(dataSet)
      //   .then(() => {
      //     toastMessageSuccess({
      //       message: 'Live Data Update Successfull',
      //     });
      //     LoggingFunction(
      //       machineName,
      //       data.title,
      //       `${user?.fname} ${user?.lname}`,
      //       "Live data",
      //       `${data.title} is updated`
      //     )
      //     handleClose();
      //   });
    }
  };
  const { progress, url } = useStorage(file);

  const handleChange = (e) => {
    let selectedFile = e.target.files[0];

    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG)",
        });
      }
    }
  };
  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection("userData")
    //   .where("email", "==", currentUser.email)
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     setUser(data[0]);
    //   });
  }, []);
  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Title</InputLabel>
      <TextField
        onChange={(e) => setTitle(e.target.value)}
        onBlur={() => setTitle(title?.trim())}
        value={title}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Description</InputLabel>
      <TextField
        onChange={(e) => setDesc(e.target.value)}
        onBlur={() => setDesc(desc?.trim())}
        value={desc}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{ marginBottom: "12px" }}
      />

      <InputLabel style={{ marginBottom: "10px" }}>Select Type</InputLabel>
      <FormControl
        style={{ marginBottom: "10px" }}
        required
        variant="outlined"
        fullWidth
      >
        <Select required value={type} onChange={(e) => setType(e.target.value)}>
          <MenuItem value={1}>Service</MenuItem>
          <MenuItem value={0}>Process</MenuItem>
        </Select>
      </FormControl>
      <div
        style={{
          marginBottom: "20px",
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <div>
          <InputLabel style={{ marginBottom: "10px" }}>
            Background Color
          </InputLabel>
          <input
            value={color}
            required
            type="color"
            onChange={(e) => setColor(e.target.value)}
          />
          <div>{color}</div>
        </div>
        <div>
          <InputLabel style={{ marginBottom: "10px" }}>Text Color</InputLabel>
          <input
            value={textColor}
            required
            type="color"
            onChange={(e) => setTextColor(e.target.value)}
          />
          <div>{textColor}</div>
        </div>
      </div>
      <InputLabel style={{ marginBottom: "10px" }}>Image</InputLabel>
      <input type="file" onChange={handleChange} />
      <div className="text-2xl text-gray-700 flex justify-end">
        <p> {progress} % Uploaded</p>
      </div>
      {url ? (
        <img alt="" src={url} />
      ) : (
        // <Empty description={<span>Please Wait for Preview ...</span>} />
        <div className="p-2">
          <img src={data.url} alt="dataImage" />
        </div>
      )}

      <div className="p-2 mt-2 flex justify-between">
        <Button onClick={handleClose} variant="outlined">
          Cancel
        </Button>
        <Button type="submit" variant="outlined">
          Submit
        </Button>
      </div>
    </form>
  );
};

export default EditLiveData;
