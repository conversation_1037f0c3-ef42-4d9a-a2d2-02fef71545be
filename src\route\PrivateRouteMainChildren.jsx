import React from "react";
import { Routes, Route } from "react-router-dom"; // Use Routes for react-router-dom v6+
import DashboardPage from "../pages/dashboardPage";
import RegistrationPortal from "../layouts/registration/Registration";
import AddUser from "../pages/addUser";
import TestingPage from "../pages/testing";
import Whiteboard from "../components/whiteboard/Whiteboard";
import CompanyIdPage from "../pages/companyIdPage";
import ForgotPasswordPage from "../pages/forgotPasswordPage";
import FATApproval from "../layouts/FATApproval/FATApproval";
import AdminLoginPage from "../pages/adminLoginPage";
import AdminForgotPasswordPage from "../pages/adminForgotPasswordPage";
import LoginMongoScreen from "../layouts/auth-mongo/login-mongo.screen";
import PrintReportsPage from "../pages/PrintReports";
import PrintFatSingle from "../layouts/machineData/printForMachineData/PrintFatSingle";
import PrintSatSingle from "../layouts/machineData/printForMachineData/PrintSatSingle";
import FlowChartType1 from "../components/Flow-Chart/Type1/index";
import ExistingFlowChart from "../components/Flow-Chart/Existing-Node/type-1";
import NewNode from "../components/New-Node/index";
import CreateNewNode from "../components/New-Node/create-node";
import AddUsers from "../layouts/FATApproval/AddUsers";
import AlarmPage from "../pages/AlarmPage";
import HashTag from "../pages/hashTag";
import alarmManagerPage from "../pages/alarmManagerPage";
import Tasks from "../layouts/Tasks/Tasks";
import LiveDataAnnotation from "../pages/LiveDataAnnotation";
import LiveDataProject from "../pages/LiveDataProject";
import BatchPage from "../pages/batchPage";
import CmsPage from "../pages/cmsPage";
import ModelPage from "../components/LiveData/ModelPage";
import ModelView from "../components/LiveData/ModelView";
import AlarmNewPage from "../pages/AlarmNewPage";
import MaintenancePageCalibration from "../pages/MaintenancePageCalibration";
import ChangeOver from "../pages/ChangeOver";
import TimelineComponent from "../layouts/machines/TimelineView";
import ARview from "../components/arview/arview";
import Arviews from "../components/arview/arview-list";
import VoicePage from "../pages/voice";
import MachineAnalysisPage from "../pages/MachineAnalysis";
import Gemba from "../layouts/machineData/gemba";
import LineClearance from "../layouts/machineData/lineClearance";
import DPRPage from "../pages/DPR";
import ENVPage from "../pages/env";
import Account from "../pages/account";
import CalendarPage from "../pages/CalendarPage"; // Fix casing to match the actual file name
import FileManagerPage from "../pages/fileManagerPage";
import Machines from "../pages/machines"; // Fix casing to match the actual file name
import MaintenancePage from "../pages/MaintenancePage"; // Fix casing to match the actual file name
import FATDocumentationPage from "../pages/FATDocumentationPage";
import SATDocumentationPage from "../pages/SATDocumentationPage";
import MachineDataPage from "../pages/machineDataPage"; // Fix casing to match the actual file name
import FatReportPage from "../pages/FatReportPage";
import PreviewReportsPage from "../pages/PreviewReports"; // Fix casing to match the actual file name
// import AddDetailsDocumentation from "../pages/addDetailsDocumentation"; // Fix casing to match the actual file name
import TrainingPage from "../pages/TrainingPage";
// import MaintenanceReportData from "../pages/maintenanceReportData"; // Fix casing to match the actual file name
// import TrainingReportData from "../pages/trainingReportData"; // Fix casing to match the actual file name
import AddMachine from "../pages/addMachine"; // Fix casing to match the actual file name
import User from "../pages/user"; // Fix casing to match the actual file name
import VideoPage from "../pages/VideoPage";
import CFRPage from "../pages/cfrPage"; // Fix casing to match the actual file name
import SettingsPage from "../pages/settingsPage"; // Fix casing to match the actual file name
import UserManualPage from "../pages/userManualPage"; // Fix casing to match the actual file name
import IssuesPage from "../pages/issuesPage";
import VideoCallPage from "../pages/videoCallPage";
import Layout from "../Layout";
import UtilsPage from "../pages/utilsPage";
import CfrPage from "../pages/cfr_page";
import JitsiMeeting from "../components/Video/JitsiMeeting";

export default function RouteMain() {
  console.log("RouteMain is rendering.");
  return (
    <Routes>
      <Route path="/" element={<DashboardPage />} />
      <Route path="/user-invitation/:id" element={<RegistrationPortal />} />
      <Route
        path="/add-user/b59083f7-5871-4ab5-996b-020090486c53"
        element={<AddUser />}
      />
      <Route path="/testing" element={<TestingPage />} />
      <Route path="/whiteboard" element={<Whiteboard />} />
      <Route path="/company-id" element={<CompanyIdPage />} />
      <Route path="/forgot-password" element={<ForgotPasswordPage />} />
      <Route
        path="/fat-approval/:companyId/:fid/:approvalId/:eid"
        element={<FATApproval />}
      />
      <Route path="/admin-login" element={<AdminLoginPage />} />
      <Route
        path="/admin-forgot-password"
        element={<AdminForgotPasswordPage />}
      />
      <Route path="/login-mongo" element={<LoginMongoScreen />} />
      <Route
        path="/fat-reports-print/:reportId"
        element={<PrintReportsPage />}
      />
      <Route path="/:mid/printFatSingle/:docId" element={<PrintFatSingle />} />
      <Route path="/:mid/printSatSingle/:docId" element={<PrintSatSingle />} />
      {/* <Route path="/flow-chart" element={<FlowChartType1 />} /> */}
      {/* <Route path="/edit" element={<ExistingFlowChart />} /> */}
      {/* <Route path="/new-node" element={<NewNode />} /> */}
      {/* <Route path="/create-node" element={<CreateNewNode />} /> */}
      {/* <Route path="/:fid/:fatId/add-users-fat" element={<AddUsers />} /> */}
      <Route path="/account" exact element={<Account />} />
      <Route exact path="/calendar" element={<CalendarPage />} />
      <Route path="/file-manager" exact element={<FileManagerPage />} />
      <Route
        exact
        path="/file-manager/folder/:folderId"
        element={<FileManagerPage />}
      />
      <Route exact path="/machines" element={<Machines />} />
      <Route exact path="/maintenance/:mid" element={<MaintenancePage />} />
      <Route exact path="/forecast/:mid" element={<MachineAnalysisPage />} />
      <Route exact path="/OEE/:mid" element={<TimelineComponent />} />
      <Route exact path="/FAT/:mid" element={<FATDocumentationPage />} />
      <Route exact path="/SAT/:mid" element={<SATDocumentationPage />} />
      <Route exact path="/liveData/:mid" element={<MachineDataPage />} />
      <Route exact path="/reports" element={<FatReportPage />} />
      <Route
        exact
        path="/fat-reports/:reportId"
        element={<PreviewReportsPage />}
      />
      {/* <Route exact path="/add-details-doc" element={<AddDetailsDocumentation />} /> */}
      {/* <Route exact path="/:mid/docDetailsFat/:docId" element={<DocumentationPage_FAT />} /> */}
      {/* <Route exact path="/:mid/docDetailsSat/:docId" element={<DocumentationPage_SAT />} /> */}
      <Route exact path="/training/:mid" element={<TrainingPage />} />
      {/* <Route exact path="/maintenanceReport/:mid" element={<MaintenanceReportData />} /> */}
      {/* <Route exact path="/trainingReport/:mid" element={<TrainingReportData />} /> */}
      <Route exact path="/add-machine" element={<AddMachine />} />
      <Route exact path="/users" element={<User />} />
      <Route exact path="/add-user" element={<AddUser />} />
      <Route exact path="/video" element={<VideoPage />} />
      <Route exact path="/video-jitsi" element={<JitsiMeeting />} />
      <Route exact path="/issues" element={<Layout />} />
      <Route exact path="/report" element={<CFRPage />} />
      <Route exact path="/settings" element={<SettingsPage />} />
      <Route exact path="/user-manual" element={<UserManualPage />} />
      <Route exact path="/videocall" element={<VideoCallPage />} />
      <Route exact path="/alarms" element={<AlarmPage />} />
      <Route exact path="/hashtag" element={<HashTag />} />
      <Route exact path="/alarm-manager" element={<alarmManagerPage />} />
      <Route exact path="/alarmsnew/:mid" element={<AlarmNewPage />} />
      <Route exact path="/tasks" element={<Tasks />} />
      <Route exact path="/annotation/:mid" element={<LiveDataProject />} />
      <Route exact path="/changeOver/:mid" element={<ChangeOver />} />
      <Route
        exact
        path="/calibration/:mid"
        element={<MaintenancePageCalibration />}
      />
      <Route exact path="/annotations/:id" element={<LiveDataAnnotation />} />
      <Route exact path="/3dmodel" element={<ModelPage />} />
      <Route exact path="/modelview" element={<ModelView />} />
      <Route exact path="/smartScan" element={<BatchPage />} />
      <Route exact path="/cms" element={<CmsPage />} />
      <Route exact path="/arview/:canvasId?" element={<ARview />} />
      <Route exact path="/arviews" element={<Arviews />} />
      <Route exact path="/voice" element={<VoicePage />} />
      <Route exact path="/gemba/:mid" element={<Gemba />} />
      <Route exact path="/lineClearance/:mid" element={<LineClearance />} />
      <Route exact path="/dpr" element={<DPRPage />} />
      <Route exact path="/configuration" element={<ENVPage />} />
      <Route exact path="/utilities" element={<UtilsPage />} />
      <Route exact path="/audit-trail" element={<CfrPage />} />
    </Routes>
  );
}
