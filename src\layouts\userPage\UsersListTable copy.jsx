import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  Box,
  TableRow,
  Typography,
  Skeleton,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import UserItem from "./UserItem copy";
import { makeStyles } from "@mui/styles";
import { v4 as uuidv4 } from "uuid";
import Pagination from "./UI_Components/pagination";
import SearchField from "./UI_Components/search-field";
import PropTypes from "prop-types";

const useStyles = makeStyles(() => ({
  container: { all: "unset", maxHeight: "84vh" },
  tableHeaderCell: {
    all: "unset",
    fontWeight: "bold !important",
    fontSize: "0.9rem !important",
    minWidth: "6rem !important",
  },
  tableCell: { all: "unset" },
}));

export const columnCode = [
  "fname",
  "username",
  "user_role",
  "email",
  "actions",
];

const UserListTable = ({
  allUsersList,
  fetchAllUsers,
  loading,
  setloading,
  isModelOpen,
}) => {
  const currentUserTemp = localStorage.getItem("@user-creds");
  let currentUser;
  try {
    currentUser = JSON.parse(currentUserTemp);
  } catch (error) {
    console.error("Failed to parse @user-creds from localStorage:", error);
  }

  console.log("current user:- ", currentUser);
  console.log("current user role:- ", currentUser?.role);
  const [passwordToggel, setPasswordToggel] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    itemsPerPage: 6,
  });
  const [search, setSearch] = useState("");
  const [sortedData, setSortedData] = useState([]);
  const [currentItems, setCurrentItems] = useState([]);

  useEffect(() => {
    setSortedData(
      allUsersList.filter(
        (user) =>
          (user.username &&
            user.username.toLowerCase().includes(search.toLowerCase())) ||
          (user.email &&
            user.email.toLowerCase().includes(search.toLowerCase())) ||
          (user.role && user.role.toLowerCase().includes(search.toLowerCase())),
      ),
    );
    setPagination((prev) => ({ ...prev, currentPage: 1 }));
  }, [search, allUsersList]);

  useEffect(() => {
    updateCurrentItems(sortedData, setCurrentItems);
  }, [sortedData, pagination]);

  const updateCurrentItems = (users, _setCurrItems) => {
    const indexOfLastItem = pagination.currentPage * pagination.itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - pagination.itemsPerPage;
    _setCurrItems(users.slice(indexOfFirstItem, indexOfLastItem));
  };

  const classes = useStyles();
  useEffect(() => {
    fetchAllUsers();
    setloading(false);
  }, []);

  return (
    <Box
      sx={{
        bgcolor: "#f5f7fa", // Light blue-gray container background
        borderRadius: 2,
        p: 2, // Padding inside the container
        border: "1px solid #e0e0e0", // Subtle border
      }}
    >
      <SearchField
        showPDFDownload={false}
        label="Search Users"
        search={search}
        setSearch={setSearch}
        placeholder="Name, ID, Role"
        sx={{ mb: 2 }}
      />

      <TableContainer
        component={Paper}
        sx={{
          maxHeight: "400px",
          borderRadius: 1,
          boxShadow: "0 2px 4px rgba(0,0,0,0.05)", // Subtle shadow
          border: "1px solid #e0e0e0",
          backgroundColor: "#ffffff", // White table background
        }}
      >
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell sx={{ bgcolor: "#e3f2fd", fontWeight: 600 }}>
                Name
              </TableCell>{" "}
              {/* Light blue header */}
              <TableCell sx={{ bgcolor: "#e3f2fd", fontWeight: 600 }}>
                ID
              </TableCell>
              <TableCell sx={{ bgcolor: "#e3f2fd", fontWeight: 600 }}>
                Role
              </TableCell>
              <TableCell sx={{ bgcolor: "#e3f2fd", fontWeight: 600 }}>
                Email
              </TableCell>
              <TableCell
                sx={{ bgcolor: "#e3f2fd", fontWeight: 600 }}
                align="center"
              >
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              Array.from({ length: 7 }).map((_, index) => (
                <TableRow key={index}>
                  {columnCode.map((_, colIndex) => (
                    <TableCell key={colIndex}>
                      <Skeleton variant="text" />
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : currentItems?.length > 0 ? (
              currentItems.map((user) => (
                <UserItem
                  isModelOpen={isModelOpen}
                  fetchAllUsers={fetchAllUsers}
                  key={uuidv4()}
                  user={user}
                  passwordToggel={passwordToggel}
                />
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} sx={{ py: 4 }}>
                  <Typography textAlign="center" color="text.secondary">
                    No Users Found
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Box
        sx={{
          mt: 2,
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
        }}
      >
        <Pagination
          totalRows={sortedData.length}
          pagination={pagination}
          setPagination={setPagination}
          sx={{
            "& .MuiPagination-ul": { margin: 0, padding: 0 },
            "& .MuiPaginationItem-root": { minWidth: "32px", height: "32px" },
          }}
        />
      </Box>
    </Box>
  );
};

UserListTable.propTypes = {
  allUsersList: PropTypes.arrayOf(
    PropTypes.shape({ _id: PropTypes.string.isRequired }),
  ).isRequired,
  fetchAllUsers: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
  setloading: PropTypes.func.isRequired,
  isModelOpen: PropTypes.bool.isRequired,
};

export default UserListTable;
