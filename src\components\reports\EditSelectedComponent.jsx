import React, { useState, useEffect } from "react";
import { makeStyles } from "@material-ui/core/styles";
import {
  <PERSON>,
  Typography,
  <PERSON><PERSON>,
  Tooltip,
  <PERSON>,
  TextField,
} from "@material-ui/core";
import AttachFileIcon from "@material-ui/icons/AttachFile";
import { dbConfig } from "../../infrastructure/db/db-config";
import { Box, CircularProgress, IconButton } from "@mui/material";
import { toastMessage } from "../../tools/toast";
import { ImEnlarge } from "react-icons/im";
import { FileUpload } from "./file-ipnut";
import { ScannerModal } from "./scannerModal";
import { Check } from "@material-ui/icons";
import { validateStep } from "./utils";
import { DocumentScanner, UploadFile } from "@mui/icons-material";
import NotAccessible from "../not-accessible/not-accessible";
import { useCheckAccess } from "../../utils/useCheckAccess";

const useStyles = makeStyles((theme) => ({
  cardContainer: {
    width: "100%",
    minHeight: "250px",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
    display: "flex",
    flexDirection: "row",
  },
  leftSection: {
    flex: 6, // 70% width
    padding: theme.spacing(2),
  },
  rightSection: {
    flex: 4, // 30% width
    padding: theme.spacing(1),
    background: "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",
    borderLeft: "1px solid #e0e0e0",
    display: "flex",
    flexDirection: "column",
    gap: theme.spacing(1),
    // maxWidth: "30%", // Ensure it does not exceed 30%
  },
  container: {
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderRadius: "8px",
    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
    padding: theme.spacing(0.5),
  },
  title: {
    textAlign: "left",
    fontSize: "15px",
    fontWeight: "bold",
    marginBottom: theme.spacing(2),
  },
  mediaContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
  },
  media: {
    width: "100%",
    maxHeight: "350px",
    objectFit: "contain",
  },
  attachmentsContainer: {
    display: "flex",
    flexDirection: "column", // Stack items vertically
    maxHeight: "120px", // Increase height for more visible items
    overflowY: "auto", // Enable vertical scrolling
    paddingRight: theme.spacing(0.5),
    gap: theme.spacing(0.5), // Add space between chips
  },
  qrCodesContainer: {
    display: "flex",
    flexDirection: "column", // Stack QR codes vertically
    maxHeight: "50px", // Adjust the height as needed
    overflowY: "auto", // Enable scrolling
    paddingRight: theme.spacing(0.5),
  },
  chip: {
    fontSize: "12px", // Reduce font size of the chip label
    height: "24px", // Reduce chip height for compactness
    marginRight: theme.spacing(0.5), // Smaller margin for tighter spacing
    marginBottom: theme.spacing(0.5),
  },
  commentInput: {
    width: "100%",
  },
  commentButton: {
    // marginTop: theme.spacing(0.5),
    textAlign: "right",
  },
}));

export const EditSelectedComponent = ({
  item,
  setEnlarge,
  setEValue,
  stepKey, // changed from index to stepKey
  handleSubmit = async (item, updatedItem) => {},
}) => {
  const classes = useStyles();
  const [isVideoLoading, setIsVideoLoading] = useState(true);
  const [tempItem, setTempItem] = useState(item);
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const hasChangeOverReportStepGETAccess = useCheckAccess(
    "changeOverReportSteps",
    "GET",
  );
  const hasChangeOverReportStepPUTAccess = useCheckAccess(
    "changeOverReportSteps",
    "PUT",
  );

  useEffect(() => {
    setTempItem(item);
  }, [item]);

  const renderMedia = () => {
    if (item?.format === "image") {
      return (
        <img
          src={`${dbConfig.url_storage}/${item?.url}`}
          alt={item.url}
          className={classes.media}
        />
      );
    } else if (item?.format === "video") {
      return (
        <div style={{ position: "relative" }}>
          {isVideoLoading && (
            <div
              style={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                zIndex: 1,
              }}
            >
              <CircularProgress />
            </div>
          )}
          <video
            controls
            className={classes.media}
            src={`${dbConfig.url_storage}/${item?.url}`}
            onCanPlay={() => setIsVideoLoading(false)}
            onWaiting={() => setIsVideoLoading(true)}
            onError={() => setIsVideoLoading(false)}
          >
            Your browser does not support the video tag.
          </video>
        </div>
      );
    } else if (item?.format === "audio") {
      return (
        <audio controls className={classes.media}>
          <source
            src={`${dbConfig.url_storage}/${item?.url}`}
            type="audio/mp3"
          />
          Your browser does not support the audio tag.
        </audio>
      );
    } else {
      return <Typography variant="body2">NO MEDIA FOUND</Typography>;
    }
  };

  const handleRemarksChange = (e) => {
    const value = e.target.value;
    // Prevent space as the first character
    if (value.length === 1 && value === " ") {
      return; // Ignore if the first character is a space
    }
    const trimmedValue = value.replace(/^\s+/, ""); // Remove leading spaces
    setTempItem((prev) => ({ ...prev, remarks: trimmedValue }));
    setError(""); // Clear error when user types
  };

  const handleSave = async () => {
    const { error, isValid } = validateStep(tempItem);
    if (!isValid) {
      setError(error);
      toastMessage("error", error);
      return;
    }
    setIsSubmitting(true);

    await handleSubmit(item, tempItem);
    setError("");
    setIsSubmitting(false);
  };

  return (
    <Card className={classes.cardContainer}>
      {/* Left Section: Media */}
      <Box className={classes.leftSection}>
        {hasChangeOverReportStepGETAccess ? (
          <>
            <Typography className={classes.title}>
              Step {stepKey}: {item.title}
            </Typography>
            <Box className={classes.mediaContainer}>{renderMedia()}</Box>
            {item?.format === "image" && (
              <Box className={classes.commentButton}>
                <Tooltip title="Enlarge">
                  <IconButton
                    onClick={() => {
                      setEnlarge(true);
                      setEValue(item.url);
                    }}
                    variant="outlined"
                    color="primary"
                  >
                    <ImEnlarge />
                  </IconButton>
                </Tooltip>
              </Box>
            )}
          </>
        ) : (
          <NotAccessible />
        )}
      </Box>

      {/* Right Section: Remarks, Attachments, QR Codes */}
      <Box className={classes.rightSection}>
        {/* QR Codes Container */}
        <Box
          className={classes.container}
          sx={{ display: "flex", justifyContent: "space-between" }}
        >
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6">QR Codes</Typography>
            <Box className={classes.qrCodesContainer}>
              {tempItem.qr_codes && tempItem.qr_codes.length > 0 ? (
                <Typography>
                  Required: {tempItem.qr_codes.join(", ")}
                </Typography>
              ) : (
                <Typography>No QR Codes Required.</Typography>
              )}
              {tempItem.qr_scanned && tempItem.qr_scanned.length > 0 ? (
                <Typography>
                  Scanned: {tempItem.qr_scanned.join(", ")}
                </Typography>
              ) : (
                <Typography color="error">No QR Codes Scanned.</Typography>
              )}
            </Box>
          </Box>
          <Box>
            <ScannerModal
              key={item.id || item._id}
              qrCodes={item.qr_codes}
              onScanComplete={(result) => {
                setTempItem((prev) => ({
                  ...prev,
                  qr_scanned: [
                    ...new Set([...(prev.qr_scanned || []), result]),
                  ],
                }));
              }}
            >
              <DocumentScanner
                color={
                  !hasChangeOverReportStepPUTAccess
                    ? "disabled"
                    : tempItem?.qr_codes?.length > 0 &&
                        tempItem?.qr_scanned?.length !==
                          tempItem?.qr_codes?.length
                      ? "error"
                      : "success"
                }
              />
            </ScannerModal>
          </Box>
        </Box>

        {/* Attachments Container */}
        <Box
          className={classes.container}
          sx={{ display: "flex", flexDirection: "column" }}
        >
          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 1 }}>
            <Typography variant="h6">Attachments</Typography>
            <FileUpload
              onUploadComplete={(result) => {
                console.log("result", result);
                setTempItem((prev) => ({
                  ...prev,
                  attachments: [
                    ...(prev.attachments || []),
                    { url: result, type: "Complete" },
                  ],
                }));
              }}
            >
              <UploadFile
                color={
                  !hasChangeOverReportStepPUTAccess
                    ? "disabled"
                    : tempItem?.step?.type.toLowerCase() === "camera" &&
                        tempItem?.attachments?.length === 0
                      ? "error"
                      : "success"
                }
                sx={{ mx: 0.5 }}
              />
            </FileUpload>
          </Box>
          <Box
            className={classes.attachmentsContainer}
            sx={{
              maxWidth: "420px",
              overflowY: "auto", // Ensure vertical scroll
              overflowX: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "normal", // Allow wrapping if needed
            }}
          >
            {tempItem.attachments && tempItem.attachments.length > 0 ? (
              tempItem.attachments.map((attachment, index) => (
                <Chip
                  key={index}
                  icon={<AttachFileIcon fontSize="small" style={{ paddingTop: 2,paddingBottom: 2}}/>}
                  label={`Attachment-${index + 1}${attachment?.type ? ` - ${attachment?.type}` : ""}`}
                  className={classes.chip}
                  style={{ justifyContent: "flex-start" }}
                  onClick={() =>
                    window.open(
                      `${dbConfig.url_storage}/${attachment?.url}`,
                      "_blank",
                    )
                  }
                />
              ))
            ) : (
              <Typography>No attachments available.</Typography>
            )}
          </Box>
        </Box>

        {/* Remarks Container */}
        <Box className={classes.container}>
          <Typography variant="h6">Remarks</Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={tempItem.remarks}
            onChange={handleRemarksChange}
            placeholder="Enter your remarks here"
            className={classes.commentInput}
            error={!!error} // Show error state if error exists
            helperText={error} // Display error message
            disabled={!hasChangeOverReportStepPUTAccess}
          />
        </Box>

        <Box>
          <Button
            onClick={handleSave}
            color="primary"
            variant="contained"
            startIcon={
              isSubmitting ? <CircularProgress size={20} /> : <Check />
            }
            disabled={!hasChangeOverReportStepPUTAccess}
          >
            Save
          </Button>
        </Box>
      </Box>
    </Card>
  );
};
