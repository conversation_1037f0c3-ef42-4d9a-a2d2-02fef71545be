import React from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
);

const options = {
  responsive: true,
  tension: 0.3,
  scales: {
    y: {
      type: "linear",
      position: "left",
      title: {
        display: true,
        text: "Time",
      },
      ticks: {
        stepSize: 60,
        callback: (value) => convertMinutesToTime(value),
      },
    },
    x: {
      type: "linear",
      position: "bottom",
      title: {
        display: true,
        text: "Minutes",
      },
      ticks: {
        stepSize: 60,
      },
    },
  },
  plugins: {
    filler: {
      propagate: true,
    },
  },
};

const stoppageData = {
  major: [
    { time: "08:15", reason: "Machine malfunction", duration: 20 }, // in minutes
    { time: "09:30", reason: "Maintenance", duration: 15 },
  ],
  minor: [
    { time: "08:45", reason: "Material shortage", duration: 10 },
    { time: "10:00", reason: "Tool change", duration: 5 },
  ],
};

const lineData = {
  labels: ["0", "60", "120", "180", "240"], // Assuming each label represents 60 minutes
  datasets: [
    {
      data: [],
      borderColor: "rgba(255, 0, 0, 1)",
      borderWidth: 2,
      fill: false,
    },
    {
      data: [],
      borderColor: "rgba(255, 165, 0, 1)",
      borderWidth: 2,
      fill: false,
    },
  ],
  fill: {
    target: "origin",
    above: "rgba(0, 255, 0, 0.3)", // Green for active state
  },
};

const convertTimeToMinutes = (time) => {
  const [hours, minutes] = time.split(":");
  return parseInt(hours) * 60 + parseInt(minutes);
};

const convertMinutesToTime = (minutes) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${String(hours).padStart(2, "0")}:${String(mins).padStart(2, "0")}`;
};

const StoppageAnalysisChart = () => {
  stoppageData.major.forEach((stoppage) => {
    const startTime = convertTimeToMinutes(stoppage.time);
    const endTime = startTime + stoppage.duration;

    lineData.datasets[0].data.push({
      x: startTime,
      y: 0,
    });
    lineData.datasets[0].data.push({
      x: endTime,
      y: 0,
    });
  });

  stoppageData.minor.forEach((stoppage) => {
    const startTime = convertTimeToMinutes(stoppage.time);
    const endTime = startTime + stoppage.duration;

    lineData.datasets[1].data.push({
      x: startTime,
      y: 1,
    });
    lineData.datasets[1].data.push({
      x: endTime,
      y: 1,
    });
  });

  return <Line options={options} data={lineData} />;
};

export default StoppageAnalysisChart;
