// CalendarToolbar.js
import React from "react";
import { Button } from "@mui/material";

export const CalendarToolbar = ({ onFetchData }) => {
  return (
    <div style={{ marginBottom: "20px" }}>
      <Button variant="outlined" onClick={() => onFetchData("maintenanceDone")}>
        Maintenance Done
      </Button>
      <Button variant="outlined" onClick={() => onFetchData("maintenanceDue")}>
        Maintenance Due
      </Button>
      <Button variant="outlined" onClick={() => onFetchData("changeoverDue")}>
        Changeover Due
      </Button>
      <Button variant="outlined" onClick={() => onFetchData("calibrationDue")}>
        Calibration Due
      </Button>
      <Button
        variant="outlined"
        onClick={() => onFetchData("maintenanceReport")}
      >
        Maintenance Report
      </Button>
    </div>
  );
};
