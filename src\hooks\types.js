/**
 * @typedef {Object} User
 * @property {string} id
 * @property {number} [created_at]
 * @property {number} [updated_at]
 * @property {string} email
 * @property {string} [fname]
 * @property {string} [lname]
 * @property {string} [phone]
 * @property {string} role
 * @property {string} username
 * @property {string} [avatar]
 * @property {string} [password]
 * @property {number} [attempt]
 * @property {number} [password_created]
 * @property {string} [qnn]
 * @property {string} [signature]
 */
/**
 * @typedef {Object} SetPointCalR
 * @property {string} _id - Unique identifier, default generated.
 * @property {number} [reading] - Standard Before value.
 * @property {number} [eq_reading] - Standard Before Equivalent value.
 * @property {string} [reading_string] - Standard Before value in string format.
 * @property {string} [eq_reading_string] - Standard Before Equivalent value in string format.
 * @property {string} [reading_image_url] - URL for the Standard Before image.
 * @property {string} [eq_reading_image_url] - URL for the Standard Before Equivalent image.
 *
 * @property {number} [reading_after] - Standard After value.
 * @property {number} [eq_reading_after] - Standard After Equivalent value.
 * @property {string} [reading_after_string] - Standard After value in string format.
 * @property {string} [eq_reading_after_string] - Standard After Equivalent value in string format.
 * @property {string} [reading_after_image_url] - URL for the Standard After image.
 * @property {string} [eq_reading_after_image_url] - URL for the Standard After Equivalent image.
 *
 * @property {number} [before] - UUC Before value.
 * @property {number} [before_for_adjustment] - UUC Before Equivalent value.
 * @property {string} [before_string] - UUC Before value in string format.
 * @property {string} [before_for_adjustment_string] - UUC Before Equivalent value in string format.
 * @property {string} [before_image_url] - URL for the UUC Before image.
 * @property {string} [before_for_adjustment_image_url] - URL for the UUC Before Equivalent image.
 *
 * @property {number} [after] -UUC After value.
 * @property {number} [eq_after] - UUC After Equivalent value.
 * @property {string} [after_string] - UUC After value in string format.
 * @property {string} [eq_after_string] - UUC After Equivalent value in string format.
 * @property {string} [after_image_url] - URL for the UUC After image.
 * @property {string} [eq_after_image_url] - URL for the UUC After Equivalent image.
 * @property {string} [result_value] - Error Result Before value as a string.
 * @property {string} [result_value_after] - Error Result After value as a string.
 *
 * @property {string} [result_status] - Status of the result (PASS / FAIL).
 *
 * @property {string} [setPointTableId] - Reference ID for SetPointTable.
 * @property {string} [calReportId] - Reference ID for CalReport.
 */

/**
 * @typedef {Object} SetPointTable
 * @property {string} _id - Unique identifier, default generated.
 * @property {string} [range] - Range of the set point.
 * @property {string} [least_count] - Least count, default is an empty string.
 * @property {string} [unit_of_measure] - Unit of measurement.
 * @property {string} [eq_unit] - Equivalent unit.
 * @property {string} [eq_unit_uuc] - Equivalent unit for UUC.
 * @property {string} [accuracy] - Accuracy of the set point.
 * @property {string} [additional_description] - Additional description.
 * @property {number} [reading_coefficient] - Reading coefficient.
 * @property {number} [reading_constant] - Reading constant.
 * @property {number} [reading_before_coefficient] - Reading before coefficient.
 * @property {number} [reading_before_constant] - Reading before constant.
 * @property {number} [reading1High] - Reading 1 high value, default is 0.
 * @property {number} [reading1Low] - Reading 1 low value, default is 0.
 * @property {number} [reading2High] - Reading 2 high value, default is 0.
 * @property {number} [reading2Low] - Reading 2 low value, default is 0.
 * @property {number} [readingBefore1High] - Reading before 1 high value, default is 0.
 * @property {number} [readingBefore1Low] - Reading before 1 low value, default is 0.
 * @property {number} [readingBefore2High] - Reading before 2 high value, default is 0.
 * @property {number} [readingBefore2Low] - Reading before 2 low value, default is 0.
 * @property {Date} [asFoundStdEnabledAt] - Timestamp when the AS found for standard was enabled.
 * @property {Date} [asFoundUUCEnabledAt] - Timestamp when the AS found for uuc was enabled.
 * @property {SetPointCalR[]} set_points - Array of set point calibration records.
 * @property {string} [calReportId] - Reference ID for calReport.
 */

/**
 * @typedef {Object} CalReport
 * @property {string} _id - Unique identifier, default generated.
 * @property {bigint} [created_at] - Timestamp when the report was created.
 * @property {bigint} [updated_at] - Timestamp when the report was updated.
 * @property {string} [sop_link] - Link to the Standard Operating Procedure.
 * @property {string} [request_type] - Type of the request.
 * @property {string} [request_object_id] - ID of the request object.
 * @property {string} [status] - Status of the report.
 * @property {bigint} [calibration_done] - Timestamp when the calibration was done.
 * @property {bigint} [calibration_due_date] - Timestamp for the calibration due date.
 * @property {string} [instrument_desc] - Description of the instrument.
 * @property {string} [instrument_id] - ID of the instrument.
 * @property {boolean} [setpoints_added] - Indicates if set points were added.
 * @property {string} [instrument_location] - Location of the instrument.
 * @property {string} [equipment_location] - Location of the equipment.
 * @property {string} [customer_name] - Name of the customer.
 * @property {bigint} [calibration_done_on_report] - Timestamp when calibration was done on the report.
 * @property {bigint} [calibration_due_on_report] - Timestamp when calibration is due on the report.
 * @property {string} [calibration_schedule_id] - ID of the calibration schedule.
 * @property {string} [range] - Range of the calibration.
 * @property {string} [accuracy] - Accuracy of the calibration.
 * @property {string} [room_temp] - Room temperature.
 * @property {string} [relative_humidity] - Relative humidity.
 * @property {string} [certificate_no_report] - Certificate number for the report.
 * @property {string} [remarks_report] - Remarks on the report.
 * @property {Object[]} performed_by - Array of users who performed the calibration.
 * @property {Object[]} approved_by - Array of users who approved the calibration.
 * @property {bigint} [time_of_calibration] - Timestamp when the calibration was performed.
 * @property {bigint} [time_of_approval] - Timestamp when the report was approved.
 * @property {boolean} [external_calibration] - Indicates if external calibration was performed.
 * @property {boolean} [manual_ins_overdue] - Indicates if manual inspection is overdue.
 * @property {string} [make] - Make of the instrument/equipment.
 * @property {string} [equipment_desc] - Description of the equipment.
 * @property {string} [equipment_id] - ID of the equipment.
 * @property {string} [frequency] - Frequency of the calibration.
 * @property {string} [category] - Category of the calibration.
 * @property {string} [report_type] - Type of the report.
 * @property {Object[]} standard_details - Array of standard details.
 * @property {string} [least_count] - Least count, default is an empty string.
 * @property {string} [least_count_for_report] - Least count for the report, default is an empty string.
 * @property {string} [remarks] - Remarks on the calibration.
 * @property {SetPointTable[]} set_points_table - Array of set point tables.
 * @property {string} [cal_master_id] - ID of the calibration master.
 * @property {string} [department_id] - ID of the department.
 * @property {string} [schedule_status] - Status of the schedule.
 * @property {string} [initiated_by_role] - Role of the user who initiated the report.
 * @property {string} [initiated_by_user] - User who initiated the report.
 * @property {string} [field_type] - Type of the field, default is "standard".
 * @property {Date} [returnedAt] - Date and time when the report was returned.
 */
