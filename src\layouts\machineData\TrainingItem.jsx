import React, { useState, useEffect, useRef, useContext } from "react";
import {
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  TableCell,
  TableRow,
  Tooltip,
  DialogActions,
  Button,
  Menu,
  MenuItem as MuiMenuItem,
} from "@mui/material";
import Delete from "../../components/Delete/Delete";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import Manuals from "../Manuals/Manuals";
import EditTraining from "./EditMaintenance/EditTraining";
import { useAuth } from "../../hooks/AuthProvider";
import { useStateContext } from "../../context/ContextProvider";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { useMaintenanceInfo } from "../../context/MaintenanceContext";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { useDeleteMachineCfr } from "../../hooks/cfr/machineCfrProvider";
import { makeStyles } from "@mui/styles";
import { HashtagContext } from "../../services2/hashtag/hashtag.context";
import { ButtonBasicCancel } from "../../components/buttons/Buttons";
import AddStepDialog from "./AddStepDialog";
import ReportIcon from "@mui/icons-material/Report";
import {
  AddCircleOutlineOutlined,
  PictureAsPdf,
  ReceiptLong,
  ViewList,
} from "@mui/icons-material";
import ReportDialog from "./ReportDialog";
import SopPreviewDialog from "./SopPreviewDialog";
import ArrangeStepsDialog from "./ArrangeStepsDialog";
import { useCheckAccess } from "../../utils/useCheckAccess";

const useCustomStyles = makeStyles((theme) => ({
  manualsContainer: {
    padding: "0.5rem",
    backgroundColor: theme.palette.custom.backgroundForth,
    height: "auto",
  },
}));
const TrainingItem = ({
  data,
  mid,
  machineName,
  role,
  parent = "Training",
  lastItem = false,
}) => {
  console.log(" training item data:- ",data)
  console.log(" training item mainual id:- ",data?._id)
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const { currentUser } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const customCss = useCustomStyles();
  const [open, setOpen] = useState(false);
  const [sensorList, setSensorList] = useState([]); // liveData2 aaray
  const [steps, setSteps] = useState([]);
  const [stepIndex, setStepIndex] = useState(0);
  const [maintenance, setMaintenance] = useState({});
  const { hashes, handleHastageData, setTempHashes, hashArray, temptemp } =
    useContext(HashtagContext);
  const maintenanceInfoFromContext = useMaintenanceInfo();
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const deletetrainingcfr = useDeleteMachineCfr();
  const [openReportConfirm, setOpenReportConfirm] = useState(false);
  var ind = 0;
  const manual_id = data?._id;
  const [openSop, setOpenSop] = useState(false);
  const [openArrangeSteps, setOpenArrangeSteps] = useState(false); // New state for ArrangeStepsDialog

  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const hasTrainingPUTAccess = useCheckAccess("training", "PUT");
  const hasTrainingDELETEAccess = useCheckAccess("training", "DELETE");
  const hasTrainingReportPOSTAccess = useCheckAccess("manualReport", "POST");
  const hasStepPOSTAccess = useCheckAccess("stepdata", "POST");

  const [anchorEl, setAnchorEl] = useState(null);
  const openMenu = Boolean(anchorEl);

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleGenerateReport = async () => {
    try {
      const reportData = {
        title: data.title,
        desc: data.desc,
        sop_url: data.sop_url,
        mid: data.mid,
        issue_id: data.issue_id,
        main_id: data._id,
        email: currentUser.email,
      };

      const response = await axios.post(
        `${dbConfig.url}/manualReport/create-report`,
        reportData
      );

      if (response.data.success) {
        toastMessageSuccess({
          message: response.data.message || "Report generated successfully!",
        });
        setRefreshCount(refreshCount + 1);
      }
      setOpenReportConfirm(false);
    } catch (error) {
      toastMessage({
        message: error.response?.data?.message || "Failed to generate report",
      });
    }
  };
  const deleteData = async () => {
    const date = new Date();
    const data2 = {
      activity: "training deleted",
      dateTime: date,

      description: "a training is deleted",
      machine: mid,
      module: "Training",
      username: currentUser.username,
    };
    await axios.delete(`${dbConfig.url}/training/${data._id}`).then(() => {
      toastMessageSuccess({
        message: `${data.title} has been deleted successfully!`,
      });
      deletetrainingcfr(data2);

      setOpenDel(false);
      setRefreshCount(refreshCount + 1);
    });
  };

  return (
    <>
      <TableRow
        hover
        sx={{
          "&:last-child td, &:last-child th": { border: 0 },
          borderBottom: lastItem ? "none" : "0.05rem solid #e0e0e0",
          "&:hover": { bgcolor: "#f5f5f5" },
        }}
        style={{ cursor: "pointer" }}
      >
        <TableCell
          style={
            currentMode === "Dark"
              ? {
                  color: "white",
                  borderBottom: "none",
                  maxWidth: "15vw",
                  wordBreak: "break-word",
                }
              : {
                  color: "black",
                  borderBottom: "none",
                  maxWidth: "15vw",
                  wordBreak: "break-word",
                }
          }
          align="left"
        >
          <b className="capitalize">{data?.title}</b>
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? {
                  color: "white",
                  borderBottom: "none",
                  maxWidth: "50vw",
                  wordBreak: "break-word",
                }
              : {
                  color: "black",
                  borderBottom: "none",
                  maxWidth: "50vw",
                  wordBreak: "break-word",
                }
          }
          align="left"
          sx={{
            maxWidth: 330,
            maxLines: parseInt(
              isNaN(Number(data?.desc?.length))
                ? 1
                : Number(data?.desc?.length) / 5
            ),
          }}
        >
          {data.desc}
        </TableCell>

        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="center"
        >
          <div className="dataBtns">
            {role !== "trainee" && (
              <>
                <IconButton
                  onClick={() => setOpenEdit(true)}
                  disabled={!hasTrainingPUTAccess}
                  sx={{
                    color: !hasTrainingPUTAccess ? "grey.500" : "primary.main",
                  }}
                >
                  <EditIcon style={{ fontSize: "20px" }} />
                </IconButton>
              </>
            )}
            <IconButton onClick={handleMenuOpen}>
              <MoreVertIcon
                style={{
                  fontSize: "20px",
                  color: currentMode === "Dark" ? "#fff" : "#000",
                }}
              />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={openMenu}
              onClose={handleMenuClose}
              PaperProps={{
                style: {
                  backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
                  color: currentMode === "Dark" ? "#fff" : "#000",
                },
              }}
            >
              <MuiMenuItem
                onClick={() => {
                  setOpenDel(true);
                  handleMenuClose();
                }}
                disabled={role === "trainee" || !hasTrainingDELETEAccess}
              >
                <DeleteIcon
                  style={{
                    fontSize: "20px",
                    color: "#f00",
                    marginRight: "8px",
                  }}
                />
                Delete
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpen(true);
                  handleMenuClose();
                }}
                disabled={!hasStepPOSTAccess}
              >
                <AddCircleOutlineOutlined
                  style={{ fontSize: "20px", marginRight: "8px" }}
                />
                Add Step
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenArrangeSteps(true);
                  handleMenuClose();
                }}
              >
                <ViewList style={{ fontSize: "20px", marginRight: "8px" }} />
                Arrange Steps
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenReportConfirm(true);
                  handleMenuClose();
                }}
                disabled={!hasTrainingReportPOSTAccess}
              >
                <ReceiptLong
                  style={{ fontSize: "20px", marginRight: "8px" }}
                />
                Create Report
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenSop(true);
                  handleMenuClose();
                }}
                disabled={!data?.sop_url}
              >
                <PictureAsPdf
                  style={{
                    fontSize: "20px",
                    marginRight: "8px",
                    color: data?.sop_url
                      ? currentMode === "Dark"
                        ? "#fff"
                        : "red"
                      : "#ccc",
                  }}
                />
                View SOP
              </MuiMenuItem>
            </Menu>
            <IconButton
              onClick={() => setIsOpen(!isOpen)}
              style={currentMode === "Dark" ? { color: "white" } : {}}
            >
              {isOpen ? (
                <ExpandLessIcon style={{ fontSize: "20px" }} />
              ) : (
                <ExpandMoreIcon style={{ fontSize: "20px" }} />
              )}
            </IconButton>
          </div>
        </TableCell>
      </TableRow>
      <>
        {isOpen && (
          <TableRow
            sx={{
              "&:last-child td, &:last-child th": {
                border: 0,
              },
            }}
          >
            <TableCell
              style={
                currentMode === "Dark"
                  ? { color: "white", borderBottom: "none" }
                  : { color: "black", borderBottom: "none" }
              }
              className="subData"
              align="center"
              colSpan={4}
            >
              <div className={customCss.manualsContainer}>
                <Manuals
                  parent="TrainingItem"
                  moduleName="TrainingItem"
                  manual_id={data._id}
                  recData={data}
                  machineName={machineName}
                  name={data.title}
                  userName={`${currentUser.fname} ${currentUser.lname}`}
                  onStepIndexChange={setCurrentStepIndex}
                />
              </div>
            </TableCell>
          </TableRow>
        )}
      </>
      <Dialog open={openDel}>
        <Delete onClose={() => setOpenDel(false)} onDelete={deleteData} />
      </Dialog>

      <Dialog open={openEdit} fullWidth>
        <DialogTitle
          style={
            currentMode === "Dark"
              ? { backgroundColor: "#212B36", color: "white" }
              : {}
          }
        >
          Edit Training - [{data.title}]
        </DialogTitle>
        <DialogContent
          style={
            currentMode === "Dark"
              ? { backgroundColor: "#212B36", color: "white" }
              : {}
          }
        >
          <EditTraining
            handleClose={() => setOpenEdit(false)}
            mid={data.mid}
            data={data}
            machineName={machineName}
            name={data.title}
            userName={`${currentUser.fname} ${currentUser.lname}`}
          />
        </DialogContent>
      </Dialog>

      <AddStepDialog
        open={open}
        setOpen={setOpen}
        data={data?.mid}
        machinetype={data?.type}
        manual_id={manual_id}
        machineName={machineName}
        handleHastageData={handleHastageData}
        steps={steps}
        setRefreshCount={setRefreshCount}
        refreshCount={refreshCount}
        currentMode={currentMode}
        parent={parent}
        setTempHashes={setTempHashes}
        hashArray={hashArray}
        temptemp={temptemp}
        currentStepIndex={currentStepIndex} // Pass current step index to AddStepDialog
      />

      <ReportDialog
        open={openReportConfirm}
        onClose={() => setOpenReportConfirm(false)}
        onConfirm={handleGenerateReport}
        title={data.title}
      />

      <SopPreviewDialog
        open={openSop}
        onClose={() => setOpenSop(false)}
        sopUrl={data?.sop_url}
        title={data?.title}
        currentMode={currentMode}
      />

      <ArrangeStepsDialog
        open={openArrangeSteps}
        onClose={() => setOpenArrangeSteps(false)}
        steps={data?.steps}
        currentMode={currentMode}
        maintenanceId={data?._id}
      />
    </>
  );
};

export default TrainingItem;
