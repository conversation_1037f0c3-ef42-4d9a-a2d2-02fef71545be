// sharedStyles.js
import { makeStyles } from "@mui/styles";
import { manuals } from "../constants/data";

export const sharedCss = makeStyles((theme) => ({
  searchBox: {
    "& input": {
      color: `${theme.palette.custom.textColor} !important`,
    },
    "& fieldset": {
      borderColor: `${theme.palette.custom.textColor} !important`,
      border: "1px solid",
    },
    "& label": {
      color: `${theme.palette.custom.textColor} !important`,
    },
    "&:focus-within fieldset": {
      borderColor: `${theme.palette.primary.main} !important`,
    },
  },
  dropDown: {
    "& input": {
      color: `${theme.palette.custom.textColor} !important`,
    },
    "& fieldset": {
      borderColor: `${theme.palette.custom.textColor} !important`,
      border: "1px solid",
    },
    "& label": {
      color: `${theme.palette.custom.textColor} !important`,
    },
    "&:focus-within fieldset": {
      borderColor: `${theme.palette.primary.main} !important`,
    },
  },
  inputAlignmentFix: {  
    "& .MuiInputBase-root": {
      height: "36px", // Match both components (adjust as per visual)
    },
  },
  headingContainer: {
    display: "flex",
    justifyContent: "space-between",
    marginBottom: theme.spacing(2),
    // padding: "2rem",
    // borderRadius: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
    color: theme.palette.custom.textColor,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  generalBackground: {
    backgroundColor: theme.palette.custom.backgroundColor,
    color: theme.palette.custom.textColor,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
    // borderRadius: "10px",
  },
  sectionContainer: {
    padding: "0.2rem 0.75rem 0.75rem 0.75rem",
    backgroundColor: theme.palette.custom.backgroundColor,
    color: theme.palette.custom.textColor,
    flexDirection: "column",
    display: "flex",
    // gap: "1rem",
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  innerSection: {
    // padding: "1rem",
    // border: "1px solid gainsboro",
    backgroundColor: theme.palette.custom.backgroundFifth,
    display: "flex",
    flexDirection: "column",
    // gap: "1rem",
  },
  tableLable: {
    color: theme.palette.custom.textColor,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    margin: "0.75rem 0rem 0.75rem 0rem",
    // marginTop: "1rem",
  },
  tableRightContent: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    gap: "1rem",
   
  },
  tableConatiner: {
    backgroundColor: theme.palette.custom.backgroundSecondary,
    color: theme.palette.custom.textColor,
    marginBlock: "1rem",
    border:
      theme.palette.type === "Light"
        ? `.2px solid ${theme.palette.custom.textColor}`
        : "none",
  },
  tablePagination: {
    display: "flex",
    justifyContent: "flex-end",
    marginTop: theme.spacing(2),
    "& .MuiTablePagination-toolbar": {
      display: "flex",
      alignItems: "center",
      gap: "0.75rem",
      paddingLeft: 0,
      paddingRight: 0,
      minHeight: "36px", // tighter and better alignment
    },
    "& .MuiTablePagination-selectLabel": {
      margin: 0,
      padding: 0,
      fontSize: "0.875rem",
      display: "flex",
      alignItems: "center",
      
    },
    "& .MuiInputBase-root": {
      margin: 0,
      fontSize: "0.875rem",
      height: "32px", // standard input height
      display: "flex",
      alignItems: "center",
      paddingTop: "0.2rem",
    },
    "& .MuiTablePagination-displayedRows": {
      margin: 0,
      padding: 0,
      fontSize: "0.875rem",
      display: "flex",
      alignItems: "center",
      paddingTop: "0.2rem",
    },
    "& .MuiTablePagination-actions": {
      display: "flex",
      alignItems: "center",
      paddingTop: "0.2rem",
      color: theme.palette.primary.main, // 👈 ensures icons inherit theme color
  "& svg": {
    color: theme.palette.primary.main, // 👈 directly styles the arrow icons
  },
    },
  },
  backgroundLight: {
    backgroundColor: theme.palette.custom.backgroundForth,
    color: theme.palette.custom.textColor,
  },
  backgroundLight2: {
    backgroundColor: theme.palette.custom.backgroundThird,
    color: theme.palette.custom.textColor,
  },
  folderLink: {
    textDecoration: "none", // Remove underline
    color: "inherit", // Inherit text color or set a specific color
  },
}));
