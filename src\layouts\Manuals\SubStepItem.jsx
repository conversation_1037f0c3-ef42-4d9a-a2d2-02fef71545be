/* eslint-disable jsx-a11y/alt-text */
import {DeleteFilled, EditOutlined} from "@ant-design/icons";
import {Button, Paper} from "@mui/material";
import {Dialog, IconButton} from "@mui/material";
import React, {useState, useRef} from "react";
import EditIcon from "@mui/icons-material/Edit";
import Delete from "../../components/Delete/Delete";
import {companies, companyId_constant, subStep} from "../../constants/data";
import {db} from "../../firebase";
import {toastMessage, toastMessageSuccess} from "../../tools/toast";
import "./SubStepModal.scss";
import "./Manual.scss";
import EditSubStep from "./EditSubStep";
import {useStateContext} from "../../context/ContextProvider";
import axios from "axios";
import {dbConfig} from "../../infrastructure/db/db-config";
import {toast} from "react-toastify";
import {useDeleteMachineCfr} from "../../hooks/cfr/machineCfrProvider";
import {useAuth} from "../../hooks/AuthProvider";
import {useParams, useNavigate} from "react-router-dom";
import DeleteIcon from "@mui/icons-material/Delete";

function GetPreview({format, url, desc}) {
  console.log("format", format);
  const {currentMode} = useStateContext();
  const fileFormatSuggestion = "Specify the format by clicking edit step";

  if (format === "image") {
    return <img style={{maxHeight: "350px", maxWidth: "350px"}} src={url} />;
  } else if (format === "video") {
    return (
      <video
        style={{width: "50rem", height: "20rem"}}
        controls
        src={url}
        alt="First slide"
      />
    );
  } else if (format === "audio") {
    return (
      <audio
        style={{marginTop: "15%", marginRight: "50px", marginBottom: "20px"}}
        controls>
        <source src={url} />
      </audio>
    );
  } else if (format === "text") {
    return (
      <div
        className="max-w-5xl w-10/12 h-96 max-h-96 p-4 mb-4 flex justify-center items-center text-lg font-bold self-center capitalize overflow-y-auto"
        style={{wordBreak: "break-word"}}>
        <span
          data-title={fileFormatSuggestion}
          style={currentMode === "Dark" ? {color: "#fff"} : {}}>
          {desc}
        </span>
      </div>
    );
  } else if (!format) {
    return (
      <div className="w-40 h-85 animate-pulse self-center">
        <span data-title={fileFormatSuggestion}>File format is missing !</span>
      </div>
    );
  }

  return <h5>NO MEDIA</h5>;
}

const SubStepItem = ({
  step,
  parent,
  onSubStepDelete = id => {},
  data,
  sensorList,
  setIsEditing,
  closeItem,
  setIsMediaPreviewOpen,
}) => {
  const {currentUser} = useAuth();
  const {mid} = useParams();

  const deletetrainingsubstepcfr = useDeleteMachineCfr();
  const deletemaintenancesubstepcfr = useDeleteMachineCfr();

  const [openDelete, setOpenDelete] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const {currentMode} = useStateContext();
  console.log("delete substep", data);

  const focusedItem = useRef(null);

  const handleDelete = async () => {
    const date = new Date();

    const data2 = {
      activity: "substep deleted",
      dateTime: date,

      description: "a substep is deleted",
      machine: "Training",
      module: mid,
      username: currentUser.username,
    };
    const data3 = {
      activity: "substep deleted",
      dateTime: date,

      description: "a substep is deleted",
      machine: "Maintenance",
      module: mid,
      username: currentUser.username,
    };

    setOpenDelete(false);
    const fullUrl =
      parent !== "Alarm SOP"
        ? `${dbConfig.url}/sub-steps/${data._id}`
        : `${dbConfig.url}/alarmSopSubStepData/${data._id}`;
    await axios
      .delete(fullUrl)
      .then(() => {
        if (parent == "Maintenance") {
          deletemaintenancesubstepcfr(data3);
        } else {
          deletetrainingsubstepcfr(data2);
        }
        toastMessageSuccess({
          message: `Deleted ${data?.title} Successfully!`,
        });
        closeItem();
      })
      .catch(err => toastMessage({message: err.message}));

    onSubStepDelete?.(data._id);
  };

  const handleClose = () => {
    setOpenEdit(false);
    // focusedItem.current.focus();
    setIsEditing(false);
  };
  return (
    <div className="outline-0">
      <div className="p-3 carouselInner outline-0" component={Paper}>
        {openEdit && (
          <EditSubStep
            parent={parent}
            collectionName="subStepData"
            step={data}
            newStep={step}
            sensorList={sensorList}
            handleClose={() => handleClose()}
            setIsEditing={setIsEditing}
            setIsMediaPreviewOpen={setIsMediaPreviewOpen}
          />
        )}
        {!openEdit && (
          <>
            <div
              className="flex items-center justify-between px-6 py-1 carouselInnerHeaderContainer"
              style={{
                background: currentMode === "Dark" ? "#161C24" : "#FFFFFF",
                color: currentMode === "Dark" ? "ghostwhite" : "gray",
                width: "100%",
                borderRadius: "10px",
                padding: "1.5rem 1.2rem",
              }}>
              <div className="left flex items-center lex-row">
                <div className="text-xl whitespace-nowrap uppercase font-bold">
                  {data.title}:
                </div>
                <div
                  style={{
                    width: "100%",
                    height: "100%",
                  }}
                  className="text-sm ml-4">
                  {data?.format === "text" ? "" : data.desc}
                </div>
              </div>

              <div className="right flex items-center justify-center">
                <IconButton
                  onClick={() => {
                    setOpenEdit(true);
                    setIsEditing(true);
                  }}
                  ref={focusedItem}>
                  <EditIcon color="primary" style={{fontSize: "20px"}} />
                </IconButton>

                <div className="ml-2"></div>
                <Button onClick={() => setOpenDelete(true)}>
                  <DeleteIcon className="text-xl text-red-600" />
                </Button>
              </div>
            </div>

            <div className="carouselInnerContent" style={{maxHeight: "350px"}}>
              <div
                className="imageContainer flex flex-wrap justify-center items-center"
                style={{
                  height: "350px",
                }}>
                <GetPreview
                  // height="maxHeight"
                  // width="200px"
                  // height="200px"
                  format={data?.format}
                  url={`${dbConfig?.url_storage}/${data?.url}`}
                  desc={data?.desc}
                />
              </div>
            </div>

            <Dialog open={openDelete}>
              <Delete
                onClose={() => setOpenDelete(false)}
                onDelete={handleDelete}
              />
            </Dialog>
          </>
        )}
      </div>
    </div>
  );
};

export default SubStepItem;
