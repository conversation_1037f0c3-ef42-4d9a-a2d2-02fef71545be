import {
  Button,
  Dialog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import { useState } from "react";
import Webcam from "react-webcam";

const videoConstraints = {
  width: 1280,
  height: 720,
  facingMode: "user",
};

const base64ToBlob = (base64, mimeType) => {
  const byteCharacters = atob(base64.split(",")[1]); // Remove base64 header
  const byteNumbers = new Array(byteCharacters.length)
    .fill(0)
    .map((_, i) => byteCharacters.charCodeAt(i));
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
};

/**
 * * Function to handle the file upload
 * * @param {{onSave: (file: File) => void}} props - The file to be uploaded
 */
export const WebcamCaptureModal = ({ onSave = (file) => {} }) => {
  const [open, setOpen] = useState(false);
  const [imageSrc, setImageSrc] = useState(null);
  return (
    <>
      <Button
        variant="outlined"
        onClick={() => setOpen(true)}
        color="primary"
        sx={{ width: "100%", my: 2 }}
        id="file-upload"
      >
        Open Camera
      </Button>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>Camera Capture</DialogTitle>
        <DialogContent>
          {!imageSrc && (
            <Webcam
              audio={false}
              height={720}
              screenshotFormat="image/jpeg"
              width={1280}
              videoConstraints={videoConstraints}
            >
              {({ getScreenshot }) => (
                <Button
                  variant="contained"
                  color="primary"
                  sx={{ width: "100%", my: 2 }}
                  onClick={() => {
                    const imageSrc = getScreenshot();
                    setImageSrc(imageSrc);
                  }}
                >
                  Capture photo
                </Button>
              )}
            </Webcam>
          )}
          {imageSrc && (
            <>
              <img
                src={imageSrc}
                alt="Captured"
                style={{ width: "100%", height: "auto", marginTop: 20 }}
              />
              <Button
                variant="contained"
                color="primary"
                sx={{ width: "100%", my: 2 }}
                onClick={() => {
                  setImageSrc(null);
                }}
              >
                Re-Capture
              </Button>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setOpen(false);
            }}
          >
            Close
          </Button>
          <Button
            onClick={() => {
              if (imageSrc) {
                const blob = base64ToBlob(imageSrc, "image/jpeg");
                const file = new File([blob], "captured-image.jpg", {
                  type: "image/jpeg",
                });

                onSave(file);
                setOpen(false);
              }
            }}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
