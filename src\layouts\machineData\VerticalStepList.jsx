import React, { useState } from "react";
import { List, ListItem, Button } from "@mui/material";

function VerticalStepList({ carousalData }) {
  const [selectedStep, setSelectedStep] = useState(0);

  const handleStepClick = (index) => {
    setSelectedStep(index);
  };

  return (
    <List>
      {carousalData.map((item, index) => (
        <ListItem key={index}>
          <Button
            variant={selectedStep === index ? "contained" : "outlined"}
            color="primary"
            onClick={() => handleStepClick(index)}
          >
            Step {index + 1}
          </Button>
        </ListItem>
      ))}
    </List>
  );
}

export default VerticalStepList;
