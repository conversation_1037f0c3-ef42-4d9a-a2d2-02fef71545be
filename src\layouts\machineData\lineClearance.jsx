import React, { useEffect, useMemo, useState } from "react";
import { useParams, useLocation } from "react-router-dom";
import { dbConfig } from "../../infrastructure/db/db-config";
import {
  useMaintenanceInfo,
  useMaintenanceInfoSeter,
} from "../../context/MaintenanceContext";
import { useStateContext } from "../../context/ContextProvider";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import axios from "axios";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  CircularProgress,
  TextField,
  Typography,
  Button,
  IconButton,
} from "@mui/material";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import TablePagination from "@mui/material/TablePagination";
import SearchOutlined from "@mui/icons-material/SearchOutlined";
import MachineDataHeader from "./MachineDataHeader";
import MaintenanceItem from "./MaintenanceItem";
import AddMaintenance from "./AddMaintenance";
import TableHeader from "./TableHeader";
import NoDataComponent from "../../components/commons/noData.component";
import CommonDropDown from "../../components/commons/dropDown.component";
import { sharedCss } from "../../styles/sharedCss";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import NotAccessible from "../../components/not-accessible/not-accessible";
import { useCheckAccess } from "../../utils/useCheckAccess";

const LineClearance = () => {
  const [open, setOpen] = useState(false);
  const { mid } = useParams();
  const location = useLocation(); // Access the location object
  const [details, setDetails] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [defaultType, setDefaultType] = useState(5);
  const [machineName, setMachineName] = useState("");
  const [hastagsForFilter, setHastagsForFilter] = useState([]);
  const [dataLoading, setDataLoading] = useState(true);
  const [page, setPage] = useState(0); // Pagination state
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const maintenanceInfoFromContext = useMaintenanceInfo();
  const maintenanceInfoSetter = useMaintenanceInfoSeter();
  const { currentColor, currentMode } = useStateContext();
  const { refreshCount } = useMongoRefresh();
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  const commonCss = sharedCss();
  const hasLineClearancePOSTAccess = useCheckAccess("maintenance", "POST");
  const hasLineClearanceGETAccess = useCheckAccess("maintenance", "GET");

  const mainIdFromAlarm = location.state?.mainId || null;
  const expandedByDefault = (data) => mainIdFromAlarm === data._id;
  const handleDropDownValueChange = (event = {}) => {
    const newType = event?.target?.value ?? defaultType;
    setDefaultType(newType);
    maintenanceInfoSetter({ type: newType }); // Update context with new type
    setPage(0); // Reset page on type change
  };

  const fetchAllMaintenance = async () => {
    try {
      const response = await axios.get(
        `${dbConfig.url}/maintenance/getFromMachine/${mid}`,
      );
      const maintenanceData = response?.data?.data || [];
      console.log("Fetched maintenance data:", maintenanceData);
      setDetails([...maintenanceData]);
    } catch (error) {
      console.error("ERROR IN MAINTENANCE:", error.message);
      setDetails([]);
    } finally {
      setDataLoading(false);
    }
  };

  const fetchCurrentMachine = async () => {
    try {
      const response = await axios.get(`${dbConfig.url}/machines/${mid}`);
      setMachineName(response.data?.data?.title || "");
    } catch (error) {
      console.error("ERROR IN FETCHING MACHINE:", error.message);
      setMachineName("");
    }
  };

  // Debounce fetchAllMaintenance to prevent rapid re-fetches
  const debounce = (func, delay) => {
    let timeoutId;
    return (...args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  };

  const debouncedFetchAllMaintenance = debounce(fetchAllMaintenance, 500);

  useEffect(() => {
    console.log("Machine ID (mid):", mid);
    debouncedFetchAllMaintenance();
    fetchCurrentMachine();
  }, [maintenanceInfoFromContext, refreshCount]);

  const handleOnChangeSearchTerm = (e) => {
    setSearchTerm(e.target.value);
    setPage(0); // Reset page on search
  };

  const defaultTypeSetter = (type) => {
    setDefaultType(type);
    maintenanceInfoSetter({});
  };

  const filterLineClearanceDetails = (data) => {
    if (data.type !== defaultType) return false;

    if (searchTerm === "" && hastagsForFilter.length === 0) return true;

    if (searchTerm.startsWith("#") || hastagsForFilter.length > 0) {
      const hasMatchingTag = hastagsForFilter.some(
        (filterData) => filterData.doc_id === data.id,
      );
      if (hasMatchingTag) return true;
    }

    if (data.title.toLowerCase().includes(searchTerm.toLowerCase())) {
      return true;
    }

    return false;
  };

  const filteredData = useMemo(() => {
    return details.filter(filterLineClearanceDetails);
  }, [defaultType, searchTerm, hastagsForFilter, details]);

  const paginatedData = useMemo(() => {
    const startIndex = page * rowsPerPage;
    return filteredData.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredData, page, rowsPerPage]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const isFilteredLineClearanceDetailsAvailable = useMemo(
    () => details.filter(filterLineClearanceDetails).length,
    [defaultType, searchTerm, hastagsForFilter, details],
  );

  return (
    <section>
      <MachineDataHeader />
      <div>
        <div
          className={`${commonCss.sectionContainer} ${commonCss.backgroundLight} border-radius-outer`}
        >
          <div className={commonCss.tableLable}>
            <Typography sx={{ ml: 2 }} fontWeight="bold" variant="h6">
              {defaultType === 5 ? "Line Clearance" : "Maintenance"}
            </Typography>
            <div
              className={commonCss.tableRightContent}
              style={{ display: "flex", alignItems: "center", gap: "1rem" }}
            >
              <div>
                <TextField
                  label="Search"
                  placeholder="Search by name"
                  className={`${commonCss.searchBox} ${commonCss.inputAlignmentFix}`}
                  id="outlined-size-small"
                  size="small"
                  value={searchTerm}
                  onChange={handleOnChangeSearchTerm}
                  InputProps={{
                    startAdornment: (
                      <IconButton>
                        <SearchOutlined />
                      </IconButton>
                    ),
                  }}
                />
              </div>

              <div>
                <CommonDropDown
                  dropDownLabel="Select type"
                  className={`${commonCss.dropDown} ${commonCss.inputAlignmentFix}`}
                  menuData={[
                    {
                      _id: "line_clearance",
                      label: "Line Clearance",
                      value: 5,
                    },
                  ]}
                  menuValue={defaultType}
                  handleChange={handleDropDownValueChange}
                  menuItemDisplay="label"
                  menuItemValue="value"
                />
              </div>

              <div>
                <Button
                  variant="contained"
                  onClick={() => setOpen(true)}
                  disabled={!hasLineClearancePOSTAccess}
                >
                  Add Line Clearance
                </Button>
              </div>
            </div>
          </div>

          {hasLineClearanceGETAccess ? (
            <div>
              <TableContainer
                component={Paper}
                className="table border-radius-inner"
                sx={commonOuterContainerStyle}
              >
                <Table sx={{ minWidth: 650 }}>
                  <TableHeader
                    currentMode={currentMode}
                    columns={[
                      {
                        label: defaultType === 0 ? "Sensor Name" : "Name",
                        align: "left",
                        width: defaultType === 0 ? "20%" : "25%",
                      },
                      { label: "Description", align: "left", width: "25%" },
                      ...(defaultType !== 1
                        ? [{ label: "Period / Cycle", align: "left" }]
                        : []),
                      { label: "Last Done", align: "left" },
                      { label: "Status", align: "center" },
                      { label: "Actions", align: "center" },
                    ]}
                  />
                  <TableBody>
                    {details.length > 0 &&
                    isFilteredLineClearanceDetailsAvailable > 0 ? (
                      paginatedData.map((data, index) => (
                        <MaintenanceItem
                          key={index}
                          data={data}
                          machineName={machineName}
                          defaultType={defaultType}
                          expandedByDefault={expandedByDefault(data)}
                        />
                      ))
                    ) : (
                      <>
                        {console.log("No data rendered:", {
                          details,
                          isFilteredLineClearanceDetailsAvailable,
                        })}
                        <NoDataComponent
                          cellColSpan={6}
                          dataLoading={dataLoading}
                        />
                      </>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              {filteredData.length > 0 && (
                <TablePagination
                  component="div"
                  count={filteredData.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  className={commonCss.tablePagination}
                />
              )}
            </div>
          ) : (
            <NotAccessible />
          )}
        </div>

        <Dialog open={open} fullWidth>
          <DialogTitle
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#212B36", color: "white" }
                : {}
            }
          >
            Add Line Clearance Schedule
          </DialogTitle>
          <DialogContent
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#212B36", color: "white" }
                : {}
            }
          >
            <AddMaintenance
              mid={mid}
              handleClose={() => setOpen(false)}
              machineName={machineName}
              useAt="LineClearance"
            />
          </DialogContent>
        </Dialog>
      </div>
    </section>
  );
};

export default LineClearance;
