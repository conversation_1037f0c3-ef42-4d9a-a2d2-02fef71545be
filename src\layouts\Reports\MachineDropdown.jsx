import React from "react";
import { FormControl, InputLabel, Select, MenuItem } from "@mui/material";

const MachineDropdown = ({ machineId, machineData, handleChangeId }) => {
  return (
    <FormControl
      size="small"
      style={{ width: "200px", marginBottom: ".4rem", marginTop: ".4rem" }}
      variant="outlined"
    >
      <InputLabel>Select Machine</InputLabel>
      <Select
        label="Select Machine"
        value={machineId}
        onChange={handleChangeId}
      >
        <MenuItem value="All">All</MenuItem>
        {machineData?.map((data) => (
          <MenuItem key={data._id} value={data._id}>
            {data.title}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default MachineDropdown;
