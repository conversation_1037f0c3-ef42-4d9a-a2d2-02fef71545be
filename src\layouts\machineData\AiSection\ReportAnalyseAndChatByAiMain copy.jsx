/** 📓
 * Text chat:- 1. Once project is created we are not updating the file for now.
 *             2. We will create more api to update the PDF files if needed.
 *             3. We are fetching particular project based on "name" field, it need to be unique or "_id" need to
 *                be custom one for mongoDb.
 * Image chat:- We are storing image URL from openAi which is valid for 1 hr only.
 * */
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  List,
  ListItem,
  ListItemText,
  TextField,
  InputAdornment,
  Tooltip,
} from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import SendIcon from "@mui/icons-material/Send";
import { PDFDocument } from "pdf-lib";
import { Divider } from "antd";
import { v4 as uuidV4 } from "uuid";
import ImageIcon from "@mui/icons-material/Image";
import ImageDisplay from "./ImageDisplay";
import { toastMessageSuccess } from "../../../tools/toast";
import { ToastContainer } from "react-toastify";
import VideoDisplay from "./VideoDisplay";
import OndemandVideoIcon from "@mui/icons-material/OndemandVideo";

import axios from "axios";
const ReportAnalyseAndChatByAiMain = ({
  handleClose,
  data,
  userName,
  reportName,
  machineName,
  reportType,
}) => {
  const [reportFile, setReportFile] = useState({});
  const [sopFile, setSopFile] = useState({});
  const [projectData, setProjectData] = useState([]);
  const [id, setId] = useState("");
  const [chatHistory, setChatHistory] = useState([]);
  const [query, setQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [loadingForStart, setLoadingForStart] = useState(false);
  const [imageMode, setImageMode] = useState(false);
  const [videoMode, setVideoMode] = useState(false);
  const divRef = useRef(null);

  // console.log("AI component props data:", data)

  const prompts = [
    "What is the SOP number?",
    "What is the objective of the document?",
    "What is the scope of this document?",
    "Who are the responsible persons or teams and what responsibilities do they have?",
    "What are the tools and material required?",
    "Summarize the procedures?",
    "Summarize all procedures along with the duration taken to complete each one",
    "What is the list of procedures?",
  ];

  const scrollToBottom = () => {
    divRef.current.scrollIntoView({ block: "end", behavior: "smooth" });
  };

  const handleReport = (e) => {
    let report = e.target.files[0];
    setReportFile(report);
    console.log("report file:", report);
  };

  const handleSop = (e) => {
    let sop = e.target.files[0];
    setSopFile(sop);
    console.log("Sop file:", sop);
  };

  // Create project
  const addProject = async (uploadedFiles) => {
    console.log("Uploaded files props:", uploadedFiles);
    if (!data?._id || !data?.title || !uploadedFiles) {
      console.log("Id not found. Please try again.");
      return;
    }

    try {
      // Use the filename field in the project data
      const requestData = {
        name: data?._id,
        description: `${data?._id} : ${data?.title}`,
        filenames: [uploadedFiles?.filename], // Use filename field
      };

      // Perform API request to create a new project
      const response = await axios.get(`${dbConfig.url_python}/projects`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        // If successful, fetch updated projects data
        ///fetchProjects();
      } else {
        // Handle error response
        const errorText = await response.text(); // Get the error text
        console.error("Error adding project:", errorText);
        alert("Error adding project. Please try again.");
      }
    } catch (error) {
      console.error("Error adding project:", error);
      alert("Error adding project. Please try again.");
    }
  };

  // Merge and upload PDFs
  const mergeAndUploadPdfs = async (pdf1Bytes, pdf2Bytes) => {
    // Merge PDFs
    const pdfDoc = await PDFDocument.create();
    const pdf1 = await PDFDocument.load(pdf1Bytes);
    const pdf2 = await PDFDocument.load(pdf2Bytes);
    const copiedPages1 = await pdfDoc.copyPages(pdf1, pdf1.getPageIndices());
    const copiedPages2 = await pdfDoc.copyPages(pdf2, pdf2.getPageIndices());
    copiedPages1.forEach((page) => pdfDoc.addPage(page));
    copiedPages2.forEach((page) => pdfDoc.addPage(page));
    const mergedPdfBytes = await pdfDoc.save();
    const mergedPdfBlob = new Blob([mergedPdfBytes], {
      type: "application/pdf",
    });
    // Create FormData
    const formData = new FormData();
    const fileName = data?._id; // maintenance report id
    formData.append("file", mergedPdfBlob, fileName + ".pdf");
    return await axios.post(`${dbConfig.url_python}/upload`, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  };

  /** Prepare the chat. This is the first main function where we setup the files and project in backend
   * 1. Merge and upload PDFs. (it returns filename)
   * 2. Add project
   */
  const startChat = async () => {
    if (!reportFile?.name || !sopFile?.name || loadingForStart) {
      console.log("Please upload both files to start the chat.");
      alert("Please upload both files to start the chat.");
      return;
    }

    setLoadingForStart(true);

    try {
      const pdf1Bytes = await reportFile?.arrayBuffer();
      const pdf2Bytes = await sopFile?.arrayBuffer();
      // Merge and upload PDFs
      const uploadedPdfFile = await mergeAndUploadPdfs(pdf1Bytes, pdf2Bytes);
      // Add project
      await addProject(uploadedPdfFile?.data);
      await fetchProjectByName(data?._id);
      toastMessageSuccess({
        message: "Now ready to chat. Please type your query.",
      });
    } catch (error) {
      console.error("Upload failed:", error);
    } finally {
      setLoadingForStart(false);
    }
  };

  // Fetch project by name i.e data?._id
  const fetchProjectByName = async (projectName) => {
    try {
      const response = await axios.get(
        `${dbConfig.url_python}/project/${projectName}`,
      );
      if (response.statusText) {
        /** response is comming as project : [{project}] */
        const projectData = await response?.data;
        setProjectData(projectData?.project);
        if (projectData?.project?.length) {
          console.log("Project data:", projectData?.project[0]?._id?.$oid);
          setId(projectData?.project[0]?._id?.$oid);
          setChatHistory(projectData?.project[0]?.chat_history || []);
        }
        console.log("Project data2:", projectData);
        return projectData;
      } else {
        console.error("Error fetching project:", response.statusText);
        return null;
      }
    } catch (error) {
      console.error("Error fetching project:", error);
      return null;
    }
  };

  // Handle query change
  const handleQueryChange = (e) => {
    setQuery(e.target.value);
  };

  // handle prompt click
  const handlePromptClick = (prompt) => {
    scrollToBottom();
    setQuery(prompt);
  };

  // Send query
  const handleSendQuery = async (e) => {
    e.preventDefault();
    if (!query) {
      return;
    }
    setLoading(true);
    const textChatUrl = `${dbConfig.url_python}/chat/${id}`;
    const imageChatUrl = `${dbConfig.url_python}/chatImage/${id}`;
    const videoChatUrl = `${dbConfig.url_python}/chatVideo/${id}`;
    //const url = imageMode ? imageChatUrl : textChatUrl;
    function decideUrl() {
      if (videoMode) {
        return videoChatUrl;
      } else if (imageMode) {
        return imageChatUrl;
      } else {
        return textChatUrl;
      }
    }
    const url = decideUrl();

    try {
      const response = await axios.post(
        `${url}`,
        { query },
        { headers: { "Content-Type": "application/json" } }, // Add Content-Type header
      );
      const botResponse = response.data.response;
      setChatHistory([
        ...chatHistory,
        { user: query, bot: botResponse, image: imageMode, video: videoMode },
      ]);
      setQuery("");
    } catch (error) {
      console.error("Error sending query:", error);
      // Handle error (e.g., show a notification to the user)
    } finally {
      setLoading(false);
      scrollToBottom();
    }
  };

  useEffect(() => {
    if (data?._id) {
      fetchProjectByName(data?._id);
    }
  }, []);

  const processedChatHistory = useMemo(() => {
    return chatHistory.map((chat) => ({ ...chat, key: uuidV4() }));
  }, [chatHistory]);

  return (
    <div
      id="divRef"
      ref={divRef}
      style={{ maxHeight: "800", overflowY: "auto" }}
    >
      <form>
        <div className="flex justify-between max-w-2xl mx-auto p-4 border rounded-lg shadow-lg bg-white">
          <div>
            <label
              className="block mb-2 text-sm font-medium text-gray-700"
              htmlFor="file_input"
            >
              Choose Report{" "}
            </label>
            <input
              type="file"
              id="file_input"
              accept="application/pdf"
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
              onChange={handleReport}
            />
          </div>

          <div>
            <label
              className="block mb-2 text-sm font-medium text-gray-700"
              htmlFor="file_input"
            >
              Choose SOP
            </label>
            <input
              type="file"
              id="file_input"
              accept="application/pdf"
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
              onChange={handleSop}
            />
          </div>

          <div className="bg-slate-400 self-center">
            <Button
              color="success"
              onClick={startChat}
              variant="contained"
              disabled={projectData?.length > 0} // || !(projectData?.length === 0 && reportFile && sopFile)
            >
              {loadingForStart && (
                <CircularProgress size="1rem" color="inherit" />
              )}
              {!loadingForStart && <>Start</>}
            </Button>
          </div>
        </div>

        {/** Chat Section */}
        <div>
          <List>
            {processedChatHistory?.map((chat, index) => (
              <React.Fragment key={uuidV4()}>
                {chat?.image && (
                  <>
                    <ListItem>
                      <ListItemText
                        primary={
                          <>
                            <b>USER : </b> {chat.user}
                          </>
                        }
                        secondary={`AR COPILOT: `}
                      />
                    </ListItem>
                    <ImageDisplay
                      imageUrl={chat.bot}
                      title="Image Title"
                      description="This is a brief description of the image."
                    />
                  </>
                )}

                {chat?.video && (
                  <>
                    <ListItem>
                      <ListItemText
                        primary={
                          <>
                            <b>USER : </b> {chat.user}
                          </>
                        }
                        secondary={`AR COPILOT: `}
                      />
                    </ListItem>
                    <VideoDisplay
                      videoUrl={chat.bot}
                      title="Image Title"
                      description="This is a brief description of the image."
                    />
                  </>
                )}

                {/* http://localhost:3009/1709652205322_01.Security%20Interlocks.mp4 */}

                {!chat?.image && !chat?.video && (
                  <ListItem>
                    <ListItemText
                      primary={
                        <>
                          <b>USER : </b> {chat.user}
                        </>
                      }
                      secondary={`AR COPILOT: ${chat.bot}`}
                    />
                  </ListItem>
                )}
                {index < chatHistory.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>

          <div style={{ display: "flex", marginTop: "10px" }}>
            <TextField
              label="Type a message..."
              variant="outlined"
              fullWidth
              value={query}
              onChange={handleQueryChange}
              margin="normal"
              size="small"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start">
                    <IconButton>
                      <Tooltip
                        title={
                          imageMode
                            ? `Image mode`
                            : `Click to enable image mode`
                        }
                        arrow
                      >
                        <ImageIcon
                          onClick={() => setImageMode(!imageMode)}
                          sx={{ color: imageMode ? "green" : `gray` }}
                        />
                      </Tooltip>
                    </IconButton>
                    <IconButton>
                      <Tooltip
                        title={
                          imageMode
                            ? `Video mode`
                            : `Click to enable video mode`
                        }
                        arrow
                      >
                        <OndemandVideoIcon
                          onClick={() => setVideoMode(!videoMode)}
                          sx={{ color: videoMode ? "green" : `gray` }}
                        />
                      </Tooltip>
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <IconButton
              variant="contained"
              color="primary"
              onClick={handleSendQuery}
              disabled={loading || !query}
              type="submit"
            >
              <SendIcon />
            </IconButton>
          </div>
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            marginBottom="10px"
          >
            {prompts.map((prompt, index) => (
              <Box
                key={uuidV4()}
                onClick={() => handlePromptClick(prompt)}
                border={1}
                padding={0.4}
                margin={0.8}
                cursor="pointer"
                textAlign="center"
                type="button"
                sx={{ fontSize: ".6rem", borderRadius: "5px" }}
              >
                {prompt}
              </Box>
            ))}
          </Box>

          {loading && (
            <Box display="flex" justifyContent="center" marginTop="10px">
              <CircularProgress />
            </Box>
          )}

          <div className="p-2 mt-2 flex justify-between">
            <Button color="error" onClick={handleClose} variant="contained">
              Close
            </Button>
            {/* <Button color="error" type="submit" variant="contained">
                    Submit
                </Button> */}
          </div>
        </div>
      </form>
    </div>
  );
};

export default ReportAnalyseAndChatByAiMain;
