import { useState } from "react";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import ChangePasswordForm from "./ChangePasswordForm";
import { useAuth } from "../../hooks/AuthProvider";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import { Typography } from "@mui/material";
import { Box, TextField, Button, useTheme } from "@mui/material";
import { makeStyles } from "@mui/styles";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useCheckAccess } from "../../utils/useCheckAccess";

const useStyles = makeStyles((theme) => ({
  textfieldStyle: {
    "& .MuiOutlinedInput-root": {
      "& fieldset": {
        borderColor: theme.palette.custom.textColor,
      },
      "&:hover fieldset": {
        borderColor: theme.palette.primary.main, // set the hover color based on the disabled prop
      },
      "&.Mui-focused fieldset": {
        borderColor: theme.palette.primary.main, // set the focus color based on the disabled prop
      },
    },
    "& .MuiInputLabel-root": {
      color: theme.palette.custom.textColor, // set the label color based on the disabled prop
    },
    "& .MuiInputBase-input": {
      color: theme.palette.custom.textColor, // set the input text color based on the disabled prop
    },
  },
  passwordContainer: {
    backgroundColor: theme.palette.custom.backgroundForth,
    color: theme.palette.custom.textColor,
    padding: "1.5rem",
    borderRadius: "10px",
  },
  changePasswordTitle: {
    fontSize: "1.5rem",
    marginBottom: "1.5rem",
  },
  passContainerInfo: {
    marginTop: "1.5rem",
  },
  heading: {
    marginBottom: "1rem",
  },
  desc: {
    marginBottom: "1rem",
    "& ul": {
      paddingLeft: "1rem",
      "& li": {
        listStyleType: "disc",
      },
    },
  },
}));

const ChangePassword = () => {
  const { currentUser } = useAuth();
  const [password, setPassword] = useState("");
  const [confirmPass, setConfirmPass] = useState("");
  const { currentMode, currentColorLight } = useStateContext();

  const hasUserPUTAccess = useCheckAccess("users", "PUT");
  const update = async (data) => {
    const id = currentUser._id;
    console.log("currentuser111", currentUser);
    console.log("password ye hai", data);

    setPassword(data);
    console.log("password is", password);
    if (password !== confirmPass) {
      toastMessage({
        message: "Your password doesnt match ! Please try again",
      });
    } else {
      await axios
        .put(`${dbConfig.url}/users/${id}`, { password: password })
        .then(() => {
          toastMessageSuccess({
            message: "Your password has been changed successfully!",
          });

          setPassword("");
          setConfirmPass("");
        });
    }
  };
  const classes = useStyles();
  const styles = {
    title: {
      color: "#3C4048",
      fontSize: "24px",
      fontWeight: "bold",
      mb: 1,
    },
    subText: {
      color: "#6B728E",
      fontSize: "16px",
      fontWeight: "light",
    },
    darkTitle: {
      color: "white",
      fontSize: "24px",
      fontWeight: "bold",
      mb: 1,
    },
    darkSubText: {
      color: "#EFF5F5",
      fontSize: "16px",
      fontWeight: "light",
    },
  };
  const isPasswordEmpty = password.trim() === "";
  const isConfirmPasswordEmpty = confirmPass.trim() === "";

  return (
    <div className={classes.passwordContainer}>
      <div>
        <Typography className={classes.changePasswordTitle} variant="h5">
          Change Password
        </Typography>
      </div>
      <ChangePasswordForm
        password={password}
        setPassword={setPassword}
        confirmPass={confirmPass}
        setConfirmPass={setConfirmPass}
      />

      <div className={classes.passContainerInfo}>
        <div className={classes.heading}>
          <h3>Password requirements</h3>
        </div>
        <div className={classes.desc}>
          <h4>Please follow this guide for a strong password</h4>
          <ul>
            <li>One special characters</li>
            <li>Min 6 characters</li>
            <li>One number (2 are recommended)</li>
            <li>Change it often</li>
          </ul>
        </div>
        <div className="updateBtn">
          {/* <button onClick={update}>update password</button> */}
          <Button
            variant="contained"
            color="primary"
            onClick={() => update(password)}
            disabled={
              isPasswordEmpty || isConfirmPasswordEmpty || !hasUserPUTAccess
            }
          >
            Update Password
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChangePassword;
