import React, { useState } from "react";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const VoiceRecognition = () => {
  const [userId, setUserId] = useState("");
  const [voiceSample, setVoiceSample] = useState(null);
  const [status, setStatus] = useState("");
  const [isRecording, setIsRecording] = useState(false);

  // Function to handle audio recording
  const handleRecordAudio = async () => {
    try {
      setIsRecording(true);
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
      });
      const mediaRecorder = new MediaRecorder(mediaStream);
      const audioChunks = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunks, { type: "audio/wav" });
        setVoiceSample(audioBlob);
        setIsRecording(false);
        setStatus("Audio recorded successfully.");
      };

      mediaRecorder.start();

      setTimeout(() => {
        mediaRecorder.stop();
        mediaStream.getTracks().forEach((track) => track.stop());
      }, 20000); // Record for 5 seconds
    } catch (error) {
      setIsRecording(false);
      setStatus("Failed to record audio.");
      console.error(error);
    }
  };

  // Function to send audio to the server
  const handleApiCall = async (endpoint, successMessage, method = "post") => {
    if (!userId || !voiceSample) {
      const errorMessage = !userId
        ? "User ID is required."
        : "Voice sample is required.";
      toast.error(errorMessage); // Show error toast
      setStatus(errorMessage);
      return;
    }

    try {
      const formData = new FormData();

      // Ensure the voiceSample is properly attached
      if (voiceSample instanceof Blob) {
        formData.append("voiceSample", voiceSample, "voiceSample.wav"); // Add a filename for backend handling
      } else {
        const errorMessage =
          "Invalid voice sample. Please record a valid sample.";
        toast.error(errorMessage); // Show error toast
        setStatus(errorMessage);
        return;
      }

      const response = await axios({
        url: `${dbConfig.url}/users/${userId}/voice/${endpoint}`,
        method,
        data: formData,
        headers: { "Content-Type": "multipart/form-data" },
      });

      if (endpoint === "authenticate") {
        console.log(response.data);
        if (response.data.match) {
          toast.success(successMessage);
          setStatus(successMessage);
        } else {
          toast.error("Voice not authenticated.");
          setStatus("Voice not authenticated.");
        }
      } else {
        toast.success(successMessage); // Show success toast
        setStatus(successMessage);
        console.log(response.data);
      }
    } catch (error) {
      let errorMessage = "API call failed. Check console for details.";
      if (
        error.response &&
        error.response.data &&
        error.response.data.message
      ) {
        errorMessage = error.response.data.message;
      }

      toast.error(errorMessage); // Show error toast
      setStatus(`API call failed: ${errorMessage}`);
      console.error(error);
    }
  };

  return (
    <div
      style={{
        maxWidth: "600px",
        margin: "0 auto",
        padding: "20px",
        textAlign: "center",
      }}
    >
      <h2>Voice Recognition System</h2>
      <div style={{ marginBottom: "20px" }}>
        <label htmlFor="userId" style={{ fontWeight: "bold" }}>
          User ID:
        </label>
        <input
          id="userId"
          type="text"
          value={userId}
          onChange={(e) => setUserId(e.target.value)}
          placeholder="Enter your User ID"
          style={{ marginLeft: "10px", padding: "5px", width: "200px" }}
        />
      </div>

      <button
        onClick={handleRecordAudio}
        style={{
          backgroundColor: isRecording ? "#f44336" : "#4CAF50",
          color: "white",
          padding: "10px 20px",
          border: "none",
          borderRadius: "5px",
          cursor: "pointer",
        }}
        disabled={isRecording}
      >
        {isRecording ? "Recording..." : "Record Voice"}
      </button>

      <div style={{ margin: "20px 0" }}>
        <button
          onClick={() =>
            handleApiCall("create", "Voice registered successfully.")
          }
          style={buttonStyle}
        >
          Register Voice
        </button>
        <button
          onClick={() =>
            handleApiCall("authenticate", "Voice authenticated successfully.")
          }
          style={buttonStyle}
        >
          Identify Voice
        </button>
        <button
          onClick={() =>
            handleApiCall("", "Voice identification removed.", "delete")
          }
          style={buttonStyle}
        >
          Remove Voice
        </button>
      </div>

      {status && (
        <div style={{ marginTop: "20px", color: "blue" }}>{status}</div>
      )}
      <ToastContainer position="top-right" autoClose={3000} />
    </div>
  );
};

const buttonStyle = {
  backgroundColor: "#008CBA",
  color: "white",
  padding: "10px 15px",
  margin: "5px",
  border: "none",
  borderRadius: "5px",
  cursor: "pointer",
};

export default VoiceRecognition;
