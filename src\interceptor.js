import axios from "axios";
import { toast } from "react-toastify";
// import { saveFailedRequest } from './services/db';
// import bugout from './layouts/settings/utilities/logs';

const USER_TOKEN_KEY = "@user-token";
const USER_CREDS_KEY = "@user-creds";
const USER_LDAP_CREDS_KEY = "@user-ldap-creds";
const BLOCK_KEY = "@block";
const LOG_SOURCE = "API Interceptor";
const LOG_LEVEL_INFO = "INFO";
const LOG_LEVEL_ERROR = "ERROR";
const MULTI_DEVICE_ERROR =
  "Your account is being used in another device/Browser. Please login using another tab.";
const ACCOUNT_LOCKED_ERROR = "Account is Locked, please logout";
const MULTI_DEVICE_ALERT =
  "You are currently logged in from another device. Click OK to log out.";
const LOGIN_ROUTE = "/login-mongo";
const UNAUTHORIZED_USER_TEXT = " by an unauthorized user";
const AUTHORIZED_NO_CREDS_TEXT =
  " by an authorized user with missing credentials";
const TOKEN_EXPIRED = "Token Expired, Please Login Again";

let isAlertShown = false;

export function APIInterceptor() {
  axios.interceptors.request.use((request) => {
    const token = JSON.parse(window.sessionStorage.getItem(USER_TOKEN_KEY));
    const decodedUser = JSON.parse(
      window.sessionStorage.getItem(USER_CREDS_KEY),
    );
    const method = request.method;
    const url = request.url;
    const body = request.data;

    const logEntry = {
      timestamp: new Date().toISOString(),
      level: LOG_LEVEL_INFO,
      source: LOG_SOURCE,
      message: `${url} - ${method} request initiated`,
      details: {
        method,
        url,
        status: 200,
        response_time: "", // Not applicable here
        user_agent: navigator.userAgent || "unknown",
      },
    };

    if (token) {
      logEntry.message += decodedUser
        ? ` by ${decodedUser.email} | ${decodedUser._id}`
        : AUTHORIZED_NO_CREDS_TEXT;
    } else {
      logEntry.message += UNAUTHORIZED_USER_TEXT;
    }

    if (body) {
      logEntry.details.request_body = JSON.stringify(body);
    }

    // bugout.log(JSON.stringify(logEntry));

    if (token) {
      request.headers.Authorization = token;
    }

    return request;
  });

  axios.interceptors.response.use(
    (response) => response,
    async (error) => {
      if (
        error.request?.response === `{"message":"${MULTI_DEVICE_ERROR}"}` &&
        !isAlertShown
      ) {
        isAlertShown = true;
        const ok = window.confirm(MULTI_DEVICE_ALERT);
        if (ok) {
          window.location.href = LOGIN_ROUTE;
          sessionStorage.removeItem(USER_CREDS_KEY);
          sessionStorage.removeItem(USER_TOKEN_KEY);
          sessionStorage.removeItem(USER_LDAP_CREDS_KEY);
          sessionStorage.removeItem(BLOCK_KEY);
        }
      }

      if (!error.response || error.response.status >= 500) {
        const { config } = error;
        // await saveFailedRequest({
        //   method: config.method,
        //   url: config.url,
        //   data: config.data,
        //   headers: config.headers,
        //   timestamp: new Date(),
        // });
      }

      const token = JSON.parse(window.sessionStorage.getItem(USER_TOKEN_KEY));
      const decodedUser = JSON.parse(
        window.sessionStorage.getItem(USER_CREDS_KEY),
      );
      const method = error.config.method;
      const url = error.config.url;
      const body = error.config.data;

      const logEntry = {
        timestamp: new Date().toISOString(),
        level: LOG_LEVEL_ERROR,
        source: LOG_SOURCE,
        message: `${url} - ${method} request failed`,
        details: {
          method,
          url,
          status: error.response ? error.response.status : 500,
          response_time: "", // Not applicable here
          user_agent: navigator.userAgent || "unknown",
        },
      };

      if (token) {
        logEntry.message += decodedUser
          ? ` by ${decodedUser.email} | ${decodedUser._id}`
          : AUTHORIZED_NO_CREDS_TEXT;
      } else {
        logEntry.message += UNAUTHORIZED_USER_TEXT;
      }

      if (body) {
        logEntry.details.request_body = JSON.stringify(body);
      }

      // bugout.error(JSON.stringify(logEntry));

      if (
        error.response &&
        error.response.status === 401 &&
        error.response.data.message === TOKEN_EXPIRED
      ) {
        toast.error("Session expired. Please log in again.");
        sessionStorage.removeItem(USER_CREDS_KEY);
        sessionStorage.removeItem(USER_TOKEN_KEY);
        sessionStorage.removeItem(BLOCK_KEY);
        window.location.href = LOGIN_ROUTE;
      } else if (
        error.response &&
        error.response.status === 403 &&
        error.response.data.message === ACCOUNT_LOCKED_ERROR
      ) {
        sessionStorage.removeItem(USER_CREDS_KEY);
        sessionStorage.removeItem(USER_TOKEN_KEY);
        sessionStorage.removeItem(BLOCK_KEY);
        window.location.href = LOGIN_ROUTE;
      } else {
        return Promise.reject(error);
      }
    },
  );
}
