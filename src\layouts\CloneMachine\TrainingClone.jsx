import { Button, Checkbox } from "@mui/material";
import React, { useState, useEffect } from "react";
import { companies, companyId_constant, machines } from "../../constants/data";
import { db } from "../../firebase";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { firebaseLooper } from "../../tools/tool";

const TrainingClone = ({ newMachineId, machineDetails }) => {
  const [openconfirm, setOpenConfirm] = useState(false);
  const [checked, setChecked] = useState(false);
  const [name, setName] = useState("");
  const [fid, setfid] = useState("");
  const [details, setDetails] = useState([]);

  useEffect(() => {
    // db.collection(companies).doc(companyId_constant)
    // .collection('manualData').where('mid', '==', machineDetails.id)
    // .onSnapshot(snap => {
    //     const data = firebaseLooper(snap)
    //     setDetails(data)
    // })
  }, []);

  const clone = () => {
    for (let i in details) {
      //      db.collection(companies).doc(companyId_constant)
      // .collection('manualData').add({...details[i], mid: newMachineId}).then((data) => {
      //     setChecked(true)
      // })
    }
  };

  return (
    <div>
      <Checkbox checked={checked} onClick={clone} />{" "}
      <span>Clone Traininng Details </span>
    </div>
  );
};

export default TrainingClone;
