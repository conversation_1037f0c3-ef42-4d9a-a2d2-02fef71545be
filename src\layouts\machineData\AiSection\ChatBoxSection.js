/** 📓
 * Text chat:- 1. Once project is created we are not updating the file for now.
 *             2. We will create more api to update the PDF files if needed.
 *             3. We are fetching particular project based on "name" field, it need to be unique or "_id" need to
 *                be custom one for mongoDb.
 * Image chat:- We are storing image URL from openAi which is valid for 1 hr only.
 * */
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  List,
  ListItem,
  ListItemText,
  TextField,
  InputAdornment,
  Tooltip,
} from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import SendIcon from "@mui/icons-material/Send";
import { PDFDocument } from "pdf-lib";
import { Divider } from "antd";
import { v4 as uuidV4 } from "uuid";
import ImageIcon from "@mui/icons-material/Image";
import ImageDisplay from "./ImageDisplay";
import { toastMessageSuccess } from "../../../tools/toast";
import { ToastContainer } from "react-toastify";
import VideoDisplay from "./VideoDisplay";
import OndemandVideoIcon from "@mui/icons-material/OndemandVideo";
import MovieFilterIcon from "@mui/icons-material/MovieFilter";
import PhotoFilterIcon from "@mui/icons-material/PhotoFilter";
import { set } from "lodash";

const ChatBoxSection = ({
  handleClose,
  data,
  userName,
  reportName,
  machineName,
  reportType,
  setChatHistory,
  chatHistory,
  id,
  scrollToBottom,
  projectData,
}) => {
  const [query, setQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [loadingForStart, setLoadingForStart] = useState(false);
  const [imageMode, setImageMode] = useState(false);
  const [videoMode, setVideoMode] = useState(false);
  const divRef = useRef(null);

  // console.log("AI component props data:", data)

  const prompts = [
    "What is the SOP number?",
    "What is the objective of the document?",
    "What is the scope of this document?",
    "Who are the responsible persons or teams and what responsibilities do they have?",
    "What are the tools and material required?",
    "Summarize the procedures?",
    "Summarize all procedures along with the duration taken to complete each one",
    "What is the list of procedures?",
  ];

  const handleImageMode = () => {
    setImageMode(!imageMode);
    setVideoMode(false);
  };

  const handleVideoMode = () => {
    setVideoMode(!videoMode);
    setImageMode(false);
  };

  // Handle query change
  const handleQueryChange = (e) => {
    setQuery(e.target.value);
  };

  // handle prompt click
  const handlePromptClick = (prompt) => {
    //scrollToBottom();
    setQuery(prompt);
  };

  // Send query
  const handleSendQuery = async (e) => {
    e.preventDefault();
    if (!query) {
      return;
    }
    setLoading(true);
    const textChatUrl = `${dbConfig.url_python}/chat/${id}`;
    const imageChatUrl = `${dbConfig.url_python}/chatImage/${id}`;
    const videoChatUrl = `${dbConfig.url_python}/chatVideo/${id}`;

    function decideUrl() {
      if (videoMode) {
        return videoChatUrl;
      } else if (imageMode) {
        return imageChatUrl;
      } else {
        return textChatUrl;
      }
    }
    const url = decideUrl();

    try {
      const response = await axios.post(
        `${url}`,
        { query },
        { headers: { "Content-Type": "application/json" } }, // Add Content-Type header
      );
      const botResponse = response.data.response;
      setChatHistory([
        ...chatHistory,
        { user: query, bot: botResponse, image: imageMode, video: videoMode },
      ]);
      setQuery("");
    } catch (error) {
      console.error("Error sending query:", error);
      // Handle error (e.g., show a notification to the user)
    } finally {
      setLoading(false);
      scrollToBottom();
    }
  };

  return (
    <>
      <div style={{ display: "flex", marginTop: "10px" }}>
        <TextField
          label="Type a message..."
          variant="outlined"
          fullWidth
          value={query}
          onChange={handleQueryChange}
          margin="normal"
          size="small"
          InputProps={{
            endAdornment: (
              <InputAdornment position="start">
                <IconButton>
                  <Tooltip
                    title={
                      imageMode ? `Image mode` : `Click to enable image mode`
                    }
                    arrow
                  >
                    <PhotoFilterIcon
                      onClick={() => handleImageMode(!imageMode)}
                      sx={{ color: imageMode ? "green" : `gray` }}
                    />
                  </Tooltip>
                </IconButton>
                <IconButton>
                  <Tooltip
                    title={
                      videoMode ? `Video mode` : `Click to enable video mode`
                    }
                    arrow
                  >
                    <MovieFilterIcon
                      onClick={() => handleVideoMode(!videoMode)}
                      sx={{ color: videoMode ? "green" : `gray` }}
                    />
                  </Tooltip>
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
        <IconButton
          variant="contained"
          color="primary"
          onClick={handleSendQuery}
          disabled={loading || !query || projectData?.length < 1}
          type="submit"
        >
          <SendIcon />
        </IconButton>
      </div>
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        marginBottom="10px"
      >
        {prompts.map((prompt, index) => (
          <Box
            key={uuidV4()}
            onClick={() => handlePromptClick(prompt)}
            border={1}
            padding={0.4}
            margin={0.8}
            cursor="pointer"
            textAlign="center"
            type="button"
            sx={{ fontSize: ".6rem", borderRadius: "5px" }}
          >
            {prompt}
          </Box>
        ))}
      </Box>

      {loading && (
        <Box display="flex" justifyContent="center" marginTop="10px">
          <CircularProgress />
        </Box>
      )}
    </>
  );
};

export default ChatBoxSection;
