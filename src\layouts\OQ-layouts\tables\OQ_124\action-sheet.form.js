import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Icon<PERSON>utton,
  Typo<PERSON>,
} from "@mui/material";
import { Box } from "@mui/system";
import React, { useState } from "react";
import EditIcon from "@mui/icons-material/Edit";
import EditActionSheet from "../../forms/OQ_124/edit-action-sheet.form";

const ActionSheet = ({ actionSheetData, docId }) => {
  const [open, setOpen] = useState(false);

  return (
    <Box sx={{ p: 4 }} className="action-sheet" style={{ width: "1200px" }}>
      <Typography variant="h6" align="left" gutterBottom>
        {actionSheetData?.title}{" "}
        <IconButton size="small">
          {" "}
          <EditIcon onClick={() => setOpen(true)} />{" "}
        </IconButton>
      </Typography>
      <Divider />
      <Box>
        {/* deviation, protocol no */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderBottom: "none",
            alignItems: "center",
          }}
        >
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Deviation No. :</b>
              {actionSheetData?.deviation_no}
            </Typography>
          </Box>
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Protocol Ref :</b> {actionSheetData?.protocol_ref}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderBottom: "none",
            alignItems: "center",
          }}
        >
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Page No. :</b>
              {actionSheetData?.page_no}
            </Typography>
          </Box>
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Item No. :</b> {actionSheetData?.item_no}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderBottom: "none",
          }}
        >
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Deviation Description: :</b>{" "}
            </Typography>
            <Typography>{actionSheetData?.desc}</Typography>
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderBottom: "none",
          }}
        >
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Impact of Deviation :</b>{" "}
            </Typography>
            <Typography>{actionSheetData?.impact}</Typography>
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderBottom: "none",
            alignItems: "center",
          }}
        >
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Tester Approval:</b>
              {actionSheetData?.tester}
            </Typography>
          </Box>
          <Box>
            <Typography>
              <b>
                Signature:{" "}
                <img
                  style={
                    actionSheetData?.sign_tester !== ""
                      ? { width: "150px", height: "150px" }
                      : {}
                  }
                  alt={"signature"}
                  src={actionSheetData.sign_tester}
                />
              </b>{" "}
            </Typography>
          </Box>
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Date:</b>{" "}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderBottom: "none",
          }}
        >
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Corrective Action :</b>{" "}
            </Typography>
            <Typography>{actionSheetData?.action}</Typography>
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderBottom: "none",
            alignItems: "center",
          }}
        >
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Engineering Approval:</b>
              {actionSheetData?.eng_approval}
            </Typography>
          </Box>
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>
                Signature:{" "}
                <img
                  style={
                    actionSheetData?.sign_eng !== ""
                      ? { width: "150px", height: "150px" }
                      : {}
                  }
                  alt={"signature"}
                  src={actionSheetData?.sign_eng}
                />
              </b>{" "}
            </Typography>
          </Box>
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Date:</b>{" "}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderBottom: "none",
          }}
        >
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Corrective Action Carried Out and Complete:</b>{" "}
            </Typography>
            <Typography>{actionSheetData?.result}</Typography>
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderBottom: "none",
            alignItems: "center",
          }}
        >
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Validation Approval:</b>
              {actionSheetData?.valid_approval}
            </Typography>
          </Box>
          <Box>
            <Typography>
              <b>
                Signature:{" "}
                <img
                  style={
                    actionSheetData?.sign_valid !== ""
                      ? { width: "150px", height: "150px" }
                      : {}
                  }
                  alt={"signature"}
                  src={actionSheetData?.sign_valid}
                />
              </b>{" "}
            </Typography>
          </Box>
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Date:</b>{" "}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderBottom: "none",
            backgroundColor: "#CFD2CF",
            height: "35px",
          }}
        ></Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            border: "1px solid black",
          }}
        >
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Customer Approval:</b>
              {actionSheetData?.cust_approval}
            </Typography>
          </Box>
          <Box>
            <Typography>
              <b>
                Signature:{" "}
                <img
                  style={
                    actionSheetData?.sign_cust !== ""
                      ? { width: "150px", height: "150px" }
                      : {}
                  }
                  alt={"signature"}
                  src={actionSheetData?.sign_cust}
                />
              </b>{" "}
            </Typography>
          </Box>
          <Box sx={{ mx: 2 }}>
            <Typography>
              <b>Date:</b>{" "}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Dialog
        maxWidth="lg"
        onClose={() => setOpen(false)}
        open={open}
        fullWidth
      >
        <DialogTitle>Edit details for the Action Sheet</DialogTitle>
        <DialogContent>
          <EditActionSheet actionSheetData={actionSheetData} docId={docId} />
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default ActionSheet;
