import React, { useState, useMemo, useContext } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "../TableTempletes.css";

import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import TextField from "@mui/material/TextField";

import { useStateContext } from "../../../../context/ContextProvider";
import { db, storage } from "../../../../firebase";
import { companies, companyId_constant } from "../../../../constants/data";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../../../components/buttons/Buttons";

import { useStorageTablesFile } from "../../../../utils/useStorageTablesFile";
import { InputLabel, LinearProgress, Link, Typography } from "@mui/material";
import { Empty } from "antd";
import { FileDownload, ImageSearchOutlined } from "@mui/icons-material";
import {
  FileImageFilled,
  FilePdfFilled,
  PictureFilled,
} from "@ant-design/icons";
import { DeleteByUrl } from "../../../../utils/StorageOptions";
import { DropzoneArea } from "material-ui-dropzone";
import { FileManagerSelectorContext } from "../../../../services/fileManager/file-manager-select.context";
import FileSelector from "../../../FileSelector/screens/FileSelector";
import PreviewIcon from "@mui/icons-material/Preview";

export default function OQ9({
  rowData,
  type,
  machineName,
  fatDataDocId,
  useAt,
}) {
  const { currentMode, currentColorLight } = useStateContext();
  const [open, setOpen] = useState(false);
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");

  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useMemo(() => {
    // when theme changes then S.No array got reversed. So to stop that This Memonisation is used.
    setSortDirection("");
    //console.log("TableTemplates usememo")
  }, [currentMode]);

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#161c24" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Shelf SP
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Acceptance Criteria
              </TableCell>
              <TableCell
                sx={{ border: theme.borderDesign }}
                colSpan={3}
                align="center"
              >
                Observation
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                {" "}
                Deviation{" "}
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                PASS/FAIL
              </TableCell>
              {useAt !== "tableMain" && (
                <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                  Image
                </TableCell>
              )}
            </TableRow>

            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }}>
                Shelf No
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Shelf Average
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                All the shelves Average
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  sx={{
                    border: theme.borderDesign,
                    background: currentMode === "Dark" ? "#212b36" : "",
                  }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[2]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[3]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[4]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[5]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[6]}
                  </TableCell>
                  {useAt !== "tableMain" && (
                    <TableCell
                      sx={{ border: theme.borderDesign }}
                      align="center"
                    >
                      {data[7] === null ? (
                        "Add One"
                      ) : (
                        <>
                          {data[7]?.includes(".pdf") ? (
                            <FilePdfFilled
                              className="text-red-700"
                              onContextMenu={() =>
                                (window.location.href = data[7])
                              }
                              title="Press right click to open file"
                            />
                          ) : (
                            <PictureFilled
                              onContextMenu={() =>
                                (window.location.href = data[7])
                              }
                              title="Press right click to open file"
                            />
                          )}
                        </>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
          <EditTableRow
            rowDataSelected={rowDataSelected}
            tableType={"OQ9"}
            type={type}
            fatDataDocId={fatDataDocId}
            machineName={machineName}
            handleClose={handleClose}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}

//////////////////

function EditTableRow({
  rowDataSelected,
  tableType,
  type,
  machineName,
  fatDataDocId,
  handleClose,
}) {
  const { currentMode } = useStateContext();

  const [selfSP, setSelfSP] = useState(rowDataSelected[0]);
  const [acceptance, setAcceptance] = useState(rowDataSelected[1]);
  const [shelfNo, setShelfNo] = useState(rowDataSelected[2]);
  const [shelfAverage, setShelfAverage] = useState(rowDataSelected[3]);
  const [allShelvesAverage, setAllShelvesAverage] = useState(
    rowDataSelected[4],
  );
  const [deviation, setDeviation] = useState(rowDataSelected[5]);
  const [passFail, setPassFail] = useState(rowDataSelected[6]);
  const [urlData, setUrlData] = useState(rowDataSelected[7]);

  const [file, setFile] = useState(null);
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];
  const { fileUrl, changeFileUrl } = useContext(FileManagerSelectorContext);
  const [fType, setFType] = useState("index" in rowDataSelected ? true : false);
  const [index, setIndex] = useState(rowDataSelected[9]);
  //
  const handleChangeImage = (file) => {
    let selectedFile = file[0];
    //console.log(file[0]) //(selectedFile?.size/1024))

    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        // delete last uploaded file via url if image changes
        if (url) {
          DeleteByUrl(url);
        }
        //
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };
  // const { currentColor, currentMode } = useStateContext();
  const { progress, url } = useStorageTablesFile(file);

  const handleUpdateRow = () => {
    if (fType && isNaN(parseInt(index))) {
      toastMessageWarning({ message: "The input of index should be a Number" });
      return;
    }
    let data = {
      selfSP,
      acceptance,
      shelfNo,
      shelfAverage,
      allShelvesAverage,
      deviation,
      passFail,
      index: index,
      url: fileUrl === null ? urlData : fileUrl,
    };
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(type)
    //     .doc(fatDataDocId)
    //     .collection("table" + tableType)
    //     .doc(rowDataSelected[8]) // rowDataSelected[9] = id of the document
    //     .update(data)
    //     .then(() => {
    //         toastMessageSuccess({ message: "Row updated Successfully" });
    //         handleClose();
    //     }).catch((e) => {
    //         toastMessageWarning({ message: "Error ", e });
    //         console.log("OQ9:", e)
    //     })
  };

  const handleCancel = () => {
    if (url) {
      DeleteByUrl(url);
    }
    handleClose();
  };

  const handleDeleteDropZone = (url) => {
    DeleteByUrl(url); // to delete the file from storage
    setFile(null); // to remove the preview
  };
  return (
    <>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          <div className="flex justify-between py-2">
            <div className="w-1/12">
              <TextField
                label="Shelf SP"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={selfSP}
                onChange={(e) => setSelfSP(e.target.value)}
              />
            </div>

            <div className="w-1/12">
              <TextField
                label="Acceptance Criteria"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={acceptance}
                onChange={(e) => setAcceptance(e.target.value)}
              />
            </div>

            <div className="w-2/12">
              <TextField
                label="Shelf No"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={shelfNo}
                onChange={(e) => setShelfNo(e.target.value)}
              />
            </div>

            <div className="w-1/12">
              <TextField
                label="Shelf Average"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={shelfAverage}
                onChange={(e) => setShelfAverage(e.target.value)}
              />
            </div>

            <div className="w-1/12">
              <TextField
                label="All the shelves Average"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={allShelvesAverage}
                onChange={(e) => setAllShelvesAverage(e.target.value)}
              />
            </div>

            <div className="w-1/12">
              <TextField
                label="Deviation"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={deviation}
                onChange={(e) => setDeviation(e.target.value)}
              />
            </div>

            <div className="4/12">
              <TextField
                label="Pass/Fail"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={passFail}
                onChange={(e) => setPassFail(e.target.value)}
              />
            </div>
            {/* {fType */}
            {(url?.includes(".pdf") ||
              fileUrl?.includes(".pdf") ||
              urlData?.includes(".pdf")) && (
              <div className="w-2/12">
                <TextField
                  label="Index"
                  id="outlined-size-small"
                  defaultValue="Na"
                  size="small"
                  value={index}
                  onChange={(e) => setIndex(e.target.value)}
                />
              </div>
            )}
          </div>
        </DialogContentText>
        <FileSelector />
        <Typography sx={{ ml: "20px" }} align="left">
          {" "}
          <PreviewIcon /> Preview
        </Typography>
        <img width="250px" src={urlData} />
      </DialogContent>
      <DialogActions>
        <ButtonBasic buttonTitle="Update" onClick={handleUpdateRow} />
        <ButtonBasicCancel
          buttonTitle="Cancel &#x2716;"
          onClick={handleCancel}
        />
      </DialogActions>
    </>
  );
}
