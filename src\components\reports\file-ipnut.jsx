import React, { useEffect } from "react";
import IconButton from "@mui/material/IconButton";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import { MuiFileInput } from "mui-file-input";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toast } from "react-toastify";
import { WebcamCaptureModal } from "./camera-capture";
import { useCheckAccess } from "../../utils/useCheckAccess";

/**
 * A component that renders a file upload input, but is styled to appear as
 * a button. When clicked, the file input will open the file browser.
 *
 * @param {{children: React.ReactNode,onUploadComplete: (url: string)=>void}} props Props to pass to the input element. This
 * can be used to specify the accept type and other attributes of
 * the input element.
 */
export const FileUpload = ({ children, onUploadComplete }) => {
  const [open, setOpen] = React.useState(false);
  const [file, setFile] = React.useState(null);
  const [uploading, setUploading] = React.useState({
    state: false,
    progress: 0,
  });
  useEffect(() => {
    setFile(null);
  }, [open]);

  const handleFileUpload = async () => {
    if (!file) {
      return;
    }
    setUploading({ state: true, progress: 0 });
    try {
      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${dbConfig.url_storage}/upload`,
        formData,
        {
          onUploadProgress: function (progressEvent) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total,
            );
            setUploading({ state: true, progress: percentCompleted });
          },
        },
      );
      onUploadComplete(response.data.data);
      // return response.data.data;
    } catch (error) {
      console.error("Error uploading file:", error);
      toast.error("Error uploading file");
    } finally {
      setOpen(false);
      setUploading({ state: false, progress: 0 });
      setFile(null);
    }
  };

  const handleFileChange = (newFile) => {
    setFile(newFile);
  };

  const hasChangeOverReportStepPUTAccess = useCheckAccess(
    "changeOverReportSteps",
    "PUT",
  );

  return (
    <>
      <IconButton
        onClick={() => setOpen(true)}
        disabled={!hasChangeOverReportStepPUTAccess}
      >
        {children}
      </IconButton>
      <Dialog open={open} onClose={() => setOpen(false)}>
        <DialogTitle>Upload</DialogTitle>
        <DialogContent>
          <MuiFileInput
            label="Select Image"
            id="file-upload"
            value={file}
            onChange={handleFileChange}
            sx={{ width: "100%", my: 2 }}
            placeholder="Select Image"
            inputProps={{
              placeholder: "Select Image",
              accept: "image/*",
            }}
          />
          <Box>OR</Box>
          <WebcamCaptureModal
            onSave={(file) => {
              setFile(file);
            }}
          />
          {/* Preview */}
          {file && (
            <Box
              component="img"
              src={URL.createObjectURL(file)}
              alt="Preview"
              sx={{
                width: "100%",
                height: "auto",
                marginTop: 2,
                borderRadius: 1,
                boxShadow: 1,
              }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpen(false)}
            color="primary"
            disabled={uploading.state}
            variant="outlined"
          >
            Close
          </Button>
          <Button
            onClick={handleFileUpload}
            disabled={!file || uploading.state}
            variant="contained"
            color="primary"
          >
            Upload{" "}
            {uploading.state && <span>ing... ({uploading.progress}%)</span>}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
