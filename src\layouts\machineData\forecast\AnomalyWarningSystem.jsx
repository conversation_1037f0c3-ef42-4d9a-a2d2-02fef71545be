import React from "react";
import {
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  Box,
} from "@mui/material";

const AnomalyWarningSystem = ({ values, UCL, LCL }) => {
  if (!values || values.length < 5) return null;

  // Calculate Mean and Standard Deviation
  const mean = values.reduce((a, b) => a + b, 0) / values.length;
  const stdDev = Math.sqrt(
    values.map((x) => Math.pow(x - mean, 2)).reduce((a, b) => a + b, 0) /
      values.length,
  );

  // Define Upper & Lower Limits
  const upperThreshold = mean + 2 * stdDev;
  const lowerThreshold = mean - 2 * stdDev;

  // Detect Anomalies
  const anomalies = values.map((value, index) => {
    if (value > UCL)
      return { time: index, value, level: "Critical", color: "red" };
    if (value < LCL)
      return { time: index, value, level: "Critical", color: "red" };
    if (value > upperThreshold)
      return { time: index, value, level: "Warning", color: "orange" };
    if (value < lowerThreshold)
      return { time: index, value, level: "Warning", color: "orange" };
    return { time: index, value, level: "Normal", color: "green" };
  });

  // Filter only warnings and critical anomalies
  const alertAnomalies = anomalies.filter((a) => a.level !== "Normal");

  return (
    <Card sx={{ width: "100%", mt: 4, p: 2 }}>
      <CardContent>
        <Typography variant="h6" align="center" gutterBottom>
          Anomaly Detection Warnings
        </Typography>

        {alertAnomalies.length === 0 ? (
          <Typography variant="body1" align="center" sx={{ color: "green" }}>
            ✅ No anomalies detected. The machine is operating normally.
          </Typography>
        ) : (
          <List>
            {alertAnomalies.map((anomaly, index) => (
              <ListItem
                key={index}
                sx={{ backgroundColor: anomaly.color, color: "white", mb: 1 }}
              >
                <ListItemText
                  primary={`Time: ${anomaly.time}`}
                  secondary={`Value: ${anomaly.value} - ${anomaly.level}`}
                />
              </ListItem>
            ))}
          </List>
        )}
      </CardContent>
    </Card>
  );
};

export default AnomalyWarningSystem;
