import { <PERSON><PERSON>, <PERSON>con<PERSON>utton, LinearProgress, TextField } from "@mui/material";
import React, { useEffect, useState } from "react";
import { firebase<PERSON>ooper } from "../../tools/tool";
import { db } from "../../firebase";
import { companies, companyId_constant } from "../../constants/data";
import { useStorage } from "../../utils/useStorage";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { DropzoneArea } from "material-ui-dropzone";

const VideoFileSendComponent = ({ setOpen }) => {
  const [file, setFile] = useState(null);
  const [fileName, setFileName] = useState("");
  const [call_id, setCallId] = useState("");

  useEffect(() => {
    // db.collection(companies).doc(companyId_constant).collection('CallCurrent').doc('x9Rmo3HXJaLjeLmOF0i2').onSnapshot(snap => {
    //     const data = snap.data()
    //     setCallId(data.call_id)
    // })
  }, []);

  const { progress, url } = useStorage(file);

  const sendFile = () => {
    if (fileName.trim().length === 0)
      return toastMessage({
        message: "Please Fill in correct name for the file",
      });

    if (url === null)
      return toastMessage({
        message:
          "Please wait for file upload / add a new file if not added yet!",
      });

    const data = { fileName, url, call_id };
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection("CallMedia")
    //   .add(data)
    //   .then(() => {
    //     setOpen();
    //     toastMessageSuccess({ message: "File sent Successfully !" });
    //   });
  };

  const handleChange = (e) => {
    let selectedFile = e.target.files[0];
    setFile(selectedFile);
  };

  return (
    <div>
      <label>File Name & File</label>
      <TextField
        onChange={(e) => setFileName(e.target.value)}
        style={{ marginBottom: "10px" }}
        fullWidth
        variant="outlined"
      />

      <input onChange={handleChange} type="file" />
      <div>
        <br />
      </div>
      <LinearProgress variant="determinate" value={progress} />
      <div className="text-xl mt-2 ">UPLOADED : {progress} %</div>

      <br />
      <Button variant="contained" onClick={sendFile}>
        Send File{" "}
      </Button>
    </div>
  );
};

export default VideoFileSendComponent;
