import React, { useEffect, useState } from "react";
import FullCalendar from "@fullcalendar/react"; // must go before plugins
import dayGridPlugin from "@fullcalendar/daygrid"; // a plugin!
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import "./styles.css";
import "./calendar.scss";
import { Box, Typography, Select, MenuItem } from "@mui/material";
import { useStateContext } from "../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import moment from "moment";
import { useMaintenanceInfoSeter } from "../../context/MaintenanceContext";
import {
  useCalenderEventContext,
  useCalenderMaintenanceEventContext,
} from "../../services2/calendar/Calender.context";
import { sharedCss } from "../../styles/sharedCss";
import { makeStyles } from "@mui/styles";
import { EventModal } from "./eventModal";

const useCustomStyles = makeStyles((theme) => ({
  calenderContainer: {
    padding: "1rem",
    borderRadius: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  calenderOuterContainer: {
    width: "100%",
  },
  calenderInnerContainer: {
    display: "flex",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  addButton: {
    width: "max-content",
  },
  calenderPageContainer: {
    padding: "1rem",
    border: "1px solid gainsboro",
  },
}));

const count = {};

export default function CalendarItem() {
  const commonCss = sharedCss();
  const customCss = useCustomStyles();
  let object = {};
  const [eventToShow, setEventToShow] = useState([]);
  const { currentMode, currentColorLight } = useStateContext();
  const [open, setOpen] = useState(false);
  const [openEvent, setOpenEvent] = useState(false);
  const [obj, setObj] = useState({});
  const maintenanceInfoSetter = useMaintenanceInfoSeter();
  const handleDateClick = (arg) => {
    const date = new Date();
    console.log(date.toLocaleDateString());
  };
  const [preview, setPreview] = useState([]);
  const [reportDataForCalendar, setReportDataForCalendar] = useState([]);
  const [eventsCount, setEventsCount] = useState(0);

  const [reportDate, setReportDate] = useState([]);
  const [showOverlay, setShowOverlay] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [currentEvent, setCurrentEvent] = useState([]);
  const events = useCalenderEventContext();
  const [selectedDate, setSelectedDate] = useState(null);
  const [eventCounts, setEventCounts] = useState({});
  const [generatingPDF, setGeneratingPDF] = useState(false);
  const [selectedModule, setSelectedModule] = useState("preventiveMaintenance"); // unique strings //on button click to show selected
  const [selectedModuleTitle, setSelectedModuleTitle] = useState(
    "Total Maintenance Done",
  ); // on calendar event popup
  const maintenanceEvents = useCalenderMaintenanceEventContext();

  //
  const fetchChangeoverDue = async () => {
    setSelectedModuleTitle("Total Change-over due");
    setSelectedModule("changeoverDue");
    await axios
      .get(`${dbConfig.url}/changeover`)
      .then((response) => {
        const tempCalendarEvents = [];
        const tempCalendarDates = [];
        console.log("resonse", response.data);

        response.data?.map((rData, index) => {
          const formattedDate = moment(
            rData?.date,
            //   .split("T")?.[0],
            //   "ddd MMM DD YYYY HH:mm:ss [GMT]ZZ"
          ).format("YYYY-MM-DD");
          tempCalendarEvents?.push({
            _id: rData?._id,
            type: rData?.type,
            title: rData?.title,
            lastdone: rData?.date,
            date: formattedDate,
            report: rData?.report, // Include report
            report_reason: rData?.report_reason, // Include report reason
          });
          tempCalendarDates?.push({
            title: rData?.title,
            date: formattedDate,
          });
        });
        console.log("tempCalendarDates", tempCalendarDates);
        const uniqueReportDate = tempCalendarDates.filter(
          (event, index, self) => {
            return index === self.findIndex((e) => e.date === event.date);
          },
        );

        console.log("processesd:", tempCalendarEvents);
        setReportDataForCalendar([...tempCalendarEvents]);
        setReportDate([...uniqueReportDate]);
        console.log("processesd:", uniqueReportDate);
        //
        const counts = {};
        tempCalendarEvents.forEach((event) => {
          const date = event.date;
          counts[date] = (counts[date] || 0) + 1;
        });

        setEventCounts(counts);
        //
        console.log("data==", eventCounts);
      })
      .catch((error) => {
        console.error(error);
      });
  };
  //
  const fetchMaintenanceReportData = async () => {
    setSelectedModuleTitle("Total Maintenance Report Done");
    setSelectedModule("maintenanceReport");
    await axios
      .get(`${dbConfig.url}/mainReportData`)
      .then((response) => {
        const tempCalendarEvents = [];
        const tempCalendarDates = [];
        console.log("resonse", response.data);

        response.data?.data?.map((rData, index) => {
          const formattedDate = moment(
            rData?.date,
            //   .split("T")?.[0],
            //   "ddd MMM DD YYYY HH:mm:ss [GMT]ZZ"
          ).format("YYYY-MM-DD");
          tempCalendarEvents?.push({
            _id: rData?._id,
            type: rData?.type,
            title: rData?.title,
            lastdone: rData?.last_done,
            date: formattedDate,
            extendedProps: {
              report: rData?.report, // Include report
              report_reason: rData?.report_reason, // Include report reason
            },
          });
          tempCalendarDates?.push({
            title: rData?.title,
            date: formattedDate,
          });
        });
        console.log("tempCalendarDates", tempCalendarDates);
        const uniqueReportDate = tempCalendarDates.filter(
          (event, index, self) => {
            return index === self.findIndex((e) => e.date === event.date);
          },
        );

        console.log("processesd:", tempCalendarEvents);
        setReportDataForCalendar([...tempCalendarEvents]);
        setReportDate([...uniqueReportDate]);
        console.log("processesd:", uniqueReportDate);
        //
        const counts = {};
        tempCalendarEvents.forEach((event) => {
          const date = event.date;
          counts[date] = (counts[date] || 0) + 1;
        });

        setEventCounts(counts);
        //
        console.log("data==", eventCounts);
      })
      .catch((error) => {
        console.error(error);
      });
  };
  const fetchChangeoverReportData = async () => {
    setSelectedModuleTitle("Total Changeover Report Done");
    setSelectedModule("changeoverReport");
    await axios
      .get(`${dbConfig.url}/changeOverReport`)
      .then((response) => {
        const tempCalendarEvents = [];
        const tempCalendarDates = [];
        console.log("response", response.data);

        response.data?.data?.map((rData, index) => {
          const formattedDate = moment(
            rData?.date,
            //   .split("T")?.[0],
            //   "ddd MMM DD YYYY HH:mm:ss [GMT]ZZ"
          ).format("YYYY-MM-DD");
          tempCalendarEvents?.push({
            _id: rData?._id,
            type: rData?.type,
            title: rData?.title,
            lastdone: rData?.last_done,
            date: formattedDate,
            extendedProps: {
              report: rData?.report, // Include report
              report_reason: rData?.report_reason, // Include report reason
            },
          });
          tempCalendarDates?.push({
            title: rData?.title,
            date: formattedDate,
          });
        });
        console.log("tempCalendarDates", tempCalendarDates);
        const uniqueReportDate = tempCalendarDates.filter(
          (event, index, self) => {
            return index === self.findIndex((e) => e.date === event.date);
          },
        );

        console.log("processesd:", tempCalendarEvents);
        setReportDataForCalendar([...tempCalendarEvents]);
        setReportDate([...uniqueReportDate]);
        console.log("processesd:", uniqueReportDate);
        //
        const counts = {};
        tempCalendarEvents.forEach((event) => {
          const date = event.date;
          counts[date] = (counts[date] || 0) + 1;
        });

        setEventCounts(counts);
        //
        console.log("data==", eventCounts);
      })
      .catch((error) => {
        console.error(error);
      });
  };
  const fetchTrainingReportData = async () => {
    setSelectedModuleTitle("Total Training Report Done");
    setSelectedModule("trainingReport");
    await axios
      .get(`${dbConfig.url}/manualReport`)
      .then((response) => {
        const tempCalendarEvents = [];
        const tempCalendarDates = [];
        console.log("resonse", response.data);

        response.data?.data?.map((rData, index) => {
          const formattedDate = moment(
            rData?.date,
            //   .split("T")?.[0],
            //   "ddd MMM DD YYYY HH:mm:ss [GMT]ZZ"
          ).format("YYYY-MM-DD");
          tempCalendarEvents?.push({
            _id: rData?._id,
            type: rData?.type,
            title: rData?.title,
            lastdone: rData?.last_done,
            date: formattedDate,
            extendedProps: {
              report: rData?.report, // Include report
              report_reason: rData?.report_reason, // Include report reason
            },
          });
          tempCalendarDates?.push({
            title: rData?.title,
            date: formattedDate,
          });
        });
        console.log("tempCalendarDates", tempCalendarDates);
        const uniqueReportDate = tempCalendarDates.filter(
          (event, index, self) => {
            return index === self.findIndex((e) => e.date === event.date);
          },
        );

        console.log("processesd:", tempCalendarEvents);
        setReportDataForCalendar([...tempCalendarEvents]);
        setReportDate([...uniqueReportDate]);
        console.log("processesd:", uniqueReportDate);
        //
        const counts = {};
        tempCalendarEvents.forEach((event) => {
          const date = event.date;
          counts[date] = (counts[date] || 0) + 1;
        });

        setEventCounts(counts);
        //
        console.log("data==", eventCounts);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  //
  const fetchMaintenanceDataDue = async (mod = null) => {
    setSelectedModule(mod);
    const type = mod === "routineMaintenance" ? 2 : 3;
    setSelectedModuleTitle(
      type === 2
        ? "Total Routine Maintenance Due"
        : "Total Preventive Maintenance Due",
    );
    await axios
      .get(`${dbConfig.url}/maintenance`)
      .then((response) => {
        const tempCalendarEvents = [];
        const tempCalendarDates = [];
        console.log("resonse", response.data);

        response.data?.data
          ?.filter((d) => d.type === type)
          .forEach((rData, index) => {
            const formattedDate = moment(rData?.dueDate).format("YYYY-MM-DD");
            tempCalendarEvents?.push({
              _id: rData?._id,
              type: rData?.type,
              title: rData?.title,
              lastdone: rData?.dueDate,
              date: formattedDate,
            });
            tempCalendarDates?.push({
              title: rData?.title,
              date: formattedDate,
            });
          });
        console.log("tempCalendarDates", tempCalendarDates);
        const uniqueReportDate = tempCalendarDates.filter(
          (event, index, self) => {
            return index === self.findIndex((e) => e.date === event.date);
          },
        );

        console.log("processesd:", tempCalendarEvents);
        setReportDataForCalendar([...tempCalendarEvents]);
        setReportDate([...uniqueReportDate]);
        console.log("processesd:", uniqueReportDate);
        //
        const counts = {};
        tempCalendarEvents.forEach((event) => {
          const date = event.date;
          counts[date] = (counts[date] || 0) + 1;
        });

        setEventCounts(counts);
        //
        console.log("data==", eventCounts);
      })
      .catch((error) => {
        console.error(error);
      });
  };
  //

  const fetchMaintenanceDataDone = async () => {
    setSelectedModuleTitle("Total Maintenance Done");
    setSelectedModule("maintenanceDone");
    await axios
      .get(`${dbConfig.url}/maintenance`)
      .then((response) => {
        const tempCalendarEvents = [];
        const tempCalendarDates = [];
        console.log("resonse", response.data);

        response.data?.data?.map((rData, index) => {
          const formattedDate = moment(
            rData?.last_done,
            //   .split("T")?.[0],
            //   "ddd MMM DD YYYY HH:mm:ss [GMT]ZZ"
          ).format("YYYY-MM-DD");
          tempCalendarEvents?.push({
            _id: rData?._id,
            type: rData?.type,
            title: rData?.title,
            lastdone: rData?.last_done,
            date: formattedDate,
          });
          tempCalendarDates?.push({
            title: rData?.title,
            date: formattedDate,
          });
        });
        console.log("tempCalendarDates", tempCalendarDates);
        const uniqueReportDate = tempCalendarDates.filter(
          (event, index, self) => {
            return index === self.findIndex((e) => e.date === event.date);
          },
        );

        console.log("processesd:", tempCalendarEvents);
        setReportDataForCalendar([...tempCalendarEvents]);
        setReportDate([...uniqueReportDate]);
        console.log("processesd:", uniqueReportDate);
        //
        const counts = {};
        tempCalendarEvents.forEach((event) => {
          const date = event.date;
          counts[date] = (counts[date] || 0) + 1;
        });

        setEventCounts(counts);
        //
        console.log("data==", eventCounts);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  //
  const fetchCalibrationDue = async () => {
    setSelectedModuleTitle("Total Calibration Due");
    setSelectedModule("calibrationDue");
    await axios
      .get(`${dbConfig.url}/maintenance`)
      .then((response) => {
        const tempCalendarEvents = [];
        const tempCalendarDates = [];
        console.log("resonse", response.data?.data);

        response.data?.data
          ?.filter((fData) => fData?.type == 0)
          ?.map((rData, index) => {
            console.log("cal data:", rData);
            const formattedDate = moment(
              rData?.dueDate,
              //   .split("T")?.[0],
              //   "ddd MMM DD YYYY HH:mm:ss [GMT]ZZ"
            ).format("YYYY-MM-DD");
            tempCalendarEvents?.push({
              _id: rData?._id,
              type: rData?.type,
              title: rData?.title,
              lastdone: rData?.dueDate,
              date: formattedDate,
            });
            tempCalendarDates?.push({
              title: rData?.title,
              date: formattedDate,
            });
          });
        console.log("tempCalendarDates", tempCalendarDates);
        const uniqueReportDate = tempCalendarDates.filter(
          (event, index, self) => {
            return index === self.findIndex((e) => e.date === event.date);
          },
        );

        console.log("processesd:", tempCalendarEvents);
        setReportDataForCalendar([...tempCalendarEvents]);
        setReportDate([...uniqueReportDate]);
        console.log("processesd:", uniqueReportDate);
        //
        const counts = {};
        tempCalendarEvents.forEach((event) => {
          const date = event.date;
          counts[date] = (counts[date] || 0) + 1;
        });

        setEventCounts(counts);
        //
        console.log("data==", eventCounts);
      })
      .catch((error) => {
        console.error(error);
      });
  };
  //

  useEffect(() => {
    // fetchMaintenanceDataDone();
    fetchMaintenanceDataDue("preventiveMaintenance");
  }, []);
  console.log("selectedEvent", selectedEvent);
  const toolBar = {
    start: "prev,next,today",
    center: "title",
    end: "dayGridMonth,timeGridWeek,timeGridDay",
  };
  function renderEventContent(eventInfo) {
    const formattedDate = moment(
      eventInfo.event.start,
      "ddd MMM DD YYYY HH:mm:ss [GMT]ZZ",
    ).format("YYYY-MM-DD");
    console.log("eventcounts", eventCounts);
    const eventCount = eventCounts[formattedDate] || 0;
    console.log("eventinfo1", eventInfo);
    return (
      <>
        <div
          style={{
            backgroundColor:
              selectedModule === "preventiveMaintenance" ||
              selectedModule === "routineMaintenance" ||
              selectedModule === "changeoverDue" ||
              selectedModule === "calibrationDue"
                ? "red"
                : "green",
            padding: "0.2rem",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          Total events: {"  "}
          <span
            style={{
              fontWeight: "bold",
              fontSize: "0.9rem",
              color: "#fdd",
              marginLeft: "0.2rem",
            }}
          >
            {" "}
            {eventCount}{" "}
          </span>{" "}
        </div>
        {/* <Box
          title="Instrument code no."
          sx={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            maxWidth: "100%",
          }}
        >
          Title : {eventInfo.event.title}
        </Box>
        {eventInfo.event.extendedProps.comment && (
          <div
            style={{
              whiteSpace: "nowrap !important",
              overflow: "hidden !important",
              textOverflow: "ellipsis !important",
              maxWidth: "100% !important",
            }}
          >
            Title: {eventInfo.event.extendedProps.comment}
          </div>
        )}
        {eventInfo.event.extendedProps.report_reason && (
          <div
            style={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: "100%",
            }}
          >
            Reason: {eventInfo.event.extendedProps.report_reason}
          </div>
        )} */}
      </>
    );
  }
  function handleEventClick(dateClickInfo) {
    //
    const clickedDate = dateClickInfo.event.start;
    const dateString = clickedDate;
    const dateString2 = clickedDate;
    const date = moment(dateString2, "ddd MMM DD YYYY HH:mm:ss [GMT]ZZ").format(
      "YYYY-MM-DD",
    );
    const eventcount = eventCounts[date];
    console.log("clickedDate", clickedDate);
    setEventsCount(eventcount);

    const eventInfo = reportDataForCalendar?.filter((event) => {
      const eventDate = new Date(event.date + "T00:00:00");

      return (
        eventDate.getFullYear() === clickedDate.getFullYear() &&
        eventDate.getMonth() === clickedDate.getMonth() &&
        eventDate.getDate() === clickedDate.getDate()
      );
    });
    const count = eventInfo.length;
    setSelectedEvent(eventInfo);

    console.log("count", count);
    setSelectedDate(clickedDate);
    setShowOverlay(true);
    console.log("amin", selectedEvent);
  }

  return (
    <div className={customCss.calenderPageContainer}>
      <div>
        {/* <PageHeader title="Calendar" subText="Calendar & Associated Modules" /> */}
        <div
          className={commonCss?.headingContainer}
          style={{
            display: "flex",
            alignItems: "center",
            padding: "0.25rem 0.75rem",
          }}
        >
          <Box>
            <Typography variant="h4">Calendar </Typography>
            <Typography variant="subtitle1">
              Calendar & Associated Modules
            </Typography>
          </Box>
          <Box sx={{ display: "flex", gap: "1rem", marginLeft: "auto" }}>
            <Select
              // label="Process"
              id="process-select"
              sx={{ minWidth: "200px" }}
              value={
                [
                  "changeoverDue",
                  "calibrationDue",
                  "preventiveMaintenance",
                  "routineMaintenance",
                ].includes(selectedModule)
                  ? selectedModule
                  : "%"
              }
              onChange={(e) => {
                const { value } = e.target;
                if (value === "changeoverDue") {
                  fetchChangeoverDue();
                } else if (value === "calibrationDue") {
                  fetchCalibrationDue();
                  // } else if (value === "maintenanceDone") {
                  //   fetchMaintenanceDataDone();
                } else if (value === "preventiveMaintenance") {
                  fetchMaintenanceDataDue(value);
                } else if (value === "routineMaintenance") {
                  fetchMaintenanceDataDue(value);
                }
              }}
            >
              <MenuItem disabled value="%">
                Select Due Process
              </MenuItem>
              <MenuItem value="changeoverDue">Changeover Due</MenuItem>
              <MenuItem value="calibrationDue">Calibration Due</MenuItem>
              <MenuItem value="preventiveMaintenance">
                Preventive Main.
              </MenuItem>
              <MenuItem value="routineMaintenance">Routine Main.</MenuItem>
              {/* <MenuItem value="maintenanceDone">Main. Done</MenuItem>
              <MenuItem value="maintenanceDue">Main. Due</MenuItem> */}
            </Select>
            <Select
              // label="Report"
              id="report-select"
              sx={{ minWidth: "200px" }}
              value={
                [
                  "maintenanceReport",
                  "changeoverReport",
                  "trainingReport",
                ].includes(selectedModule)
                  ? selectedModule
                  : "%"
              }
              onChange={(e) => {
                const { value } = e.target;
                if (value === "maintenanceReport") {
                  fetchMaintenanceReportData();
                } else if (value === "changeoverReport") {
                  fetchChangeoverReportData();
                } else if (value === "trainingReport") {
                  fetchTrainingReportData();
                }
              }}
            >
              <MenuItem value="%" disabled>
                Select Report
              </MenuItem>
              <MenuItem value="maintenanceReport">Maintenance</MenuItem>
              <MenuItem value="changeoverReport">Changeover</MenuItem>
              <MenuItem value="trainingReport">Training</MenuItem>
            </Select>
            {/* <ButtonBasic
              disabled={selectedModule === "changeoverDue"}
              buttonTitle="Changeover Due"
              onClick={() => fetchChangeoverDue()}
            />
            &nbsp;
            <ButtonBasic
              disabled={selectedModule === "calibrationDue"}
              buttonTitle="Calibration Due"
              onClick={() => fetchCalibrationDue()}
            />
            &nbsp;
            <ButtonBasic
              disabled={selectedModule === "maintenanceDone"}
              buttonTitle="Main. Done"
              onClick={() => fetchMaintenanceDataDone()}
            />
            &nbsp;
            <ButtonBasic
              disabled={selectedModule === "maintenanceDue"}
              buttonTitle="Main. Due"
              onClick={() => fetchMaintenanceDataDue()}
            />
            &nbsp;
            <ButtonBasic
              disabled={selectedModule === "maintenanceReport"}
              buttonTitle="Main. Report"
              onClick={() => fetchMaintenanceDatamaintenanceReport()}
            /> */}
          </Box>
        </div>
      </div>
      <p style={{ fontWeight: "bolder", fontSize: "25px" }}></p>

      <div
        style={{
          marginBlock: "1rem",
          padding: "1rem",
          boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
          color: currentMode === "Dark" ? "#fff" : "#000",
        }}
        // className={commonCss.backgroundLight}
      >
        <div>
          <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
            initialView="dayGridMonth"
            events={reportDate}
            eventContent={renderEventContent}
            headerToolbar={toolBar}
            eventClick={handleEventClick}
            dateClick={(arg) => handleDateClick(arg)}
            height="80vh"
          />
        </div>
        {/* {showOverlay && (
          <div className="overlay">
            <div
              style={{
                backgroundColor: "white",
                padding: "1.5rem",
              }}
            >
              <h3
                style={{
                  fontWeight: "bold",
                }}
              >
                {selectedModuleTitle}:{" "}
                <span
                  style={{
                    fontWeight: "bold",
                    fontSize: "1.1rem",
                    color:
                      (selectedModule === "preventiveMaintenance" || selectedModule === "routineMaintenance") ? "red" : "green",
                  }}
                >
                  {eventsCount}{" "}
                </span>{" "}
              </h3>
              {selectedDate && (
                <p>
                  <span
                    style={{
                      fontWeight: "bold",
                    }}
                  >
                    Date:{" "}
                  </span>
                  {selectedDate.toLocaleDateString()}
                </p>
              )}{" "}
              <Divider variant="middle" component="li" />
              <div style={{ margin: "0.5rem 1.5rem" }}>
                <ul>
                  {selectedDate &&
                    selectedEvent.map((data, index) => (
                      <li style={{ listStyle: "disc", display: "flex", alignItems: "center", justifyContent: "space-between" }} key={index}>
                        <span>
                          <span style={{ color: "gray" }}>Title :</span>{" "}
                          {data.title}
                        </span>
                        {['maintenanceReport', 'changeoverReport', 'trainingReport'].includes(selectedModule) && (
                          <IconButton
                            onClick={() => handleDownloadPDF(data._id)}
                            disabled={generatingPDF}
                          >
                            <Tooltip title="Download PDF">
                              {generatingPDF ? (
                                <CircularProgress size="20px" />
                              ) : (
                                <Download style={{ fontSize: "20px", color: "red" }} />
                              )}
                            </Tooltip>
                          </IconButton>
                        )}
                      </li>
                    ))}
                </ul>
              </div>
              <div style={{ display: "flex", justifyContent: "center" }}>
                <Button
                  variant="contained"
                  onClick={() => setShowOverlay(false)}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        )} */}
        <EventModal
          open={showOverlay}
          setOpen={setShowOverlay}
          eventsCount={eventsCount}
          selectedDate={selectedDate}
          selectedEvent={selectedEvent}
          selectedModule={selectedModule}
          selectedModuleTitle={selectedModuleTitle}
        />
      </div>
    </div>
  );
}
