export const MODULES_NAMES_TO_ROUTES = {
  Logs: ["/logs"],
  "Failed Requests": ["/failed_requests"],
  "Audit Trial Configuration": ["/cfrconfig"],
  Configuration: ["/ENV"],
  Messages: ["/admin-messages"],
  Utilities: ["/utilities"],
  "User Accounts": ["/account"],
  Users: ["/users"],
  Settings: ["/settings", "/db-backupAndRestore"],
  // 'Planner Settings' : ['/settings'],
  "Audit Trails": ["/cfr"],
  Dashboard: ["", "/"],
  "Instrument List": ["/instruments", "/eq_master"],
  "Instrument Department": ["/instrument_departments"],
  "Addendum Instruments": ["/addendum"],
  "Decommissioned Instruments": ["/removeins"],
  Calendar: ["/calendar"],
  "Calibration Schedule": ["/calscheli", "/stdcal<PERSON><PERSON>"],
  "My Tasks": ["/jobs", "/stdjobs"],
  "Live Calibration": ["/reports", "/external"],
  "Instrument Request": ["/requests"],
  "Calibration Reports": ["/reports?live=false", "/external?live=false"],
  "Test Points": ["/set_points"],
};

export const validateQnnPattern = (qnn) => {
  const response = {
    isValid: false,
    text: "",
    style: {
      color: "rgba(0, 0, 0, 0.6)",
      fontWeight: "normal",
    },
  };

  if (!qnn) {
    response.isValid = true; // we won't show the error message here, because it's an empty string
    response.text = "";
    return response;
  }

  const trimmedQnn = qnn.trim();

  if (trimmedQnn.length === 0) {
    response.isValid = true; // we won't show the error message here, because it's an empty string
    response.text = "";
    return response;
  }

  if (trimmedQnn.length > 100) {
    response.isValid = false;
    response.text = "Invalid (QNN cannot exceed 100 characters)";
    return response;
  }

  const qnnPatterns = {
    capa: /^(CAPA([-][A-Z0-9]{3}[-][A-Z0-9]{2}[-]\d{4})|(CAPA[/][A-Z0-9]{3}[/][A-Z0-9]{2}[/]\d{4}))$/,
    cc: /^(CC([-][A-Z0-9]{3}[-][A-Z0-9]{2}[-]\d{4})|(CC[/][A-Z0-9]{3}[/][A-Z0-9]{2}[/]\d{4}))$/,
    dev: /^(DEV([-][A-Z0-9]{3}[-][A-Z0-9]{2}[-]\d{4})|(DEV[/][A-Z0-9]{3}[/][A-Z0-9]{2}[/]\d{4}))$/,
    mcc: /^(MCC([-][A-Z0-9]{3}[-][A-Z0-9]{2}[-]\d{4})|(AAA[/]MDEV[/][A-Z0-9]{3}[/]\d{2}[/][A-Z0-9]{3}))$/,
    wo: /^WO[/][A-Z0-9]{2}[/][A-Z0-9]{2}[/][A-Z0-9]{4}[/]\d{2}$/,
    others: /^.{1,100}$/,
  };

  const qnnType = Object.keys(qnnPatterns).find((type) =>
    trimmedQnn.startsWith(type.toUpperCase()),
  );
  if (qnnType) {
    response.isValid = qnnPatterns[qnnType].test(trimmedQnn);
    response.text =
      qnnType.toUpperCase() + " - " + (response.isValid ? "Valid" : "Invalid");

    response.style.color = response.isValid ? "rgba(0, 0, 0, 0.6)" : "#d32f2f";

    return response;
  } else {
    // response.isValid = qnnPatterns.others.test(trimmedQnn);
    // response.text = (response.isValid ? "Caution! Other Valid Type" : "Caution! Other Invalid Type");

    // We are already handling the case of trimmedQnn.length > 100
    response.isValid = true;
    response.text = "Caution! Other Valid Type";
    response.style.color = "#d32f2f";
    response.style.fontWeight = "bold";

    return response;
  }
};
