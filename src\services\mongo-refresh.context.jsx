import React, { useState, useContext, createContext } from "react";

const MongoRefreshContext = createContext();

export function useMongoRefresh() {
  return useContext(MongoRefreshContext);
}

const MongoRefreshProvider = ({ children }) => {
  const [refreshCount, setRefreshCount] = useState(0);

  return (
    <MongoRefreshContext.Provider value={{ refreshCount, setRefreshCount }}>
      {children}
    </MongoRefreshContext.Provider>
  );
};

export default MongoRefreshProvider;
