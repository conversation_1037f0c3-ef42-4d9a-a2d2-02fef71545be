/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable no-script-url */
import React from "react";

const FATLogin = ({ otp, setOtp, handleLogin }) => {
  return (
    <section className="bg-[#F4F7FF] w-full h-full py-20 lg:py-[120px]">
      <div className="container">
        <div className="flex flex-wrap -mx-4">
          <div className="w-full px-4">
            <div
              className="
               max-w-[525px]
               mx-auto
               text-center
               bg-white
               rounded-lg
               relative
               overflow-hidden
               py-16
               px-10
               sm:px-12
               md:px-[60px]
               "
            >
              <div className="mb-10 md:mb-16 text-center">
                <a
                  href="javascript:void(0)"
                  className="inline-block text-xl font-bold max-w-[250px] mx-auto"
                >
                  Accept Approval Request
                </a>
              </div>
              <form>
                <div className="mb-6">
                  <input
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                    placeholder="OTP"
                    className="
                        w-full
                        rounded-md
                        border
                        bordder-[#E9EDF4]
                        py-3
                        px-5
                        bg-[#FCFDFE]
                        text-base text-body-color
                        placeholder-[#ACB6BE]
                        outline-none
                        focus-visible:shadow-none
                        focus:border-primary
                        "
                  />
                </div>

                <div className="mb-10">
                  <input
                    onClick={handleLogin}
                    value="Continue"
                    className="
                        w-full
                        rounded-md
                        border
                        bordder-primary
                        py-3
                        px-5
                        bg-sky-600
                        text-base text-white
                        cursor-pointer
                        hover:bg-opacity-90
                        transition
                        "
                  />
                </div>
              </form>
              <Circles />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FATLogin;

export const Circles = () => {
  return (
    <div>
      <span className="absolute top-1 right-1">
        <svg
          width="40"
          height="40"
          viewBox="0 0 40 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="1.39737"
            cy="38.6026"
            r="1.39737"
            transform="rotate(-90 1.39737 38.6026)"
            fill="#3056D3"
          />
          <circle
            cx="1.39737"
            cy="1.99122"
            r="1.39737"
            transform="rotate(-90 1.39737 1.99122)"
            fill="#3056D3"
          />
          <circle
            cx="13.6943"
            cy="38.6026"
            r="1.39737"
            transform="rotate(-90 13.6943 38.6026)"
            fill="#3056D3"
          />
          <circle
            cx="13.6943"
            cy="1.99122"
            r="1.39737"
            transform="rotate(-90 13.6943 1.99122)"
            fill="#3056D3"
          />
          <circle
            cx="25.9911"
            cy="38.6026"
            r="1.39737"
            transform="rotate(-90 25.9911 38.6026)"
            fill="#3056D3"
          />
          <circle
            cx="25.9911"
            cy="1.99122"
            r="1.39737"
            transform="rotate(-90 25.9911 1.99122)"
            fill="#3056D3"
          />
          <circle
            cx="38.288"
            cy="38.6026"
            r="1.39737"
            transform="rotate(-90 38.288 38.6026)"
            fill="#3056D3"
          />
          <circle
            cx="38.288"
            cy="1.99122"
            r="1.39737"
            transform="rotate(-90 38.288 1.99122)"
            fill="#3056D3"
          />
          <circle
            cx="1.39737"
            cy="26.3057"
            r="1.39737"
            transform="rotate(-90 1.39737 26.3057)"
            fill="#3056D3"
          />
          <circle
            cx="13.6943"
            cy="26.3057"
            r="1.39737"
            transform="rotate(-90 13.6943 26.3057)"
            fill="#3056D3"
          />
          <circle
            cx="25.9911"
            cy="26.3057"
            r="1.39737"
            transform="rotate(-90 25.9911 26.3057)"
            fill="#3056D3"
          />
          <circle
            cx="38.288"
            cy="26.3057"
            r="1.39737"
            transform="rotate(-90 38.288 26.3057)"
            fill="#3056D3"
          />
          <circle
            cx="1.39737"
            cy="14.0086"
            r="1.39737"
            transform="rotate(-90 1.39737 14.0086)"
            fill="#3056D3"
          />
          <circle
            cx="13.6943"
            cy="14.0086"
            r="1.39737"
            transform="rotate(-90 13.6943 14.0086)"
            fill="#3056D3"
          />
          <circle
            cx="25.9911"
            cy="14.0086"
            r="1.39737"
            transform="rotate(-90 25.9911 14.0086)"
            fill="#3056D3"
          />
          <circle
            cx="38.288"
            cy="14.0086"
            r="1.39737"
            transform="rotate(-90 38.288 14.0086)"
            fill="#3056D3"
          />
        </svg>
      </span>
      <span className="absolute left-1 bottom-1">
        <svg
          width="29"
          height="40"
          viewBox="0 0 29 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="2.288"
            cy="25.9912"
            r="1.39737"
            transform="rotate(-90 2.288 25.9912)"
            fill="#3056D3"
          />
          <circle
            cx="14.5849"
            cy="25.9911"
            r="1.39737"
            transform="rotate(-90 14.5849 25.9911)"
            fill="#3056D3"
          />
          <circle
            cx="26.7216"
            cy="25.9911"
            r="1.39737"
            transform="rotate(-90 26.7216 25.9911)"
            fill="#3056D3"
          />
          <circle
            cx="2.288"
            cy="13.6944"
            r="1.39737"
            transform="rotate(-90 2.288 13.6944)"
            fill="#3056D3"
          />
          <circle
            cx="14.5849"
            cy="13.6943"
            r="1.39737"
            transform="rotate(-90 14.5849 13.6943)"
            fill="#3056D3"
          />
          <circle
            cx="26.7216"
            cy="13.6943"
            r="1.39737"
            transform="rotate(-90 26.7216 13.6943)"
            fill="#3056D3"
          />
          <circle
            cx="2.288"
            cy="38.0087"
            r="1.39737"
            transform="rotate(-90 2.288 38.0087)"
            fill="#3056D3"
          />
          <circle
            cx="2.288"
            cy="1.39739"
            r="1.39737"
            transform="rotate(-90 2.288 1.39739)"
            fill="#3056D3"
          />
          <circle
            cx="14.5849"
            cy="38.0089"
            r="1.39737"
            transform="rotate(-90 14.5849 38.0089)"
            fill="#3056D3"
          />
          <circle
            cx="26.7216"
            cy="38.0089"
            r="1.39737"
            transform="rotate(-90 26.7216 38.0089)"
            fill="#3056D3"
          />
          <circle
            cx="14.5849"
            cy="1.39761"
            r="1.39737"
            transform="rotate(-90 14.5849 1.39761)"
            fill="#3056D3"
          />
          <circle
            cx="26.7216"
            cy="1.39761"
            r="1.39737"
            transform="rotate(-90 26.7216 1.39761)"
            fill="#3056D3"
          />
        </svg>
      </span>
    </div>
  );
};
