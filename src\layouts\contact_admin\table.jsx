import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  Button,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  TextField,
  FormControl,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
} from "@mui/material";
import Pagination from "../../components/ui/pagination";
import { HourglassEmptyOutlined } from "@mui/icons-material";
import moment from "moment";
import axios from "axios";
import { dbUrl } from "../../constants/db";
import { toast } from "react-toastify";
import { useAuthCurrentuser } from "../../hooks/AuthProvider";
import AcceptRejectAlertModal from "../../components/ui/alert-ui";
import { getAllContactAdminMessages } from "../../services/contact-admin";
import { formMatrix } from "../login/contact-form";
import PrevNextPagination from "../../components/ui/prev-next-pagination";
import { useUtils } from "../../hooks/UtilsProvider";

import PropTypes from "prop-types";

const filtersColumnsKeyLabel = {
  All: "All",
  email: "Email",
  subject: "Subject",
  message: "Message",
  status: "Status",
};

const INITIAL_FILTERS = {
  searchTerm: "",
  selectedOption: ["All"],
};

const FILTER_LABEL = "Filter";
const SEARCH_LABEL = "Search";
const SEARCH_PLACEHOLDER = "Search...";
const SL_NO_LABEL = "Sl. No.";
const EMAIL_NAME_LABEL = "Email/Name";
const SUBJECT_LABEL = "Subject";
const MESSAGE_LABEL = "Message";
const REQUEST_DATA_LABEL = "Request Data";
const DATE_LABEL = "Date";
const STATUS_LABEL = "Status";
const ACTIONS_LABEL = "Actions";
const NO_DATA_MESSAGE = "No Data";
const CONFIRM_DESC = "Confirm admin request";

const APPROVE_LABEL = "Approve";
const REJECT_LABEL = "Reject";
const COMPLETE_LABEL = "Complete";
const ERROR_MESSAGE = "Error Sending Message.";
const SUCCESS_MESSAGE = "Request successfully.";

const ALL_LABEL = "All";

const MyTableRow = ({ message, index, selectRequest }) => {
  const currentuser = useAuthCurrentuser();

  return (
    <TableRow key={message._id}>
      <TableCell>
        <Typography variant="body2">{index + 1}</Typography>
      </TableCell>
      <TableCell>
        <Typography variant="body2">{message.email}</Typography>
      </TableCell>
      <TableCell>
        <Typography variant="body2">
          {(message.subject ?? "")
            .split("_")
            .map((s) => s.charAt(0).toUpperCase() + s.slice(1))
            .join(" ")}
        </Typography>
      </TableCell>
      <TableCell>
        <Typography
          // sx={openReadMore ? null  lineClamp}
          variant="body2"
        >
          {message.message}
          {/* <span
            style={{ cursor: 'pointer', color: 'blue' }}
            onClick={() => setOpenReadMore(!openReadMore)}
          >
            Read {openReadMore ? 'Less' : 'More'}
          </span> */}
        </Typography>
      </TableCell>
      <TableCell>
        <Box>
          {(formMatrix[message.type] ?? []).map((f) => {
            const label = (f ?? "")
              .split("_")
              .map((s) => s.charAt(0).toUpperCase() + s.slice(1))
              .join(" ");
            return (
              <Typography key={f}>
                <span style={{ fontWeight: "500" }}>{label}</span>:{" "}
                {JSON.parse(message.request_JSON)[f]}
              </Typography>
            );
          })}
        </Box>
      </TableCell>
      <TableCell sx={{ minWidth: "6rem" }}>
        <Typography variant="body2">
          {message.created_at
            ? moment(message.created_at).format("DD-MM-YYYY hh:mm A")
            : "N/A"}
        </Typography>
      </TableCell>
      <TableCell sx={{ minWidth: "6rem" }}>
        <Typography variant="body2">
          {(message.status ?? "")
            .split("_")
            .map((s) => s.charAt(0).toUpperCase() + s.slice(1))
            .join(" ")}
        </Typography>
      </TableCell>
      {currentuser && currentuser.role === "approver" && (
        <TableCell>
          {message.status === "ready_for_approval" && (
            <Box
              sx={{
                display: "flex",
                gap: "8px",
                alignItems: "center",
              }}
            >
              <Button
                type="submit"
                variant="contained"
                color="success"
                // sx={{ margin: ".5rem 1rem " }}
                onClick={(e) => selectRequest(e, message, "approved")}
              >
                {APPROVE_LABEL}
              </Button>

              <Button
                type="submit"
                variant="contained"
                color="error"
                // sx={{ margin: ".5rem 1rem " }}
                onClick={(e) => selectRequest(e, message, "rejected")}
              >
                {REJECT_LABEL}
              </Button>
            </Box>
          )}

          {message.status !== "ready_for_approval" && "-"}
        </TableCell>
      )}

      {currentuser && currentuser.role === "admin" && (
        <TableCell>
          {message.status === "ready_for_approval" &&
            currentuser.role === "admin" &&
            "-"}
          {(message.status === "rejected" || message.status === "completed") &&
            "-"}
          {message.status === "approved" && (
            <Box
              sx={{
                display: "flex",
                gap: "8px",
                alignItems: "center",
              }}
            >
              <Button
                type="submit"
                variant="contained"
                color="success"
                // sx={{ margin: ".5rem 1rem " }}
                onClick={(e) => selectRequest(e, message, "completed")}
              >
                {COMPLETE_LABEL}
              </Button>

              <Button
                type="submit"
                variant="contained"
                color="error"
                // sx={{ margin: ".5rem 1rem " }}
                onClick={(e) => selectRequest(e, message, "rejected")}
              >
                {REJECT_LABEL}
              </Button>
            </Box>
          )}
        </TableCell>
      )}
    </TableRow>
  );
};
MyTableRow.propTypes = {
  message: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    email: PropTypes.string,
    subject: PropTypes.string,
    message: PropTypes.string,
    type: PropTypes.string,
    status: PropTypes.string,
    created_at: PropTypes.string,
    request_JSON: PropTypes.string,
  }),
  index: PropTypes.number,
  selectRequest: PropTypes.func.isRequired,
};
const ContactAdminTable = () => {
  const { envData } = useUtils();
  const INITIAL_PAGINATION = {
    currentPage: 1,
    itemsPerPage: envData.ROW_PER_PAGE,
  };
  const currentuser = useAuthCurrentuser();
  const [openConfirm, setOpenConfirm] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState([]);
  const [filteredMessages, setFilteredMessages] = useState([]);
  const [filters, setFilters] = useState(INITIAL_FILTERS);
  const [pagination, setPagination] = useState(INITIAL_PAGINATION);

  const handleChangeFilters = (key, val) => {
    setFilters({ ...filters, [key]: val });
  };

  const handleSearchFilterChange = (event) => {
    const newValue = event.target.value;
    if (filters.selectedOption.includes("All") && newValue.length > 0) {
      setFilters({
        ...filters,
        selectedOption: newValue.filter((value) => value !== "All"),
      });
    } else {
      if (newValue.includes("All") || newValue.length === 0) {
        setFilters({
          ...filters,
          selectedOption: ["All"],
        });
      } else {
        setFilters({
          ...filters,
          selectedOption: newValue,
        });
      }
    }
  };

  const filterMessages = useCallback((data, filters) => {
    const searchValue = filters.searchTerm?.trim()?.toUpperCase();
    return data.filter((item) => {
      if (filters.selectedOption?.includes("All")) {
        return Object.keys(item).some((field) => {
          const fieldValue = item[field];
          const stringValue = String(fieldValue);
          return stringValue.toUpperCase().includes(searchValue);
        });
      } else {
        const fields = filters.selectedOption;
        return fields.some((field) => {
          const fieldValue = item[field];
          const stringValue = String(fieldValue);
          return stringValue.toUpperCase().includes(searchValue);
        });
      }
    });
  }, []);

  useEffect(() => {
    getAllContactAdminMessages(setMessages, setLoading);
  }, []);

  useEffect(() => {
    const filteredData = filterMessages(messages, filters);
    setFilteredMessages(filteredData);
    setPagination(INITIAL_PAGINATION);
  }, [filters, messages, filterMessages]);

  const changeStatus = async (message, status) => {
    try {
      await axios.put(`${dbUrl}/contactadmin/${message._id}`, {
        status: status,
      });
      toast.success(SUCCESS_MESSAGE);
    } catch (error) {
      toast.error(ERROR_MESSAGE);
    } finally {
      setSelectedRequest(null);
      setSelectedStatus(null);
      setOpenConfirm(false);
      getAllContactAdminMessages(setMessages, setLoading);
    }
  };

  const selectRequest = (e, message, status) => {
    e.preventDefault();
    setOpenConfirm(true);
    setSelectedRequest(message);
    setSelectedStatus(status);
  };

  return (
    <Box boxShadow={2} padding={2}>
      <Box
        sx={{
          marginBlock: "1rem",
          display: "flex",
          gap: "1rem",
          justifyContent: "flex-end",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-end",
            gap: "16px",
            width: "456px",
          }}
        >
          <span
            style={{
              fontWeight: "700",
              fontSize: "15px",
              whiteSpace: "nowrap",
            }}
          >
            {FILTER_LABEL}
          </span>
          <FormControl size="small" sx={{ width: "100%" }}>
            <Select
              labelId="filterDropdownLabel"
              id="filterDropdown"
              multiple
              value={filters.selectedOption}
              onChange={handleSearchFilterChange}
              size="small"
              renderValue={(selected) => {
                const selectedLabels = selected.map(
                  (value) => filtersColumnsKeyLabel[value],
                );
                return selectedLabels.join(", ");
              }}
            >
              {Object.keys(filtersColumnsKeyLabel).map((key) => (
                <MenuItem key={key} value={key}>
                  <Checkbox checked={filters.selectedOption.includes(key)} />
                  <ListItemText primary={filtersColumnsKeyLabel[key]} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
        <TextField
          label={SEARCH_LABEL}
          placeholder={SEARCH_PLACEHOLDER}
          size="small"
          value={filters.searchTerm}
          onChange={(e) => handleChangeFilters("searchTerm", e.target.value)}
          style={{ width: "30rem" }}
        />
      </Box>
      <Box>
        <PrevNextPagination
          currentPage={pagination.currentPage}
          itemsPerPage={pagination.itemsPerPage}
          totalPages={Math.ceil(
            (filteredMessages ?? []).length / pagination.itemsPerPage,
          )}
          totalRows={(filteredMessages ?? []).length}
          setCurrentPage={(page) =>
            setPagination({ ...pagination, currentPage: page })
          }
        />
        <TableContainer
          component={Paper}
          sx={{
            // height: "100%",
            height: "calc(100vh - 304px)",
          }}
        >
          <Table>
            <TableHead
              sx={{
                backgroundColor: "#f8f9fa",
                color: "black",
                fontWeight: "bold",
                fontSize: "0.9rem",
                minWidth: "6rem",
                borderBottom: "2px solid lightslategray",
              }}
            >
              <TableRow>
                <TableCell>{SL_NO_LABEL}</TableCell>
                <TableCell>{EMAIL_NAME_LABEL}</TableCell>
                <TableCell>{SUBJECT_LABEL}</TableCell>
                <TableCell>{MESSAGE_LABEL}</TableCell>
                <TableCell>{REQUEST_DATA_LABEL}</TableCell>
                <TableCell>{DATE_LABEL}</TableCell>
                <TableCell>{STATUS_LABEL}</TableCell>
                {currentuser &&
                  (currentuser.role === "approver" ||
                    currentuser.role === "admin") && (
                    <TableCell sx={{ minWidth: "6rem" }}>
                      {ACTIONS_LABEL}
                    </TableCell>
                  )}
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredMessages
                .slice(
                  (pagination.currentPage - 1) * pagination.itemsPerPage,
                  pagination.currentPage * pagination.itemsPerPage,
                )
                .map((message, index) => (
                  <MyTableRow
                    key={message._id}
                    index={
                      index +
                      (pagination.currentPage - 1) * pagination.itemsPerPage
                    }
                    message={message}
                    selectRequest={selectRequest}
                  />
                ))}
            </TableBody>
          </Table>
          {(filteredMessages.length === 0 || loading) && (
            <TableRow
              sx={{
                display: "flex",
                flexDirection: "column",
                width: "100%",
                justifyContent: "center",
                alignItems: "center",
                height: "320px",
              }}
            >
              {!loading ? (
                <>
                  <HourglassEmptyOutlined color="error" fontSize="large" />
                  <Typography>{NO_DATA_MESSAGE}</Typography>
                </>
              ) : (
                <>
                  <CircularProgress color="secondary" />
                </>
              )}
            </TableRow>
          )}
        </TableContainer>
      </Box>
      {/* <Box
        sx={{
          px: 3,
          py: 1,
          border: "1px solid #e0e0e0",
          display: "flex",
          justifyContent: "center",
        }}
      >
        <Pagination
          totalRows={filteredMessages.length}
          pagination={pagination}
          setPagination={setPagination}
        />
      </Box> */}
      <AcceptRejectAlertModal
        open={openConfirm}
        setOpen={setOpenConfirm}
        handleAccept={() => {
          changeStatus(selectedRequest, selectedStatus);
        }}
        handleReject={() => {
          setSelectedRequest(null);
          setSelectedStatus(null);
          setOpenConfirm(false);
        }}
        desc={CONFIRM_DESC}
        pending={false}
        hideRemark={false}
        hideQnn={true}
      />
    </Box>
  );
};

ContactAdminTable.propTypes = {
  filters: PropTypes.shape({
    searchTerm: PropTypes.string,
    selectedOption: PropTypes.arrayOf(PropTypes.string),
  }),
  pagination: PropTypes.shape({
    currentPage: PropTypes.number.isRequired,
    itemsPerPage: PropTypes.number.isRequired,
  }),
  messages: PropTypes.arrayOf(
    PropTypes.shape({
      _id: PropTypes.string.isRequired,
      email: PropTypes.string,
      subject: PropTypes.string,
      message: PropTypes.string,
      type: PropTypes.string,
      status: PropTypes.string,
      created_at: PropTypes.string,
    }),
  ),
  currentuser: PropTypes.shape({
    role: PropTypes.string.isRequired,
  }),
  setFilters: PropTypes.func.isRequired,
  setPagination: PropTypes.func.isRequired,
  selectRequest: PropTypes.func.isRequired,
};

export default ContactAdminTable;
