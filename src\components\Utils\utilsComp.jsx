import { SearchOutlined } from "@mui/icons-material";
import CustomTimePicker from "./CustomTimePicker";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Paper,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TablePagination,
  TableRow,
  Tabs,
  TextField,
  InputLabel,
  IconButton,
  Typography,
  Select,
  MenuItem,
  FormHelperText,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import { useStateContext } from "../../context/ContextProvider";
import TableHeader from "../../layouts/machineData/TableHeader";
import { sharedCss } from "../../styles/sharedCss";
import NoDataComponent from "../commons/noData.component";
import { fetchData, saveData, deleteData } from "./api";
import { timeToMinutes, minutesToTime } from "./timeUtils";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import { commonRowStyle } from "../../layouts/machineData/MaintenanceReportDataMain";
import Delete from "../Delete/Delete";
import { ButtonBasic, ButtonBasicCancel } from "../buttons/Buttons";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../not-accessible/not-accessible";

const UtilsComp = ({ onOptionsChange }) => {
  const {
    currentColor,
    currentMode,
    toastMessage,
    toastMessageWarning,
    toastMessageSuccess,
  } = useStateContext() || {
    currentColor: "#1976d2",
    currentMode: "Light",
    toastMessage: ({ message }) => {
      if (
        message.includes("Failed to create") ||
        message.includes("Failed to update")
      ) {
        try {
          const errorStart = message.indexOf("[");
          if (errorStart > -1) {
            const errorJson = message.substring(errorStart);
            const errors = JSON.parse(errorJson);
            errors.forEach((err) => {
              const fieldName = err.path[err.path.length - 1];
              const fieldLabel =
                currentFields.find((f) => f.key === fieldName)?.label ||
                fieldName;
              toast(`${fieldLabel}: ${err.message}`, {
                type: toast.TYPE.ERROR,
              });
            });
            return;
          }
        } catch (e) {}
      }
      if (message.includes("Network Error")) {
        toast("Network error: Please check your internet connection", {
          type: toast.TYPE.ERROR,
        });
        return;
      }
      if (
        message.includes("500") ||
        message.includes("Internal Server Error")
      ) {
        toast("Server error: Please try again later or contact support", {
          type: toast.TYPE.ERROR,
        });
        return;
      }
      if (
        message.includes("401") ||
        message.includes("403") ||
        message.includes("Unauthorized") ||
        message.includes("Forbidden")
      ) {
        toast("Authentication error: Please log in again", {
          type: toast.TYPE.ERROR,
        });
        return;
      }
      if (message.includes("404") || message.includes("Not Found")) {
        toast("Resource not found: The requested item does not exist", {
          type: toast.TYPE.ERROR,
        });
        return;
      }
      if (message.includes("timeout") || message.includes("ECONNABORTED")) {
        toast("Request timed out: Server is taking too long to respond", {
          type: toast.TYPE.ERROR,
        });
        return;
      }
      if (message.includes("duplicate") || message.includes("already exists")) {
        toast("Duplicate entry: This item already exists", {
          type: toast.TYPE.ERROR,
        });
        return;
      }
      toast(message, { type: toast.TYPE.ERROR });
    },
    toastMessageWarning: ({ message }) =>
      toast(message, { type: toast.TYPE.WARNING }),
    toastMessageSuccess: ({ message }) =>
      toast(message, { type: toast.TYPE.SUCCESS }),
  };
  const useStyles = sharedCss();
  const sharedStyles = useStyles || {};
  const [options, setOptions] = useState({
    line: [],
    shift: [],
    product_name: [],
    reasons: [],
  });
  const [machines, setMachines] = useState([]);
  const [loadingMachines, setLoadingMachines] = useState(false);

  const categories = ["line", "shift", "product_name", "reasons"];
  const labels = {
    line: "Lines",
    shift: "Shifts",
    product_name: "Products",
    reasons: "Reasons",
  };

  const fields = {
    line: [
      { key: "title", label: "Title" },
      { key: "description", label: "Description" },
      { key: "location", label: "Location" },
    ],
    shift: [
      { key: "title", label: "Title" },
      { key: "description", label: "Description" },
      { key: "start_time", label: "Start Time" },
      { key: "end_time", label: "End Time" },
      { key: "break_time", label: "Break Time (minutes)", type: "number" },
      { key: "instance_time", label: "Instance Time (HH:MM)", disabled: true },
      {
        key: "total_shift_time",
        label: "Total Shift Time (HH:MM)",
        disabled: true,
      },
    ],
    product_name: [
      {
        key: "mid",
        label: "Machine",
        type: "dropdown",
        options: machines.map((m) => ({
          value: m._id,
          label: `${m.title} (${m.model}) - ${m.equipment_id}`,
        })),
      },
      { key: "title", label: "Title" },
      { key: "description", label: "Description" },
      { key: "pack_style", label: "Pack Style" },
      { key: "running_rpm", label: "Running RPM" },
      { key: "optimal_speed", label: "Optimal Speed" },
      { key: "qty_per_shipper", label: "Qty per Shipper" },
      { key: "quality", label: "Quality" },
    ],
    reasons: [
      { key: "category", label: "Category" },
      { key: "description", label: "Description" },
    ],
  };

  const [tabIndex, setTabIndex] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState("add");
  const [formData, setFormData] = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [errors, setErrors] = useState({});
  const commonOuterContainerStyle = useCommonOuterContainerStyle();
  const [openDel, setOpenDel] = useState(false);
  const currentKey = categories[tabIndex];
  const currentFields = fields[currentKey];
  const currentData = options[currentKey];

  const hasShiftGETAccess = useCheckAccess("shifts", "GET");
  const hasShiftPOSTAccess = useCheckAccess("shifts", "POST");
  const hasShiftPUTAccess = useCheckAccess("shifts", "PUT");
  const hasShiftDELETEAccess = useCheckAccess("shifts", "DELETE");

  const hasLineGETAccess = useCheckAccess("lines", "GET");
  const hasLinePOSTAccess = useCheckAccess("lines", "POST");
  const hasLinePUTAccess = useCheckAccess("lines", "PUT");
  const hasLineDELETEAccess = useCheckAccess("lines", "DELETE");

  const hasProductGETAccess = useCheckAccess("products", "GET");
  const hasProductPOSTAccess = useCheckAccess("products", "POST");
  const hasProductPUTAccess = useCheckAccess("products", "PUT");
  const hasProductDELETEAccess = useCheckAccess("products", "DELETE");

  console.log(currentKey, "currentKey");

  useEffect(() => {
    const fetchMachines = async () => {
      setLoadingMachines(true);
      try {
        const result = await fetchData("machine");
        setMachines(Array.isArray(result) ? result : []);
      } catch (error) {
        console.error("Failed to load machines:", error);
      } finally {
        setLoadingMachines(false);
      }
    };

    fetchMachines();
  }, []);

  useEffect(() => {
    const loadData = async () => {
      try {
        const results = await Promise.all(
          categories.map(async (key) => {
            const result = await fetchData(key);
            if (key === "product_name" && Array.isArray(result)) {
              return {
                key,
                data: result.map((product) => ({
                  ...product,
                  machine: product.machine || product.mid,
                })),
              };
            }
            return { key, data: result };
          }),
        );

        const newOptions = { ...options };
        results.forEach(({ key, data }) => {
          newOptions[key] = Array.isArray(data) ? data : [];
        });
        setOptions(newOptions);
      } catch (error) {
        console.error("Failed to load data:", error);
      }
    };

    loadData();
  }, []);

  const filteredData = Array.isArray(currentData)
    ? currentData.filter((item) =>
        currentFields.some((field) =>
          String(item[field.key] || "")
            .toLowerCase()
            .includes(searchTerm.toLowerCase()),
        ),
      )
    : [];

  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortConfig.key) return 0;
    if (sortConfig.direction === "asc")
      return String(a[sortConfig.key]).localeCompare(String(b[sortConfig.key]));
    return String(b[sortConfig.key]).localeCompare(String(a[sortConfig.key]));
  });

  const paginatedData = sortedData.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage,
  );

  const columns = [
    ...currentFields.map((f) => ({
      label: f.label,
      align: f.key === "actions" ? "right" : "left",
    })),
    { label: "Actions", align: "center" },
  ];

  const openDialog = (mode, item = {}, idx = -1) => {
    setDialogMode(mode);
    setFormData(item);
    setDialogOpen(true);
  };

  const closeDialog = () => {
    setDialogOpen(false);
    setFormData({});
    setErrors({});
  };

  const handleChange = (key, value) => {
    if (
      key.includes("rpm") ||
      key.includes("speed") ||
      key.includes("qty") ||
      key === "quality" ||
      key === "pack_style" ||
      key === "total_shift_time" ||
      key === "instance_time"
    ) {
      const sanitizedValue = String(value).replace(/-/g, "");
      if (sanitizedValue === "") {
        setFormData((prev) => ({ ...prev, [key]: "" }));
      } else if (!isNaN(sanitizedValue)) {
        const numValue = Math.max(0, Number(sanitizedValue));
        setFormData((prev) => ({ ...prev, [key]: numValue }));
      }
    } else if (key === "start_time" || key === "end_time") {
      if (value === "" || value === null || value === undefined) {
        setFormData((prev) => ({ ...prev, [key]: 0 }));
      } else if (value === "12:00 AM") {
        setFormData((prev) => ({ ...prev, [key]: 0 }));
      } else {
        const minutesValue = timeToMinutes(value);
        setFormData((prev) => ({ ...prev, [key]: minutesValue }));
      }
    } else {
      setFormData((prev) => ({ ...prev, [key]: value }));
    }
    setErrors((prev) => ({ ...prev, [key]: "" }));
  };

  const validateForm = () => {
    const newErrors = {};
    currentFields.forEach((field) => {
      if (
        field.key.includes("rpm") ||
        field.key.includes("speed") ||
        field.key.includes("qty") ||
        field.key === "quality" ||
        field.key === "pack_style" ||
        field.key === "total_shift_time" ||
        field.key === "instance_time"
      ) {
        if (
          formData[field.key] === undefined ||
          formData[field.key] === null ||
          formData[field.key] === ""
        ) {
          newErrors[field.key] = `${field.label} is required`;
        }
      } else if (!formData[field.key] && formData[field.key] !== 0) {
        newErrors[field.key] = `${field.label} is required`;
      }
    });

    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) {
      Object.entries(newErrors).forEach(([key, errorMessage]) => {
        toast.error(errorMessage);
      });
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      const dataToSubmit = { ...formData };
      Object.keys(dataToSubmit).forEach((key) => {
        if (
          (key.includes("time") || key.includes("Time")) &&
          key !== "total_shift_time" &&
          key !== "instance_time" &&
          key !== "updated_at" && // Explicitly exclude updated_at
          key !== "created_at" // Exclude created_at to be safe
        ) {
          if (
            typeof dataToSubmit[key] === "string" &&
            dataToSubmit[key].includes(":")
          ) {
            dataToSubmit[key] = timeToMinutes(dataToSubmit[key]);
          }
        }
      });

      if (currentKey === "shift") {
        if (
          dataToSubmit.start_time === undefined ||
          dataToSubmit.start_time === null
        ) {
          dataToSubmit.start_time = 0;
        }
        if (
          dataToSubmit.end_time === undefined ||
          dataToSubmit.end_time === null
        ) {
          dataToSubmit.end_time = 0;
        }
        if (
          dataToSubmit.break_time === undefined ||
          dataToSubmit.break_time === null ||
          dataToSubmit.break_time === ""
        ) {
          dataToSubmit.break_time = 0;
        } else {
          dataToSubmit.break_time = Number(dataToSubmit.break_time);
        }
        if (
          dataToSubmit.total_shift_time !== undefined &&
          dataToSubmit.total_shift_time !== null
        ) {
          dataToSubmit.total_shift_time = Math.round(
            parseFloat(dataToSubmit.total_shift_time) * 60,
          );
        } else {
          dataToSubmit.total_shift_time = 0;
        }
        if (
          dataToSubmit.instance_time !== undefined &&
          dataToSubmit.instance_time !== null
        ) {
          dataToSubmit.instance_time = Math.round(
            parseFloat(dataToSubmit.instance_time) * 60,
          );
        } else {
          dataToSubmit.instance_time = 0;
        }
      }

      // Remove updated_at and created_at to let the server handle them
      delete dataToSubmit.updated_at;
      delete dataToSubmit.created_at;

      console.log("dataToSubmit:", dataToSubmit); // Log to verify payload

      const savedData = await saveData(
        currentKey,
        dataToSubmit,
        dialogMode === "edit",
      );

      setOptions((prev) => {
        const updatedData = [...prev[currentKey]];
        if (dialogMode === "edit") {
          const index = updatedData.findIndex(
            (item) => item._id === savedData._id,
          );
          if (index !== -1) {
            updatedData[index] = savedData;
          }
        } else {
          updatedData.push(savedData);
        }
        return { ...prev, [currentKey]: updatedData };
      });

      onOptionsChange?.(options);
      setDialogOpen(false);
      setFormData({});

      const entityName =
        currentKey === "line"
          ? "Line"
          : currentKey === "shift"
            ? "Shift"
            : currentKey === "product_name"
              ? "Product"
              : "Reasons";
      toast.success(
        `${entityName} ${dialogMode === "edit" ? "updated" : "created"} successfully`,
      );
    } catch (error) {
      console.log(`Error saving ${currentKey} :`, error);
      if (
        error.message &&
        (error.message.includes("Failed to create") ||
          error.message.includes("Failed to update"))
      ) {
        try {
          const errorStart = error.message.indexOf("[");
          if (errorStart > -1) {
            const errorJson = error.message.substring(errorStart);
            const errors = JSON.parse(errorJson);
            errors.forEach((err) => {
              const fieldName = err.path[err.path.length - 1];
              const fieldLabel =
                currentFields.find((f) => f.key === fieldName)?.label ||
                fieldName;
              toast.error(`${fieldLabel}: ${err.message}`);
            });
            return;
          }
        } catch (e) {}
      }
      toast.error(`Error saving ${currentKey}: ${error.message}`);
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteData(currentKey, id);
      setOptions((prev) => {
        const updated = { ...prev };
        updated[currentKey] = updated[currentKey].filter(
          (item) => item._id !== id,
        );
        onOptionsChange?.(updated);
        return updated;
      });
    } catch (error) {
      console.error(`Error deleting ${currentKey}:`, error);
    }
  };

  const handleEditClick = (row) => {
    const formattedRow = { ...row };
    Object.keys(row).forEach((key) => {
      if (
        (key.includes("time") || key.includes("Time")) &&
        key !== "total_shift_time" &&
        key !== "instance_time" &&
        key !== "break_time"
      ) {
        if (row[key] !== undefined && row[key] !== null) {
          formattedRow[key] = minutesToTime(row[key]);
        }
      }
    });
    if (row.break_time !== undefined && row.break_time !== null) {
      formattedRow.break_time = Number(row.break_time);
    }
    setFormData(formattedRow);
    setDialogMode("edit");
    setDialogOpen(true);
  };

  useEffect(() => {
    if (
      currentKey === "shift" &&
      formData.start_time !== undefined &&
      formData.end_time !== undefined
    ) {
      const startMinutes =
        typeof formData.start_time === "number"
          ? formData.start_time
          : timeToMinutes(formData.start_time);
      const endMinutes =
        typeof formData.end_time === "number"
          ? formData.end_time
          : timeToMinutes(formData.end_time);
      const breakMinutes =
        typeof formData.break_time === "number" ? formData.break_time : 0;
      const normalizedEndMinutes = endMinutes === 0 ? 1440 : endMinutes;
      let instanceMinutes = 0;
      if (normalizedEndMinutes >= startMinutes) {
        instanceMinutes = normalizedEndMinutes - startMinutes;
      } else {
        instanceMinutes = 24 * 60 - startMinutes + normalizedEndMinutes;
      }
      const totalShiftMinutes = Math.max(0, instanceMinutes - breakMinutes);
      setFormData((prev) => ({
        ...prev,
        instance_time: (instanceMinutes / 60).toFixed(2),
        total_shift_time: (totalShiftMinutes / 60).toFixed(2),
      }));
    }
  }, [formData.start_time, formData.end_time, formData.break_time, currentKey]);

  const formatHoursToHHMM = (hours) => {
    if (!hours && hours !== 0) return "";
    const totalMinutes = Math.round(hours * 60);
    const hrs = Math.floor(totalMinutes / 60);
    const mins = totalMinutes % 60;
    return `${String(hrs).padStart(2, "0")}:${String(mins).padStart(2, "0")}`;
  };

  return (
    <section className="machineDataViewPage">
      <ToastContainer position="top-right" autoClose={5000} />
      <Tabs
        value={tabIndex}
        onChange={(_, newIndex) => setTabIndex(newIndex)}
        indicatorColor="primary"
        textColor="primary"
        disableRipple
        sx={{
          all: "unset",
          "& .MuiTab-root:hover": {
            backgroundColor: "transparent",
          },
        }}
      >
        {categories.map((cat) => (
          <Tab key={cat} label={labels[cat]} />
        ))}
      </Tabs>

      <div className="allMachineDataPreviewContainer">
        <div
          className={`${sharedStyles.sectionContainer} ${sharedStyles.backgroundLight} border-radius-outer`}
        >
          <div className={sharedStyles.tableLable}>
            <Typography
              sx={{ ml: 2, ...sharedStyles.typography }}
              fontWeight="bold"
              variant="h6"
            >
              {labels[currentKey]}
            </Typography>
            <div className={sharedStyles.tableRightContent}>
              <TextField
                label="Search"
                className={sharedStyles.searchBox}
                variant="outlined"
                size="small"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchOutlined />,
                }}
              />
              <Button
                variant="contained"
                onClick={() => openDialog("add", {})}
                disabled={
                  currentKey === "shift"
                    ? !hasShiftPOSTAccess
                    : currentKey === "product_name"
                      ? !hasProductPOSTAccess
                      : !hasLinePOSTAccess
                }
              >
                Add {labels[currentKey].slice(0, -1)}
              </Button>
            </div>
          </div>

          <div className="liveDataContainer">
            {(
              currentKey === "shift"
                ? hasShiftGETAccess
                : currentKey === "product_name"
                  ? hasProductGETAccess
                  : hasLineGETAccess
            ) ? (
              <>
                <TableContainer
                  component={Paper}
                  className="table border-radius-inner"
                  sx={commonOuterContainerStyle}
                >
                  <Table
                    sx={{ minWidth: 650 }}
                    size="small"
                    aria-label="a dense table"
                  >
                    <TableHeader currentMode={currentMode} columns={columns} />
                    <TableBody>
                      {paginatedData.length > 0 ? (
                        paginatedData.map((row) => (
                          <TableRow key={row._id} sx={commonRowStyle}>
                            {currentFields.map((f) => (
                              <TableCell
                                key={f.key}
                                align={f.key === "actions" ? "center" : "left"}
                                sx={{ ...sharedStyles.tableCell }}
                              >
                                {f.key === "mid"
                                  ? (() => {
                                      const machine = machines.find(
                                        (m) => m._id === row.mid,
                                      );
                                      return machine
                                        ? `${machine.title} (${machine.model}) - ${machine.equipment_id}`
                                        : "-";
                                    })()
                                  : f.key === "break_time"
                                    ? row[f.key] !== undefined &&
                                      row[f.key] !== null
                                      ? `${row[f.key]} min`
                                      : "-"
                                    : f.key === "total_shift_time" ||
                                        f.key === "instance_time"
                                      ? row[f.key] !== undefined &&
                                        row[f.key] !== null
                                        ? formatHoursToHHMM(row[f.key] / 60)
                                        : "-"
                                      : f.key.includes("time") ||
                                          f.key.includes("Time")
                                        ? minutesToTime(row[f.key]) || "-"
                                        : row[f.key] !== undefined &&
                                            row[f.key] !== null
                                          ? row[f.key].toString()
                                          : "-"}
                              </TableCell>
                            ))}
                            <TableCell
                              align="center"
                              sx={{ ...sharedStyles.tableCell }}
                            >
                              <IconButton
                                size="small"
                                sx={{
                                  color:
                                    currentMode === "Dark"
                                      ? "#E0E0E0"
                                      : currentColor,
                                }}
                                onClick={() => handleEditClick(row)}
                                disabled={
                                  currentKey === "shift"
                                    ? !hasShiftPUTAccess
                                    : currentKey === "product_name"
                                      ? !hasProductPUTAccess
                                      : !hasLinePUTAccess
                                }
                              >
                                <EditIcon style={{ fontSize: "20px" }} />
                              </IconButton>
                              <IconButton
                                size="small"
                                sx={{ color: "red" }}
                                onClick={() => {
                                  setOpenDel(true);
                                  setFormData(row);
                                }}
                                disabled={
                                  currentKey === "shift"
                                    ? !hasShiftDELETEAccess
                                    : currentKey === "product_name"
                                      ? !hasProductDELETEAccess
                                      : !hasLineDELETEAccess
                                }
                              >
                                <DeleteIcon style={{ fontSize: "20px" }} />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <NoDataComponent
                          cellColSpan={currentFields.length + 1}
                        />
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>

                <TablePagination
                  component="div"
                  count={filteredData.length}
                  page={page}
                  onPageChange={(_, newPage) => setPage(newPage)}
                  rowsPerPage={rowsPerPage}
                  onRowsPerPageChange={(e) => {
                    setRowsPerPage(parseInt(e.target.value, 10));
                    setPage(0);
                  }}
                  sx={{
                    ...sharedStyles.pagination,
                  }}
                />
              </>
            ) : (
              <NotAccessible />
            )}
          </div>
        </div>
      </div>

      <Dialog open={openDel}>
        <Delete
          onClose={() => setOpenDel(false)}
          onDelete={handleDelete}
          id={formData._id}
        />
      </Dialog>

      <Dialog
        open={dialogOpen}
        onClose={closeDialog}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          sx: {
            ...sharedStyles.dialogPaper,
          },
        }}
      >
        <DialogTitle>
          {dialogMode === "add" ? "Add" : "Edit"}{" "}
          {labels[currentKey].slice(0, -1)}
        </DialogTitle>
        <DialogContent>
          {currentFields.map((f) => {
            if (f.disabled) {
              return (
                <Box key={f.key} sx={{ mb: 2 }}>
                  <InputLabel sx={{ mb: 1, ...sharedStyles.inputLabel }}>
                    {f.label}
                  </InputLabel>
                  <TextField
                    fullWidth
                    value={
                      f.key === "total_shift_time" || f.key === "instance_time"
                        ? formatHoursToHHMM(parseFloat(formData[f.key]))
                        : formData[f.key] || ""
                    }
                    disabled={true}
                    sx={{ ...sharedStyles.textField }}
                  />
                </Box>
              );
            } else if (f.key === "mid") {
              return (
                <Box key={f.key} sx={{ mb: 2 }}>
                  <InputLabel sx={{ mb: 1, ...sharedStyles.inputLabel }}>
                    {f.label}
                  </InputLabel>
                  <Select
                    fullWidth
                    value={formData.mid || ""}
                    onChange={(e) => handleChange("mid", e.target.value)}
                    error={!!errors.mid}
                    sx={{ ...sharedStyles.textField, marginBottom: 1 }}
                    disabled={loadingMachines}
                  >
                    <MenuItem value="">
                      <em>Select a Machine</em>
                    </MenuItem>
                    {machines.map((machine) => (
                      <MenuItem key={machine._id} value={machine._id}>
                        {`${machine.title} (${machine.model}) - ${machine.equipment_id}`}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.mid && (
                    <FormHelperText error>{errors.mid}</FormHelperText>
                  )}
                </Box>
              );
            } else if (f.key === "break_time") {
              return (
                <React.Fragment key={f.key}>
                  <InputLabel sx={{ ...sharedStyles.inputLabel }}>
                    {f.label}
                  </InputLabel>
                  <TextField
                    margin="dense"
                    fullWidth
                    value={formData[f.key] ?? ""}
                    onChange={(e) =>
                      handleChange(f.key, Number(e.target.value))
                    }
                    error={!!errors[f.key]}
                    helperText={errors[f.key] || ""}
                    type="number"
                    inputProps={{ min: 0 }}
                    sx={{
                      ...sharedStyles.textField,
                      marginBottom: 1,
                    }}
                  />
                </React.Fragment>
              );
            } else if (f.key.includes("time") || f.key.includes("Time")) {
              return (
                <Box key={f.key} sx={{ mb: 2 }}>
                  <InputLabel sx={{ mb: 1, ...sharedStyles.inputLabel }}>
                    {f.label}
                  </InputLabel>
                  <CustomTimePicker
                    onTimeChange={(time) => handleChange(f.key, time)}
                    initialValue={
                      formData[f.key] !== undefined && formData[f.key] !== null
                        ? typeof formData[f.key] === "number"
                          ? minutesToTime(formData[f.key])
                          : formData[f.key]
                        : ""
                    }
                  />
                  {errors[f.key] && (
                    <Typography color="error" variant="caption">
                      {errors[f.key]}
                    </Typography>
                  )}
                </Box>
              );
            } else {
              return (
                <React.Fragment key={f.key}>
                  <InputLabel sx={{ ...sharedStyles.inputLabel }}>
                    {f.label}
                  </InputLabel>
                  <TextField
                    margin="dense"
                    fullWidth
                    value={formData[f.key] ?? ""}
                    onChange={(e) => handleChange(f.key, e.target.value)}
                    error={!!errors[f.key]}
                    helperText={errors[f.key] || ""}
                    type={
                      f.key.includes("rpm") ||
                      f.key.includes("speed") ||
                      f.key.includes("qty") ||
                      f.key === "quality" ||
                      f.key === "pack_style"
                        ? "number"
                        : "text"
                    }
                    sx={{
                      ...sharedStyles.textField,
                      marginBottom: 1,
                    }}
                  />
                </React.Fragment>
              );
            }
          })}
        </DialogContent>
        <DialogActions>
          <ButtonBasicCancel
            onClick={closeDialog}
            buttonTitle="CANCEL"
            type="button"
          />
          <ButtonBasic onClick={handleSave} buttonTitle="SAVE" type="button" />
        </DialogActions>
      </Dialog>
    </section>
  );
};

export default UtilsComp;
