import React, { useState } from "react";
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { companies, companyId_constant } from "../../../constants/data";
import { db } from "../../../firebase";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { useStateContext } from "../../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import { toastMessage, toastMessageSuccess } from "../../../tools/toast";
import { useMongoRefresh } from "../../../services/mongo-refresh.context";

const EditMaintenanceReport = ({
  handleClose,
  data,
  userName,
  reportName,
  machineName,
  reportType,
}) => {
  console.log("data", data);
  console.log("data._id", data._id);
  const [localData, setLocalData] = useState(data);
  const [remarks, setRemarks] = useState(data.remarks || ""); // Default to empty string if undefined
  const [status, setStatus] = useState(data.report_status);
  const [remarksError, setRemarksError] = useState("");
  const { currentMode } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();

  // Handle remarks change and prevent leading space
  const handleRemarksChange = (e) => {
    const value = e.target.value;
    // Prevent space as the first character
    if (value.length === 1 && value === " ") {
      return; // Ignore the input if it's just a space
    }
    // Remove leading spaces if they somehow get through (e.g., pasting)
    const trimmedValue = value.replace(/^\s+/, "");
    setRemarks(trimmedValue);
    setRemarksError(""); // Clear error when user types valid input
  };

  const handleRemarksBlur = () => {
    const trimmedRemarks = remarks.trim();
    setRemarks(trimmedRemarks);
    if (!trimmedRemarks) {
      setRemarksError("Remarks cannot be empty or just spaces");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate remarks
    const trimmedRemarks = remarks.trim();
    if (!trimmedRemarks) {
      setRemarksError("Remarks cannot be empty or just spaces");
      toastMessage({ message: "Please enter valid remarks" });
      return; // Prevent submission
    }

    // Prevent update if trying to set Review Done when report_status is 0
    if (status === 2 && localData.report_status === 0) {
      toastMessage({ message: "First review all steps" });
      return;
    }

    // Prevent update if report_status is 0 (legacy check)
    if (localData.report_status === 0) {
      toastMessage({ message: "First review all steps" });
      return;
    }

    // Create updated data object
    const updatedData = {
      ...localData,
      report_status: status, // Use the selected status
      remarks: trimmedRemarks, // Use trimmed value
      date: new Date(localData.date), // Ensure date is in correct format
    };

    try {
      const response = await axios.put(
        `${dbConfig.url}/mainReportData/${localData._id}`,
        updatedData,
      );

      if (response.status === 200) {
        setRefreshCount(refreshCount + 1); // Refresh data after update
        toastMessageSuccess({ message: "Report Updated Successfully" });
        setLocalData(updatedData);
        setRemarks(updatedData.remarks);
        setStatus(updatedData.report_status);
        handleClose();
      }
    } catch (error) {
      console.log("Update failed:", error.message);
      toastMessage({ message: "Failed to update report: " + error.message });
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Remarks</InputLabel>
      <TextField
        onChange={handleRemarksChange}
        onBlur={handleRemarksBlur}
        value={remarks}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{ marginBottom: "12px" }}
        error={!!remarksError}
        helperText={remarksError}
      />

      <InputLabel style={{ marginBottom: "10px" }}>Select Status</InputLabel>
      <FormControl
        style={{ marginBottom: "10px" }}
        required
        variant="outlined"
        fullWidth
      >
        <Select
          required
          value={status}
          onChange={(e) => setStatus(e.target.value)}
        >
          <MenuItem
            value={2}
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#212B36", color: "white" }
                : {}
            }
          >
            Review Done
          </MenuItem>
          <MenuItem
            value={1}
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#212B36", color: "white" }
                : {}
            }
          >
            Pending
          </MenuItem>
        </Select>
      </FormControl>

      <div className="p-2 mt-2 flex justify-between">
        <Button color="error" onClick={handleClose} variant="contained">
          Cancel
        </Button>
        <Button color="success" type="submit" variant="contained">
          Submit
        </Button>
      </div>
    </form>
  );
};

export default EditMaintenanceReport;
