import React, { useEffect, useMemo, useState } from "react";
import {
  Avatar,
  Box,
  Button,
  FormControl,
  IconButton,
  Stack,
  Switch,
  Typography,
} from "@mui/material";
import { IoCloudUploadOutline, IoTrashBin } from "react-icons/io5";
import { FaSignature } from "react-icons/fa";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useAuth } from "../../hooks/AuthProvider";
import { toastMessage, toastMessageSuccess, toastMessageWarning } from "../../tools/toast";
import { useCheckAccess } from "../../utils/useCheckAccess";
import { useStateContext } from "../../context/ContextProvider";
import { alpha, styled } from "@mui/material/styles";
import { makeStyles } from "@mui/styles";
import TextField from "@mui/material/TextField";

// Custom styling for TextField
const useStyles = makeStyles((theme) => ({
  textfieldStyling: {
    "& .MuiInputLabel-root, & .MuiInputBase-input": {
      color: theme.palette.custom.textColor,
    },
    "& .Mui-disabled .MuiInputBase-input, & .Mui-disabled .MuiInputLabel-root": {
      color: "grey",
    },
    "& .MuiOutlinedInput-notchedOutline": {
      borderColor: theme.palette.custom.textColor,
    },
    "& .Mui-disabled .MuiOutlinedInput-notchedOutline": {
      borderColor: "grey",
    },
    "& .MuiOutlinedInput-root fieldset": {
      borderColor: theme.palette.custom.textColor,
    },
    "& .MuiOutlinedInput-root.Mui-focused fieldset": {
      borderColor: theme.palette.primary.main,
    },
    "& .Mui-disabled .MuiInputAdornment-root .MuiSvgIcon-root": {
      color: "grey",
    },
  },
}));

// Custom styled InputBase
const BootstrapInput = styled(TextField)(({ theme }) => ({
  "label + &": {
    marginTop: theme.spacing(0),
  },
  "& .MuiInputBase-input": {
    borderRadius: 4,
    position: "relative",
    backgroundColor: theme.palette.mode === "light" ? "#fcfcfb" : "#161C24",
    border: "1px solid #ced4da",
    fontSize: 14,
    padding: "10px 12px",
    transition: theme.transitions.create(["border-color", "background-color", "box-shadow"]),
    "&:focus": {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main,
    },
  },
}));

// UserDetails component (assumed to handle text fields)
const UserDetails = ({BootstrapInput, setUser, user, handleChange, }) =>
  {
  console.log("UserDetails component is not implemented yet", user)

    return (
      <div
      style={{
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        gap: "1rem",
        padding: "1rem",
      }}
    >
      <Stack direction="row" spacing={2} sx={{ width: "100%" }}>
        <div
          style={{
            marginTop: "0.5rem",
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-end",
            gap: "2.2rem",
          }}
        >
          <Typography>First Name</Typography>
          <Typography>Last Name</Typography>
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "1rem",
          }}
        >
          <FormControl>
            <BootstrapInput
              id="fname-input"
              name="fname"
              value={user?.fname}
              disabled
              onChange={handleChange}
              sx={{
                "& label": { marginRight: "0.5rem" },
                width: "15rem",
              }}
              fullWidth
            />
          </FormControl>
          <FormControl>
            <BootstrapInput
              id="lname-input"
              name="lname"
              disabled
              value={user?.lname}
              onChange={handleChange}
              sx={{ "& label": { marginRight: "0.5rem" } }}
              fullWidth
            />
          </FormControl>
        </div>
      </Stack>

      <Stack direction="row" spacing={2} sx={{ width: "100%" }}>
        <div
          style={{
            marginTop: "0.5rem",

            display: "flex",
            flexDirection: "column",
            alignItems: "flex-end",
            gap: "2.2rem",
          }}
        >
          <Typography>Email</Typography>
          <Typography style={{ width: "max-content" }}>
            Employee ID
          </Typography>
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "1rem",
          }}
        >
          <FormControl>
            <BootstrapInput
              id="email-input"
              name="email"
              value={user?.email}
              disabled
              sx={{
                "& label": { marginRight: "0.5rem" },
                width: "15rem",
              }}
              fullWidth
            />
          </FormControl>
          <FormControl>
            <BootstrapInput
              id="phone-input"
              name="phone"
              value={user?.username}
              disabled
              sx={{ "& label": { marginRight: "0.5rem" } }}
              fullWidth
            />
          </FormControl>
        </div>
      </Stack>
     
    </div>
    )
}

const Form = () => {
  const { currentUser } = useAuth();
  const { currentMode, currentColorLight } = useStateContext();
  const hasUserPUTAccess = useCheckAccess("users", "PUT");
  const classes = useStyles();

  const [user, setUser] = useState(null);
  const [userUpdateTracker, setUserUpdateTracker] = useState(null);
  const [file, setFile] = useState(null);
  const [signatureFile, setSignatureFile] = useState(null);
  const [fileUrl, setFileUrl] = useState(null);
  const [signatureFileUrl, setSignatureFileUrl] = useState(null);
  const [filesizeAlert, setFilesizeAlert] = useState(false);

  // Fetch user details
  const getuserDetails = async () => {
    try {
      const response = await axios.get(`${dbConfig.url}/users/${currentUser._id}`);
      const data = response.data?.data;
      setUser({
        fname: data?.fname || "",
        lname: data?.lname || "",
        email: data?.email || "",
        phone: data?.phone || "",
        avatar: data?.avatar || "",
        signature: data?.signature || "",
        username: data?.username || "",
        notify: data?.notify || false, // Assuming notify is part of user data
      });
      setUserUpdateTracker({
        fname: data?.fname || "",
        lname: data?.lname || "",
        phone: data?.phone || "",
        avatar: data?.avatar || "",
        signature: data?.signature || "",
        notify: data?.notify || false,
      });
    } catch (error) {
      toastMessage({ message: `Failed to fetch user details: ${error.message}` });
    }
  };

  useEffect(() => {
    getuserDetails();
  }, []);

  // Handle input changes
  const handleChange = (event) => {
    setUser({
      ...user,
      [event.target.name]: event.target.value,
    });
  };

  // Handle notification toggle
  const handleNotifyToggle = () => {
    setUser((prev) => ({
      ...prev,
      notify: !prev.notify,
    }));
  };

  // Image upload handler
  const handleImageUpload = async (file) => {
    if (file.size > 2 * 1024 * 1024) { // 2MB limit
      setFilesizeAlert(true);
      return null;
    }
    setFilesizeAlert(false);
    const formData = new FormData();
    formData.append("image", file);
    try {
      const response = await axios.post(`${dbConfig?.url_storage}/upload`, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      return response?.data?.data;
    } catch (error) {
      toastMessage({ message: `Image upload failed: ${error.response?.data?.message || error.message}` });
      return null;
    }
  };

  // Profile picture change handler
  const handleChangeImage = async (event) => {
    const selectedFile = event.target.files[0];
    const types = ["image/png", "image/jpeg", "image/jpg"];
    if (selectedFile && types.includes(selectedFile.type)) {
      setFile(selectedFile);
      const uploadedUrl = await handleImageUpload(selectedFile);
      if (uploadedUrl) {
        setFileUrl(`${dbConfig?.url_storage}/images/${uploadedUrl}`);
        setUser((prev) => ({ ...prev, avatar: uploadedUrl }));
      }
    } else {
      toastMessage({ message: "Please select an image file (png or jpg)" });
    }
  };

  // Signature change handler
  const handleSignatureChange = async (event) => {
    const selectedFile = event.target.files[0];
    const types = ["image/png", "image/jpeg", "image/jpg"];
    if (selectedFile && types.includes(selectedFile.type)) {
      setSignatureFile(selectedFile);
      const uploadedUrl = await handleImageUpload(selectedFile);
      if (uploadedUrl) {
        setSignatureFileUrl(`${dbConfig?.url_storage}/images/${uploadedUrl}`);
        setUser((prev) => ({ ...prev, signature: uploadedUrl }));
      }
    } else {
      toastMessage({ message: "Please select an image file (png or jpg)" });
    }
  };

  // Remove profile picture
  const removeProfilePicture = () => {
    setFile(null);
    setFileUrl(null);
    setUser((prev) => ({ ...prev, avatar: "" }));
  };

  // Remove signature
  const removeSignature = () => {
    setSignatureFile(null);
    setSignatureFileUrl(null);
    setUser((prev) => ({ ...prev, signature: "" }));
  };

  // Update user details
  const updateUserDetails = async () => {
    if (user.phone.length !== 10) {
      toastMessageWarning({ message: "Phone No must be 10 digits" });
      return;
    }
    try {
      await axios.put(`${dbConfig.url}/users/${currentUser._id}`, user);
      toastMessageSuccess({ message: "UserInfo has been updated successfully!" });
      setUser({
        fname: "",
        lname: "",
        email: "",
        phone: "",
        avatar: "",
        signature: "",
        notify: false,
      });
      setFile(null);
      setSignatureFile(null);
      setFileUrl(null);
      setSignatureFileUrl(null);
      window.location.reload();
    } catch (error) {
      toastMessage({ message: `Update failed: ${error.response?.data?.message || error.message}` });
    }
  };

  // Check if user info has changed
  const isUserInfoUpdated = useMemo(() => {
    if (!userUpdateTracker || !user) return true;
    return (
      userUpdateTracker.fname === user.fname &&
      userUpdateTracker.lname === user.lname &&
      userUpdateTracker.phone === user.phone &&
      userUpdateTracker.avatar === user.avatar &&
      userUpdateTracker.signature === user.signature &&
      userUpdateTracker.notify === user.notify
    );
  }, [user, userUpdateTracker]);

  const isUsernameEmpty = !user?.fname?.trim();
  const isPhoneEmpty = !user?.phone?.trim();
  const isUpdateUserButtonDisabled = isUsernameEmpty || isPhoneEmpty || isUserInfoUpdated;

  return (
    <div
      style={{
        borderRadius: "5px",
        margin: "auto",
        padding: "1rem",
      }}
    >
      {user && user.email && (
        <>
          <div
            style={{
              background: "lightgrey",
              marginBottom: 0,
              alignContent: "center",
              padding: "1rem",
              borderRadius: "5px 5px 0px 0px",
            }}
          >
            <Typography variant="h6">Basic Info</Typography>
          </div>
          <div
            style={{
              borderRadius: "0px 0px 5px 5px",
              borderColor: "lightgray",
              backgroundColor: "#f5f5f5",
              marginTop: "-1px",
              paddingTop: "1px",
            }}
          >
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              gap={2}
              m={2}
            >
              {(fileUrl || signatureFileUrl) && (
                <Typography variant="subtitle2" color="red">
                  Update User to save the Profile Picture
                </Typography>
              )}
              <div style={{ display: "flex", gap: "100px" }}>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "0.5rem",
                  }}
                >
                  <Avatar
                    src={fileUrl || `${dbConfig?.url_storage}/images/${user?.avatar}`}
                    alt="profile"
                    sx={{ width: "100px", height: "100px", display: "flex" }}
                  />
                  <Typography
                    variant="h6"
                    style={{
                      color: "black",
                      display: "flex",
                      textTransform: "capitalize",
                      alignItems: "center",
                      flexDirection: "row",
                      justifyContent: "center",
                    }}
                  >
                    Profile Picture
                  </Typography>
                  <Typography
                    fontSize={12}
                    sx={{
                      width: "156px",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                      whiteSpace: "nowrap",
                      textAlign: "center",
                    }}
                  >
                    {file ? file.name : "Upload Profile Picture"}
                  </Typography>
                  <div
                    style={{
                      display: "flex",
                      gap: "24px",
                      width: "100%",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Button sx={{ fontSize: "20px", color: "blue", mt: 1, fontWeight: "bold" }}>
                      <label htmlFor="profilePic">
                        <IoCloudUploadOutline />
                      </label>
                    </Button>
                    <input
                      id="profilePic"
                      onChange={handleChangeImage}
                      type="file"
                      accept="image/png, image/jpeg, image/jpg"
                      style={{ cursor: "pointer", display: "none" }}
                    />
                    <Button
                      onClick={removeProfilePicture}
                      sx={{ color: "red", fontSize: "20px" }}
                    >
                      <IoTrashBin />
                    </Button>
                  </div>
                  {filesizeAlert && (
                    <Typography style={{ fontSize: ".8rem", color: "red", marginBottom: ".2rem" }}>
                      Max file size 2MB
                    </Typography>
                  )}
                </div>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "0.5rem",
                  }}
                >
                  <Avatar
                    sx={{ width: "100px", height: "100px", display: "flex" }}
                    src={signatureFileUrl || `${dbConfig?.url_storage}/images/${user?.signature}`}
                  >
                    {!signatureFileUrl && !user?.signature && (
                      <FaSignature style={{ fontSize: "48px", color: "#666666" }} />
                    )}
                  </Avatar>
                  <Typography
                    variant="h6"
                    style={{
                      color: "black",
                      display: "flex",
                      textTransform: "capitalize",
                      alignItems: "center",
                      flexDirection: "row",
                      justifyContent: "center",
                    }}
                  >
                    Signature
                  </Typography>
                  <Typography
                    fontSize={12}
                    sx={{
                      width: "156px",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                      whiteSpace: "nowrap",
                      textAlign: "center",
                    }}
                  >
                    {signatureFile ? signatureFile.name : "Upload Signature"}
                  </Typography>
                  <div
                    style={{
                      display: "flex",
                      gap: "24px",
                      width: "100%",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Button sx={{ fontSize: "20px", color: "blue", mt: 1, fontWeight: "bold" }}>
                      <label htmlFor="signaturePic">
                        <IoCloudUploadOutline />
                      </label>
                    </Button>
                    <input
                      id="signaturePic"
                      onChange={handleSignatureChange}
                      type="file"
                      accept="image/png, image/jpeg, image/jpg"
                      style={{ cursor: "pointer", display: "none" }}
                    />
                    <Button
                      onClick={removeSignature}
                      sx={{ color: "red", fontSize: "20px" }}
                    >
                      <IoTrashBin />
                    </Button>
                  </div>
                  {filesizeAlert && (
                    <Typography style={{ fontSize: ".8rem", color: "red", marginBottom: ".2rem" }}>
                      Max file size 2MB
                    </Typography>
                  )}
                </div>
              </div>
              <Typography component="p" variant="h6" style={{ display: "flex", flexDirection: "column" }}>
                {user?.fname + " " + user?.lname}
              </Typography>
              <UserDetails
                BootstrapInput={BootstrapInput}
                setUser={setUser}
                user={user}
                handleChange={handleChange}
              />
              <Box display="flex" alignItems="center" gap={1} mt={2}>
                <Typography variant="body1">Enable Notifications</Typography>
                <Switch
                  checked={user?.notify || false}
                  onChange={handleNotifyToggle}
                  color="primary"
                />
              </Box>
              <Button
                onClick={updateUserDetails}
                variant="contained"
                style={{ marginBottom: "1rem" }}
                disabled={isUpdateUserButtonDisabled || !hasUserPUTAccess}
              >
                Update User
              </Button>
            </Box>
          </div>
        </>
      )}
    </div>
  );
};

export default Form;