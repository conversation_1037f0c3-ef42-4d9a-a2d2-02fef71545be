import { Box, Typography } from "@mui/material";
import React from "react";
import { useStateContext } from "../../context/ContextProvider";
import { sharedCss } from "../../styles/sharedCss";
const PageHeader = ({ title, subText }) => {
  const { currentMode } = useStateContext();
  const commonCss = sharedCss();

  return (
    <div className={commonCss?.headingContainer}>
      <Box>
        <Typography variant="h4">{title}</Typography>
        <Typography variant="subtitle1">{subText}</Typography>
      </Box>
    </div>
  );
};

export default PageHeader;

// const styles = {
//   title: {
//     color: "#3C4048",
//     fontSize: "24px",
//     fontWeight: "bold",
//     mb: 1,
//   },
//   subText: {
//     color: "#6B728E",
//     fontSize: "16px",
//     fontWeight: "light",
//   },
//   darkTitle: {
//     color: "white",
//     fontSize: "24px",
//     fontWeight: "bold",
//     mb: 1,
//   },
//   darkSubText: {
//     color: "#EFF5F5",
//     fontSize: "16px",
//     fontWeight: "light",
//   },
// };
