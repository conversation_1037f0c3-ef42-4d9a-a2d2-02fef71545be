import { TextField } from "@mui/material";
import {
  FIELD_TYPE_TO_ONCHANGE_FUNCTION,
  REPORT_FIELD_TYPE,
  TIME_VARIATION_UNITS_TYPE,
} from "../../features/equipment_master/calibration/newSetPointList/utils";
import PropTypes from "prop-types";

export const TimeVarInput = ({
  label = "Label",
  defaultValue,
  value,
  onChange = (value, name) => {},
  name,
  disabled,
  sx = {},
  fieldType,
  channelUnit,
  type = "text",
  size = "medium",
  ...props
}) => {
  if (!fieldType) {
    fieldType = REPORT_FIELD_TYPE.STANDARD;
  }
  if (!channelUnit) {
    channelUnit = TIME_VARIATION_UNITS_TYPE.HHMMSS;
  }

  const inputValue = value;

  const handleOnChange = (e) => {
    const value =
      fieldType === REPORT_FIELD_TYPE.TIME_VARIATION
        ? FIELD_TYPE_TO_ONCHANGE_FUNCTION[fieldType][channelUnit](e)
        : FIELD_TYPE_TO_ONCHANGE_FUNCTION[fieldType](e);
    // console.log(value, name)
    if (value === null) {
      return;
    } else {
      const stringValue = value.toString();
      onChange(stringValue, name);
    }
  };

  return (
    <TextField
      label={label}
      size={size}
      sx={{
        width: "15%",
        ...sx,
      }}
      type={type}
      name={name}
      value={inputValue}
      onChange={handleOnChange}
      defaultValue={defaultValue}
      placeholder={channelUnit}
      disabled={disabled}
      {...props}
    />
  );
};

TimeVarInput.propTypes = {
  label: PropTypes.string,
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  name: PropTypes.string,
  disabled: PropTypes.bool,
  sx: PropTypes.object,
  fieldType: PropTypes.oneOf(Object.values(REPORT_FIELD_TYPE)),
  channelUnit: PropTypes.oneOf(Object.values(TIME_VARIATION_UNITS_TYPE)),
  type: PropTypes.string,
  size: PropTypes.oneOf(["small", "medium", "large"]),
};
