import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import React, { useState } from "react";
import { useEffect } from "react";
import { useParams } from "react-router-dom";
import {
  companies,
  companyId_constant,
  fatReport,
  machines,
} from "../../../constants/data";
import { db } from "../../../firebase";
import { firebaseLooper } from "../../../tools/tool";
import "./contentPagePrint.scss";
import ReportItemPrint from "./ReportItemPrint";
import { useStateContext } from "../../../context/ContextProvider";

const DataReportsPrint = ({ type }) => {
  const { reportId } = useParams(); //ID of reports from which the fatData has to be fetched
  const [reportDetails, setReportDetails] = useState([]);
  const [reportInfo, setReportInfo] = useState([]);
  const [reportTableDetails, setReportTableDetails] = useState([]);
  const [approvalTable, setApprovalTable] = useState([]);

  const [machineAll, setMachineAll] = useState([]);
  const [companyInfo, setCompanyInfo] = useState({});

  const { currentMode } = useStateContext();

  // const databaseCollection = db.collection(companies).doc(companyId_constant)
  //     .collection(fatReport).doc(reportId).collection(`fatData`);

  // const databaseReportInfo = db.collection(companies).doc(companyId_constant)
  //     .collection(fatReport).doc(reportId)

  // const DatabaseMachines = db.collection(companies).doc(companyId_constant)
  //     .collection(machines);

  // const DatabaseCompanyInfo = db.collection(companies).doc(companyId_constant);

  //Fetch fat Data from the collection fatReportData
  useEffect(() => {
    // DatabaseMachines.onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     setMachineAll(data);
    //     //console.log("dataReport machines:", data)
    // })
    // DatabaseCompanyInfo.get().then((snap) => {
    //     setCompanyInfo(snap.data())
    // })
    // //
    // databaseReportInfo.get().
    //     then((snapshot) => { setReportInfo(snapshot.data()) }).
    //     catch((e) => console.log("report info failed:[DataReportsPrint]", e))
    // //Fetching all the data from the fatData collection inside reportId document and storing in a state
    // databaseCollection
    //     .onSnapshot(snap => {
    //         const data = firebaseLooper(snap)
    //         data.sort(function (a, b) { return (a.index - b.index) })
    //         setReportDetails(data)
    //         //using snap : fetch table details of the collection i.e
    //         snap.forEach((record) => {
    //             //catching each record in the fn
    //             databaseCollection.doc(record.id).collection('approvalTable') //Approval table for only 1 section - Pre approvals
    //                 .onSnapshot(snap => {
    //                     const data = firebaseLooper(snap)
    //                     setApprovalTable(data)
    //                 })
    //         })
    //     })
  }, []); //reportId changes automatically reIterate data from the db

  //console.log(reportTableDetails)

  const background = {
    background: currentMode === "Dark" ? "#fff" : "#fff",
    color: currentMode === "Dark" ? "#333 " : "#333",
    border: currentMode === "Dark" ? "2px solid #2b2b2b" : "2px solid #2b2b2b",
  };
  return (
    <>
      <section className="contentViewPrintPage bg-white">
        {reportDetails.map((data) => (
          <>
            {/*  below div is very very important for border in PRINT . margin b/w pages used to be removed while printing.
                             So this div is giving top padding in pages. "h-4"
                        */}
            <div className=" h-4"></div>

            <div className="outerMainConatiner">
              <div className="allContentPreviewContainerPrintPage">
                <header
                  className="contentPrintPageHeading font-thin text-xs"
                  style={background}
                >
                  <div className="contentHeading_left">
                    <div className=" p-1">
                      <img src={companyInfo.url} width="100" />
                    </div>
                    <div
                      style={{
                        border: "1px solid black",
                        height: "8rem",
                        backgroundColor: "#111",
                      }}
                    ></div>{" "}
                    {/* height:"8rem*/}
                    <div className="contentHeadingInfoLeft">
                      <div className="pl-1">
                        <div className="font-bold text-2xl ">
                          {
                            machineAll?.filter(
                              (data) => data?.id === reportInfo?.mid,
                            )[0]?.title
                          }
                        </div>

                        {/* <div> <span className="text-lg font-medium ">Description:</span> 
                                                <span className="text-xl font-bold "> {data?.desc ? data?.desc : "N/A" } </span>
                                                 </div> */}

                        <div>
                          {" "}
                          <span className="text-lg font-medium ">Model:</span>
                          <span className="text-xl font-bold ">
                            {" "}
                            {
                              machineAll?.filter(
                                (data) => data?.id === reportInfo?.mid,
                              )[0]?.model
                            }{" "}
                          </span>
                        </div>
                        <div>
                          {" "}
                          <span className="text-lg font-medium">
                            Serial No:
                          </span>
                          <span className="text-xl font-bold ">
                            {
                              machineAll?.filter(
                                (data) => data?.id === reportInfo?.mid,
                              )[0]?.serialNo
                            }{" "}
                          </span>
                        </div>
                        <div>
                          {" "}
                          <span className="text-lg font-medium">
                            Protocol No:
                          </span>
                          <span className="text-xl font-bold ">
                            {" "}
                            {reportInfo?.protocol_no}{" "}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="contentHeading_right">
                    <div className="contentHeadingInfoRight">
                      {/* <div className="modelNo">
                                                <span> <b>Model No: </b> </span>{machineAll?.filter((data) => data?.id == reportInfo?.mid)[0]?.model}
                                            </div> */}

                      <div className="dataTime">
                        <span className="text-xl font-bold ">
                          {" "}
                          {reportInfo.date?.toString().substring(0, 15)}{" "}
                        </span>
                      </div>
                    </div>
                  </div>
                </header>
              </div>

              <ReportItemPrint type={type} data={data} />
            </div>
          </>
        ))}
      </section>
    </>
  );
};

export default DataReportsPrint;
