import React, { useState, useEffect } from "react";
import { TextField } from "@mui/material";
import {
  Button,
  Select,
  MenuItem,
  InputLabel,
  LinearProgress,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CloseIcon from "@mui/icons-material/Close";
import { db } from "../../../firebase";
import { firebaseLooper } from "../../../tools/tool";
import { companies, companyId_constant } from "../../../constants/data";
import { toastMessage, toastMessageSuccess } from "../../../tools/toast";
import { useParams } from "react-router-dom";
import { useStorage } from "../../../hooks/useStorage";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { useAuth } from "../../../hooks/AuthProvider";

const AddApproval = ({
  collectionId,
  type,
  handleClose,
  index,
  machineName,
  reportTitle,
}) => {
  const [signature, setSignature] = useState("");
  const [name, setName] = useState("");
  const types = ["image/png", "image/jpeg", "image/jpg"];
  const [file, setFile] = useState(null);
  const { currentUser } = useAuth();
  const { docId } = useParams();
  const [user, setUser] = useState([]);
  const [approverType, setApproverType] = useState("");

  const moduleName = type === "fatData" ? "FAT" : "SAT";

  const handleSubmit = (e) => {
    e.preventDefault();
    // if(name.trim() === "") {
    //   toastMessage({message: "Empty inputs are invalid!"})
    //   return
    // }
    const docdata =
      name && url
        ? { signature: url, name, index, date: new Date(), approverType }
        : { signature: url, name, index, approverType }; // now blank aproval can be added
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(type)
    //   .doc(collectionId)
    //   .collection('approvalTable')
    //   .add(docdata)
    //   .then((data) => {
    //     handleClose()
    //     LoggingFunction(
    //       machineName,
    //       reportTitle,
    //       `${user?.fname} ${user?.lname}`,
    //       moduleName,
    //       `NewApproval Signature is added to  ${moduleName}`
    //     )
    //     toastMessageSuccess({ message: "Added details for documentation Successfully !" })
    //   })
  };

  const handleChange = (e) => {
    let selectedFile = e.target.files[0];

    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG)",
        });
      }
    }
  };

  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection("userData")
    //   .where("email", "==", currentUser.email)
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     setUser(data[0]);
    //   });
  }, []);

  const { progress, url } = useStorage(file);
  return (
    <form onSubmit={handleSubmit}>
      <InputLabel>Name</InputLabel>
      <TextField
        onChange={(e) => setName(e.target.value)}
        style={{ marginBottom: "10px" }}
        variant="outlined"
        fullWidth
        placeholder="Full Name (not cumpulsory)"
      />
      <InputLabel htmlFor="approverType">Approver Type</InputLabel>
      <Select
        required
        displayEmpty
        name="approverType"
        value={approverType}
        placeholder="Select Approver Type (cumpulsory)"
        onChange={(e) => setApproverType(e.target.value)}
        variant="outlined"
        fullWidth
        style={{ marginBottom: "10px" }}
        renderValue={(selected) => {
          if (selected === "") {
            return (
              <em style={{ opacity: 0.3 }}>
                Please Select Approver Type (cumpulsory)
              </em>
            );
          }
          return (
            <span style={{ textTransform: "capitalize" }}>{selected}</span>
          );
        }}
      >
        <MenuItem disabled value="">
          <em>Please Select Approver Type</em>
        </MenuItem>
        <MenuItem value="company">Company</MenuItem>
        <MenuItem value="customer">Customer</MenuItem>
      </Select>

      <InputLabel>Signature</InputLabel>
      <input
        // required
        placeholder="(not cumpulsory)"
        type="file"
        onChange={handleChange}
      />
      <div></div>
      <br />
      {url && <img width="300px" src={url} alt="signature" />}
      <LinearProgress value={progress} variant="determinate" />
      <div className="mt-10 flex justify-between">
        <Button
          onClick={handleClose}
          variant="contained"
          color="error"
          endIcon={<CloseIcon />}
        >
          Cancel{" "}
        </Button>
        <Button type="submit" variant="contained" endIcon={<AddIcon />}>
          ADD Approval Signature{" "}
        </Button>
      </div>
    </form>
  );
};

export default AddApproval;
