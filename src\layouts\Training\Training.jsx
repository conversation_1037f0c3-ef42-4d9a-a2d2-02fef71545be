import { <PERSON><PERSON>, <PERSON><PERSON>, DialogContent, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { companies, companyId_constant, training } from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";

import AddTraining from "./AddTraining";
import MainItem from "./TrainingItem";

const Training = () => {
  const [maintenance, setMaintenance] = useState([]);
  const [open, setOpen] = useState(false);
  const { mid } = useParams();
  const handletrainingdata = async () => {
    await axios
      .get(`${dbConfig.url}/training`)
      .then((response) => {
        console.log(response?.data, "ye training data");
        if (response?.data.mid == mid) {
          setMaintenance(response?.data);
          console.log("ye function chal rha hai");
        }
      })
      .catch((error) => {
        console.log("ERROR IN MAINTENANCE:", error.message);
      });
  };
  useEffect(() => {
    // db.collection(companies).doc(companyId_constant)
    // .collection(training).where('mid', '==',mid)
    // .onSnapshot(snap => {
    //     const data = firebaseLooper(snap)
    //     setMaintenance(data)
    //     console.log("this is training data",data)
    // })
    handletrainingdata();
  }, []);

  return (
    <div className="flex mt-12 flex-col">
      <div className="bg-gray-50 rounded flex justify-end">
        <Button onClick={() => setOpen(true)} variant="outlined">
          Add Data
        </Button>
      </div>
      <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
          <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Name
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Description
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Status
                  </th>

                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {maintenance.map((data) => (
                  <MainItem data={data} key={data.id} />
                ))}
              </tbody>
            </table>
          </div>
          <Dialog fullWidth open={open}>
            <div className="p-2">
              <Typography variant="h5" align="center" gutterBottom>
                Add Training Modules
              </Typography>
              <DialogContent>
                <AddTraining mid={mid} handleClose={() => setOpen(false)} />
              </DialogContent>
            </div>
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default Training;
