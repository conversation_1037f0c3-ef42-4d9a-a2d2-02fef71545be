import React, { useContext, useState } from "react";
import axios from "axios";
// import { useUserInfo } from './AuthProvider';
// import { useServerConnection } from './ServerConnetionContext';
// import { useUserByIdBasicInfo } from './UserContext';
// import { toastMessage } from '../tost';
import { dbConfig } from "../../infrastructure/db/db-config";

// const BatchDataAllContext = React.createContext();
// const BatchDataAllSetterContext = React.createContext();
// const BatchUpdateContext = React.createContext();
// const BatchDataSingleContext = React.createContext();
// const BatchDeleteContext = React.createContext();
//
const UserActivityContext = React.createContext();

//

export function useUserActivity() {
  return useContext(UserActivityContext);
}

// export function useBatchAll() {
//   return useContext(BatchDataAllContext);
// }

// export function useBatchAllSetter() {
//   return useContext(BatchDataAllSetterContext);
// }

// export function useBatchUpdate() {
//   return useContext(BatchUpdateContext);
// }

// export function useBatchDataSingleByName() {
//   return useContext(BatchDataSingleContext);
// }

// export function useBatchDeleteByBatchNo() {
//   return useContext(BatchDeleteContext);
// }
//
export function UserCfrContextProvider({ children }) {
  //   const [batchDataAll, setBatchDataAll] = useState();
  //   const [login, setLogin] = useState();
  //   const userInfo = useUserInfo(); // it's an object
  //   const [batchDataSingle, setBatchDataSingle] = useState([{ comment: [] }]);

  //   const [userAssignedTo, setUserAssignedTo] = useState([]);
  //   const [userAssignedBy, setUserAssignedBy] = useState([]);

  //   const [ip, setIpHandler, port, setPortHandler] = useServerConnection();
  // const [userBasicInfo, handleUserByIdBasicSingle] = useUserByIdBasicInfo();

  //

  function handleUserActivity(sessionData) {
    const UpdateActivity = async (sessionData) => {
      await axios
        .post(`${dbConfig.url}/cfr/userSessions`, sessionData)
        .then((response) => {
          console.log("cfrLogoutdata", sessionData);
        })
        .catch((error) => {
          console.error(error);
        });
    };

    UpdateActivity(sessionData);
  }

  // function handleBatchDataAll() {
  //     // const [batchAll, setBatchAll] = useState([]);

  //     const fetchBatchAll = async () => {
  //         const response = await axios(
  //             {
  //                 method: 'get',
  //                 url: 'http://' + ip + ":" + port + '/getAllBatch',
  //                 headers: {
  //                     "Authorization": `Bearer ${userInfo?.email}`,
  //                     'Content-Type': 'application/x-www-form-urlencoded'
  //                 }
  //                 //data: order
  //             }
  //         );

  //         if (true) {
  //             setBatchDataAll(response.data?.message?.reverse());
  //             console.group("responce:", response)
  //         }
  //     }

  //     fetchBatchAll();
  // }

  // function handleBatchUpdate(batchNo, batchDataNew) {
  //     //let batchNo = batchNo;
  //     // let batchDataNew = batchDataNew;
  //     const updateBatch = async (batchNo, batchDataNew) => {
  //         console.log('update batch', batchNo, batchDataNew)
  //         const response = await axios(
  //             {
  //                 method: 'PUT',
  //                 url: 'http://' + ip + ":" + port + '/updateBatch/' + batchNo,
  //                 headers: {
  //                     "Authorization": `Bearer ${userInfo?.email}`,
  //                 },
  //                 data: { ...batchDataNew },

  //             }
  //         );

  //         if (true) {
  //             //setBatchDataAll(response.data?.message);
  //             console.group("responce:", response)
  //         }
  //     }

  //     updateBatch(batchNo, batchDataNew);
  // }

  // function handleBatchDataSingle(batchNo) {
  //     // const [batchAll, setBatchAll] = useState([]);

  //     const fetchBatchDataSingle = async (batchNo) => {
  //         const response = await axios(
  //             {
  //                 method: 'get',
  //                 url: 'http://' + ip + ":" + port + '/getBatchByName/' + batchNo,
  //                 headers: {
  //                     "Authorization": `Bearer ${userInfo?.email}`,
  //                     'Content-Type': 'application/x-www-form-urlencoded'
  //                 }
  //                 //data: order
  //             }
  //         );

  //         if (response) { // true
  //             setBatchDataSingle(response.data?.message);
  //             console.log("batch data single: ", response.data?.message)

  //             // converting user id to object of user detail. if needed then more can be added
  //             if (response.data?.message[0]?.assigned_to) {
  //                 axios(
  //                     {
  //                         method: 'GET',
  //                         // url: 'http://' + ip + ":" + port + '/getUserBasicById/' + response.data?.message[0]?.assigned_to,
  //                         url: 'http://' + ip + ":" + port + '/getUserBasicByEmail/' + response.data?.message[0]?.assigned_to,
  //                         headers: {
  //                             "Authorization": `Bearer ${userInfo?.email}`,
  //                             'Content-Type': 'application/x-www-form-urlencoded'
  //                         },
  //                         //data: order
  //                     }
  //                 ).then((res) => {
  //                     console.log("user single basic batchContext", res?.data);
  //                     setUserAssignedTo(res?.data.message)
  //                 })
  //                 console.group("responce single batch:", response)
  //             }
  //         }
  //     }

  //     fetchBatchDataSingle(batchNo);
  // }

  // function handleBatchDeleteSingle (batch_no) {
  //     const deleteBatch = async () => {
  //         const response = await axios(
  //             {
  //                 method: 'delete',
  //                 url: 'http://' + ip + ":" + port + '/deleteBatch/' + batch_no,
  //                 headers: {
  //                     "Authorization": `Bearer ${userInfo?.email}`,
  //                     'Content-Type': 'application/x-www-form-urlencoded'
  //                 }
  //                 //data: order
  //             }
  //         );

  //         if (true) {
  //             toastMessage({message: batch_no + " deleted successfully"})
  //             console.group("responce:", response);
  //         }
  //     }

  //     deleteBatch();
  //}

  return (
    <UserActivityContext.Provider value={handleUserActivity}>
      {children}
    </UserActivityContext.Provider>
  );
}
