import { Debugout } from "debugout.js";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";

const bugout = new Debugout();
const LOGS_KEY = "unsentLogs";
const LOG_INTERVAL = 5 * 60 * 1000; // Send logs every 5 minutes

// Function to save log entries in sessionStorage
function saveLog(logEntry) {
  const logs = JSON.parse(sessionStorage.getItem(LOGS_KEY)) || [];
  logs.push(logEntry);
  sessionStorage.setItem(LOGS_KEY, JSON.stringify(logs));
}

// Modified log functions to save logs locally and use bugout for in-app viewing
bugout.logInfo = function (message, details) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level: "INFO",
    source: "Frontend",
    message,
    details,
  };
  saveLog(logEntry);
  bugout.log(JSON.stringify(logEntry)); // Original bugout logging for frontend view
};

bugout.logError = function (message, details) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level: "ERROR",
    source: "Frontend",
    message,
    details,
  };
  saveLog(logEntry);
  bugout.error(JSON.stringify(logEntry)); // Original bugout logging for frontend view
};

// Function to send logs to backend and clear sessionStorage after successful send
async function sendLogsToBackend() {
  const logs = JSON.parse(sessionStorage.getItem(LOGS_KEY)) || [];
  if (logs.length === 0) return;

  try {
    await axios.post(`${dbConfig.url}/envdata/append_logs`, { logs }); // Adjust backend endpoint as necessary
    sessionStorage.removeItem(LOGS_KEY); // Clear logs after successful send
  } catch (error) {
    console.error("Failed to send logs to backend:", error);
  }
}

// Periodic log sending
setInterval(sendLogsToBackend, LOG_INTERVAL);

// Attempt to send logs when the tab is closed
window.addEventListener("beforeunload", sendLogsToBackend);

export default bugout;
