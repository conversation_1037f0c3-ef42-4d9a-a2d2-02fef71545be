import React, {useState, useEffect} from "react";
import {toastMessage, toastMessageSuccess} from "../../../tools/toast";
import {
  TableCell,
  TableRow,
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  Box,
  Tabs,
  Tab,
  Chip,
  Tooltip,
  CircularProgress,
  Menu,
  MenuItem as MuiMenuItem,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import BuildIcon from "@mui/icons-material/Build";
import Delete from "../../../components/Delete/Delete";
import {useStateContext} from "../../../context/ContextProvider";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import {useMaintenanceInfo} from "../../../context/MaintenanceContext";
import {useUser} from "../../../context/UserContext";
import moment from "moment";
import axios from "axios";
import {dbConfig} from "../../../infrastructure/db/db-config";
import SelectedComponent from "../../../components/reports/SelectedComponent";
import {useMongoRefresh} from "../../../services/mongo-refresh.context";
import {Download, PictureAsPdf} from "@mui/icons-material";
import NoDataComponent from "../../../components/commons/noData.component";
import {commonRowStyle} from "../MaintenanceReportDataMain";
import {toast} from "react-toastify";
import AutoAwesomeIcon from "@mui/icons-material/AutoAwesome";
import {PerformModal} from "../../../components/reports/PerformModal";
import {useCheckAccess} from "../../../utils/useCheckAccess";
import NotAccessible from "../../../components/not-accessible/not-accessible";
import EditAlarmSopReport from "./EditAlarmSopReport";

const AlarmSopReportDataItem = ({
  data,
  machineData,
  stepData,
  reset,
  setReset,
  setAlarmSopReportDataAll,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openAiDialog, setOpenAiDialog] = useState(false);
  const [allSortKeys, setAllSortKeys] = useState([]);
  const [openPerformModal, setOpenPerformModal] = useState(false); // State for PerformModal
  const [carousalData, setCarousalData] = useState([]); // Unused but kept for consistency
  const {currentMode} = useStateContext();
  const [enlarge, setEnlarge] = useState(false);
  const [eValue, setEValue] = useState("");
  const [remarks, setRemarks] = useState(""); // Changed to remarks
  const maintenanceInfoFromContext = useMaintenanceInfo();
  const [selectedStep, setSelectedStep] = useState(0);
  const {refreshCount, setRefreshCount} = useMongoRefresh();
  const user = useUser();
  const [pdfGenrationActive, setPdfGenrationActive] = useState(false);

  const [anchorEl, setAnchorEl] = useState(null);
  const openMenu = Boolean(anchorEl);

  const hasAlarmSopReportPUTAccess = useCheckAccess("alarmReport", "PUT");
  const hasAlarmSopReportDELETEAccess = useCheckAccess("alarmReport", "DELETE");
  const hasAlarmSopReportStepGETAccess = useCheckAccess(
    "alarmReportSteps",
    "GET",
  );

  const handleMenuOpen = event => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  useEffect(() => {
    if (!data?._id) return;

    axios
      .get(`${dbConfig.url}/alarmReportSteps/getFromReport/${data?._id}`)
      .then(res => {
        let dataNow = res?.data?.data;

        // Sort data by sortKey
        if (Array.isArray(dataNow)) {
          dataNow = [...dataNow].sort(
            (a, b) => (a.sortKey ?? 0) - (b.sortKey ?? 0),
          );
        }

        setCarousalData(dataNow);

        // Extract and store sort keys
        const sortKeys = Array.isArray(dataNow)
          ? dataNow
              .map(step => step.sortKey)
              .filter(k => typeof k === "number" && !isNaN(k))
          : [];
        setAllSortKeys(sortKeys);

        console.log("Training report step data from mongo:", dataNow);
      })
      .catch(e => {
        console.log("Error fetching training report step data:", e);
        setCarousalData([]);
        setAllSortKeys([]);
      });
  }, [data, refreshCount]);

  useEffect(() => {
    setRemarks(stepData[selectedStep]?.remarks || ""); // Changed to remarks
  }, [selectedStep, stepData]);

  const machineName = machineData.filter(
    machine => machine?.id === data?.mid,
  )[0]?.title;

  async function deleteData() {
    await axios.delete(`${dbConfig.url}/alarmReport/${data?._id}`).then(() => {
      setOpenDel(false);
      toastMessage({message: `Deleted ${data?.title} successfully!`});
      setRefreshCount(refreshCount + 1);
      setReset(reset + 1);
      setAlarmSopReportDataAll(prev =>
        prev.filter(item => item._id !== data._id),
      );
    });
  }

  const uOrA = async updatedItem => {
    var manualId;
    const document = {
      ...updatedItem,
      date: new Date(updatedItem.date),
      updated_at: new Date().toISOString(),
      sub_steps:
        updatedItem.sub_steps !== undefined ? updatedItem.sub_steps : false,
    };

    await axios
      .put(`${dbConfig.url}/alarmReportSteps/${updatedItem._id}`, document)
      .then(res => {
        manualId = res.data.data.manual_id;
        setReset(reset + 1);
        setRefreshCount(refreshCount + 1);
        toastMessageSuccess({message: "Updated Remarks Successfully"});
        setCarousalData(prev =>
          prev.map(item =>
            item._id === updatedItem._id ? {...item, ...updatedItem} : item,
          ),
        );
      })
      .catch(err => {
        console.error("Error updating remarks:", err);
      });

    // Check all steps' remarks for this report
    try {
      const stepsRes = await axios.get(
        `${dbConfig.url}/alarmReportSteps/getFromReport/${manualId}`,
      );
      const steps = stepsRes?.data?.data || [];
      const allFilled =
        steps.length > 0 &&
        steps.every(
          step =>
            step.remarks !== "" &&
            step.remarks !== null &&
            step.remarks !== "NA",
        );

      if (allFilled) {
        await axios
          .put(`${dbConfig.url}/alarmReport/${manualId}`, {report_status: 1})
          .then(() => {
            // Update the parent component's state
            setAlarmSopReportDataAll(prev =>
              prev.map(item =>
                item._id === manualId ? {...item, report_status: 1} : item,
              ),
            );
            setRefreshCount(refreshCount + 1);
            toastMessageSuccess({
              message: "Report Status Updated Successfully",
            });
          })
          .catch(err => {
            console.error("Error updating report status:", err);
          });
      }
    } catch (err) {
      console.error("Error checking all step remarks:", err);
    }
  };

  const handleDownloadPDF = async () => {
    setPdfGenrationActive(true);
    try {
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const res = await axios.get(
        `${dbConfig.url}/alarmReportSteps/pdf/export?manual_id=${
          data?._id
        }&timezone=${encodeURIComponent(timeZone)}`,
      );
      console.log("PDF response:", res);
      console.log("timezone:", timeZone);
      window.open(`${dbConfig.url}/${res.data.data}`, "_blank");
    } catch (err) {
      console.error(err);
      toastMessage({
        message: err?.response?.data?.message ?? "Error generating PDF",
      });
    } finally {
      setPdfGenrationActive(false);
    }
  };

  return (
    <>
      <TableRow sx={commonRowStyle}>
        <TableCell
          style={
            currentMode === "Dark"
              ? {color: "white", borderBottom: "1px solid white"}
              : {borderBottom: "1px solid #e0e0e0"}
          }
          align="left">
          <b className="capitalize">{data?.title}</b>
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? {color: "white", borderBottom: "1px solid white"}
              : {borderBottom: "1px solid #e0e0e0"}
          }
          align="left">
          {moment(data?.date).format("DD MMM YYYY")}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? {color: "white", borderBottom: "1px solid white"}
              : {borderBottom: "1px solid #e0e0e0"}
          }
          align="left">
          <b className="capitalize" data-title={data?.email}>
            {data?.email.split("@")[0]}
          </b>
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? {color: "white", borderBottom: "1px solid white"}
              : {borderBottom: "1px solid #e0e0e0"}
          }
          align="left">
          {data?.remarks}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? {color: "white", borderBottom: "1px solid white"}
              : {borderBottom: "1px solid #e0e0e0"}
          }
          align="center">
          <span className="font-bold">
            {data?.report_status === 0 && (
              <span className="text-yellow-500">In Progress</span>
            )}
            {data?.report_status === 1 && (
              <span className="text-blue-500">Ready for Review</span>
            )}
            {data?.report_status === 2 && (
              <span className="text-green-500">Reviewed</span>
            )}
          </span>
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? {color: "white", borderBottom: "1px solid white"}
              : {borderBottom: "1px solid #e0e0e0"}
          }
          align="center">
          <div className="dataBtns">
            <IconButton
              onClick={() => setOpenEdit(true)}
              disabled={!hasAlarmSopReportPUTAccess}
              sx={{
                color: !hasAlarmSopReportPUTAccess
                  ? "grey.500"
                  : "primary.main",
              }}>
              <EditIcon style={{fontSize: "20px"}} />
            </IconButton>

            <IconButton onClick={handleMenuOpen}>
              <MoreVertIcon
                style={{
                  fontSize: "20px",
                  color: currentMode === "Dark" ? "#fff" : "#000",
                }}
              />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={openMenu}
              onClose={handleMenuClose}
              PaperProps={{
                style: {
                  backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
                  color: currentMode === "Dark" ? "#fff" : "#000",
                },
              }}>
              <MuiMenuItem
                onClick={() => {
                  setOpenDel(true);
                  handleMenuClose();
                }}
                disabled={!hasAlarmSopReportDELETEAccess}>
                <DeleteIcon
                  style={{
                    fontSize: "20px",
                    color: "#f00",
                    marginRight: "8px",
                  }}
                />
                Delete
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenAiDialog(true);
                  handleMenuClose();
                }}>
                <AutoAwesomeIcon
                  style={{
                    fontSize: "20px",
                    color: "forestgreen",
                    marginRight: "8px",
                  }}
                />
                Al Chat and Analysis
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  handleDownloadPDF();
                  // Do not close the menu immediately to show loading state
                }}
                disabled={pdfGenrationActive || !hasAlarmSopReportStepGETAccess} // Disable the menu item while loading
              >
                {pdfGenrationActive ? (
                  <CircularProgress size={20} style={{marginRight: "8px"}} />
                ) : (
                  <PictureAsPdf
                    style={{
                      fontSize: "20px",
                      color: "red",
                      marginRight: "8px",
                    }}
                  />
                )}
                {pdfGenrationActive ? "Generating PDF..." : "Download PDF"}
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenPerformModal(true);
                  handleMenuClose();
                }}>
                <BuildIcon
                  style={{
                    fontSize: "20px",
                    color: "#2196f3",
                    marginRight: "8px",
                  }}
                />
                Perform
              </MuiMenuItem>
            </Menu>
            <IconButton
              onClick={() => setIsOpen(!isOpen)}
              style={currentMode === "Dark" ? {color: "white"} : {}}>
              {isOpen ? (
                <ExpandLessIcon style={{fontSize: "20px"}} />
              ) : (
                <ExpandMoreIcon style={{fontSize: "20px"}} />
              )}
            </IconButton>
          </div>
        </TableCell>
      </TableRow>

      {isOpen &&
        (carousalData?.length > 0 ? (
          <TableRow sx={{"&:last-child td, &:last-child th": {border: 0}}}>
            <TableCell colSpan={6}>
              <Box
                sx={{
                  padding: 2,
                  backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
                  border:
                    currentMode === "Dark"
                      ? "1px solid white"
                      : "1px solid black",
                }}>
                <Box sx={{display: "flex", gap: "20px"}}>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      flexWrap: "wrap",
                      gap: 1, // Space between buttons
                      maxHeight: "300px", // Limit height with scroll if needed
                      overflowY: "auto", // Scroll if too many steps
                    }}>
                    {carousalData?.length > 0 &&
                    hasAlarmSopReportStepGETAccess ? (
                      carousalData.map((item, idx) => (
                        <Chip
                          key={idx}
                          label={`Step ${idx + 1}`}
                          onClick={() => setSelectedStep(idx)}
                          color={selectedStep === idx ? "primary" : "default"}
                          sx={{
                            borderRadius: "50%", // Circular buttons
                            width: "50px", // Fixed width for uniformity
                            height: "50px", // Fixed height
                            fontSize: "12px", // Smaller text
                            backgroundColor:
                              item.remarks === null ||
                              item.remarks === "NA" ||
                              item.remarks === ""
                                ? "#E78895"
                                : "#74E291",
                            "& .MuiChip-label": {
                              padding: "0", // Center text
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            },
                          }}
                        />
                      ))
                    ) : (
                      <>
                        {/*
                    <p>No steps available</p>
                    */}
                        {null}
                      </>
                    )}
                  </Box>
                  <Box sx={{flexGrow: 1}}>
                    {hasAlarmSopReportStepGETAccess ? (
                      carousalData[selectedStep] && (
                        <SelectedComponent
                          index={selectedStep}
                          key={carousalData[selectedStep]._id}
                          item={carousalData[selectedStep]}
                          setRemarks={setRemarks} // Changed to setRemarks
                          setEValue={setEValue}
                          setEnlarge={setEnlarge}
                          remarks={remarks} // Changed to remarks
                          uOrA={uOrA}
                        />
                      )
                    ) : (
                      <NotAccessible />
                    )}
                  </Box>
                </Box>
              </Box>
            </TableCell>
          </TableRow>
        ) : hasAlarmSopReportStepGETAccess ? (
          <NoDataComponent
            noDataMessage={"No Steps Available"}
            paddText={true}
            padding={"12px"}
          />
        ) : (
          <NotAccessible />
        ))}

      <Dialog open={openDel}>
        <Delete onClose={() => setOpenDel(false)} onDelete={deleteData} />
      </Dialog>
      <Dialog open={openEdit} fullWidth>
        <DialogTitle>Edit Report [{data?.title}]</DialogTitle>
        <DialogContent>
          <EditAlarmSopReport
            handleClose={() => setOpenEdit(false)}
            mid={data.mid}
            data={data}
            userName={`${user?.fname} ${user?.lname}`}
            reportName={data.title}
            machineName={machineName}
          />
        </DialogContent>
      </Dialog>
      <Dialog
        open={enlarge}
        onClose={() => setEnlarge(false)}
        fullWidth
        maxWidth="lg">
        <DialogTitle>Enlarged Image</DialogTitle>
        <DialogContent>
          <img
            src={`${dbConfig.url_storage}/${eValue}`}
            alt="Enlarged"
            style={{width: "100%", height: "100%"}}
          />
        </DialogContent>
      </Dialog>
      <PerformModal
        rowData={data}
        currStep={selectedStep}
        carousalData={carousalData}
        setEValue={setEValue}
        setEnlarge={setEnlarge}
        handleSubmit={async (item, updatedItem) => {
          await uOrA(updatedItem);
          // Force a refresh of the data
          setAlarmSopReportDataAll(prev => [...prev]);
        }}
        open={openPerformModal}
        handleClose={() => setOpenPerformModal(false)}
        allSortKeys={allSortKeys}
      />
    </>
  );
};

export default AlarmSopReportDataItem;
