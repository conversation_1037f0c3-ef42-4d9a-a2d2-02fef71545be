import {
  <PERSON><PERSON>,
  Table<PERSON>ell,
  TableRow,
  <PERSON>alog,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import React, { useState } from "react";
import Delete from "../../components/Delete/Delete";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import { toastMessage } from "../../tools/toast";
import EditDocumentation from "./EditMaintenance/EditDocumentation";

const ReportItem = ({ data, type }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);

  const handleDelete = () => {
    // db.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(type)
    // 	.doc(data.id)
    // 	.delete()
    // 	.then(() => {
    // 		toastMessage({ message: "Deleted Successfully !" });
    // 	});
  };
  const date = new Date(data.createdAt);
  return (
    <>
      <TableRow
        sx={{
          "&:last-child td, &:last-child th": { border: 0 },
        }}
        style={{ cursor: "pointer" }}
      >
        <TableCell style={{ borderBottom: "none" }} align="left">
          {data.title}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          {data.desc}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          {data.report}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          {date.toDateString()}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          <Button onClick={() => setOpenEdit(true)}>
            <i className="ri-pencil-fill"></i>
          </Button>
          <Button onClick={() => setOpenDel(true)}>
            <i className="ri-delete-bin-6-fill text-red-700"></i>
          </Button>
          {/* <i
              className={
                isOpen
                  ? 'ri-arrow-up-s-fill'
                  : 'ri-arrow-down-s-fill'
              }
              onClick={() => setIsOpen(!isOpen)}
            ></i> */}
        </TableCell>
      </TableRow>
      <div>
        {isOpen && (
          <TableRow
            sx={{
              "&:last-child td, &:last-child th": {
                border: 0,
              },
            }}
          >
            <TableCell
              className="subData"
              style={{ borderBottom: "none" }}
              align="center"
              colSpan={4}
            ></TableCell>
          </TableRow>
        )}
      </div>
      <Dialog open={openEdit} fullWidth>
        <DialogTitle>
          Edit {type} - [{data.title}]
        </DialogTitle>
        <DialogContent>
          <EditDocumentation
            mid={data.mid}
            data={data}
            type={type}
            handleClose={() => setOpenEdit(false)}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={openDel}>
        <Delete onDelete={handleDelete} onClose={() => setOpenDel(false)} />
      </Dialog>
    </>
  );
};

export default ReportItem;
