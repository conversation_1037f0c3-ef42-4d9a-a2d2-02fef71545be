import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import React, { useState } from "react";
import {
  companies,
  companyId_constant,
  maintenanceReport,
} from "../../constants/data";

export default function AddMaintenanceReport({ handleClose }) {
  return (
    <>
      <form>
        <div className="p-2 mt-2 flex justify-between">
          <Button onClick={handleClose} variant="outlined">
            Cancel
          </Button>
          <Button type="submit" variant="outlined">
            Submit
          </Button>
        </div>
      </form>
    </>
  );
}
