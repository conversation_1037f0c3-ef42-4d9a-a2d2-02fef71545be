import "./userPage.scss";
import UserInfoContainer from "./UserInfoContainer";
import UsersListTable from "./UsersListTable";
import { makeStyles } from "@mui/styles";
import { sharedCss } from "../../styles/sharedCss";
import User from "./User";

const useCustomStyles = makeStyles((theme) => ({
  userContainer: {
    padding: "1rem",
    borderRadius: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  usersOuterContainer: {
    width: "100%",
  },
  usersInnerContainer: {
    display: "flex",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  addButton: {
    width: "max-content",
  },
  userPageContainer: {
    padding: "1rem",
    border: "1px solid gainsboro",
  },
}));

const UserPage = () => {
  const customCss = useCustomStyles();
  return (
    // <section className={customCss.userPageContainer}>
    //   <UserInfoContainer />
    //   <UsersListTable />
    // </section>
    <>
      {/* <h1>hello users</h1> */}
      <User />
    </>
  );
};

export default UserPage;
