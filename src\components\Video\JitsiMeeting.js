// components/JitsiMeeting.js
import { useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom"; // If using React Router

const JitsiMeeting = ({ roomName = "helloTeam", userName }) => {
  const containerRef = useRef(null);
  const navigate = useNavigate(); // Needed for navigation

  useEffect(() => {
    const domain = process.env.REACT_APP_JITSI_DOMAIN; // Use env variable
    const options = {
      roomName,
      width: "100%",
      height: 600,
      parentNode: containerRef.current,
      interfaceConfigOverwrite: {
        DEFAULT_REMOTE_DISPLAY_NAME: 'Guest',
        SHOW_JITSI_WATERMARK: false,
      },
      userInfo: {
        displayName: userName || "Guest",
      },
    };

    const api = new window.JitsiMeetExternalAPI(domain, options);

    // 🔑 Add this event handler to prevent going to Jitsi home after meeting ends
    api.addEventListener("readyToClose", () => {
      // You can navigate, show a modal, or do whatever you want
      navigate("/videocall"); // Make sure this route exists in your app
    });

    return () => api.dispose();
  }, [roomName, userName, navigate]);

  return <div ref={containerRef} />;
};

export default JitsiMeeting;
