import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ell, TableRow } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import InfoIcon from "@mui/icons-material/Info";
import { NavLink } from "react-router-dom";
import PrintIcon from "@mui/icons-material/Print";
import { useStateContext } from "../../context/ContextProvider";

import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import Slide from "@mui/material/Slide";
import PrintReportPdfMain, { PrintReport } from "./print/PrintReportPdfMain";
import PrintReportsPage from "../../pages/PrintReports";
import { ButtonBasicCancel } from "../../components/buttons/Buttons";
import { commonRowStyle } from "../machineData/MaintenanceReportDataMain";

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const Item = ({ data }) => {
  const { currentMode } = useStateContext();
  const [open, setOpen] = React.useState(false);
  const [showInfo, setShowInfo] = React.useState(true);

  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleShowInfo = () => {
    setShowInfo(!showInfo);
    setTimeout(() => {
      setShowInfo(false);
    }, 3000);
  };

  const handleClose = () => {
    setOpen(false);
  };

  // const printAFour = () => {
  // 	var strWindowFeatures = "location=yes,height=1000,width=1500,scrollbars=yes,status=yes"; //height=1000,width=1500 is for consuming whole screen
  // 	var URL = "/fat-reports-print/" + data.id;
  // 	window.open(URL, "_blank", strWindowFeatures);
  // }

  const printAFour = () => {
    handleClickOpen();
  };

  return (
    <>
      <TableRow sx={commonRowStyle}>
        <TableCell
          sx={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { textTransform: "none", borderBottom: "1px solid #e0e0e0" }
          }
          // style={{ padding: '10px' }}
          align="left"
        >
          {data.protocol_no}
        </TableCell>
        <TableCell
          sx={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { textTransform: "none", borderBottom: "1px solid #e0e0e0" }
          }
          // style={{ padding: '10px' }}
          align="left"
          data-title={data.description}
        >
          {data.description.slice(0, 30)}...
        </TableCell>
        <TableCell
          sx={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { textTransform: "none", borderBottom: "1px solid #e0e0e0" }
          }
          // style={{ padding: '10px' }}
          align="left"
        >
          {data.email}
        </TableCell>
        <TableCell
          sx={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { textTransform: "none", borderBottom: "1px solid #e0e0e0" }
          }
          // style={{ padding: '10px' }}
          align="left"
        >
          {data.date?.toString().substring(0, 15)}
        </TableCell>
        <TableCell
          sx={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "1px solid white" }
              : { textTransform: "none", borderBottom: "1px solid #e0e0e0" }
          }
          // style={{ padding: '10px' }}
          align="left"
        >
          <IconButton onClick={() => printAFour()}>
            <PrintIcon
              style={{ color: currentMode === "Dark" ? "#B0DFE5" : "#1D2951" }}
            />
          </IconButton>
          <IconButton component={NavLink} to={`/fat-reports/${data._id}`}>
            <ExpandMoreIcon style={{ fontSize: "20px" }} />
          </IconButton>
        </TableCell>
      </TableRow>

      <Dialog
        open={open}
        TransitionComponent={Transition}
        keepMounted
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
      >
        <DialogTitle>{"FAT-REPORT DOWNLOAD"}</DialogTitle>
        <DialogContent>
          {open && <PrintReportPdfMain reportId={data._id} />}

          {showInfo && (
            <div className=" flex justify-center text-xs">
              <div className=" text-xs ">
                <InfoIcon style={{ color: "orange", fontSize: 16 }} />
                &nbsp; Please wait when document preparation starts. <br />
                <InfoIcon style={{ color: "orange", fontSize: 16 }} />
                &nbsp; Page may remain unresponsive while preparation.
              </div>
            </div>
          )}
        </DialogContent>
        <div className="flex justify-between p-4">
          <Button onClick={handleShowInfo} title="Info...">
            <InfoIcon style={{ color: "orange" }} />
          </Button>

          <ButtonBasicCancel onClick={handleClose} buttonTitle="Cancel" />
        </div>
      </Dialog>
    </>
  );
};
export default Item;
