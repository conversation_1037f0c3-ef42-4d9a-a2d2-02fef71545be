import {
  KeyboardArrowLeftOutlined,
  KeyboardArrowRightOutlined,
} from "@mui/icons-material";
import { Button, IconButton, Typography } from "@mui/material";
import { Box } from "@mui/system";
import { useEffect, useState } from "react";
import { getEnvData } from "../../services/env";
import { useUtils } from "../../hooks/UtilsProvider";

const renderPaginationButtons = (
  currentPage,
  itemsPerPage,
  totalPages,
  setCurrentPage,
) => {
  const createButton = (label, onClick, variant) => (
    <Button
      key={label}
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        width: "2rem",
        minWidth: "2rem",
        aspectRatio: "1 / 1",
        flexShrink: "0",
        borderRadius: "0.25rem",
        fontSize: "14px",
        fontWeight: "bold",
      }}
      onClick={onClick}
      variant={variant}
    >
      {label}
    </Button>
  );

  const buttons = [];
  const maxButtons = 5;

  const addButtonsInRange = (start, end) => {
    for (let i = start; i <= end; i++) {
      buttons.push(
        createButton(
          i,
          () => {
            setCurrentPage(i);
          },
          i === currentPage ? "contained" : "outlined",
        ),
      );
    }
  };

  const startPage = Math.max(1, currentPage - Math.floor(maxButtons / 2));
  const endPage = Math.min(totalPages, startPage + maxButtons - 1);

  if (startPage > 1) {
    buttons.push(
      createButton("<", () => setCurrentPage(currentPage - 1), "outlined"),
    );
  }

  addButtonsInRange(startPage, endPage);

  if (endPage < totalPages) {
    buttons.push(
      createButton(">", () => setCurrentPage(currentPage + 1), "outlined"),
    );
  }

  return buttons;
};

const renderRowCount = (currentPage, itemsPerPage, totalRows) => {
  const firstIndex = (currentPage - 1) * itemsPerPage + 1;
  const lastIndex = Math.min(currentPage * itemsPerPage, totalRows);
  // const totalRows = data?.length;
  return `${firstIndex}-${lastIndex} of ${totalRows}`;
};

export default function PrevNextPagination({
  currentPage,
  itemsPerPage,
  totalPages,
  totalRows,
  setCurrentPage = (currPage) => console.log(currPage),
}) {
  const { envData } = useUtils();

  const isNumeric = envData?.PAGINATION_TYPE
    ? envData.PAGINATION_TYPE === "Numeric"
    : true;

  return (
    <>
      {isNumeric ? (
        <Box
          sx={{
            width: "100%",
            pb: "0.5rem",
          }}
        >
          {/* Pagination buttons */}
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              gap: "1rem",
            }}
          >
            <Box visibility={"hidden"}>{renderRowCount()}</Box>
            <Box sx={{ display: "flex", gap: "1rem", alignItems: "center" }}>
              {renderPaginationButtons(
                currentPage,
                itemsPerPage,
                totalPages,
                setCurrentPage,
              )}
            </Box>
            <Box>{renderRowCount(currentPage, itemsPerPage, totalRows)}</Box>
          </Box>
        </Box>
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {/* '<' icon Button */}
          <IconButton
            onClick={() => {
              if (currentPage > 1) {
                setCurrentPage(currentPage - 1);
              }
            }}
            disabled={currentPage === 1}
          >
            <KeyboardArrowLeftOutlined />
          </IconButton>
          {/* row count like 1-50 of 100 */}
          <Typography
            variant="h6"
            style={{
              fontSize: "0.8rem",
              marginInline: "0.5rem",
            }}
          >
            {`${currentPage * itemsPerPage - itemsPerPage + 1 > totalRows ? totalRows : currentPage * itemsPerPage - itemsPerPage + 1} - ${currentPage * itemsPerPage < totalRows ? currentPage * itemsPerPage : totalRows} of ${totalRows}`}
          </Typography>
          {/* '>' icon Button */}
          <IconButton
            onClick={() => {
              if (currentPage < totalPages) {
                setCurrentPage(currentPage + 1);
              }
            }}
            disabled={currentPage >= totalPages}
          >
            <KeyboardArrowRightOutlined />
          </IconButton>
        </Box>
      )}
    </>
  );
}
