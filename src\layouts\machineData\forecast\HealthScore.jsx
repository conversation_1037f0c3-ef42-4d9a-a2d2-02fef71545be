import React from "react";
import { Card, CardContent, Typography } from "@mui/material";

/**
 * Health Score Tile Component
 * @param {Object} props
 * @param {number[]} props.values - Array of numerical values (machine readings)
 */
const HealthScoreTile = ({ values }) => {
  if (!values || values.length === 0) return null;

  // Filter out null/undefined values
  const validValues = values.filter((v) => v !== null && v !== undefined);

  if (validValues.length < 2) return null; // Need at least 2 values for calculations

  // Calculate Mean
  const mean =
    validValues.reduce((acc, val) => acc + val, 0) / validValues.length;

  // Calculate Standard Deviation
  const variance =
    validValues.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) /
    validValues.length;
  const stdDev = Math.sqrt(variance);

  // Calculate Health Score
  const healthScore = Math.max(0, (1 - stdDev / mean) * 100).toFixed(2);

  // Determine Color
  let bgColor = "#4CAF50"; // Green
  if (healthScore < 80) bgColor = "#FFC107"; // Yellow
  if (healthScore < 50) bgColor = "#F44336"; // Red

  return (
    <Card
      sx={{
        backgroundColor: bgColor,
        height: "150px",
        color: "#fff",
        textAlign: "center",
        p: 2,
        mt: 4,
      }}
    >
      <CardContent>
        <Typography variant="h6">Health Score</Typography>
        <Typography variant="h4" fontWeight="bold">
          {healthScore}%
        </Typography>
      </CardContent>
    </Card>
  );
};

export default HealthScoreTile;
