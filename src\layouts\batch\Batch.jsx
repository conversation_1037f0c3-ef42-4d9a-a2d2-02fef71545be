import React, { useEffect, useMemo, useState } from "react";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import Carousel from "react-material-ui-carousel";
import "./Batch.scss";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import {
  batch,
  companies,
  companyId_constant,
  media,
} from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";
import { Box } from "@mui/system";
import {
  FormControl,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import BatchItems from "./BatchItems";
import { SearchOutlined } from "@mui/icons-material";
import NoDataComponent from "../../components/commons/noData.component";
import { dbConfig } from "../../infrastructure/db/db-config";

export default function Batch() {
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const [batchAll, setBatchAll] = useState([]);
  const [batchActiveMedias, setBatchActiveMedias] = useState([]);
  const [batchSelected, setBatchSelected] = useState({});
  const [batchSelectedName, setBatchSelectedName] = useState("");
  const [index, setIndex] = useState(0);
  const [searchKeyWordBatch, setSearchKeywordBatch] = useState("");

  useEffect(() => {
    // db.collection(companies).doc(companyId_constant).collection(batch)
    //     .onSnapshot((snap) => {
    //         const data = firebaseLooper(snap);
    //         setBatchAll(data);
    //         console.log("batch: ", data)
    //     })
  }, []);

  useEffect(() => {
    const batchUrl = process.env.REACT_APP_SMART_SCAN_URL;
    console.log("REACT_APP_SMART_SCAN_URL:", batchUrl); // Debug: check value at runtime
    if (
      batchUrl &&
      typeof batchUrl === "string" &&
      batchUrl.trim() !== "" &&
      !batchUrl.includes("undefined")
    ) {
      window.open(batchUrl.trim(), "_blank");
    } else {
      window.open("https://localhost:3001/", "_blank");
    }
  }, []);

  const filterBatchAll = (data) => {
    return (
      data.product_name
        ?.toUpperCase()
        .search(searchKeyWordBatch.toUpperCase()) >= 0 ||
      data.batch_no?.toUpperCase().search(searchKeyWordBatch.toUpperCase()) >= 0
    );
  };

  const isFilteredBatchDataAvailable = useMemo(
    () => batchAll.filter(filterBatchAll).length,
    [searchKeyWordBatch],
  );

  return (
    <>
      {/* <section style={currentMode === 'Dark' ? { backgroundColor: '#161C24', color: 'white' } : { backgroundColor: currentColorLight }} className="machineContainer"> */}
      <section className="userPage">
        {/* <div className='flex justify-between'> */}
        <div
          style={
            currentMode === "Dark"
              ? {
                  backgroundColor: "#161C24",
                  color: "white",
                  border: "1px solid white",
                }
              : { border: "1px solid black" }
          }
          className="userPageInfoContainer"
        >
          {/* <div>
                        <div className="title">
                            <h3></h3>
                        </div>
                        <div className="desc">
                            <p>Some description about Batch.</p>
                        </div>
                    </div> */}

          <div className="info">
            <h3>Batch Manager</h3>
            <p>Some description about Batch.</p>
          </div>

          <div>
            <TextField
              size="small"
              label="Batch"
              type="text"
              value={searchKeyWordBatch}
              onChange={(e) => setSearchKeywordBatch(e.target.value)}
              placeholder="Name or batch no..."
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchOutlined />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        </div>

        {/* ///////// */}
        <div className="userListTableContainer">
          <TableContainer
            component={Paper}
            className="table"
            style={
              currentMode === "Dark"
                ? { border: "1px solid white" }
                : { border: "1px solid black" }
            }
          >
            <Table
              style={
                currentMode === "Dark"
                  ? { backgroundColor: "#161C24", borderRadius: "10px" }
                  : { borderRadius: "10px" }
              }
              sx={{ minWidth: 650 }}
            >
              <TableHead>
                <TableRow>
                  <TableCell align="left">Product Name</TableCell>
                  <TableCell align="left">Batch No</TableCell>
                  <TableCell align="left">Count</TableCell>
                  <TableCell align="left">Date</TableCell>
                  <TableCell align="left">Email</TableCell>
                  <TableCell align="left">Action</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {batchAll?.filter(filterBatchAll).map((batch, index) => (
                  <BatchItems
                    batchData={batch}
                    searchKeyWordBatch={searchKeyWordBatch}
                  />
                ))}
                {!isFilteredBatchDataAvailable && (
                  <>
                    <NoDataComponent cellColSpan={6} />
                  </>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </div>
        {/* ///////// */}
      </section>
    </>
  );
}
