import React, { useState, useEffect } from "react";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import "./ModelPage.css";
import ViewInArIcon from "@mui/icons-material/ViewInAr";
import {
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
} from "@mui/material";
import { sharedCss } from "../../styles/sharedCss";
import Add3dModel from "./Add3dModel";
import { Link, useNavigate } from "react-router-dom";
import { useStateContext } from "../../context/ContextProvider";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toastMessageSuccess } from "../../tools/toast";
import axios from "axios";
import Edit3dModel from "./Edit3dModel";
import NoDataComponent from "../commons/noData.component";
import TableHeader from "../../layouts/machineData/TableHeader";
import { commonRowStyle } from "../../layouts/machineData/MaintenanceReportDataMain";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import NotAccessible from "../not-accessible/not-accessible";
import { useCheckAccess } from "../../utils/useCheckAccess";

function ModelPage() {
  const commonCss = sharedCss();
  const [add3dModelModal, setAdd3dModelModal] = useState(false);
  const [edit3dModelModal, setEdit3dModelModal] = useState(false);
  const [editModelId, setEditModelId] = useState("");
  const [recentModels, setRecentmodels] = useState([]);
  const [machinesData, setMachineData] = useState([]);
  const [loading, setLoading] = useState(false);
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  const navigate = useNavigate();

  console.log(edit3dModelModal, "edit3dModelModal");
  const {
    viewModelId,
    setViewModelId,
    midForModels,
    setMidForModels,
    currentMode,
  } = useStateContext();

  const hasModelGETAccess = useCheckAccess("model", "GET");
  const hasModelPOSTAccess = useCheckAccess("model", "POST");
  const hasModelPUTAccess = useCheckAccess("model", "PUT");
  const hasModelDELETEAccess = useCheckAccess("model", "DELETE");

  const fetchAllMachines = async () => {
    await axios.get(`${dbConfig.url}/machines`).then((response) => {
      setMachineData(response?.data?.data);
    });
  };

  const getRecentModel = async () => {
    setLoading(true);
    try {
      await axios.get(`${dbConfig.url}/model`).then((response) => {
        setRecentmodels(response?.data?.data);
      });
    } catch (error) {
      console.error("Error: \n", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    getRecentModel();
    fetchAllMachines();
  }, []);
  console.log(recentModels, "recentModels");
  function findTitleById(id) {
    const item = machinesData?.find((obj) => obj?._id === id);
    console.log(item, "item111");
    return item ? item?.title : "";
  }

  const deleteModel = async (modelId) => {
    try {
      await axios.delete(`${dbConfig.url}/model/${modelId}`);
      toastMessageSuccess({ message: "Deleted Model successfully!" });
      await getRecentModel(); // Fetch updated data after deletion
    } catch (error) {
      console.error("Error deleting model:", error);
    }
  };
  function formatDate(dateString) {
    const date = new Date(dateString);
    const options = { month: "long", day: "numeric", year: "numeric" };
    const formattedDate = date.toLocaleDateString("en-US", options);
    return formattedDate;
  }
  return (
    <div
      className={`${commonCss.sectionContainer} border-radius-outer`}
      style={{ marginTop: "16px" }}
    >
      <div
        className="pt-2 pb-4"
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography fontWeight="bold" variant="h6" className="text-2xl">
          3D Models
        </Typography>
        <Button
          onClick={() => setAdd3dModelModal(true)}
          variant="contained"
          disabled={!hasModelPOSTAccess}
        >
          Add 3D Model
        </Button>
      </div>
      <div>
        <TableContainer
          component={Paper}
          className="table border-radius-inner"
          sx={commonOuterContainerStyle}
        >
          {hasModelGETAccess ? (
            <Table sx={{ minWidth: 650 }}>
              <TableHeader
                currentMode={currentMode}
                columns={[
                  { id: "serialNo", label: "Serial No", align: "left" },
                  { id: "name", label: "Name", align: "left" },
                  { id: "desc", label: "Description", align: "left" },
                  { id: "mid", label: "Machine", align: "left" },
                  { id: "created_at", label: "Created At", align: "left" },
                  { id: "3d_model", label: "3D Model", align: "left" },
                  { id: "actions", label: "Actions", align: "left" },
                ]}
              />

              <TableBody>
                <>
                  {recentModels?.map((modelItems, index) => {
                    console.log(modelItems, "wwwqqq");
                    return (
                      <TableRow
                        sx={{
                          "&:last-child td, &:last-child th": {
                            border: 0,
                          },
                          "&:hover": { bgcolor: "#f5f5f5" },
                          cursor: "pointer",
                        }}
                        key={index}
                      >
                        <TableCell
                          style={
                            currentMode === "Dark"
                              ? {
                                  color: "white",
                                  borderBottom: "1px solid white",
                                }
                              : { borderBottom: "1px solid #e0e0e0" }
                          }
                          align="left"
                        >
                          {index + 1}
                        </TableCell>
                        <TableCell
                          style={
                            currentMode === "Dark"
                              ? {
                                  color: "white",
                                  borderBottom: "1px solid white",
                                }
                              : { borderBottom: "1px solid #e0e0e0" }
                          }
                          align="left"
                        >
                          {modelItems["name"]}
                        </TableCell>
                        <TableCell
                          style={
                            currentMode === "Dark"
                              ? {
                                  color: "white",
                                  borderBottom: "1px solid white",
                                }
                              : { borderBottom: "1px solid #e0e0e0" }
                          }
                          align="left"
                        >
                          {modelItems["desc"]}
                        </TableCell>
                        <TableCell
                          style={
                            currentMode === "Dark"
                              ? {
                                  color: "white",
                                  borderBottom: "1px solid white",
                                }
                              : { borderBottom: "1px solid #e0e0e0" }
                          }
                          align="left"
                        >
                          {findTitleById(modelItems["mid"])}
                        </TableCell>
                        <TableCell
                          style={
                            currentMode === "Dark"
                              ? {
                                  color: "white",
                                  borderBottom: "1px solid white",
                                }
                              : { borderBottom: "1px solid #e0e0e0" }
                          }
                          align="left"
                        >
                          {modelItems && formatDate(modelItems["created_at"])}
                        </TableCell>
                        <TableCell
                          style={
                            currentMode === "Dark"
                              ? {
                                  color: "white",
                                  borderBottom: "1px solid white",
                                }
                              : { borderBottom: "1px solid #e0e0e0" }
                          }
                          align="left"
                        >
                          <IconButton
                            onClick={() => {
                              setViewModelId(modelItems._id);
                              setMidForModels(modelItems.mid);
                              navigate("/modelview");
                            }}
                            disabled={!hasModelGETAccess}
                            sx={{
                              color: !hasModelGETAccess
                                ? "grey.500"
                                : "primary.main", // Grey when disabled, primary when enabled
                            }}
                          >
                            <ViewInArIcon />
                          </IconButton>
                        </TableCell>
                        <TableCell
                          style={
                            currentMode === "Dark"
                              ? {
                                  color: "white",
                                  borderBottom: "1px solid white",
                                }
                              : { borderBottom: "1px solid #e0e0e0" }
                          }
                          align="left"
                        >
                          <IconButton
                            onClick={() => {
                              setEdit3dModelModal(true);
                              setEditModelId(modelItems["_id"]);
                            }}
                            disabled={!hasModelPUTAccess}
                            sx={{
                              color: !hasModelPUTAccess
                                ? "grey.500"
                                : "primary.main",
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>

                          <IconButton
                            onClick={() => deleteModel(modelItems["_id"])}
                            disabled={!hasModelDELETEAccess}
                            sx={{
                              color: !hasModelDELETEAccess
                                ? "grey.500"
                                : "error.main",
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                  {!recentModels.length && (
                    <>
                      <NoDataComponent dataLoading={loading} cellColSpan={7} />
                    </>
                  )}
                </>
              </TableBody>
            </Table>
          ) : (
            <NotAccessible />
          )}
        </TableContainer>

        <Dialog
          onClose={() => setAdd3dModelModal(false)}
          open={add3dModelModal}
          fullWidth
          maxWidth="md"
        >
          <DialogTitle>Add 3D Model</DialogTitle>
          <DialogContent>
            <Add3dModel
              getRecentModel={getRecentModel}
              machinesData={machinesData}
              setMachineData={setMachineData}
              onClose={() => setAdd3dModelModal(false)}
            />
          </DialogContent>
        </Dialog>
        <Dialog
          onClose={() => setEdit3dModelModal(false)}
          open={edit3dModelModal}
          fullWidth
          maxWidth="md"
        >
          <DialogTitle>Edit 3D Model</DialogTitle>
          <DialogContent>
            <Edit3dModel
              getRecentModel={getRecentModel}
              machinesData={machinesData}
              editModelId={editModelId}
              onClose={() => setEdit3dModelModal(false)}
            />
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}

export default ModelPage;
