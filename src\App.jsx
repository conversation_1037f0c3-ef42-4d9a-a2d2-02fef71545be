import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./hooks/AuthProvider";
import LoginComponent from "./components/LoginComponent";
import PrivateRouteMain from "./route/PrivateRouteMain";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

function App() {
  return (
    <Router>
      <AuthProvider>
        <Routes>
          <Route path="/login-mongo" element={<LoginComponent />} />
          <Route path="/*" element={<PrivateRouteMain />} />
        </Routes>

        {/* This must be here */}
        <ToastContainer />
      </AuthProvider>
    </Router>
  );
}

export default App;
