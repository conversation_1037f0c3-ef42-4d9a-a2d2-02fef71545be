import React, { useEffect, useMemo, useState } from "react";
import "./machineData.scss";
import MaintenanceReportItem from "./MaintenanceReportItem";
import {
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogContent,
  DialogTitle,
  MenuItem,
  Button,
  FormControl,
  InputLabel,
  Select,
  TablePagination,
  Typography,
  Box,
} from "@mui/material";
import BasicMenu from "../../components/menus/BasicMenu";
import { addDays, toDate } from "date-fns";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { DateRangePicker } from "react-date-range";
import { useAuth } from "../../hooks/AuthProvider";
import { useStateContext } from "../../context/ContextProvider";
import { useMaintenanceInfo } from "../../context/MaintenanceContext";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import moment from "moment";
import { makeStyles } from "@mui/styles";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import MachineDropdown from "../Reports/MachineDropdown";
import TableHeader from "./TableHeader";
import NoDataComponent from "../../components/commons/noData.component";
import CommonDropDownComponent from "../../components/commons/dropDown.component";
import useDateFilter from "./useDateFilter";
import { format } from "date-fns";
import DateRangeMenu from "../../components/menus/DateRangeMenu";
import "../machineData/table.scss";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import NotAccessible from "../../components/not-accessible/not-accessible";
import { useCheckAccess } from "../../utils/useCheckAccess";

const useCustomStyles = makeStyles((theme) => ({
  fatInnerContent: {
    padding: "0.5rem !important",
    backgroundColor: `${theme.palette.custom.backgroundForth} !important`,
  },
  tableContainer: {
    maxHeight: "200px",
    overflowY: "auto",
  },
}));

export const commonRowStyle = {
  "&:last-child td, &:last-child th": { border: 0 },
  borderBottom: "1px solid #e0e0e0",
  "&:hover": { bgcolor: "#f5f5f5" },
  cursor: "pointer",
};

function MaintenanceReportDataMain({ machineData, machineId, handleChangeId }) {
  const { currentUser } = useAuth();
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [defaultType, setDefaultType] = useState(0);
  const [maintenanceReportDataAll, setMaintenanceReportDataAll] = useState([]);
  const [cycleForFilter, setCycleForFilter] = useState("All");
  const { currentMode } = useStateContext();
  const [frequencies, setFrequencies] = useState([
    "All",
    "Daily",
    "Weekly",
    "Every 1 Month",
    "Every 3 Months",
    "Every 6 Months",
    "Every 1 Year",
    "Every 2 Years",
    "Every 3 Years",
    "Every 5 Years",
  ]);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState([]);
  const [stepData, setSD] = useState([]);
  const maintenanceInfoFromContext = useMaintenanceInfo();
  const { refreshCount, setRefreshCount } = useMongoRefresh();

  // State for user filter
  const [selectedUser, setSelectedUser] = useState("All");

  // Pagination states
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const { date, shadowOnDate, colorOnDate, dateFilter, resetDateFilter } =
    useDateFilter();
  const commonOuterContainerStyle = useCommonOuterContainerStyle();
  const hasMainReportGETAccess = useCheckAccess("mainReportData", "GET");

  // Format the date range for display
  const formattedDateRange = useMemo(() => {
    const start = date[0]?.startDate
      ? format(new Date(date[0].startDate), "dd MMM yyyy")
      : "N/A";
    const end = date[0]?.endDate
      ? format(new Date(date[0].endDate), "dd MMM yyyy")
      : "N/A";
    return `Date Range: ${start} - ${end}`;
  }, [date]);

  // Derive user options from currentUser list, filtered by report creators
  const userOptions = useMemo(() => {
    const reportEmails = new Set(
      maintenanceReportDataAll.map((data) => data.email),
    );

    const filteredcurrentUser = reportEmails.has(currentUser.email)
      ? [currentUser]
      : [];

    return [
      { label: "All", value: "All" },
      ...filteredcurrentUser
        .map((user) => ({
          label: user.email.split("@")[0],
          value: user.email,
        }))
        .sort((a, b) => a.label.localeCompare(b.label)),
    ];
  }, [currentUser, maintenanceReportDataAll]);

  const typeOptions = [
    { label: "Calibration", value: 0, color: "red" },
    { label: "Machine Breakdown", value: 1, color: "green" },
    { label: "Routine", value: 2, color: "blue" },
    { label: "Preventive", value: 3, color: "red" },
    { label: "Gemba", value: 4, color: "green" },
    { label: "Line Clearance", value: 5, color: "blue" },
  ];

  useEffect(() => {
    console.log("machineId from useeffect:", machineId);
    if (maintenanceInfoFromContext?.type !== undefined) {
      setDefaultType(maintenanceInfoFromContext?.type);
    }
    axios
      .get(`${dbConfig.url}/mainReportData`)
      .then((data) => {
        if (machineId === "All") {
          setMaintenanceReportDataAll(data?.data?.data);
        } else {
          setMaintenanceReportDataAll(
            data?.data?.data?.filter((d) => d?.mid === machineId),
          );
        }
      })
      .catch((e) => {
        console.log("error maintenance report data from mongo : data:", e);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [machineId, refreshCount]);

  const handleDownloadPDF = async () => {
    await axios
      .get(`${dbConfig.url}/mainReportData/pdf/export`)
      .then((res) => {
        window.open(`${dbConfig.url}/${res.data.file}`, "_blank");
      })
      .catch((err) => {
        console.log(err, "error");
      })
      .finally(() => {});
  };

  const filterReports = (reports = []) => {
    return reports.filter((data) => {
      const matchesSearch =
        searchTerm === "" ||
        data.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        data.email
          .split("@")[0]
          .toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        data.comment.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesMachine = data.mid === machineId || machineId === "All";

      const startOfDay = moment(date[0]?.startDate).startOf("day");
      const endOfDay = moment(date[0]?.endDate).endOf("day");
      const isWithinDateRange = moment(data?.date).isBetween(
        startOfDay,
        endOfDay,
        undefined,
        "[]",
      );

      const isFilteredByType = data.type === defaultType;

      // User filter
      const matchesUser = selectedUser === "All" || data.email === selectedUser;

      return (
        matchesSearch &&
        matchesMachine &&
        isWithinDateRange &&
        isFilteredByType &&
        matchesUser
      );
    });
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Get paginated reports
  const paginatedReports = useMemo(() => {
    const filtered = filterReports(maintenanceReportDataAll);
    const startIndex = page * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    return filtered.slice(startIndex, endIndex);
  }, [
    maintenanceReportDataAll,
    page,
    rowsPerPage,
    searchTerm,
    machineId,
    date,
    defaultType,
    selectedUser,
  ]);

  const customCss = useCustomStyles();

  return (
    <main className="allMachineDataPreviewContainer">
      <div
        className={`${customCss.fatInnerContent} liveDataOuterContainer border-radius-outer`}
      >
        <div className="liveDataHeading">
          <div
            className="title"
            style={{
              color: currentMode === "Dark" ? "#fff" : "#000",
            }}
          >
            Maintenance Reports
          </div>
          <div style={{ display: "flex", alignItems: "center", gap: "1rem" }}>
            {/* Interactive Date Range Picker */}
            <DateRangeMenu
              textShadow={shadowOnDate}
              color={colorOnDate}
              fontSize="0.9rem"
              displayText={formattedDateRange}
              items={
                <DateRangePicker
                  onChange={(item) => dateFilter(item)}
                  showSelectionPreview={true}
                  moveRangeOnFirstSelection={false}
                  months={1}
                  ranges={date}
                  direction="horizontal"
                  maxDate={new Date()}
                />
              }
              resetDateFilter={resetDateFilter}
            />

            <CommonDropDownComponent
              dropDownLabel={"Select Type"}
              menuValueDefault={defaultType}
              menuValue={defaultType}
              menuItemValue={"value"}
              menuData={typeOptions}
              menuItemDisplay={"label"}
              handleChange={(e) => setDefaultType(e.target.value)}
              dropDownContainerStyle={{ minWidth: 200 }}
            />

            {/* User Dropdown */}
            <CommonDropDownComponent
              dropDownLabel={"Select User"}
              menuValueDefault={selectedUser}
              menuValue={selectedUser}
              menuItemValue={"value"}
              menuData={userOptions}
              menuItemDisplay={"label"}
              handleChange={(e) => setSelectedUser(e.target.value)}
              dropDownContainerStyle={{ minWidth: 200 }}
            />

            <MachineDropdown
              machineId={machineId}
              machineData={machineData}
              handleChangeId={handleChangeId}
            />
          </div>
        </div>

        <div className="liveDataContainer">
          {hasMainReportGETAccess ? (
            <>
              <TableContainer
                component={Paper}
                className={`table ${customCss.tableContainer} border-radius-inner`}
                sx={commonOuterContainerStyle}
              >
                <Table
                  style={{
                    minWidth: 650,
                    width: "100%",
                    backgroundColor:
                      currentMode === "Dark" ? "#161C24" : "#fff",
                  }}
                >
                  <TableHeader
                    currentMode={currentMode}
                    columns={[
                      { label: "Title", align: "left", width: "25%" },
                      {
                        label: `Performed on`,
                        align: "left",
                        width: "15%",
                        filterComponent: (
                          <BasicMenu
                            textShadow={shadowOnDate}
                            color={colorOnDate}
                            fontSize="0.75rem"
                            items={
                              <DateRangePicker
                                onChange={(item) => dateFilter(item)}
                                showSelectionPreview={true}
                                moveRangeOnFirstSelection={false}
                                months={1}
                                ranges={date}
                                direction="horizontal"
                                maxDate={new Date()}
                              />
                            }
                          />
                        ),
                      },
                      { label: "Done By", align: "left", width: "15%" },
                      { label: "Remarks", align: "left", width: "10%" },
                      { label: "Status", align: "center", width: "15%" },
                      { label: "Actions", align: "center" },
                    ]}
                  />

                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={9} align="center">
                          <CircularProgress />
                        </TableCell>
                      </TableRow>
                    ) : paginatedReports.length > 0 ? (
                      paginatedReports.map((data, index) => (
                        <MaintenanceReportItem
                          key={index}
                          data={data}
                          userName={`${user?.fname} ${user?.lname}`}
                          stepData={stepData
                            .filter((d) => d.manual_id === data._id)
                            .sort((a, b) => a.index - b.index)}
                          machineData={machineData}
                          setMaintenanceReportDataAll={
                            setMaintenanceReportDataAll
                          }
                        />
                      ))
                    ) : (
                      <NoDataComponent cellColSpan={9} />
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination Component */}
              <TablePagination
                rowsPerPageOptions={[10, 25, 50, 100]}
                component="div"
                count={filterReports(maintenanceReportDataAll).length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="Reports per page:"
              />
            </>
          ) : (
            <NotAccessible />
          )}
        </div>
      </div>
    </main>
  );
}

export default MaintenanceReportDataMain;
