import React, { useEffect, useState } from "react";
// import { useParams } from "react-router-dom";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import "./machineData.scss";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import ReportItem from "./ReportItem";
import { Dialog, DialogContent, DialogTitle } from "@mui/material";

const ReportData = ({ type, docId }) => {
  // const { mid } = useParams();
  const [details, setDetails] = useState([]);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(type)
    //   .where("docId", "==", docId)
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     setDetails(data);
    //   });
  }, []);

  return (
    <section className="machineDataViewPage">
      <div className="allMachineDataPreviewContainer">
        <div className="liveDataOuterContainer">
          {type == "FATReport" ? (
            ""
          ) : (
            <div className="liveDataHeading">
              <div className="title"></div>
              <div className="btn">
                <button onClick={() => setOpen(true)} className="addBtn">
                  Add Report
                </button>
              </div>
            </div>
          )}

          <div className="liveDataContainer">
            <TableContainer component={Paper} className="table">
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow>
                    <TableCell align="left">Title</TableCell>
                    <TableCell align="center">Description</TableCell>
                    <TableCell align="center">Report</TableCell>
                    <TableCell align="center">Created on</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {details.length > 0 ? (
                    details.map((data) => (
                      <ReportItem key={data.id} type={type} data={data} />
                    ))
                  ) : (
                    <TableRow
                      sx={{
                        "&:last-child td, &:last-child th": {
                          border: 0,
                        },
                      }}
                    >
                      <TableCell
                        style={{ borderBottom: "none" }}
                        align="center"
                        colSpan={8}
                      >
                        <img
                          src="https://i.pinimg.com/originals/c9/22/68/c92268d92cf2dbf96e3195683d9e14fb.png"
                          alt="Such a Empty"
                        />
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        </div>
      </div>
      <Dialog open={open} fullWidth>
        <DialogTitle>Add {type} Details </DialogTitle>
        <DialogContent>
          {/*<AddSAT mid={mid} docId={docId} handleClose={() => setOpen(false)} />*/}
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default ReportData;
