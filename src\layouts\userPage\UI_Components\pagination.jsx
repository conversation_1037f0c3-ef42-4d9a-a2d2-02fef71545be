import { Box, Button, Typography } from "@mui/material";

import PropTypes from "prop-types";

const buttonStyle = {
  minWidth: "2.5rem",
  minHeight: "2.5rem",
  borderRadius: "50%",
  fontSize: "14px",
  fontWeight: "bold",
};

const Pagination = ({
  totalRows = 0,
  pagination = {
    currentPage: 1,
    itemsPerPage: 10,
  },
  setPagination = () => console.log(),
}) => {
  const currentPageTable = pagination.currentPage;
  const setCurrentPageTable = (i) =>
    setPagination({ ...pagination, currentPage: i });
  const itemsPerPage = pagination.itemsPerPage;

  const totalPages = Math.ceil(totalRows / itemsPerPage);

  const renderPaginationButtons = () => {
    const createButton = (label, onClick, variant) => (
      <Button
        key={label}
        style={buttonStyle}
        onClick={onClick}
        variant={variant}
      >
        {label}
      </Button>
    );

    const buttons = [];
    const maxButtons = 5;

    const addButtonsInRange = (start, end) => {
      for (let i = start; i <= end; i++) {
        buttons.push(
          createButton(
            i,
            () => {
              setCurrentPageTable(i);
            },
            i === currentPageTable ? "contained" : "outlined",
          ),
        );
      }
    };

    const startPage = Math.max(
      1,
      currentPageTable - Math.floor(maxButtons / 2),
    );
    const endPage = Math.min(totalPages, startPage + maxButtons - 1);

    if (startPage > 1) {
      buttons.push(
        createButton(
          "<",
          () => setCurrentPageTable(currentPageTable - 1),
          "outlined",
        ),
      );
    }

    addButtonsInRange(startPage, endPage);

    if (endPage < totalPages) {
      buttons.push(
        createButton(
          ">",
          () => setCurrentPageTable(currentPageTable + 1),
          "outlined",
        ),
      );
    }

    return buttons;
  };

  const renderRowCount = () => {
    const firstIndex = (currentPageTable - 1) * itemsPerPage + 1;
    const lastIndex = Math.min(currentPageTable * itemsPerPage, totalRows);
    // const totalRows = data?.length;
    return `${firstIndex}-${lastIndex} of ${totalRows}`;
  };
  return (
    <Box
      sx={{
        width: "100%",
      }}
    >
      <Typography
        style={{ textAlign: "right", margin: "1rem" }}
        variant="body2"
        color="textSecondary"
      >
        {renderRowCount()}
      </Typography>
      {/* Pagination buttons */}
      <Box
        style={{
          display: "flex",
          justifyContent: "center",
          gap: "1rem",
        }}
      >
        {renderPaginationButtons()}
      </Box>
    </Box>
  );
};

Pagination.propTypes = {
  totalRows: PropTypes.number, // Total number of rows/items
  pagination: PropTypes.shape({
    currentPage: PropTypes.number, // Current page number
    itemsPerPage: PropTypes.number, // Number of items per page
  }).isRequired, // Pagination object is required
  setPagination: PropTypes.func.isRequired, // Function to update pagination
};

Pagination.defaultProps = {
  totalRows: 0, // Default total rows
  pagination: {
    currentPage: 1, // Default current page
    itemsPerPage: 10, // Default items per page
  },
  setPagination: () => console.log("Set pagination called!"), // Default handler for pagination updates
};

export default Pagination;
