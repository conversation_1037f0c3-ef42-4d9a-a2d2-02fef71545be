import React, { useState, useRef } from "react";
import { usePdf } from "@mikecousins/react-pdf";
import { base64toBlob } from "../hooks/useBase64";

const TestingPage = ({ file }) => {
  const [page, setPage] = useState(1);
  const canvasRef = useRef(null);

  const url = URL.createObjectURL(base64toBlob(file.url));

  const { pdfDocument, pdfPage } = usePdf({
    file: url,
    page,
    canvasRef,
  });

  return (
    <div>
      {!pdfDocument && <span>Loading...</span>}
      <canvas ref={canvasRef} />
      {Boolean(pdfDocument && pdfDocument.numPages) && (
        <nav>
          <ul className="pager">
            <li className="previous">
              <button disabled={page === 1} onClick={() => setPage(page - 1)}>
                Previous
              </button>
            </li>
            <li className="next">
              <button
                disabled={page === pdfDocument.numPages}
                onClick={() => setPage(page + 1)}
              >
                Next
              </button>
            </li>
          </ul>
        </nav>
      )}
    </div>
  );
};

export default TestingPage;
