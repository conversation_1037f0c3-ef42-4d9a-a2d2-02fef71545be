import { Box, TextField } from "@mui/material";
import React, { useState } from "react";
import { HexColorPicker } from "react-colorful";

const ColorPickerInput = ({
  label = "Color",
  color = "#ffffff",
  setColor = (color) => {},
}) => {
  const [open, setOpen] = useState(false);

  const handleColorChange = (newColor) => {
    setColor(newColor);
  };
  return (
    <Box
      sx={{
        position: "relative",
      }}
    >
      <TextField
        label={label}
        value={color}
        onClick={() => setOpen(true)}
        onChange={(e) => setColor(e.target.value)}
        InputProps={{
          style: { backgroundColor: color },
          readOnly: true,
        }}
        fullWidth
      />
      {open && (
        <Box
          sx={{
            position: "absolute",
            top: "100%",
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 99,
          }}
          onBlur={() => setOpen(false)}
        >
          <HexColorPicker color={color} onChange={handleColorChange} />
        </Box>
      )}
    </Box>
  );
};

export default ColorPickerInput;
