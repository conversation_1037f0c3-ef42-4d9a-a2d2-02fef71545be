import React from "react";
import { Routes, Route } from "react-router-dom"; // Use Routes and Route for react-router-dom v6
import { Grid } from "@mui/material"; // Keep Grid import from @mui/material
import Sidebar, { S_TYPE } from "../components/Sidebar/Sidebar";
import Header from "../components/Header/Header";
import RegistrationPortal from "../layouts/registration/Registration";
import AddUser from "../pages/addUser";
import TestingPage from "../pages/testing";
import Whiteboard from "../components/whiteboard/Whiteboard";
import CompanyIdPage from "../pages/companyIdPage";
import ForgotPasswordPage from "../pages/forgotPasswordPage";
import FATApproval from "../layouts/FATApproval/FATApproval";
import AdminLoginPage from "../pages/adminLoginPage";
import LoginPage from "../pages/loginPage";
import AdminForgotPasswordPage from "../pages/adminForgotPasswordPage";
import LoginMongoScreen from "../layouts/auth-mongo/login-mongo.screen";
import PrintReportsPage from "../pages/PrintReports";
import PrintFatSingle from "../layouts/machineData/printForMachineData/PrintFatSingle";
import PrintSatSingle from "../layouts/machineData/printForMachineData/PrintSatSingle";
import PrivateRoutesForLsiAdminMain from "./PrivateRoutesForLsiAdminMain";
import FlowChartType1 from "../components/Flow-Chart/Type1/index";
import ExistingFlowChart from "../components/Flow-Chart/Existing-Node/type-1";
import CreateNewNode from "../components/New-Node/create-node";
import NewNode from "../components/New-Node/index";
import AddUsers from "../layouts/FATApproval/AddUsers";
import RouteMain from "./PrivateRouteMainChildren";

function PrivateRouteMain({
  confirmOpen,
  setConfirmOpen,
  handleRouteNavigation,
  inactive,
  setInactive,
  blockRouteNav,
  currentUser,
  lsiRoute, // Accept lsiRoute as a prop
  currentMode,
  currentColor,
}) {
  const sidebarState = { someProperty: "Example State" }; // Define the state to pass to Sidebar

  return (
    <Routes>
      <Route path="/user-invitation/:id" element={<RegistrationPortal />} />
      <Route
        path="/add-user/b59083f7-5871-4ab5-996b-020090486c53"
        element={<AddUser />}
      />
      <Route path="/testing" element={<TestingPage />} />
      <Route path="/whiteboard" element={<Whiteboard />} />
      <Route path="/company-id" element={<CompanyIdPage />} />
      {currentUser == null ? (
        <Route path="/" element={<CompanyIdPage />} />
      ) : null}
      <Route path="/forgot-password" element={<ForgotPasswordPage />} />
      <Route
        path="/fat-approval/:companyId/:fid/:approvalId/:eid"
        element={<FATApproval />}
      />
      <Route path="/admin-login" element={<AdminLoginPage />} />
      <Route path="/login" element={<LoginPage />} />
      <Route
        path="/admin-forgot-password"
        element={<AdminForgotPasswordPage />}
      />
      <Route path="/login-mongo" element={<LoginMongoScreen />} />
      <Route
        path="/fat-reports-print/:reportId"
        element={<PrintReportsPage />}
      />
      <Route path="/:mid/printFatSingle/:docId" element={<PrintFatSingle />} />
      <Route path="/:mid/printSatSingle/:docId" element={<PrintSatSingle />} />
      {lsiRoute && (
        <Route path="/*" element={<PrivateRoutesForLsiAdminMain />} />
      )}
      <Route
        path="/*"
        element={
          <Grid container>
            <Grid
              item
              style={{
                height: "100vh",
                overflowY: "scroll",
                ...(S_TYPE === "3" && {
                  backgroundColor:
                    currentMode === "Dark" ? "#1e1e1e" : currentColor,
                }),
                color: "#fff",
              }}
              xs={inactive ? 1 : 2}
              className="transition-all duration-500 ease-in-out"
            >
              <Sidebar inactive={inactive} state={sidebarState} />
            </Grid>
            <Grid
              item
              xs={inactive ? 11 : 10}
              style={{
                borderLeft: "2px solid gainsboro",
                height: "100vh",
                overflowY: "scroll",
                padding: "1rem",
              }}
            >
              <Header setInactive={setInactive} inactive={inactive} />
              <RouteMain />

              <Routes>
                <Route
                  path="/flow-chart"
                  element={
                    <FlowChartType1
                      blockRouteNav={blockRouteNav}
                      handleRouteNavigation={handleRouteNavigation}
                    />
                  }
                />
                <Route
                  path="/edit/:id"
                  element={
                    <ExistingFlowChart
                      blockRouteNav={blockRouteNav}
                      handleRouteNavigation={handleRouteNavigation}
                    />
                  }
                />
                <Route path="/new-node" element={<NewNode />} />
                <Route
                  path="/create-node/:id"
                  element={
                    <CreateNewNode
                      blockRouteNav={blockRouteNav}
                      handleRouteNavigation={handleRouteNavigation}
                    />
                  }
                />
                <Route
                  path="/:fid/:fatId/add-users-fat"
                  element={<AddUsers />}
                />
              </Routes>
            </Grid>
          </Grid>
        }
      />
    </Routes>
  );
}

export default PrivateRouteMain;
