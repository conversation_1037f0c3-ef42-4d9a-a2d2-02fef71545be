import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { companies, companyId_constant, training } from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import "./machineData.scss";
import MachineDataHeader from "./MachineDataHeader";
import { AddFAT, AddSAT } from "./AddDocumentation";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import DocumentationItem from "./DocumentationItem";
import { Dialog, DialogContent, DialogTitle } from "@mui/material";
import { CircularProgress } from "@mui/material";

const DocumentationData = ({ type, docId, fatSeriesId }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { mid } = useParams();
  const [details, setDetails] = useState([]);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(type)
    //   .where('docId', '==', docId)
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     data.sort(function(a, b) {
    // 			return (a.index - b.index)
    // 		})
    //     setDetails(data);
    //   });
  }, []);

  return (
    <section className="machineDataViewPage">
      <div className="allMachineDataPreviewContainer">
        <div className="liveDataOuterContainer">
          <div className="liveDataHeading">
            <div className="title"></div>
            <div className="btn">
              <button onClick={() => setOpen(true)} className="addBtn">
                Add Data
              </button>
            </div>
          </div>

          <div className="liveDataContainer">
            <TableContainer component={Paper} className="table">
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow>
                    <TableCell align="left">Name</TableCell>
                    <TableCell align="center">Comment</TableCell>
                    <TableCell align="center">Start Value</TableCell>
                    <TableCell align="center">Stop Value</TableCell>
                    <TableCell align="center">Time</TableCell>
                    <TableCell align="center">Tolerance</TableCell>
                    <TableCell align="center">Status</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {details.length > 0 ? (
                    details.map((data) => (
                      <DocumentationItem
                        key={data.id}
                        type={type}
                        data={data}
                        fatSeriesId
                      />
                    ))
                  ) : (
                    <TableRow
                      sx={{
                        "&:last-child td, &:last-child th": {
                          border: 0,
                        },
                      }}
                    >
                      <TableCell
                        style={{ borderBottom: "none" }}
                        align="center"
                        colSpan={8}
                      >
                        <CircularProgress />
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        </div>
      </div>
      <Dialog open={open} fullWidth>
        <DialogTitle>Add {type} Details </DialogTitle>
        <DialogContent>
          {type == "FAT" ? (
            <AddFAT
              mid={mid}
              docId={docId}
              handleClose={() => setOpen(false)}
            />
          ) : (
            <AddSAT
              mid={mid}
              docId={docId}
              handleClose={() => setOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default DocumentationData;
