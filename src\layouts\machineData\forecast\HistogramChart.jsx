import React, { useEffect, useRef } from "react";
import * as d3 from "d3";
import { Card, CardContent, Typography, Box } from "@mui/material";

const HistogramChart = ({ values }) => {
  const chartRef = useRef();

  useEffect(() => {
    if (!values || values.length === 0) return;

    // Clear previous chart
    d3.select(chartRef.current).selectAll("*").remove();

    // Set up dimensions
    const width = chartRef.current.parentElement.clientWidth - 40;
    const height = 250;
    const margin = { top: 20, right: 30, bottom: 40, left: 40 };

    // Create SVG element
    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left}, ${margin.top})`);

    // Define X and Y scales
    const xScale = d3
      .scaleLinear()
      .domain([d3.min(values) - 5, d3.max(values) + 5])
      .range([0, width - margin.left - margin.right]);

    const histogram = d3
      .histogram()
      .domain(xScale.domain())
      .thresholds(xScale.ticks(10));
    const bins = histogram(values);

    const yScale = d3
      .scaleLinear()
      .domain([0, d3.max(bins, (d) => d.length)])
      .range([height - margin.top - margin.bottom, 0]);

    // Draw bars
    svg
      .selectAll("rect")
      .data(bins)
      .enter()
      .append("rect")
      .attr("x", (d) => xScale(d.x0))
      .attr("y", (d) => yScale(d.length))
      .attr("width", (d) => xScale(d.x1) - xScale(d.x0) - 1)
      .attr(
        "height",
        (d) => height - margin.top - margin.bottom - yScale(d.length),
      )
      .attr("fill", "#4285F4");

    // Add X Axis
    svg
      .append("g")
      .attr("transform", `translate(0,${height - margin.top - margin.bottom})`)
      .call(d3.axisBottom(xScale));

    // Add Y Axis
    svg.append("g").call(d3.axisLeft(yScale));
  }, [values]);

  return (
    <Card sx={{ width: "100%", mt: 4 }}>
      <CardContent>
        <Typography variant="h6" align="center" gutterBottom>
          Histogram of Machine Values
        </Typography>
        <Box ref={chartRef} sx={{ width: "100%", height: "250px" }} />
      </CardContent>
    </Card>
  );
};

export default HistogramChart;
