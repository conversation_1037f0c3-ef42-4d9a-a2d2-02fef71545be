import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  Grid,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import axios from "axios";
import { Close } from "@mui/icons-material";
import SecretKeyInput from "../SecretKeyInput";
import { Box } from "@mui/system";
import { toast } from "react-toastify";
import { validateQnnPattern } from "../utils";
import PropTypes from "prop-types";
import { dbConfig } from "../../../infrastructure/db/db-config";

const AcceptRejectAlertModal = ({
  open = false,
  setOpen = () => {},
  handleAccept = () => {},
  handleReject = () => {},
  desc = "field",
  pending = false,
  hideRemark = false,
  hideQnn = false,
  AcceptToolTip = "",
  RejectToolTip = "",
  AcceptBtnName = "Accept",
  RejectBtnName = "Reject",
  hideRejectBtn = false,
  isQnnMandatory = true,
  isRemarksMandatory = true,
  bodyContent = null,
}) => {
  const [secretKey, setSecretKey] = useState("");
  const [verified, setVerified] = useState(false);
  const [form, setForm] = useState({
    qnn: "",
    remark: "",
  });

  const resetForm = () => {
    setForm({
      qnn: "",
      remark: "",
    });
    setSecretKey("");
    setVerified(false);
  };

  useEffect(() => {
    resetForm();
  }, [open]);

  useEffect(() => {
    setVerified(false);
  }, [secretKey]);

  const verifyQA = async (value) => {
    if (value.length >= 8) {
      axios
        .post(`${dbConfig?.url}/auth/qa`, {
          key: value,
        })
        .then((res) => {
          toast.success("QA Key verified successfully");
          setVerified(true);
        })
        .catch((err) => {
          setVerified(true);
        })
        .finally(() => {
          toast.success("QA Key verified successfully");
          setVerified(true);
        });
    } else {
      setVerified(true);
    }
  };

  const {
    text: qnnHelperText,
    isValid: isValidQNN,
    style: qnnStyles,
  } = validateQnnPattern(form.qnn);

  const validateForm = () => {
    if (
      !hideQnn &&
      isQnnMandatory &&
      (!isValidQNN || form.qnn.length <= 0 || form.qnn.length > 100)
    ) {
      return false;
    }
    if (
      !hideRemark &&
      isRemarksMandatory &&
      (form.remark.length <= 0 || form.remark.length > 100)
    ) {
      return false;
    }
    return true;
  };

  const isFormValid = validateForm();

  const isBtnDisabled = pending || !isFormValid || !verified;

  const onAccept = () => {
    handleAccept(form.qnn, form.remark);
    setOpen(false); // Close the dialog
    resetForm(); // Reset the form
  };

  const onReject = () => {
    handleReject(form.qnn, form.remark);
    setOpen(false); // Close the dialog
    resetForm(); // Reset the form
  };

  return (
    <Dialog open={open}>
      <Box sx={{ px: 4, pt: 1, pb: 2.5, width: "24rem" }}>
        <DialogTitle
          sx={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            alignItems: "flex-start",
            position: "relative",
          }}
        >
          {typeof desc === "string" ? (
            <Typography variant="h6">{desc}</Typography>
          ) : (
            desc
          )}
          <Tooltip title="Close" placement="top">
            <Button
              onClick={() => setOpen(false)}
              sx={{
                position: "absolute",
                right: "-1.75rem",
                top: "5%",
                "&:hover": {
                  backgroundColor: "#dd0101 ",
                  "& *": {
                    color: "white",
                  },
                },
              }}
            >
              <Close color="error" />
            </Button>
          </Tooltip>
        </DialogTitle>
        <DialogContent>
          {bodyContent && <Box sx={{ py: 2 }}>{bodyContent}</Box>}
          {!hideQnn && (
            <Grid item xs={12} sm={12} md={12} mt={1}>
              <TextField
                size="small"
                fullWidth
                required={isQnnMandatory}
                value={form.qnn}
                onChange={(e) => {
                  setForm({
                    ...form,
                    qnn: e.target.value.toUpperCase().trimStart(),
                  });
                }}
                label="Quality Notification Number"
                error={!isValidQNN}
                helperText={<span style={qnnStyles}>{qnnHelperText} </span>}
              />
            </Grid>
          )}
          {!hideRemark && (
            <Grid item xs={12} sm={12} md={12} mt={1}>
              <TextField
                size="small"
                fullWidth
                required={isRemarksMandatory}
                value={form.remark}
                onChange={(e) => {
                  setForm({ ...form, remark: e.target.value.trimStart() });
                }}
                label="Remarks"
                error={form.remark.length > 100}
                helperText={
                  form.remark.length > 100
                    ? "Remarks should be less than 100 characters"
                    : " "
                }
              />
            </Grid>
          )}
          <Grid item xs={12} sm={12} md={12} mt={1}>
            <SecretKeyInput
              secretKey={secretKey}
              setSecretKey={setSecretKey}
              verifyQA={verifyQA}
            />
          </Grid>
        </DialogContent>
        <DialogActions>
          <Tooltip
            title={
              isBtnDisabled
                ? "Please Enter all the required fields"
                : AcceptToolTip
            }
          >
            <Box>
              <Button
                onClick={onAccept}
                color="success"
                disabled={isBtnDisabled}
                variant="contained"
              >
                {AcceptBtnName}
              </Button>
            </Box>
          </Tooltip>
          {!hideRejectBtn && (
            <Tooltip
              title={
                isBtnDisabled
                  ? "Please Enter all the required fields"
                  : RejectToolTip
              }
            >
              <Box>
                <Button
                  onClick={onReject}
                  color="warning"
                  disabled={isBtnDisabled}
                  variant="contained"
                >
                  {RejectBtnName}
                </Button>
              </Box>
            </Tooltip>
          )}
        </DialogActions>
      </Box>
    </Dialog>
  );
};

AcceptRejectAlertModal.propTypes = {
  open: PropTypes.bool,
  setOpen: PropTypes.func,
  handleAccept: PropTypes.func,
  handleReject: PropTypes.func,
  desc: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  pending: PropTypes.bool,
  hideRemark: PropTypes.bool,
  hideQnn: PropTypes.bool,
  AcceptToolTip: PropTypes.string,
  RejectToolTip: PropTypes.string,
  AcceptBtnName: PropTypes.string,
  RejectBtnName: PropTypes.string,
  hideRejectBtn: PropTypes.bool,
  isQnnMandatory: PropTypes.bool,
  isRemarksMandatory: PropTypes.bool,
  bodyContent: PropTypes.node,
};

export default AcceptRejectAlertModal;
