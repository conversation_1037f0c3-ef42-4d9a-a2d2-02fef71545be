import { Button, MenuItem, Select } from "@mui/material";
import { Dialog } from "@mui/material";
import React, { useState } from "react";
import { ButtonBasic } from "../../components/buttons/Buttons";
import ConfirmModal from "../../components/ConfirmModel/ConfirmModal";
import { companies, companyId_constant, liveData } from "../../constants/data";
import { db } from "../../firebase";
import { toastMessageSuccess, toastMessageWarning } from "../../tools/toast";

const AnoItem = ({
  data,
  docData,
  handleMqtt,
  annotationDataTemp,
  processValues,
  onClose,
}) => {
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [mqttId, setMqttId] = useState("");
  const [tempAnn, setTempAnn] = useState();

  const updateMqtt = () => {
    console.log("calibUpdateData:", annotationDataTemp);

    // if ((annotationDataTemp.filter((data) => data.calib_id == undefined)?.length === 0)) {
    //   // Avobe 'if': Newlly anoteted data does not have a calibration
    //   //and we forget to assgning for the first time.

    // }
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(liveData).doc(docData.id).update({ 'annotationData': annotationDataTemp }).then(() => {
    //       //console.log('updated thanks')
    //       toastMessageSuccess({ message: 'Updated Successfully' });
    //       onClose();
    //     })
    // else {
    //   console.log("else anoItem: ", (annotationDataTemp.filter((data) => data.calib_id == undefined)?.length))
    //   let promise = new Promise((resolve, reject) => {
    //     resolve();
    //   });
    //   const temp = [];
    //   promise.then(() => {
    //     annotationDataTemp.map((data) => data.calib_id == undefined ? temp.push(data) : console.log(data))
    //   })
    //     .then(() => {
    //       console.log("temp:", temp)
    //       toastMessageWarning({ message: 'Missing values : Calibration is missing in newlly created anotations ' + ": " + temp.map((data) => data.comment) });
    //     })
    // }
  };

  return (
    <div>
      <div
        style={{
          display: "flex",
          justifyContent: "flex-start",
          marginTop: "18px",
        }}
      >
        <ButtonBasic
          buttonTitle="Save Process Value"
          onClick={() => {
            handleMqtt(data.id, processValues);
            setConfirmOpen(true);
          }}
        />
      </div>
      <Dialog open={confirmOpen}>
        <ConfirmModal
          onConfirm={() => updateMqtt()}
          onClose={() => setConfirmOpen(false)}
        />
      </Dialog>
    </div>
  );
};

export default AnoItem;
