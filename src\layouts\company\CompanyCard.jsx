import React from "react";
import ButtonMoreOptions from "../../components/buttons/ButtonMoreOptions";
import { EditButtons, ViewButtons } from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import { Card } from "@mui/material";
const CompanyCard = (props) => {
  const { currentColor, currentColorLight, currentMode } = useStateContext();
  return (
    <Card
      className="card hover:shadow-xl hover:shadow-gray-500 "
      style={
        currentMode === "Dark"
          ? { background: "#212B36" }
          : { background: "#fff" }
      }
    >
      <div className="headerContainer">
        <div className="logo">
          <img src={props.url} alt="" />
        </div>

        <div className="details">
          {/* <div className='text-xs'> <b>ID:</b> {props.mid}</div> */}
          <div
            className="text-2xl font-medium capitalize"
            style={currentMode === "Dark" ? { color: currentColorLight } : {}}
          >
            {props.title}
          </div>
          <div
            className="text-sm  font-normal capitalize"
            style={currentMode === "Dark" ? { color: currentColorLight } : {}}
          >
            {props.description}
          </div>
          {/* <div className='flex justify-center justify-items-center'>
          <div className="companyColorTheme"  data-title="Theme" style={{backgroundColor:props.theme}}/>
          </div> */}
        </div>
      </div>
      {/* <div className="secondContainer">
        <div className="secondHeading">Domain:</div>
        <div className="secondName">{props.domain}</div>
      </div> */}

      {/* <div className="secondContainer">
        <div className="secondHeading">Theme:</div>
        <div className="secondName">
        <div className="companyColorTheme" style={{backgroundColor:props.theme}}></div>
        </div>
      </div> */}

      {/* <div className="companyColorTheme" style={{backgroundColor:props.theme}}></div> */}
      <div className="flex justify-between w-full">
        <EditButtons onClick={props.onClickEdit} />
        <div
          className="w-4 h-4 rounded-lg self-center"
          data-title="Theme"
          style={{ backgroundColor: props.theme }}
        ></div>
        <ViewButtons onClick={props.onClickView} />
      </div>
    </Card>
  );
};

export default CompanyCard;
