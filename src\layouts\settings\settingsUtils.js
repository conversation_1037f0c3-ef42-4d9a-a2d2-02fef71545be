import { toastMessage, toastMessageSuccess } from "../../tools/toast";

// Helper function to handle permission results
const handlePermissionResult = (res, deviceType) => {
  if (res.state === "prompt") {
    // Ask user to grant permission
    const constraints =
      deviceType?.toLowerCase() === "camera"
        ? { video: true }
        : { audio: true };

    navigator.mediaDevices
      .getUserMedia(constraints)
      .then((stream) => {
        console.log(`${deviceType} permission granted`);
        // Remember to stop the stream when done
        stream.getTracks().forEach((track) => track.stop());
        toastMessageSuccess({ message: `${deviceType} permission granted` });
      })
      .catch((err) => {
        console.error(`${deviceType} permission denied:`, err);
        toastMessage({
          message:
            err?.name === "NotFoundError"
              ? `${deviceType} unavailable`
              : `${deviceType} permission denied. Please enable it in your browser settings.`,
        });
      });
  } else if (res.state === "granted") {
    console.log(`${deviceType} permission already granted`);
    toastMessageSuccess({
      message: `${deviceType} permission already granted`,
    });
  } else if (res.state === "denied") {
    console.log(`${deviceType} permission denied`);
    toastMessage({
      message: `${deviceType} permission denied. Please enable it in your browser settings.`,
    });
  }
};

export const handleCamera = async () => {
  try {
    const cameraPermissionResults = await navigator.permissions.query({
      name: "camera",
    });
    handlePermissionResult(cameraPermissionResults, "Camera");
  } catch (error) {
    console.error(error);
  }
};

export const handleMicrophone = async () => {
  try {
    const microphonePermissionResults = await navigator.permissions.query({
      name: "microphone",
    });
    handlePermissionResult(microphonePermissionResults, "Microphone");
  } catch (error) {
    console.error(error);
  }
};

// Add this new function to check and reset device permissions
export const resetVideoDevices = async () => {
  try {
    // First, check if there are any active media streams and stop them
    const allTracks = [];
    const mediaDevices = await navigator.mediaDevices.enumerateDevices();

    // Get all video and audio devices
    const videoDevices = mediaDevices.filter(
      (device) => device.kind === "videoinput",
    );
    const audioDevices = mediaDevices.filter(
      (device) => device.kind === "audioinput",
    );

    if (videoDevices.length === 0 && audioDevices.length === 0) {
      toastMessage({
        message: "No camera or microphone devices detected on your system.",
      });
      return false;
    }

    // Try to get a new media stream to reset permissions
    const stream = await navigator.mediaDevices.getUserMedia({
      video: videoDevices.length > 0,
      audio: audioDevices.length > 0,
    });

    // Stop all tracks immediately to release the devices
    stream.getTracks().forEach((track) => {
      track.stop();
      allTracks.push(track.label);
    });

    toastMessageSuccess({
      message: `Successfully reset camera and microphone. Available devices: ${allTracks.join(", ")}`,
    });
    return true;
  } catch (error) {
    console.error("Error resetting devices:", error);

    let errorMessage = "Failed to reset camera/microphone.";
    if (error.name === "NotAllowedError") {
      errorMessage =
        "Permission denied. Please allow camera/microphone access in your browser settings.";
    } else if (error.name === "NotFoundError") {
      errorMessage =
        "No camera or microphone found. Please connect a device and try again.";
    } else if (error.name === "NotReadableError") {
      errorMessage =
        "Camera or microphone is already in use by another application. Please close other applications using these devices.";
    }

    toastMessage({ message: errorMessage });
    return false;
  }
};
