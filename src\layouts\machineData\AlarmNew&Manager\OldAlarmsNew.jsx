import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import "../machineData.scss";
import MachineDataHeader from "../MachineDataHeader";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import SearchIcon from "@mui/icons-material/Search";
import {
  Dialog,
  DialogContent,
  Button,
  Badge,
  DialogTitle,
  CircularProgress,
  TextField,
  Typography,
  Tooltip,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import { sharedCss } from "../../../styles/sharedCss";
import AttachMaintenance from "./AttachMaintenance";
import CommonDropDown from "../../../components/commons/dropDown.component";
import TableHeader from "../TableHeader";
import NoDataComponent from "../../../components/commons/noData.component";
import { Edit, Visibility } from "@mui/icons-material";
import { useCommonOuterContainerStyle } from "../../../styles/useCommonOuterContainerStyle";

const AlarmsNew = () => {
  const [open, setOpen] = useState(false);
  const [open2, setOpen2] = useState(false);
  const { mid } = useParams();
  const history = useNavigate(); // Changed to useHistory
  //   const [details, setDetails] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [defaultType, setDefaultType] = useState(1);
  const [machineName, setMachineName] = useState("");
  //   const [hastTagsAll, setHastagsAll] = useState([]);

  const [dataLoading, setDataLoading] = useState(true);
  //   const [hastagsForFilter, setHastagsForFilter] = useState([]);
  //   const maintenanceInfoFromContext = useMaintenanceInfo();
  //   const maintenanceInfoSetter = useMaintenanceInfoSeter();
  const { currentColor, currentMode, currentColorLight } = useStateContext();

  const [liveAlarmsAll, setLiveAlarmsAll] = useState([]); // liveAlarm
  const [activeAlarmsAll, setActiveAlarmsAll] = useState([]); // alarms_created
  const [maintenanceOfMachine, setMaintenanceOfMachine] = useState([]);
  const [newLiveAlarms, setNewLiveAlarms] = useState([]);
  const [alarmIdOfClicked, setAlarmIdOfClicked] = useState("");
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  const fetchMaintenaceData = () => {
    // here new api needed "maintennceByMachine"
    axios
      .get(`${dbConfig.url}/maintenance/getFromMachine/${mid}`)
      .then((response) => {
        const data = response.data?.data;
        const sortedData = data?.filter((data) => data?.mid == mid);
        setMaintenanceOfMachine(sortedData);
      })
      .catch((error) => {
        console.log("maintenance data fetch error:", error);
      });
  };

  const fetchAlarmsData = () => {
    setDataLoading(true);

    axios
      .get(`${dbConfig.url}/alarms_new`)
      .then((response) => {
        if (response.data?.success && Array.isArray(response.data.data)) {
          const filteredData = response.data.data.filter(
            (item) => item.mid === mid,
          );
          setNewLiveAlarms(filteredData);
        } else {
          console.warn("Unexpected data format:", response.data);
          setNewLiveAlarms([]);
        }
      })
      .catch((err) => {
        console.error("Error fetching alarms data:", err);
        setNewLiveAlarms([]);
      })
      .finally(() => {
        setDataLoading(false);
      });
  };

  useEffect(() => {
    fetchAlarmsData();
    fetchMaintenaceData();
  }, []);

  //   const onHastagSelect = (tag) => {
  //     //setSearchTerm("#"+tag.title)
  //     let temp = [...hastagsForFilter, tag]
  //     setHastagsForFilter([...new Set(temp)])
  //     //console.log("onclick", hastagsForFilter)
  //   }
  //
  const handleOnChangeSearchTerm = (e) => {
    // if (e.target.value == "" && hastagsForFilter.length > 0) {
    //   setSearchTerm('#')
    // }
    // else {
    setSearchTerm(e.target.value);
    //     }
  };
  //

  //   const removeSelectedHastag = (index) => {
  //     let temp = hastagsForFilter
  //     temp?.splice(index, 1);
  //     setHastagsForFilter([...temp])
  //   }

  //

  const defaultTypeSetter = (type) => {
    setDefaultType(type);
    // type can be decided by notifications/alarms also . so  maintenanceInfoSetter({}) is used
    //maintenanceInfoSetter()
  };

  const handleOpenForAttachMaintenance = (alarmInfoOfClicked) => {
    // console.log("alarm Info", alarmInfoOfClicked)
    setAlarmIdOfClicked(alarmInfoOfClicked?._id);
    setOpen2(true);
  };

  const commonCss = sharedCss();

  const filterNewAlarms = (sData) => {
    return (
      sData?.tag?.toLowerCase()?.search(searchTerm?.toLowerCase()) > -1 ||
      sData?.def_value?.toLowerCase()?.search(searchTerm?.toLowerCase()) > -1 ||
      sData?.value?.toLowerCase()?.search(searchTerm?.toLowerCase()) > -1
    );
  };

  const isFilteredAlarmDataAvailable = newLiveAlarms?.find(filterNewAlarms);

  console.log("isFilteredAlarmDataAvailable", isFilteredAlarmDataAvailable);
  console.log("newLiveAlarms", newLiveAlarms);

  return (
    <section className="machineDataViewPage">
      <MachineDataHeader />

      <div
        className={`allMachineDataPreviewContainer ${commonCss.innerSection} `}
      >
        <div
          className={`${commonCss.sectionContainer} ${commonCss.backgroundLight} border-radius-outer`}
        >
          <div className={commonCss.tableLable}>
            <Typography sx={{ ml: 2 }} fontWeight="bold" variant="h6">
              Alarm Management
            </Typography>
            <div className={commonCss.tableRightContent}>
              <div>
                <Tooltip title="Title, Values" arrow placement="top">
                  <TextField
                    label="Search"
                    className={commonCss.searchBox}
                    id="outlined-size-small"
                    value={searchTerm}
                    size="small"
                    onChange={(e) => handleOnChangeSearchTerm(e)}
                    type="text"
                    placeholder="Search ..."
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="end">
                          <SearchIcon style={{ opacity: "0.5" }} />
                        </InputAdornment>
                      ),
                    }}
                    // variant="standard"
                  />
                </Tooltip>

                {/* <div className="relative">
                  <TextField
                    value={searchTerm}
                    size="small"
                    // style={currentMode === 'Dark' ? { backgroundColor: '#212B36', color: 'white', border: '1px solid white' } : { border: '1px solid black' }}
                    onChange={(e) => handleOnChangeSearchTerm(e)}
                    type="text"
                    placeholder="Search ..."
                  /> */}
                {/* <button className="absolute top-1 right-3">
                    {" "}
                    <SearchIcon style={{ opacity: "0.5" }} />{" "}
                  </button> */}
                {/* <div className='bg-slate-600  '> */}
                {/* <div className='flex absolute flex-wrap  max-w-sm'>
                    {" "}
                    {hastagsForFilter?.map((data, index) => (
                      <div className='px-0.5 mx-0.5 mb-0.5 rounded-sm bg-cyan-50 shadow-sm text-xs'>
                        #{data.title}
                        <i
                          onClick={() => removeSelectedHastag(index)}
                          class='ri-close-circle-fill hover:text-red-500 hover:cursor-pointer'></i>
                      </div>
                    ))}
                  </div> */}
                {/* </div> */}
                {/* <div className="absolute top-12 backdrop-blur-sm z-10 px-1 max-h-36 shadow-md overflow-y-scroll"> */}
                {/* {searchTerm.startsWith("#") &&
                      hastTagsAll
                        ?.filter((fdata) =>
                          searchTerm?.slice(1)
                            ? fdata.title.includes(searchTerm?.slice(1))
                            : false
                        )
                        .map((hData) => (
                          <>
                            <div
                              onClick={() => onHastagSelect(hData)}
                              className='hover:cursor-pointer'>
                              {hData.title}
                            </div>
                          </>
                        ))} */}
                {/* </div> */}
                {/* </div> */}
              </div>
              {false && (
                <div style={{ display: "flex" }}>
                  {/* //Deprecated */}
                  {/* {defaultType === 0 ? (
                  <div className=" mr-6 p-3 cursor-pointer underline font-bold ">
                    Manage
                  </div>
                ) : (
                  <div
                    onClick={() => defaultTypeSetter(0)}
                    className="font-bold cursor-pointer   mr-6 p-3"
                  >
                    Manage
                  </div>
                )} */}

                  {defaultType === 1 ? (
                    <div className=" mr-6 p-3 cursor-pointer underline font-bold ">
                      Alarms
                    </div>
                  ) : (
                    <div
                      onClick={() => defaultTypeSetter(1)}
                      className="font-bold cursor-pointer   mr-6 p-3"
                    >
                      Alarms
                    </div>
                  )}

                  {/* {defaultType === 1 ? (
                  <div className='text-green-500 mr-6 p-3 cursor-pointer underline font-bold '>
                    Machine Breakdown
                  </div>
                ) : (
                  <div
                    onClick={() => defaultTypeSetter(1)}
                    className='font-bold cursor-pointer mr-6 p-3'>
                    Machine Breakdown
                  </div>
                )}
                {defaultType === 2 ? (
                  <div className='text-blue-500 mr-6 p-3 cursor-pointer underline font-bold '>
                    Routine
                  </div>
                ) : (
                  <div
                    onClick={() => defaultTypeSetter(2)}
                    className='font-bold cursor-pointer mr-6 p-3'>
                    Routine
                  </div>
                )} */}
                </div>
              )}
              <div>
                <div style={{ display: "flex" }}>
                  <CommonDropDown
                    dropDownLabel={"Select type"}
                    dropDownContainerStyle={{
                      maxHeight: "65px",
                      minWidth: "15vw",
                    }}
                    menuData={[
                      // {_id: "_id_", label: "Manage", value: 0},
                      { _id: "_id_", label: "Alarms", value: 1 },
                    ]}
                    menuValue={defaultType}
                    handleChange={(event) =>
                      setDefaultType(event?.target?.value ?? defaultType)
                    }
                    menuItemDisplay={"label"}
                    menuItemValue={"value"}
                  />
                </div>
              </div>
              <div style={{ paddingBlock: "1rem" }}>
                {/* <Button variant="contained" onClick={() => setOpen(true)}>
                  Add
                </Button> */}
              </div>
            </div>
          </div>

          <div className="liveDataContainer">
            {defaultType === 0 && (
              <TableContainer
                component={Paper}
                className="table border-radius-inner"
                sx={commonOuterContainerStyle}
              >
                <Table sx={{ minWidth: 650 }}>
                  <TableHead>
                    <TableRow>
                      {/* {defaultType === 0 ? (
											<TableCell
												style={
													currentMode === "Dark"
														? { color: "white" }
														: { color: "black" }
												}
												align='left'>
												Sensor Name
											</TableCell>
										) : (
											<TableCell
												style={
													currentMode === "Dark"
														? { color: "white" }
														: { color: "black" }
												}
												align='left'>
												Name
											</TableCell>
										)} */}
                      <TableCell
                        style={
                          currentMode === "Dark"
                            ? { color: "white" }
                            : { color: "black" }
                        }
                        align="left"
                      >
                        Name
                      </TableCell>

                      {/* {defaultType != 1 ? (
											<TableCell
												style={
													currentMode === "Dark"
														? { color: "white" }
														: { color: "black" }
												}
												align='left'>
												Period / Cycle
											</TableCell>
										) : null} */}
                      <TableCell
                        style={
                          currentMode === "Dark"
                            ? { color: "white" }
                            : { color: "black" }
                        }
                        align="left"
                      >
                        Maintenance
                      </TableCell>

                      <TableCell
                        style={
                          currentMode === "Dark"
                            ? { color: "white" }
                            : { color: "black" }
                        }
                        align="left"
                      >
                        Status
                      </TableCell>

                      <TableCell
                        style={
                          currentMode === "Dark"
                            ? { color: "white" }
                            : { color: "black" }
                        }
                        align="left"
                      >
                        Value
                      </TableCell>

                      <TableCell
                        style={
                          currentMode === "Dark"
                            ? { color: "white" }
                            : { color: "black" }
                        }
                        align="left"
                      >
                        Action
                      </TableCell>
                    </TableRow>
                  </TableHead>

                  <TableBody>
                    {activeAlarmsAll?.length ? (
                      activeAlarmsAll?.map((dataA, index) => (
                        <TableRow
                          key={dataA?.mid + index}
                          sx={{
                            "&:last-child td, &:last-child th": {
                              border: 0,
                            },
                          }}
                        >
                          <TableCell
                            style={{ borderBottom: "none" }}
                            align="left"
                          >
                            {/* {dataA?.tag} */}
                            {
                              liveAlarmsAll?.find(
                                (dataL) => dataL?.id == dataA?.alarm_id,
                              )?.tag
                            }
                          </TableCell>
                          <TableCell
                            style={{ borderBottom: "none" }}
                            align="left"
                          >
                            {dataA?.main_id}
                          </TableCell>

                          <TableCell
                            style={{ borderBottom: "none" }}
                            align="left"
                          >
                            not active
                          </TableCell>

                          <TableCell
                            style={{ borderBottom: "none" }}
                            align="left"
                          >
                            {/* {dataA?.value} */}
                            {
                              liveAlarmsAll?.find(
                                (dataL) => dataL?.id == dataA?.alarm_id,
                              )?.value
                            }
                          </TableCell>

                          <TableCell
                            style={{ borderBottom: "none" }}
                            align="left"
                          >
                            <Button variant="contained">Edit</Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      // {/* {details.length > 0 ? (
                      // 	details
                      // 		.filter((data) => {
                      // 			if (data.type === defaultType) {
                      // 				if (
                      // 					(searchTerm == "" &&
                      // 						hastagsForFilter?.length == 0) ||
                      // 					(searchTerm == "#" &&
                      // 						hastagsForFilter?.length == 0) ||
                      // 					(searchTerm?.startsWith("#") &&
                      // 						hastagsForFilter?.length == 0)
                      // 				) {
                      // 					return data;
                      // 				}
                      // 				//
                      // 				else if (
                      // 					searchTerm.startsWith("#") ||
                      // 					hastagsForFilter?.length > 0
                      // 				) {
                      // 					let temp = hastagsForFilter.filter(
                      // 						(filterData) => filterData.doc_id == data.id
                      // 					);
                      // 					if (temp?.length > 0) {
                      // 						return data;
                      // 					}
                      // 				} else if (
                      // 					data.title
                      // 						.toLowerCase()
                      // 						.includes(searchTerm.toLocaleLowerCase())
                      // 				) {
                      // 					return data;
                      // 				}
                      // 			}
                      // 		})
                      // 		.map((data, index) => (
                      // 			<>
                      // 				<MaintenanceItem
                      // 					key={index}
                      // 					data={data}
                      // 					machineName={machineName}
                      // 					defaultType={defaultType}
                      // 				/>
                      // 			</>
                      // 		)) */}

                      <>
                        {/*
                      <TableRow
                        sx={{
                          "&:last-child td, &:last-child th": {
                            border: 0,
                          },
                        }}
                      >
                        <TableCell
                          style={{ borderBottom: "none" }}
                          align="center"
                          colSpan={5}
                        >
                          {dataLoading && <CircularProgress />}
                          {!dataLoading && <>No Data</>}
                        </TableCell>
                      </TableRow>
                      */}
                        <NoDataComponent
                          dataLoading={dataLoading}
                          cellColSpan={5}
                        />
                      </>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* {defaultType === 1 && <div> In progress...</div>} */}
          </div>
        </div>
        {defaultType === 1 && (
          <div
            className={`${commonCss.sectionContainer} ${commonCss.backgroundLight} border-radius-outer`}
          >
            <div className="flex justify-between items-center">
              <Typography fontWeight="bold" variant="h6">
                Live Alarms
              </Typography>
              {/* <Button
                // onClick={() => setShowGraphNew(true)}
                variant="contained"
              >
                Show
              </Button> */}
            </div>
            <div>
              <TableContainer
                component={Paper}
                className="table border-radius-inner"
                sx={commonOuterContainerStyle}
              >
                <Table sx={{ minWidth: 650 }}>
                  <TableHeader
                    currentMode={currentMode}
                    columns={[
                      { label: "Title", align: "left" },
                      { label: "Maintenance", align: "left" },
                      { label: "Current Value", align: "left" },
                      { label: "Default Value", align: "left" },
                      { label: "Alarms Status", align: "center" },
                      { label: "Actions", align: "left" },
                    ]}
                  />
                  <TableBody>
                    {newLiveAlarms?.length > 0 &&
                      isFilteredAlarmDataAvailable &&
                      newLiveAlarms
                        ?.filter(filterNewAlarms)
                        ?.map((pData, index) => (
                          <>
                            <TableRow
                              // onClick={() => handleOpenForAttachMaintenance(pData)}
                              key={index}
                              hover
                              sx={{
                                "&:last-child td, &:last-child th": {
                                  border: 0,
                                },
                                "&:hover": { bgcolor: "#f5f5f5" },
                                borderBottom:
                                  newLiveAlarms?.length - 1 === index
                                    ? "none"
                                    : "0.05rem solid #e0e0e0",
                              }}
                              className="hover:shadow-md  hover:cursor-pointer"
                            >
                              <TableCell align="left">{pData?.tag}</TableCell>
                              <TableCell align="left">
                                {maintenanceOfMachine?.find(
                                  (mData) => mData?._id == pData?.main_id,
                                )?.title ?? "NA"}
                              </TableCell>
                              <TableCell align="left">{pData?.value}</TableCell>
                              <TableCell align="left">
                                {pData?.def_value}
                              </TableCell>
                              <TableCell align="center">
                                {pData?.def_value == pData?.value ? (
                                  <Badge color="success" variant="dot" />
                                ) : (
                                  <Badge color="error" variant="dot" />
                                )}
                              </TableCell>
                              <TableCell align="left">
                                <div className="flex gap-2">
                                  <Tooltip
                                    title="Edit Maintenance Attachment"
                                    arrow
                                  >
                                    <IconButton
                                      onClick={() =>
                                        handleOpenForAttachMaintenance(pData)
                                      }
                                      variant="outlined"
                                      size="small"
                                      color="secondary"
                                      sx={{ minWidth: "auto" }}
                                    >
                                      <Edit size={20} /> {/* Edit icon */}
                                    </IconButton>
                                  </Tooltip>
                                  {pData?.main_id && (
                                    <Tooltip title="View Details" arrow>
                                      <IconButton
                                        onClick={() => {
                                          const commonState = {
                                            alarmId: pData?._id,
                                            mainId: pData?.main_id,
                                            type: pData?.type,
                                          };
                                          switch (pData?.type) {
                                            case 0:
                                              history({
                                                pathname: `/calibration/${mid}`,
                                                state: commonState,
                                              });
                                              break;
                                            case 1:
                                              history({
                                                pathname: `/maintenance/${mid}`,
                                                state: commonState,
                                              });
                                              break;
                                            case 3:
                                              history({
                                                pathname: `/maintenance/${mid}`,
                                                state: commonState,
                                              });
                                              break;
                                            case 4:
                                              history({
                                                pathname: `/gemba/${mid}`,
                                                state: commonState,
                                              });
                                              break;
                                            case 5:
                                              history({
                                                pathname: `/lineClearance/${mid}`,
                                                state: commonState,
                                              });
                                              break;
                                            default:
                                              console.warn(
                                                `Unhandled type: ${pData?.type}`,
                                              );
                                              break;
                                          }
                                        }}
                                        variant="outlined"
                                        size="small"
                                        color="success"
                                        sx={{ minWidth: "auto" }}
                                      >
                                        <Visibility size={20} />
                                      </IconButton>
                                    </Tooltip>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          </>
                        ))}
                    {(!newLiveAlarms?.length ||
                      !isFilteredAlarmDataAvailable) && (
                      <>
                        {/*
                      <TableRow 
                      sx={{
                        "&:last-child td, &:last-child th": {
                          border: 0,
                        },
                      }}>
                        <TableCell 
                          colSpan={5}
                          align={"center"}
                          style={{ borderBottom: "none" }}>
                          No Data
                        </TableCell>
                      </TableRow>
                    */}
                        <NoDataComponent
                          dataLoading={dataLoading}
                          cellColSpan={6}
                        />
                      </>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          </div>
        )}
        {/* <Dialog open={open} fullWidth>
          <DialogTitle>Add Alarm</DialogTitle>
          // <DialogContent>
          //   {/* <AddMaintenance
					// 		mid={mid}
					// 		handleClose={() => setOpen(false)}
					// 		machineName={machineName}
					// 		useAt="MaintenanceDataCalibration" // also used in MaintenanceData
					// 	/> 
            <AddAlarm
              handleClose={() => setOpen(false)}
              liveAlarmsAll={liveAlarmsAll}
              activeAlarmsAll={activeAlarmsAll}
              maintenanceOfMachine={maintenanceOfMachine}
            />
          </DialogContent>
        </Dialog> */}

        {/* // attach maintenance */}
        <Dialog open={open2} fullWidth>
          <DialogTitle>Attach Maintenance</DialogTitle>
          <DialogContent>
            <div className="m1">
              <AttachMaintenance
                handleClose={() => setOpen2(false)}
                alarmId={alarmIdOfClicked}
                fetchAlarm={fetchAlarmsData}
                maintenanceOfMachine={maintenanceOfMachine}
              />
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </section>
  );
};

export default AlarmsNew;
