import React, { createContext, useContext, useEffect, useState } from "react";
import {
  companies,
  companyId_constant,
  maintenance,
} from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
const CalenderEventContext = createContext();
const CalenderMaintenaceEventContext = createContext();

export function useCalenderEventContext() {
  return useContext(CalenderEventContext);
}

export function useCalenderMaintenanceEventContext() {
  return useContext(CalenderMaintenaceEventContext);
}

const CalenderProvider = ({ children }) => {
  const [events, setEvents] = useState([]);
  const [maintenanceEvents, setMaintenanceEvents] = useState([]);

  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection("calendarEvents")
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     setEvents(data);
    //   });
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(maintenance)
    //   .onSnapshot((snap) => {
    //     let data = firebaseLooper(snap);
    // //     const obj = {
    // //       Jan: "01",
    // //       Feb: "02",
    // //       Mar: "03",
    // //       Apr: "04",
    // //       May: "05",
    // //       Jun: "06",
    // //       Jul: "07",
    // //       Aug: "08",
    // //       Sep: "09",
    // //       Oct: "10",
    // //       Nov: "11",
    // //       Dec: "12",
    // //     };
    // //     let event = [];
    // //     data.forEach((item) => {
    // //       let dueDate = new Date(item?.dueDate);
    // //       dueDate = dueDate?.toString().substring(4, 15);
    // //       dueDate =
    // //         dueDate.substring(7) +
    // //         "-" +
    // //         obj[dueDate.substring(0, 3)] +
    // //         "-" +
    // //         dueDate.substring(4, 7);
    // //       let last_done = item?.last_done?.toDate();
    // //       last_done = last_done?.toString().substring(4, 15);
    // //       last_done =
    // //         last_done.substring(7) +
    // //         "-" +
    // //         obj[last_done.substring(0, 3)] +
    // //         "-" +
    // //         last_done.substring(4, 7);
    // //       // console.log(last_done, "- ",dueDate)
    // //       const dueDateEvent = {
    // //         backgroundColor: "#FFABE1",
    // //         maintenance_id: item?.id,
    // //         start: dueDate.trim(),
    // //         title: `Due date - ${item?.title}`,
    // //       };
    // //       const lastDoneEvent = {
    // //         backgroundColor: "#FFABE1",
    // //         maintenance_id: item?.id,
    // //         start: last_done.trim(),
    // //         title: `Last Done - ${item?.title}`,
    // //       };
    // //       event.push(dueDateEvent);
    // //       event.push(lastDoneEvent);
    // //     });
    // //     setMaintenanceEvents(event);
    //   });
  }, []);

  return (
    <CalenderEventContext.Provider value={events}>
      <CalenderMaintenaceEventContext.Provider value={maintenanceEvents}>
        {children}
      </CalenderMaintenaceEventContext.Provider>
    </CalenderEventContext.Provider>
  );
};

export default CalenderProvider;
