import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Paper,
  TextField,
  FormControl,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
  Skeleton,
  TablePagination,
} from "@mui/material";
import axios from "axios";
import dayjs from "dayjs";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { makeStyles } from "@mui/styles";
import { toast } from "react-toastify";
import CfrRow from "../components/cfr/cfrRow";
import MyPDFPreview from "../components/pdf/Pdf-preview-modal";
import { useLoading } from "../hooks/LoadingProvider";
import { useUtils } from "../hooks/UtilsProvider";
import { formatDateTime } from "../components/Utils/timeUtils";
import { dbConfig } from "../infrastructure/db/db-config";
import { useCommonOuterContainerStyle } from "../styles/useCommonOuterContainerStyle";
import { sharedCss } from "../styles/sharedCss";
import MyDateTimePicker from "../components/ui/date-time-picker";

const useStyles = makeStyles(() => ({
  container: {
    maxHeight: "84vh",
  },
  tableHeaderCell: {
    fontWeight: "bold",
    fontSize: "0.9rem",
    height: "56px",
    padding: "16px",
  },
}));

export const filtersColumnsKeyLabel = {
  All: "All",
  role: "Role",
  email: "User",
  module: "Module",
  activity: "Event",
  description: "Activity",
};

export default function CFR() {
  const classes = useStyles();
  const commonOuterStyle = useCommonOuterContainerStyle();
  const sharedStyles = sharedCss();
  const { envData } = useUtils();
  const [loadingGlobal, setLoadingGlobal] = useLoading() || [false, () => {}];

  const [data, setData] = useState([]);
  const [filtered, setFiltered] = useState([]);
  const [filters, setFilters] = useState({
    from: dayjs().subtract(1, "month"),
    to: dayjs(),
    search: "",
    selected: ["All"],
  });
  const [loading, setLoading] = useState(true);
  const [datesValid, setDatesValid] = useState(true);
  const [preview, setPreview] = useState(false);
  const [pageInfo, setPageInfo] = useState({ page: 0, rowsPerPage: 10 });

  const TITLE = "Audit Trails";
  const APPLY_TEXT = "Apply";
  const CLEAR_TEXT = "Clear";
  const PDF_TEXT = "PDF Download";
  const SEARCH_PLACEHOLDER = "Module, Event, Activity...";

  useEffect(() => {
    fetchData(filters.from, filters.to);
  }, []);

  useEffect(() => {
    const term = filters.search.trim().toUpperCase();
    const result = data.filter((item) => {
      const keys = filters.selected.includes("All")
        ? Object.keys(item)
        : filters.selected;
      return keys.some((k) =>
        String(item[k] ?? "")
          .toUpperCase()
          .includes(term),
      );
    });
    setFiltered(result);
    setPageInfo({ page: 0, rowsPerPage: pageInfo.rowsPerPage });
  }, [filters.search, filters.selected, data]);

  const handleChangeFilters = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  async function fetchData(from, to) {
    setLoadingGlobal(true);
    setLoading(true);
    try {
      const res = await axios.get(`${dbConfig.url}/cfr`, {
        params: {
          startDate: dayjs(from).valueOf(),
          endDate: dayjs(to).valueOf(),
        },
      });
      setData(res.data);
    } catch (e) {
      toast.error("Error fetching Audit Trails");
    } finally {
      setLoadingGlobal(false);
      setLoading(false);
    }
  }

  const applyDates = () => {
    fetchData(filters.from, filters.to);
    setPageInfo((prev) => ({ ...prev, page: 0 }));
  };

  const clearDates = () => {
    setFilters((prev) => ({
      ...prev,
      from: dayjs().subtract(1, "month"),
      to: dayjs(),
    }));
    setPageInfo((prev) => ({ ...prev, page: 0 }));
  };

  const downloadPDF = async () => {
    setLoadingGlobal(true);
    setLoading(true);
    try {
      const res = await axios.get(`${envData.dbUrl}/cfr/pdf`, {
        params: {
          search: filters.search,
          fields: filters.selected,
          startDate: dayjs(filters.from).valueOf(),
          endDate: dayjs(filters.to).valueOf(),
        },
      });
      const token = JSON.parse(window.sessionStorage.getItem("@user-token"));
      window.open(`${envData.dbUrl}/${res.data.file}?token=${token}`, "_blank");
    } catch {
      toast.error("Server Error");
    } finally {
      setLoadingGlobal(false);
      setLoading(false);
    }
  };

  const handleSelectChange = (event) => {
    const value = event.target.value;

    // If "All" was selected before, and now user is selecting others
    if (filters.selected.includes("All")) {
      // Remove "All", and keep only the newly selected item(s) (excluding "All")
      const newSelection = value.filter((val) => val !== "All");
      handleChangeFilters("selected", newSelection);
      return;
    }

    // If "All" is selected now (and wasn't before), set only "All"
    if (value.includes("All")) {
      handleChangeFilters("selected", ["All"]);
      return;
    }

    // Normal selection
    handleChangeFilters("selected", value);
  };

  const handleChangePage = (_, newPage) =>
    setPageInfo((prev) => ({ ...prev, page: newPage }));

  const handleChangeRows = (e) =>
    setPageInfo({ page: 0, rowsPerPage: parseInt(e.target.value, 10) });

  const pageData = filtered.slice(
    pageInfo.page * pageInfo.rowsPerPage,
    pageInfo.page * pageInfo.rowsPerPage + pageInfo.rowsPerPage,
  );

  const headerStyle =
    envData.currentMode === "Dark"
      ? { backgroundColor: "#212B36", color: "white" }
      : { backgroundColor: "#E0E0E0", color: "black" };

  return (
    <>
      <section
        className={`${sharedStyles.sectionContainer} ${sharedStyles.backgroundLight} border-radius-outer`}
        style={{ padding: "1rem" }}
      >
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Typography variant="h4">{TITLE}</Typography>
          <Box sx={{ display: "flex", gap: 2 }}>
            <MyDateTimePicker
              label="From Date"
              slotProps={{ textField: { size: "small" } }}
              disableFuture
              value={filters.from}
              maxDateTime={filters.to}
              minDateTime={dayjs("2020-01-01 00:00:00")}
              size="small"
              onChange={(newValue) =>
                handleChangeFilters("from", dayjs(newValue))
              }
              onError={(error, value) =>
                setDatesValid(error === null && value !== null)
              }
            />
            <MyDateTimePicker
              label="To Date"
              slotProps={{ textField: { size: "small" } }}
              disableFuture
              size="small"
              minDateTime={filters.from}
              value={filters.to}
              onChange={(newValue) =>
                handleChangeFilters("to", dayjs(newValue))
              }
              onError={(error, value) =>
                setDatesValid(error === null && value !== null)
              }
            />
            <Button
              variant="contained"
              color="success"
              disabled={!datesValid}
              onClick={applyDates}
              sx={{ textTransform: "none", maxHeight: "40px" }}
            >
              {APPLY_TEXT}
            </Button>
            <Button
              variant="contained"
              onClick={clearDates}
              sx={{ textTransform: "none", maxHeight: "40px" }}
            >
              {CLEAR_TEXT}
            </Button>
            {/* <Button variant="outlined" onClick={downloadPDF} disabled={!filtered.length} sx={{ textTransform: "none", maxHeight: "40px" }}>
              {PDF_TEXT}
            </Button> */}
            <MyPDFPreview
              openModal={preview}
              setOpenModal={setPreview}
              fileName={`audit-trails-${formatDateTime(new Date())}.pdf`}
              modelTitle={TITLE}
            />
          </Box>
        </Box>

        <Box
          sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 2 }}
        >
          <FormControl size="small" sx={{ width: 300 }}>
            <Select
              multiple
              value={filters.selected}
              onChange={handleSelectChange}
              renderValue={(sel) =>
                sel.map((s) => filtersColumnsKeyLabel[s]).join(", ")
              }
              size="small"
            >
              {Object.keys(filtersColumnsKeyLabel).map((key) => (
                <MenuItem key={key} value={key}>
                  <Checkbox checked={filters.selected.includes(key)} />
                  <ListItemText primary={filtersColumnsKeyLabel[key]} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <TextField
            size="small"
            placeholder={SEARCH_PLACEHOLDER}
            value={filters.search}
            onChange={(e) => handleChangeFilters("search", e.target.value)}
            sx={{ width: 400 }}
          />
        </Box>
      </section>

      <section
        className={`${sharedStyles.sectionContainer} ${sharedStyles.backgroundLight} border-radius-outer mt-6`}
        style={{ padding: "1rem" }}
      >
        <TableContainer
          component={Paper}
          className="table border-radius-inner"
          sx={commonOuterStyle}
        >
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                {[
                  "Date/Time",
                  "Module",
                  "Event",
                  "Activity",
                  "User ID",
                  "Role",
                ].map((label, idx) => (
                  <TableCell
                    key={idx}
                    align={idx === 0 ? "center" : "left"}
                    sx={headerStyle}
                    className={classes.tableHeaderCell}
                  >
                    {label}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                Array.from({ length: pageInfo.rowsPerPage }).map((_, ridx) => (
                  <TableRow key={ridx}>
                    {Array.from({ length: 6 }).map((__, cidx) => (
                      <TableCell key={cidx} align="center">
                        <Skeleton />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : pageData.length ? (
                pageData.map((row, rIdx) => (
                  <CfrRow key={`${row._id}-${rIdx}`} row={row} />
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="h5" color="textSecondary">
                      No Data
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          component="div"
          count={filtered.length}
          page={pageInfo.page}
          rowsPerPage={pageInfo.rowsPerPage}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRows}
          rowsPerPageOptions={[5, 10, 25, 50]}
        />
      </section>
    </>
  );
}
