/* eslint-disable jsx-a11y/alt-text */
import { TableCell, TableRow } from "@mui/material";
import React from "react";
import { useStateContext } from "../../../context/ContextProvider";

const ApprovalTableItem = ({ data, useAt }) => {
  const { currentMode } = useStateContext();

  return (
    <TableRow
    // sx={{
    // 	backgroundColor: currentMode === 'Dark' ? "#212B36" : "#fff",
    // 	color: currentMode === 'Dark' ? 'white' : "black",
    // 	textTransform: 'capitalize',
    // }}
    >
      {useAt === "print" ? (
        <>
          {" "}
          <TableCell
            style={{
              border: "1px solid black",
              backgroundColor: "#fff",
              color: "black",
            }}
            align="left"
          >
            {data?.name}
          </TableCell>
          <TableCell align="center">
            <span>
              <img
                style={{ maxWidth: "300px", maxHeight: "200px" }}
                src={data?.url}
              />
            </span>
          </TableCell>
        </>
      ) : (
        <>
          <TableCell
            style={{
              border:
                currentMode === "Dark" ? "1px solid white" : "1px solid black",
              height: "1.8rem", // specially added
            }}
            align="left"
          >
            {data?.name ? data?.name : " "}
          </TableCell>
          <TableCell
            style={{
              border:
                currentMode === "Dark" ? "1px solid white" : "1px solid black",
            }}
            align="left"
          >
            {data?.email}
          </TableCell>
          <TableCell
            style={{
              border:
                currentMode === "Dark" ? "1px solid white" : "1px solid black",
            }}
            align="left"
          >
            {data?.status === true && "Approved"}
            {data?.status === false && "Not Approved"}
          </TableCell>
        </>
      )}

      {useAt === "print" ? (
        <TableCell
          style={{
            border: "1px solid black",
            backgroundColor: "#fff",
            color: "black",
          }}
          align="center"
        >
          {data?.date?.toDate().toString().substring(0, 15)}
        </TableCell>
      ) : (
        <TableCell
          style={{
            border:
              currentMode === "Dark" ? "1px solid white" : "1px solid black",
          }}
          align="center"
        >
          {data?.date?.toDate().toString().substring(0, 15)}
        </TableCell>
      )}
    </TableRow>
  );
};
export default ApprovalTableItem;
