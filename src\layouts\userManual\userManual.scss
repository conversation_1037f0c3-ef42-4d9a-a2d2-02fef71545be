.userManualSection {
  .addBtn {
    padding: 0.7rem 1.8rem;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    outline: none;
    cursor: pointer;
    border-radius: 8px;
    border: none;
    background-image: linear-gradient(
      310deg,
      rgb(33, 82, 255),
      rgb(33, 212, 253)
    );
    color: #fff;
    box-shadow:
      rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
      rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
    &:hover {
      opacity: 0.85;
    }
  }
  .infoContainer {
    width: 100%;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    color: #344767;
    padding: 1.5rem 1.2rem;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .info {
      display: flex;
      flex-direction: column;
      h3 {
        font-size: 1.4rem;
        font-weight: 500;
        margin-bottom: 0.7rem;
      }
    }
  }

  .carouselContainer {
    width: 100%;
    // background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    color: #344767;
    padding: 1.5rem 1.2rem;
    margin-top: 2rem;
    .carousel {
      width: 100%;
      box-shadow:
        0px 2px 1px -1px rgba(0, 0, 0, 0.2),
        0px 1px 1px 0px rgba(0, 0, 0, 0.14),
        0px 1px 3px 0px rgba(0, 0, 0, 0.12);
      border-radius: 10px;
      padding: 0.3rem 0.5rem;
      .carouselInner {
        border-radius: 10px;
        height: 750px;
        .carouselInnerHeaderContainer {
          width: 100%;
          height: 15%;
          box-shadow:
            0px 2px 1px -1px rgba(0, 0, 0, 0.2),
            0px 1px 1px 0px rgba(0, 0, 0, 0.14),
            0px 1px 3px 0px rgba(0, 0, 0, 0.12);
          // background-color: #2d2b2b;
          border-radius: 10px;
          color: #344767;
          padding: 1.5rem 1.2rem;
        }
        .carouselInnerContent {
          margin-top: 2rem;
          width: 100%;
          height: 80%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;

          border-radius: 10px;
          color: #344767;
          padding: 1.5rem 1.2rem;
          .imageContainer {
            width: 100%;
            height: 85%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1.5rem 0;

            .imgContainer {
              width: 70%;
              height: 97%;
            }

            video {
              border-radius: 20px;
              width: 80%;
              height: 97%;
            }
          }
          .subBtnsContainer {
            width: 100%;
            margin-top: 2rem;
            .subStep {
              padding: 0.7rem 1.8rem;
              font-size: 0.8rem;
              font-weight: bold;
              text-transform: uppercase;
              outline: none;
              cursor: pointer;
              border-radius: 8px;
              border: none;
              background-color: #e9ecef;
              color: #344767;
              box-shadow:
                rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
                rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
              margin-left: 1rem;
              &:hover {
                opacity: 0.85;
              }
            }
          }
        }
      }
    }
  }
}
