import React, { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  Paper,
  IconButton,
} from "@mui/material";
import { Delete, Edit } from "@mui/icons-material";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import NoDataComponent from "../commons/noData.component";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../not-accessible/not-accessible";

const Arviews = () => {
  const [canvasList, setCanvasList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  const hasCanvasPOSTAccess = useCheckAccess("arview", "POST");
  const hasCanvasGETAccess = useCheckAccess("arview", "GET");
  const hasCanvasDELETEAccess = useCheckAccess("arview", "DELETE");
  const hasCanvasPUTAccess = useCheckAccess("arview", "PUT");

  useEffect(() => {
    fetchCanvases();
  }, []);

  const fetchCanvases = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${dbConfig.url}/arview`);
      setCanvasList(response.data);
    } catch (error) {
      console.error("Error fetching canvases:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await axios.delete(`${dbConfig.url}/arview/${id}`);
      fetchCanvases();
    } catch (error) {
      console.error("Error deleting canvas:", error);
    }
  };

  const handleExport = async (id) => {
    try {
      const response = await axios.get(`${dbConfig.url}/arview/${id}`);
      const canvasData = response.data;
      const blob = new Blob([JSON.stringify(canvasData, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `canvas_${id}.json`;
      link.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error exporting canvas:", error);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Paper sx={{ width: "100%", overflow: "hidden" }}>
      <Button
        variant="contained"
        color="primary"
        onClick={() => {
          window.location.href = "/arview/new";
        }}
        sx={{ margin: 2 }}
        disabled={!hasCanvasPOSTAccess}
      >
        Create New Canvas
      </Button>
      {hasCanvasGETAccess ? (
        <>
          <TableContainer>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>Title</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {canvasList
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((canvas) => (
                    <TableRow key={canvas._id} hover>
                      <TableCell>{canvas.title || "Untitled"}</TableCell>
                      <TableCell>
                        {canvas.description || "No description"}
                      </TableCell>
                      <TableCell>
                        <IconButton
                          onClick={() => {
                            window.location.href = `/arview/${canvas._id}`;
                          }}
                          disabled={!hasCanvasPUTAccess}
                          sx={{
                            color: !hasCanvasPUTAccess
                              ? "grey.500"
                              : "primary.main",
                          }}
                        >
                          <Edit />
                        </IconButton>

                        <IconButton
                          onClick={() => handleDelete(canvas._id)}
                          disabled={!hasCanvasDELETEAccess}
                          sx={{
                            color: !hasCanvasDELETEAccess
                              ? "grey.500"
                              : "secondary.main",
                          }}
                        >
                          <Delete />
                        </IconButton>

                        <Button
                          variant="outlined"
                          onClick={() => handleExport(canvas._id)}
                          disabled={!hasCanvasGETAccess}
                        >
                          Export JSON
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                {!canvasList.length && (
                  <>
                    <NoDataComponent dataLoading={loading} cellColSpan={3} />
                  </>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          {!!canvasList.length && (
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={canvasList.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          )}
        </>
      ) : (
        <NotAccessible />
      )}
    </Paper>
  );
};

export default Arviews;
