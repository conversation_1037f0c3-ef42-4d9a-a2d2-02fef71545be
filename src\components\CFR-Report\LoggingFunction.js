import { db } from "../../firebase";
import { companies, companyId_constant } from "../../constants/data";

let timer = null;
export default function LoggingFunction(
  machine,
  activity,
  username,
  module,
  description,
) {
  const dateTime = new Date();
  let dataObj = {
    machine,
    activity,
    username,
    module,
    description,
    dateTime,
  };
  if (timer) clearTimeout(timer);
  timer = setTimeout(() => {
    // db.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection("cfrReport")
    // 	.add(dataObj);
    // timer = null;
  }, 1000);
}
