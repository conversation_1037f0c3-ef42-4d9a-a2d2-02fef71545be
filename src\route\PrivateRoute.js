import React from "react";
import { Navigate, useLocation } from "react-router-dom"; // Use Navigate for react-router-dom v6+
import { useAuth } from "../hooks/AuthProvider";

const PrivateRoute = ({ component: Component, ...rest }) => {
  const { currentUser } = useAuth();
  const location = useLocation();

  console.log("PrivateRoute is rendering. Current User:", currentUser);

  if (!currentUser || currentUser.email === "<EMAIL>") {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <Component {...rest} />;
};

export default PrivateRoute;
