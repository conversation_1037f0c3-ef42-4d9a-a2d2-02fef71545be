import React from "react";
import {
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Box,
} from "@mui/material";

const MachineDowntimeProbability = ({ values, forecastValues }) => {
  const calculateDowntimeProbability = (values, forecastValues) => {
    if (!values || !forecastValues || values.length < 5) return 0;

    const avg =
      values.reduce((sum, val) => sum + (val || 0), 0) / values.length;
    const variance =
      values.reduce((sum, val) => sum + Math.pow((val || 0) - avg, 2), 0) /
      values.length;
    const stdDev = Math.sqrt(variance);

    let dropCount = 0;
    forecastValues.forEach((val, i) => {
      if (val !== null && values[i] !== null && val < values[i] * 0.8) {
        dropCount++;
      }
    });

    return Math.min(100, ((stdDev / avg) * 100 + dropCount * 10).toFixed(2));
  };

  const downtimeProbability = calculateDowntimeProbability(
    values,
    forecastValues,
  );

  const getColor = () => {
    if (downtimeProbability < 30) return "success";
    if (downtimeProbability < 60) return "warning";
    return "error";
  };

  return (
    <Card sx={{ p: 1.5, height: "150px", textAlign: "center", mt: 4 }}>
      <CardContent sx={{ p: 0 }}>
        <Typography variant="body1" align="center" sx={{ fontWeight: "bold" }}>
          Downtime Probability
        </Typography>
        <Box display="flex" flexDirection="column" alignItems="center" mt={1}>
          <Typography variant="h6" sx={{ fontWeight: "bold", mb: 0.5 }}>
            {downtimeProbability}%
          </Typography>
          <CircularProgress
            variant="determinate"
            value={downtimeProbability}
            color={getColor()}
            size={50}
            thickness={4}
          />
        </Box>
      </CardContent>
    </Card>
  );
};

export default MachineDowntimeProbability;
