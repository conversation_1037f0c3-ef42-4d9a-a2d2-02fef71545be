import React, { useContext, useState } from "react";
import axios from "axios";

import { dbConfig } from "../../infrastructure/db/db-config";

const CreateMachineContext = React.createContext();
const UpdateMachineContext = React.createContext();
const DeleteMachineContext = React.createContext();

export function useCreateMachineCfr() {
  return useContext(CreateMachineContext);
}

export function useEditMachineCfr() {
  return useContext(UpdateMachineContext);
}

export function useDeleteMachineCfr() {
  return useContext(DeleteMachineContext);
}

export function MachineCfrContextProvider({ children }) {
  function handleMachineCreate(sessionData) {
    const MachineCreate = async (sessionData) => {
      await axios
        .post(`${dbConfig.url}/cfr/machine_events`, sessionData)
        .then((response) => {
          console.log("cfr-createMachineData", sessionData);
        })
        .catch((error) => {
          console.error(error);
        });
    };

    MachineCreate(sessionData);
  }

  function handleMachineUpdate(sessionData) {
    const MachineUpdate = async (sessionData) => {
      await axios
        .post(`${dbConfig.url}/cfr/machine_events`, sessionData)
        .then((response) => {
          console.log("cfr-createMachineData", sessionData);
        })
        .catch((error) => {
          console.error(error);
        });
    };

    MachineUpdate(sessionData);
  }

  function handleMachineDelete(sessionData) {
    const MachineDelete = async (sessionData) => {
      await axios
        .post(`${dbConfig.url}/cfr/machine_events`, sessionData)
        .then((response) => {
          console.log("cfr-createMachineData", sessionData);
        })
        .catch((error) => {
          console.error(error);
        });
    };

    MachineDelete(sessionData);
  }

  return (
    <CreateMachineContext.Provider value={handleMachineCreate}>
      <DeleteMachineContext.Provider value={handleMachineDelete}>
        <UpdateMachineContext.Provider value={handleMachineUpdate}>
          {children}
        </UpdateMachineContext.Provider>
      </DeleteMachineContext.Provider>
    </CreateMachineContext.Provider>
  );
}
