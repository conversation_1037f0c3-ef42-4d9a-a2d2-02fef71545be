import { Link } from "react-router-dom";
import folderImg from "../../assets/images/folder.png";
import { Card, Typography } from "@mui/material";
import { useStateContext } from "../../context/ContextProvider";

import { makeStyles } from "@mui/styles";
const useStyles = makeStyles((theme) => ({
  folderContainer: {
    backgroundColor: theme.palette.custom.backgroundThird,
    color: theme.palette.custom.textColor,
    paddingBlock: "1rem 1.5rem",
    maxWidth: "16rem",
  },
}));

const FolderCardDashboard = ({ grid, folder, readOnly }) => {
  const { currentMode } = useStateContext();
  const classes = useStyles();
  return (
    <Card variant="outlined" className={classes.folderContainer}>
      <center>
        <div
          style={{ maxWidth: "25%", margin: "auto" }}
          className="folderImgContainer"
        >
          <Link
            to={{
              pathname: `/file-manager/folder/${folder?._id}`,
              state: { folder: folder },
            }}
          >
            <img
              style={{ width: "100%", height: "100%" }}
              src={folderImg}
              alt=""
            />
          </Link>
        </div>
        <div className="folderTitleContainer">
          <Link
            to={{
              pathname: `/file-manager/folder/${folder?._id}`,
              state: { folder: folder },
            }}
            style={{ color: "black", textDecoration: "none" }} // 👈 set color to black
          >
            {folder?.name}{" "}
          </Link>
        </div>

        <div style={{ display: "flex", justifyContent: "space-around" }}>
          {/* <p className="font-bold mr-2">Created at:</p>
          <p>{folder?.createdAt.substring(0,15)}</p> */}
          <Typography variant="subtitle2">Created At: &nbsp;</Typography>
          <Typography variant="subtitle2">
            {folder?.created_at?.substring(0, 10)}
          </Typography>
        </div>
      </center>
    </Card>
  );
};

export default FolderCardDashboard;
