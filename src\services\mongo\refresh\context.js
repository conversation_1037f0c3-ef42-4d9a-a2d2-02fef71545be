import React, { createContext, useContext, useState } from "react";

const MongoRefreshContext = createContext();

export const MongoRefreshProvider = ({ children }) => {
  const [refreshCount, setRefreshCount] = useState(0);

  console.log(
    "MongoRefreshProvider initialized with refreshCount:",
    refreshCount,
  );

  return (
    <MongoRefreshContext.Provider value={{ refreshCount, setRefreshCount }}>
      {children}
    </MongoRefreshContext.Provider>
  );
};

export const useMongoRefresh = () => {
  const context = useContext(MongoRefreshContext);
  if (!context) {
    throw new Error(
      "useMongoRefresh must be used within a MongoRefreshProvider.",
    );
  }
  console.log("useMongoRefresh accessed. Context:", context);
  return context;
};
