// now not in use , can be deleted
import React, { useState } from "react";
import DataReports from "../layouts/Reports/DataReports";
import DataReportsPrint from "../layouts/Reports/print/DataReportsPrint";
import PrintReportPdfMain from "../layouts/Reports/print/PrintReportPdfMain";

const PrintReportsPage = () => {
  const [printButtonVisivility, setPrintButtonVisibility] = useState(true);

  const handlePrint = () => {
    let promise = new Promise((resolve, reject) => {
      resolve();
    });
    promise
      .then(() => {
        setPrintButtonVisibility(false);
      })
      .then(() => {
        return new Promise((resolve, reject) => {
          window.print();
          resolve();
        });
      })
      .then(() => {
        window.close();
      });
  };
  return (
    <div>
      {/* <DataReportsPrint type="print" />  */}
      {/* <PrintReportPdfMain type = 'print' /> */}

      {printButtonVisivility && (
        <div className="fixed bottom-8 right-1">
          <button
            className="bg-gray-400 hover:bg-gray-500 text-white font-bold py-1 px-2 m-1 rounded animate-bounce"
            onClick={() => handlePrint()}
          >
            Print
          </button>
          <div className="bg-gray-400 hover:bg-gray-500 active:bg-gray-300 text-white font-bold py-1 px-2 m-1 rounded animate-bounce">
            <PrintReportPdfMain type="print" />
          </div>
        </div>
      )}
    </div>
  );
};

export default PrintReportsPage;
