import React, { useState, useEffect } from "react";
import "./Notification.scss";

import { db } from "../../firebase";
import { companies, companyId_constant } from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";

import { useStateContext } from "../../context/ContextProvider";
import AccItem from "./AccItem";
import { Box } from "@mui/material";

const NotificationBox = () => {
  // const dbref = db
  //   .collection(companies)
  //   .doc(companyId_constant)
  //   .collection("videoCallData");

  const [alerts, setAlerts] = useState([]);
  const [allMachines, setAllMachines] = useState([]);
  const [selectedMachine, setSelectedMachine] = useState([]);
  const [selectedMachineValue, setSelectedMachineValue] = useState(""); // to show selected name
  const [machineHaveAlerts, setMachineHaveAlerts] = useState(true); //

  const { currentColor, currentMode, currentColorLight } = useStateContext();

  // useEffect(() => {
  // dbref.onSnapshot((snap) => {
  //   const data = firebaseLooper(snap);
  //   data?.sort(function (a, b) {

  //     return a.index - b.index;
  //   });
  //   setAlerts(data);
  // });

  // }, []);

  //
  // const handleChangeSelect = (event) => {
  //   setSelectedMachine([event.target.value]);
  //   setSelectedMachineValue(event.target.value);
  //   if (event.target.value == "all") {
  //     setSelectedMachine([]);
  //   }

  //   if (event.target.value != "all") {
  //     var temp = 0;
  //     alerts?.map((data) => {
  //       if (data?.mid == event.target.value.id) {
  //         temp += 1;
  //       }
  //     });
  //     if (temp == "0") {
  //       setMachineHaveAlerts(false);
  //     } else {
  //       setMachineHaveAlerts(true);
  //     }
  //   }
  // };

  return (
    <Box
      sx={
        currentMode === "Dark"
          ? { color: "white" }
          : { backgroundColor: "white" }
      }
    >
      <div
        style={{ marginTop: "2%", fontSize: "20px", marginBottom: "5%" }}
        className="heading"
      >
        Notifications
      </div>

      <ul
        sx={
          currentMode === "Dark"
            ? { backgroundColor: "#161C24", color: "white" }
            : { backgroundColor: "white" }
        }
        className="notificationBoxWrapper"
      >
        {alerts
          .slice(alerts.length - 5, alerts.length)
          .filter((item) => item?.status === "waiting")
          .map((data) => (
            <div
              style={{
                border: "1px solid gray",
                marginBottom: "10px",
                padding: "20px",
              }}
            >
              <div>
                <b>
                  Call By :{" "}
                  <Box style={{ color: "lightblue" }}> {data?.callerId} </Box>
                </b>
              </div>
              <div>
                Status : <b>{data?.status}</b>
              </div>
              <AccItem callId={data?.call_id} />
            </div>
          ))}
      </ul>
    </Box>
  );
};

export default NotificationBox;
