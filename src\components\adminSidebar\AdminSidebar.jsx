import React, { useContext } from "react";
import { AdminMenuData } from "./AdminMenuData";
import { NavLink } from "react-router-dom";
import { ThemeContext } from "../../context/ThemeContext";
import { useAuth } from "../../hooks/AuthProvider";
import { useStateContext } from "../../context/ContextProvider";
import { ButtonBasic } from "../buttons/Buttons";
import logo from "../../assets/images/logo.png";

const AdminSidebar = ({ inactive = false }) => {
  const theme = useContext(ThemeContext);
  const darkMode = theme?.state?.darkMode || false;
  const { logout } = useAuth();
  const { currentColor, currentMode, currentColorLight } = useStateContext();

  const handleLogout = () => {
    window.localStorage.removeItem("companyId");
    window.localStorage.removeItem("adminType");
    logout();
  };

  if (!AdminMenuData || AdminMenuData.length === 0) {
    console.warn("AdminSidebar: AdminMenuData is empty or undefined.");
    return <div>No menu items available.</div>;
  }

  return (
    <>
      <aside
        className={`drawer ${inactive ? "inactive" : ""} aside ${
          currentMode === "Dark" ? "aside-dark" : "aside-light"
        }`}
      >
        <div className="logo">
          <div className="logoImg">
            <img className="rounded-md" src={logo} alt="logo" />
          </div>
          <h4>Arizon ADMIN</h4>
        </div>
        <hr />
        {AdminMenuData.map((item, index) => (
          <div className="menu" key={index}>
            <div className="listHeading">{item.listHeading}</div>
            <ul>
              {item.link.map((links, index) => (
                <NavLink
                  to={links.path}
                  className="navLink"
                  style={({ isActive }) =>
                    isActive
                      ? currentMode === "Dark"
                        ? {
                            border: `1px solid ${currentColor}`,
                            color: "white",
                            borderRadius: 12,
                          }
                        : {
                            border: `1px solid ${currentColor}`,
                            color: "black",
                            borderRadius: 12,
                          }
                      : {}
                  }
                  key={index}
                >
                  <li className="link">
                    <div
                      style={
                        currentMode === "Dark"
                          ? { backgroundColor: currentColor, color: "white" }
                          : { backgroundColor: currentColor, color: "white" }
                      }
                      className="icon"
                    >
                      {links.icon}
                    </div>
                    <div className="text">{links.text}</div>
                  </li>
                </NavLink>
              ))}
            </ul>
            <hr />
          </div>
        ))}
        <div className="flex justify-items-center justify-center">
          <ButtonBasic buttonTitle="Logout" onClick={handleLogout} />
        </div>
      </aside>
    </>
  );
};

export default AdminSidebar;
