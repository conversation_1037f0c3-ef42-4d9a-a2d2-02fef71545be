import React from "react";
import useState from "react";
import { <PERSON> } from "react-router-dom";
import "./videoCall.scss";
import { useStateContext } from "../../context/ContextProvider";
import { Button, Box, Typography } from "@mui/material";
import VideoCallIcon from "@mui/icons-material/VideoCall";
import { sharedCss } from "../../styles/sharedCss";
import JitsiMeeting from "../../components/Video/JitsiMeeting";

const VideoCall = () => {
  const { currentColor, currentMode } = useStateContext();
  const commonCss = sharedCss();
  // const [name, setName] = useState("");
  // const [joined, setJoined] = useState(false);

  return (<>
    <section className="videoCallLandingPage">
      <Box
        className={`confirmationBox ${commonCss.backgroundLight}`}
        sx={{
          padding: "2rem",
          borderRadius: "12px",
          boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
          textAlign: "center",
          maxWidth: "500px",
          margin: "0 auto",
        }}
      >
        <VideoCallIcon sx={{ fontSize: 60, color: currentColor, mb: 2 }} />
        <Typography variant="h5" className="confirmationTitle" sx={{ mb: 3 }}>
          Do you want to enter the live video call session?
        </Typography>
        <div className="confirmationBtn">
          <Link to="/video-jitsi" style={{ textDecoration: "none" }}>
            <Button
              variant="contained"
              startIcon={<VideoCallIcon />}
              sx={{
                backgroundColor: currentColor,
                "&:hover": {
                  backgroundColor: currentColor,
                  opacity: 0.9,
                },
                padding: "10px 30px",
                fontSize: "1.1rem",
              }}
            >
              Join Video Call
            </Button>
          </Link>
        </div>
      </Box>
    </section>
      {/* <JitsiMeeting roomName="helloTeam" userName="deepak"/> */}
    </>
  );
};

export default VideoCall;
