import React, { useEffect, useState } from "react";
import { toastMessage, toastMessageSuccess } from "../../../tools/toast";
import { useStateContext } from "../../../context/ContextProvider";
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
} from "@mui/material";
import { useParams } from "react-router-dom";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";

export default function AttachMaintenance({
  handleClose,
  maintenanceOfMachine,
  alarmId,
  fetchAlarm,
}) {
  const [selectedMaintenance, setSelectedMaintenance] = useState("");
  const [selectedMainType, setSelectedMainType] = useState(1); // 0 = "calibration", 1 = "maintenance"
  const { currentMode } = useStateContext();
  const { mid } = useParams();

  // Fetch the current alarm data to pre-populate the selected maintenance
  useEffect(() => {
    const fetchCurrentAlarm = async () => {
      try {
        const response = await axios.get(
          `${dbConfig.url}/alarms_new/${alarmId}`,
        );
        const alarmData = response.data.data;
        if (alarmData) {
          setSelectedMaintenance(alarmData.main_id || "");
          setSelectedMainType(
            alarmData.type !== undefined ? alarmData.type : 1,
          );
        }
      } catch (error) {
        console.error("Error fetching current alarm data:", error);
      }
    };
    fetchCurrentAlarm();
  }, [alarmId]);

  function handleSubmit(e) {
    e.preventDefault();
    const data = {
      main_id: selectedMaintenance,
      type: selectedMainType,
    };

    axios
      .put(`${dbConfig.url}/alarms_new/${alarmId}`, data)
      .then((response) => {
        toastMessageSuccess({
          message: "Maintenance attached successfully...",
        });
        console.log("alarm update:", data);
        fetchAlarm(); // Refresh the alarms data
        handleClose(); // Close the dialog after successful submission
      })
      .catch((error) => {
        toastMessage({ message: "Maintenance attachment failed..." });
        console.log("maintenance attachment error:", error);
      });
  }

  const handleSelectedMaintenance = (_id) => {
    setSelectedMaintenance(_id);
    const maintenance = maintenanceOfMachine?.find(
      (fData) => fData?._id === _id,
    );
    setSelectedMainType(maintenance?.type !== undefined ? maintenance.type : 1);
  };

  return (
    <>
      <form onSubmit={handleSubmit}>
        <FormControl
          style={{ marginBottom: "12px" }}
          variant="outlined"
          label="Select Maintenance"
          fullWidth
        >
          <InputLabel className="mb-1">Select Maintenance</InputLabel>
          <Select
            required
            value={selectedMaintenance}
            label="Select Maintenance"
            onChange={(e) => handleSelectedMaintenance(e.target.value)}
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {maintenanceOfMachine?.map((mdata) => (
              <MenuItem key={mdata?._id} value={mdata?._id}>
                {mdata?.title}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <div className="p-2 mt-2 flex justify-between">
          <Button color="error" variant="contained" onClick={handleClose}>
            Cancel
          </Button>
          <Button type="submit" color="primary" variant="contained">
            Submit
          </Button>
        </div>
      </form>
    </>
  );
}
