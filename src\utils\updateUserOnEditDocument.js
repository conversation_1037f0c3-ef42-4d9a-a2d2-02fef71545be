import { companies, companyId_constant, fatReport } from "../constants/data";
import { db } from "../firebase";
import { toastMessageSuccess } from "../tools/toast";

export const updateUserOnEditDocument = ({
  userEmail,
  docId,
  fatDataId,
  date,
}) => {
  // db.collection(companies).doc(companyId_constant)
  // .collection(fatReport).doc(docId)
  // .collection('fatData').doc(fatDataId)
  // .collection('approval')
  // .add({name: userEmail, url: '', date: new Date()})
  // .then(() => {
  //     toastMessageSuccess({message: 'Added to Approval'})
  // })
};
