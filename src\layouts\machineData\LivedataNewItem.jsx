import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  IconButton,
  TableCell,
  TableRow,
} from "@mui/material";
import React, { useState } from "react";
import { useStateContext } from "../../context/ContextProvider";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import EditLiveData from "../../components/LiveData/EditLiveData";
import Delete from "../../components/Delete/Delete";
import { db } from "../../firebase";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { NavLink } from "react-router-dom";
import { themeColors } from "../../infrastructure/theme";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { useDeleteMachineCfr } from "../../hooks/cfr/machineCfrProvider";
import { useAuth } from "../../hooks/AuthProvider";
import { useCheckAccess } from "../../utils/useCheckAccess";

const LivedataNewItem = ({ data, lastItem = false }) => {
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const { currentUser } = useAuth();

  const deletelivedatacfr = useDeleteMachineCfr();
  const date = new Date();
  const data2 = {
    activity: "live data deleted",
    dateTime: date,

    description: " a live data is deleted",
    machine: data.mid,
    module: "Live data",
    username: currentUser.username,
  };

  const hasImageAnnotationPUTAccess = useCheckAccess(
    "imageAnnotationModules",
    "PUT",
  );
  const hasImageAnnotationDELETEAccess = useCheckAccess(
    "imageAnnotationModules",
    "DELETE",
  );

  const onDelete = async () => {
    // Delete image file from storage
    axios
      .post(`${dbConfig?.url_storage}/deleteImage`, { file_name: data.img_url })
      .then((res1) => {
        // Image deleted successfully
        console.log(res1.data?.message, "Image deleted successfully");
      })
      .catch((err) => {
        console.log("Delete image from storage error:", err);
      });

    // Delete record from the collection
    await axios
      .delete(`${dbConfig.url}/imageAnnotationModules/${data._id}`)
      .then(() => {
        deletelivedatacfr(data2);
        toastMessageSuccess({ message: "Deleted Successfully!" });
        setRefreshCount(refreshCount + 1);
        setOpenDel(false);
      })
      .catch((err) => {
        toastMessage({ message: err.message });
      });
  };

  return (
    <>
      <TableRow
        hover
        sx={{
          "&:hover": { bgcolor: "#f5f5f5" },
          "&:last-child td, &:last-child th": { border: 0 },
          borderBottom: lastItem ? "none" : "0.05rem solid #e0e0e0",
        }}
        style={{ cursor: "pointer" }}
      >
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="left"
        >
          {data.title}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="left"
        >
          {data.desc}
        </TableCell>
        <TableCell
          className="uppercase "
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="center"
        >
          {data?.type}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="center"
        >
          <IconButton
            onClick={() => setOpenEdit(true)}
            disabled={!hasImageAnnotationPUTAccess}
            sx={{
              color: !hasImageAnnotationPUTAccess ? "grey.500" : "primary.main",
            }}
          >
            <EditIcon style={{ fontSize: "20px" }} />
          </IconButton>

          <IconButton
            onClick={() => setOpenDel(true)}
            sx={{
              margin: "0px 16px",
              color: !hasImageAnnotationDELETEAccess ? "grey.500" : "#f00",
            }}
            disabled={!hasImageAnnotationDELETEAccess}
          >
            <DeleteIcon style={{ fontSize: "20px" }} />
          </IconButton>

          <NavLink to={`/annotations/${data._id}`}>
            <IconButton>
              <ArrowForwardIcon />
            </IconButton>
          </NavLink>
        </TableCell>
      </TableRow>

      <Dialog open={openDel}>
        <Delete onClose={() => setOpenDel(false)} onDelete={onDelete} />
      </Dialog>

      <Dialog open={openEdit} fullWidth>
        <DialogTitle
          style={
            currentMode === "Dark"
              ? { backgroundColor: themeColors.dark.secordary }
              : { backgroundColor: "white" }
          }
        >
          Edit Live Data - [{data.title}]
        </DialogTitle>
        <DialogContent
          style={
            currentMode === "Dark"
              ? { backgroundColor: themeColors.dark.secordary }
              : { backgroundColor: "white" }
          }
        >
          <EditLiveData annData={data} onClose={() => setOpenEdit(false)} />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default LivedataNewItem;
