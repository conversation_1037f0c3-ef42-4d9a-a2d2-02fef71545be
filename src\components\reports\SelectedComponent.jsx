import React, { useState, useEffect } from "react";
import { makeStyles } from "@material-ui/core/styles";
import {
  Card,
  Typography,
  Button,
  Tooltip,
  Chip,
  TextField,
} from "@material-ui/core";
import AttachFileIcon from "@material-ui/icons/AttachFile";
import { dbConfig } from "../../infrastructure/db/db-config";
import { Box, CircularProgress, IconButton } from "@mui/material";
import { MdAddComment } from "react-icons/md";
import { toastMessage } from "../../tools/toast";
import { ImEnlarge } from "react-icons/im";

const useStyles = makeStyles((theme) => ({
  cardContainer: {
    width: "100%",
    minHeight: "250px",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
    display: "flex",
    flexDirection: "row",
  },
  leftSection: {
    flex: 6, // 70% width
    padding: theme.spacing(1),
  },
  rightSection: {
    flex: 4, // 30% width
    padding: theme.spacing(1),
    background: "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",
    borderLeft: "1px solid #e0e0e0",
    display: "flex",
    flexDirection: "column",
    gap: theme.spacing(1),
    // maxWidth: "30%", // Ensure it does not exceed 30%
  },
  container: {
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderRadius: "8px",
    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
    padding: theme.spacing(0.5),
  },
  title: {
    textAlign: "left",
    fontSize: "15px",
    fontWeight: "bold",
    marginBottom: theme.spacing(2),
  },
  mediaContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
  },
  media: {
    width: "100%",
    maxHeight: "350px",
    objectFit: "contain",
  },
  attachmentsContainer: {
    display: "flex",
    flexDirection: "column", // Change to column to stack items vertically
    maxHeight: "80px", // Increased height to show more attachments
    overflowY: "auto", // Enable vertical scrolling if needed
    paddingRight: theme.spacing(0.5), // Prevent horizontal scrollbar
  },
  qrCodesContainer: {
    display: "flex",
    flexDirection: "column", // Stack QR codes vertically
    maxHeight: "50px", // Adjust the height as needed
    overflowY: "auto", // Enable scrolling
    paddingRight: theme.spacing(0.5),
  },
  chip: {
    fontSize: "12px", // Reduce font size of the chip label
    height: "24px", // Reduce chip height for compactness
    marginRight: theme.spacing(0.5), // Smaller margin for tighter spacing
    marginBottom: theme.spacing(0.5),
  },
  commentInput: {
    width: "100%",
  },
  commentButton: {
    // marginTop: theme.spacing(0.5),
    textAlign: "right",
  },
}));

const SelectedComponent = ({ item, setEnlarge, setEValue, index }) => {
  const classes = useStyles();
  const [isVideoLoading, setIsVideoLoading] = useState(true);
  // const [tempRemarks, setTempRemarks] = useState(remarks || "");
  // const [error, setError] = useState(""); // Add error state for validation feedback

  // Sync tempRemarks with remarks when remarks prop changes
  // useEffect(() => {
  //   setTempRemarks(remarks || "");
  // }, [remarks]);

  const renderMedia = () => {
    if (item?.format === "image") {
      return (
        <img
          src={`${dbConfig.url_storage}/${item?.url}`}
          alt={item.url}
          className={classes.media}
        />
      );
    } else if (item?.format === "video") {
      return (
        <div style={{ position: "relative" }}>
          {isVideoLoading && (
            <div
              style={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                zIndex: 1,
              }}
            >
              <CircularProgress />
            </div>
          )}
          <video
            controls
            className={classes.media}
            src={`${dbConfig.url_storage}/${item?.url}`}
            onCanPlay={() => setIsVideoLoading(false)}
            onWaiting={() => setIsVideoLoading(true)}
            onError={() => setIsVideoLoading(false)}
          >
            Your browser does not support the video tag.
          </video>
        </div>
      );
    } else if (item?.format === "audio") {
      return (
        <audio controls className={classes.media}>
          <source
            src={`${dbConfig.url_storage}/${item?.url}`}
            type="audio/mp3"
          />
          Your browser does not support the audio tag.
        </audio>
      );
    } else {
      return <Typography variant="body2">NO MEDIA FOUND</Typography>;
    }
  };

  // Handle remarks change and prevent leading space
  // const handleRemarksChange = (e) => {
  //   const value = e.target.value;
  //   // Prevent space as the first character
  //   if (value.length === 1 && value === " ") {
  //     return; // Ignore if the first character is a space
  //   }
  //   const trimmedValue = value.replace(/^\s+/, ""); // Remove leading spaces
  //   setTempRemarks(trimmedValue);
  //   setError(""); // Clear error when user types
  // };

  // const handleCommentSubmit = () => {
  //   const trimmedRemarks = tempRemarks.trim();
  //   if (!trimmedRemarks) {
  //     setError("Remarks cannot be empty or just spaces");
  //     toastMessage({ message: "Please enter valid remarks" });
  //     return; // Prevent submission
  //   }

  //   setRemarks(trimmedRemarks); // Update parent's remarks for this step
  //   uOrA(item, trimmedRemarks); // Pass the updated remark to the parent function
  //   setError(""); // Clear error on successful submission
  // };

  return (
    <Card className={classes.cardContainer}>
      {/* Left Section: Media */}
      <Box className={classes.leftSection}>
        <Typography className={classes.title}>
          Step {index + 1}: {item.title}
        </Typography>
        <Box className={classes.mediaContainer}>{renderMedia()}</Box>
        {item?.format === "image" && (
          <Box className={classes.commentButton}>
            <Tooltip title="Enlarge">
              <IconButton
                onClick={() => {
                  setEnlarge(true);
                  setEValue(item.url);
                }}
                variant="outlined"
                color="primary"
                size="small"
                sx={{ marginTop: "-45px" }}
              >
                <ImEnlarge />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      </Box>

      {/* Right Section: Remarks, Attachments, QR Codes */}
      <Box className={classes.rightSection}>
        {/* QR Codes Container */}
        <Box className={classes.container}>
          <Typography variant="h6">QR Codes</Typography>
          <Box className={classes.qrCodesContainer}>
            {item.qr_codes && item.qr_codes.length > 0 ? (
              <Typography>Required: {item.qr_codes.join(", ")}</Typography>
            ) : (
              <Typography>No QR Codes Required.</Typography>
            )}
            {item.qr_scanned && item.qr_scanned.length > 0 ? (
              <Typography>Scanned: {item.qr_scanned.join(", ")}</Typography>
            ) : (
              <Typography color="error">No QR Codes Scanned.</Typography>
            )}
          </Box>
        </Box>

        {/* Attachments Container */}
        <Box className={classes.container}>
          <Typography variant="h6">Attachments</Typography>
          <Box
            className={classes.attachmentsContainer}
            sx={{
              overflow: "auto", // Ensure scrolling is enabled
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {item.attachments && item.attachments.length > 0 ? (
              item.attachments.map((attachment, index) => (
                <Chip
                  key={index}
                  icon={
                    <AttachFileIcon fontSize="small" style={{ paddingTop: 2, paddingBottom: 2 }} />
                  }
                  label={`Attachment-${index + 1} - ${attachment?.type}`}
                  className={classes.chip}
                  style={{ justifyContent: "flex-start" }}
                  onClick={() =>
                    window.open(
                      `${dbConfig.url_storage}/${attachment?.url}`,
                      "_blank",
                    )
                  }
                />
              ))
            ) : (
              <Typography>No attachments available.</Typography>
            )}
          </Box>
        </Box>

        {/* Remarks Container */}
        <Box className={classes.container}>
          <Typography variant="h6">Remarks</Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={item.remarks || ""}
            // value={tempRemarks}
            // onChange={handleRemarksChange}
            placeholder="Enter your remarks here"
            className={classes.commentInput}
            // error={!!error} // Show error state if error exists
            // helperText={error} // Display error message
            disabled
          />
          {/* <Box className={classes.commentButton}>
            <Button disabled={!tempRemarks} onClick={handleCommentSubmit} color="primary" startIcon={<MdAddComment />}>
              {remarks ? "Update" : "Add"} Remark
            </Button>
          </Box> */}
        </Box>
      </Box>
    </Card>
  );
};

export default SelectedComponent;
