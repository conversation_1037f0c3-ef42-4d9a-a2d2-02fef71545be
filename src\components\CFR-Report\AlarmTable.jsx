import React from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { CircularProgress } from "@mui/material";
import { useStateContext } from "../../context/ContextProvider";

function AlarmTable(props) {
  const { data, load, mid } = props;
  const { currentMode } = useStateContext();

  let reportItems = data.filter((item) => item.mid === mid);

  reportItems.sort((a, b) => {
    let a_date = a.time.seconds;
    let b_date = b.time.seconds;
    if (a_date < b_date) {
      return 1;
    }
    if (a_date > b_date) {
      return -1;
    }
    return 0;
  });

  return reportItems.length > 0 ? (
    <TableContainer
      component={Paper}
      className="tableContainer"
      style={{
        border: currentMode === "Dark" ? "1px solid #fff" : "1px solid #000",
      }}
    >
      <Table className="insideTable">
        <TableHead>
          <TableRow>
            <TableCell align="left">Date/Time</TableCell>
            <TableCell align="left">Name</TableCell>
            <TableCell align="left">Description</TableCell>
            <TableCell align="left">Alarm Value</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {reportItems.map((item, idx) => (
            <TableRow
              key={`${item.time.toDate().toLocaleDateString()} - ${item.time.toDate().toLocaleTimeString()}`}
            >
              <TableCell
                style={
                  idx === reportItems.length - 1 && currentMode === "Dark"
                    ? { borderBottom: "1px solid white" }
                    : {}
                }
                align="left"
              >{`${item.time.toDate().toLocaleDateString()} at ${item.time.toDate().toLocaleTimeString()}`}</TableCell>
              <TableCell
                style={
                  idx === reportItems.length - 1 && currentMode === "Dark"
                    ? { borderBottom: "1px solid white" }
                    : {}
                }
                align="left"
              >
                {item.name}
              </TableCell>
              <TableCell
                style={
                  idx === reportItems.length - 1 && currentMode === "Dark"
                    ? { borderBottom: "1px solid white" }
                    : {}
                }
                align="left"
              >
                {item.name} have sensed the value ({item.value}) which has
                passed the tolerance value ({item.tolerance})
              </TableCell>
              <TableCell
                style={
                  idx === reportItems.length - 1 && currentMode === "Dark"
                    ? { borderBottom: "1px solid white" }
                    : {}
                }
                align="left"
              >
                Alarm Triggered
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  ) : (
    <div className="h-32 w-full flex justify-center items-center">
      {load ? (
        <CircularProgress />
      ) : (
        <div style={currentMode === "Dark" ? { color: "white" } : {}}>
          No Data
        </div>
      )}
    </div>
  );
}

export default AlarmTable;
