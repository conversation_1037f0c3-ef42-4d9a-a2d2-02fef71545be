import React, { useState } from "react";
import { db } from "../../../firebase";
import { companies, companyId_constant } from "../../../constants/data";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { toastMessageSuccess } from "../../../tools/toast";
import { Button, InputLabel, TextField, Select, MenuItem } from "@mui/material";
import { useParams, useNavigate } from "react-router-dom";

import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../../components/buttons/Buttons";
import { useStateContext } from "../../../context/ContextProvider";
import { themeColors } from "../../../infrastructure/theme";
import { dbConfig } from "../../../infrastructure/db/db-config";
import axios from "axios";
// import { useEditMachineCfr } from "../../../hooks/cfr/machineCfrProvider";
import { useAuth } from "../../../hooks/AuthProvider";
import { useEditMachineCfr } from "../../../hooks/cfr/machineCfrProvider";
import { useAuditEditCountSetter } from "../../../services3/audits/AuditContext";

const EditSeries = ({ handleClose, type, machineName, user, data }) => {
  const { currentUser } = useAuth();
  const { mid } = useParams();
  console.log("mid", mid);
  const [protocol_no, setProtocol_No] = useState(
    data.protocol_no ? data.protocol_no : "",
  );
  const [description, setDescription] = useState(
    data.description ? data.description : "",
  );
  const [status, setStatus] = useState(data.status ? data.status : "");
  const [index, setIndex] = useState(data.index ? data.index : 0);
  const [rev_no, setRevno] = useState("rev_no" in data ? data.rev_no : "");
  const { currentMode } = useStateContext();
  const menuItemTheme = {
    backgroundColor:
      currentMode === "Dark"
        ? themeColors.dark.primary
        : themeColors.light.primary,
  };

  const editauditseriescfr = useEditMachineCfr();
  const editsatseriescfr = useEditMachineCfr();
  const handleEditCount = useAuditEditCountSetter();
  const handleSubmit = (e) => {
    e.preventDefault();
    let date = new Date();
    const data2 = {
      activity: "audit series edited",
      dateTime: date,
      description: "an audit series is edited",
      machine: mid,
      module: "AUDIT",
      username: currentUser.username,
    };
    const data3 = {
      activity: "sat series edited",
      dateTime: date,

      description: "a sat series is edited",
      machine: mid,
      module: "SAT",
      username: currentUser.username,
    };
    const updatedObj = {
      protocol_no,
      description,
      status,
      index: parseInt(index),
      rev_no,
      mid: data?.mid,
    };
    axios
      .put(
        `${dbConfig.url}/${
          type === "AUDIT" ? "FAT"?.toLowerCase() : type?.toLowerCase()
        }lists/${data?._id}`,
        {
          ...updatedObj,
        },
      )
      .then(() => {
        if (type == "SAT") {
          editsatseriescfr(data3);
        } else {
          editauditseriescfr(data2);
        }
        handleEditCount();
        handleClose();
        toastMessageSuccess({ message: "Updated successfully" });
      });

    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(`${type.toLowerCase()}List`)
    //     .doc(data.id)
    //     .update(updatedObj)
    //     .then(() => {
    //         LoggingFunction(
    //             machineName,
    //             protocol_no,
    //             `${user?.fname} ${user?.lname}`,
    //             type,
    //             `${protocol_no} series is updated in ${type} module`
    //         )
    //         handleClose();
    //         toastMessageSuccess({ message: "Updated successfully" })
    //     })
  };

  return (
    // <DialogContent>
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Index</InputLabel>
      <TextField
        type="number"
        onChange={(e) => setIndex(e.target.value)}
        value={index}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Protocol No.</InputLabel>
      <TextField
        onChange={(e) => setProtocol_No(e.target.value)}
        value={protocol_no}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Description</InputLabel>
      <TextField
        onChange={(e) => setDescription(e.target.value)}
        value={description}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Status</InputLabel>
      <Select
        onChange={(e) => setStatus(e.target.value)}
        value={status}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      >
        <MenuItem value="completed" sx={menuItemTheme}>
          Completed
        </MenuItem>
        <MenuItem value="not completed" sx={menuItemTheme}>
          Not Completed
        </MenuItem>
        <MenuItem value="pending" sx={menuItemTheme}>
          Pending
        </MenuItem>
      </Select>
      <InputLabel style={{ marginBottom: "10px" }}>Revision Number</InputLabel>
      <TextField
        onChange={(e) => setRevno(e.target.value)}
        value={rev_no}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <div className="p-2 mt-2 flex justify-between">
        {/* <Button onClick={handleClose} variant='outlined'>Cancel</Button> */}
        <ButtonBasicCancel
          type="button"
          buttonTitle="Cancel"
          onClick={handleClose}
        />
        {/* <Button type="submit" variant='outlined'>Submit</Button> */}
        <ButtonBasic type="submit" buttonTitle="Submit" />
      </div>
    </form>
    // </DialogContent>
  );
};

export default EditSeries;
