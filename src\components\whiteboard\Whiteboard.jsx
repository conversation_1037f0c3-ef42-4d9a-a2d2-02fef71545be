import React, { useRef, useState, useEffect } from "react";
import {
  FaMousePointer,
  FaPencilAlt,
  FaEraser,
  FaUndo,
  FaRedo,
  FaTrash,
  FaFont,
  FaImage,
  FaDownload,
  FaShapes,
  FaPalette,
  FaTh,
  FaPlus,
  FaChevronLeft,
  FaChevronRight,
  FaAngleDoubleLeft,
  FaAngleDoubleRight,
  FaSquare,
  FaCircle,
  FaSlash,
  FaStar,
  FaHeart,
  FaDrawPolygon,
  FaBorderStyle,
  FaCopy,
} from "react-icons/fa";
import "./Whiteboard.css";

const tools = [
  { icon: <FaMousePointer />, name: "select" },
  { icon: <FaTh />, name: "grid" },
  { icon: <FaPalette />, name: "color" },
  { icon: <FaPencilAlt />, name: "pencil" },
  { icon: <FaEraser />, name: "eraser" },
  {
    icon: <FaShapes />,
    name: "shapes",
    subTools: [
      { icon: <FaSquare />, shape: "rectangle" },
      { icon: <FaBorderStyle />, shape: "square" },
      { icon: <FaCircle />, shape: "circle" },
      { icon: <FaSlash />, shape: "line" },
      { icon: <FaStar />, shape: "star" },
      { icon: <FaHeart />, shape: "heart" },
      { icon: <FaDrawPolygon />, shape: "triangle" },
      { icon: <FaDrawPolygon />, shape: "pentagon" },
      { icon: <FaDrawPolygon />, shape: "hexagon" },
    ],
  },
  { icon: <FaFont />, name: "text" },
  { icon: <FaImage />, name: "image" },
  { icon: <FaDownload />, name: "export" },
  { icon: <FaUndo />, name: "undo" },
  { icon: <FaRedo />, name: "redo" },
  { icon: <FaTrash />, name: "clear" },
];

const Whiteboard = () => {
  const canvasRef = useRef(null);
  const containerRef = useRef(null); // Add ref for container
  const [tool, setTool] = useState("pencil");
  const [color, setColor] = useState("#000");
  const [isDrawing, setIsDrawing] = useState(false);
  const [history, setHistory] = useState([]);
  const [redoHistory, setRedoHistory] = useState([]);
  const [showGrid, setShowGrid] = useState(false);
  const [textInput, setTextInput] = useState("");
  const [showTextInput, setShowTextInput] = useState(false);
  const [textPosition, setTextPosition] = useState({ x: 0, y: 0 });
  const [activeShape, setActiveShape] = useState(null);
  const [shapeStart, setShapeStart] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [fontSize, setFontSize] = useState(16);
  const [fontFamily, setFontFamily] = useState("Arial");
  const [fontStyle, setFontStyle] = useState("normal");
  const [textAlign, setTextAlign] = useState("left");
  const [resizeCorner, setResizeCorner] = useState(null);
  const [isResizing, setIsResizing] = useState(false);
  const [currentPath, setCurrentPath] = useState(null);
  const [screenshot, setScreenshot] = useState(null);
  const [eraserSize, setEraserSize] = useState(10); // Add eraser size state
  const [eraserPos, setEraserPos] = useState(null); // Track eraser position for box
  const [pencilSize, setPencilSize] = useState(2); // Add pencil size state

  const [whiteboards, setWhiteboards] = useState([
    { id: 1, name: "Whiteboard 1", data: null },
    { id: 2, name: "Whiteboard 2", data: null },
  ]);
  const [currentWhiteboard, setCurrentWhiteboard] = useState(1);
  const [visibleWhiteboards, setVisibleWhiteboards] = useState([0, 1]);

  const [objects, setObjects] = useState([]); // All drawn objects
  const [selectedObjectIndex, setSelectedObjectIndex] = useState(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);

  // Helper to revive image objects after undo/redo (reloads img property)
  function reviveObjects(objs) {
    return objs.map((obj) => {
      if (obj.type === "image" && obj.img && typeof obj.img === "string") {
        const img = new window.Image();
        img.src = obj.img;
        return { ...obj, img };
      }
      return obj;
    });
  }

  // Helper to set canvas size to match container
  const setCanvasSize = () => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (canvas && container) {
      // Only set size if changed to avoid flicker
      const rect = container.getBoundingClientRect();
      if (
        canvas.width !== Math.floor(rect.width) ||
        canvas.height !== Math.floor(rect.height)
      ) {
        canvas.width = Math.floor(rect.width);
        canvas.height = Math.floor(rect.height);
        redrawAll();
      }
    }
  };

  // On mount and on window resize, set canvas size
  useEffect(() => {
    setCanvasSize();
    window.addEventListener("resize", setCanvasSize);
    return () => window.removeEventListener("resize", setCanvasSize);
    // eslint-disable-next-line
  }, []);

  // When whiteboard content area changes size, update canvas size
  useEffect(() => {
    setTimeout(setCanvasSize, 100);
  }, [showGrid, objects, selectedObjectIndex, currentPath]);

  // Helper to get mouse position relative to canvas (handles any CSS offset/padding/scroll)
  function getCanvasPos(e) {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    // Use clientX/clientY for mouse, touches for touch events
    return {
      x: (e.touches ? e.touches[0].clientX : e.clientX) - rect.left,
      y: (e.touches ? e.touches[0].clientY : e.clientY) - rect.top,
    };
  }

  // Patch: When saving image objects, store img.src instead of the HTMLImageElement
  const saveState = (newObjects = objects) => {
    const serializable = newObjects.map((obj) => {
      if (obj.type === "image" && obj.img && obj.img.src) {
        return { ...obj, img: obj.img.src };
      }
      return obj;
    });
    setHistory((prev) => [...prev, JSON.stringify(serializable)]);
    setRedoHistory([]);
  };

  const redrawAll = () => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = "#fff";
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    objects.forEach((obj, idx) => {
      drawObject(ctx, obj, idx === selectedObjectIndex);
    });
    if (isDrawing && currentPath) {
      drawObject(ctx, currentPath, false);
    }
    if (showGrid) renderGrid();
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    ctx.fillStyle = "#fff";
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    setObjects([]); // clear objects
    setHistory([JSON.stringify([])]);
    setRedoHistory([]);
  }, []);

  useEffect(() => {
    const subscriberScreenshot = localStorage.getItem("subscriberScreenshot");
    if (subscriberScreenshot) {
      const img = new Image();
      img.onload = () => {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext("2d");
        ctx.drawImage(img, 50, 50, img.width / 2, img.height / 2);
        saveState();
      };
      img.src = subscriberScreenshot;
      localStorage.removeItem("subscriberScreenshot"); // Clear after use
    }

    // Import screenshot from localStorage if present
    const whiteboardScreenshot = localStorage.getItem("whiteboardScreenshot");
    if (whiteboardScreenshot) {
      const img = new window.Image();
      img.onload = () => {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext("2d");
        // Draw screenshot at (0,0) at its natural size
        ctx.drawImage(img, 0, 0, 500, 300);
        // Add as an image object to the whiteboard objects array, at natural size
        const newObj = {
          type: "image",
          img: img.src,
          x: 0,
          y: 0,
          w: 500,
          h: 300,
        };
        setObjects((prevObjs) => {
          const updated = [...prevObjs, newObj];
          setTimeout(() => {
            saveState(updated);
            setSelectedObjectIndex(updated.length - 1);
            setTool("select");
          }, 0);
          return updated;
        });
      };
      img.src = whiteboardScreenshot;
      localStorage.removeItem("whiteboardScreenshot"); // Clear after use
    }
  }, []);

  useEffect(() => {
    const img = localStorage.getItem("whiteboardScreenshot");
    if (img) {
      setScreenshot(img);
      // Optionally clear after use:
      // localStorage.removeItem('whiteboardScreenshot');
    }
  }, []);

  const drawResizeHandles = (ctx, x, y, w, h) => {
    const size = 8;
    [
      [x, y],
      [x + w, y],
      [x, y + h],
      [x + w, y + h],
    ].forEach(([hx, hy]) => {
      ctx.save();
      ctx.fillStyle = "#fff";
      ctx.strokeStyle = "#00f";
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.rect(hx - size / 2, hy - size / 2, size, size);
      ctx.fill();
      ctx.stroke();
      ctx.restore();
    });
  };

  // Patch: When drawing, if image object, create HTMLImageElement from src
  const drawObject = (ctx, obj, isSelected = false) => {
    ctx.save();
    ctx.strokeStyle = obj.color || "#000";
    ctx.lineWidth = obj.size || 2;
    switch (obj.type) {
      case "rectangle":
      case "square":
        ctx.beginPath();
        ctx.rect(obj.x, obj.y, obj.w, obj.h);
        ctx.stroke();
        break;
      case "circle":
        ctx.beginPath();
        ctx.arc(obj.x, obj.y, obj.r, 0, 2 * Math.PI);
        ctx.stroke();
        break;
      case "line":
        ctx.beginPath();
      case "triangle":
        drawPolygon(ctx, obj.x, obj.y, obj.r, 3);
        break;
      case "pentagon":
        drawPolygon(ctx, obj.x, obj.y, obj.r, 5);
        break;
      case "hexagon":
        drawPolygon(ctx, obj.x, obj.y, obj.r, 6);
        break;
      case "text":
        ctx.font = `${obj.fontStyle || "normal"} ${obj.fontSize || 16}px ${obj.fontFamily || "Arial"}`;
        ctx.fillStyle = obj.color || "#000";
        ctx.textAlign = obj.textAlign || "left";
        ctx.fillText(obj.text, obj.x, obj.y);
        break;
      case "image":
        if (obj.img) {
          let imgEl = obj.img;
          if (typeof imgEl === "string") {
            imgEl = new window.Image();
            imgEl.src = obj.img;
            obj.img = imgEl; // cache for next draw
          }
          ctx.drawImage(imgEl, obj.x, obj.y, obj.w, obj.h);
        }
        break;
      case "pen":
      case "eraser":
        ctx.beginPath();
        obj.points.forEach((pt, i) => {
          if (i === 0) ctx.moveTo(pt.x, pt.y);
          else ctx.lineTo(pt.x, pt.y);
        });
        ctx.strokeStyle = obj.type === "eraser" ? "#fff" : obj.color;
        ctx.lineWidth = obj.size || (obj.type === "eraser" ? 10 : 2);
        ctx.stroke();
        break;
    }
    if (isSelected) {
      ctx.strokeStyle = "#00f";
      ctx.setLineDash([4, 2]);
      // Rectangle, Square, Image: bounding box
      if (
        obj.type === "rectangle" ||
        obj.type === "square" ||
        obj.type === "image"
      ) {
        ctx.strokeRect(obj.x, obj.y, obj.w, obj.h);
        drawResizeHandles(ctx, obj.x, obj.y, obj.w, obj.h);
      }
      // Circle: bounding box
      if (obj.type === "circle") {
        ctx.beginPath();
        ctx.arc(obj.x, obj.y, obj.r, 0, 2 * Math.PI);
        ctx.stroke();
        drawResizeHandles(
          ctx,
          obj.x - obj.r,
          obj.y - obj.r,
          obj.r * 2,
          obj.r * 2,
        );
      }
      // Text: bounding box
      if (obj.type === "text") {
        const width = ctx.measureText(obj.text).width;
        ctx.strokeRect(obj.x, obj.y - obj.fontSize, width, obj.fontSize);
        drawResizeHandles(
          ctx,
          obj.x,
          obj.y - obj.fontSize,
          width,
          obj.fontSize,
        );
      }
      // Polygonal shapes and star: bounding box
      if (
        obj.type === "triangle" ||
        obj.type === "pentagon" ||
        obj.type === "hexagon" ||
        obj.type === "star"
      ) {
        const r = obj.r || 0;
        ctx.strokeRect(obj.x - r, obj.y - r, r * 2, r * 2);
        drawResizeHandles(ctx, obj.x - r, obj.y - r, r * 2, r * 2);
      }
      ctx.setLineDash([]);
    }
    ctx.restore();
  };

  const hitTest = (x, y) => {
    for (let i = objects.length - 1; i >= 0; i--) {
      const obj = objects[i];
      switch (obj.type) {
        case "rectangle":
        case "square":
        case "triangle":
        case "pentagon":
        case "hexagon":
        case "star": {
          // Use bounding rectangle for all these shapes
          let rx1, rx2, ry1, ry2;
          if (obj.type === "rectangle" || obj.type === "square") {
            rx1 = Math.min(obj.x, obj.x + obj.w);
            rx2 = Math.max(obj.x, obj.x + obj.w);
            ry1 = Math.min(obj.y, obj.y + obj.h);
            ry2 = Math.max(obj.y, obj.y + obj.h);
          } else {
            // For polygons and star, treat as bounding box centered at (x, y) with radius r
            const r = obj.r || 0;
            rx1 = obj.x - r;
            rx2 = obj.x + r;
            ry1 = obj.y - r;
            ry2 = obj.y + r;
          }
          if (x >= rx1 && x <= rx2 && y >= ry1 && y <= ry2) return i;
          break;
        }
        case "circle":
          if (Math.hypot(x - obj.x, y - obj.y) <= Math.abs(obj.r)) return i;
          break;
        case "line":
          const distToLine = (x1, y1, x2, y2, px, py) => {
            const A = px - x1;
            const B = py - y1;
            const C = x2 - x1;
            const D = y2 - y1;
            const dot = A * C + B * D;
            const len_sq = C * C + D * D;
            let param = -1;
            if (len_sq !== 0) param = dot / len_sq;
            let xx, yy;
            if (param < 0) {
              xx = x1;
              yy = y1;
            } else if (param > 1) {
              xx = x2;
              yy = y2;
            } else {
              xx = x1 + param * C;
              yy = y1 + param * D;
            }
            const dx = px - xx;
            const dy = py - yy;
            return Math.sqrt(dx * dx + dy * dy);
          };
          if (distToLine(obj.x1, obj.y1, obj.x2, obj.y2, x, y) < 8) return i;
          break;
        case "heart":
          const s = obj.size || 0;
          if (x >= obj.x && x <= obj.x + s && y >= obj.y && y <= obj.y + s)
            return i;
          break;
        case "text":
          const canvas = canvasRef.current;
          const ctx = canvas.getContext("2d");
          ctx.font = `${obj.fontStyle || "normal"} ${obj.fontSize || 16}px ${obj.fontFamily || "Arial"}`;
          const width = ctx.measureText(obj.text).width;
          if (
            x >= obj.x &&
            x <= obj.x + width &&
            y >= obj.y - obj.fontSize &&
            y <= obj.y
          )
            return i;
          break;
        case "image":
          if (
            x >= obj.x &&
            x <= obj.x + obj.w &&
            y >= obj.y &&
            y <= obj.y + obj.h
          )
            return i;
          break;
        case "pen":
        case "eraser": {
          // Check if mouse is near any point in the path
          for (let pt of obj.points) {
            if (Math.hypot(x - pt.x, y - pt.y) < 8) return i;
          }
          break;
        }
      }
    }
    return null;
  };

  const hitTestResizeHandle = (x, y, obj) => {
    const size = 8;
    let rect;
    if (obj.type === "circle") {
      rect = { x: obj.x - obj.r, y: obj.y - obj.r, w: obj.r * 2, h: obj.r * 2 };
    } else if (obj.type === "text") {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext("2d");
      ctx.font = `${obj.fontStyle || "normal"} ${obj.fontSize || 16}px ${obj.fontFamily || "Arial"}`;
      const width = ctx.measureText(obj.text).width;
      rect = { x: obj.x, y: obj.y - obj.fontSize, w: width, h: obj.fontSize };
    } else if (obj.type === "image") {
      rect = { x: obj.x, y: obj.y, w: obj.w, h: obj.h };
    } else {
      rect = { x: obj.x, y: obj.y, w: obj.w, h: obj.h };
    }
    const handles = [
      { corner: "tl", x: rect.x, y: rect.y },
      { corner: "tr", x: rect.x + rect.w, y: rect.y },
      { corner: "bl", x: rect.x, y: rect.y + rect.h },
      { corner: "br", x: rect.x + rect.w, y: rect.y + rect.h },
    ];
    for (let handle of handles) {
      if (
        x >= handle.x - size &&
        x <= handle.x + size &&
        y >= handle.y - size &&
        y <= handle.y + size
      ) {
        return handle.corner;
      }
    }
    return null;
  };

  const handleCanvasMouseDown = (e) => {
    const { x, y } = getCanvasPos(e.nativeEvent);

    // First check if we're clicking on an existing object
    const idx = hitTest(x, y);

    if (idx !== null) {
      // We clicked on an existing object, select it regardless of current tool
      setSelectedObjectIndex(idx);
      let obj = objects[idx];
      let resizeObj = obj;
      if (
        obj.type === "triangle" ||
        obj.type === "pentagon" ||
        obj.type === "hexagon" ||
        obj.type === "star"
      ) {
        const r = obj.r || 0;
        resizeObj = { ...obj, x: obj.x - r, y: obj.y - r, w: r * 2, h: r * 2 };
      }
      const corner = hitTestResizeHandle(x, y, resizeObj);
      if (corner) {
        setIsResizing(true);
        setResizeCorner(corner);
      } else {
        setIsDragging(true);
        setDragOffset({
          x: x - (obj.x || 0),
          y: y - (obj.y || 0),
        });
      }
    } else {
      // We clicked on empty space
      setSelectedObjectIndex(null);

      // If tool is eraser, handle eraser
      if (tool === "eraser") {
        eraseAt(x, y);
        setIsDrawing(true);
        setEraserPos({ x, y });
      } else if (tool === "shapes") {
        // If tool is shapes, start drawing a shape
        startDrawing(e);
      } else if (tool === "pencil") {
        // If tool is pencil, start drawing
        startDrawing(e);
      } else if (tool === "text") {
        // Handle text tool
        startDrawing(e);
      }
    }
  };

  const handleCanvasMouseMove = (e) => {
    const { x, y } = getCanvasPos(e.nativeEvent);
    if (tool === "eraser") {
      setEraserPos({ x, y });
      if (isDrawing) {
        eraseAt(x, y);
      }
    } else {
      setEraserPos(null);
    }

    // Handle dragging and resizing regardless of current tool
    if (selectedObjectIndex !== null) {
      if (isResizing && resizeCorner) {
        setObjects((prevObjs) => {
          const objs = [...prevObjs];
          const obj = { ...objs[selectedObjectIndex] };
          // Rectangle, Square, Image
          if (
            obj.type === "rectangle" ||
            obj.type === "square" ||
            obj.type === "image"
          ) {
            let x0 = obj.x,
              y0 = obj.y,
              w0 = obj.w,
              h0 = obj.h;
            if (resizeCorner === "br") {
              obj.w = x - x0;
              obj.h = y - y0;
            } else if (resizeCorner === "tr") {
              obj.h = h0 + (y0 - y);
              obj.y = y;
              obj.w = x - x0;
            } else if (resizeCorner === "bl") {
              obj.w = w0 + (x0 - x);
              obj.x = x;
              obj.h = y - y0;
            } else if (resizeCorner === "tl") {
              obj.w = w0 + (x0 - x);
              obj.x = x;
              obj.h = h0 + (y0 - y);
              obj.y = y;
            }
          }
          // Handle other shape types...
          objs[selectedObjectIndex] = obj;
          return objs;
        });
        redrawAll();
      } else if (isDragging) {
        setObjects((prevObjs) => {
          const objs = [...prevObjs];
          const obj = { ...objs[selectedObjectIndex] };
          if (
            obj.type === "rectangle" ||
            obj.type === "square" ||
            obj.type === "circle" ||
            obj.type === "image" ||
            obj.type === "star" ||
            obj.type === "triangle" ||
            obj.type === "pentagon" ||
            obj.type === "hexagon" ||
            obj.type === "heart"
          ) {
            obj.x = x - dragOffset.x;
            obj.y = y - dragOffset.y;
          }
          // Handle other object types...
          objs[selectedObjectIndex] = obj;
          return objs;
        });
        redrawAll();
      }
    } else {
      if (tool === "shapes") {
        drawShape(e);
      } else if (tool === "pencil") {
        draw(e);
      }
    }
  };

  const handleCanvasMouseLeave = (e) => {
    setEraserPos(null);
    setIsDrawing(false);
    setIsDragging(false);
    setIsResizing(false);
    handleCanvasMouseUp(e);
  };

  const handleCanvasMouseUp = (e) => {
    if (tool === "eraser") {
      setIsDrawing(false);
      setTimeout(() => saveState(objects), 0);
      return;
    }

    // Save state if we were dragging or resizing
    if (isDragging || isResizing) {
      setTimeout(() => saveState(objects), 0);
    }

    // Reset dragging and resizing states
    setIsDragging(false);
    setIsResizing(false);
    setResizeCorner(null);

    // Handle shape or drawing completion
    if (tool === "shapes" && shapeStart) {
      stopDrawing(e);
    } else if (isDrawing) {
      stopDrawing(e);
    }
  };

  // Erase objects that intersect with eraser box
  function eraseAt(x, y) {
    setObjects((prevObjs) => {
      const box = {
        x: x - eraserSize / 2,
        y: y - eraserSize / 2,
        w: eraserSize,
        h: eraserSize,
      };
      let changed = false;
      const newObjs = prevObjs
        .map((obj) => {
          if (obj.type === "pen" || obj.type === "eraser") {
            // Split path into segments outside the eraser box
            let segments = [];
            let current = [];
            for (let pt of obj.points) {
              const inside =
                pt.x >= box.x &&
                pt.x <= box.x + box.w &&
                pt.y >= box.y &&
                pt.y <= box.y + box.h;
              if (!inside) {
                current.push(pt);
              } else {
                if (current.length > 1) segments.push([...current]);
                current = [];
                changed = true;
              }
            }
            if (current.length > 1) segments.push([...current]);
            // Each segment becomes a new path object
            if (segments.length === 0) {
              changed = true;
              return null;
            }
            if (segments.length === 1) {
              return { ...obj, points: segments[0] };
            }
            changed = true;
            return segments.map((seg) => ({
              ...obj,
              points: seg,
            }));
          }
          // For shapes, text, images: remove if their bounding box intersects eraser box
          const bbox = (() => {
            switch (obj.type) {
              case "rectangle":
              case "square":
              case "image":
                return { x: obj.x, y: obj.y, w: obj.w, h: obj.h };
              case "circle":
                return {
                  x: obj.x - obj.r,
                  y: obj.y - obj.r,
                  w: obj.r * 2,
                  h: obj.r * 2,
                };
              case "text": {
                const canvas = canvasRef.current;
                if (!canvas) return null;
                const ctx = canvas.getContext("2d");
                ctx.font = `${obj.fontStyle || "normal"} ${obj.fontSize || 16}px ${obj.fontFamily || "Arial"}`;
                const width = ctx.measureText(obj.text).width;
                return {
                  x: obj.x,
                  y: obj.y - obj.fontSize,
                  w: width,
                  h: obj.fontSize,
                };
              }
              case "triangle":
              case "pentagon":
              case "hexagon":
              case "star": {
                const r = obj.r || 0;
                return { x: obj.x - r, y: obj.y - r, w: r * 2, h: r * 2 };
              }
              case "heart":
                return { x: obj.x, y: obj.y, w: obj.size, h: obj.size };
              case "line":
                return {
                  x: Math.min(obj.x1, obj.x2),
                  y: Math.min(obj.y1, obj.y2),
                  w: Math.abs(obj.x2 - obj.x1),
                  h: Math.abs(obj.y2 - obj.y1),
                };
              default:
                return null;
            }
          })();
          if (
            bbox &&
            box.x < bbox.x + bbox.w &&
            box.x + box.w > bbox.x &&
            box.y < bbox.y + bbox.h &&
            box.y + box.h > bbox.y
          ) {
            changed = true;
            return null;
          }
          return obj;
        })
        .flat() // flatten in case of multiple pen segments
        .filter(Boolean);
      return changed ? newObjs : prevObjs;
    });
  }

  // Show text input at click position when text tool is active
  const startDrawing = (e) => {
    const { x, y } = getCanvasPos(e.nativeEvent);
    if (tool === "pencil") {
      setIsDrawing(true);
      setCurrentPath({
        type: "pen",
        color: color,
        size: pencilSize,
        points: [{ x, y }],
      });
    } else if (tool === "shapes") {
      setShapeStart({ x, y });
    } else if (tool === "text") {
      setTimeout(() => {
        setTextPosition({ x, y });
        setShowTextInput(true);
        setTextInput("");
      }, 0);
    }
  };

  const draw = (e) => {
    if (!isDrawing || tool !== "pencil") return;
    const { x, y } = getCanvasPos(e.nativeEvent);
    setCurrentPath((prev) => {
      if (!prev) return prev;
      if (!prev.points || prev.points.length === 0) {
        return {
          ...prev,
          points: [{ x, y }],
        };
      }
      const last = prev.points[prev.points.length - 1];
      if (last && last.x === x && last.y === y) return prev;

      // Draw only the new segment
      const canvas = canvasRef.current;
      const ctx = canvas.getContext("2d");
      ctx.save();
      ctx.strokeStyle = prev.color;
      ctx.lineWidth = prev.size;
      ctx.beginPath();
      ctx.moveTo(last.x, last.y);
      ctx.lineTo(x, y);
      ctx.stroke();
      ctx.restore();

      return {
        ...prev,
        points: [...prev.points, { x, y }],
      };
    });
  };

  const drawShape = (e) => {
    if (tool !== "shapes" || !shapeStart) return;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    const { x, y } = getCanvasPos(e.nativeEvent);

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = "#fff";
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    objects.forEach((obj, idx) => {
      drawObject(ctx, obj, idx === selectedObjectIndex);
    });
    drawCurrentShape(ctx, shapeStart.x, shapeStart.y, x, y);

    if (isDrawing && currentPath) {
      drawObject(ctx, currentPath, false);
    }

    if (showGrid) renderGrid();
  };

  const drawCurrentShape = (ctx, startX, startY, endX, endY) => {
    ctx.fillStyle = color;
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;

    switch (activeShape) {
      case "rectangle":
        ctx.beginPath();
        ctx.rect(startX, startY, endX - startX, endY - startY);
        ctx.stroke();
        break;
      case "square":
        const size = Math.min(Math.abs(endX - startX), Math.abs(endY - startY));
        ctx.beginPath();
        ctx.rect(
          startX,
          startY,
          size * Math.sign(endX - startX),
          size * Math.sign(endY - startY),
        );
        ctx.stroke();
        break;
      case "circle":
        const radius = Math.sqrt(
          Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2),
        );
        ctx.beginPath();
        ctx.arc(startX, startY, radius, 0, 2 * Math.PI);
        ctx.stroke();
        break;
      case "line":
        ctx.beginPath();
        ctx.moveTo(startX, startY);
        ctx.lineTo(endX, endY);
        ctx.stroke();
        break;
      case "star":
        drawStar(ctx, startX, startY, Math.abs(endX - startX), 5);
        break;
      case "heart":
        drawHeart(ctx, startX, startY, Math.abs(endX - startX));
        break;
      case "triangle":
        drawPolygon(ctx, startX, startY, Math.abs(endX - startX), 3);
        break;
      case "pentagon":
        drawPolygon(ctx, startX, startY, Math.abs(endX - startX), 5);
        break;
      case "hexagon":
        drawPolygon(ctx, startX, startY, Math.abs(endX - startX), 6);
        break;
      default:
        break;
    }
  };

  const drawStar = (ctx, cx, cy, size, spikes) => {
    let rot = (Math.PI / 2) * 3;
    let x = cx;
    let y = cy;
    const step = Math.PI / spikes;

    ctx.beginPath();
    ctx.moveTo(cx, cy - size);

    for (let i = 0; i < spikes; i++) {
      x = cx + Math.cos(rot) * size;
      y = cy + Math.sin(rot) * size;
      ctx.lineTo(x, y);
      rot += step;

      x = cx + Math.cos(rot) * (size * 0.4);
      y = cy + Math.sin(rot) * (size * 0.4);
      ctx.lineTo(x, y);
      rot += step;
    }
    ctx.lineTo(cx, cy - size);
    ctx.closePath();
    ctx.stroke();
  };

  const drawHeart = (ctx, x, y, size) => {
    const width = size;
    const height = size;

    ctx.beginPath();
    ctx.moveTo(x, y + height / 4);
    ctx.quadraticCurveTo(x, y, x + width / 4, y);
    ctx.quadraticCurveTo(x + width / 2, y, x + width / 2, y + height / 4);
    ctx.quadraticCurveTo(x + width / 2, y, x + (width * 3) / 4, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + height / 4);
    ctx.quadraticCurveTo(x + width, y + height / 2, x + width / 2, y + height);
    ctx.quadraticCurveTo(x, y + height / 2, x, y + height / 4);
    ctx.stroke();
  };

  const drawPolygon = (ctx, cx, cy, radius, sides) => {
    ctx.beginPath();
    ctx.moveTo(cx + radius * Math.cos(0), cy + radius * Math.sin(0));

    for (let i = 1; i <= sides; i++) {
      const angle = (i * 2 * Math.PI) / sides;
      ctx.lineTo(cx + radius * Math.cos(angle), cy + radius * Math.sin(angle));
    }

    ctx.closePath();
    ctx.stroke();
  };

  const stopDrawing = (e) => {
    if (tool === "shapes" && shapeStart) {
      const { x, y } = getCanvasPos(e.nativeEvent);
      let newObj = null;
      switch (activeShape) {
        case "rectangle":
          newObj = {
            type: "rectangle",
            x: shapeStart.x,
            y: shapeStart.y,
            w: x - shapeStart.x,
            h: y - shapeStart.y,
            color,
          };
          break;
        case "square":
          const size = Math.min(
            Math.abs(x - shapeStart.x),
            Math.abs(y - shapeStart.y),
          );
          newObj = {
            type: "square",
            x: shapeStart.x,
            y: shapeStart.y,
            w: size * Math.sign(x - shapeStart.x),
            h: size * Math.sign(y - shapeStart.y),
            color,
          };
          break;
        case "circle":
          const r = Math.sqrt(
            Math.pow(x - shapeStart.x, 2) + Math.pow(y - shapeStart.y, 2),
          );
          newObj = {
            type: "circle",
            x: shapeStart.x,
            y: shapeStart.y,
            r,
            color,
          };
          break;
        case "line":
          newObj = {
            type: "line",
            x1: shapeStart.x,
            y1: shapeStart.y,
            x2: x,
            y2: y,
            color,
          };
          break;
        case "star":
          newObj = {
            type: "star",
            x: shapeStart.x,
            y: shapeStart.y,
            r: Math.abs(x - shapeStart.x),
            color,
          };
          break;
        case "heart":
          newObj = {
            type: "heart",
            x: shapeStart.x,
            y: shapeStart.y,
            size: Math.abs(x - shapeStart.x),
            color,
          };
          break;
        case "triangle":
          newObj = {
            type: "triangle",
            x: shapeStart.x,
            y: shapeStart.y,
            r: Math.abs(x - shapeStart.x),
            color,
          };
          break;
        case "pentagon":
          newObj = {
            type: "pentagon",
            x: shapeStart.x,
            y: shapeStart.y,
            r: Math.abs(x - shapeStart.x),
            color,
          };
          break;
        case "hexagon":
          newObj = {
            type: "hexagon",
            x: shapeStart.x,
            y: shapeStart.y,
            r: Math.abs(x - shapeStart.x),
            color,
          };
          break;
      }
      if (newObj) {
        setObjects((prevObjs) => {
          const updated = [...prevObjs, newObj];
          setTimeout(() => {
            saveState(updated);
            setSelectedObjectIndex(updated.length - 1);
            // Don't switch to select tool here
          }, 0);
          return updated;
        });
      }
      setShapeStart(null);
    } else if (isDrawing && currentPath) {
      setObjects((prevObjs) => {
        const updated = [...prevObjs, currentPath];
        setTimeout(() => {
          saveState(updated);
        }, 0);
        return updated;
      });
      setIsDrawing(false);
      setCurrentPath(null);
    }
  };

  // Add text object to whiteboard
  const addText = () => {
    if (!textInput.trim()) {
      setShowTextInput(false);
      setTextInput("");
      return;
    }
    const newObj = {
      type: "text",
      x: textPosition.x,
      y: textPosition.y,
      text: textInput,
      color,
      fontSize,
      fontFamily,
      fontStyle,
      textAlign,
    };
    setObjects((prevObjs) => {
      const updated = [...prevObjs, newObj];
      setTimeout(() => {
        saveState(updated);
        setSelectedObjectIndex(updated.length - 1); // Select the new object
        setTool("select"); // Switch to select tool
      }, 0); // Save after state update
      return updated;
    });
    setShowTextInput(false);
    setTextInput("");
  };

  // Add this function to validate image dimensions
  const validateImageDimensions = (img) => {
    const requiredWidth = 600;
    const requiredHeight = 400;

    return new Promise((resolve) => {
      if (img.width === requiredWidth && img.height === requiredHeight) {
        resolve(true);
      } else {
        resolve(false);
      }
    });
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const img = new Image();
      img.onload = () => {
        // Calculate dimensions while preserving aspect ratio
        const maxWidth = 600;
        const maxHeight = 400;
        let newWidth, newHeight;

        // Calculate dimensions based on orientation
        if (img.width / img.height > maxWidth / maxHeight) {
          // Landscape orientation (wider than 3:2)
          newWidth = maxWidth;
          newHeight = (img.height * maxWidth) / img.width;
        } else {
          // Portrait orientation (taller than 3:2)
          newHeight = maxHeight;
          newWidth = (img.width * maxHeight) / img.height;
        }

        // Center the image within the 600x400 area
        const x = 50 + (maxWidth - newWidth) / 2;
        const y = 50 + (maxHeight - newHeight) / 2;

        // Create image object with calculated dimensions
        const imgObj = {
          type: "image",
          x: x,
          y: y,
          w: newWidth,
          h: newHeight,
          img: img.src,
          dragging: false,
        };

        setObjects((prevObjs) => {
          const updated = [...prevObjs, imgObj];
          setTimeout(() => {
            saveState(updated);
            setSelectedObjectIndex(updated.length - 1);
            setSelectedImage(img.src);
            setTool("select");
          }, 0);
          return updated;
        });
      };
      img.src = event.target.result;
    };
    reader.readAsDataURL(file);
  };

  const exportCanvas = () => {
    const canvas = canvasRef.current;
    const dataUrl = canvas.toDataURL("image/png");
    const link = document.createElement("a");
    link.download = "whiteboard.png";
    link.href = dataUrl;
    link.click();
  };

  // Patch: When undo/redo, revive image objects
  const handleToolClick = (name) => {
    setTool(name);

    switch (name) {
      case "clear":
        setShowConfirmDialog(true);
        break;
      case "undo":
        if (history.length > 1) {
          setHistory((prev) => {
            const newHistory = [...prev];
            const last = newHistory.pop();
            setRedoHistory((rh) => [...rh, last]);
            const prevState = reviveObjects(
              JSON.parse(newHistory[newHistory.length - 1]),
            );
            setObjects(prevState);
            return newHistory;
          });
        }
        break;
      case "redo":
        if (redoHistory.length > 0) {
          setRedoHistory((prev) => {
            const newRedo = [...prev];
            const last = newRedo.pop();
            setHistory((h) => [...h, last]);
            const nextState = reviveObjects(JSON.parse(last));
            setObjects(nextState);
            return newRedo;
          });
        }
        break;
      case "grid":
        setShowGrid(!showGrid);
        break;
      case "shapes":
        setActiveShape("rectangle");
        break;
      case "image":
        document.getElementById("image-upload").click();
        break;
      case "export":
        exportCanvas();
        break;
      default:
        break;
    }
  };

  const handleClearConfirm = () => {
    setObjects([]);
    setShowConfirmDialog(false);
    setSelectedImage(null); // Reset selectedImage when clearing
    setTimeout(() => saveState([]), 0);
  };

  useEffect(() => {
    redrawAll();
    // eslint-disable-next-line
  }, [objects, showGrid, selectedObjectIndex, currentPath]);

  // Patch: Draw eraser box overlay (solid white fill, black border)
  useEffect(() => {
    if (tool !== "eraser" || !eraserPos) return;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    redrawAll();
    ctx.save();
    ctx.fillStyle = "#fff";
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 1.5;
    ctx.setLineDash([]);
    ctx.beginPath();
    ctx.rect(
      eraserPos.x - eraserSize / 2,
      eraserPos.y - eraserSize / 2,
      eraserSize,
      eraserSize,
    );
    ctx.fill();
    ctx.stroke();
    ctx.restore();
    // eslint-disable-next-line
  }, [
    eraserPos,
    eraserSize,
    tool,
    objects,
    currentPath,
    showGrid,
    selectedObjectIndex,
  ]);

  // Update text options for selected text object
  useEffect(() => {
    if (
      selectedObjectIndex !== null &&
      objects[selectedObjectIndex] &&
      objects[selectedObjectIndex].type === "text"
    ) {
      setObjects((prevObjs) => {
        const objs = [...prevObjs];
        const obj = { ...objs[selectedObjectIndex] };
        obj.fontSize = fontSize;
        obj.fontFamily = fontFamily;
        obj.fontStyle = fontStyle;
        obj.textAlign = textAlign;
        obj.color = color;
        objs[selectedObjectIndex] = obj;
        return objs;
      });
    }
    // eslint-disable-next-line
  }, [fontSize, fontFamily, fontStyle, textAlign, color]);

  const renderGrid = () => {
    if (!showGrid) return null;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    ctx.strokeStyle = "#ddd";
    ctx.lineWidth = 1;

    for (let x = 0; x <= canvas.width; x += 20) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvas.height);
      ctx.stroke();
    }

    for (let y = 0; y <= canvas.height; y += 20) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvas.width, y);
      ctx.stroke();
    }
  };

  useEffect(() => {
    if (showGrid) {
      renderGrid();
    }
  }, [showGrid]);

  const renderToolOptions = () => {
    // Show text options if a text object is selected, regardless of tool
    const selectedTextObj =
      selectedObjectIndex !== null &&
      objects[selectedObjectIndex] &&
      objects[selectedObjectIndex].type === "text"
        ? objects[selectedObjectIndex]
        : null;

    if (selectedTextObj) {
      return (
        <div className="tool-panel">
          <h4>Text Options</h4>
          <div className="tool-content">
            <div className="text-options">
              <div className="option-row">
                <label>Font Size:</label>
                <input
                  type="number"
                  min="8"
                  max="72"
                  value={selectedTextObj.fontSize}
                  onChange={(e) => {
                    const val = parseInt(e.target.value);
                    setFontSize(val);
                    setObjects((prevObjs) => {
                      const objs = [...prevObjs];
                      objs[selectedObjectIndex] = {
                        ...objs[selectedObjectIndex],
                        fontSize: val,
                      };
                      return objs;
                    });
                  }}
                />
              </div>
              <div className="option-row">
                <label>Font Family:</label>
                <select
                  value={selectedTextObj.fontFamily}
                  onChange={(e) => {
                    const val = e.target.value;
                    setFontFamily(val);
                    setObjects((prevObjs) => {
                      const objs = [...prevObjs];
                      objs[selectedObjectIndex] = {
                        ...objs[selectedObjectIndex],
                        fontFamily: val,
                      };
                      return objs;
                    });
                  }}
                >
                  <option value="Arial">Arial</option>
                  <option value="Times New Roman">Times New Roman</option>
                  <option value="Courier New">Courier New</option>
                  <option value="Georgia">Georgia</option>
                  <option value="Verdana">Verdana</option>
                </select>
              </div>
              <div className="option-row">
                <label>Style:</label>
                <select
                  value={selectedTextObj.fontStyle}
                  onChange={(e) => {
                    const val = e.target.value;
                    setFontStyle(val);
                    setObjects((prevObjs) => {
                      const objs = [...prevObjs];
                      objs[selectedObjectIndex] = {
                        ...objs[selectedObjectIndex],
                        fontStyle: val,
                      };
                      return objs;
                    });
                  }}
                >
                  <option value="normal">Normal</option>
                  <option value="bold">Bold</option>
                  <option value="italic">Italic</option>
                  <option value="bold italic">Bold Italic</option>
                </select>
              </div>
              <div className="option-row">
                <label>Alignment:</label>
                <select
                  value={selectedTextObj.textAlign}
                  onChange={(e) => {
                    const val = e.target.value;
                    setTextAlign(val);
                    setObjects((prevObjs) => {
                      const objs = [...prevObjs];
                      objs[selectedObjectIndex] = {
                        ...objs[selectedObjectIndex],
                        textAlign: val,
                      };
                      return objs;
                    });
                  }}
                >
                  <option value="left">Left</option>
                  <option value="center">Center</option>
                  <option value="right">Right</option>
                </select>
              </div>
              <div className="option-row">
                <label>Color:</label>
                <input
                  type="color"
                  value={selectedTextObj.color}
                  onChange={(e) => {
                    const val = e.target.value;
                    setColor(val);
                    setObjects((prevObjs) => {
                      const objs = [...prevObjs];
                      objs[selectedObjectIndex] = {
                        ...objs[selectedObjectIndex],
                        color: val,
                      };
                      return objs;
                    });
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      );
    }

    switch (tool) {
      case "color":
        return (
          <div className="tool-panel">
            <h4>Color Picker</h4>
            <div className="tool-content">
              <input
                type="color"
                value={color}
                onChange={(e) => setColor(e.target.value)}
                className="color-picker"
              />
              <div className="color-preview">
                <span>Selected Color:</span>
                <div style={{ background: color }}></div>
              </div>
              <div className="preset-colors">
                {["#000000", "#ff0000", "#00ff00", "#0000ff", "#ffff00"].map(
                  (presetColor) => (
                    <div
                      key={presetColor}
                      style={{ background: presetColor }}
                      onClick={() => setColor(presetColor)}
                    ></div>
                  ),
                )}
              </div>
            </div>
          </div>
        );
      case "shapes":
        return (
          <div className="tool-panel">
            <h4>Shapes</h4>
            <div className="tool-content">
              <div className="shape-options-grid">
                {tools
                  .find((t) => t.name === "shapes")
                  .subTools.map(({ icon, shape }) => (
                    <button
                      key={shape}
                      className={`shape-option-btn ${activeShape === shape ? "active" : ""}`}
                      onClick={() => setActiveShape(shape)}
                      title={shape.charAt(0).toUpperCase() + shape.slice(1)}
                    >
                      {icon}
                    </button>
                  ))}
              </div>
              <div className="shape-properties">
                <div className="property-row">
                  <span>Color:</span>
                  <input
                    type="color"
                    value={color}
                    onChange={(e) => setColor(e.target.value)}
                    className="shape-color-picker"
                  />
                </div>
              </div>
            </div>
          </div>
        );
      case "pencil":
      case "eraser":
        return (
          <div className="tool-panel">
            <h4>{tool === "pencil" ? "Pencil Options" : "Eraser Options"}</h4>
            <div className="tool-content">
              {tool === "eraser" && (
                <div className="size-slider">
                  <label>
                    Size: <span style={{ fontWeight: 600 }}>{eraserSize}</span>
                  </label>
                  <input
                    type="range"
                    min="10"
                    max="150"
                    value={eraserSize}
                    onChange={(e) => setEraserSize(Number(e.target.value))}
                  />
                </div>
              )}
              {tool === "pencil" && (
                <div className="size-slider">
                  <label>
                    Size: <span style={{ fontWeight: 600 }}>{pencilSize}</span>
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="20"
                    value={pencilSize}
                    onChange={(e) => setPencilSize(Number(e.target.value))}
                  />
                </div>
              )}
              {tool === "pencil" && (
                <div className="color-picker">
                  <label>Color</label>
                  <input
                    type="color"
                    value={color}
                    onChange={(e) => setColor(e.target.value)}
                  />
                </div>
              )}
            </div>
          </div>
        );
      case "text":
        return (
          <div className="tool-panel">
            <h4>Text Options</h4>
            <div className="tool-content">
              <div className="text-options">
                <div className="option-row">
                  <label>Font Size:</label>
                  <input
                    type="number"
                    min="8"
                    max="72"
                    value={fontSize}
                    onChange={(e) => setFontSize(parseInt(e.target.value))}
                  />
                </div>
                <div className="option-row">
                  <label>Font Family:</label>
                  <select
                    value={fontFamily}
                    onChange={(e) => setFontFamily(e.target.value)}
                  >
                    <option value="Arial">Arial</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Courier New">Courier New</option>
                    <option value="Georgia">Georgia</option>
                    <option value="Verdana">Verdana</option>
                  </select>
                </div>
                <div className="option-row">
                  <label>Style:</label>
                  <select
                    value={fontStyle}
                    onChange={(e) => setFontStyle(e.target.value)}
                  >
                    <option value="normal">Normal</option>
                    <option value="bold">Bold</option>
                    <option value="italic">Italic</option>
                    <option value="bold italic">Bold Italic</option>
                  </select>
                </div>
                <div className="option-row">
                  <label>Alignment:</label>
                  <select
                    value={textAlign}
                    onChange={(e) => setTextAlign(e.target.value)}
                  >
                    <option value="left">Left</option>
                    <option value="center">Center</option>
                    <option value="right">Right</option>
                  </select>
                </div>
                <div className="option-row">
                  <label>Color:</label>
                  <input
                    type="color"
                    value={color}
                    onChange={(e) => setColor(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="tool-panel">
            <h4>{tool.charAt(0).toUpperCase() + tool.slice(1)}</h4>
            <div className="tool-content">
              <p>Select a tool to see options</p>
            </div>
          </div>
        );
    }
  };

  // Patch: When switching whiteboard, always save current objects to its data, then load the new one
  const switchWhiteboard = (id) => {
    setWhiteboards((prevWhiteboards) => {
      // Save current objects to the current whiteboard's data
      const updatedWhiteboards = prevWhiteboards.map((wb) =>
        wb.id === currentWhiteboard
          ? {
              ...wb,
              data: JSON.stringify(
                objects.map((obj) => {
                  // Always save image as src string
                  if (obj.type === "image" && obj.img && obj.img.src) {
                    return { ...obj, img: obj.img.src };
                  }
                  return obj;
                }),
              ),
            }
          : wb,
      );
      // Find the selected whiteboard after update
      const selectedBoard = updatedWhiteboards.find((wb) => wb.id === id);
      // Load objects from selected whiteboard's data (if any)
      if (selectedBoard && selectedBoard.data) {
        // Always revive image objects
        const revived = reviveObjects(JSON.parse(selectedBoard.data));
        setObjects(revived);
        setHistory([selectedBoard.data]);
        setRedoHistory([]);
      } else {
        setObjects([]);
        setHistory([JSON.stringify([])]);
        setRedoHistory([]);
      }
      setCurrentWhiteboard(id);
      return updatedWhiteboards;
    });
  };

  const createNewWhiteboard = () => {
    const newId = Math.max(...whiteboards.map((wb) => wb.id)) + 1;
    const newWhiteboard = {
      id: newId,
      name: `Whiteboard ${newId}`,
      data: JSON.stringify([]),
    };
    const newWhiteboards = [...whiteboards, newWhiteboard];
    setWhiteboards(newWhiteboards);
    setCurrentWhiteboard(newId);
    setVisibleWhiteboards([
      Math.max(0, newWhiteboards.length - 2),
      newWhiteboards.length - 1,
    ]);
    setObjects([]);
    setHistory([JSON.stringify([])]);
    setRedoHistory([]);
  };

  const navigateWhiteboard = (direction) => {
    const currentIndex = whiteboards.findIndex(
      (wb) => wb.id === currentWhiteboard,
    );
    let newIndex;

    switch (direction) {
      case "first":
        newIndex = 0;
        setVisibleWhiteboards([0, 1]);
        break;
      case "last":
        newIndex = whiteboards.length - 1;
        setVisibleWhiteboards([whiteboards.length - 2, whiteboards.length - 1]);
        break;
      case "prev":
        newIndex = currentIndex > 0 ? currentIndex - 1 : whiteboards.length - 1;
        setVisibleWhiteboards([newIndex, (newIndex + 1) % whiteboards.length]);
        break;
      case "next":
        newIndex = currentIndex < whiteboards.length - 1 ? currentIndex + 1 : 0;
        setVisibleWhiteboards([
          newIndex - 1 >= 0 ? newIndex - 1 : whiteboards.length - 1,
          newIndex,
        ]);
        break;
    }

    switchWhiteboard(whiteboards[newIndex].id);
  };

  // Helper: get bounding box for selected object (for icon positioning)
  const getSelectedObjectBoundingBox = () => {
    if (selectedObjectIndex === null || !objects[selectedObjectIndex])
      return null;
    const obj = objects[selectedObjectIndex];
    switch (obj.type) {
      case "rectangle":
      case "square":
      case "image":
        return { x: obj.x, y: obj.y, w: obj.w, h: obj.h };
      case "circle":
        return {
          x: obj.x - obj.r,
          y: obj.y - obj.r,
          w: obj.r * 2,
          h: obj.r * 2,
        };
      case "text": {
        const canvas = canvasRef.current;
        if (!canvas) return null;
        const ctx = canvas.getContext("2d");
        ctx.font = `${obj.fontStyle || "normal"} ${obj.fontSize || 16}px ${obj.fontFamily || "Arial"}`;
        const width = ctx.measureText(obj.text).width;
        return { x: obj.x, y: obj.y - obj.fontSize, w: width, h: obj.fontSize };
      }
      case "triangle":
      case "pentagon":
      case "hexagon":
      case "star": {
        const r = obj.r || 0;
        return { x: obj.x - r, y: obj.y - r, w: r * 2, h: r * 2 };
      }
      case "heart":
        return { x: obj.x, y: obj.y, w: obj.size, h: obj.size };
      case "line":
        return {
          x: Math.min(obj.x1, obj.x2),
          y: Math.min(obj.y1, obj.y2),
          w: Math.abs(obj.x2 - obj.x1),
          h: Math.abs(obj.y2 - obj.y1),
        };
      default:
        return null;
    }
  };

  // Delete selected object
  const handleDeleteSelected = () => {
    if (selectedObjectIndex === null) return;
    setObjects((prevObjs) => {
      const updated = prevObjs.filter((_, idx) => idx !== selectedObjectIndex);
      setTimeout(() => {
        saveState(updated);
        setSelectedObjectIndex(null);
        // If all images are deleted, reset selectedImage
        if (!updated.some((obj) => obj.type === "image")) {
          setSelectedImage(null);
        }
      }, 0);
      return updated;
    });
  };

  // Copy selected object
  const handleCopySelected = () => {
    if (selectedObjectIndex === null) return;
    setObjects((prevObjs) => {
      const obj = prevObjs[selectedObjectIndex];
      // Deep copy for image/text/shape/line
      let copy = JSON.parse(JSON.stringify(obj));
      // For image, keep the img src string
      if (
        copy.type === "image" &&
        obj.img &&
        typeof obj.img === "object" &&
        obj.img.src
      ) {
        copy.img = obj.img.src;
      }
      // Offset the copy a bit
      if ("x" in copy) copy.x += 20;
      if ("y" in copy) copy.y += 20;
      if (copy.type === "line") {
        copy.x1 += 20;
        copy.y1 += 20;
        copy.x2 += 20;
        copy.y2 += 20;
      }
      const updated = [...prevObjs, copy];
      setTimeout(() => {
        saveState(updated);
        setSelectedObjectIndex(updated.length - 1);
      }, 0);
      return updated;
    });
  };

  return (
    <div className="whiteboard-container">
      <div className="whiteboard-header">
        <h1>AR Smart Whiteboard</h1>
        <div className="whiteboard-nav">
          <div className="whiteboard-tabs">
            <button
              className="nav-button"
              onClick={() => navigateWhiteboard("first")}
              title="First whiteboard"
            >
              <FaAngleDoubleLeft />
            </button>
            <button
              className="nav-button"
              onClick={() => navigateWhiteboard("prev")}
              title="Previous whiteboard"
            >
              <FaChevronLeft />
            </button>
            {whiteboards
              .filter((_, index) => visibleWhiteboards.includes(index))
              .map((wb) => (
                <div
                  key={wb.id}
                  className={`whiteboard-tab${currentWhiteboard === wb.id ? " active" : ""}`}
                  onClick={() => switchWhiteboard(wb.id)}
                >
                  {wb.name}
                </div>
              ))}
            <button
              className="nav-button"
              onClick={() => navigateWhiteboard("next")}
              title="Next whiteboard"
            >
              <FaChevronRight />
            </button>
            <button
              className="nav-button"
              onClick={() => navigateWhiteboard("last")}
              title="Last whiteboard"
            >
              <FaAngleDoubleRight />
            </button>
            <div className="new-board-button" onClick={createNewWhiteboard}>
              <FaPlus />
            </div>
          </div>
          <button
            className="header-button"
            onClick={() => handleToolClick("export")}
          >
            Save Drawing
          </button>
        </div>
      </div>
      <div className="whiteboard-content">
        <div className="toolbar">
          {tools.map(({ icon, name }) => (
            <div
              key={name}
              className={`tool-button${tool === name ? " active" : ""}`}
              onClick={() => handleToolClick(name)}
              title={name}
            >
              {icon}
            </div>
          ))}
          {/* <input
            type="color"
            className="color-picker-tool"
            value={color}
            onChange={(e) => setColor(e.target.value)}
          /> */}
          <input
            id="image-upload"
            type="file"
            accept="image/*"
            style={{ display: "none" }}
            onChange={handleImageUpload}
          />
        </div>
        <div
          className="canvas-container"
          ref={containerRef}
          style={{
            position: "relative",
            width: "100%",
            height: "600px",
            maxHeight: "80vh",
            minHeight: "300px",
            background: "#fff",
          }}
        >
          <canvas
            ref={canvasRef}
            className="drawing-canvas"
            style={{
              width: "100%",
              height: "100%",
              display: "block",
              background: "#fff",
              cursor: isDragging
                ? "move"
                : isResizing
                  ? "nwse-resize"
                  : selectedObjectIndex !== null
                    ? "pointer"
                    : tool === "select"
                      ? "default"
                      : tool === "pencil"
                        ? `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M3,21 L1,23 L1,21 L3,21 Z' fill='%23000'/%3E%3Cpath d='M3,21 L15,9 C16,8 16,6.5 15,5.5 C14,4.5 12.5,4.5 11.5,5.5 L1,16 L1,21 L3,21 Z' fill='%23222' stroke='%23000' stroke-width='0.5'/%3E%3Cpath d='M11.5,5.5 L15,9 L9,15 L5.5,11.5 Z' fill='%23555' stroke='%23000' stroke-width='0.5'/%3E%3C/svg%3E") 1 23, crosshair`
                        : tool === "eraser"
                          ? "url(\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='24' height='24'><rect x='2' y='2' width='20' height='20' rx='4' fill='#eee' stroke='#888' stroke-width='2'/></svg>\") 12 12, pointer"
                          : "default",
            }}
            onMouseDown={handleCanvasMouseDown}
            onMouseMove={handleCanvasMouseMove}
            onMouseUp={handleCanvasMouseUp}
            onMouseLeave={handleCanvasMouseLeave}
          />
          {/* Show delete/copy icons above selected object */}
          {selectedObjectIndex !== null &&
            (() => {
              const bbox = getSelectedObjectBoundingBox();
              if (!bbox) return null;
              const iconSize = 28;
              const left = bbox.x + bbox.w / 2 - iconSize;
              const top = Math.max(bbox.y - iconSize - 8, 0);
              return (
                <div
                  className="object-action-icons"
                  style={{
                    left: left,
                    top: top,
                    width: "auto",
                    position: "absolute",
                  }}
                >
                  <button
                    className="object-action-btn delete"
                    title="Delete"
                    onClick={handleDeleteSelected}
                  >
                    <FaTrash />
                  </button>
                  <button
                    className="object-action-btn copy"
                    title="Copy"
                    onClick={handleCopySelected}
                  >
                    <FaCopy />
                  </button>
                </div>
              );
            })()}
          {showTextInput && (
            <input
              className="text-input-overlay"
              type="text"
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              onBlur={addText}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  addText();
                }
              }}
              autoFocus
              style={{
                position: "absolute",
                left: textPosition.x,
                top: textPosition.y - fontSize,
                font: `${fontStyle} ${fontSize}px ${fontFamily}`,
                color: color,
                background: "rgba(255,255,255,0.95)",
                border: "1px solid #ccc",
                padding: "2px 4px",
                minWidth: 40,
                outline: "none",
                zIndex: 1002,
                pointerEvents: "auto",
              }}
            />
          )}
        </div>
        <div className="options-panel">{renderToolOptions()}</div>
      </div>
      {showConfirmDialog && (
        <div className="confirm-dialog-overlay">
          <div className="confirm-dialog">
            <div className="confirm-dialog-content">
              <p>Are you sure you want to clear the whiteboard?</p>
              <div className="confirm-dialog-actions">
                <button className="confirm-btn" onClick={handleClearConfirm}>
                  Confirm
                </button>
                <button
                  className="cancel-btn"
                  onClick={() => setShowConfirmDialog(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Whiteboard;
