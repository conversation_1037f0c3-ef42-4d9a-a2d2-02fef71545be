import {
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  RightOutlined,
} from "@ant-design/icons";
import { Collapse, IconButton, TableCell, TableRow } from "@mui/material";
import React from "react";
import SubTraining from "./SubMenu/SubTraining";
import { NavLink, useParams } from "react-router-dom";
const MainItem = ({ data }) => {
  const { mid } = useParams();
  return (
    <>
      <TableRow
        sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
        style={{ cursor: "pointer" }}
      >
        <TableCell
          style={{ borderBottom: "none", color: "green" }}
          align="center"
        >
          {data.title}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          {data.desc}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          Active
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          <IconButton>
            <EditOutlined />
          </IconButton>

          <IconButton>
            <DeleteOutlined />
          </IconButton>

          <IconButton>
            <NavLink to={`/${mid}/manuals/${data.id}`}>
              <RightOutlined />
            </NavLink>
          </IconButton>
        </TableCell>
      </TableRow>
    </>
  );
};

export default MainItem;
