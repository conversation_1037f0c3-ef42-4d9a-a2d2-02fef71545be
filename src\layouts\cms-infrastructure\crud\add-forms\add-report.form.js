import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { addDataToReports } from "../functions/cms-infra.functions";
import { useContext, useState } from "react";
import { CmsInfraContext } from "../../../../services/cms-infrastructure/cms-infra.context";

const AddCmsReportForm = () => {
  const { instruments, cmsData } = useContext(CmsInfraContext);
  const [code, setCode] = useState("");
  const [ecode, setECode] = useState("");

  async function handleOnSubmit(e) {
    e.preventDefault();

    const formData = {
      sap_master_key: code,
      date: new Date(),
      eqp_master_key: ecode,
    };

    Array.from(e.currentTarget.elements).forEach((field) => {
      if (!field.name) return;
      formData[field.name] = field.value;
    });

    addDataToReports(formData);
  }

  return (
    <div>
      <form onSubmit={handleOnSubmit}>
        <TextField
          sx={{ mb: 2 }}
          placeholder="Calibration Procedure Number"
          fullWidth
          variant="outlined"
          name="cal_p_no"
        />
        <TextField
          sx={{ mb: 2 }}
          placeholder="Calibrated By"
          fullWidth
          variant="outlined"
          name="calibrated_by"
        />
        <TextField
          sx={{ mb: 2 }}
          placeholder="Created By"
          fullWidth
          variant="outlined"
          name="created_by"
        />
        <TextField
          sx={{ mb: 2 }}
          placeholder="Criticality"
          fullWidth
          variant="outlined"
          name="criticality"
        />
        <TextField
          sx={{ mb: 2 }}
          placeholder="Remarks"
          fullWidth
          variant="outlined"
          name="remarks"
        />
        <FormControl sx={{ mb: 3 }} fullWidth>
          <InputLabel>Select Instrument</InputLabel>
          <Select
            label="Select Instrument"
            onChange={(e) => setCode(e.target.value)}
          >
            {instruments.map((item, i) => (
              <MenuItem value={item?.id} key={i}>
                {item?.code_no}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl sx={{ mb: 3 }} fullWidth>
          <InputLabel>Select Equipment</InputLabel>
          <Select
            label="Select Instrument"
            onChange={(e) => setECode(e.target.value)}
          >
            {cmsData.map((item, i) => (
              <MenuItem value={item?.id} key={i}>
                {item?.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Button type="submit" variant="contained" fullWidth>
          Submit
        </Button>
      </form>
    </div>
  );
};

export default AddCmsReportForm;
