import React, { useState, useEffect, useMemo } from "react";
import "./cfr.scss";
import { sharedCss } from "../../styles/sharedCss";
import { companies, companyId_constant, machines } from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";
import {
  FormControl,
  Select,
  Checkbox,
  InputLabel,
  MenuItem,
  ListItemText,
  IconButton,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import MachineItem from "./MachineItem";
import UserTable from "./UserTable";
import KeyboardDoubleArrowDownIcon from "@mui/icons-material/KeyboardDoubleArrowDown";
import KeyboardDoubleArrowUpIcon from "@mui/icons-material/KeyboardDoubleArrowUp";
import { useStateContext } from "../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import NoDataComponent from "../commons/noData.component";

const StyledSelect = styled(Select)(({ theme }) => ({
  "& .MuiInputBase-input": {
    position: "relative",
    fontSize: 13,
    padding: "8px 18px",
  },
}));

const CFR_TYPE = ["events", "alarms", "users"];
const Activities = ["login", "logout"];

function CFR({ filteredMachineName }) {
  const [reportData, setReportData] = useState({});
  const [Machines, setMachines] = useState([]);
  const [machinesData, setMachinesData] = useState([]);
  // const [filteredMachine, setFilteredMachine] = useState([]);
  const [filteredActivity, setFilteredActivity] = useState(Activities);
  const [cfrType, setCfrType] = useState(CFR_TYPE[0]);
  const [loading, setLoading] = useState(true);
  const [alerts, setAlerts] = useState("");
  const [alertLoading, setAlertLoading] = useState(true);
  const [sessionData, setSessionData] = useState([]);
  const [sessionLoading, setSessionLoading] = useState(true);
  const [ascending, setAscending] = useState(true);
  const { currentMode, currentColorLight } = useStateContext();
  const [value, setValue] = useState("all");
  const [allMachines, setAllMachines] = useState(["All"]);

  const handleMachineData = async () => {
    await axios
      .get(`${dbConfig.url}/machines`)
      .then((response) => {
        setMachinesData(response.data?.data);
        const allMachinesName = response.data?.data.map(
          (machine) => machine.title,
        );
        setMachines(allMachinesName);
      })
      .catch((error) => {
        console.error(error);
      });

    await axios
      .get(`${dbConfig.url}/cfr/machine_events`)
      .then((response) => {
        setReportData(response.data);
        console.log("cfr  machine user ka data", response.data);
      })
      .catch((error) => {
        console.error(error);
      });

    await axios
      .get(`${dbConfig.url}/cfr/userSessions`)
      .then((response) => {
        setSessionData(response.data);
        console.log("cfr user ka data", response.data);
      })
      .catch((error) => {
        console.error(error);
      });
  };
  useEffect(() => {
    handleMachineData();
    setValue("all");

    setAllMachines((prev) => {
      if (filteredMachineName?.toLowerCase() === "all") {
        return prev;
      }

      return [filteredMachineName];
    });
  }, []);
  // useEffect(() => {
  //     db.collection(companies)
  //         .doc(companyId_constant)
  //         .collection(machines)
  //         .onSnapshot((snap) => {
  //             const data = firebaseLooper(snap);
  //             const allMachinesName = data.map(machine => machine.title);
  //             setMachinesData(data)
  //             setAllMachines(allMachinesName);
  //             setMachines(allMachinesName)
  //             // setter(allMachinesName)
  //         });
  //     db.collection(companies)
  //         .doc(companyId_constant)
  //         .collection("cfrReport")
  //         .onSnapshot((snap) => {
  //             const data = firebaseLooper(snap);
  //             setReportData(data);
  //             setLoading(false);
  //         });
  //     db.collection(companies)
  //         .doc(companyId_constant)
  //         .collection('Alerts')
  //         .onSnapshot(snap => {
  //             const data = firebaseLooper(snap)
  //             setAlerts(data)
  //             setAlertLoading(false)
  //         })
  //     db.collection(companies)
  //         .doc(companyId_constant)
  //         .collection("cfr_userSession")
  //         .onSnapshot(snap => {
  //             const data = firebaseLooper(snap)
  //             setSessionData(data)
  //             setSessionLoading(false)
  //         })
  // }, []);

  // const setter = toSet => {
  //     setFilteredMachine(toSet);
  // }

  const setterUser = (toSet) => {
    if (cfrType === CFR_TYPE[2]) setFilteredActivity(toSet);
    else setAllMachines(toSet);
  };

  const handleFilter = (event) => {
    setValue("");
    const {
      target: { value },
    } = event;
    if (cfrType === CFR_TYPE[2])
      setFilteredActivity(typeof value === "string" ? value.split(",") : value);
    else setAllMachines(typeof value === "string" ? value.split(",") : value);
  };

  const handleSort = () => {
    const newArray = [...machinesData];
    newArray.sort((a, b) => {
      const nameA = a.title.toUpperCase();
      const nameB = b.title.toUpperCase();
      if (!ascending) {
        if (nameA < nameB) {
          return -1;
        }
        if (nameA > nameB) {
          return 1;
        }
      } else {
        if (nameA < nameB) {
          return 1;
        }
        if (nameA > nameB) {
          return -1;
        }
      }
      return 0;
    });
    setMachinesData(newArray);
    setAscending(!ascending);
  };

  // const handleRadioButtons = e => {
  //     setValue(e.target.value)
  //     if (e.target.value === "all") {
  //         setter(allMachines)
  //     } else if (e.target.value === "none") {
  //         setter([])
  //     }
  // }

  const handleRadioButtonsUser = (e) => {
    setValue(e.target.value);
    if (e.target.value === "all") {
      if (cfrType === CFR_TYPE[2]) setterUser(Activities);
      else setterUser(Machines);
    } else if (e.target.value === "none") {
      setterUser([]);
    }
  };
  const commonCss = sharedCss();

  const filterMachineCFRData = (machine) => {
    if (allMachines.includes("All")) return true;
    else return allMachines.includes(machine.title);
  };

  const isFilteredTrainingReportDataAvailable = useMemo(
    () => machinesData.filter(filterMachineCFRData).length,
    [allMachines, machinesData],
  );

  return (
    <main
      className={`cfrMain ${commonCss?.generalBackground} border-radius-outer`}
    >
      <header className="machineListHeader">
        <div className="tabsContainer">
          <div
            onClick={() => setCfrType(CFR_TYPE[0])}
            className={
              cfrType === CFR_TYPE[0] ? "cfr_tab cfr_active_tab" : "cfr_tab"
            }
            style={currentMode === "Dark" ? { color: "white" } : {}}
          >
            Events
          </div>
          <div
            onClick={() => setCfrType(CFR_TYPE[1])}
            className={
              cfrType === CFR_TYPE[1] ? "cfr_tab cfr_active_tab" : "cfr_tab"
            }
            style={currentMode === "Dark" ? { color: "white" } : {}}
          >
            Alarms
          </div>
          <div
            onClick={() => setCfrType(CFR_TYPE[2])}
            className={
              cfrType === CFR_TYPE[2] ? "cfr_tab cfr_active_tab" : "cfr_tab"
            }
            style={currentMode === "Dark" ? { color: "white" } : {}}
          >
            Users
          </div>
        </div>
        <div>
          {cfrType === CFR_TYPE[2] ? (
            <div className="machine-filter">
              <span>Select Activity: </span>
              <FormControl fullWidth size="small">
                <InputLabel htmlFor="activity">Activity</InputLabel>
                <StyledSelect
                  fullWidth
                  multiple
                  value={filteredActivity}
                  onChange={handleFilter}
                  name="activity"
                  label="Activity"
                  renderValue={(selected) => {
                    if (selected.length === Activities.length) return "All";
                    else return selected.join(", ");
                  }}
                >
                  <MenuItem>
                    <FormControl>
                      <RadioGroup
                        name="radio-buttons-group"
                        value={value}
                        onChange={(e) => handleRadioButtonsUser(e)}
                        className="flex"
                      >
                        <FormControlLabel
                          value="all"
                          control={<Radio />}
                          label="SELECT ALL"
                        />
                        <FormControlLabel
                          value="none"
                          control={<Radio />}
                          label="SELECT NONE"
                        />
                      </RadioGroup>
                    </FormControl>
                  </MenuItem>
                  {Activities?.map((activity) => {
                    return (
                      <MenuItem
                        key={activity}
                        className="activity-option"
                        value={activity}
                      >
                        <Checkbox
                          checked={filteredActivity.indexOf(activity) > -1}
                        />
                        <ListItemText primary={activity} />
                      </MenuItem>
                    );
                  })}
                </StyledSelect>
              </FormControl>
            </div>
          ) : (
            <div className="machine-filter">
              <span>Select Machines: </span>
              <FormControl fullWidth size="small">
                <InputLabel htmlFor="activity">Machines</InputLabel>
                <StyledSelect
                  fullWidth
                  multiple
                  value={allMachines}
                  onChange={handleFilter}
                  name="activity"
                  label="Activity"
                  renderValue={(selected) => {
                    if (allMachines.length === Machines.length) return "All";
                    else return selected.join(", ");
                  }}
                >
                  <MenuItem>
                    <FormControl>
                      <RadioGroup
                        name="radio-buttons-group"
                        value={value}
                        onChange={(e) => handleRadioButtonsUser(e)}
                        className="flex"
                      >
                        <FormControlLabel
                          value="all"
                          control={<Radio />}
                          label="SELECT ALL"
                        />
                        <FormControlLabel
                          value="none"
                          control={<Radio />}
                          label="SELECT NONE"
                        />
                      </RadioGroup>
                    </FormControl>
                  </MenuItem>
                  {Machines?.map((activity) => {
                    return (
                      <MenuItem
                        key={activity}
                        className="activity-option"
                        value={activity}
                      >
                        <Checkbox
                          checked={allMachines.indexOf(activity) > -1}
                        />
                        <ListItemText primary={activity} />
                      </MenuItem>
                    );
                  })}
                </StyledSelect>
              </FormControl>
            </div>
          )}
        </div>
      </header>
      {cfrType !== CFR_TYPE[2] ? (
        <section className="machineItems ">
          <div
            className="machineItems_tableHead"
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#212B36", border: "1px solid white" }
                : { border: "1px solid black" }
            }
          >
            <div className="machineItems_tableCell"> Machines</div>
            <div className="machineItems_tableCell">
              <div className="machine-filter">
                {/* <span>Select Machines: </span>
                                <FormControl fullWidth size="small">
                                    <InputLabel
                                        htmlFor="machines">Machines</InputLabel>
                                    <StyledSelect
                                        className="select-machine"
                                        multiple
                                        value={filteredMachine}
                                        onChange={handleFilter}
                                        name="machines"
                                        label="Machines"
                                        renderValue={(selected) => {
                                            if (selected.length === allMachines.length) return "All";
                                            else return selected.join(', ');
                                        }}
                                    >
                                        <MenuItem>
                                            <FormControl>
                                                <RadioGroup name="radio-buttons-group" value={value} onChange={e => handleRadioButtons(e)}>
                                                    <FormControlLabel value="all" control={<Radio />} label="SELECT ALL" />
                                                    <FormControlLabel value="none" control={<Radio />} label="SELECT NONE" />
                                                </RadioGroup>
                                            </FormControl>
                                        </MenuItem>
                                        {allMachines
                                            ?.sort((a, b) => {
                                                const nameA = a.toUpperCase();
                                                const nameB = b.toUpperCase();
                                                if (nameA < nameB) {
                                                    return -1;
                                                }
                                                if (nameA > nameB) {
                                                    return 1;
                                                }
                                                return 0;
                                            })
                                            ?.map((machine) => {
                                                return (
                                                    <MenuItem
                                                        key={machine}
                                                        className="machine-option"
                                                        value={machine}
                                                    >
                                                        <Checkbox checked={filteredMachine.indexOf(machine) > -1} />
                                                        <ListItemText primary={
                                                            machine
                                                        } />
                                                    </MenuItem>
                                                );
                                            })}
                                    </StyledSelect>
                                </FormControl> */}
                <IconButton
                  onClick={handleSort}
                  style={currentMode === "Dark" ? { color: "white" } : {}}
                >
                  {ascending ? (
                    <KeyboardDoubleArrowDownIcon />
                  ) : (
                    <KeyboardDoubleArrowUpIcon />
                  )}
                </IconButton>
              </div>
            </div>
          </div>
          {machinesData
            ?.filter(filterMachineCFRData)
            ?.sort((a, b) => {
              const nameA = a.title.toUpperCase();
              const nameB = b.title.toUpperCase();
              if (ascending) {
                if (nameA < nameB) {
                  return -1;
                }
                if (nameA > nameB) {
                  return 1;
                }
              } else {
                if (nameA < nameB) {
                  return 1;
                }
                if (nameA > nameB) {
                  return -1;
                }
              }
              return 0;
            })
            ?.map((machine) => {
              return (
                <MachineItem
                  key={machine._id}
                  machineDetails={machine.title}
                  mid={machine._id}
                  data={reportData}
                  load={loading}
                  alert={alerts}
                  cfrType={cfrType}
                  alertLoad={alertLoading}
                />
              );
            })}
          {!isFilteredTrainingReportDataAvailable && (
            <div className={"machineItem__noData"}>
              <NoDataComponent paddText={true} />
            </div>
          )}
        </section>
      ) : (
        <>
          {sessionData.filter((data) =>
            new Set(filteredActivity).has(data.activity),
          ).length ? (
            <UserTable
              data={sessionData.filter((data) =>
                new Set(filteredActivity).has(data.activity),
              )}
              load={sessionLoading}
            />
          ) : (
            <div className={"flex items-center justify-center"}>
              <NoDataComponent useAtTable={false} />
            </div>
          )}
        </>
      )}
    </main>
  );
}

export default CFR;
