import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  InputLabel,
  Typography,
  Box,
  TextField,
} from "@mui/material";

const CommonNodeModal = ({
  openDialog = false,
  dialogTitleText = "",
  textName = "",
  textNameInputLabel = "",
  textNameInputSize = "small",
  textNameInputStyle = {},
  textNameInputPlaceholder = "",
  handleTextNameChange = () => {},
  handleTextNameBlur = () => {},
  textDescription = "",
  textDescriptionInputLabel = "",
  textDescriptionInputSize = "small",
  textDescriptionInputStyle = {},
  textDescriptionInputPlaceholder = "",
  handleTextDescriptionChange = () => {},
  handleTextDescriptionBlur = () => {},
  buttonCloseText = "",
  buttonSubmitText = "",
  isSubmitButtonDisabled = true,
  handleClose = () => {},
  handleSubmit = () => {},
}) => {
  return (
    <Dialog open={openDialog} fullWidth>
      <DialogTitle>
        <Typography variant={"h5"} component={"h5"}>
          {dialogTitleText}
        </Typography>
      </DialogTitle>
      <DialogContent>
        <Box>
          {!!textNameInputLabel && (
            <InputLabel id={`${textNameInputLabel?.toString()}-id-`}>
              {textNameInputLabel}
            </InputLabel>
          )}
          <TextField
            fullWidth
            variant="outlined"
            value={textName || ""}
            size={textNameInputSize}
            sx={textNameInputStyle}
            placeholder={textNameInputPlaceholder}
            onChange={handleTextNameChange}
            onBlur={handleTextNameBlur}
          />
        </Box>
        <Box>
          {!!textDescriptionInputLabel && (
            <InputLabel id={`${textDescriptionInputLabel?.toString()}-id-`}>
              {textDescriptionInputLabel}
            </InputLabel>
          )}
          <TextField
            fullWidth
            variant="outlined"
            value={textDescription || ""}
            size={textDescriptionInputSize}
            sx={textDescriptionInputStyle}
            placeholder={textDescriptionInputPlaceholder}
            onChange={handleTextDescriptionChange}
            onBlur={handleTextDescriptionBlur}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button variant={"contained"} color={"error"} onClick={handleClose}>
          {buttonCloseText}
        </Button>
        <Button
          variant={"contained"}
          color={"primary"}
          onClick={handleSubmit}
          disabled={isSubmitButtonDisabled}
        >
          {buttonSubmitText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CommonNodeModal;
