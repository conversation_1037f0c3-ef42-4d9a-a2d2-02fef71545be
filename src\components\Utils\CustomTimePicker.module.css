.container {
  position: relative;
  width: 100%;
}

.inputButton {
  height: 60px;
  width: 100%;
  border: 1px solid gray;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.inputButton:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.active {
  border-width: 2px;
}

.placeholderText {
  color: #6b7280; /* <PERSON><PERSON><PERSON>'s gray-500 */
}

.selectedText {
  color: #111827; /* <PERSON><PERSON><PERSON>'s gray-900 */
}

.icon {
  color: #9ca3af; /* <PERSON><PERSON><PERSON>'s gray-400 */
}

.pickerDropdown {
  position: absolute;
  z-index: 50;
  margin-top: 0.5rem;
  border: 1px solid;
  border-radius: 0.375rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-height: 300px;
}

.timePickerContainer {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  gap: 0.5rem;
}

.timePickerColumn {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: center;
}

.columnLabel {
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  text-align: center;
}

.columnScroller {
  display: flex;
  flex-direction: column;
  height: 150px;
  overflow-y: auto;
  width: 100%;
  border-radius: 4px;
  scrollbar-width: thin;
  scroll-behavior: smooth;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: rgba(0, 0, 0, 0.02);
}

@media (max-height: 600px) {
  .columnScroller {
    height: 120px;
  }
}

.columnScroller::-webkit-scrollbar {
  width: 4px;
}

.columnScroller::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.optionButton {
  padding: 0.5rem;
  border: none;
  background: transparent;
  cursor: pointer;
  text-align: center;
  border-radius: 4px;
  margin: 2px 0;
  color: inherit;
}

.optionButton:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.optionButton.selected {
  color: white;
}

.footer {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  border-top: 1px solid #e5e7eb; /* Tailwind's gray-200 */
}

.cancelButton {
  font-size: 0.875rem;
  background: none;
  border: none;
  cursor: pointer;
}

.okButton {
  font-size: 0.875rem;
  padding: 0.25rem 1rem;
  color: white;
  border-radius: 0.25rem;
  border: none;
  cursor: pointer;
}
