import React, { useState } from "react";
import {
  Input<PERSON><PERSON><PERSON>,
  TextField,
} from "@mui/material";
import { useAuth } from "../../hooks/AuthProvider";
import {
  ButtonBasic,
  ButtonBasicCancel,
  SubmitButtons,
} from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { useCreateMachineCfr } from "../../hooks/cfr/machineCfrProvider";
import { DropzoneArea } from "material-ui-dropzone";
import { convertBase64 } from "../../hooks/useBase64";
import GetPreviewComponent from "../../components/commons/getPreview.component";

const AddTraining = ({ mid, machineName, handleClose }) => {
  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");
  const { currentColor, currentMode } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const { currentUser } = useAuth();
  const addtrainingcfr = useCreateMachineCfr();

  const [imageUrl, setImageUrl] = useState("");
  const [selectedImageForStorage, setSelectedImageForStorage] = useState(null);

  const handleImageChange = async (loadedFiles) => {
    if (!loadedFiles || loadedFiles.length === 0) {
      setSelectedImageForStorage(null);
      setImageUrl("");
      return;
    }
    const selectedFile = loadedFiles[0];
    const base64 = await convertBase64(selectedFile);
    setSelectedImageForStorage(selectedFile);
    setImageUrl(base64);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const date = new Date();

    let imageUploadRes = null;
    if (selectedImageForStorage) {
      let fd = new FormData();
      fd.append("image", selectedImageForStorage);
      try {
        imageUploadRes = await axios.post(
          `${dbConfig?.url_storage}/upload`,
          fd
        );
      } catch (err) {
        console.log("Image upload error:", err);
        toastMessage({ message: "Image upload failed" });
        return;
      }
    }

    const data2 = {
      activity: "Training added",
      dateTime: date,
      description: "Training is added",
      machine: mid,
      module: "Training",
      username: currentUser.username,
    };

    const data = {
      title,
      desc,
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      mid: mid,
      image_url: selectedImageForStorage ? imageUploadRes?.data?.data : "",
    };

    await axios.post(`${dbConfig.url}/training`, data).then(() => {
      addtrainingcfr(data2);
      handleClose();
      setRefreshCount(refreshCount + 1);
      toastMessageSuccess({ message: "Training added successfully!" });
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Title</InputLabel>
      <TextField
        onChange={(e) => setTitle(e.target.value)}
        onBlur={(e) => setTitle(title?.trim())}
        value={title}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Description</InputLabel>
      <TextField
        onChange={(e) => setDesc(e.target.value)}
        onBlur={() => setDesc(desc?.trim())}
        value={desc}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{ marginBottom: "12px" }}
      />

      <InputLabel className="mb-1">Add Image</InputLabel>
      <DropzoneArea
        acceptedFiles={["image/*"]}
        showFileNames={true}
        onChange={(loadedFiles) => handleImageChange(loadedFiles)}
        dropzoneText="Drag and Drop / Click to ADD Image"
        showAlerts={false}
        filesLimit={1}
        maxFileSize={10 * 1024 * 1024}
        onDelete={() => handleImageChange([])}
      />
      {imageUrl && (
        <div className="my-2" style={{ display: "flex", justifyContent: "center" }}>
          <GetPreviewComponent
            sourceUrl={imageUrl}
            fileFormat="image"
            previewImageStyle={{ width: "450px" }}
          />
        </div>
      )}

      <div className="p-2 mt-2 flex justify-between">
        <ButtonBasicCancel
          type="button"
          buttonTitle="Cancel"
          onClick={handleClose}
        />
        <SubmitButtons type="submit" buttonTitle="Submit" />
      </div>
    </form>
  );
};

export default AddTraining;
