const ERR_STEP_QR1 = "Please scan all QR Codes";
const ERR_STEP_QR2 = "Scanned QR codes do not match required ones.";
const ERR_STEP_ATTACH = "Please attach a photo for this step.";
const ERR_STEP_REMARK = "Remarks are required before moving forward.";

export const validateStep = (item = {}) => {
  const step = item;

  // 1. Check QR codes
  if (Array.isArray(step.qr_codes) && step.qr_codes.length > 0) {
    if (
      !Array.isArray(step.qr_scanned) ||
      step.qr_scanned.length < step.qr_codes.length
    ) {
      return { isValid: false, error: ERR_STEP_QR1 };
    }
    if (!step.qr_codes.every((code) => step.qr_scanned.includes(code))) {
      return { isValid: false, error: ERR_STEP_QR2 };
    }
  }

  // 2. Check attachments (for Camera type)
  if (typeof step.type === "string" && step.type.toLowerCase() === "camera") {
    const hasValidAttachment =
      step.attachments && step.attachments.some((attach) => attach?.url);
    if (!hasValidAttachment) {
      return { isValid: false, error: ERR_STEP_ATTACH };
    }
  }

  // 3. Check remarks
  if (
    !step.remarks ||
    typeof step.remarks !== "string" ||
    !step.remarks.trim()
  ) {
    return { isValid: false, error: ERR_STEP_REMARK };
  }

  // All good
  return { isValid: true, error: "" };
};
