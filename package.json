{"name": "arizon", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^4.7.0", "@editorjs/editorjs": "^2.25.0", "@editorjs/header": "^2.6.2", "@editorjs/list": "^1.7.0", "@emailjs/browser": "^3.6.2", "@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@fortawesome/fontawesome-svg-core": "^1.2.36", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@fortawesome/react-fontawesome": "^0.1.16", "@fullcalendar/daygrid": "^5.10.1", "@fullcalendar/interaction": "^5.10.1", "@fullcalendar/react": "^5.10.1", "@fullcalendar/timegrid": "^5.10.1", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@mikecousins/react-pdf": "^6.1.1", "@mui/icons-material": "^5.2.5", "@mui/lab": "^5.0.0-alpha.163", "@mui/material": "^5.2.6", "@mui/styles": "^5.12.0", "@mui/x-date-pickers": "^5.0.5", "@react-pdf-viewer/core": "^3.7.0", "@react-pdf-viewer/default-layout": "^3.11.0", "@react-pdf/renderer": "^3.0.0", "@react-three/drei": "^9.69.2", "@react-three/fiber": "^8.13.0", "@rooks/use-undo-state": "^4.11.2", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@use-it/event-listener": "^0.1.7", "antd": "^4.18.2", "axios": "^1.2.6", "base64toblob": "^0.0.2", "bignumber.js": "^9.3.0", "bootstrap": "^5.1.3", "browserify-zlib": "^0.2.0", "chart.js": "^3.7.0", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-annotation": "^2.0.3", "common-base64-downloader-react": "^1.0.7", "d3": "^7.9.0", "date-fns": "^2.28.0", "debugout.js": "^1.1.0", "file-saver": "^2.0.5", "firebase": "^8.10.0", "html-to-image": "^1.9.0", "html2canvas": "^1.4.1", "i": "^0.3.7", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.25", "konva": "9.3.18", "lodash": "^4.17.21", "lucide-react": "^0.503.0", "material-ui-dropzone": "^3.5.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mui-file-input": "^7.0.0", "native-base": "^3.4.4", "npm": "^8.19.2", "opentok-react": "^0.11.0", "path": "^0.12.7", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.1.91", "pure-react-carousel": "^1.28.1", "qr-scanner": "^1.4.2", "re-resizable": "^6.9.1", "react": "^18.2.0", "react-base64-downloader": "^2.1.7", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.1.0", "react-chartjs-2": "^4.0.0", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-date-range": "^1.4.0", "react-dom": "^18.2.0", "react-editor-js": "^2.1.0", "react-flow-renderer": "^9.7.3", "react-icons": "^4.3.1", "react-image-annotation": "^0.9.10", "react-image-magnify": "^2.7.4", "react-intl": "^5.24.2", "react-konva": "18.0.0-0", "react-material-ui-carousel": "^3.1.1", "react-mui-dropzone": "^4.0.6", "react-native-safe-area-context": "^4.2.5", "react-native-svg": "^12.3.0", "react-pdf": "^9.2.1", "react-picture-annotation": "^1.2.0", "react-pro-sidebar": "^0.7.1", "react-qr-code": "^2.0.12", "react-resizable-rotatable-draggable": "^0.2.0", "react-responsive-carousel": "^3.2.22", "react-rnd": "^10.3.5", "react-router": "^7.2.0", "react-router-dom": "^7.5.2", "react-scripts": "5.0.0", "react-sketch2": "^0.5.7", "react-switch": "^6.0.0", "react-syntax-highlighter": "^15.4.5", "react-time-picker": "^7.0.0", "react-to-pdf": "^0.0.14", "react-toastify": "^9.1.1", "react-use-history-reducer": "^0.1.18", "react-vertical-timeline-component": "^3.6.0", "react-webcam": "^7.2.0", "reactjs-popup": "^2.0.5", "read-excel-file": "^5.4.2", "sass": "^1.56.2", "socket.io-client": "^4.8.1", "source-map-loader": "^4.0.1", "stream": "^0.0.2", "styled-components": "^5.3.5", "three": "^0.152.2", "uuid": "^8.3.2", "web-vitals": "^2.1.2", "xlsx": "^0.18.5", "zod": "^3.25.56"}, "scripts": {"start": "node --max-old-space-size=4096 node_modules/react-scripts/scripts/start.js", "build": "react-scripts build", "test": "jest --coverage --runInBand --forceExit --detectOpenHandles --passWithNoTests", "lint": "eslint --fix \"src/**/*.{js,jsx}\" --format html --output-file eslint-report.html || true", "coverage": "mkdir -p .nyc_output && nyc report --reporter=html", "quality:sonarqube": "npx sonar-scanner", "report": "npm run lint && npm run test || true && mkdir -p .nyc_output && npm run coverage && npm run quality:sonarqube"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-sonarjs": "^3.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "nyc": "^15.1.0", "typescript": "4.1.3"}}