import { <PERSON>alog, DialogContent, DialogTitle } from "@mui/material";
import { Button, TableCell, TableRow } from "@mui/material";
import React, { useState } from "react";
import Delete from "../../components/Delete/Delete";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import { toastMessage } from "../../tools/toast";
import EditDocumentation from "./EditMaintenance/EditDocumentation";

const DocumentationItem = ({ data, type }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);

  const handleDelete = () => {
    // db.collection(companies).doc(companyId_constant)
    // .collection(type).doc(data.id).delete().then(() => {
    //    toastMessage({message: "Deleted Successfully !"})
    // })
  };

  return (
    <>
      <TableRow
        sx={{
          "&:last-child td, &:last-child th": { border: 0 },
        }}
        style={{ cursor: "pointer" }}
      >
        <TableCell style={{ borderBottom: "none" }} align="left">
          {data.title}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          {data.comment}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          {data.start}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          {data.stop}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          {data.time}
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          {data.tolerance}
        </TableCell>
        <TableCell
          style={{ borderBottom: "none", color: "green" }}
          align="center"
        >
          Active
        </TableCell>
        <TableCell style={{ borderBottom: "none" }} align="center">
          <Button onClick={() => setOpenEdit(true)}>
            <i className="ri-pencil-fill"></i>
          </Button>
          <Button onClick={() => setOpenDel(true)}>
            <i className="ri-delete-bin-6-fill text-red-700"></i>
          </Button>
          {/* <i
              className={
                isOpen
                  ? 'ri-arrow-up-s-fill'
                  : 'ri-arrow-down-s-fill'
              }
              onClick={() => setIsOpen(!isOpen)}
            ></i> */}
        </TableCell>
      </TableRow>
      <>
        {isOpen && (
          <TableRow
            sx={{
              "&:last-child td, &:last-child th": {
                border: 0,
              },
            }}
          >
            <TableCell
              className="subData"
              style={{ borderBottom: "none" }}
              align="center"
              colSpan={4}
            ></TableCell>
          </TableRow>
        )}
      </>
      <Dialog open={openEdit} fullWidth>
        <DialogTitle>
          Edit {type} - [{data.title}]
        </DialogTitle>
        <DialogContent>
          <EditDocumentation
            mid={data.mid}
            data={data}
            type={type}
            handleClose={() => setOpenEdit(false)}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={openDel}>
        <Delete onDelete={handleDelete} onClose={() => setOpenDel(false)} />
      </Dialog>
    </>
  );
};

export default DocumentationItem;
