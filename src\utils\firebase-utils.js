import { companies, companyId_constant } from "../constants/data";
import { db } from "../firebase";

import { toastMessage, toastMessageSuccess } from "../tools/toast";

// const ROOT = db.collection(companies).doc(companyId_constant);

export const deleteDocument = (collectionName, docId, deleteMessage) => {
  //   ROOT.collection(collectionName)
  //     .doc(docId)
  //     .delete()
  //     .then((item) => {
  //       toastMessage({message: deleteMessage})
  //     });
  // };
  // export const updateDocument = (collectionName, docId, data, updateMessage) => {
  //   ROOT.collection(collectionName)
  //     .doc(docId)
  //     .update(data)
  //     .then((item) => {
  //       toastMessageSuccess({message: updateMessage})
  //     });
  // };
  // export const addDocumentToCollection = (collectionName, data, addMessage) => {
  //   ROOT.collection(collectionName)
  //     .add(data)
  //     .then((item) => {
  //       console.log('ADDED', item, addMessage);
  //     });
};
