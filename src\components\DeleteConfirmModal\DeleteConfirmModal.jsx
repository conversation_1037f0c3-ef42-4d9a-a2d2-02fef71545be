import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  <PERSON>alog<PERSON>ontent,
  DialogContentText,
  DialogActions,
  TextField,
  Button,
} from "@mui/material";

function DeleteConfirmModal(props) {
  const { open, handleClose, name, type, onDelete } = props;
  const [inputName, setInputName] = useState("");

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle>Delete {type}</DialogTitle>
      <DialogContent>
        <DialogContentText>Are you absolutely sure?</DialogContentText>
        <DialogContentText>
          You are going to delete <b>{name}</b>, this {type} will be deleted
          along with all of its content.
        </DialogContentText>
        <DialogContentText
          sx={{
            padding: "8px",
            color: "#c50000",
            textAlign: "center",
            background: "#f7d4d6",
            border: "1px solid #c50000",
            borderRadius: "4px",
            margin: "16px 0px",
          }}
        >
          <b>Warning:</b> This action is not reversible. Please be certain.
        </DialogContentText>
        <DialogContentText sx={{ marginBottom: "16px" }}>
          Please type <b>{name}</b> to proceed or press Cancel to close.
        </DialogContentText>
        <TextField
          fullWidth
          autoFocus
          type="text"
          variant="outlined"
          label=""
          value={inputName}
          onChange={(e) => setInputName(e.target.value)}
        />
        <DialogActions
          sx={{
            display: "flex",
            justifyContent: "space-between",
            marginTop: "24px",
          }}
        >
          <Button onClick={handleClose} variant="contained" color="success">
            Cancel
          </Button>
          <Button
            disabled={inputName === name ? false : true}
            onClick={() => {
              onDelete();
              handleClose();
            }}
            variant="contained"
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </DialogContent>
    </Dialog>
  );
}

export default DeleteConfirmModal;
