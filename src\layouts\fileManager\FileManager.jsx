import FolderCard from "./FolderCard";
// import FolderPreviewContainer from './FolderPreviewContainer';
import { useFolder } from "../../hooks/useFolder";
import { useParams } from "react-router-dom";
import AddFileButton from "./addFileButton/AddFileButton";
import FileCard from "./FileCard";
import FolderBreadcrumbs from "./FolderBreadCrumbs";
import "./fileManager.scss";
import Box from "@mui/material/Box";
import { Typography } from "@mui/material";
import { useEffect, useMemo, useReducer, useState } from "react";
import { db } from "../../firebase";
import { companies, companyId_constant, machines } from "../../constants/data";
import { userDetails } from "../../context/RoleContext";
import { useAuth } from "../../hooks/AuthProvider";
import { firebaseLooper } from "../../tools/tool";
import GridViewIcon from "@mui/icons-material/GridView";
import FormatListBulletedIcon from "@mui/icons-material/FormatListBulleted";
import {
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from "@mui/material";
import { makeStyles } from "@mui/styles";

import ClearAllIcon from "@mui/icons-material/ClearAll";
import { useStateContext } from "../../context/ContextProvider";
import PageHeader from "../../components/commons/page-header.component";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { sharedCss } from "../../styles/sharedCss";
import NoDataComponent from "../../components/commons/noData.component";
import CommonDropDown from "../../components/commons/dropDown.component";
import FileFolderHeader from "./FileFolderHeader";
import moment from "moment";
import AddFolderButton from "./addFolderButton/AddFolderButton";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";

const sortReducer = (
  sortState,
  { type = "created_at", sortDirection = null },
) => {
  switch (type) {
    case "name":
      return {
        ...sortState,
        name: sortDirection,
        created_at: null,
      };

    case "created_at":
      return {
        ...sortState,
        created_at: sortDirection,
        name: null,
      };

    default:
      return {
        ...sortState,
        name: null,
        created_at: null,
      };
  }
};

const GridSubHeader = ({ subHeaderName = "Folders" }) => {
  const { currentMode } = useStateContext();

  const getHeaderStyle = () => {
    const baseStyle = {
      fontWeight: "bold",
    };

    return {
      ...baseStyle,
      color: currentMode === "Dark" ? "#fff" : "#000",
    };
  };

  return (
    <div className="mx-8">
      <h6 style={getHeaderStyle()}>{subHeaderName}</h6>
    </div>
  );
};

const FileManager = () => {
  const { folderId } = useParams();
  const { folder, childFolders, childFiles } = useFolder(folderId);
  const [sortState, dispatchSort] = useReducer(sortReducer, {
    name: null,
    created_at: null,
  });
  let gridItem = localStorage.getItem("fileGrid");
  const { currentUser } = useAuth();
  const [readOnly, setReadOnly] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortTerm, setSortTerm] = useState("");
  const [machinesData, setMachines] = useState([]);
  const [grid, setGrid] = useState(gridItem === "no" ? true : false);
  const { currentMode, currentColorLight } = useStateContext();
  console.log("FOLDER ID : ", folderId);

  const hasFileGETAccess = useCheckAccess("files", "GET");
  const hasFolderGETAccess = useCheckAccess("folders", "GET");

  const useCustomStyles = makeStyles((theme) => ({
    machineContainer: {
      // padding: "1rem",
      // borderRadius: "10px",
      backgroundColor: theme.palette.custom.backgroundForth,
      boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
    },
    machinesOuterContainer: {
      width: "100%",
    },
    machinesInnerContainer: {
      display: "flex",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    addButton: {
      width: "max-content",
    },
    machinePageContainer: {
      // padding: "1rem",
      // border: "1px solid gainsboro",
    },
  }));

  const changeLocalGrid = () => {
    localStorage.setItem("fileGrid", grid ? "no" : "yes");
  };

  const fetchAllMachines = async () => {
    try {
      const response = await axios.get(`${dbConfig.url}/machines`);
      if (response.data && response.data.data) {
        setMachines(response.data.data);
      } else {
        console.error("No machine data returned from API");
      }
    } catch (error) {
      console.error("Error fetching machines:", error);
    }
  };

  useEffect(() => {
    changeLocalGrid();
    fetchAllMachines();
  }, [grid]);

  const handleMachineSelect = (e) => {
    const machineId = e.target.value;
    setSortTerm(machineId);
    // Optionally persist to local storage
    localStorage.setItem("selectedMachine", machineId);
  };

  // Restore sortTerm on component mount
  useEffect(() => {
    const savedMachine = localStorage.getItem("selectedMachine");
    if (savedMachine) {
      setSortTerm(savedMachine);
    }
  }, []);
  const theme = {
    darkCell: { color: "white", borderBottom: "1px solid white", fontSize: 18 },
    lightCell: { borderBottom: "1px solid #000", fontSize: 18 },
    dark: {
      backgroundColor: "#161C24",
      color: "white",
      border: "1px solid white",
    },
    light: { backgroundColor: currentColorLight, border: "1px solid black" },
  };

  const commonCss = sharedCss();
  const customCss = useCustomStyles();

  const sortChildFilesAndFolders = (prev, next) => {
    // Check if prev or next is undefined
    if (!prev || !next) return 0;

    // Sort by creation date
    if (
      (sortState.created_at || sortState.created_at === null) &&
      sortState.name === null
    ) {
      return moment(next?.created_at).isAfter(moment(prev?.created_at))
        ? 1
        : -1;
    }

    // Sort by creation date in reverse order
    if (sortState.created_at === false && sortState.name === null) {
      return moment(next?.created_at).isBefore(moment(prev?.created_at))
        ? 1
        : -1;
    }

    // Sort by name
    if (sortState.name && sortState.created_at === null) {
      return next?.name?.localeCompare(prev?.name) || 0;
    }

    // Sort by name in reverse order
    if (sortState.name === false && sortState.created_at === null) {
      return prev?.name?.localeCompare(next?.name) || 0;
    }

    return 0;
  };

  const filterChildFilesAndFolders = (data = []) => {
    const isSearchResultAvailable =
      searchTerm === "" ||
      data?.name?.toLowerCase()?.includes(searchTerm?.toLocaleLowerCase());

    const isMachineSelectedMatch = data?.mid === sortTerm || sortTerm === "";

    return isMachineSelectedMatch && isSearchResultAvailable;
  };

  const isSearchResultsFound = useMemo(
    () =>
      childFolders?.some(filterChildFilesAndFolders) ||
      childFiles?.some(filterChildFilesAndFolders),
    [childFiles, childFolders, searchTerm, sortTerm],
  );

  return (
    <Box className={customCss.machinePageContainer}>
      <section className="fileManager">
        <div
          className="alarmsContainer folderContainer"
          style={{ width: "100%", flexDirection: "column", display: "flex" }}
        >
          <header
            // style={
            //   currentMode === "Dark"
            //     ? { backgroundColor: "#161C24", color: "white", padding: 0 }
            //     : { background: currentColorLight, padding: 0 }
            // }
            className={`${commonCss.headingContainer}  ${commonCss.backgroundLight} border-radius-inner`}
            style={{ width: "100%", padding: "0.5rem 1rem" }}
          >
            <div>
              <div>
                <Box>
                  <Typography variant="h4">Library</Typography>
                  <Typography variant="h6">Folders & Documents</Typography>
                </Box>
                {/* <PageHeader title="File Manager" subText="Folders & Documents" /> */}
              </div>
            </div>
            <div style={{ marginRight: "20px" }} className="flex">
              <span className="self-center">
                {!readOnly && (
                  <div className="btnContainer">
                    <TextField
                      fullWidth
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="Search..."
                      style={{ marginRight: "20px", width: "250px" }}
                      size="small"
                    />
                    {/*
                    <FormControl size="small">
                      <InputLabel id="machines">Machine</InputLabel>
                      <Select
                        displayEmpty
                        labelId="machines"
                        onChange={(e) => handleMachineSelect(e)}
                        label="Machine"
                        value={sortTerm}
                        sx={{ width: "250px", marginRight: "20px" }}
                      >
                        {machinesData.map((data) => (
                          <MenuItem key={data._id} value={data._id}>
                            {data.title}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    */}
                    <CommonDropDown
                      dropDownContainerStyle={{
                        width: "250px",
                        marginRight: "20px",
                      }}
                      dropDownLabel={"Machine"}
                      handleChange={handleMachineSelect}
                      menuValue={sortTerm}
                      menuData={[{ _id: "", title: "All" }, ...machinesData]} // Add "All" option
                      menuItemDisplay={"title"}
                      menuItemValue={"_id"}
                    />

                    <Tooltip title="Clear All Filters">
                      <IconButton
                        style={currentMode === "Dark" ? { color: "white" } : {}}
                        onClick={() => setSortTerm("")}
                      >
                        <ClearAllIcon />
                      </IconButton>
                    </Tooltip>

                    <Tooltip
                      title={grid ? "Toggle List View" : "Toggle Grid View"}
                      placement={"bottom"}
                    >
                      <IconButton
                        style={currentMode === "Dark" ? { color: "white" } : {}}
                        onClick={() => {
                          setGrid(!grid);
                          dispatchSort({ type: null });
                          changeLocalGrid();
                        }}
                      >
                        {grid ? <GridViewIcon /> : <FormatListBulletedIcon />}
                      </IconButton>
                    </Tooltip>
                  </div>
                )}
              </span>
            </div>
          </header>

          <div
            className={`${commonCss.generalBackground} border-radius-inner`}
            style={{ width: "100%" }}
          >
            <div
              style={{
                display: "flex",
                marginBottom: "-20px",
                justifyContent: "space-between",
              }}
              className="mt-2 p-4"
            >
              <FolderBreadcrumbs currentFolder={folder} />
              <div className="flex">
                <AddFileButton currentFolder={folder} />
                <AddFolderButton currentFolder={folder} />
              </div>
            </div>
            {hasFolderGETAccess || hasFolderGETAccess ? (
              isSearchResultsFound ? (
                <>
                  <FileFolderHeader
                    grid={grid}
                    dispatchSort={dispatchSort}
                    sortDirection={sortState}
                  />
                  <div
                    className="foldersCardContainer"
                    style={{ marginTop: 0 }}
                  >
                    {childFolders?.length > 0 && (
                      <>
                        {grid &&
                          childFolders?.length > 0 &&
                          childFolders?.some(filterChildFilesAndFolders) && (
                            <GridSubHeader />
                          )}{" "}
                        <div
                          style={
                            grid
                              ? {
                                  display: "flex",
                                  flexDirection: "row",
                                  justifyContent: "flex-start",
                                  alignItems: "center",
                                  flexWrap: "wrap",
                                  paddingLeft: "2%",
                                  borderRadius: "10px",
                                }
                              : {
                                  flexDirection: "column",
                                }
                          }
                        >
                          {hasFolderGETAccess &&
                            (sortTerm != ""
                              ? childFolders
                                  .filter(filterChildFilesAndFolders)
                                  .sort(sortChildFilesAndFolders)
                                  .map((childFolder) =>
                                    childFolder.mid === sortTerm ? (
                                      <FolderCard
                                        grid={grid}
                                        readOnly={readOnly}
                                        folder={childFolder}
                                        key={childFolder.id}
                                      />
                                    ) : null,
                                  )
                              : childFolders
                                  .filter(filterChildFilesAndFolders)
                                  .sort(sortChildFilesAndFolders)
                                  .map((childFolder) => (
                                    <FolderCard
                                      grid={grid}
                                      readOnly={readOnly}
                                      folder={childFolder}
                                      key={childFolder.id}
                                    />
                                  )))}
                        </div>
                      </>
                    )}

                    {childFolders.length > 0 &&
                      childFolders?.some(filterChildFilesAndFolders) &&
                      childFiles.length > 0 &&
                      childFiles?.some(filterChildFilesAndFolders) &&
                      grid && <hr className={"my-2"} />}
                    {hasFileGETAccess && childFiles.length > 0 && (
                      <>
                        {grid &&
                          childFiles?.length > 0 &&
                          childFiles?.some(filterChildFilesAndFolders) && (
                            <GridSubHeader subHeaderName={"Documents"} />
                          )}
                        <div
                          style={
                            grid
                              ? {
                                  display: "flex",
                                  flexDirection: "row",
                                  justifyContent: "flex-start",
                                  alignItems: "center",
                                  flexWrap: "wrap",
                                  paddingLeft: "2%",
                                }
                              : {
                                  display: "flex",
                                  flexDirection: "column",
                                }
                          }
                        >
                          {childFiles
                            ?.filter(filterChildFilesAndFolders)
                            .sort(sortChildFilesAndFolders)
                            .map((childFile) => (
                              <FileCard
                                grid={grid}
                                readOnly={readOnly}
                                file={childFile}
                                key={childFile.id}
                              />
                            ))}
                        </div>
                      </>
                    )}
                  </div>
                </>
              ) : (
                <>
                  {/*
                  <div className="noFilesOrFolders">
                    <strong>
                      {'NO FILES OR FOLDERS FOUND'}
                    </strong>
                  </div>
                  */}
                  <NoDataComponent
                    useAtTable={false}
                    noDataMessage={"NO FILES OR FOLDERS FOUND"}
                  />
                </>
              )
            ) : (
              <NotAccessible />
            )}
          </div>
        </div>

        {/* <FolderPreviewContainer /> */}
      </section>
    </Box>
  );
};

export default FileManager;
