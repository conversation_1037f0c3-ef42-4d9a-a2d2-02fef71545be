import React from "react";
import {
  companies,
  companyId_constant,
  maintenanceReport,
} from "../../constants/data";

import {
  Dialog,
  DialogContent,
  DialogActions,
  DialogTitle,
  Button,
  TextField,
} from "@mui/material";
import "./MaintenanceReport.css";
import { db } from "../../firebase";
import LoggingFunction from "../../components/CFR-Report/LoggingFunction";
import { useStateContext } from "../../context/ContextProvider";

export default function AddComment(props) {
  const { userName, reportName, machineName } = props;
  const [open, setOpen] = React.useState(false);
  const [comment, setComment] = React.useState(props.comment);
  const { currentMode } = useStateContext();

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  // comment add is updating the null comment
  const commentUpdate = () => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(maintenanceReport)
    //     .doc(props.maintenanceReportId)
    //     .collection('stepData').doc(props.id).set({ 'comment': comment }, { merge: true })
    LoggingFunction(
      machineName,
      reportName,
      userName,
      "Maintenance Report",
      `New Comment is added to ${reportName}`,
    );
    handleClose();
  };

  return (
    <>
      <div className="p-3">
        <div
          className="flex items-center justify-between mt-2 carouselInnerHeaderContainer"
          style={{
            backgroundColor: currentMode === "Dark" ? "#161C24" : "#fff",
            color: currentMode === "Dark" ? "#fff" : "#000",
          }}
        >
          <div className="left flex items-center ">
            <div className="text-xl uppercase font-bold">Comment:</div>
            <div className="text-sm ml-4">No comment yet</div>
          </div>

          <div className="right flex items-center justify-center">
            <button
              onClick={handleClickOpen}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Add comment
            </button>
          </div>
        </div>
      </div>
      {/* Dialog */}

      <Dialog open={open} onClose={handleClose} fullWidth="true" maxWidth="sm">
        <DialogTitle>Add Comment</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Comment"
            type="text"
            fullWidth
            variant="standard"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button color="primary" variant="contained" onClick={commentUpdate}>
            Add
          </Button>
          <Button color="inherit" variant="contained" onClick={handleClose}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
