import { memo } from "react";
import { TableRow, TableCell } from "@mui/material";
import { CircularProgress } from "@material-ui/core";
import { useStateContext } from "../../context/ContextProvider";

/**
 * NoData Component
 *
 * A functional React component designed to render a table row with a dynamic cell content.
 * The content displayed can indicate loading status or display a custom "No Data" message based on the passed properties.
 *
 * Props:
 * @param {boolean} dataLoading - Determines if the loading spinner (CircularProgress) should be displayed. Default is false.
 * @param {boolean} animatePulseNoData - If true, applies pulse animation and red text styling to the "No Data" message. Default is true.
 * @param {boolean} useAtTable - Specifies whether the component is to be rendered as a table row or not. Defaults to false.
 * @param {number} cellColSpan - Specifies the column span of the table cell. Default is 6.
 * @param {number} cellRowSpan - Specifies the row span of the table cell. Default is 1.
 * @param {boolean} paddText - If true, adds padding of 24px to the table cell. Default is false.
 * @param {string} padding - Padding style string, needs paddText to be true to be usable.
 * @param {string} noDataMessage - The message to display when there's no data. Default is "No Data".
 * @param {boolean} borderNeeded - If true creates a border around the no data component
 *
 * Returns:
 * @returns {JSX.Element} - A <TableRow> component containing a single <TableCell>.
 * The cell adapts its styling based on the currentMode (Dark/Light theme) and renders either a loading spinner or a "No Data" message.
 *
 * Notes:
 * - The <TableCell> styling dynamically changes based on the theme (Dark or Light).
 * - Conditional rendering is used to display the CircularProgress component when dataLoading is true.
 * - The "No Data" message text styling changes based on animatePulseNoData property.
 */
const NoData = ({
  dataLoading = false,
  animatePulseNoData = false,
  useAtTable = true,
  cellColSpan = 6,
  cellRowSpan = 1,
  paddText = false,
  padding = "24px",
  noDataMessage = "No Data",
  borderNeeded = false,
}) => {
  const { currentMode } = useStateContext();

  if (!useAtTable) {
    return (
      <div className="noFilesOrFolders">
        <strong
          style={currentMode === "Dark" ? { color: "#fff" } : { color: "#000" }}
        >
          {noDataMessage}
        </strong>
      </div>
    );
  }

  return (
    <TableRow
      sx={{
        "&:last-child td, &:last-child th": {
          border: 0,
        },
        border: borderNeeded
          ? `1px solid ${currentMode === "Dark" ? "#fff" : "#000"}`
          : "none",
      }}
    >
      <TableCell
        style={
          currentMode === "Dark"
            ? { color: "white", borderBottom: "none" }
            : { color: "black", borderBottom: "none" }
        }
        align="center"
        colSpan={cellColSpan}
        rowSpan={cellRowSpan}
        sx={{
          textTransform: "capitalize",
          padding: paddText ? padding : "none",
        }}
      >
        {dataLoading && <CircularProgress />}
        {!dataLoading && (
          <div
            className={
              animatePulseNoData
                ? " animate-pulse text-red-500"
                : currentMode === "Dark"
                  ? "text-white"
                  : "text-black"
            }
          >
            {noDataMessage}
          </div>
        )}
      </TableCell>
    </TableRow>
  );
};

export default memo(NoData);
