/* eslint-disable default-case */
import React, { useState, useEffect } from "react";
import { SearchOutlined } from "@mui/icons-material";
import {
  Box,
  FormControl,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import CmsCard from "./CmsCard";
import { Error, Settings, WatchLater } from "@mui/icons-material";
import { db } from "../../firebase";
import {
  companies,
  companyId_constant,
  machines,
  maintenance,
} from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";

import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import SearchIcon from "@mui/icons-material/Search";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  CircularProgress,
} from "@mui/material";
import MaintenanceItem from "../machineData/MaintenanceItem";
import MasterEquipmentScreen from "../cms-infrastructure/screens/master-equipment.screen";
import { dbConfig } from "../../infrastructure/db/db-config";

export default function CmsMain() {
  const { currentMode, currentColorLight } = useStateContext();
  const cycleArr = [
    "Daily",
    "Every 1 Month",
    "Every 3 Months",
    "Every 6 Months",
    "Every 1 Year",
    "Every 2 Years",
    "Every 3 Years",
    "Every 5 Years",
  ];
  const [dataLoading, setDataLoading] = useState(true);
  const [activeMaintenanceGroup, setActiveMaintenanceGroup] = useState([]);
  const [machineAll, setMachineAll] = useState([]);
  const [selectedMachine, setSelectedMachine] = useState("");

  const [searchKeyWordMaintenance, setSearchKeyWordMaintenance] = useState("");
  const [maintenanceCalibrationAll, setMaintenanceCalibrationAll] = useState(
    [],
  ); //
  const [maintenanceCalibration, setMaintenanceCalibration] = useState([]);
  const [maintenanceCalibrationExp, setMaintenanceCalibrationExp] = useState(
    [],
  );
  const [
    maintenanceCalibrationExpNextWeek,
    setMaintenanceCalibrationExpNextWeek,
  ] = useState([]);

  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(maintenance)
    //   .where('type', '==', 0)
    //   //.where('mid' , '==' , 'zVQdHBL3SJfOvZ6vN4ds')
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     console.log("maintenance data:", data)
    //     setMaintenanceCalibrationAll(data);
    //     setMaintenanceCalibration(data);
    //     maintenanceExpireFilter(data);
    //     setActiveMaintenanceGroup(data); // default
    //     setDataLoading(false)
    //   });
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(machines)
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     setMachineAll(data?.reverse());
    //   })
  }, []);

  useEffect(() => {
    const cmsUrl = process.env.REACT_APP_CMS_URL;
    console.log("REACT_APP_CMS_URL:", cmsUrl); // Debug: check value at runtime
    if (
      cmsUrl &&
      typeof cmsUrl === "string" &&
      cmsUrl.trim() !== "" &&
      !cmsUrl.includes("undefined")
    ) {
      window.open(cmsUrl.trim(), "_blank");
    } else {
      window.open("https://localhost:3001/", "_blank");
    }
  }, []);

  //
  //   const alarm = (data) =>
  function maintenanceExpireFilter(maintenanceDataCalibAll) {
    let today = new Date();
    const maintenanceCalibrationExpTemp = [];

    //let temp = null;
    // const alarmIcon = (last_done, days) => {
    //   let lastDoneDate = last_done?.toDate();
    //   let expiredOn = lastDoneDate.setDate(new Date(lastDoneDate).getDate() + days);
    //   let expiredTodate = new Date(expiredOn);
    //   return (
    //     <>
    //       <span data-title={'Expired on :' + expiredTodate.toString().substring(0, 15)}>
    //         <i className="ri-alarm-fill text-red-600  text-xl" ></i>
    //       </span>

    //     </>
    //   )
    // }
    maintenanceDataCalibAll?.map((data) => {
      switch (data.cycle_time) {
        case cycleArr[0]: // daily
          if (
            1 <
            Math.abs(
              Math.ceil(
                (today - data.last_done?.toDate()) / (1000 * 60 * 60 * 24),
              ),
            )
          ) {
            maintenanceCalibrationExpTemp.push(data);
          }
          break;
        case cycleArr[1]: // 1 month
          if (
            1 <
            Math.abs(
              Math.ceil(
                (today - data.last_done?.toDate()) / (1000 * 60 * 60 * 24 * 30),
              ),
            )
          ) {
            maintenanceCalibrationExpTemp.push(data);
          }
          break;
        case cycleArr[2]: // 3 months
          if (
            3 <
            Math.abs(
              Math.ceil(
                (today - data.last_done?.toDate()) / (1000 * 60 * 60 * 24 * 30),
              ),
            )
          ) {
            maintenanceCalibrationExpTemp.push(data);
          }
          break;
        case cycleArr[3]: // 6 months
          if (
            6 <
            Math.abs(
              Math.ceil(
                (today - data.last_done?.toDate()) / (1000 * 60 * 60 * 24 * 30),
              ),
            )
          ) {
            maintenanceCalibrationExpTemp.push(data);
          }
          break;
        case cycleArr[4]: // 1 Year
          if (
            12 <
            Math.abs(
              Math.ceil(
                (today - data.last_done?.toDate()) / (1000 * 60 * 60 * 24 * 30),
              ),
            )
          ) {
            maintenanceCalibrationExpTemp.push(data);
          }
          break;
        case cycleArr[5]: // 2 Years
          if (
            2 <
            Math.abs(
              Math.ceil(
                (today - data.last_done?.toDate()) /
                  (1000 * 60 * 60 * 24 * 30 * 12),
              ),
            )
          ) {
            maintenanceCalibrationExpTemp.push(data);
          }
          break;
        case cycleArr[6]: // 3 Years
          if (
            3 <
            Math.abs(
              Math.ceil(
                (today - data.last_done?.toDate()) /
                  (1000 * 60 * 60 * 24 * 30 * 12),
              ),
            )
          ) {
            maintenanceCalibrationExpTemp.push(data);
          }
          break;
        case cycleArr[7]: // 5 Years
          if (
            5 <
            Math.abs(
              Math.ceil(
                (today - data.last_done?.toDate()) /
                  (1000 * 60 * 60 * 24 * 30 * 12),
              ),
            )
          ) {
            maintenanceCalibrationExpTemp.push(data);
          }
          break;
      }
    });

    setMaintenanceCalibrationExp([...maintenanceCalibrationExpTemp]);
    const maintenanceCalibrationExpNextWeekTemp =
      maintenanceDataCalibAll.filter(
        (value) => !maintenanceCalibrationExpTemp.includes(value),
      );
    setMaintenanceCalibrationExpNextWeek(maintenanceCalibrationExpNextWeekTemp);
  }
  ////
  //
  const handleMachineSelection = (e) => {
    let machineId = e.target.value;
    setSelectedMachine(machineId);
    if (machineId !== "All") {
      let filteredMaintenances = maintenanceCalibrationAll?.filter(
        (data) => data?.mid === machineId,
      );
      setActiveMaintenanceGroup(filteredMaintenances);
      setMaintenanceCalibration(filteredMaintenances);
      maintenanceExpireFilter(filteredMaintenances);
    } else {
      setActiveMaintenanceGroup([...maintenanceCalibrationAll]);
      setMaintenanceCalibration([...maintenanceCalibrationAll]);
      maintenanceExpireFilter(maintenanceCalibrationAll);
    }
  };

  const theme = {
    darkCell: {
      padding: "20px 10px",
      color: "white",
      borderBottom: "1px solid white",
      fontSize: 18,
    },
    lightCell: {
      padding: "20px 10px",
      borderBottom: "1px solid #000",
      fontSize: 18,
    },
    dark: { backgroundColor: "#161C24", color: "white" },
    light: { backgroundColor: currentColorLight },
  };

  return (
    <>
      {/* <section className='alarmsContainer transition delay-700 ease-in-out '>
        <header className='alarmManagerHeader'
          style={
            currentMode === "Dark" ? theme.dark : theme.light
          }>
          <div className='tabsContainer' >
            <div
              style={{
                color: currentMode === "Dark" ? "white" : '',
                fontSize: '20px',
                fontWeight: 'bold'
              }}
            >
              Calibration Management System
            </div>
          </div >
          <div className='flex justify-end w-2/4'>
            <div className='px-1'>
              <Box sx={{ minWidth: 150 }}>
                <FormControl fullWidth size='small'>
                  <InputLabel id="demo-simple-select-label">Machine</InputLabel>
                  <Select
                    labelId="demo-simple-select-label"
                    id="demo-simple-select"
                    value={selectedMachine}
                    label="Machine"
                    onChange={(e) => handleMachineSelection(e)}
                  >
                    <MenuItem value="All">All</MenuItem>
                    {machineAll?.map((data , index) =>
                      <MenuItem key={data+index} value={data.id}>{data?.title}</MenuItem>
                    )}

                  </Select>
                </FormControl>
              </Box>
            </div>

            <div className='px-1'>
              <Box sx={{ minWidth: 150 }}>
                <TextField
                  size='small'
                  label="Maintenace"
                  type="text"
                  value={searchKeyWordMaintenance}
                  onChange={(e) => setSearchKeyWordMaintenance(e.target.value)}
                  placeholder="Maintenace..."
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchOutlined />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
            </div>
            <div className='flex justify-center content-center pl-1'>
              <ButtonBasic buttonTitle='Add' onClick={()=> alert("Beta version...")} />
            </div>
          </div>
        </header >

        <main
          className='alarmManagerMain'
          style={
            currentMode === "Dark" ? theme.dark : theme.light
          }
        >
          <header className='header'>
            <div>
              <h2 style={
                currentMode === "Dark"
                  ? { color: "white" } : {}
              }>Calibration Management System</h2>
            </div>
          </header>

          <div>
            <div className='flex justify-between '>
              <CmsCard
                iconName="Settings"
                cardText={"Number Of Maintenaces: " + maintenanceCalibration?.length}
                percentage={100}
                color="primary"
                onClick={() => setActiveMaintenanceGroup(maintenanceCalibration)}
                active={maintenanceCalibration == activeMaintenanceGroup ? true : false} // this for theme
              />
              <CmsCard
                iconName="Error"
                cardText={"Due Maintenaces: " + maintenanceCalibrationExp?.length}
                percentage={(maintenanceCalibrationExp?.length / maintenanceCalibration?.length) * 100}
                color="error"
                onClick={() => setActiveMaintenanceGroup(maintenanceCalibrationExp)}
                active={maintenanceCalibrationExp == activeMaintenanceGroup ? true : false} // this for theme
              />
              <CmsCard
                iconName="WatchLater"
                cardText={"Upcomming Next Week: " + maintenanceCalibrationExpNextWeek?.length}
                percentage={(maintenanceCalibrationExpNextWeek?.length / maintenanceCalibration?.length) * 100}
                color="success"
                onClick={() => setActiveMaintenanceGroup(maintenanceCalibrationExpNextWeek)}
                active={maintenanceCalibrationExpNextWeek == activeMaintenanceGroup ? true : false} // this for theme
              />
            </div>
          </div>


          <div className="liveDataContainer py-4">
            <TableContainer style={currentMode === 'Dark' ? { backgroundColor: '#212B36', color: 'white', border: '1px solid white' } : { border: '1px solid black' }} component={Paper} className="table">
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow>

                    <TableCell style={currentMode === 'Dark' ? { color: 'white' } : { color: 'black' }} align="left">Sensor Name</TableCell>

                    <TableCell style={currentMode === 'Dark' ? { color: 'white' } : { color: 'black' }} align="left">Comment</TableCell>

                    <TableCell style={currentMode === 'Dark' ? { color: 'white' } : { color: 'black' }} align="left">Period / Cycle</TableCell>

                    <TableCell style={currentMode === 'Dark' ? { color: 'white' } : { color: 'black' }} align="left">Last Done</TableCell>
                    <TableCell style={currentMode === 'Dark' ? { color: 'white' } : { color: 'black' }} align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {activeMaintenanceGroup.length > 0 ? (
                    activeMaintenanceGroup
                      .filter((data) => {
                      // if (data.type === defaultType) {

                      //   if ((searchTerm == "" && hastagsForFilter?.length == 0) ||
                      //     (searchTerm == "#" && hastagsForFilter?.length == 0) ||
                      //     (searchTerm?.startsWith("#") && hastagsForFilter?.length == 0)
                      //   ) {
                      //     return data
                      //   }
                      //       //
                      //       else if (searchTerm.startsWith("#") || hastagsForFilter?.length > 0) {
                      //         let temp = hastagsForFilter.filter((filterData) => filterData.doc_id == data.id);
                      //         if (temp?.length > 0) {
                      //           return data
                      //         }
                      //       }
                            if (data.title.toLowerCase().includes(searchKeyWordMaintenance.toLocaleLowerCase())) {
                              return data
                            }
                      //     }
                      })
                      .map((data, index) => (
                        <>
                          <MaintenanceItem
                            key={index}
                            data={data}
                            machineName={'machineName'}
                            defaultType={0}
                          />
                        </>
                      ))
                  ) : (
                    <TableRow
                      sx={{
                        '&:last-child td, &:last-child th': {
                          border: 0,
                        },
                      }}
                    >
                      <TableCell
                        style={{ borderBottom: 'none' }}
                        align="center"
                        colSpan={4}
                      >
                        {dataLoading && <CircularProgress />}
                        {!dataLoading && <>No Data</>}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>

        </main>

      </section> */}
      <MasterEquipmentScreen />
    </>
  );
}
