import React, { useState, useEffect } from "react";
import "./ModelPage.css";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
} from "@mui/material";
import { useStateContext } from "../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { themeColors } from "../../infrastructure/theme";
import { toastMessageWarning } from "../../tools/toast";

export default function Modal({ visible, onSubmit, onCancel, setMarkers }) {
  const [text, setText] = useState("");
  const [color, setColor] = useState("#000000");
  const [sensorData, setSensorData] = useState([]);
  const { midForModels, currentMode } = useStateContext();
  const [selectedSensor, setSelectedSensor] = useState(null);
  const [sensorValue, setSensorValue] = useState(""); // Stores the sensor ID (tag)
  const menuItemTheme = {
    backgroundColor:
      currentMode === "Dark"
        ? themeColors.dark.primary
        : themeColors.light.primary,
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(
          `${dbConfig.url}/livedata/getFromMachine/${midForModels}`,
        );
        setSensorData(response.data.data);
      } catch (error) {
        console.error("Error fetching sensor data:", error);
      }
    };

    fetchData();
  }, [midForModels]);

  const handleSensorChange = (e) => {
    const selectedSensorId = e.target.value;
    const selectedSensorObj = sensorData?.find(
      (sensor) => sensor?.tag === selectedSensorId,
    );
    setSensorValue(selectedSensorId);
    setSelectedSensor(selectedSensorObj);
  };

  const handleCancel = () => {
    setText("");
    setColor("#000000");
    setSensorValue("");
    setSelectedSensor(null);
    onCancel();
  };

  const handleSubmit = () => {
    if (!selectedSensor) {
      toastMessageWarning({ message: "Please select a sensor" });
      return;
    }

    onSubmit(text, color, selectedSensor);
    handleCancel();
  };

  return (
    <Dialog open={visible} fullWidth onClose={handleCancel}>
      <DialogTitle>Add Annotation</DialogTitle>
      <DialogContent>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "1rem",
            padding: "1rem",
          }}
        >
          <TextField
            label="Annotation Text"
            fullWidth
            value={text}
            onChange={(e) => setText(e.target.value)}
            required
          />

          <FormControl fullWidth>
            <InputLabel>Sensor</InputLabel>
            <Select
              required
              label="Sensor"
              value={sensorValue}
              onChange={handleSensorChange}
            >
              {sensorData?.map((data) => (
                <MenuItem key={data.tag} value={data.tag} sx={menuItemTheme}>
                  {data.tag}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {selectedSensor && (
            <div style={{ marginTop: "1rem" }}>
              <div>
                <span className="font-bold">Sensor Tag: </span>
                {selectedSensor.tag}
              </div>
              <div>
                <span className="font-bold">Sensor Value: </span>
                {selectedSensor.value ?? "N/A"}
              </div>
            </div>
          )}

          <TextField
            label="Color"
            type="color"
            fullWidth
            value={color}
            onChange={(e) => setColor(e.target.value)}
          />
        </div>
      </DialogContent>
      <DialogActions>
        <Button variant="contained" color="error" onClick={handleCancel}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={!text.trim() || !sensorValue}
        >
          Submit
        </Button>
      </DialogActions>
    </Dialog>
  );
}
