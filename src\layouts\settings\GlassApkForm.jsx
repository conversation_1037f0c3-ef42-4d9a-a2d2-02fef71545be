import React, { useState } from "react";
import { alpha, styled } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import InputLabel from "@mui/material/InputLabel";
import { DropzoneArea } from "material-ui-dropzone";
import { db } from "../../firebase";
import { useStorage } from "../../hooks/useStorage";
import { toastMessageSuccess, toastMessageWarning } from "../../tools/toast";
import { useStateContext } from "../../context/ContextProvider";
import { sharedCss } from "../../styles/sharedCss";
import { Button } from "@mui/material";

const BootstrapInput = styled(InputBase)(({ theme }) => ({
  "label + &": {
    marginTop: theme.spacing(0),
  },
  "& .MuiInputBase-input": {
    borderRadius: 4,
    position: "relative",
    backgroundColor: theme.palette.mode === "light" ? "#fcfcfb" : "#2b2b2b",
    border: "1px solid #ced4da",
    fontSize: 14,
    padding: "10px 12px",
    transition: theme.transitions.create([
      "border-color",
      "background-color",
      "box-shadow",
    ]),
    "&:focus": {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main,
    },
  },
}));

// const mobileDb = db.collection('apks').doc('glass')

const GlassApkForm = () => {
  const [version, setVersion] = useState("");
  const [about, setAbout] = useState("");
  const [file, setFile] = useState(null);
  const { currentMode } = useStateContext();
  const commonCss = sharedCss();

  function handleSubmitDetails(event) {
    event.preventDefault();
    if (url) {
      const data = { version, about, apk_url: url };
      // mobileDb.update(data).then(() => {
      //   toastMessageSuccess({message: "Updated APK Successfully !"})
      // })
    } else {
      toastMessageWarning("Please wait while APK is being updated ...");
    }
  }

  const uploadApk = (e) => {
    let selectedFile = e[0];
    setFile(selectedFile);
  };

  const { progress, url } = useStorage(file);
  const style = {
    backgroundColor: currentMode === "Dark" ? "#2b2b2b" : "",
  };

  const color = {
    color: currentMode === "Dark" ? "#fff" : "",
  };

  return (
    <div
      className={`glassFormContainer ${commonCss.backgroundLight}`}
      style={{ boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)" }}
    >
      <div className="title" style={color}>
        <h3>Glass Apk Upload</h3>
      </div>
      <div className="desc" style={color}>
        <p>Mandatory information</p>
      </div>

      <form onSubmit={handleSubmitDetails} className="formInner">
        <div className="labelFields">
          <InputLabel shrink htmlFor="verseionNo" style={color}>
            Version Number
          </InputLabel>
          <BootstrapInput
            value={version}
            onChange={(e) => setVersion(e.target.value)}
            id="verseionNo"
            placeholder="Your Version Number"
          />
        </div>

        <div className="labelFields">
          <InputLabel shrink htmlFor="about" style={color}>
            About
          </InputLabel>
          <BootstrapInput
            value={about}
            onChange={(e) => setAbout(e.target.value)}
            id="about"
            placeholder="you@apk changes"
          />
        </div>

        <div className="labelFields">
          <InputLabel shrink htmlFor="apk" style={color}>
            Upload APK
          </InputLabel>
          <DropzoneArea
            onChange={(loadedFiles) => uploadApk(loadedFiles)}
            id="apk"
          />
        </div>

        <div className="flex opacity-20 justify-center">
          <h1 className="text-3xl font-bold" style={color}>
            {progress} % UPLOADED
          </h1>
        </div>

        <div className="buttons">
          <Button type="submit" variant="contained" color="primary">
            Submit
          </Button>
        </div>
      </form>
    </div>
  );
};

export default GlassApkForm;
