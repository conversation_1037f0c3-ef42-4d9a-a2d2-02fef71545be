.header {
  width: 100%;
  // height: 5rem;
  border-radius: 10px;
  // padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #344767;
  // margin-bottom: 1rem;
  // position: sticky;
  // top: 0;
  // z-index: 999;

  .headerLeft,
  .headerRight {
    display: flex;
    align-items: center;
  }

  .headerLeft {
    display: flex;
    align-items: center;
    .pageName {
      font-size: 1.2rem;
      font-weight: 500;
      text-transform: capitalize;
    }
    .menu {
      padding-right: 1.5rem;
      cursor: pointer;
      font-size: 1.2rem;
    }
    .themeSwitcher {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-right: 1.5rem;
      .themeBtn {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 4rem;
        height: 1.8rem;
        margin: 0 auto;
        border-radius: 30px;
        font-size: 0.5rem;
        padding: 0.5rem;
        overflow: hidden;
        cursor: pointer;
        svg {
          width: 1rem;
          height: auto;
          transition: all 0.3s linear;
        }
      }
    }

    .darkThemeBtn {
      background-image: linear-gradient(
        to right top,
        #393977,
        #2e3069,
        #23275b,
        #191e4d,
        #0e1640
      );
      border: 1px solid #6b8096;
      svg {
        &:first-child {
          transform: translateY(-100px);
        }
        &:nth-child(2) {
          transform: translateY(0);
        }
      }
    }

    .lightThemeBtn {
      background-image: linear-gradient(
        to left top,
        #f0f2f4,
        #cfe4f4,
        #a8d8f3,
        #76ccef,
        #17c1e8
      );
      border: 1px solid #fff;
      svg {
        &:first-child {
          transform: translateY(0);
        }
        &:nth-child(2) {
          transform: translateY(100px);
        }
      }
    }
  }

  .headerRight {
    .search_bar {
      width: 200px;
      height: 30px;
      background-color: #fafafa;
      border: 1px solid #dbdbdb;
      border-radius: 4px;
      text-align: center;
      outline: none;
    }

    .search_bar::placeholder {
      color: #808080;
    }

    .signin {
      display: flex;
      align-items: center;
      .text {
        font-size: 0.8rem;
        font-weight: 500;
        padding-left: 0.3rem;
      }
    }

    .signin,
    .settings,
    .notification {
      padding-left: 0.9rem;
      cursor: pointer;
    }

    i,
    .signin {
      &:hover {
        opacity: 0.9;
      }
    }
    .notification {
      position: relative;
      .notificationContainerHide {
        display: none;
        position: absolute;
      }
      .notificationContainer {
        z-index: 999;
        width: 300px;
        position: absolute;
        display: block;
        transition:
          block 290ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
          transform 193ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
        top: 2rem;
        right: -9.8rem;
      }
    }
  }
}
