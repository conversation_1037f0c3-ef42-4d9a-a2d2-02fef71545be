import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  TextField,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { useCheckAccess } from "../../utils/useCheckAccess";

const OeeForm = ({ refreshThem, oeeData, mid }) => {
  const [formData, setFormData] = useState({
    status: false,
    value: "",
    def_value: "",
    runTime: 0,
    idealCycleTime: 0,
    plannedProductionTime: 0,
    totalCount: 0,
    stopTime: 0,
    goodCount: 0,
    downtimeReasons: [
      {
        reason: "",
        description: "",
        criticality: "",
        duration: 0,
      },
    ],
    shiftInfo: {
      shiftNumber: 0,
      shiftStart: "",
      shiftEnd: "",
    },
    operatorInfo: {
      operatorId: "",
      operatorName: "",
    },
    environmentalFactors: {
      temperature: 0,
      humidity: 0,
      otherFactors: "",
    },
    maintenanceRecords: [
      {
        maintenanceDate: "",
        maintenanceType: "",
        comments: "",
      },
    ],
    performanceTargets: {
      targetCycleTime: 0,
      targetOutput: 0,
    },
    changeoverTimes: 0,
    qualityMetrics: "",
    energyConsumption: 0,
    historicalDataComparison: false,
    realTimeDataFlag: false,
    annotations: "",
  });

  const hasOeeReportsPOSTAccess = useCheckAccess("oeeReports", "POST");
  const hasOeePUTAccess = useCheckAccess("oee", "PUT");

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;

    setFormData({
      ...formData,
      [name]: checked,
    });
  };

  const handleNestedChange = (e, category) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [category]: {
        ...formData[category],
        [name]: value,
      },
    });
  };

  const handleDowntimeReasonChange = (e, index) => {
    const { name, value } = e.target;
    const updatedDowntimeReasons = [...formData?.downtimeReasons];
    updatedDowntimeReasons[index][name] = value;
    setFormData({
      ...formData,
      downtimeReasons: updatedDowntimeReasons,
    });
  };

  const addDowntimeReason = () => {
    setFormData({
      ...formData,
      downtimeReasons: [
        ...formData?.downtimeReasons,
        {
          reason: "",
          description: "",
          criticality: "",
          duration: 0,
        },
      ],
    });
  };

  const removeDowntimeReason = (index) => {
    const updatedDowntimeReasons = [...formData?.downtimeReasons];
    updatedDowntimeReasons.splice(index, 1);
    setFormData({
      ...formData,
      downtimeReasons: updatedDowntimeReasons,
    });
  };

  const handleMaintenanceRecordChange = (e, index) => {
    const { name, value } = e.target;
    const updatedMaintenanceRecords = [...formData?.maintenanceRecords];
    updatedMaintenanceRecords[index][name] = value;
    setFormData({
      ...formData,
      maintenanceRecords: updatedMaintenanceRecords,
    });
  };

  const addMaintenanceRecord = () => {
    setFormData({
      ...formData,
      maintenanceRecords: [
        ...formData?.maintenanceRecords,
        {
          maintenanceDate: "",
          maintenanceType: "",
          comments: "",
        },
      ],
    });
  };

  const removeMaintenanceRecord = (index) => {
    const updatedMaintenanceRecords = [...formData?.maintenanceRecords];
    updatedMaintenanceRecords.splice(index, 1);
    setFormData({
      ...formData,
      maintenanceRecords: updatedMaintenanceRecords,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log(formData);
    // Validation: Check if all required fields are filled
    if (!validateFormData(formData)) {
      alert("Please fill in all required fields.");
      return;
    }

    // Convert numeric fields to numbers
    const numericFields = [
      "runTime",
      "idealCycleTime",
      "plannedProductionTime",
      "totalCount",
      "stopTime",
      "goodCount",
      "changeoverTimes",
      "energyConsumption",
      "targetCycleTime",
      "targetOutput",
    ];
    numericFields.forEach((field) => {
      formData[field] = parseFloat(formData[field]);
    });

    // Ensure downtime reasons and maintenance records have valid structures
    formData?.downtimeReasons.forEach((reason) => {
      reason.duration = parseFloat(reason.duration); // Convert duration to a number
    });

    try {
      // Handle form submission based on whether oeeData exists
      if (oeeData) {
        await axios.put(`${dbConfig.url}/oee/${oeeData?._id}`, formData);
        refreshThem();
      } else {
        const response = await axios.post(`${dbConfig.url}/oee`, {
          ...formData,
          mid,
        });
        // Assuming the response contains the newly created document with an _id field
        if (response.data?._id) {
          toastMessageSuccess({ message: "Updated OEE Successfully!" });
        } else {
          toastMessage({ message: "Failed to create OEE record." });
        }
      }

      // Calculate additional values
      const {
        goodCount,
        idealCycleTime,
        plannedProductionTime,
        stopTime,
        totalCount,
      } = formData;
      const OEE = (
        (goodCount * idealCycleTime) /
        plannedProductionTime
      ).toFixed(2);
      const Availability = (
        (formData?.runTime / plannedProductionTime) *
        100
      ).toFixed(2);
      const Performance = (
        (idealCycleTime * totalCount) /
        formData?.runTime
      ).toFixed(2);
      const Quality = ((goodCount / totalCount) * 100).toFixed(2);

      alert(`Form submitted successfully!
        OEE: ${OEE}%
        Availability: ${Availability}%
        Run Time: ${formData?.runTime}
        Performance: ${Performance}%
        Quality: ${Quality}%
      `);
    } catch (error) {
      console.error("Error during form submission:", error);
      toastMessage({ message: "An error occurred while processing the form." });
    }
  };
  const handleSubmitReports = async (e) => {
    e.preventDefault();
    console.log(formData);
    // Validation: Check if all required fields are filled
    if (!validateFormData(formData)) {
      alert("Please fill in all required fields.");
      return;
    }

    // Convert numeric fields to numbers
    const numericFields = [
      "runTime",
      "idealCycleTime",
      "plannedProductionTime",
      "totalCount",
      "stopTime",
      "goodCount",
      "changeoverTimes",
      "energyConsumption",
      "targetCycleTime",
      "targetOutput",
    ];
    numericFields.forEach((field) => {
      formData[field] = parseFloat(formData[field]);
    });

    // Ensure downtime reasons and maintenance records have valid structures
    formData?.downtimeReasons.forEach((reason) => {
      reason.duration = parseFloat(reason.duration); // Convert duration to a number
    });

    try {
      // Handle form submission based on whether oeeData exists

      const response = await axios.post(`${dbConfig.url}/oeeReports`, {
        ...formData,
        mid,
      });
      // Assuming the response contains the newly created document with an _id field
      if (response.data?._id) {
        toastMessageSuccess({ message: "An OEE Report has been generated" });
      } else {
        toastMessage({ message: "Failed to create OEE Report record." });
      }

      // Calculate additional values
      const {
        goodCount,
        idealCycleTime,
        plannedProductionTime,
        stopTime,
        totalCount,
      } = formData;
      const OEE = (
        (goodCount * idealCycleTime) /
        plannedProductionTime
      ).toFixed(2);
      const Availability = (
        (formData?.runTime / plannedProductionTime) *
        100
      ).toFixed(2);
      const Performance = (
        (idealCycleTime * totalCount) /
        formData?.runTime
      ).toFixed(2);
      const Quality = ((goodCount / totalCount) * 100).toFixed(2);
    } catch (error) {
      console.error("Error during form submission:", error);
      toastMessage({ message: "An error occurred while processing the form." });
    }
  };

  // Validation function (customize this based on your field requirements)
  const validateFormData = (data) => {
    // Example: Check if required fields are filled
    return (
      data?.status &&
      data?.value &&
      data?.runTime !== null &&
      data?.idealCycleTime !== null &&
      data?.plannedProductionTime !== null &&
      data?.totalCount !== null
    );
  };

  useEffect(() => {
    if (oeeData) {
      setFormData(oeeData);
    }
  }, [oeeData]);

  return (
    <Container maxWidth="xl">
      <Paper elevation={3} sx={{ padding: 3 }}>
        <Typography variant="h5" align="left" gutterBottom>
          OEE Details
        </Typography>
        <form onSubmit={handleSubmit}>
          {/* Basic Information Section */}
          <Box mb={3}>
            <Typography gutterBottom variant="h6">
              Basic Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Status</InputLabel>
                  <Select
                    label="Status"
                    name="status"
                    value={formData?.status}
                    onChange={handleChange}
                  >
                    <MenuItem value={true}>Active</MenuItem>
                    <MenuItem value={false}>Inactive</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Default Value"
                  name="def_value"
                  variant="outlined"
                  value={formData?.def_value}
                  onChange={handleChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Run Time (minutes)"
                  name="runTime"
                  type="number"
                  variant="outlined"
                  value={formData?.runTime}
                  onChange={handleChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Good Count"
                  name="goodCount"
                  type="number"
                  variant="outlined"
                  value={formData?.goodCount}
                  onChange={handleChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Ideal Cycle Time (minutes)"
                  name="idealCycleTime"
                  type="number"
                  variant="outlined"
                  value={formData?.idealCycleTime}
                  onChange={handleChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Planned Production Time (minutes)"
                  name="plannedProductionTime"
                  type="number"
                  variant="outlined"
                  value={formData?.plannedProductionTime}
                  onChange={handleChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Stop Time (minutes)"
                  name="stopTime"
                  type="number"
                  variant="outlined"
                  value={formData?.stopTime}
                  onChange={handleChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Total Count"
                  name="totalCount"
                  type="number"
                  variant="outlined"
                  value={formData?.totalCount}
                  onChange={handleChange}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Shift Information Section */}
          <Box mb={3}>
            <Typography gutterBottom variant="h6">
              Shift Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Shift Number"
                  name="shiftNumber"
                  type="number"
                  variant="outlined"
                  value={formData?.shiftInfo.shiftNumber}
                  onChange={(e) => handleNestedChange(e, "shiftInfo")}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Shift Start"
                  name="shiftStart"
                  type="datetime-local"
                  variant="outlined"
                  value={formData?.shiftInfo.shiftStart}
                  onChange={(e) => handleNestedChange(e, "shiftInfo")}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Shift End"
                  name="shiftEnd"
                  type="datetime-local"
                  variant="outlined"
                  value={formData?.shiftInfo.shiftEnd}
                  onChange={(e) => handleNestedChange(e, "shiftInfo")}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Operator Information Section */}
          <Box mb={3}>
            <Typography gutterBottom variant="h6">
              Operator Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Operator ID"
                  name="operatorId"
                  type="number"
                  variant="outlined"
                  value={formData?.operatorInfo.operatorId}
                  onChange={(e) => handleNestedChange(e, "operatorInfo")}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Operator Name"
                  name="operatorName"
                  variant="outlined"
                  value={formData?.operatorInfo.operatorName}
                  onChange={(e) => handleNestedChange(e, "operatorInfo")}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Environmental Factors Section */}
          <Box mb={3}>
            <Typography gutterBottom variant="h6">
              Environmental Factors
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Temperature"
                  name="temperature"
                  type="number"
                  variant="outlined"
                  value={formData?.environmentalFactors.temperature}
                  onChange={(e) =>
                    handleNestedChange(e, "environmentalFactors")
                  }
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Humidity"
                  name="humidity"
                  type="number"
                  variant="outlined"
                  value={formData?.environmentalFactors.humidity}
                  onChange={(e) =>
                    handleNestedChange(e, "environmentalFactors")
                  }
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Other Factors"
                  name="otherFactors"
                  variant="outlined"
                  value={formData?.environmentalFactors.otherFactors}
                  onChange={(e) =>
                    handleNestedChange(e, "environmentalFactors")
                  }
                />
              </Grid>
            </Grid>
          </Box>

          {/* Performance Targets Section */}
          <Box mb={3}>
            <Typography gutterBottom variant="h6">
              Performance Targets
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Target Cycle Time (minutes)"
                  name="targetCycleTime"
                  type="number"
                  variant="outlined"
                  value={formData?.performanceTargets.targetCycleTime}
                  onChange={(e) => handleNestedChange(e, "performanceTargets")}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Target Output"
                  name="targetOutput"
                  type="number"
                  variant="outlined"
                  value={formData?.performanceTargets.targetOutput}
                  onChange={(e) => handleNestedChange(e, "performanceTargets")}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Changeover Times"
                  name="changeoverTimes"
                  type="number"
                  variant="outlined"
                  value={formData?.changeoverTimes}
                  onChange={handleChange}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Quality Metrics Section */}
          <Box mb={3}>
            <Typography gutterBottom variant="h6">
              Quality Metrics
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Quality Metrics"
                  name="qualityMetrics"
                  variant="outlined"
                  value={formData?.qualityMetrics}
                  onChange={handleChange}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Energy Consumption Section */}
          <Box mb={3}>
            <Typography gutterBottom variant="h6">
              Energy Consumption
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Energy Consumption (kW)"
                  name="energyConsumption"
                  type="number"
                  variant="outlined"
                  value={formData?.energyConsumption}
                  onChange={handleChange}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Downtime Reasons Section */}
          <Box mb={3}>
            <Typography gutterBottom variant="h6">
              Downtime Reasons
            </Typography>
            {formData?.downtimeReasons.map((reason, index) => (
              <Box key={index} mb={3}>
                <Typography variant="subtitle1">Reason #{index + 1}</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={3}>
                    <TextField
                      fullWidth
                      label="Reason"
                      name="reason"
                      variant="outlined"
                      value={reason.reason}
                      onChange={(e) => handleDowntimeReasonChange(e, index)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <TextField
                      fullWidth
                      label="Description"
                      name="description"
                      variant="outlined"
                      value={reason.description}
                      onChange={(e) => handleDowntimeReasonChange(e, index)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <InputLabel>Criticality</InputLabel>
                      <Select
                        label="Criticality"
                        name="criticality"
                        value={reason.criticality}
                        onChange={(e) => handleDowntimeReasonChange(e, index)}
                      >
                        <MenuItem value="minor">Minor</MenuItem>
                        <MenuItem value="major">Major</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <TextField
                      fullWidth
                      label="Duration (minutes)"
                      name="duration"
                      type="number"
                      variant="outlined"
                      value={reason.duration}
                      onChange={(e) => handleDowntimeReasonChange(e, index)}
                    />
                  </Grid>
                </Grid>
                <Button
                  variant="outlined"
                  color="error"
                  onClick={() => removeDowntimeReason(index)}
                >
                  Remove Reason
                </Button>
              </Box>
            ))}
            <Button
              variant="outlined"
              color="primary"
              onClick={addDowntimeReason}
            >
              Add Downtime Reason
            </Button>
          </Box>

          {/* Maintenance Records Section */}
          <Box mb={3}>
            <Typography gutterBottom variant="h6">
              Maintenance Records
            </Typography>
            {formData?.maintenanceRecords.map((record, index) => (
              <Box key={index} mb={3}>
                <Typography variant="subtitle1">Record #{index + 1}</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="Maintenance Date"
                      name="maintenanceDate"
                      type="datetime-local"
                      variant="outlined"
                      value={record.maintenanceDate}
                      onChange={(e) => handleMaintenanceRecordChange(e, index)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="Maintenance Type"
                      name="maintenanceType"
                      variant="outlined"
                      value={record.maintenanceType}
                      onChange={(e) => handleMaintenanceRecordChange(e, index)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="Comments"
                      name="comments"
                      variant="outlined"
                      value={record.comments}
                      onChange={(e) => handleMaintenanceRecordChange(e, index)}
                    />
                  </Grid>
                </Grid>
                <Button
                  variant="outlined"
                  color="error"
                  onClick={() => removeMaintenanceRecord(index)}
                >
                  Remove Record
                </Button>
              </Box>
            ))}
            <Button
              variant="outlined"
              color="primary"
              onClick={addMaintenanceRecord}
            >
              Add Maintenance Record
            </Button>
          </Box>

          {/* Additional Information Section */}
          <Box mb={3}>
            <Typography gutterBottom variant="h6">
              Additional Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formData?.historicalDataComparison}
                      onChange={handleCheckboxChange}
                      name="historicalDataComparison"
                      color="primary"
                    />
                  }
                  label="Historical Data Comparison"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formData?.realTimeDataFlag}
                      onChange={handleCheckboxChange}
                      name="realTimeDataFlag"
                      color="primary"
                    />
                  }
                  label="Real-time Data Flag"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Annotations"
                  name="annotations"
                  variant="outlined"
                  multiline
                  rows={4}
                  value={formData?.annotations}
                  onChange={handleChange}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Submit Button */}
          <Box mt={3} display="flex" justifyContent="flex-end" gap={2}>
            <Button
              variant="outlined"
              onClick={handleSubmitReports}
              color="inherit"
              type="submit"
              disabled={!hasOeeReportsPOSTAccess}
            >
              Create Report
            </Button>{" "}
            <Button
              variant="contained"
              color="primary"
              type="submit"
              disabled={!hasOeePUTAccess}
            >
              Submit
            </Button>
          </Box>
        </form>
      </Paper>
    </Container>
  );
};

export default OeeForm;
