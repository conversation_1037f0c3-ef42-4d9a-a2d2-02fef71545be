// AxiosErrorHandler.js
import React, { useEffect, useContext, useState, createContext } from "react";
import axios from "axios";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useLocation } from "react-router-dom";
import MaintenancePopup from "../features/env/maintenance";
import PropTypes from "prop-types";

// Constants
const ERR_NETWORK = "ERR_NETWORK";
const OFFLINE_MESSAGE = "You are offline";
const MAINTENANCE_MESSAGE = "The application is in maintenance mode.";
const TOAST_POSITION = toast.POSITION.TOP_RIGHT;
const EXEMPT_ROUTES = ["/login", "/ENV"];

const MaintenanceContext = createContext();

export const MaintenanceProvider = ({ children }) => {
  const [error, setError] = useState(null);
  return (
    <MaintenanceContext.Provider value={{ error, setError }}>
      {children}
    </MaintenanceContext.Provider>
  );
};
MaintenanceProvider.propTypes = {
  children: PropTypes.node,
};
export const useMaintenance = () => useContext(MaintenanceContext);

const AxiosErrorHandler = ({ children }) => {
  // eslint-disable-next-line no-unused-vars
  const { setError, error } = useMaintenance();
  const location = useLocation();

  useEffect(() => {
    const responseInterceptor = axios.interceptors.response.use(
      (response) => {
        localStorage.removeItem("isOffline");
        return response;
      },
      (error) => {
        if (error.code === ERR_NETWORK && !localStorage.getItem("isOffline")) {
          localStorage.setItem("isOffline", "true");
          toast.warning(OFFLINE_MESSAGE, { position: TOAST_POSITION });
        } else if (
          error.response &&
          error.response.status === 403 &&
          error.response.data.message.includes("maintenance")
        ) {
          if (!EXEMPT_ROUTES.includes(location.pathname)) {
            setError(error.response.data.message || MAINTENANCE_MESSAGE);
          }
        } else {
          localStorage.setItem("isOffline", "false");
        }
        return Promise.reject(error);
      },
    );

    return () => {
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, [setError, location.pathname]);

  return (
    <>
      {children}
      {!EXEMPT_ROUTES.includes(location.pathname) && <MaintenancePopup />}
      <ToastContainer />
    </>
  );
};
AxiosErrorHandler.propTypes = {
  children: PropTypes.node,
};
export default AxiosErrorHandler;
