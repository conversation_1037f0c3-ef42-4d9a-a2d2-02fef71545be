.modal {
  form {
    .inputBox {
      display: flex;
      flex-direction: column;
      label {
        font-size: 1rem;
        color: #344767;
        padding-bottom: 1rem;
      }
      input {
        font-size: 0.8rem;
        color: #344767;
        padding: 9px 12px;
        border-radius: 8px;
        border: 1px solid #ced4da;
        &:focus {
          outline: none;
        }
      }
    }
    .buttons {
      margin-top: 2.5rem;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .btn {
        padding: 0.68rem 1.6rem;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
        outline: none;
        cursor: pointer;
        border-radius: 8px;
        border: none;
        box-shadow:
          rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
          rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
        &:hover {
          opacity: 0.85;
        }
      }
      .closeBtn {
        background-color: #e9ecef;
        color: #344767;
      }
      .createBtn {
        background-image: linear-gradient(
          310deg,
          rgb(33, 82, 255),
          rgb(33, 212, 253)
        );
        color: #fff;
        margin-left: 1rem;
      }
    }
  }
}
