import React from "react";
import { createRoot } from "react-dom/client"; // React 18
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import App from "./App";
import { APIInterceptor } from "./interceptor";
import { ContextProvider } from "./context/ContextProvider"; // Import ContextProvider
import { AuthProvider } from "./hooks/AuthProvider"; // Import AuthProvider
// Initialize the API interceptor
APIInterceptor();

const container = document.getElementById("root");
const root = createRoot(container);

root.render(
  <ContextProvider>
    <BrowserRouter>
      <AuthProvider>
        <App />
      </AuthProvider>
    </BrowserRouter>
  </ContextProvider>,
);
