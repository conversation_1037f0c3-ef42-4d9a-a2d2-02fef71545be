import React from "react";
import { Link } from "react-router-dom";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";

const AdminHeaderContainer = () => {
  const { currentColorLight, currentMode } = useStateContext();
  return (
    <div
      className="adminPageInfoContainer"
      style={
        currentMode === "Dark"
          ? { backgroundColor: "#161C24", color: "white" }
          : { backgroundColor: currentColorLight }
      }
    >
      <div className="info">
        <h3>Admins List</h3>
        <p>Information about all the Admins.</p>
      </div>
      <div className="btn">
        <Link to="/add-admin">
          <ButtonBasic buttonTitle="Add new Admin" />
        </Link>
      </div>
    </div>
  );
};

export default AdminHeaderContainer;
