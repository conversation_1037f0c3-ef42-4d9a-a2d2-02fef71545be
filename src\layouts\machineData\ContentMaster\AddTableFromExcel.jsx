// Note : Now Not in use (It is the first implimentation) . Now the updated one is in TableMain.js
import React, { useState } from "react";
import readXlsxFile from "read-excel-file";
import { ButtonBasic } from "../../../components/buttons/Buttons";
import { companies, companyId_constant } from "../../../constants/data";
import { db } from "../../../firebase";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../tools/toast";

export default function AddTableFromExcel({
  type,
  handleClose,
  machineName,
  fatDataDocId,
}) {
  const [fileExcel, setFileExcel] = useState();
  const [excelData, setExcelData] = useState([]);
  const [tableData, setTableData] = useState([]); // formated data
  // excel file
  const handleFileChange = (e) => {
    setFileExcel(e.target.files[0]);
    console.log("one:", e.target.files[0]);

    readXlsxFile(e.target.files[0]).then((rows) => {
      console.log("two:", rows);
      setExcelData(rows);

      const temp = [];
      let head = rows.slice(0, 1);
      //console.log("addTableFrromExcell: head:", head[0][0]);
      let body = rows.slice(1);
      let promise = new Promise((resolve, regect) => {
        resolve();
      });
      promise
        .then(() => {
          body?.map((data) => {
            temp.push({
              [head[0][0]]: data[0],
              [head[0][1]]: data[1],
              [head[0][2]]: data[2],
              [head[0][3]]: data[3],
              [head[0][4]]: data[4],
              [head[0][5]]: data[5],
              [head[0][6]]: data[6],
              [head[0][7]]: data[7],
              [head[0][8]]: data[8],
              [head[0][9]]: data[9],
              [head[0][10]]: data[10],
              [head[0][11]]: data[11],
              [head[0][12]]: data[12],
              [head[0][13]]: data[13],
            });
          });
        })
        .then(() => {
          setTableData([...temp]);
          console.log("temp:", temp);
        });
    });
  };

  //
  const handleAdd = () => {
    if (tableData?.length > 0) {
      let promise = new Promise((resolve, reject) => {
        resolve();
      });

      promise
        .then(() => {
          tableData?.map((data, index) => {
            // db.collection(companies)
            //     .doc(companyId_constant)
            //     .collection(type)
            //     .doc(fatDataDocId)
            //     .collection('table')
            //     .add(data)
            //     .then(() => {
            //         console.log("Execution table no: " + index + 1 + " added from excel sheet added")
            //     }).catch((e) => {
            //         toastMessageWarning({ message: "Something went wrong with table: " + index + 1 })
            //     })
          });
        })
        .then(() => {
          handleClose();
          toastMessageSuccess({
            message: tableData?.length + " table added Successfully !",
          });
        });
    } else {
      toastMessage({ message: "! File not found" });
    }
  };

  return (
    <>
      <input type="file" onChange={(e) => handleFileChange(e)} />
      <ButtonBasic buttonTitle=" Add + " onClick={handleAdd} />
    </>
  );
}
