import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import {
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import HealthScoreTile from "./forecast/HealthScore";
import AnomalyIndicator from "./forecast/AnomalyIndicator";
import TrendAnalysisChart from "./forecast/TrendAnalysisChart";
import ForecastChart from "./forecast/ForecastChart";
import ForecastTable from "./forecast/ForecastTable";
import MaintenanceSuggestionBox from "./forecast/MaintenanceSuggestionBox";
import EfficiencyLossTable from "./forecast/EfficiencyLossTable";
import ForecastConfidenceIndicator from "./forecast/ForecastConfidenceIndicator";
import MachineDowntimeProbability from "./forecast/MachineDowntimeProbability";
import ControlChart from "./forecast/ControlChart";
import ProcessCapability from "./forecast/ProcessCapability";
import AnomalyWarningSystem from "./forecast/AnomalyWarningSystem";
import Histogram<PERSON>hart from "./forecast/HistogramChart";
import TrendOutlierChart from "./forecast/TrendOutlierChart";
import MachineDataHeader from "./MachineDataHeader";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";
// Mock Data (10 AM - 10 PM actual, 10 PM - 12 AM forecast)
const mockData = {
  timestamps: [
    "2025-02-18T10:00:00",
    "2025-02-18T11:00:00",
    "2025-02-18T12:00:00",
    "2025-02-18T13:00:00",
    "2025-02-18T14:00:00",
    "2025-02-18T15:00:00",
    "2025-02-18T16:00:00",
    "2025-02-18T17:00:00",
    "2025-02-18T18:00:00",
    "2025-02-18T19:00:00",
    "2025-02-18T20:00:00",
    "2025-02-18T21:00:00",
    "2025-02-18T22:00:00",
    "2025-02-18T23:00:00",
    "2025-02-19T00:00:00",
  ],
  values: [
    100,
    102,
    98,
    105,
    103,
    107,
    110,
    108,
    109,
    112,
    113,
    115,
    null,
    null,
    null,
  ],
  forecastValues: [
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    115,
    115,
    130,
    140,
    150,
  ],
};

const MachineAnalysis = () => {
  const chartRef = useRef(null);
  const threshold = 3; // Stability threshold

  const hasMachineGETAccess = useCheckAccess("machines", "GET");

  return (
    <Container maxWidth="lg">
      <MachineDataHeader />
      {hasMachineGETAccess ? (
        <>
          {/* Row 1: Machine Health Score */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <HealthScoreTile values={mockData.values} />
            </Grid>

            <Grid item xs={12} md={6}>
              <AnomalyIndicator
                values={mockData.values}
                forecastValues={mockData.forecastValues}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <MaintenanceSuggestionBox
                values={mockData.values}
                forecastValues={mockData.forecastValues}
              />
            </Grid>

            <Grid item xs={12} sm={3} md={3}>
              <ForecastConfidenceIndicator
                values={mockData.values}
                forecastValues={mockData.forecastValues}
              />
            </Grid>

            <Grid item xs={12} sm={3} md={3}>
              <MachineDowntimeProbability
                values={mockData.values}
                forecastValues={mockData.forecastValues}
              />
            </Grid>

            <Grid item xs={12} md={12}>
              <TrendAnalysisChart
                values={mockData.values}
                timestamps={mockData.timestamps}
              />
            </Grid>

            {/* Placeholder for additional insights (you can replace later) */}

            <Grid item xs={12} md={12}>
              <ForecastChart
                timestamps={mockData.timestamps}
                values={mockData.values}
                forecastValues={mockData.forecastValues}
              />
            </Grid>

            <Grid item xs={12} md={12}>
              <ForecastTable
                timestamps={mockData.timestamps}
                values={mockData.values}
                forecastValues={mockData.forecastValues}
              />
            </Grid>

            {/* Efficiency Loss Table (Newly Added) */}
            <Grid item xs={12} md={12}>
              <EfficiencyLossTable
                values={mockData.values}
                forecastValues={mockData.forecastValues}
              />
            </Grid>

            <Grid item xs={12} md={12}>
              <ControlChart
                timestamps={mockData.timestamps}
                values={mockData.values}
              />
              ;
            </Grid>

            <Grid item xs={12} md={12}>
              <ProcessCapability values={mockData.values} USL={120} LSL={80} />;
            </Grid>

            <Grid item xs={12} md={12}>
              <AnomalyWarningSystem
                values={mockData.values}
                UCL={120}
                LCL={80}
              />
              ;
            </Grid>

            <Grid item xs={12} md={12}>
              <HistogramChart values={mockData.values} />;
            </Grid>

            <Grid item xs={12} md={12}>
              <TrendOutlierChart
                timestamps={mockData.timestamps}
                values={mockData.values}
              />
            </Grid>
          </Grid>
        </>
      ) : (
        <NotAccessible />
      )}
    </Container>
  );
};

export default MachineAnalysis;
