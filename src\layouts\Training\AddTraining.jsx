import { Button, InputLabel, TextField } from "@mui/material";
import React, { useState } from "react";
import { companies, companyId_constant, training } from "../../constants/data";
import { db } from "../../firebase";

const AddTraining = ({ mid, handleClose }) => {
  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = { title, desc, createdAt: new Date(), mid: mid };
    // db.collection(companies).doc(companyId_constant)
    // .collection(training).add(data).then(() => {
    //     handleClose()
    // })
  };

  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Title</InputLabel>
      <TextField
        onChange={(e) => setTitle(e.target.value)}
        value={title}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Description</InputLabel>
      <TextField
        onChange={(e) => setDesc(e.target.value)}
        value={desc}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{ marginBottom: "12px" }}
      />

      <div className="p-2 mt-2 flex justify-between">
        <Button onClick={handleClose} variant="outlined">
          Cancel
        </Button>
        <Button type="submit" variant="outlined">
          Submit
        </Button>
      </div>
    </form>
  );
};

export default AddTraining;
