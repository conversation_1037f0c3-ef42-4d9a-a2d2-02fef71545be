import axios from "axios";
import React, { createContext, useEffect, useState, useContext } from "react";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMachinesSetter } from "./MachineContext2";

export const EditMachinesContext = createContext();
export const EditMachinesSetterContext = createContext();

export function useEditMachinesGetter() {
  return useContext(EditMachinesContext);
}
export function useEditMachinesSetter() {
  return useContext(EditMachinesSetterContext);
}

const EditMachinesProvider = ({ children }) => {
  const [editCount, setEditCount] = useState(0);
  const allMachineSetter = useMachinesSetter();

  const handleEditMachine = () => {
    allMachineSetter();
    setEditCount(editCount + 1);
    console.log("CHECK", editCount);
  };
  return (
    <EditMachinesContext.Provider value={editCount}>
      <EditMachinesSetterContext.Provider value={handleEditMachine}>
        {children}
      </EditMachinesSetterContext.Provider>
    </EditMachinesContext.Provider>
  );
};

export default EditMachinesProvider;
