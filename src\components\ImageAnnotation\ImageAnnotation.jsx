import React, { useState } from "react";
import { useEffect } from "react";
import Annotation from "react-image-annotation";
import {
  PointSelector,
  RectangleSelector,
  OvalSelector,
} from "react-image-annotation/lib/selectors";
import { ShowMqttExpected } from "./ShowMqttValue";

export const colorOptions = [
  {
    color: "red",
    hex: "#D61C4E",
  },
  {
    color: "blue",
    hex: "#5800FF",
  },
  {
    color: "green",
    hex: "#5BB318",
  },
  {
    color: "purple",
    hex: "#A760FF",
  },
  {
    color: "F7EC09",
    hex: "#5BB318",
  },
  {
    color: "orange",
    hex: "#F15412",
  },
  {
    color: "Navy",
    hex: "#1F4690",
  },
  {
    color: "Black",
    hex: "#100F0F",
  },
  {
    color: "White",
    hex: "#FFFFFF",
  },
];

const Box = ({ children, geometry, style }) => (
  <div
    style={{
      ...style,
      position: "absolute",
      left: `${geometry.x}%`,
      top: `${geometry.y}%`,
      height: `${geometry.height}%`,
      width: `${geometry.width}%`,
    }}
  >
    {children}
  </div>
);

function renderSelector({ annotation, active }) {
  const { geometry } = annotation;
  if (!geometry) return null;

  return (
    <Box
      geometry={geometry}
      style={{
        background: "rgba(255, 255, 255, 0.5)",
        border: "solid 4px red",
      }}
    >
      Annotation Area
    </Box>
  );
}

function renderHighlight({ annotation, active }) {
  if (!annotation || !annotation.data || !annotation.geometry) return null;
  const { geometry } = annotation;
  return (
    <Box
      key={annotation.data.id}
      geometry={geometry}
      style={{
        alignItems: "center",
        justifyContent: "center",
        display: "flex",
        border: `solid 4px ${annotation?.data?.color.border_color}`,
        boxShadow: active && "0 0 20px 20px rgba(255, 255, 255, 0.3) inset",
      }}
    ></Box>
  );
}

function renderContent({ annotation }) {
  if (!annotation || !annotation.data || !annotation.geometry) return null;
  const { geometry } = annotation;
  return (
    <div
      key={annotation.data.id}
      style={{
        background: annotation.data.color
          ? annotation?.data.color.background_color
          : "black",
        color: annotation.data.color
          ? annotation?.data.color.text_color
          : "white",
        padding: 10,
        position: "absolute",
        fontSize: 12,
        left: `${geometry.x}%`,
        top: `${geometry.y + geometry.height}%`,
      }}
    >
      {annotation.data && annotation.data.text} :{" "}
      <ShowMqttExpected data={annotation.data} />
    </div>
  );
}

const ImageAnnotation = ({
  mqttList,
  annotation,
  annotations,
  setAnnotation,
  setColor,
  onSubmit,
  imgUrl,
  mode,
}) => {
  const [processValues, setProcessValues] = useState([]);
  const [displayAnnotations, setDisplayAnnotations] = useState([]);

  useEffect(() => {
    const tempAnn = annotations
      .map((annotation) => {
        const matchingData = mqttList.find(
          (data) => data._id === annotation.data.mqtt_id,
        );
        const id = annotation.data?.id ?? Math.random();

        if (matchingData) {
          return {
            ...annotation,
            id,
            data: {
              ...annotation.data,
              text: annotation.data.text,
            },
          };
        }
        return {
          ...annotation,
          id,
        };
      })
      .filter(
        (a) =>
          a.geometry &&
          typeof a.geometry.x === "number" &&
          typeof a.geometry.y === "number" &&
          typeof a.geometry.width === "number" &&
          typeof a.geometry.height === "number" &&
          typeof a.geometry.type === "string" &&
          a.geometry.width > 0 &&
          a.geometry.height > 0 &&
          a.geometry.type === "RECTANGLE" // Only allow rectangles
      );

    // Log filtered annotations for debugging
    console.log("Filtered annotations to display:", tempAnn);

    setProcessValues(mqttList);
    setDisplayAnnotations(tempAnn);
  }, [annotations, mqttList]);

  return (
    <div className="mt-4 text-black">
      {displayAnnotations.length > 0 ? (
        <Annotation
          src={imgUrl}
          alt="Two pebbles anthropomorphized holding hands"
          annotations={displayAnnotations}
          type={RectangleSelector.TYPE}
          value={annotation}
          onChange={setAnnotation}
          renderContent={renderContent}
          onSubmit={onSubmit}
          disableAnnotations={mode}
          disableSelector={mode}
          disableEditor={mode}
          renderSelector={renderSelector}
          renderHighlight={renderHighlight}
          activeAnnotations={displayAnnotations}
        />
      ) : (
        <div>No valid annotations to display.</div>
      )}
    </div>
  );
};

export default ImageAnnotation;
