import React, { useState, useEffect } from "react";
import {
  Grid,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  MenuItem,
  Select,
  Button,
} from "@mui/material";
import PropTypes from "prop-types";

const LABEL_MODEL_FILTER = "Select Model";
const LABEL_ROLE_FILTER = "Select Role";
const MODULES_ACCESS_TITLE = "Modules Access";
const ACTIONS_MAP = {
  GET: "VIEW",
  POST: "CREATE",
  PUT: "UPDATE",
  DELETE: "DELETE",
};

const ModulesAccess = ({ formData, setFormData, roles }) => {
  const [selectedModel, setSelectedModel] = useState("");
  const [selectedRole, setSelectedRole] = useState("");
  const [editableRules, setEditableRules] = useState({});

  // Get models from rules
  const models = Object.keys(formData.rules || {});

  // Always use string IDs for comparison
  const rolesMap = {};
  (roles || []).forEach((r) => {
    rolesMap[String(r.id)] = r.name;
  });

  // Set default model if not set
  useEffect(() => {
    if (models.length > 0 && !selectedModel) setSelectedModel(models[0]);
  }, [models, selectedModel]);

  // Update editableRules when model or rules change
  useEffect(() => {
    if (selectedModel) {
      const modelRules = formData.rules[selectedModel] || {};
      setEditableRules(JSON.parse(JSON.stringify(modelRules)));
    }
  }, [selectedModel, formData.rules]);

  // Only show roles present in the current model's rules
  const modelRoleIds = Object.keys(editableRules);

  // Filtered role IDs for table (by filter or all)
  const filteredRoleIds = modelRoleIds.filter(
    (id) => !selectedRole || String(id) === String(selectedRole),
  );

  const handleCheckboxChange = (roleId, action) => {
    setEditableRules((prev) => ({
      ...prev,
      [roleId]: prev[roleId]?.includes(action)
        ? prev[roleId].filter((a) => a !== action)
        : [...(prev[roleId] || []), action],
    }));
  };

  const handleSubmit = () => {
    setFormData({
      ...formData,
      rules: { ...formData.rules, [selectedModel]: editableRules },
    });
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {MODULES_ACCESS_TITLE}
      </Typography>
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={6}>
          <Select
            fullWidth
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            displayEmpty
          >
            <MenuItem value="" disabled>
              {LABEL_MODEL_FILTER}
            </MenuItem>
            {models.map((model) => (
              <MenuItem key={model} value={model}>
                {model.toUpperCase()}
              </MenuItem>
            ))}
          </Select>
        </Grid>
        <Grid item xs={6}>
          <Select
            fullWidth
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
            displayEmpty
          >
            <MenuItem value="">{LABEL_ROLE_FILTER}</MenuItem>
            {(roles || []).map((role) => (
              <MenuItem key={role.id} value={role.id}>
                {role.name.toUpperCase()}
              </MenuItem>
            ))}
          </Select>
        </Grid>
      </Grid>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Role</TableCell>
              {Object.values(ACTIONS_MAP).map((action) => (
                <TableCell key={action} align="center">
                  {action}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredRoleIds.map((roleId) => (
              <TableRow key={roleId}>
                <TableCell>
                  {rolesMap[roleId] ? (
                    rolesMap[roleId].toUpperCase()
                  ) : (
                    <span style={{ color: "red" }}>Unknown (ID: {roleId})</span>
                  )}
                </TableCell>
                {Object.keys(ACTIONS_MAP).map((action) => (
                  <TableCell key={action} align="center">
                    <Checkbox
                      checked={editableRules[roleId]?.includes(action)}
                      onChange={() => handleCheckboxChange(roleId, action)}
                    />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Grid container justifyContent="flex-end" sx={{ mt: 2 }}>
        <Button variant="contained" color="primary" onClick={handleSubmit}>
          UPDATE
        </Button>
      </Grid>
    </Paper>
  );
};

ModulesAccess.propTypes = {
  formData: PropTypes.shape({
    rules: PropTypes.object.isRequired,
  }).isRequired,
  setFormData: PropTypes.func.isRequired,
  roles: PropTypes.array.isRequired,
};

export default ModulesAccess;
