import React from "react";
import { Box, <PERSON><PERSON><PERSON>, But<PERSON> } from "@mui/material";
import PropTypes from "prop-types";

const SearchField = ({
  label = "Search",
  placeholder = "Name, ID, Location or Equipment",
  search,
  setSearch,
  handlePDFDownload,
  showPDFDownload = false,
}) => {
  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          my: 1,
        }}
      >
        <TextField
          label={label}
          placeholder={placeholder}
          sx={{
            maxWidth: "30rem",
          }}
          size="small"
          value={search}
          onChange={(e) => setSearch(e.target.value.trimStart())}
          fullWidth
        />
        &nbsp;
        {showPDFDownload ? (
          <Button
            variant="outlined"
            color="inherit"
            sx={{
              textTransform: "none",
            }}
            onClick={handlePDFDownload}
          >
            PDF Download
          </Button>
        ) : (
          ""
        )}
      </Box>
    </Box>
  );
};

SearchField.propTypes = {
  label: PropTypes.string, // Label for the search field
  placeholder: PropTypes.string, // Placeholder text for the search input
  search: PropTypes.string.isRequired, // Current search value
  setSearch: PropTypes.func.isRequired, // Function to update the search value
  handlePDFDownload: PropTypes.func, // Function to handle PDF download
  showPDFDownload: PropTypes.bool, // Determines if the PDF download button should be displayed
};

SearchField.defaultProps = {
  label: "Search", // Default label
  placeholder: "Name, ID, Location or Equipment", // Default placeholder text
  handlePDFDownload: () => {}, // Default is a no-op function
  showPDFDownload: false, // Default is to not show the PDF download button
};

export default SearchField;
