import React from "react";
import {
  Modal,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
  Button,
} from "@mui/material";

const ColumnSelectionModal = ({
  isOpen,
  toggleModal,
  config,
  visibleColumns,
  handleColumnToggle,
  applyColumnSelection,
}) => {
  return (
    <Modal open={isOpen} onClose={toggleModal}>
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "80%",
          height: "70%",
          bgcolor: "background.paper",
          boxShadow: 24,
          p: 4,
          borderRadius: "8px",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Typography variant="h5" gutterBottom>
          Select Columns
        </Typography>
        <Box
          sx={{
            flex: 1,
            overflowY: "auto",
            display: "flex",
            flexWrap: "wrap",
            gap: 2,
          }}
        >
          {config.map((column) => (
            <FormControlLabel
              key={column.key}
              control={
                <Checkbox
                  checked={visibleColumns.includes(column.key)}
                  onChange={() => handleColumnToggle(column.key)}
                />
              }
              label={column.label}
            />
          ))}
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2,
            marginTop: 2,
          }}
        >
          <Button variant="contained" color="secondary" onClick={toggleModal}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={applyColumnSelection}
          >
            Apply
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default ColumnSelectionModal;
