import React from "react";
import { useFolder } from "../../hooks/useFolder";
import { useParams, useNavigate } from "react-router-dom";
import FolderCardDashboard from "./FolderCardDashboard";
import { useStateContext } from "../../context/ContextProvider";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { Button, Typography } from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";
const useStyles = makeStyles((theme) => ({
  fileManagerSection: {
    backgroundColor: theme.palette.custom.backgroundForth,
    padding: "2rem",
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
    // borderRadius: "5px",
  },
  fileManagerHeading: {
    display: "flex",
    justifyContent: "space-between",
  },
  buttonConatiner: {
    alignSelf: "center",
  },
  foldersCardContainer: {
    display: "flex",
    justifyContent: "center",
    padding: "1.2rem 0",
    gap: "1rem",
  },
}));
const FileManagerSection = () => {
  const { folderId } = useParams();
  const { childFolders } = useFolder(folderId);
  const { currentColorLight, currentMode } = useStateContext();
  const navigate = useNavigate();
  const classes = useStyles();
  const hasGETAccess = useCheckAccess("folders", "GET");
  return (
    <section className={`${classes.fileManagerSection} border-radius-inner`}>
      <div className={classes.fileManagerHeading}>
        <div className="info">
          <Typography variant="h5">Library</Typography>
          <Typography variant="subtitle2">
            Important Files and Folders
          </Typography>
        </div>

        <div className={classes.buttonConatiner}>
          {!!childFolders.length && childFolders.length > 0 ? (
            <Button
              onClick={() => navigate("/file-manager")}
              variant="contained"
              disabled={!hasGETAccess}
            >
              View All Files
            </Button>
          ) : null}
        </div>
      </div>

      {hasGETAccess ? (
        <div className={classes.foldersCardContainer}>
          {childFolders.length > 0 ? (
            <>
              {childFolders.slice(0, 3).map((childFolder) => (
                <FolderCardDashboard
                  folder={childFolder}
                  key={childFolder.id}
                />
              ))}
            </>
          ) : (
            "NO FILES AVAILABLE"
          )}
        </div>
      ) : (
        <NotAccessible />
      )}
    </section>
  );
};

export default FileManagerSection;
