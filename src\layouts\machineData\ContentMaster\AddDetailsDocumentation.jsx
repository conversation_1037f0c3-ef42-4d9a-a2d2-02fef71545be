import React, { useState, useEffect } from "react";
import { TextField } from "@mui/material";
import { Button, Checkbox, IconButton, InputLabel } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CloseIcon from "@mui/icons-material/Close";
import { db } from "../../../firebase";
import { firebaseLooper } from "../../../tools/tool";
import { companies, companyId_constant } from "../../../constants/data";
import { toastMessageSuccess } from "../../../tools/toast";
import { useParams } from "react-router-dom";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { useAuth } from "../../../hooks/AuthProvider";

const AddDetailsDocumentation = ({ type, handleClose, machineName }) => {
  const [user, setUser] = useState([]);
  const [summary, setSummary] = useState(false);
  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");
  const [method, setMethod] = useState("");
  const [objective, setObjective] = useState("");
  const [preValue, setPreValue] = useState("");
  const [proValue, setProValue] = useState("");
  const [pre, setPre] = useState([]);
  const [pro, setPro] = useState([]);
  const { docId } = useParams();
  const { currentUser } = useAuth();

  const prePopData = (data) => {
    setPre((pre) => pre.filter((dataItem) => dataItem !== data));
  };
  const proPopData = (data) => {
    setPro((pro) => pro.filter((dataItem) => dataItem !== data));
  };
  const moduleName = type === "fatData" ? "FAT" : "SAT";
  const handleSubmit = (e) => {
    e.preventDefault();
    const docdata = {
      title,
      desc,
      method,
      objective,
      pre,
      procedure: pro,
      summary,
      index: 0,
      docId: docId,
      createdAt: new Date(),
    };
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(type)
    //     .add(docdata)
    //     .then((data) => {
    //         handleClose()
    //         LoggingFunction(
    //             machineName,
    //             title,
    //             `${user?.fname} ${user?.lname}`,
    //             moduleName,
    //             `New ${moduleName} Report is added`
    //         )
    //         toastMessageSuccess({ message: "Added details for documentation Successfully !" })
    //     })
  };
  useEffect(() => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection("userData")
    //     .where("email", "==", currentUser.email)
    //     .onSnapshot((snap) => {
    //         const data = firebaseLooper(snap);
    //         setUser(data[0]);
    //     });
  }, []);
  return (
    <form onSubmit={handleSubmit}>
      <InputLabel>Title</InputLabel>
      <TextField
        onChange={(e) => setTitle(e.target.value)}
        style={{ marginBottom: "10px" }}
        variant="outlined"
        fullWidth
        placeholder="Enter Document Title"
      />
      <InputLabel>Description</InputLabel>
      <TextField
        onChange={(e) => setDesc(e.target.value)}
        style={{ marginBottom: "10px" }}
        variant="outlined"
        fullWidth
        placeholder="Enter Document Description"
      />
      <InputLabel>Method</InputLabel>
      <TextField
        onChange={(e) => setMethod(e.target.value)}
        style={{ marginBottom: "10px" }}
        variant="outlined"
        fullWidth
        placeholder="Enter Document Title"
      />
      <InputLabel>Objective</InputLabel>
      <TextField
        onChange={(e) => setObjective(e.target.value)}
        style={{ marginBottom: "10px" }}
        variant="outlined"
        fullWidth
        placeholder="Enter Document Title"
      />
      <InputLabel>Pre Requisites</InputLabel>
      <section style={{ marginBottom: "10px" }} className="flex">
        <TextField
          onChange={(e) => setPreValue(e.target.value)}
          variant="outlined"
          fullWidth
          placeholder="Enter Document Title"
        />
        <IconButton onClick={() => setPre([...pre, preValue])}>
          <AddIcon />
        </IconButton>
      </section>
      <div style={{ marginBottom: "20px" }}>
        {pre.map((data, idx) => (
          <div key={data + idx} className="text-gray-600">
            <span className="font-bold">{idx + 1}. </span> {data}{" "}
            <span>
              <IconButton onClick={() => prePopData(data)}>
                <RemoveIcon />
              </IconButton>
            </span>
          </div>
        ))}
      </div>
      <InputLabel>Procedure</InputLabel>
      <section style={{ marginBottom: "10px" }} className="flex">
        <TextField
          onChange={(e) => setProValue(e.target.value)}
          variant="outlined"
          fullWidth
          placeholder="Enter Document Title"
        />
        <IconButton onClick={() => setPro([...pro, proValue])}>
          <AddIcon />
        </IconButton>
      </section>
      <div style={{ marginBottom: "20px" }}>
        {pro.map((data, idx) => (
          <div key={data + idx} className="text-gray-600">
            <span className="font-bold">{idx + 1}. </span> {data}{" "}
            <span>
              <IconButton onClick={() => proPopData(data)}>
                <RemoveIcon />
              </IconButton>
            </span>
          </div>
        ))}
      </div>
      <InputLabel>Summary</InputLabel>
      <section style={{ marginBottom: "10px" }} className="flex">
        <div
          onClick={() => setSummary(true)}
          className={
            summary
              ? `flex p-2  cursor-pointer  border-2 border-blue-400 mr-4 rounded-md bg-blue-100 `
              : `flex cursor-pointer  p-2 border-2 border-gray-400 mr-4 rounded-md  `
          }
        >
          <Checkbox
            label="Yes"
            checked={summary}
            icon={<CheckCircleIcon />}
            checkedIcon={<CheckCircleIcon />}
          />
          <p>YES</p>
        </div>
        <div
          onClick={() => setSummary(false)}
          className={
            !summary
              ? `flex cursor-pointer  p-2 border-2 border-purple-400 mr-4 rounded-md bg-purple-100 `
              : `flex cursor-pointer p-2 border-2 border-gray-400 mr-4 rounded-md  `
          }
        >
          <Checkbox
            checked={!summary}
            color="secondary"
            icon={<CloseIcon />}
            checkedIcon={<CloseIcon />}
          />
          <p>NO</p>
        </div>
      </section>

      <div className="flex justify-between">
        <Button
          onClick={handleClose}
          variant="contained"
          color="success"
          endIcon={<CloseIcon />}
        >
          Cancel{" "}
        </Button>
        <Button type="submit" variant="contained" endIcon={<AddIcon />}>
          ADD Details{" "}
        </Button>
      </div>
    </form>
  );
};

export default AddDetailsDocumentation;
