import {
  Box,
  Button,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Tooltip,
  Typography,
} from "@mui/material";
import React from "react";
import AddTaskIcon from "@mui/icons-material/AddTask";
import AdjustIcon from "@mui/icons-material/Adjust";
import { useNavigate } from "react-router-dom";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";

const AnnotationHeader = ({
  heading,
  save,
  mode,
  setMode,
  mqttId,
  setMqttId,
  mqttList,
  setColor,
  color,
  mainMid,
  openDialog,
}) => {
  const history = useNavigate();
  const isSelectProcessValuesDisabled = !mqttList.filter(
    (item, idx) => item?.mid === mainMid,
  )?.length;

  return (
    <>
      {" "}
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Typography gutterBottom variant="h5" align="left">
          <span>
            <Tooltip title="Return">
              <IconButton onClick={() => history(-1)}>
                <ArrowBackIcon />
              </IconButton>
            </Tooltip>
          </span>{" "}
          <b>{heading}</b>
        </Typography>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {/* Icons for Save , Edit/ Live Mode & Assign Mode */}
          <Box>
            <Button
              onClick={openDialog}
              color="inherit"
              fullWidth
              variant="outlined"
            >
              View Details
            </Button>
          </Box>
          {!mode && (
            <div className="mr-5 ml-5" style={{ display: "flex" }}>
              <Box sx={{ mr: 2, align: "center" }}>
                <p className="text-sm">BG Color</p>
                <input
                  value={color.background_color}
                  required
                  type="color"
                  onChange={(e) =>
                    setColor({ ...color, background_color: e.target.value })
                  }
                />
              </Box>
              <Box sx={{ mr: 2, align: "center" }}>
                <p className="text-sm"> Text</p>
                <input
                  value={color.text_color}
                  required
                  type="color"
                  onChange={(e) =>
                    setColor({ ...color, text_color: e.target.value })
                  }
                />
              </Box>
              <Box sx={{ mr: 2, align: "center" }}>
                <p className="text-sm">Border</p>
                <input
                  value={color.borderColor}
                  required
                  type="color"
                  onChange={(e) =>
                    setColor({ ...color, border_color: e.target.value })
                  }
                />
              </Box>
            </div>
          )}

          {!mode && (
            <FormControl style={{ width: 230 }}>
              <InputLabel>Select Process Values</InputLabel>
              <Select
                value={mqttId}
                onChange={(e) => setMqttId(e.target.value)}
                size="small"
                disabled={isSelectProcessValuesDisabled}
                label="Select Process Values"
              >
                {mqttList
                  .filter((item, idx) => item?.mid === mainMid)

                  .map((data, index) => (
                    <MenuItem key={data._id + index} value={data._id}>
                      {data.tag}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
          )}

          <IconButton
            disabled={mode || isSelectProcessValuesDisabled}
            onClick={save}
          >
            <AddTaskIcon /> <span className="text-sm"> Save</span>
          </IconButton>

          <IconButton onClick={() => setMode(!mode)}>
            <AdjustIcon style={mode ? { color: "red" } : { color: "green" }} />{" "}
            <span className="text-sm uppercase">{mode ? "LIVE" : "EDIT"}</span>
          </IconButton>
        </Box>
      </Box>
      <hr className="mt-4" />
    </>
  );
};

export default AnnotationHeader;
