import { Box, Grid, Typography } from "@mui/material";
import React from "react";

const SetPointsForm = () => {
  return (
    <div>
      <Box sx={{ border: "1px solid black", p: 1 }}>
        <Typography align="center">
          <b>Revision No : </b>{" "}
        </Typography>
      </Box>

      {/*Master Equipment used starts */}

      {/* Table ends master equipment */}

      {/* Result custom table */}
      <Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderTop: "none",
          }}
        >
          {/* Sr no0 */}
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center">Sl. No. </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center">Instrument ID</Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center">Overall Range</Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center">Operating Range</Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center">Test Point No. (1)</Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center">Test Point No. (2)</Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center">Test Point No. (3)</Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center">Test Point No. (4)</Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center">Test Point No. (5)</Typography>
          </Box>
        </Box>
      </Box>
      {/* Data Mapping  */}
      <Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderTop: "none",
          }}
        >
          {/* Sr no0 */}
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center">1 </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center"></Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center"> </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center"></Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center"></Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center"></Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center"></Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center"></Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "120px" }}>
            <Typography align="center"></Typography>
          </Box>
        </Box>
      </Box>

      <Box sx={{ p: 1 }}>
        <Typography align="left">
          Note:Actual Calibration test points during calibration should be
          closer to the pre-defined 'calibration test points'{" "}
        </Typography>
      </Box>

      <Box
        sx={{
          border: "1px solid black",
          p: 1,
          display: "flex",
          justifyContent: "center",
        }}
      >
        <Box sx={{ borderRight: "1px solid black", p: 1, width: "50%" }}>
          <Typography align="center">
            <b>Prepared By</b>{" "}
          </Typography>
        </Box>
        <Box sx={{ p: 1, width: "50%" }}>
          <Typography align="center">
            <b> Checked by(F&E)(Sign/Date)</b>{" "}
          </Typography>
        </Box>
      </Box>
      <Box
        sx={{
          border: "1px solid black",
          borderTop: "none",
          p: 1,
          display: "flex",
          justifyContent: "center",
        }}
      >
        <Box
          sx={{
            borderRight: "1px solid black",
            p: 1,
            width: "50%",
            height: "100px",
          }}
        >
          <Typography align="center"></Typography>
        </Box>
        <Box sx={{ p: 1, width: "50%", height: "100px" }}>
          <Typography align="center"> </Typography>
        </Box>
      </Box>
    </div>
  );
};

export default SetPointsForm;
