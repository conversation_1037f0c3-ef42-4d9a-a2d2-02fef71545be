import { Box } from "@mui/material";
import React from "react";
import { Link } from "react-router-dom";
import { removeTrailingSlash } from "../../utils";
import PropTypes from "prop-types";

const LinkStyles = {
  textDecoration: "none",
  padding: "8px 12px",
  color: "black",
  fontWeight: "bold",
  // border: "1px solid",
  borderRadius: "20px",
  boxShadow: "0 0 10px 4px rgba(0,0,0,0.1)",
};

const CustomLinkToggle = ({
  currentPath,
  path1,
  title1 = "Title1",
  path2,
  title2 = "Title2",
  href1,
  href2,
}) => {
  const refinedCurrPath = removeTrailingSlash(currentPath);

  return (
    <Box
      sx={{
        display: "flex",
        gap: "8px",
        alignItems: "center",
        padding: "4px",
        borderRadius: "42px",
        boxShadow: "0 0 4px 4px rgba(0,0,0,0.1)",
        // border: "0.5px solid #000",
        // backgroundColor: "#f5f5f5",
      }}
    >
      <Link
        style={{
          ...LinkStyles,
          borderColor: refinedCurrPath === path1 ? "black" : "transparent",
          backgroundColor:
            refinedCurrPath === path1 ? "blueviolet" : "transparent",
          color: refinedCurrPath === path1 ? "white" : "black",
        }}
        to={href1 ?? path1}
      >
        {title1}
      </Link>
      <Link
        style={{
          ...LinkStyles,
          borderColor: refinedCurrPath === path2 ? "black" : "transparent",
          backgroundColor:
            refinedCurrPath === path2 ? "blueviolet" : "transparent",
          color: refinedCurrPath === path2 ? "white" : "black",
        }}
        to={href2 ?? path2}
      >
        {title2}
      </Link>
    </Box>
  );
};

CustomLinkToggle.propTypes = {
  currentPath: PropTypes.string.isRequired, // Current URL path
  path1: PropTypes.string.isRequired, // First toggle path
  title1: PropTypes.string, // First toggle button title
  path2: PropTypes.string.isRequired, // Second toggle path
  title2: PropTypes.string, // Second toggle button title
  href1: PropTypes.string, // Optional override for first link href
  href2: PropTypes.string, // Optional override for second link href
};

export default CustomLinkToggle;
