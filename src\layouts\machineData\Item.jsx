/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from "react";
import {
  companies,
  companyId_constant,
  liveData,
  maintenance,
} from "../../constants/data";
import { db } from "../../firebase";
import {
  ReactPictureAnnotation,
  defaultShapeStyle,
} from "react-picture-annotation";
import CloseIcon from "@mui/icons-material/Close";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import {
  IconButton,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  MenuItem,
  Select,
  Tab,
  TableRow,
  TableCell,
  CircularProgress,
  InputLabel,
  Toolt<PERSON>,
} from "@mui/material";
import Delete from "../../components/Delete/Delete";
import EditLiveData from "./EditMaintenance/EditLiveData";
import { firebaseLooper } from "../../tools/tool";
import { useAuth } from "../../hooks/AuthProvider";
import AnnotationShow from "../AnnotationShowcase/AnnotationShow";
import AnnotationChart from "./AnnotationChart";
import AnoItem from "./anoItem";
import ArchitectureIcon from "@mui/icons-material/Architecture";
import ConfirmModal from "../../components/ConfirmModel/ConfirmModal";
import { useParams } from "react-router-dom";
import LoggingFunction from "../../components/CFR-Report/LoggingFunction";
import ShowChartIcon from "@mui/icons-material/ShowChart";
import SubItem from "./SubItem";
import { useStateContext } from "../../context/ContextProvider";

const Item = ({ data, activeRole, machineName, activeChartFromSelected }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [ann, setAnn] = useState([]);
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openAssign, setOpenAssign] = useState(false);
  const [mqttId, setMqttId] = useState("");
  const [annData, setAnnData] = useState([]);
  const [processValues, setProcessValues] = useState([]);
  const { currentUser } = useAuth();
  const [user, setUser] = useState([]);
  const [disabled, setDisabled] = useState(true);
  const [show, setShow] = useState(true);
  const [disabledShowButton, setDisabledShowButton] = useState(false);
  const [annotationData, setAnnotationData] = useState([]);
  const [openGraph, setOpenGraph] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [openCalibration, setOpenCalibration] = useState(false);
  const [calibrationValues, setCalibrationValues] = useState([]);
  const [assignCalib, setAssignCalib] = useState(false);
  const [calib_id, setCalibId] = useState("");
  const { mid } = useParams();
  const { currentColor, currentMode, currentColorLight } = useStateContext();

  //
  //const [anotationDataIndex, setAnotationDataIndex] = useState(null);
  //

  //MQTT VALUES SHOW ON SCREEN WITH USEEFFECT

  useEffect(() => {
    //users
    // db.collection('users').where('email', '==', currentUser.email).onSnapshot(snap => {
    //   const data = firebaseLooper(snap)
    //   setUser(data[0])
    // })

    //annotation
    setAnnotationData(data.annotationData);

    //Calibration
    // db.collection(companies).doc(companyId_constant).collection(maintenance).where('type', '==', 0).where('mid', '==', mid).onSnapshot(snap => {
    //   const data = firebaseLooper(snap)
    //   setCalibrationValues(data)
    // })
  }, [data, currentUser]);

  const destructureMqttValues = () => {
    const alterData = data;

    const tempAlterData = [];

    for (let index = 0; index < data.annotationData.length; index++) {
      //console.log(data.annotationData[index].mqttId)

      let value = "";
      // db.collection(companies)
      //   .doc(companyId_constant)
      //   .collection("liveData2")
      //   .doc(data.annotationData[index].mqttId)
      //   .onSnapshot((snap) => {
      //     const newdata = snap.data();
      //     value = newdata?.value;
      //     //console.log(value)
      //     alterData.annotationData[index].comment =
      //       annotationData[index].comment +
      //       ` : ${Math.round(value * 1000) / 1000}`;
      //   });
    }
    tempAlterData.push(...alterData);
    setAnnotationData(tempAlterData.annotationData);
  };

  const handleDisable = () => {
    setDisabledShowButton(true);
  };

  const resetValues = () => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(liveData)
    //   .doc(data.id)
    //   .onSnapshot((snap) => {
    //     const data = snap.data();
    //     setAnnotationData(data.annotationData);
    //   });
  };
  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection("liveData2")
    //   .onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     setProcessValues(data);
    //   });
  }, []);

  const onSelect = (selectedId) => {}; //console.log(selectedId);
  const onChange = (data) => {
    // console.log(data);
    setAnnotationData(data);
  };

  const handleSubmit = () => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(liveData)
    //   .doc(data.id)
    //   .update({ annotationData: ann });
  };

  const assignCalibValue = () => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(liveData)
    //   .doc(data.id)
    //   .update({ calib_id: calib_id })
    //   .then(() => {
    //     setAssignCalib(false);
    //   });
  };

  function deleteData() {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(liveData)
    //   .doc(data.id)
    //   .delete()
    //   .then(() => {
    //     LoggingFunction(
    //       machineName,
    //       data.title,
    //       `${user?.fname} ${user?.lname}`,
    //       "Live data",
    //       `${data.title} is deleted from Live Data module`
    //     );
    //     toastMessage({ message: "Deleted data successfully !" });
    //   });
  }

  //Updating process values to annotated image data

  const handleMqtt = (arrId) => {
    let tempArray = new Array();

    tempArray.push(...annotationData);
    //console.log(tempArray);
    for (let index = 0; index < tempArray.length; index++) {
      if (tempArray[index].id === arrId) {
        tempArray[index].mqttId = mqttId;
        tempArray[index].calib_id = calib_id;
        break;
      }
    }
    //console.log(tempArray)
    setAnnotationData(tempArray);
    setDisabled(false);
    setConfirmOpen(true);
  };

  //Update MQTT Values
  const updateMqtt = () => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(liveData)
    //   .doc(data.id)
    //   .update({ annotationData: annotationData })
    //   .then(() => {
    //     //console.log('updated thanks')
    //     setDisabled(true);
    //   });
  };

  //SAVE ANNOTATION DATA WITH MQTT function

  const saveData = (id) => {
    let tempArray = [];
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(liveData)
    //   .doc(data.id)
    //   .onSnapshot((doc) => {
    //     const data = doc.data();
    //     //console.log(data.annotationData)
    //     tempArray.push(...data.annotationData);
    //   });
    tempArray.push(...annotationData);
    for (let x in tempArray) {
      //console.log(x)
      //console.log(tempArray[x])

      if (tempArray[x].mqttId == null) {
        tempArray[x].mqttId = "Temp-cond";
        //console.log("Hello world")
        //console.log(tempArray[x])
      }

      // db.collection(companies)
      //   .doc(companyId_constant)
      //   .collection(liveData)
      //   .doc(data.id)
      //   .update({
      //     annotationData: tempArray,
      //   })
      //   .then(() => {
      //     //console.log('updated success')
      //   });
    }
    toastMessageSuccess({ message: "Updated successfully" });
  };
  return (
    <>
      <TableRow
        sx={{
          "&:last-child td, &:last-child th": { border: 0 },
        }}
        style={{ cursor: "pointer" }}
      >
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="left"
        >
          {data.title}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="left"
        >
          {data.desc}
        </TableCell>
        <TableCell
          className="uppercase "
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="center"
        >
          {data?.type === 0 ? <b>PROCESS</b> : <b>SERVICE</b>}
        </TableCell>
        <TableCell
          style={{ borderBottom: "none", color: "green" }}
          align="center"
        >
          Active
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="center"
        >
          {data?.createdAt.toDate().toString().substring(0, 15)}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="center"
        >
          {activeRole && (
            <>
              <IconButton onClick={() => setOpenEdit(true)}>
                <EditIcon style={{ fontSize: "20px", color: "#00f" }} />
              </IconButton>
              <IconButton
                onClick={() => setOpenDel(true)}
                sx={{ margin: "0px 16px" }}
              >
                <DeleteIcon style={{ fontSize: "20px", color: "#f00" }} />
              </IconButton>
            </>
          )}
          <IconButton
            onClick={() => {
              setIsOpen(!isOpen);
              setOpenCalibration(false);
            }}
          >
            {isOpen ? (
              <ExpandLessIcon style={{ fontSize: "20px" }} />
            ) : (
              <ExpandMoreIcon style={{ fontSize: "20px" }} />
            )}
          </IconButton>
        </TableCell>
      </TableRow>
      <>
        {isOpen && (
          <TableRow
            sx={{
              "&:last-child td, &:last-child th": {
                border: 0,
              },
            }}
          >
            <TableCell
              style={
                currentMode === "Dark" ? { color: "white" } : { color: "black" }
              }
              align="center"
              colSpan={6}
            >
              <div className="subData">
                <div className="flex w-full justify-center  ">
                  <div
                    style={
                      currentMode === "Dark"
                        ? { color: "white", backgroundColor: "#29293d" }
                        : { color: "black", backgroundColor: currentColor }
                    }
                    className=" flex  justify-center p-1 rounded-md shadow-md  "
                  >
                    {activeRole && (
                      <>
                        <div className="flex justify-start mb-0.5">
                          {show && (
                            <>
                              <button
                                disabled
                                onClick={() => saveData()}
                                className="bg-gray-300 w-8 h-8 py-2 px-4 rounded-full flex justify-center"
                              >
                                <i class="ri-indeterminate-circle-line text-white"></i>
                              </button>
                              <div className="self-center px-1 font-bold text-gray-300 text-xs">
                                {" "}
                                Update Annotation{" "}
                              </div>
                            </>
                          )}
                          {!show && (
                            <>
                              {" "}
                              <button
                                onClick={() => saveData()}
                                className="bg-black w-8 h-8 hover:bg-gray-700  py-2 px-4 rounded-full flex justify-center"
                              >
                                <i class="ri-refresh-fill text-xl text-white self-center"></i>
                              </button>
                              <div className="self-center px-1 font-bold">
                                {" "}
                                Update Annotation{" "}
                              </div>
                            </>
                          )}
                        </div>

                        <div className="flex justify-start mb-0.5">
                          {show && (
                            <>
                              <button
                                disabled
                                onClick={() => setOpenAssign(true)}
                                className="bg-gray-300 w-8 h-8 py-2 px-4 rounded-full flex justify-center"
                              >
                                <i class="ri-indeterminate-circle-line text-white"></i>
                              </button>
                              <div className="self-center px-1 font-bold text-gray-300 text-xs">
                                {" "}
                                Assign{" "}
                              </div>
                            </>
                          )}
                          {!show && (
                            <>
                              <button
                                onClick={() => setOpenAssign(true)}
                                className="bg-sky-600 w-8 h-8 hover:bg-sky-500 py-2 px-4 rounded-full flex justify-center"
                              >
                                <i class="ri-pencil-fill text-white"></i>
                              </button>
                              <div className="self-center px-1 font-bold">
                                {" "}
                                Assign{" "}
                              </div>
                            </>
                          )}
                        </div>
                      </>
                    )}

                    {!show && (
                      <div className="flex justify-start pb-0.5">
                        <button
                          disabled={disabledShowButton}
                          onClick={() => {
                            setShow(true);
                            destructureMqttValues();
                            handleDisable();
                          }}
                          data-title="Click to Show Values"
                          className="bg-red-700 w-8 h-8 hover:bg-red-500 py-2 px-4 rounded-full flex justify-center "
                        >
                          <i class="ri-eye-fill text-white"></i>
                        </button>
                        <div className="self-center px-1 font-bold">
                          {" "}
                          Edit mode
                        </div>
                      </div>
                    )}

                    {show && (
                      <div className="flex justify-start mb-0.5">
                        <button
                          onClick={() => {
                            setShow(false);
                            resetValues();
                          }}
                          data-title="Click to Edit values"
                          className="bg-red-700 w-8 h-8 hover:bg-red-500  py-2 px-4 rounded-full flex justify-center"
                        >
                          <i class="ri-pencil-fill text-white"></i>
                        </button>
                        <div className="self-center px-1 font-bold">
                          {" "}
                          Show Mode{" "}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  {show ? (
                    <AnnotationShow
                      onSelect={() => {
                        /*console.log('onselect')*/
                      }}
                      onChange={() => {
                        /*console.log('onChange')*/
                      }}
                      color={data.color}
                      textColor={data.textColor}
                      annotationData={data.annotationData}
                      imgUrl={data.url}
                      mqttData={processValues}
                    />
                  ) : (
                    <ReactPictureAnnotation
                      onSelect={onSelect}
                      onChange={onChange}
                      image={data.url}
                      annotationStyle={{
                        ...defaultShapeStyle,
                        shapeStrokeStyle: data.color,
                        transformerBackground: data.color,
                        lineWidth: 5,
                        fontBackground: data.color,
                        fontColor: data.textColor,
                      }}
                      annotationData={annotationData}
                    />
                  )}
                </div>
              </div>
            </TableCell>
          </TableRow>
        )}
      </>

      {/* // Use of this dialog unKnown >> hense handleMqtt (discuss and remove) */}
      <Dialog open={assignCalib} fullWidth>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            padding: "1.2rem",
          }}
        >
          <DialogTitle>Assign Calibration Maintenance</DialogTitle>
          <Button onClick={() => setAssignCalib(false)}>
            <CloseIcon />
          </Button>
        </div>
        <DialogContent></DialogContent>
        <DialogActions>
          <Button
            onClick={() => handleMqtt()}
            style={{ backgroundColor: "#65C18C", color: "white" }}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={openDel}>
        <Delete onClose={() => setOpenDel(false)} onDelete={deleteData} />
      </Dialog>

      <Dialog open={openEdit} fullWidth>
        <DialogTitle>Edit Live Data - [{data.title}]</DialogTitle>
        <DialogContent>
          <EditLiveData
            handleClose={() => setOpenEdit(false)}
            mid={data.mid}
            data={data}
            machineName={machineName}
          />
        </DialogContent>
      </Dialog>

      <Dialog
        maxWidth="xl"
        onClose={() => setOpenAssign(false)}
        open={openAssign}
        fullWidth
        disableEnforceFocus
      >
        <DialogTitle>
          <div className="flex ">
            <i
              onClick={() => setOpenAssign(false)}
              className="ri-close-circle-line font-semibold text-2xl mr-1 hover:cursor-pointer hover:text-gray-600"
            ></i>
            Assign Values to Annotations
          </div>
        </DialogTitle>
        <DialogContent>
          {annotationData.map((dataItem, index) => (
            <>
              <SubItem
                dataItem={dataItem}
                calibrationValues={calibrationValues}
                processValues={processValues}
                annotationData={annotationData}
                data={data}
                onClose={() => setOpenAssign(false)}
              />
            </>
          ))}
        </DialogContent>
      </Dialog>

      <Dialog open={openGraph} maxWidth="xl" fullWidth>
        <div className="flex justify-between p-3">
          <div className="font-bold text-xl  ">Live Data Graphs</div>
          <IconButton onClick={() => setOpenGraph(false)}>
            <CloseIcon />
          </IconButton>
        </div>

        <div>
          <div className="px-2">
            <AnnotationChart
              processValues={processValues}
              activeChartFromSelected={activeChartFromSelected}
            />
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default Item;
