import { Button, InputLabel, TextField } from "@mui/material";
import React, { useState } from "react";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";

export const AddFAT = ({ docId, handleClose }) => {
  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");
  const [start, setStart] = useState("");
  const [stop, setStop] = useState("");
  const [time, setTime] = useState("");
  const [comment, setComment] = useState("");
  const [tolerance, setTolerance] = useState("");
  const [type, setType] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = {
      title,
      start,
      stop,
      tolerance,
      time,
      comment,
      createdAt: new Date(),
      docId: docId,
    };
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection("FAT")
    //   .add(data)
    //   .then(() => {
    //     handleClose();
    //   });
  };

  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Title</InputLabel>
      <TextField
        onChange={(e) => setTitle(e.target.value)}
        value={title}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Start Value</InputLabel>
      <TextField
        onChange={(e) => setStart(e.target.value)}
        value={start}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Stop Value</InputLabel>
      <TextField
        onChange={(e) => setStop(e.target.value)}
        value={stop}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Time</InputLabel>
      <TextField
        onChange={(e) => setTime(e.target.value)}
        value={time}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Tolerance</InputLabel>
      <TextField
        onChange={(e) => setTolerance(e.target.value)}
        value={tolerance}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Comment</InputLabel>
      <TextField
        onChange={(e) => setComment(e.target.value)}
        value={comment}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />

      <div className="p-2 mt-2 flex justify-between">
        <Button onClick={handleClose} variant="outlined">
          Cancel
        </Button>
        <Button type="submit" variant="outlined">
          Submit
        </Button>
      </div>
    </form>
  );
};

export const AddSAT = ({ docId, handleClose }) => {
  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");
  const [start, setStart] = useState("");
  const [stop, setStop] = useState("");
  const [time, setTime] = useState("");
  const [comment, setComment] = useState("");
  const [tolerance, setTolerance] = useState("");
  const [type, setType] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = {
      title,
      start,
      stop,
      tolerance,
      time,
      comment,
      createdAt: new Date(),
      docId: docId,
    };
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection("SAT")
    //   .add(data)
    //   .then(() => {
    //     handleClose();
    //   });
  };

  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Title</InputLabel>
      <TextField
        onChange={(e) => setTitle(e.target.value)}
        value={title}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Start Value</InputLabel>
      <TextField
        onChange={(e) => setStart(e.target.value)}
        value={start}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Stop Value</InputLabel>
      <TextField
        onChange={(e) => setStop(e.target.value)}
        value={stop}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Time</InputLabel>
      <TextField
        onChange={(e) => setTime(e.target.value)}
        value={time}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Tolerance</InputLabel>
      <TextField
        onChange={(e) => setTolerance(e.target.value)}
        value={tolerance}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Comment</InputLabel>
      <TextField
        onChange={(e) => setComment(e.target.value)}
        value={comment}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />

      <div className="p-2 mt-2 flex justify-between">
        <Button onClick={handleClose} variant="outlined">
          Cancel
        </Button>
        <Button type="submit" variant="outlined">
          Submit
        </Button>
      </div>
    </form>
  );
};

export default AddSAT;
