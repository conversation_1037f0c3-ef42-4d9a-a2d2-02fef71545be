import React from "react";
import { OTSubscriber } from "opentok-react";
import { Box, IconButton, Tooltip } from "@mui/material";
import MicIcon from "@mui/icons-material/Mic";
import MicOffIcon from "@mui/icons-material/MicOff";
import VideocamIcon from "@mui/icons-material/Videocam";
import VideocamOffIcon from "@mui/icons-material/VideocamOff";

class Subscriber extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      error: null,
      audio: true,
      video: true,
    };
  }

  setAudio = (audio) => {
    this.setState({ audio });
  };

  setVideo = (video) => {
    this.setState({ video });
  };

  onError = (err) => {
    this.setState({ error: `Failed to subscribe: ${err.message}` });
  };

  render() {
    return (
      <Box className="subscriber">
        {this.state.error ? (
          <Box className="errorMessage">{this.state.error}</Box>
        ) : null}

        <OTSubscriber
          properties={{
            subscribeToAudio: this.state.audio,
            subscribeToVideo: this.state.video,
          }}
          onError={this.onError}
        />

        <Box className="controlsContainer">
          <Tooltip title={this.state.audio ? "Mute Audio" : "Unmute Audio"}>
            <IconButton
              onClick={() => this.setAudio(!this.state.audio)}
              className={`controlButton ${!this.state.audio ? "disabled" : ""}`}
            >
              {this.state.audio ? <MicIcon /> : <MicOffIcon />}
            </IconButton>
          </Tooltip>

          <Tooltip
            title={this.state.video ? "Turn Off Video" : "Turn On Video"}
          >
            <IconButton
              onClick={() => this.setVideo(!this.state.video)}
              className={`controlButton ${!this.state.video ? "disabled" : ""}`}
            >
              {this.state.video ? <VideocamIcon /> : <VideocamOffIcon />}
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    );
  }
}
export default Subscriber;
