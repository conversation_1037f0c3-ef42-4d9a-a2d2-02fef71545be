import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  Typography,
  Box,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
} from "@mui/material";
import {
  Image as ImageIcon,
  VideoLibrary as VideoIcon,
  AudioFile as AudioIcon,
  Close as CloseIcon,
  Search as SearchIcon,
  BrokenImage as BrokenImageIcon,
} from "@mui/icons-material";
import { convertBase64 } from "../../hooks/useBase64";
import { dbConfig } from "../../infrastructure/db/db-config";
import { ButtonBasicCancel } from "../../components/buttons/Buttons";

const GalleryModal = ({
  open,
  onClose,
  onSelectFile,
  files,
  format,
  currentMode,
}) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [filteredFiles, setFilteredFiles] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [imageErrors, setImageErrors] = useState(new Set());

  // Define supported MIME types
  const fileTypeToMime = {
    image: ["image/png", "image/jpeg", "image/jpg"],
    video: ["video/mp4", "video/mkv", "video/mov"],
    audio: ["audio/mp3", "audio/mpeg"],
  };

  useEffect(() => {
    if (files && format) {
      console.log("All files:", files);
      console.log("Selected format:", format);
      
      const filtered = files.filter((file) => {
        const matchesMimeType = fileTypeToMime[format]?.includes(file.file_type);
        const matchesFormat = format === file.format; // Check if your API uses 'format' field
        const matchesFileType = format === file.file_type;
        
        console.log(`File: ${file.name}, file_type: ${file.file_type}, format: ${file.format}, matches: ${matchesMimeType || matchesFormat || matchesFileType}`);
        
        return matchesMimeType || matchesFormat || matchesFileType;
      });
      
      console.log("Filtered files:", filtered);
      setFilteredFiles(filtered);
    }
  }, [files, format]);

  useEffect(() => {
    if (searchTerm && filteredFiles.length > 0) {
      const searchFiltered = filteredFiles.filter((file) =>
        file.name?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredFiles(searchFiltered);
    } else if (files && format) {
      // Re-apply the original filter when search is cleared
      const filtered = files.filter((file) => {
        const matchesMimeType = fileTypeToMime[format]?.includes(file.file_type);
        const matchesFormat = format === file.format;
        const matchesFileType = format === file.file_type;
        return matchesMimeType || matchesFormat || matchesFileType;
      });
      setFilteredFiles(filtered);
    }
  }, [searchTerm, files, format]);

  const handleFileSelect = (file) => {
    setSelectedFile(file);
  };

  const handleConfirmSelection = () => {
    if (selectedFile) {
      onSelectFile(selectedFile);
      handleClose();
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setSearchTerm("");
    setImageErrors(new Set());
    onClose();
  };

  const getFileIcon = (fileType, size = 40) => {
    const iconStyle = { fontSize: size, color: currentMode === "Dark" ? "#fff" : "#666" };
    
    if (fileType?.startsWith("image/") || fileType === "image") return <ImageIcon style={iconStyle} />;
    if (fileType?.startsWith("video/") || fileType === "video") return <VideoIcon style={iconStyle} />;
    if (fileType?.startsWith("audio/") || fileType === "audio") return <AudioIcon style={iconStyle} />;
    return <ImageIcon style={iconStyle} />;
  };

  const getPreviewUrl = (file) => {
    if (!file.url) {
      console.log("No URL found for file:", file);
      return null;
    }

    console.log("Original file URL:", file.url);
    
    // Try different base URL patterns
    const possibleUrls = [
      file.url, // Original URL
      `${dbConfig.url_storage}/images/${file.url}`, // Your current pattern
      `${dbConfig.url_storage}/${file.url}`, // Without /images/
      `${dbConfig.url_storage}/files/${file.url}`, // With /files/
    ];

    // Return the first URL that looks valid
    for (const url of possibleUrls) {
      if (url.startsWith('http') || url.includes(dbConfig.url_storage)) {
        console.log("Using preview URL:", url);
        return url;
      }
    }

    return possibleUrls[1]; // Default to your current pattern
  };

  const handleImageError = (fileId, url) => {
    console.log(`Image failed to load for file ${fileId}, URL: ${url}`);
    setImageErrors(prev => new Set([...prev, fileId]));
  };

  const isImageFile = (file) => {
    return file.file_type?.startsWith("image/") || 
           file.format === "image" || 
           file.file_type === "image";
  };

  const isVideoFile = (file) => {
    return file.file_type?.startsWith("video/") || 
           file.format === "video" || 
           file.file_type === "video";
  };

  const renderPreview = (file) => {
    const previewUrl = getPreviewUrl(file);
    const hasImageError = imageErrors.has(file._id);
    console.log("previewUrl:", previewUrl, "hasImageError:", hasImageError);

    if (isImageFile(file) && previewUrl && !hasImageError) {
      return (
        <CardMedia
          component="img"
          height="150"
          image={previewUrl}
          alt={file.name}
          style={{ 
            objectFit: "cover",
            width: "100%"
          }}
          onError={() => handleImageError(file._id, previewUrl)}
          onLoad={() => console.log(`Image loaded successfully: ${previewUrl}`)}
        />
      );
    } else if (isVideoFile(file) && previewUrl && !hasImageError) {
        return (
          <Box
            style={{
              position: "relative",
              width: "100%",
              height: "150px",
              backgroundColor: "#1a1a1a",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundImage: file.thumbnail ? `url(${file.thumbnail})` : 'none',
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
          >
            <Box
              style={{
                backgroundColor: "rgba(0,0,0,0.8)",
                borderRadius: "50%",
                padding: "12px",
              }}
            >
              <VideoIcon style={{ color: "white", fontSize: 28 }} />
            </Box>
            <Typography
              variant="caption"
              style={{
                position: "absolute",
                bottom: "8px",
                left: "8px",
                backgroundColor: "rgba(0,0,0,0.8)",
                color: "white",
                padding: "2px 6px",
                borderRadius: "3px",
                fontSize: "10px",
              }}
            >
              {file.file_type?.toUpperCase() || 'VIDEO'}
            </Typography>
          </Box>
        )} else {
      // Fallback icon display
      return (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          height="100%"
        >
          {hasImageError ? (
            <>
              <BrokenImageIcon style={{ fontSize: 40, color: "#f44336" }} />
              <Typography variant="caption" style={{ marginTop: "8px", color: "#f44336" }}>
                Failed to load
              </Typography>
            </>
          ) : (
            <>
              {getFileIcon(file.file_type || file.format)}
              <Typography variant="caption" style={{ marginTop: "8px" }}>
                {(file.format || file.file_type || format).toUpperCase()}
              </Typography>
            </>
          )}
        </Box>
      );
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        style: {
          backgroundColor: currentMode === "Dark" ? "black" : "#fff",
          color: currentMode === "Dark" ? "white" : "black",
          minHeight: "70vh",
        },
      }}
    >
      <DialogTitle
        style={{
          backgroundColor: currentMode === "Dark" ? "#161C24" : "#f5f5f5",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h6">Select File from Gallery</Typography>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent style={{ padding: "20px" }}>
        {/* Debug Info - Remove this in production
        {process.env.NODE_ENV === 'development' && (
          <Box style={{ marginBottom: "10px", padding: "10px", backgroundColor: "#f0f0f0", fontSize: "12px" }}>
            <div>Total files: {files?.length || 0}</div>
            <div>Filtered files: {filteredFiles.length}</div>
            <div>Selected format: {format}</div>
            <div>Storage URL: {dbConfig.url_storage}</div>
          </Box>
        )} */}

        {/* Search Bar */}
        <TextField
          fullWidth
          placeholder="Search files..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          style={{ marginBottom: "20px" }}
        />

        {/* File Grid */}
        <Grid container spacing={2} style={{ maxHeight: "400px", overflow: "auto" }}>
          {filteredFiles.length === 0 ? (
            <Grid item xs={12}>
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                minHeight="200px"
              >
                <Typography variant="h6" color="textSecondary">
                  No {format} files found
                </Typography>
              </Box>
            </Grid>
          ) : (
            filteredFiles.map((file) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={file._id}>
                <Card
                  onClick={() => handleFileSelect(file)}
                  style={{
                    cursor: "pointer",
                    border:
                      selectedFile?._id === file._id
                        ? "3px solid #1976d2"
                        : "1px solid #e0e0e0",
                    backgroundColor:
                      selectedFile?._id === file._id
                        ? currentMode === "Dark"
                          ? "#1a237e"
                          : "#e3f2fd"
                        : currentMode === "Dark"
                        ? "#2c3e50"
                        : "#fff",
                    transition: "all 0.2s ease-in-out",
                    transform:
                      selectedFile?._id === file._id ? "scale(1.02)" : "scale(1)",
                  }}
                >
                  {/* Preview */}
                  <Box
                    style={{
                      height: "150px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: currentMode === "Dark" ? "#34495e" : "#f8f9fa",
                      overflow: "hidden",
                    }}
                  >
                    {renderPreview(file)}
                  </Box>

                  <CardContent style={{ padding: "12px" }}>
                    <Typography
                      variant="body2"
                      style={{
                        fontWeight: selectedFile?._id === file._id ? "bold" : "normal",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}
                      title={file.name}
                    >
                      {file.name || "Unnamed file"}
                    </Typography>
                    <Box
                      display="flex"
                      justifyContent="space-between"
                      alignItems="center"
                      marginTop="8px"
                    >
                      {/* <Chip
                        label={file.format || file.file_type || format}
                        size="small"
                        color={selectedFile?._id === file._id ? "primary" : "default"}
                      /> */}
                      {file.creator && (
                        <Typography variant="caption" color="textSecondary">
                          {file.creator}
                        </Typography>
                      )}
                    </Box>
                    {/* Debug info for each file - Remove in production */}
                    {/* {process.env.NODE_ENV === 'development' && (
                      <Typography variant="caption" style={{ fontSize: "10px", color: "#999" }}>
                        URL: {file.url}
                      </Typography>
                    )} */}
                  </CardContent>
                </Card>
              </Grid>
            ))
          )}
        </Grid>

        {/* Selected File Info */}
        {selectedFile && (
          <Box
            style={{
              marginTop: "20px",
              padding: "15px",
              backgroundColor: currentMode === "Dark" ? "#1a237e" : "#e3f2fd",
              borderRadius: "8px",
            }}
          >
            <Typography variant="subtitle1" style={{ fontWeight: "bold" }}>
              Selected: {selectedFile.name}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Type: {selectedFile.file_type || selectedFile.format} | Creator: {selectedFile.creator || "Unknown"}
            </Typography>
            <Typography variant="caption" style={{ fontSize: "11px" }}>
              URL: {selectedFile.url}
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions style={{ padding: "16px 24px" }}>
        <ButtonBasicCancel onClick={handleClose} buttonTitle="Cancel" />
        <Button
          onClick={handleConfirmSelection}
          variant="contained"
          color="primary"
          disabled={!selectedFile}
        >
          Select File
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GalleryModal;
