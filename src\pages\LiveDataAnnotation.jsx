import React, { useEffect, useState } from "react";
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
  Alert,
} from "@mui/material";
import { useParams } from "react-router-dom";
import { io } from "socket.io-client";
import AnnotationHeader from "../components/ImageAnnotation/AnnotationHeader";
import ImageAnnotation from "../components/ImageAnnotation/ImageAnnotation";
import AnnotationTags from "../components/ImageAnnotation/AnnotationTags";
import LiveDataChart from "../components/LiveData/LiveDataChart";
import { toastMessage, toastMessageSuccess } from "../tools/toast";
import { useStateContext } from "../context/ContextProvider";
import { sharedCss } from "../styles/sharedCss";
import AddTaskIcon from "@mui/icons-material/AddTask";
import AdjustIcon from "@mui/icons-material/Adjust";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import Carousel from "react-material-ui-carousel";
import { ButtonBasicCancel } from "../components/buttons/Buttons";
import axios from "axios";
import { dbConfig } from "../infrastructure/db/db-config";
import ErrorBoundary from "../components/ErrorBoundary";

const LiveDataAnnotation = () => {
  const [annotation, setAnnotation] = useState({});
  const [annotations, setAnnotations] = useState([]);
  const { id } = useParams();
  const [liveData, setLiveData] = useState([]);
  const [mode, setMode] = useState(true);
  const [mqttId, setMqttId] = useState("");
  const [mqttList, setMqttList] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [annColor, setAnnColor] = useState({
    background_color: "#DF2E38",
    text_color: "#FFFFFF",
    border_color: "",
  });
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const [socket, setSocket] = useState(null);

  // Fetch initial live data details
  const fetchLiveDataDetails = async () => {
    await axios
      .get(`${dbConfig.url}/imageAnnotationModules/${id}`)
      .then((response) => {
        console.log("response", response);
        const data = response.data?.data;
        setLiveData(data);
        setAnnotations(data?.annotation_data);
      })
      .catch((error) => {
        toastMessage({ message: error.message });
      });
  };

  // Fetch MQTT data
  const fetchLiveData2 = async () => {
    await axios
      .get(`${dbConfig.url}/livedata`)
      .then((response) => {
        const data = response.data?.data;
        setMqttList(data);
        setMqttId(data[0]._id);
      })
      .catch((error) => {
        toastMessage({ message: error.message });
      });
  };



  useEffect(() => {
    fetchLiveDataDetails();
    fetchLiveData2();
  }, []);

  const onSubmit = (annotation) => {
    if (mqttList.filter((item) => item?.mid === liveData?.mid).length === 0) {
      return toastMessage({
        message: "No Process Data Available to Annotate!",
      });
    } else {
      const { geometry, data } = annotation;
      setAnnotations(
        annotations.concat({
          geometry,
          data: {
            ...data,
            id: Math.random(),
            mqtt_id: mqttId,
            color: annColor,
          },
        }),
      );
      setAnnotation({});
    }
  };

  const updateAnnotation = async () => {
    await axios
      .put(`${dbConfig.url}/imageAnnotationModules/${liveData._id}`, {
        ...liveData,
        annotation_data: annotations,
      })
      .then(() => {
        toastMessageSuccess({ message: "Updated Annotations successfully!" });
      })
      .catch((error) => {
        toastMessage({ message: error.message });
      });
  };

  const commonCss = sharedCss();

  return (
    <ErrorBoundary>
      <Box
        className={commonCss.backgroundLight}
        style={
          currentMode === "Dark"
            ? { backgroundColor: "#161C24", color: "white" }
            : { backgroundColor: "" }
        }
      >
        <Box
          style={{
            justifyContent: "center",
            alignItems: "center",
            width: "100%",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box
            sx={{
              p: 1,
              mt: 4,
              width: "100%",
            }}
          >
            <AnnotationHeader
              mainMid={liveData?.mid}
              color={annColor}
              setColor={setAnnColor}
              mqttId={mqttId}
              mqttList={mqttList}
              setMqttId={setMqttId}
              mode={mode}
              setMode={setMode}
              save={updateAnnotation}
              heading={liveData?.title}
              openDialog={() => setOpenDialog(true)}
            />
            <Alert variant="outlined" severity={mode ? "info" : "warning"}>
              {mode
                ? "To Edit, change mode to Edit Mode!"
                : "Please Save after preview"}
            </Alert>
            <ImageAnnotation
              mqttList={mqttList}
              mode={mode}
              setMode={setMode}
              imgUrl={`${dbConfig?.url_storage}/${liveData?.img_url}`}
              annotation={annotation}
              onSubmit={onSubmit}
              annotations={annotations}
              setAnnotations={setAnnotations}
              setAnnotation={setAnnotation}
            />
          </Box>
        </Box>

        <Dialog
          maxWidth="xl"
          fullWidth
          open={openDialog}
          onClose={() => setOpenDialog(false)}
        >
          <DialogTitle>Live Data Details</DialogTitle>
          <DialogContent>
            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <IconButton disabled={mode} onClick={updateAnnotation}>
                <AddTaskIcon /> <span className="text-sm"> Save</span>
              </IconButton>
              <IconButton onClick={() => setMode(!mode)}>
                <AdjustIcon
                  style={mode ? { color: "red" } : { color: "green" }}
                />{" "}
                <span className="text-sm uppercase">
                  {mode ? "LIVE" : "EDIT"}
                </span>
              </IconButton>
            </Box>
            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <Box
                sx={{
                  border:
                    currentMode === "Dark"
                      ? `1px solid white`
                      : `1px solid black`,
                  width: "43%",
                  mt: 4,
                }}
              >
                <AnnotationTags
                  mainMid={liveData?.mid}
                  mqttList={mqttList}
                  currentMode={currentMode}
                  mode={mode}
                  currentColorLight={currentColorLight}
                  setAnnotations={setAnnotations}
                  annotations={annotations}
                />
              </Box>

              <Box
                sx={{
                  border:
                    currentMode === "Dark"
                      ? `1px solid white`
                      : `1px solid black`,
                  width: "55%",
                  maxHeight: "700px",
                  mt: 4,
                  p: 2,
                }}
              >
                <Typography gutterBottom variant="h5">
                  <b>Chart </b>
                </Typography>
                <hr />
                <Carousel
                  NextIcon={<NavigateNextIcon />}
                  PrevIcon={<NavigateBeforeIcon />}
                  fullHeightHover={false}
                  cycleNavigation={false}
                  navButtonsProps={{
                    style: {
                      backgroundColor: "#666",
                      color: "#fff",
                      borderRadius: "50%",
                      padding: ".75rem",
                      margin: "0 1.5rem",
                    },
                  }}
                  indicators={false}
                  autoPlay={false}
                  animation="slide"
                  duration="800"
                  navButtonsAlwaysVisible={true}
                  className={`carousel`}
                >
                  {annotations.map((data) => (
                    <LiveDataChart
                      currentMode={currentMode}
                      currentColorLight={currentColorLight}
                      color={data.data.color.background_color}
                      text={data.data.text}
                      mqttId={data.data.mqtt_id}
                    />
                  ))}
                </Carousel>
              </Box>
            </Box>
          </DialogContent>
          <DialogActions>
            <ButtonBasicCancel
              buttonTitle="Close Window"
              onClick={() => setOpenDialog(false)}
            />
          </DialogActions>
        </Dialog>
      </Box>
    </ErrorBoundary>
  );
};

export default LiveDataAnnotation;
