import React, { Children, useEffect, useState } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  RadioGroup,
  FormControlLabel,
  Radio,
  Typography,
  Grid,
  Card,
  CardHeader,
  CardContent,
  Tabs,
  Tab,
  Container,
  CircularProgress,
} from "@mui/material";
import { <PERSON><PERSON><PERSON>, <PERSON>, Pie } from "react-chartjs-2";
import DashboardGrid from "./DashboardGrid";
import DownTimeKPI from "./DowntimeKPI";
import OeeForm from "../../components/oee/oee-form";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useParams } from "react-router-dom";
import MachineDataHeader from "../machineData/MachineDataHeader";
import NotAccessible from "../../components/not-accessible/not-accessible";
import { useCheckAccess } from "../../utils/useCheckAccess";

const TimelineComponent = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState("manufacturing");
  const [oeeData, setOeeData] = useState([]);
  const { mid } = useParams();
  const [refresh, setRefresh] = useState(1);

  const getOeeData = async () => {
    await axios.get(`${dbConfig.url}/oee`).then((response) => {
      const data = response.data;
      const filteredData = data?.filter((item) => item.mid === mid)[0];
      setOeeData(filteredData);
      console.log("OEEE >>  ", filteredData);
    });
  };

  const hasOeeGETAccess = useCheckAccess("oee", "GET");

  useEffect(() => {
    getOeeData();
  }, [refresh]);
  const handleOpen = () => {
    setDialogOpen(true);
  };

  const handleClose = () => {
    setDialogOpen(false);
  };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const NoDataOrLoadingContainer = ({ children }) => (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        margin: "5rem",
      }}
    >
      {children}
    </div>
  );

  if (!oeeData) {
    return (
      <>
        <MachineDataHeader />
        <NoDataOrLoadingContainer>
          <Typography>No OEE Data Available</Typography>
        </NoDataOrLoadingContainer>
      </>
    );
  }

  if (oeeData.length === 0) {
    return (
      <>
        <MachineDataHeader />
        <NoDataOrLoadingContainer>
          <CircularProgress />
        </NoDataOrLoadingContainer>
      </>
    );
  }

  // Destructure properties with null checks
  const {
    plannedProductionTime,
    stopTime,
    idealCycleTime,
    totalCount,
    runTime,
    goodCount,
    maintenanceRecords,
    operatorInfo,
    performanceTargets,
    changeoverTimes,
    qualityMetrics,
  } = oeeData || {};

  const refreshThem = () => {
    setRefresh(refresh + 1);
  };

  // Calculate Availability in percentage with null checks
  const availability =
    runTime && plannedProductionTime
      ? ((runTime - stopTime) / plannedProductionTime) * 100
      : 0;
  const availabilityFormatted = availability.toFixed(2);

  // Calculate Performance in percentage with null checks
  const performance =
    idealCycleTime && totalCount && runTime
      ? ((idealCycleTime * totalCount) / runTime) * 100
      : 0;
  const performanceFormatted = performance.toFixed(2);

  // Calculate Quality in percentage with null checks
  const quality = goodCount && totalCount ? (goodCount / totalCount) * 100 : 0;
  const qualityFormatted = quality.toFixed(2);

  // Calculate Overall OEE with null checks
  const overallOEE =
    availability && performance && quality
      ? (
          (availability / 100) *
          (performance / 100) *
          (quality / 100) *
          100
        ).toFixed(2)
      : 0;

  // Revised calculation functions
  const calculateAvailability = () => {
    if (!oeeData || !oeeData.plannedProductionTime || !oeeData.stopTime)
      return "0";
    const { plannedProductionTime, stopTime } = oeeData;
    const runTime = plannedProductionTime - stopTime;
    return ((runTime / plannedProductionTime) * 100).toFixed(2);
  };

  const calculatePerformance = () => {
    if (
      !oeeData ||
      !oeeData.idealCycleTime ||
      !oeeData.totalCount ||
      !oeeData.runTime
    )
      return "0";
    const { idealCycleTime, totalCount, runTime } = oeeData;
    const theoreticalMaxOutput = (runTime * 60) / idealCycleTime;
    return ((totalCount / theoreticalMaxOutput) * 100).toFixed(2);
  };

  const calculateQuality = () => {
    if (!oeeData || !oeeData.goodCount || !oeeData.totalCount) return "0";
    const { goodCount, totalCount } = oeeData;
    return ((goodCount / totalCount) * 100).toFixed(2);
  };

  const calculateOverallOEE = () => {
    const availability = parseFloat(calculateAvailability()) / 100;
    const performance = parseFloat(calculatePerformance()) / 100;
    const quality = parseFloat(calculateQuality()) / 100;
    return (availability * performance * quality * 100).toFixed(2);
  };
  // Data for OEE Overall Doughnut Chart
  const overallOeeData = {
    labels: ["Availability", "Performance", "Quality"],
    datasets: [
      {
        data: [
          calculateAvailability(),
          calculatePerformance(),
          calculateQuality(),
        ],
        backgroundColor: ["#4CAF50", "#FF5722", "#FFC107"],
      },
    ],
  };

  // Data for Availability Bar Chart
  const availabilityData = {
    labels: ["Planned Runtime", "Actual Runtime", "Planned Downtime"],
    datasets: [
      {
        label: "Availability",
        data: [plannedProductionTime || 0, runTime || 0, stopTime || 0],
        backgroundColor: ["#2196F3", "#4CAF50", "#FF5722"],
      },
    ],
  };

  // Data for Performance Bar Chart
  const performanceData = {
    labels: ["Planned Quantity", "Actual Quantity"],
    datasets: [
      {
        label: "Performance",
        data: [totalCount || 0, goodCount || 0],
        backgroundColor: ["#2196F3", "#4CAF50"],
      },
    ],
  };

  // Data for Quality Bar Chart
  const qualityData = {
    labels: ["Actual Quantity", "Rejected Quantity"],
    datasets: [
      {
        label: "Quality",
        data: [goodCount || 0, totalCount - goodCount || 0],
        backgroundColor: ["#4CAF50", "#FF5722"],
      },
    ],
  };

  return (
    <div>
      <MachineDataHeader />
      <Typography mt={5} variant="h6">
        (OEE) Machine Timeline Dashboard
      </Typography>
      <Container>
        <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
          <Tabs value={selectedTab} onChange={handleTabChange}>
            <Tab label="Manufacturing KPI" value="manufacturing" />
            <Tab label="Downtime KPI" value="downtime" />
            <Tab label="OEE Form" value="form" />
          </Tabs>
        </Box>

        {selectedTab === "manufacturing" &&
          (hasOeeGETAccess ? (
            <>
              <DashboardGrid oeeData={oeeData} />
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Card elevation={0}>
                    <CardHeader title="Availability" />
                    <CardContent>
                      <Bar data={availabilityData} />
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card elevation={0}>
                    <CardHeader title="Performance" />
                    <CardContent>
                      <Bar data={performanceData} />
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card elevation={0}>
                    <CardHeader title="Quality" />
                    <CardContent>
                      <Bar data={qualityData} />
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card elevation={0}>
                    <CardHeader title="OEE Overall" />
                    <CardContent>
                      <Doughnut data={overallOeeData} />
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
              <Card elevation={0} sx={{ marginTop: 2 }}>
                <CardHeader title="Maintenance Records" />
                <CardContent>
                  {/* Display Maintenance Records */}
                  {maintenanceRecords &&
                    maintenanceRecords.map((record, index) => (
                      <div key={index} style={{ marginBottom: 16 }}>
                        <Typography variant="subtitle1">
                          Maintenance Record #{index + 1}
                        </Typography>
                        <Typography variant="body1">
                          Maintenance Date: {record.maintenanceDate}
                        </Typography>
                        <Typography variant="body1">
                          Maintenance Type: {record.maintenanceType}
                        </Typography>
                        <Typography variant="body1">
                          Comments: {record.comments}
                        </Typography>
                      </div>
                    ))}
                </CardContent>
              </Card>

              <Card elevation={0} sx={{ marginTop: 2 }}>
                <CardHeader title="Operator Information" />
                <CardContent>
                  {/* Display Operator Information */}
                  <Typography variant="body1">
                    Operator ID: {operatorInfo?.operatorId}
                  </Typography>
                  <Typography variant="body1">
                    Operator Name: {operatorInfo?.operatorName}
                  </Typography>
                </CardContent>
              </Card>

              <Card elevation={0} sx={{ marginTop: 2 }}>
                <CardHeader title="Performance Targets" />
                <CardContent>
                  {/* Display Performance Targets */}
                  <Typography variant="body1">
                    Target Cycle Time: {performanceTargets?.targetCycleTime}
                  </Typography>
                  <Typography variant="body1">
                    Target Output: {performanceTargets?.targetOutput}
                  </Typography>
                  <Typography variant="body1">
                    Changeover Times: {changeoverTimes}
                  </Typography>
                </CardContent>
              </Card>

              <Card elevation={0} sx={{ marginTop: 2 }}>
                <CardHeader title="Quality Metrics" />
                <CardContent>
                  {/* Display Quality Metrics */}
                  <Typography variant="body1">
                    Quality Metrics: {qualityMetrics}
                  </Typography>
                </CardContent>
              </Card>
            </>
          ) : (
            <NotAccessible />
          ))}
        {selectedTab === "downtime" &&
          (hasOeeGETAccess ? (
            <>
              <DownTimeKPI refreshThem={refreshThem} oeeData={oeeData} />
            </>
          ) : (
            <NotAccessible />
          ))}
        {selectedTab === "form" && (
          <OeeForm refreshThem={refreshThem} mid={mid} oeeData={oeeData} />
        )}
      </Container>
    </div>
  );
};

export default TimelineComponent;
