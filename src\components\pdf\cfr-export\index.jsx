import React from "react";
import {
  Page,
  View,
  Document,
  StyleSheet,
  Font,
  Text,
} from "@react-pdf/renderer";
import MyHeader from "../Header";
import { MyTable } from "../Table";
import moment from "moment";
import { CfrPdfTable } from "./table";
import { formatDateTime } from "../../../utils";
import PropTypes from "prop-types";

Font.register({
  family: "Open Sans",
  fonts: [
    {
      src: "https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-regular.ttf",
    },
    {
      src: "https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-600.ttf",
      fontWeight: 600,
    },
    {
      src: "https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-700.ttf",
      fontWeight: 700,
    },
    {
      src: "https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-800.ttf",
      fontWeight: 800,
    },
  ],
});

const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    paddingVertical: 10,
    paddingHorizontal: 10,
    // fontFamily: "Open Sans",
  },
  section: {
    width: "100%",
  },
  break: {
    paddingBottom: "10px",
    paddingTop: "10px",
  },
  footer: {
    fontSize: "8px",
    padding: "10px",
  },
});

// Create Document Component

const labelKeys = [
  {
    label: "Date/Time",
    key: "timestamp",
  },
  {
    label: "Module",
    key: "module",
  },
  {
    label: "Activity",
    key: "description",
  },
  {
    label: "User",
    key: "email",
  },
  {
    label: "Role",
    key: "role",
  },
];

const UNIT_NAME = "Gagillapur";
const TITLE = "Audit Trails";
const FOOTER_TEXT =
  "This document is computer generated and does not require any signature or stamp for validation";
const USER_CREDS_KEY = "@user-creds";
const PRINTED_BY_LABEL = "Printed By:";
const DATE_TIME_LABEL = "Date & Time:";

const AuditTrailsPDF = ({ data = [] }) => {
  data = data.slice(0, 400).map((d) => ({
    ...d,
    timestamp: moment(d.timestamp).format("DD/MM/YYYY HH:mm A"),
  }));
  const currentUser = JSON.parse(sessionStorage.getItem(USER_CREDS_KEY));

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.section}>
          <MyHeader UnitName={UNIT_NAME} title={TITLE} />
        </View>
        <View style={styles.break} />
        <View style={styles.section}>
          {/* <MyTable data={data} labelKeys={labelKeys} /> */}
          <CfrPdfTable data={data} labelKeys={labelKeys} />
        </View>
        <View style={styles.footer}>
          <Text>{FOOTER_TEXT}</Text>
          <Text>
            {PRINTED_BY_LABEL} {currentUser.email}
          </Text>
          <Text>
            {DATE_TIME_LABEL} {formatDateTime(new Date())}
          </Text>
        </View>
      </Page>
    </Document>
  );
};

AuditTrailsPDF.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      timestamp: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        .isRequired,
      module: PropTypes.string,
      description: PropTypes.string,
      email: PropTypes.string,
      role: PropTypes.string,
    }),
  ),
};
export default AuditTrailsPDF;
