import React, { useEffect, useState } from "react";
import ToggleButton from "../../components/ToggleButton/ToggleButton";
import { useAuth } from "../../hooks/AuthProvider";
import { useStateContext } from "../../context/ContextProvider";
import { makeStyles } from "@mui/styles";
import { Avatar, Box, Typography } from "@mui/material";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useUtils } from "../../hooks/UtilsProvider";

const useStyles = makeStyles((theme) => ({
  profileContainer: {
    backgroundColor: theme.palette.custom.backgroundForth,
    color: theme.palette.custom.textColor,
    borderRadius: "10px",
    display: "flex",
    gap: "4rem",
  },
}));

const ProfileContainer = () => {
  const { currentUser } = useAuth();
  const { envData, isFetching } = useUtils(); // Access envData and isFetching
  const [user, setUser] = useState(null); // Initialize as null for clarity
  const { currentMode, currentColorLight } = useStateContext();
  const [file, setFile] = useState(null);

  const background = {
    backgroundColor: currentMode === "Dark" ? "#161C24" : currentColorLight,
  };
  const id = currentUser?._id;

  // Helper to get role name from role ID
  const getRoleName = (roleValue, roles) => {
    if (!roles || roles.length === 0 || isFetching) return roleValue || "";
    const roleObj = roles.find((r) => r.id === Number(roleValue));
    return roleObj
      ? roleObj.name.charAt(0).toUpperCase() + roleObj.name.slice(1)
      : roleValue || "";
  };

  useEffect(() => {
    const fetchUserdetails = async () => {
      if (!id) return; // Guard against missing id
      try {
        const res = await axios.get(`${dbConfig.url}/users/${id}`);
        const data = res.data.data;
        setUser(data);
      } catch (error) {
        console.error("Error fetching user details:", error);
      }
    };
    fetchUserdetails();
  }, [id]);

  const classes = useStyles();

  return (
    <>
      <Box className={classes.profileContainer}>
        <Box className="flex flex-row gap-x-2 ml-4">
          <Box
            style={{ display: "flex", placeItems: "center" }}
            className="avatar"
          >
            <Avatar src={`${dbConfig?.url_storage}/images/${user?.avatar}`} alt="profile" />
          </Box>
          <Box
            style={{ display: "flex", placeItems: "center" }}
            className="description"
          >
            <Box className="name">
              <Typography component="p">
                {user?.fname + " " + user?.lname} -{" "}
                <Typography
                  component="span"
                  variant="subtitle2"
                  sx={{
                    color: "text.secondary",
                    textTransform: "capitalize",
                  }}
                >
                  {user?.lsi ? "LSI " : ""} (
                  {isFetching
                    ? "Loading..."
                    : getRoleName(user?.role, envData.ROLES)}
                  )
                </Typography>
              </Typography>
            </Box>
          </Box>
        </Box>
        <Box className="flex flex-row gap-x-2">
          <Box style={{ display: "flex", alignItems: "center", width: "7vw" }}>
            <Avatar
              src={`${dbConfig?.url_storage}/images/${user?.signature}`}
              alt="Signature"
              className="avatar"
              variant="square"
              sx={{ width: "7vw", objectFit: "contain" }}
            />
          </Box>
          <Box
            style={{ display: "flex", alignItems: "start" }}
            className="description"
          >
            <Box className="name">
              <Typography component="p">- Signature</Typography>
            </Box>
          </Box>
        </Box>
      </Box>
      <Box>{/* <input type="file"/> */}</Box>
    </>
  );
};

export default ProfileContainer;
