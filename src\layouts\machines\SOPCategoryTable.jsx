import React, { useState } from "react";
import {
  TableContainer,
  Paper,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  IconButton,
  Collapse,
  Box,
  Typography,
  Pagination,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import SOPStepsTable from "./SOPStepsTable";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import { commonRowStyle } from "../machineData/MaintenanceReportDataMain";

const SOPCategoryTable = ({
  categories,
  stepsByCategory,
  catPage,
  setCatPage,
  expandedCat,
  onExpandCat,
  stepPage,
  onStepPageChange,
  subStepPage,
  localObjectUrlMap,
  onSubStepPageChange,
  CATS_PER_PAGE,
  STEPS_PER_PAGE,
}) => {
  const [expandedManual, setExpandedManual] = useState(null);
  const [expandedStep, setExpandedStep] = useState({});
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  const handleExpandManual = (manualIdx) => {
    setExpandedManual(expandedManual === manualIdx ? null : manualIdx);
  };

  const handleExpandStep = (manualIdx, stepIdx) => {
    setExpandedStep((prev) => ({
      ...prev,
      [manualIdx]: prev[manualIdx] === stepIdx ? null : stepIdx,
    }));
  };

  const paged = (arr, page, perPage) =>
    arr.slice((page - 1) * perPage, page * perPage);

  // Define alternating colors for category rows
  const getRowBackgroundColor = (index) => {
    return index % 2 === 0 ? "#E8F0FE" : "#F5F7FA"; // Soft blue for even, light gray for odd
  };

  console.log("SOPCategoryTable categories:", categories);
  console.log("SOPCategoryTable stepsByCategory:", stepsByCategory);

  return (
    <TableContainer component={Paper} sx={commonOuterContainerStyle}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell
              sx={{
                fontWeight: "bold",
                fontSize: 18,
                py: 3,
                backgroundColor: "rgb(224, 224, 224)",
              }}
            >
              Category Name
            </TableCell>
            <TableCell
              sx={{
                fontWeight: "bold",
                fontSize: 18,
                py: 3,
                backgroundColor: "rgb(224, 224, 224)",
              }}
            >
              Description
            </TableCell>
            <TableCell
              sx={{
                fontWeight: "bold",
                fontSize: 18,
                py: 3,
                backgroundColor: "rgb(224, 224, 224)",
              }}
            >
              Expand
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {categories.map((cat, idx) => (
            <React.Fragment key={cat.name || idx}>
              <TableRow sx={commonRowStyle}>
                <TableCell sx={{ py: 2.5, pl: 4 }}>{cat.name}</TableCell>
                <TableCell sx={{ py: 2.5 }}>{cat.description}</TableCell>
                <TableCell sx={{ py: 2.5 }}>
                  <IconButton
                    onClick={() => onExpandCat(cat.name)}
                    aria-label={
                      expandedCat === cat.name
                        ? "Collapse category"
                        : "Expand category"
                    }
                    sx={{
                      "&:hover": { backgroundColor: "#D1E9FF" },
                      "& .MuiSvgIcon-root": { fontSize: 24 },
                    }}
                  >
                    {expandedCat === cat.name ? (
                      <ExpandLessIcon />
                    ) : (
                      <ExpandMoreIcon />
                    )}
                  </IconButton>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell colSpan={4} sx={{ p: 0, border: 0 }}>
                  <Collapse
                    in={expandedCat === cat.name}
                    timeout="auto"
                    unmountOnExit
                  >
                    <Box
                      sx={{
                        m: 2,
                        p: 2,
                        backgroundColor: "#CAE8BD",
                        borderRadius: "6px",
                        boxShadow: 1,
                      }}
                    >
                      <Typography
                        variant="h6"
                        sx={{ mb: 2, fontWeight: "bold", color: "#2B6CB0" }}
                      >
                        Manuals
                      </Typography>
                      <SOPStepsTable
                        manuals={stepsByCategory[cat.name] || []}
                        expandedManual={expandedManual}
                        onExpandManual={handleExpandManual}
                        expandedStep={expandedStep}
                        onExpandStep={handleExpandStep}
                        subStepPage={subStepPage}
                        localObjectUrlMap={localObjectUrlMap}
                        onSubStepPageChange={onSubStepPageChange}
                        stepPageCount={Math.ceil(
                          (stepsByCategory[cat.name] || []).length /
                            STEPS_PER_PAGE,
                        )}
                        onStepPageChange={(value) =>
                          onStepPageChange(cat.name, value)
                        }
                        catName={cat.name}
                      />
                    </Box>
                  </Collapse>
                </TableCell>
              </TableRow>
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
      {categories.length > CATS_PER_PAGE && (
        <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
          <Pagination
            count={Math.ceil(categories.length / CATS_PER_PAGE)}
            page={catPage}
            onChange={(_, value) => setCatPage(value)}
            size="small"
            sx={{ "& .MuiPaginationItem-root": { color: "#2B6CB0" } }}
          />
        </Box>
      )}
    </TableContainer>
  );
};

export default SOPCategoryTable;
