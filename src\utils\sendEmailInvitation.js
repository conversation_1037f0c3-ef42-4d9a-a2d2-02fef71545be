import emailjs from "@emailjs/browser";
import { toastMessage } from "../tools/toast";

// export const sendEmailInvitation = ({}) => {

// }

export const sendEmail = (e) => {
  e.preventDefault();

  emailjs
    .sendForm(
      "gmail",
      "template_3a12ff8",
      e.target,
      "user_gqmHsTLEHxh06fhWlDnqq",
    )
    .then(
      (result) => {
        console.log(result.text);
      },
      (error) => {
        toastMessage({ message: error.text });
      },
    );

  e.target.reset();
};
