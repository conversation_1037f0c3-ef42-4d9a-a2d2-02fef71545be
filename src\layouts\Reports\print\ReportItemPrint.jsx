import {
  <PERSON>alog,
  <PERSON>alogContent,
  DialogTitle,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import React, { useState } from "react";
import "./contentPagePrint.scss";
import ReportsTableForPrint from "../print/ReportsTableForPrint";
import Formatting from "../../../components/Editor/Formatting";
import { T1 } from "../../machineData/TableTemplates/TableTemplates";

const ReportItemPrint = ({ type, data }) => {
  return (
    <section key={`Reports-${data.id}`} className="contentViewPrintPage">
      <div className="allContentPreviewContainerPrintPage">
        <main className="contentPageMain ">
          <div className="contentMainContainer ">
            <div
              className={
                data.procedure?.length > 5 && data?.objective?.length > 350
                  ? "height90Vh  "
                  : ""
              }
            >
              {" "}
              {/* data.procedure?.length > 5 && data?.objective?.length > 350 */}
              <div className="content_sub-section">
                <header className="contentMainTitle">
                  <div className="capitalize">{data?.title}</div>
                </header>
                <div className="italic font-medium ">
                  {/* {data?.desc}  */}
                  {/* now Description is comming as object (as it has bold, etalic and pargraph identifires) */}
                  {data?.desc?.blocks?.length &&
                    data?.desc.blocks.map((options) => (
                      <Formatting key={options.id} data={options} />
                    ))}
                </div>
              </div>
              {/* data?.template -> remove this as we are keeping type instead */}
              {data?.template != "0" ||
                (data?.type === 0 && (
                  <>
                    {data?.objective && (
                      <div className="content_sub-section">
                        <div className="content_subtitle flex">
                          <h4 className="font-bold underline mr-3">
                            {" "}
                            OBJECTIVE:
                          </h4>
                          <p>{data?.objective}</p>
                        </div>
                      </div>
                    )}

                    {data?.method && (
                      <div className="content_sub-section">
                        <div className="content_subtitle flex">
                          <h4 className="font-bold underline mr-3"> METHOD:</h4>
                          <p>{data?.method}</p>
                        </div>
                      </div>
                    )}

                    {data?.pre && (
                      <div className="content_sub-section">
                        <div className="content_subtitle">
                          <h4 className="font-bold underline mr-3 mb-2">
                            {" "}
                            PREREQUISITES:
                          </h4>
                          {data.pre.map((point, idx) => (
                            <p className="mb-2" key={idx}>
                              <span className="font-bold ">{idx + 1}.</span>{" "}
                              {point}
                            </p>
                          ))}
                        </div>
                      </div>
                    )}

                    {data?.procedure && (
                      <div className="content_sub-section">
                        <div className="content_subtitle">
                          <h4 className="font-bold underline mr-3 mb-2">
                            {" "}
                            TEST PROCEDURE:
                          </h4>
                          {data.procedure.map((point, idx) => (
                            <p className="mb-1" key={idx}>
                              <span className="font-bold ">{idx + 1}.</span>{" "}
                              {point}
                            </p>
                          ))}
                        </div>
                      </div>
                    )}
                  </>
                ))}
              {/* <div style={{border:"1px solid black", height:"8.4rem" }}></div>  height:"8rem */}
            </div>

            {/* new component for print */}

            {/* { type === 'print' && */}
            <div
              className={
                data.procedure?.length > 5 && data?.objective?.length > 350
                  ? "height90Vh"
                  : " w-full"
              }
            >
              {" "}
              {/* data.procedure?.length > 5 && data?.objective.length) > 350*/}
              <ReportsTableForPrint type={type} fatDataId={data.id} />
            </div>
            {/* } */}

            {/* {data?.template != '0' ?
                        <div className={ (data.procedure?.length > 5 || data?.objective?.length > 35 ) ? "height90Vh bg-green-600" : " w-full"}> 
                        <T1 rowData={(JSON.parse(window.localStorage.getItem('table1'))).slice(0,2) }/>
                        </div>
                         : null } */}
          </div>
        </main>
      </div>
      <div className="flex justify-items-center  place-content-center ">
        ***
      </div>
    </section>
  );
};

export default ReportItemPrint;
