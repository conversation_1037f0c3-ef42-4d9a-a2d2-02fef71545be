.dashboardPage {
  width: 100%;
  display: flex;
  justify-content: space-between;
  color: #344767;

  .leftContainer {
    width: 73%;
    display: flex;
    flex-direction: column;

    .fileManagerHeading,
    .machineSectionHeading {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 1.2rem;
      margin-top: 2rem;

      .info {
        h3 {
          font-size: 1.2rem;
          font-weight: 500;
          opacity: 0.9;
        }

        p {
          margin-top: 1rem;
          font-size: 0.9rem;
          opacity: 0.9;
        }
      }
    }

    // Machines Section
    .machinesSection {
      width: 100%;
      background-color: #fff;
      box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      color: #344767;

      .title {
        padding: 0 1rem;
        padding-top: 1.5rem;
        margin-bottom: 1rem;

        h3 {
          font-size: 1.2rem;
          font-weight: 500;
          opacity: 0.9;
        }
      }

      .desc {
        padding: 0 1rem;

        p {
          font-size: 0.9rem;
          opacity: 0.7;
        }
      }

      .machinesOuterContainer {
        padding: 1.2rem 0;

        .machinesInnerContainer {
          width: 100%;
          display: flex;
          flex-flow: row wrap;
          display: flex;
          align-items: center;
          justify-content: center;

          .singleMachineContainer {
            width: 30%;
            height: 224px;
            padding: 0.5rem 0.9rem;
            border-radius: 16px;
            margin: 0.75rem;

            .machineDescContainer {
              margin-top: 1rem;
              padding-left: 0.5rem;

              .machineBlock {
                font-size: 0.7rem;
                font-weight: 400;
                opacity: 0.95;
                text-transform: capitalize;
              }

              .machineCommon {
                align-self: flex-start;
                width: 50%;
                font-size: 0.7rem;
                font-weight: 800;
                text-transform: capitalize;
              }

              .machineModel {
                font-size: 0.7rem;
                font-weight: 400;
                opacity: 0.95;
                text-transform: capitalize;
              }

              .machineEquipmentId {
                font-size: 0.7rem;
                font-weight: 400;
                opacity: 0.95;
                text-transform: capitalize;
              }

              .machineName {
                font-size: 1.25rem;
                font-weight: 500;
              }

              .machineDesc {
                margin: 0.25rem 0;
                font-size: 0.9rem;
                font-weight: 400;
                opacity: 0.85;
                letter-spacing: 1px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                /* number of lines to show */
                -webkit-box-orient: vertical;
              }

              .machineBtns {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 1.5rem;
                .right {
                  .rightBtn {
                    border: none;
                    background: #fff;
                    cursor: pointer;
                    margin: 0 0.25rem;

                    i {
                      font-size: 1.2rem;
                    }

                    &:hover {
                      transform: scale(1.1);
                    }
                  }

                  .editBtn {
                    i {
                      color: rgb(68, 75, 118);
                    }
                  }

                  .delBtn {
                    i {
                      color: rgba(240, 2, 2, 0.801);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .allMachineLink {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }

  // File Manager section
  .fileManagerSection {
    width: 100%;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    color: #344767;
    margin-top: 1.5rem;

    .foldersCardContainer,
    .filesCardContainer {
      padding: 1.2rem 0;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-flow: row wrap;

      .folderCard,
      .fileCard {
        width: 30%;
        border-radius: 10px;
        margin: 1.1rem 1.1rem;
        margin-left: 0;
        padding: 1rem;

        &:last-child {
          margin-right: 0;
        }

        .folderInfoContainer {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 0.5rem 0;

          .folderImgContainer {
            width: 50%;
            height: 30%;
            user-select: none;

            img {
              user-select: none;
              width: 100%;
              height: 100%;
              cursor: pointer;
            }
          }

          .folderTitleContainer {
            padding: 0.8rem 0;
            text-align: center;
            font-size: 0.9rem;
            font-weight: 500;
          }
        }

        .folderBtnContainer {
          padding-top: 1rem;
          padding-bottom: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .info {
            display: flex;
            flex-direction: column;

            h4 {
              font-size: 0.8rem;
              opacity: 0.9;
            }

            p {
              margin-top: 0.3rem;
              font-size: 0.75rem;
              opacity: 0.9;
            }
          }

          .btns {
            display: flex;
            align-items: center;

            .btn {
              cursor: pointer;
              padding: 0.3rem;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 5px;
              color: #fff;

              i {
                font-size: 0.8rem;
              }
            }

            .editBtn {
              background: #098def;
            }

            .deleteBtn {
              background: rgba(245, 29, 29, 0.884);
              margin-left: 0.6rem;
            }
          }
        }
      }
    }
  }
}

// Right side
.rightContainer {
  display: flex;
  flex-direction: column;
  width: 25%;

  // Admin container
  .adminsListContainer {
    margin-top: 1.5rem;
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.05) 0rem 1.25rem 1.6875rem 0rem;
    border-radius: 10px;
    padding: 1rem;
    min-height: 250px;

    .heading {
      font-size: 1rem;
      font-weight: 500;
      opacity: 0.9;
      padding-left: 0.6rem;
      margin-bottom: 0.4rem;
    }

    .adminsBoxWrapper {
      display: flex;
      flex-direction: column;

      .adminsList {
        display: flex;
        align-items: center;
        padding: 0.5rem 0.8rem;
        margin: 0.5rem 0;
        cursor: pointer;
        border-radius: 10px;

        &:hover {
          background-color: rgba(94, 122, 170, 0.35);
        }

        .iconContainer {
          width: 35px;
          height: 35px;
          margin-right: 0.5rem;

          img {
            width: 100%;
            height: 100%;
            border-radius: 8px;
          }
        }

        .adminNameContainer {
          font-size: 1rem;
          font-weight: 500;
        }
      }
    }
  }
}

.hashtags {
  position: absolute;
  bottom: 1.5rem;
  right: 6rem;
  border-radius: 1rem;
}
