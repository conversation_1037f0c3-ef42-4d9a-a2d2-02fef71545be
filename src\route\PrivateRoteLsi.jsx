import React from "react";
import { Route, Navigate } from "react-router-dom";
import { useAuth } from "../hooks/AuthProvider";

const PrivateRouteLsi = ({ component: Component, ...rest }) => {
  const { currentUser, adminType } = useAuth();
  const companyId = window.localStorage.companyId
    ? window.localStorage.companyId
    : "undefined"; // try to take it from context
  return (
    <Route
      {...rest}
      render={(props) => {
        return (
          <>
            {currentUser && companyId == "undefined" ? (
              <Component {...props} />
            ) : null}{" "}
            {/* can go to any page spesified */}
            {!currentUser && !companyId ? (
              <Navigate to="/company-id" replace />
            ) : null}
          </>
        );
      }}
    ></Route>
  );
};

export default PrivateRouteLsi;
