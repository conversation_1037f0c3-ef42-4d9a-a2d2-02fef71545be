.videoSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 10px;
  background-color: #f5f7fa;

  .videoContainer {
    width: 100%;
    max-width: 1200px;
    border-radius: 16px !important;
    overflow: hidden;
    background-color: #fff;
  }

  .videoContainerHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);

    .leftSide {
      .connectionStatus {
        font-size: 1rem;
        display: flex;
        align-items: center;

        strong {
          font-size: 1.1rem;
          margin-right: 0.5rem;
          color: #333;
        }
      }
    }

    .rightSide {
      .actionButtons {
        display: flex;
        gap: 10px;
      }
    }
  }

  .videoScreens {
    display: flex;
    justify-content: center;
    gap: 24px;
    padding: 24px;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
    }
  }

  .videoLabel {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    z-index: 10;
  }

  .publisherContainer {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .publisher,
  .subscriber {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    background-color: #f0f2f5;
    min-height: 360px;
    display: flex;
    align-items: center;
    justify-content: center;

    .OTPublisherContainer,
    .OTSubscriberContainer {
      width: 480px !important;
      height: 360px !important;
      border-radius: 12px !important;
      overflow: hidden;

      @media (max-width: 768px) {
        width: 100% !important;
        height: 300px !important;
      }
    }
  }

  .subscriberOuterContainer {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .subscriber {
      position: relative;
      border-radius: 12px;
      overflow: hidden;

      .OTSubscriberContainer {
        width: 480px !important;
        height: 360px !important;
        border-radius: 12px !important;
        overflow: hidden;

        @media (max-width: 768px) {
          width: 100% !important;
          height: 300px !important;
        }
      }
    }
  }

  .errorMessageContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 12px;

    .MuiAlert-root {
      width: 100%;
      max-width: 450px;
    }
  }

  .controlsContainer {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 6px 12px;
    border-radius: 24px;
    z-index: 100;

    .controlButton {
      color: white;
      background-color: rgba(255, 255, 255, 0.2);
      margin: 0 4px;
      padding: 8px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.3);
      }

      &.disabled {
        color: #ff5252;
        background-color: rgba(255, 82, 82, 0.2);

        &:hover {
          background-color: rgba(255, 82, 82, 0.3);
        }
      }

      &.active {
        color: #4caf50;
        background-color: rgba(76, 175, 80, 0.2);

        &:hover {
          background-color: rgba(76, 175, 80, 0.3);
        }
      }
    }
  }

  .subscriberCaptureButton {
    display: flex;
    justify-content: center;
    margin-top: 5px;
    margin-bottom: 10px;
  }
}
