import React from "react";
import {
  Box,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import { useEffect, useState } from "react";
import { dbConfig } from "../../infrastructure/db/db-config";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import axios from "axios";

function Edit3dModel({ onClose, editModelId, getRecentModel, machinesData }) {
  const [newModelName, setNewModelName] = useState("");
  const [newModelDescription, setNewModelDescription] = useState("");
  const [machineId, setMachineId] = useState("");
  // Store initial values to track changes
  const [initialModelName, setInitialModelName] = useState("");
  const [initialModelDescription, setInitialModelDescription] = useState("");
  const [initialMachineId, setInitialMachineId] = useState("");

  // Fetch initial data when component mounts
  useEffect(() => {
    const getModel = async () => {
      try {
        const response = await axios.get(
          `${dbConfig.url}/model/${editModelId}`,
        );
        const modelData = response.data.data;
        setNewModelName(modelData?.name || "");
        setNewModelDescription(modelData?.desc || "");
        setMachineId(modelData?.mid || "");
        // Set initial values
        setInitialModelName(modelData?.name || "");
        setInitialModelDescription(modelData?.desc || "");
        setInitialMachineId(modelData?.mid || "");
      } catch (error) {
        console.error("Error fetching model:", error);
      }
    };
    getModel();
  }, [editModelId]);

  // Determine if any field has changed
  const hasChanges =
    newModelName.trim() !== initialModelName.trim() ||
    newModelDescription.trim() !== initialModelDescription.trim() ||
    machineId !== initialMachineId;

  const firebaseUpdate = async () => {
    if (
      newModelName.trim() === "" ||
      newModelDescription.trim() === "" ||
      !machineId
    ) {
      toastMessageWarning({
        message: "Title, Description, or Machine cannot be empty",
      });
      return;
    }

    try {
      // Retrieve the existing document
      const response = await axios.get(`${dbConfig.url}/model/${editModelId}`);
      const existingDocument = response.data.data;

      // Prepare updated document
      const updatedDocument = {
        ...existingDocument,
        name: newModelName,
        desc: newModelDescription,
        mid: machineId,
      };

      // Update the document
      await axios.put(`${dbConfig.url}/model/${editModelId}`, updatedDocument);

      toastMessageSuccess({ message: "Updated Model successfully!" });

      // Refresh data
      if (getRecentModel) {
        await getRecentModel();
      }

      onClose();
    } catch (error) {
      console.error("Error updating document:", error);
      toastMessage({ message: "Failed to update model" });
      onClose();
    }
  };

  return (
    <Box>
      <TextField
        required
        sx={{ mb: 3, mt: 3 }}
        label="Title"
        fullWidth
        placeholder="eg: Vacuum PID"
        value={newModelName}
        onChange={(e) => setNewModelName(e.target.value)}
      />
      <TextField
        required
        sx={{ mb: 3 }}
        label="Description"
        fullWidth
        rows={4}
        multiline
        value={newModelDescription}
        onChange={(e) => setNewModelDescription(e.target.value)}
        placeholder="eg: This is the 3D Model of a Motor"
      />
      <FormControl fullWidth>
        <InputLabel>Machine</InputLabel>
        <Select
          required
          label="Machine"
          sx={{ mb: 3 }}
          value={machineId}
          onChange={(e) => setMachineId(e.target.value)}
        >
          {machinesData?.map((data) => (
            <MenuItem key={data?._id} value={data?._id}>
              {data?.title}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
        <Button variant="contained" onClick={onClose} color="error">
          Cancel
        </Button>
        <Button
          onClick={firebaseUpdate}
          variant="contained"
          disabled={
            !newModelName.trim() ||
            !newModelDescription.trim() ||
            !machineId ||
            !hasChanges
          }
        >
          Update
        </Button>
      </Box>
    </Box>
  );
}

export default Edit3dModel;
