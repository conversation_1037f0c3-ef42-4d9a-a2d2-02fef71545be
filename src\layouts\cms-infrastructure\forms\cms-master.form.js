import {
  Box,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Typography,
  Button,
  Dialog,
  DialogContent,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useContext } from "react";
import {
  cmsInfra,
  companies,
  companyId_constant,
} from "../../../constants/data";
import { db } from "../../../firebase";
import { CmsInfraContext } from "../../../services/cms-infrastructure/cms-infra.context";
import { firebaseLooper } from "../../../tools/tool";
import * as XLSX from "xlsx";
import BlindsClosedIcon from "@mui/icons-material/BlindsClosed";
import CalibrationDataForm from "./calibration-data.form";
import { toastMessageSuccess } from "../../../tools/toast";

const SapMasterForm = ({ equipItem }) => {
  const { cmsData } = useContext(CmsInfraContext);
  const [cmsId, setCmsId] = useState("");
  const [masterData, setMasterData] = useState([]);
  const [excelFile, setExcelFile] = useState(null);
  const [excelFileError, setExcelFileError] = useState(null);

  // submit
  const [excelData, setExcelData] = useState(null);
  // it will contain array of objects

  // handle File
  const fileType = [
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ];
  const handleFile = (e) => {
    let selectedFile = e.target.files[0];
    if (selectedFile) {
      // console.log(selectedFile.type);
      if (selectedFile && fileType.includes(selectedFile.type)) {
        let reader = new FileReader();
        reader.readAsArrayBuffer(selectedFile);
        reader.onload = (e) => {
          setExcelFileError(null);
          setExcelFile(e.target.result);
        };
      } else {
        setExcelFileError("Please select only excel file types");
        setExcelFile(null);
      }
    } else {
      console.log("plz select your file");
    }
  };

  // submit function
  const handleSubmit = (e) => {
    e.preventDefault();
    if (excelFile !== null) {
      const workbook = XLSX.read(excelFile, { type: "buffer" });
      const worksheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[worksheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);
      setExcelData(data);
    } else {
      setExcelData(null);
    }
  };

  const uploadExcelData = () => {};

  return (
    <div>
      {/* Excel data to table */}
      <form className="form-group" autoComplete="off" onSubmit={handleSubmit}>
        <label>
          <h5>Upload Excel file</h5>
        </label>
        <br></br>
        <input
          type="file"
          className="form-control"
          onChange={handleFile}
          required
        ></input>
        {excelFileError && (
          <div className="text-red-500" style={{ marginTop: 5 + "px" }}>
            {excelFileError}
          </div>
        )}
        <Button
          type="submit"
          variant="contained"
          color="primary"
          style={{ marginTop: 5 + "px" }}
        >
          Submit
        </Button>
      </form>
      {/* 
        <Box sx={{p: 1}}>
            <FormControl variant="outlined" fullWidth >
                <InputLabel>Select CMS</InputLabel>
                <Select label="Select CMS" onChange={(e) => setCmsId(e.target.value)}>
                    {
                        cmsData.map((item, idx) => (
                            <MenuItem key={idx+item?.id} value={item?.id}>{item?.name}</MenuItem>
                        ))
                    }
                </Select>
            </FormControl>
        </Box> */}
      <br />
      <Box sx={{ border: "1px solid black", p: 1 }}>
        <Typography sx={{ fontSize: "12px" }} align="center">
          Master Data For Incorporation / Replacing / Obsoleting / Updating
          Instrument in SAP{" "}
        </Typography>
      </Box>

      {/*Master Equipment used starts */}
      {/* Table ends master equipment */}

      <Box sx={{ border: "1px solid black", borderTop: "none", p: 1 }}>
        <Typography sx={{ fontSize: "12px" }}>
          Change Control / Incident report / Break down / PM order
          reference/Change request/QMS element:{" "}
        </Typography>
      </Box>

      {/* Result custom table */}
      <Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            border: "1px solid black",
            borderTop: "none",
            alignItems: { lg: "normal", md: "center", sm: "center" },
            flexWrap: { lg: "nowrap", md: "wrap", sm: "wrap" },
          }}
        >
          {/* Sr no0 */}
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Instrument Code No{" "}
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Equipment Sort Field
            </Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Instrument Desc.& range{" "}
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Make
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Location
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Equipment/ Plant
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Frequency
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Order
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Certificate No.
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Calibration Done on
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Due Date
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Least Count
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Unit of Measure
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Error Claimed
            </Typography>
          </Box>

          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Remarks
            </Typography>
          </Box>
          <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
            <Typography sx={{ fontSize: "12px" }} align="center">
              Calibration Form
            </Typography>
          </Box>
        </Box>
      </Box>
      {/* Data Mapping  */}
      {masterData &&
        masterData?.map((item, idx) => (
          <MasterItem eqItem={equipItem} item={item} />
        ))}

      {excelData &&
        excelData?.map((item, idx) => (
          <MasterItem eqItem={equipItem} item={item} />
        ))}
      <Button
        onClick={() => uploadExcelData()}
        variant="contained"
        color="primary"
        style={{ marginTop: 5 + "px" }}
      >
        Upload
      </Button>
    </div>
  );
};

export default SapMasterForm;

const MasterItem = ({ item, eqItem }) => {
  const [open, setOpen] = useState(false);
  const { cmsData } = useContext(CmsInfraContext);

  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          border: "1px solid black",
          borderTop: "none",
          flexWrap: { lg: "nowrap", md: "wrap", sm: "wrap" },
        }}
      >
        {/* Sr no0 */}
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {" "}
            {item?.code_no}{" "}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.sort_field}
          </Typography>
        </Box>

        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.desc_range}{" "}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.make}{" "}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.location}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.plant}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.frequency}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.order}
          </Typography>
        </Box>

        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.certificate_no}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.done_on}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.due_date}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.least_count}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.unit}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.error}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            {item?.remarks}
          </Typography>
        </Box>
        <Box sx={{ p: 1, borderRight: "1px solid black", width: "15%" }}>
          <Typography sx={{ fontSize: "12px" }} align="center">
            <BlindsClosedIcon onClick={() => setOpen(true)} />
          </Typography>
        </Box>
      </Box>
      <Dialog
        maxWidth="xl"
        onClose={() => setOpen(false)}
        open={open}
        fullWidth
      >
        <DialogContent>
          <CalibrationDataForm
            masterEquip={cmsData?.filter(
              (eq) => item?.code_no == eq?.instrument_code,
            )}
            item={item}
          />
        </DialogContent>
      </Dialog>
    </Box>
  );
};
