import React, { useEffect, useMemo, useState } from "react";
import "./machineData.scss";
import ChangeOverReportItem from "./ChangeOverReportItem";
import {
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  Typography,
  Box,
} from "@mui/material";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { useStateContext } from "../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { makeStyles } from "@mui/styles";
import MachineDropdown from "../Reports/MachineDropdown";
import TableHeader from "./TableHeader";
import NoDataComponent from "../../components/commons/noData.component";
import BasicMenu from "../../components/menus/BasicMenu";
import { DateRangePicker } from "react-date-range";
import useDateFilter from "./useDateFilter";
import moment from "moment";
import { format } from "date-fns";
import { useAuth } from "../../hooks/AuthProvider";
import CommonDropDownComponent from "../../components/commons/dropDown.component";
import DateRangeMenu from "../../components/menus/DateRangeMenu";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import NotAccessible from "../../components/not-accessible/not-accessible";
import { useCheckAccess } from "../../utils/useCheckAccess";

const useCustomStyles = makeStyles((theme) => ({
  fatInnerContent: {
    padding: "0.5rem",
    backgroundColor: `${theme.palette.custom.backgroundForth} !important`,
  },
}));

function ChangeOverReportDataMain({ machineId, machineData, handleChangeId }) {
  const { currentUser } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [changeOverReportDataAll, setChangeOverReportDataAll] = useState([]);
  const { currentMode } = useStateContext();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState([]);
  const [stepData, setSD] = useState([]);
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  // State for user filter
  const [selectedUser, setSelectedUser] = useState("All");

  // Pagination states
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const { date, shadowOnDate, colorOnDate, dateFilter, resetDateFilter } =
    useDateFilter();

  // Format the date range for display
  const formattedDateRange = useMemo(() => {
    const start = date[0]?.startDate
      ? format(new Date(date[0].startDate), "dd MMM yyyy")
      : "N/A";
    const end = date[0]?.endDate
      ? format(new Date(date[0].endDate), "dd MMM yyyy")
      : "N/A";
    return `Date Range: ${start} - ${end}`;
  }, [date]);

  // Derive user options from currentUser list, filtered by report creators
  const userOptions = useMemo(() => {
    const reportEmails = new Set(
      changeOverReportDataAll.map((data) => data.email),
    );
    const filteredcurrentUser = reportEmails.has(currentUser.email)
      ? [currentUser]
      : [];
    return [
      { label: "All", value: "All" },
      ...filteredcurrentUser
        .map((user) => ({
          label: user.email.split("@")[0],
          value: user.email,
        }))
        .sort((a, b) => a.label.localeCompare(b.label)),
    ];
  }, [currentUser, changeOverReportDataAll]);

  const hasChangeOverReportGETAccess = useCheckAccess(
    "changeOverReport",
    "GET",
  );

  useEffect(() => {
    const getAllChangeOverData = async () => {
      try {
        await getAllChangeOverReports();
      } finally {
        setLoading(false);
      }
    };

    getAllChangeOverData();
  }, []);

  const getAllChangeOverReports = async () => {
    await axios
      .get(`${dbConfig.url}/changeOverReport`)
      .then((data) => {
        setChangeOverReportDataAll(data?.data?.data);
        console.log(
          "changeover report data from mongo : data:",
          data?.data?.data,
        );
      })
      .catch((e) => {
        console.log("error changeover report data from mongo : data:", e);
      });
  };

  const customCss = useCustomStyles();

  const filterChangeOverReportData = (data) => {
    const matchesSearch =
      searchTerm === "" ||
      data.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      data.email
        .split("@")[0]
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      data.comment.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesMachine = data.mid === machineId || machineId === "All";

    const startOfDay = moment(date[0]?.startDate).startOf("day");
    const endOfDay = moment(date[0]?.endDate).endOf("day");
    const isWithinDateRange = moment(data?.date).isBetween(
      startOfDay,
      endOfDay,
      undefined,
      "[]",
    );

    // User filter
    const matchesUser = selectedUser === "All" || data.email === selectedUser;

    return matchesSearch && matchesMachine && isWithinDateRange && matchesUser;
  };

  // Compute paginated reports
  const paginatedReports = useMemo(() => {
    const filtered = changeOverReportDataAll.filter(filterChangeOverReportData);
    const startIndex = page * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    return filtered.slice(startIndex, endIndex);
  }, [
    changeOverReportDataAll,
    page,
    rowsPerPage,
    searchTerm,
    machineId,
    date,
    selectedUser,
  ]);

  const filteredReportCount = useMemo(
    () => changeOverReportDataAll.filter(filterChangeOverReportData).length,
    [changeOverReportDataAll, searchTerm, machineId, date, selectedUser],
  );

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Add this function in ChangeOverReportDataMain
  const refreshChangeOverReports = async () => {
    try {
      const response = await axios.get(`${dbConfig.url}/changeOverReport`);
      setChangeOverReportDataAll(response?.data?.data);
    } catch (e) {
      console.log("error refreshing changeover report data:", e);
    }
  };

  return (
    <main className="allMachineDataPreviewContainer">
      <div
        className={`${customCss.fatInnerContent} liveDataOuterContainer border-radius-outer`}
      >
        <div className="liveDataHeading">
          <div
            className="title"
            style={{
              color: currentMode === "Dark" ? "#fff" : "#000",
            }}
          >
            Change Over Reports
          </div>
          <div style={{ display: "flex", alignItems: "center", gap: "1rem" }}>
            {/* Interactive Date Range Picker */}
            <DateRangeMenu
              textShadow={shadowOnDate}
              color={colorOnDate}
              fontSize="0.9rem"
              displayText={formattedDateRange}
              items={
                <DateRangePicker
                  onChange={(item) => dateFilter(item)}
                  showSelectionPreview={true}
                  moveRangeOnFirstSelection={false}
                  months={1}
                  ranges={date}
                  direction="horizontal"
                  maxDate={new Date()}
                />
              }
              resetDateFilter={resetDateFilter} // Pass the reset function
            />
            {/* User Dropdown */}
            <CommonDropDownComponent
              dropDownLabel={"Select User"}
              menuValueDefault={selectedUser}
              menuValue={selectedUser}
              menuItemValue={"value"}
              menuData={userOptions}
              menuItemDisplay={"label"}
              handleChange={(e) => setSelectedUser(e.target.value)}
              dropDownContainerStyle={{ minWidth: 200 }}
            />
            <MachineDropdown
              machineId={machineId}
              machineData={machineData}
              handleChangeId={handleChangeId}
            />
          </div>
        </div>

        {hasChangeOverReportGETAccess ? (
          <div className="liveDataContainer">
            <TableContainer
              component={Paper}
              className="table border-radius-inner"
              sx={commonOuterContainerStyle}
            >
              <Table
                style={{
                  minWidth: 650,
                  width: "100%",
                  backgroundColor: currentMode === "Dark" ? "#161C24" : "#fff",
                }}
              >
                <TableHeader
                  currentMode={currentMode}
                  columns={[
                    { label: "Title", align: "left", width: "30%" },
                    {
                      label: `Performed on`,
                      align: "left",
                      width: "15%",
                      filterComponent: (
                        <BasicMenu
                          textShadow={shadowOnDate}
                          color={colorOnDate}
                          fontSize="0.75rem"
                          items={
                            <DateRangePicker
                              onChange={(item) => dateFilter(item)}
                              showSelectionPreview={true}
                              moveRangeOnFirstSelection={false}
                              months={1}
                              ranges={date}
                              direction="horizontal"
                            />
                          }
                        />
                      ),
                    },
                    { label: "Done By", align: "left", width: "15%" },
                    { label: "Remarks", align: "left", width: "10%" },
                    { label: "Status", align: "center", width: "15%" },
                    { label: "Actions", align: "center" },
                  ]}
                />

                <TableBody>
                  {loading ? (
                    <NoDataComponent
                      cellColSpan={7}
                      cellRowSpan={4}
                      dataLoading={loading}
                    />
                  ) : filteredReportCount > 0 ? (
                    paginatedReports.map((data, index) => (
                      <ChangeOverReportItem
                        key={data._id + index}
                        data={data}
                        userName={`${user?.fname} ${user?.lname}`}
                        stepData={stepData
                          .filter((d) => d.manual_id === data._id)
                          .sort((a, b) => a.index - b.index)}
                        machineData={machineData}
                        setChangeOverReportDataAll={setChangeOverReportDataAll}
                        refreshChangeOverReports={refreshChangeOverReports}
                      />
                    ))
                  ) : (
                    <NoDataComponent
                      cellColSpan={7}
                      cellRowSpan={4}
                      dataLoading={loading}
                    />
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            {/* Pagination Component */}
            <TablePagination
              component="div"
              count={filteredReportCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage="Reports per page:"
            />
          </div>
        ) : (
          <NotAccessible />
        )}
      </div>
    </main>
  );
}

export default ChangeOverReportDataMain;
