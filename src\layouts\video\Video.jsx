import React from "react";
import "./video.scss";
import { OTSession, OTStreams, preloadScript } from "opentok-react";
import ConnectionStatus from "../../components/Video/ConnectionStatus";
import Publisher from "../../components/Video/Publisher";
import Subscriber from "../../components/Video/Subscriber";
import { Link } from "react-router-dom";
import CloseIcon from "@mui/icons-material/Close";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Button,
  Box,
  Paper,
  Typography,
} from "@mui/material";
import VideoFileSendComponent from "./VideoFileSendComponent";
import DashboardCustomizeIcon from "@mui/icons-material/DashboardCustomize";
import SendIcon from "@mui/icons-material/Send";
import CallEndIcon from "@mui/icons-material/CallEnd";
import WhiteboardIcon from "@mui/icons-material/Dashboard";
import PhotoCameraIcon from "@mui/icons-material/PhotoCamera";
import VideocamIcon from "@mui/icons-material/Videocam";
import MicIcon from "@mui/icons-material/Mic";
import AccessTimeIcon from "@mui/icons-material/AccessTime";

class Video extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      error: null,
      connected: false,
      open: false,
      callSeconds: 0,
      timerInterval: null,
    };
    this.sessionEvents = {
      sessionConnected: () => {
        this.setState({ connected: true });
        // Start timer
        if (!this.state.timerInterval) {
          const interval = setInterval(() => {
            this.setState((prev) => ({ callSeconds: prev.callSeconds + 1 }));
          }, 1000);
          this.setState({ timerInterval: interval });
        }
      },
      sessionDisconnected: () => {
        this.setState({ connected: false });
        // Stop timer
        if (this.state.timerInterval) {
          clearInterval(this.state.timerInterval);
          this.setState({ timerInterval: null });
        }
      },
    };

    this.setOpen = (state) => {
      this.setState({ open: state });
    };
  }

  componentWillUnmount() {
    // Clean up timer if component unmounts
    if (this.state.timerInterval) {
      clearInterval(this.state.timerInterval);
    }
  }

  onError = (err) => {
    this.setState({ error: `Failed to connect: ${err.message}` });
  };

  captureSubscriberScreenshot = () => {
    const screenshotCanvas = document.createElement("canvas");
    const subscriberVideo = document.querySelector(".subscriber video");

    if (subscriberVideo) {
      screenshotCanvas.width = subscriberVideo.videoWidth;
      screenshotCanvas.height = subscriberVideo.videoHeight;
      screenshotCanvas.getContext("2d").drawImage(subscriberVideo, 0, 0);
      const screenshot = screenshotCanvas.toDataURL("image/png");
      localStorage.setItem("whiteboardScreenshot", screenshot);
      window.open("/whiteboard", "_blank");
    } else {
      window.open("/whiteboard", "_blank");
    }
  };

  formatTime = (seconds) => {
    const m = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const s = (seconds % 60).toString().padStart(2, "0");
    return `${m}:${s}`;
  };

  render() {
    return (
      <>
        <section className="videoSection">
          <Paper elevation={3} className="videoContainer">
            <OTSession
              apiKey={this.props.apiKey}
              sessionId={this.props.sessionId}
              token={this.props.token}
              eventHandlers={this.sessionEvents}
              onError={this.onError}
            >
              {this.state.error ? (
                <Box className="errorMessage">
                  <Typography color="error">{this.state.error}</Typography>
                </Box>
              ) : null}

              <Box className="videoContainerHeader">
                <Box className="leftSide">
                  <ConnectionStatus connected={this.state.connected} />
                </Box>

                <Box
                  className="rightSide"
                  sx={{ display: "flex", alignItems: "center" }}
                >
                  {/* Timer display - styled badge */}
                  <Box
                    sx={{
                      display: this.state.connected ? "flex" : "none",
                      alignItems: "center",
                      background: "rgba(79,110,163,0.08)",
                      color: "#4f6ea3",
                      borderRadius: "16px",
                      padding: "4px 14px 4px 10px",
                      marginRight: 2,
                      fontWeight: 500,
                      fontSize: "1rem",
                      boxShadow: "0 1px 4px rgba(79,110,163,0.08)",
                    }}
                  >
                    <AccessTimeIcon sx={{ fontSize: 20, marginRight: 1 }} />
                    {this.formatTime(this.state.callSeconds)}
                  </Box>
                  <Box className="actionButtons">
                    <Button
                      variant="outlined"
                      startIcon={<SendIcon />}
                      onClick={() => this.setOpen(true)}
                      sx={{
                        borderRadius: "8px",
                        textTransform: "none",
                        margin: "0 8px",
                        color: "#4f6ea3",
                        borderColor: "#4f6ea3",
                        "&:hover": {
                          backgroundColor: "rgba(79, 110, 163, 0.08)",
                          borderColor: "#4f6ea3",
                        },
                      }}
                    >
                      Send file
                    </Button>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<WhiteboardIcon />}
                      sx={{
                        borderRadius: "8px",
                        textTransform: "none",
                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                        "&:hover": { boxShadow: "0 4px 8px rgba(0,0,0,0.15)" },
                      }}
                      onClick={() => {
                        const screenshotCanvas =
                          document.createElement("canvas");
                        const publisherVideo =
                          document.querySelector(".publisher video");
                        if (publisherVideo) {
                          screenshotCanvas.width = publisherVideo.videoWidth;
                          screenshotCanvas.height = publisherVideo.videoHeight;
                          screenshotCanvas
                            .getContext("2d")
                            .drawImage(publisherVideo, 0, 0);
                          const screenshot =
                            screenshotCanvas.toDataURL("image/png");
                          localStorage.setItem(
                            "whiteboardScreenshot",
                            screenshot,
                          );
                          window.open("/whiteboard", "_blank");
                        } else {
                          window.open("/whiteboard", "_blank");
                        }
                      }}
                    >
                      Whiteboard
                    </Button>
                  </Box>
                </Box>
              </Box>

              <Box className="videoScreens">
                <Box className="publisherContainer">
                  <Typography variant="subtitle1" className="videoLabel">
                    You
                  </Typography>
                  <Publisher />
                </Box>
                <OTStreams>
                  <Box className="subscriberOuterContainer">
                    <Typography variant="subtitle1" className="videoLabel">
                      Participant
                    </Typography>
                    <Subscriber />
                    <Box className="subscriberCaptureButton">
                      <Button
                        variant="contained"
                        color="secondary"
                        startIcon={<PhotoCameraIcon />}
                        sx={{
                          borderRadius: "8px",
                          textTransform: "none",
                          margin: "10px 0",
                          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                          "&:hover": {
                            boxShadow: "0 4px 8px rgba(0,0,0,0.15)",
                          },
                        }}
                        onClick={this.captureSubscriberScreenshot}
                      >
                        Capture to Whiteboard
                      </Button>
                    </Box>
                  </Box>
                </OTStreams>
              </Box>

              <Box
                className="callControls"
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  marginBottom: "10px",
                }}
              >
                <Link to="/videocall" style={{ textDecoration: "none" }}>
                  <Button
                    variant="contained"
                    color="error"
                    startIcon={<CallEndIcon />}
                    sx={{
                      borderRadius: "24px",
                      padding: "8px 24px",
                      backgroundColor: "rgba(180, 24, 24, 0.84)",
                      "&:hover": {
                        backgroundColor: "rgba(180, 24, 24, 0.92)",
                      },
                    }}
                  >
                    End Call
                  </Button>
                </Link>
              </Box>
            </OTSession>
          </Paper>

          <Dialog
            open={this.state.open}
            maxWidth="lg"
            PaperProps={{
              sx: { borderRadius: "12px" },
            }}
          >
            <Box
              sx={{
                padding: "1.2rem",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                borderBottom: "1px solid rgba(0,0,0,0.1)",
              }}
            >
              <DialogTitle sx={{ padding: 0 }}>
                <SendIcon sx={{ verticalAlign: "middle", marginRight: 1 }} />
                Send File
              </DialogTitle>
              <IconButton
                onClick={() => this.setOpen(false)}
                sx={{
                  "&:hover": { backgroundColor: "rgba(0,0,0,0.04)" },
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
            <DialogContent>
              <VideoFileSendComponent setOpen={() => this.setOpen(false)} />
            </DialogContent>
          </Dialog>
        </section>
      </>
    );
  }
}

export default preloadScript(Video);
