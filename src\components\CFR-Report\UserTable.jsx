import React from "react";
import moment from "moment-timezone";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import { CircularProgress } from "@mui/material";
import { useStateContext } from "../../context/ContextProvider";

function UserTable(props) {
  const { data, load } = props;
  const { currentMode } = useStateContext();

  let reportItems = data
    .slice()
    .sort((a, b) => {
      let a_date = a.dateTime.seconds;
      let b_date = b.dateTime.seconds;
      if (a_date < b_date) {
        return 1;
      }
      if (a_date > b_date) {
        return -1;
      }
      return 0;
    })
    .reverse();
  const indianTimeZone = "Asia/Kolkata";

  // reportItems.forEach((item) => {
  //   //const formattedDate = moment.utc(item.dateTime).format("DD/MM/YYYY");
  //   const parsedDate = moment.utc(item.dateTime).format("YYYY-MM-DD");

  //   item.dateTime = parsedDate;
  // });
  console.log("naya date", reportItems);
  return (
    <TableContainer
      component={Paper}
      className="tableContainer"
      style={{
        border: currentMode === "Dark" ? "1px solid #fff" : "1px solid #000",
      }}
    >
      <Table
        className="insideTable"
        style={{
          backgroundColor: currentMode === "Dark" ? "#161C24" : "#fff",
        }}
      >
        <TableHead
          style={{
            backgroundColor: currentMode === "Dark" ? "#212B36" : "#eee",
          }}
        >
          <TableRow>
            <TableCell
              sx={
                currentMode === "Dark"
                  ? { color: "white", borderBottom: "1px solid white" }
                  : { borderBottom: "1px solid #000" }
              }
              align="left"
            >
              Date/Time
            </TableCell>
            <TableCell
              sx={
                currentMode === "Dark"
                  ? { color: "white", borderBottom: "1px solid white" }
                  : { borderBottom: "1px solid #000" }
              }
              align="left"
            >
              Username/EmailId
            </TableCell>
            <TableCell
              sx={
                currentMode === "Dark"
                  ? { color: "white", borderBottom: "1px solid white" }
                  : { borderBottom: "1px solid #000" }
              }
              align="left"
            >
              Role
            </TableCell>
            <TableCell
              sx={
                currentMode === "Dark"
                  ? { color: "white", borderBottom: "1px solid white" }
                  : { borderBottom: "1px solid #000" }
              }
              align="left"
            >
              Activity
            </TableCell>
          </TableRow>
        </TableHead>
        {reportItems.length > 0 ? (
          <TableBody>
            {reportItems.map((item, idx) => (
              <TableRow key={`${item?.dateTime} - ${item?.dateTime}`}>
                <TableCell
                  sx={currentMode === "Dark" ? { color: "white" } : {}}
                  style={
                    idx === reportItems.length - 1 && currentMode === "Dark"
                      ? { borderBottom: "1px solid white" }
                      : {}
                  }
                  align="left"
                >
                  {`${moment.tz(item.dateTime, indianTimeZone).format("YYYY-MM-DD [at] HH:mm:ss")}`}
                </TableCell>
                <TableCell
                  style={
                    idx === reportItems.length - 1 && currentMode === "Dark"
                      ? { borderBottom: "1px solid white" }
                      : {}
                  }
                  align="left"
                >
                  {item?.userName_email}
                </TableCell>
                <TableCell
                  sx={
                    currentMode === "Dark"
                      ? { color: "white", textTransform: "capitalize" }
                      : { textTransform: "capitalize" }
                  }
                  style={
                    idx === reportItems.length - 1 && currentMode === "Dark"
                      ? { borderBottom: "1px solid white" }
                      : {}
                  }
                  align="left"
                >
                  {item?.role}
                </TableCell>
                <TableCell
                  sx={
                    currentMode === "Dark"
                      ? { color: "white", textTransform: "capitalize" }
                      : { textTransform: "capitalize" }
                  }
                  style={
                    idx === reportItems.length - 1 && currentMode === "Dark"
                      ? { borderBottom: "1px solid white" }
                      : {}
                  }
                  align="left"
                >
                  {item?.activity}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <TableRow>
            <TableCell
              sx={
                currentMode === "Dark"
                  ? { color: "white", borderBottom: "1px solid white" }
                  : {}
              }
              className="h-32 w-full flex justify-center items-center"
              colSpan={4}
            >
              {load ? (
                <CircularProgress />
              ) : (
                <div className="h-32 w-full flex justify-center items-center">
                  No Data
                </div>
              )}
            </TableCell>
          </TableRow>
        )}
      </Table>
    </TableContainer>
  );
}

export default UserTable;
