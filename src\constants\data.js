let companyId = window.localStorage.getItem("companyId");
let adminType = window.localStorage.getItem("adminType");

export const alluser = "A_allUserData"; //'users'
export const companies = "A_companyData";
export const companyId_constant = companyId ? companyId : "Id_not_set"; //`bn85y2VZujFbrK80Xa22`
export const adminType_constant_temp = adminType ? adminType : "no_AdminType";
export const lsiAdmin = "lsi_admin";
export const machines = "machineData";
export const steps = "stepData";
export const subStep = "subStepData";
export const manuals = "stepData";
export const liveData = "liveData";
export const users = "userData";
export const maintenance = "maintenanceData";
export const training = "manualData";
export const liveEvents = "liveData2";
export const maintenanceReport = "maintenanceReport";
export const fatReport = `fatReportData`;
export const satReport = `satReportData`;
export const openTalk = "OpenTokConfig";
export const openTalkRelayed = "relayed";
export const testValues = "_TestValue";
export const hasTags = "Hastags";
export const lyoAlarm = "Lyo_Alarm";
export const batch = "batch";
export const media = "media";
export const cmsInfra = "cms-infrastructure";
export const pType = [
  {
    key: "Auto",
    value: 0,
  },
  {
    key: "Manual",
    value: 1,
  },
];
export const pFType = [
  {
    key: "Checking a condition is less than or greater than",
    value: 0,
  },
  {
    key: "Checking for a general parametric range using the given parameters",
    value: 1,
  },
  {
    key: "Checking for a particular point of some parameter given a time allowance",
    value: 2,
  },
];
export const pInput = [
  {
    key: "Time",
    value: 0,
  },
  {
    key: "Temperature",
    value: 1,
  },
  {
    key: "Pressure",
    value: 2,
  },
];
//cfr descriptions

export const machine_events = {
  post: "machine created",
  put: "machine updated",
  delete: "machine deleted",
};

//
