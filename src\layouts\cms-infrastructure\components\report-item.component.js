import React, { useContext } from "react";
import {
  Table<PERSON>ow,
  Table<PERSON>ell,
  IconButton,
  <PERSON>alog,
  DialogContent,
} from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import EditIcon from "@mui/icons-material/Edit";
import InventoryIcon from "@mui/icons-material/Inventory";
import { useState } from "react";
import CalibrationDataPrint from "../forms/calibration-data.print";
import { CmsInfraContext } from "../../../services/cms-infrastructure/cms-infra.context";
import EditReportForm from "../crud/edit-forms/edit-report.form";
import DeleteIcon from "@mui/icons-material/Delete";
import Delete from "../../../components/Delete/Delete";
import { deleteDataFromReports } from "../crud/functions/cms-infra.functions";

const ReportItem = ({ item }) => {
  const { currentMode } = useStateContext();
  const [open, setOpen] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const { cmsData, instruments, reports } = useContext(CmsInfraContext);

  return (
    <TableRow
      key={item.id}
      // sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
      style={{
        cursor: "pointer",
        borderBottom:
          currentMode === "Dark" ? ".5px solid #444" : ".5px solid #ddd",
      }}
      className={"shadow-inner"}
    >
      <TableCell
        style={{ borderBottom: "none", textTransform: "capitalize" }}
        align="left"
      >
        {item?.cal_p_no}
      </TableCell>

      <TableCell
        style={{ borderBottom: "none", textTransform: "capitalize" }}
        align="left"
      >
        {item?.calibrated_by}
      </TableCell>
      <TableCell style={{ borderBottom: "none" }} align="left">
        {item?.created_by}
      </TableCell>
      <TableCell style={{ borderBottom: "none" }} align="left">
        {item?.criticality}
      </TableCell>
      <TableCell style={{ borderBottom: "none" }} align="left">
        {item?.sap_master_key}
      </TableCell>
      <TableCell style={{ borderBottom: "none" }} align="left">
        {item?.remarks}
      </TableCell>
      <TableCell style={{ borderBottom: "none" }} align="left">
        <IconButton onClick={() => setOpen(true)}>
          <InventoryIcon style={{ fontSize: "20px" }} />
        </IconButton>

        <IconButton onClick={() => setOpenEdit(true)}>
          <EditIcon style={{ fontSize: "20px", color: "#00f" }} />
        </IconButton>

        <IconButton onClick={() => setOpenDelete(true)}>
          <DeleteIcon style={{ fontSize: "20px", color: "red" }} />
        </IconButton>
      </TableCell>

      <Dialog
        onClose={() => setOpen(false)}
        open={open}
        fullWidth
        maxWidth="lg"
      >
        <DialogContent>
          <CalibrationDataPrint
            item={instruments?.filter(
              (instrument) => instrument?.id === item?.sap_master_key,
            )}
            masterEquip={cmsData?.filter(
              (equip) => equip?.id == item?.eqp_master_key,
            )}
            report={item}
          />
        </DialogContent>
      </Dialog>

      <Dialog
        onClose={() => setOpenEdit(false)}
        open={openEdit}
        fullWidth
        maxWidth="lg"
      >
        <DialogContent>
          <EditReportForm closeEdit={() => setOpenEdit(false)} item={item} />
        </DialogContent>
      </Dialog>

      <Dialog onClose={() => setOpenDelete(false)} open={openDelete}>
        <Delete
          onClose={() => setOpenDelete(false)}
          onDelete={() => deleteDataFromReports(item)}
        />
      </Dialog>
    </TableRow>
  );
};

export default ReportItem;
