import axios from "axios";
import { useReducer, useEffect } from "react";
import { database } from "../firebase";
import { dbConfig } from "../infrastructure/db/db-config";
import { useMongoRefresh } from "../services/mongo-refresh.context";
import { useAuth } from "./AuthProvider";
const ACTIONS = {
  SELECT_FOLDER: "select-folder",
  UPDATE_FOLDER: "update-folder",
  SET_CHILD_FOLDERS: "set-child-folders",
  SET_CHILD_FILES: "set-child-files",
};

export const ROOT_FOLDER = { name: "Root", id: "", path: [] };

function reducer(state, { type, payload }) {
  switch (type) {
    case ACTIONS.SELECT_FOLDER:
      return {
        folderId: payload.folderId,
        folder: payload.folder,
        childFiles: [],
        childFolders: [],
      };
    case ACTIONS.UPDATE_FOLDER:
      return {
        ...state,
        folder: payload.folder,
      };
    case ACTIONS.SET_CHILD_FOLDERS:
      return {
        ...state,
        childFolders: payload.childFolders,
      };
    case ACTIONS.SET_CHILD_FILES:
      return {
        ...state,
        childFiles: payload.childFiles,
      };
    default:
      return state;
  }
}

export function useFolder(folderId = "", folder = null) {
  let refreshCount = 0;
  let setRefreshCount = () => {};
  const { currentUser } = useAuth();

  try {
    const mongoContext = useMongoRefresh();
    if (mongoContext) {
      refreshCount = mongoContext.refreshCount ?? 0;
      setRefreshCount = mongoContext.setRefreshCount ?? (() => {});
    }
  } catch (error) {
    console.error(
      "Error using MongoRefreshContext in useFolder:",
      error.message,
    );
  }

  const [state, dispatch] = useReducer(reducer, {
    folderId,
    folder,
    childFolders: [],
    childFiles: [],
  });

  const getFolderDataById = async () => {
    await axios
      .get(`${dbConfig.url}/folders/${folderId}`)
      .then((response) => {
        dispatch({
          type: ACTIONS.UPDATE_FOLDER,
          payload: { folder: response.data.data },
        });
      })
      .catch(() => {
        dispatch({
          type: ACTIONS.UPDATE_FOLDER,
          payload: { folder: ROOT_FOLDER },
        });
      });
  };

  useEffect(() => {
    dispatch({ type: ACTIONS.SELECT_FOLDER, payload: { folderId, folder } });
  }, [folderId, folder, refreshCount]);

  useEffect(() => {
    if (folderId === "") {
      return dispatch({
        type: ACTIONS.UPDATE_FOLDER,
        payload: { folder: ROOT_FOLDER },
      });
    }

    getFolderDataById();
  }, [folderId, refreshCount]);

  const getFolderDataByParentId = async () => {
    await axios.get(`${dbConfig.url}/folders`).then((response) => {
      const currentFolder = folderId === undefined ? "" : folderId;
      const folderData = response.data.data.filter(
        (file) => file.parent_id === currentFolder,
      );
      dispatch({
        type: ACTIONS.SET_CHILD_FOLDERS,
        payload: { childFolders: folderData },
      });
    });
  };

  useEffect(() => {
    if (currentUser) {
      getFolderDataByParentId();
    }
  }, [folderId, refreshCount, currentUser]);

  const fetchAllFiles = async () => {
    await axios.get(`${dbConfig.url}/files`).then((response) => {
      const currentFolder = folderId === undefined ? "" : folderId;
      const files = response.data.data.filter(
        (file) => file.parent_id === currentFolder,
      );
      dispatch({
        type: ACTIONS.SET_CHILD_FILES,
        payload: { childFiles: files },
      });
    });
  };

  useEffect(() => {
    if (currentUser) {
      fetchAllFiles();
    }
  }, [folderId, refreshCount, currentUser]);

  return state;
}
