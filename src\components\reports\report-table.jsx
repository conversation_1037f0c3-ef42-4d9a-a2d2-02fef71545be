import React, { useState } from "react";
import {
  Table,
  TableContainer,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Paper,
  Button,
  Typography,
  Box,
} from "@mui/material";

const ReportTable = ({ data, columns, expandableContent }) => {
  const [expanded, setExpanded] = useState(false);

  const toggleExpansion = () => {
    setExpanded(!expanded);
  };

  return (
    <div>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell key={column.dataKey}>{column.label}</TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.map((item) => (
              <TableRow key={item.id}>
                {columns.map((column) => (
                  <TableCell key={column.dataKey}>
                    {item[column.dataKey]}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Button
        fullWidth
        variant="outlined"
        onClick={toggleExpansion}
        style={{ marginTop: "1rem" }}
      >
        {expanded ? "Collapse" : "Expand"}
      </Button>

      {expanded && (
        <Box mt={2} p={2} bgcolor="#f5f5f5">
          {expandableContent}
        </Box>
      )}
    </div>
  );
};

export default ReportTable;
