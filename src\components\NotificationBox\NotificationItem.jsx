import React, { useState, useEffect } from "react";
import "./Notification.scss";
import AddAlertIcon from "@mui/icons-material/AddAlert";
import FmdBadIcon from "@mui/icons-material/FmdBad";
import Badge from "@mui/material/Badge";
import { AlertTitle } from "@mui/material";
import { useNavigate, useLocation } from "react-router-dom";
import {
  useMaintenanceInfo,
  useMaintenanceInfoSeter,
} from "../../context/MaintenanceContext";
import { useStateContext } from "../../context/ContextProvider";

export default function NotificationItem({ dataM, alerts }) {
  const [showAlerts, setShowAlerts] = useState(true);
  const history = useNavigate();
  const maintenanceInfoSetter = useMaintenanceInfoSeter();
  const location = useLocation();
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const pathName = location.pathname;
  //const [machineNotificationsCount, setMachineNotificationCount] = useState(0);

  // useEffect(() => {
  //     let temp = 0;
  //     alerts?.map((data) => {
  //         if (data.mid == dataM.id) {
  //             temp += 1;
  //         }

  //     });
  //     setMachineNotificationCount(temp);
  //     if (temp == 0)
  //     setShowAlerts(false)
  // })

  const handleRedirect = (data) => {
    //console.log("notification pathName:", pathName)
    if (pathName == "/maintenance/" + data?.mid) {
      // maintenanceInfoSetter(data.maintenance_id);
      maintenanceInfoSetter({ ...data });
    } else {
      //maintenanceInfoSetter(data.maintenance_id);
      maintenanceInfoSetter(data);
      history?.replace("/maintenance" + "/" + data?.mid);
    }
  };

  return (
    <>
      {/* <div className='bg-slate-50 shadow-md hover:shadow-lg flex flex-col my-1 pt-1 rounded-md'> */}

      {/* <div className='capitalize self-center flex justify-center justify-items-center flex-col '>
                    <div>
                        {dataM.title}
                        {(machineNotificationsCount > 0 && !showAlerts) &&
                            <Badge badgeContent={machineNotificationsCount}  >
                                <AddAlertIcon className='text-red-500 '  />
                            </Badge>
                        }
                    </div>

                    {showAlerts ? <i class="ri-arrow-down-s-line self-center font-bold hover:cursor-pointer hover:font-light" onClick={() => setShowAlerts(!showAlerts)} />
                        :
                        <i class="ri-arrow-up-s-line self-center font-bold hover:cursor-pointer hover:font-light" onClick={() => setShowAlerts(!showAlerts)} />
                    }
                </div> */}

      {showAlerts && (
        <>
          {alerts?.map((data) => (
            <div
              style={
                currentMode === "Dark"
                  ? { backgroundColor: "#242A2D", color: "white" }
                  : {}
              }
              onClick={() => handleRedirect(data)}
            >
              {dataM?.id == data?.mid && (
                <li
                  style={
                    currentMode === "Dark"
                      ? { backgroundColor: "#242A2D", color: "white" }
                      : {}
                  }
                >
                  <div>
                    <AddAlertIcon />
                  </div>
                  <div>
                    <div
                      style={
                        currentMode === "Dark"
                          ? { backgroundColor: "#242A2D", color: "white" }
                          : {}
                      }
                      className="title"
                    >
                      <span>{data?.name}</span> :{" "}
                      {parseFloat(data?.value).toFixed(2)}
                    </div>
                    <div className="time">
                      <span>
                        <i className="ri-timer-2-fill"></i>
                      </span>
                      {data?.time?.toDate().toString().substring(0, 15)}
                    </div>
                    <div>
                      <span className="font-bold text-sm">Exp:</span>
                      <span className="text-sm">
                        {data?.exp_date?.toDate().toString().substring(0, 15)}
                      </span>
                    </div>
                    <div className="text-sm capitalize">{dataM?.title}</div>
                  </div>
                </li>
              )}
            </div>
          ))}
        </>
      )}
      {/* </div> */}
    </>
  );
}
