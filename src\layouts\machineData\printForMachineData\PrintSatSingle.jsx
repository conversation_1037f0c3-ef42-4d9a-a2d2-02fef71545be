import React, { useEffect, useState } from "react";
import { companies, companyId_constant } from "../../../constants/data";
import { db } from "../../../firebase";
import { useParams } from "react-router-dom";
import { firebaseLooper } from "../../../tools/tool";
import ContentTableItem from "../ContentTableItem";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { FeaturedPlayListSharp } from "@mui/icons-material";
import Paper from "@mui/material/Paper";
import "./printForMachineData.scss";
import ApprovalTableItem from "../ApprovalTable/ApprovalTable";

export default function PrintSatSingle() {
  const { docId } = useParams();
  const type = "satData";
  const [satData, setSatData] = useState([]);
  const [tableDetails, setTableDetails] = useState([]);
  const [approvalTable, setApprovalTable] = useState([]);
  const [dynamicTableDetail, setDynamicTableDetail] = useState([]);
  const [printButtonVisivility, setPrintButtonVisibility] = useState(true);
  const [companyLogo, setCompanyLogo] = useState("");

  useEffect(() => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(type)
    //     .where("docId", "==", docId)
    //     .onSnapshot((snap) => {
    //         snap.forEach((snap) => {
    //             db.collection(companies)
    //                 .doc(companyId_constant)
    //                 .collection(type)
    //                 .doc(snap.id)
    //                 .collection("table")
    //                 .onSnapshot((snap) => {
    //                     const data = firebaseLooper(snap);
    //                     data.sort(function (a, b) {
    //                         return (a.index - b.index)
    //                     })
    //                     setTableDetails(data);
    //                 });
    //             db.collection(companies)
    //                 .doc(companyId_constant)
    //                 .collection(type)
    //                 .doc(snap.id)
    //                 .collection("approvalTable")
    //                 .onSnapshot((snap) => {
    //                     const data = firebaseLooper(snap);
    //                     data.sort(function (a, b) {
    //                         return (a.index - b.index)
    //                     })
    //                     setApprovalTable(data);
    //                 });
    //             db.collection(companies)
    //                 .doc(companyId_constant)
    //                 .collection(type)
    //                 .doc(snap.id)
    //                 .collection("dynamic_table")
    //                 .onSnapshot((snap) => {
    //                     const data = firebaseLooper(snap);
    //                     setDynamicTableDetail(data);
    //                 });
    //         });
    //         const data = firebaseLooper(snap);
    //         setSatData(data);
    //     });
    // //
    // db.collection(companies)
    //     .doc(companyId_constant).get()
    //     .then((value) => {
    //         setCompanyLogo(value.data()["url"]);
    //     })
  }, []);

  const handlePrint = () => {
    let promise = new Promise((resolve, reject) => {
      resolve();
    });
    promise
      .then(() => {
        setPrintButtonVisibility(false);
      })
      .then(() => {
        return new Promise((resolve, reject) => {
          window.print();
          resolve();
        });
        //window.print();
      })
      .then(() => {
        window.close();
      });
  };

  return (
    <>
      <div className="mainSection p-1">
        <div className="flex p-1 border-2">
          <div className=" w-1/5 border-r-2">
            <img src={companyLogo}></img>
          </div>
          <div className=" flex-col pl-2">
            <div>Lyo</div>
            <div>Operational Qualification</div>
            <div>Performance Test (FAT)</div>
            <div>Model No: LYOMAX 9S</div>
            <div className="dataTime">
              {satData[0]?.createdAt.toDate().toString().substring(0, 25)}
            </div>
          </div>
        </div>
        {satData[0]?.title ? (
          <div className="content_sub-section">
            <header className="contentMainTitle">
              <div className="capitalize ">{satData[0]?.title}</div>
            </header>
            <div className="italic font-medium">" {satData[0]?.desc} "</div>
          </div>
        ) : (
          ""
        )}
        {satData[0]?.objective ? (
          <div className="content_sub-section">
            <div className="content_subtitle  flex">
              <h4 className="font-bold underline mr-3"> OBJECTIVE:</h4>
              <p>{satData[0]?.objective}</p>
            </div>
          </div>
        ) : (
          ""
        )}

        {satData[0]?.method ? (
          <div className="content_sub-section">
            <div className="content_subtitle flex">
              <h4 className="font-bold underline mr-3"> METHOD:</h4>
              <p>{satData[0]?.method}</p>
            </div>
          </div>
        ) : (
          ""
        )}

        {satData[0]?.pre?.length > 0 ? (
          <div className="content_sub-section">
            <div className="content_subtitle ">
              <h4 className="font-bold underline mr-3 mb-2"> PREREQUISITES:</h4>
              {satData[0].pre.map((point, idx) => (
                <p className="mb-2" key={idx}>
                  <span className="font-bold ">{idx + 1}.</span> {point}
                </p>
              ))}
            </div>
          </div>
        ) : (
          ""
        )}

        {satData[0]?.procedure?.length > 0 ? (
          <div className="content_sub-section">
            <div className="content_subtitle">
              <h4 className="font-bold underline mr-3 mb-2">
                {" "}
                TEST PROCEDURE:
              </h4>
              {satData[0].procedure.map((point, idx) => (
                <p className="mb-2" key={idx}>
                  <span className="font-bold ">{idx + 1}.</span> {point}
                </p>
              ))}
            </div>
          </div>
        ) : (
          ""
        )}

        {satData?.length > 0 ? (
          <div className="content_sub-section">
            <div className="table_title">
              <h4 className="font-bold underline mr-3 mb-4">TEST EXECUTION</h4>
            </div>

            <div className="flex w-full h-16 p-1 mb-2 bg-black text-white justify-between items-center">
              <div className="w-1/5">Check Point</div>
              <div className="w-1/5">Observation</div>
              <div className="w-1/5">Acceptance Criteria</div>
              <div className="w-1/5">Confirm YES/NO</div>
              <div className="w-1/5">Deviation</div>
            </div>

            {tableDetails.map((data) => (
              <div className="flex w-full h-16 p-1 mb-2 justify-between items-center">
                <div className="w-1/5">{data.check}</div>
                <div className="w-1/5">{data.observation}</div>
                <div className="w-1/5">{data.acceptance}</div>
                <div className="w-1/5">{data.confirm}</div>
                <div className="w-1/5">{data.dev}</div>
              </div>
            ))}

            {approvalTable.map((data) => (
              <ApprovalTableItem key={data.id} data={data} />
            ))}

            {/* <TableContainer component={Paper} className="table">
                            <Table sx={{ minWidth: 650, width: "100%" }}>
                                <TableHead>
                                    <TableRow >
                                        <TableCell align="left">Check Point</TableCell>
                                        <TableCell align="center">Observation</TableCell>
                                        <TableCell align="center">
                                            Acceptance Criteria
                                        </TableCell>
                                        <TableCell align="center">Confirm YES/NO</TableCell>
                                        <TableCell align="center">Deviation</TableCell>
                                        <TableCell align="center">Actions</TableCell>
                                    </TableRow>
                                </TableHead>

                                <TableBody>
                                    {tableDetails.map((data) => (
                                        <ContentTableItem collectionId={satData[0]?.id} index={tableDetails?.length} type={type} key={data.id} data={data} />
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer> */}
          </div>
        ) : (
          ""
        )}

        {printButtonVisivility && (
          <div className="flex justify-end">
            <button
              className="bg-gray-400 hover:bg-gray-500 text-white font-bold py-1 px-2 m-1 rounded"
              onClick={() => handlePrint()}
            >
              Print
            </button>
          </div>
        )}
      </div>
    </>
  );
}
