// Convert time string (HH:MM AM/PM) to minutes since midnight
export const timeToMinutes = (timeStr) => {
  if (!timeStr) return ""; // Return empty string instead of 0 for null/undefined

  const [time, period] = timeStr.split(" ");
  if (!time || !period) return ""; // Return empty if format is invalid

  let [hours, minutes] = time.split(":").map(Number);
  if (isNaN(hours) || isNaN(minutes)) return ""; // Return empty if parsing fails

  // Convert to 24-hour format first
  if (period === "PM" && hours !== 12) {
    hours += 12;
  } else if (period === "AM" && hours === 12) {
    hours = 0; // 12:00 AM is 00:00 in 24-hour format
  }

  // Convert to minutes since midnight
  return hours * 60 + minutes;
};

// Convert minutes since midnight back to time string (HH:MM AM/PM)
export const minutesToTime = (totalMinutes) => {
  if (!totalMinutes && totalMinutes !== 0) return "";

  // Ensure totalMinutes is within 24-hour range (0-1439)
  totalMinutes = totalMinutes % 1440;

  let hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  const period = hours >= 12 ? "PM" : "AM";

  // Convert to 12-hour format
  if (hours > 12) {
    hours -= 12;
  } else if (hours === 0) {
    hours = 12;
  }

  return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")} ${period}`;
};

// Example usage:
// timeToMinutes("09:30 AM") returns 570 (9 hours * 60 + 30 minutes)
// timeToMinutes("02:30 PM") returns 870 (14 hours * 60 + 30 minutes)
// minutesToTime(570) returns "09:30 AM"
// minutesToTime(870) returns "02:30 PM"

export function formatDateTime(date) {
  // Get date components
  var day = date.getDate();
  var month = date.getMonth() + 1; // Months are zero-based
  var year = date.getFullYear();
  var hours = date.getHours();
  var minutes = date.getMinutes();
  var seconds = date.getSeconds();

  // Ensure two-digit format for day, month, hours, minutes, and seconds
  if (day < 10) {
    day = "0" + day;
  }
  if (month < 10) {
    month = "0" + month;
  }
  if (hours < 10) {
    hours = "0" + hours;
  }
  if (minutes < 10) {
    minutes = "0" + minutes;
  }
  if (seconds < 10) {
    seconds = "0" + seconds;
  }

  // Format the date as "dd-mm-yyyy_hh:mm:ss"
  var formattedDateTime =
    day +
    "-" +
    month +
    "-" +
    year +
    "_" +
    hours +
    ":" +
    minutes +
    ":" +
    seconds;

  // Replace spaces with underscores
  formattedDateTime = formattedDateTime.replace(/\s/g, "_");

  return formattedDateTime;
}
// Output example: "29/04/2024_10:15:30_AM" (assuming the date is April 29, 2024, 10:15:30 AM)
