import React, { useState, useEffect, useMemo } from "react";
import annotationPlugin from "chartjs-plugin-annotation";
import { Line } from "react-chartjs-2";
import {
  TextField,
  Button,
  IconButton,
  Slider,
  Typography,
  Box,
  Menu,
  MenuItem,
  Divider,
} from "@mui/material";
import SettingsIcon from "@mui/icons-material/Settings";
import RefreshIcon from "@mui/icons-material/Refresh";
import moment from "moment";

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

ChartJS.register(
  annotationPlugin,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend,
);

const colors = [
  "#FF6F61",
  "#70C3B0",
  "#536780",
  "#FFD700",
  "#9400D3",
  "#00CED1",
];

const AnnotationChartNew = ({
  activeChartsArrayFromSelected,
  processValues,
}) => {
  const [data, setData] = useState({ labels: [], datasets: [] });
  const [startDateTime, setStartDateTime] = useState("");
  const [endDateTime, setEndDateTime] = useState("");
  const [yAxisLimit, setYAxisLimit] = useState([-50, 50]);
  const [lastValues, setLastValues] = useState(100);
  const [anchorEl, setAnchorEl] = useState(null);
  const [initialState, setInitialState] = useState({
    yAxisLimit: [-50, 50],
    lastValues: 100,
    startDateTime: "",
    endDateTime: "",
  });

  // UseMemo for processing data efficiently
  const { chartLabels, chartDatasets } = useMemo(() => {
    let timestampSet = new Set();
    const chartMap = {};

    activeChartsArrayFromSelected?.forEach((chartId, index) => {
      const chartData = processValues?.find((d) => d._id === chartId);
      const unit = chartData?.unit;
      const filteredData = chartData?.previous || [];

      const startTime = startDateTime ? moment(startDateTime).valueOf() : null;
      const endTime = endDateTime ? moment(endDateTime).valueOf() : null;

      const rangeFiltered = filteredData.filter((item) => {
        const time = moment(item.time).valueOf();
        return (
          (!startTime || time >= startTime) && (!endTime || time <= endTime)
        );
      });

      // ✅ Get the last `lastValues` from rangeFiltered
      // Get the last N values
      const limited = rangeFiltered
        .slice(-lastValues)
        .sort((a, b) => moment(a.time) - moment(b.time));

      console.log("Limited Data:", limited);

      // Add timestamps to global set
      limited.forEach((item) => timestampSet.add(item.time));

      // Store chart-specific data for later alignment
      chartMap[chartId] = {
        label: `${chartData?.tag} (${unit})`,
        color: colors[index % colors.length],
        data: new Map(limited.map((item) => [item.time, item.value])),
      };
    });

    // Sort and format timestamps
    const sortedTimestamps = Array.from(timestampSet).sort(
      (a, b) => moment(a) - moment(b),
    );
    const labels = sortedTimestamps.map((t) => moment(t).format("HH:mm:ss"));

    const datasets = Object.values(chartMap).map(({ label, color, data }) => ({
      label,
      data: sortedTimestamps.map((t) => data.get(t) ?? null), // fill nulls
      borderColor: color,
      pointStyle: "rectRounded",
      yAxisID: "y",
    }));

    return { chartLabels: labels, chartDatasets: datasets };
  }, [
    activeChartsArrayFromSelected,
    processValues,
    startDateTime,
    endDateTime,
    lastValues,
  ]);

  // Update chart data
  useEffect(() => {
    setData({
      labels: chartLabels,
      datasets: chartDatasets,
    });
  }, [chartLabels, chartDatasets]);

  // Initial range detection
  useEffect(() => {
    if (processValues.length) {
      const allTimes = processValues
        .flatMap((d) => d.previous.map((item) => moment(item.time)))
        .filter(Boolean);

      const minTime = moment.min(allTimes).format("YYYY-MM-DDTHH:mm:ss");
      const maxTime = moment.max(allTimes).format("YYYY-MM-DDTHH:mm:ss");

      setStartDateTime(minTime);
      setEndDateTime(maxTime);
      setInitialState((prev) => ({
        ...prev,
        startDateTime: minTime,
        endDateTime: maxTime,
      }));
    }
  }, [processValues]);

  const handleClick = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const handleGraphReset = () => {
    setYAxisLimit(initialState.yAxisLimit);
    setLastValues(initialState.lastValues);
    setStartDateTime(initialState.startDateTime);
    setEndDateTime(initialState.endDateTime);
  };

  const chartOptions = {
    responsive: true,
    interaction: { mode: "index", intersect: false },
    plugins: {
      title: {
        display: true,
        text: "Live Data - Comparison Chart",
        color: "black",
      },
      legend: { position: "top" },
      annotation: {
        annotations: {
          yMinLine: {
            type: "line",
            yMin: yAxisLimit[0],
            yMax: yAxisLimit[0],
            borderColor: "rgb(255, 99, 132)",
            borderWidth: 2,
            borderDash: [6, 6],
            label: {
              enabled: true,
              content: "Min Value",
              position: "start",
              backgroundColor: "rgba(255, 99, 132, 0.8)",
            },
          },
          yMaxLine: {
            type: "line",
            yMin: yAxisLimit[1],
            yMax: yAxisLimit[1],
            borderColor: "rgb(54, 162, 235)",
            borderWidth: 2,
            borderDash: [6, 6],
            label: {
              enabled: true,
              content: "Max Value",
              position: "start",
              backgroundColor: "rgba(54, 162, 235, 0.8)",
            },
          },
        },
      },
    },
    scales: {
      x: {
        type: "category",
        title: {
          display: true,
          text: "Time (HH:mm:ss)",
        },
      },
      y: {
        type: "linear",
        position: "left",
        title: {
          display: true,
          text: "Values",
        },
      },
    },
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        bgcolor: "white",
        color: "black",
        p: 0.5,
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <IconButton onClick={handleClick}>
          <SettingsIcon />
        </IconButton>
        <IconButton onClick={handleGraphReset}>
          <RefreshIcon />
        </IconButton>
      </Box>

      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
        <MenuItem>
          <TextField
            helperText="Start Time"
            type="datetime-local"
            value={startDateTime}
            onChange={(e) => setStartDateTime(e.target.value)}
            sx={{ mr: 1 }}
          />
        </MenuItem>
        <MenuItem>
          <TextField
            helperText="End Time"
            type="datetime-local"
            value={endDateTime}
            onChange={(e) => setEndDateTime(e.target.value)}
            sx={{ mr: 1 }}
          />
        </MenuItem>
        <MenuItem>
          <Typography gutterBottom>Y-Axis Limits</Typography>
          <Slider
            value={yAxisLimit}
            onChange={(e, val) => setYAxisLimit(val)}
            valueLabelDisplay="on"
            min={-500}
            max={500}
          />
        </MenuItem>
        <Divider sx={{ my: 1 }} />
        <MenuItem>
          <Button fullWidth variant="outlined" onClick={handleGraphReset}>
            Reset View
          </Button>
        </MenuItem>
      </Menu>

      <Box sx={{ width: "1000px", height: "500px" }}>
        {data && <Line options={chartOptions} data={data} />}
      </Box>

      <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
        {[100, 250, 500, 1000, 2000, 5000].map((val, idx) => (
          <React.Fragment key={val}>
            {idx !== 0 && (
              <div
                style={{
                  borderLeft: "1px solid #CFD2CF",
                  height: "25px",
                  marginRight: "10px",
                }}
              />
            )}
            <Box sx={{ mr: 2 }}>
              <span
                onClick={() => setLastValues(val)}
                className="cursor-pointer text-gray-400 hover:text-indigo-600"
              >
                {val} <span className="text-xs italic">values</span>
              </span>
            </Box>
          </React.Fragment>
        ))}
      </Box>
    </Box>
  );
};

export default AnnotationChartNew;
