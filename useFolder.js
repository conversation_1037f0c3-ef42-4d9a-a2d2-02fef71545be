import { useMongoRefresh } from "./src/services/mongo/refresh/context";

const useFolder = () => {
  let refreshCount = 0;

  const context = (() => {
    try {
      return useMongoRefresh();
    } catch (error) {
      console.error("Error in useFolder: ", error.message);
      return null;
    }
  })();

  refreshCount = context?.refreshCount ?? 0;

  console.log("useFolder accessed. refreshCount:", refreshCount);

  const currentUser = context?.currentUser;

  if (currentUser) {
    fetchAllFiles();
  }

  if (currentUser) {
    getFolderDataByParentId();
  }

  // Folder-related logic here
};

export default useFolder;
