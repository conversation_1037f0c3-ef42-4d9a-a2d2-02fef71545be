import { db } from "../../firebase";
import { companies, companyId_constant } from "../../constants/data";

const CFR_COLLECTION = "cfr_userSession";

let timer = null;
export default function SecurityLoggingFunction(email, activity) {
  const dateTime = new Date();
  let dataObj = {
    dateTime,
    userName_email: email,
    activity,
    role: "NA",
  };
  if (timer) clearTimeout(timer);
  timer = setTimeout(() => {
    // db.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(CFR_COLLECTION)
    // 	.add(dataObj);
    // timer = null;
  }, 1000);
}
