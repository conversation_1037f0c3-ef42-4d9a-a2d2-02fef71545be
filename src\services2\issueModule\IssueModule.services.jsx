// import { companies, companyId_constant } from "../../constants/data";
// import { db } from "../../firebase";
// import { firebaseLooper } from "../../tools/tool";
import { dbConfig } from "../../infrastructure/db/db-config";
import axios from "axios";

// --------------- Read ALL NODE/PROJECT Data---------------

export const getData = async (name, handleEvent) => {
  try {
    // await db
    // 	.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(name)
    // 	.onSnapshot((snap) => {
    // 		const data = firebaseLooper(snap);
    // 		handleEvent(data);
    // 		return data;
    // 	});

    const res = await axios.get(
      `${dbConfig.url}/issueModule/${
        name === "issueModule" ? "projects" : "nodes"
      }`,
    );
    console.log("res", res?.data?.data);
    handleEvent(res?.data?.data);
  } catch (error) {
    console.error(error);
  }
};

// --------------- Read Specific NODE/PROJECT Data---------------

export const getSpecificData = async (name, id, handleEvent) => {
  try {
    // await db
    // 	.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(name)
    // 	.onSnapshot((snap) => {
    // 		const data = firebaseLooper(snap);
    // 		handleEvent(data);
    // 		return data;
    // 	});

    const res = await axios.get(
      `${dbConfig.url}/issueModule/${
        name === "issueModule" ? "projects" : "nodes"
      }/${id}`,
    );
    handleEvent(res?.data);
  } catch (error) {
    console.error(error);
  }
};

// --------------- Create NODE/PROJECT Data---------------

export const addData = async (name, data) => {
  // await db
  // 	.collection(companies)
  // 	.doc(companyId_constant)
  // 	.collection(name)
  // 	.add(data);

  await axios.post(
    `${dbConfig.url}/issueModule/${
      name === "issueModule" ? "projects" : "nodes"
    }`,
    data,
  );
};

// --------------- Update NODE/PROJECT Data ---------------

export const updateData = async (name, id, data) => {
  // await db
  // 	.collection(companies)
  // 	.doc(companyId_constant)
  // 	.collection(name)
  // 	.doc(id)
  // 	.update(data);

  await axios.put(
    `${dbConfig.url}/issueModule/${
      name === "issueModule" ? "projects" : "nodes"
    }/${id}`,
    data,
  );
};

// ------------ Delete NODE/PROJECT Data --------------

export const deleteData = async (name, id) => {
  // await db
  // 	.collection(companies)
  // 	.doc(companyId_constant)
  // 	.collection(name)
  // 	.doc(id)
  // 	.delete();

  await axios.delete(
    `${dbConfig.url}/issueModule/${
      name === "issueModule" ? "projects" : "nodes"
    }/${id}`,
  );
};
