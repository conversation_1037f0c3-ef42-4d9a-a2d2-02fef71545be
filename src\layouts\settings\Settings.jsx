/* eslint-disable react/jsx-no-target-blank */
import blue from "../../assets/images/blue.png";
import dark from "../../assets/images/dark.png";
import maroon from "../../assets/images/maroon.png";
import orange from "../../assets/images/orange.png";
import green from "../../assets/images/green.png";
import lightblue from "../../assets/images/lightblue.png";

import {
  Box,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormHelperText,
  InputLabel,
  TextField,
  Typography,
} from "@mui/material";
import { useCallback, useEffect, useState } from "react";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import { db } from "../../firebase";
import GlassApkForm from "./GlassApkForm";
import MobileApkForm from "./MobileApkForm";
import "./settings.scss";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { convertBase64 } from "../../hooks/useBase64";
import { sharedCss } from "../../styles/sharedCss";
import { Button } from "@mui/material";
import { makeStyles } from "@mui/styles";
import ThemeSettings from "./ThemeSetting";
import { Tooltip } from "@mui/material";
import { BsCheck } from "react-icons/bs";
// import { TooltipComponent } from '@syncfusion/ej2-react-popups';
import { themeColors, themeColorsPremium } from "../../constants/dummy";
import { MarkunreadTwoTone } from "@mui/icons-material";
import { useAuth } from "../../hooks/AuthProvider";
import {
  handleCamera,
  handleMicrophone,
  triggerFileSelection,
} from "./settingsUtils";
import ThemeSettingsDialog from "./ThemeSettingsDialog";
import LogoControl from "./LogoControl";
import { useCheckAccess } from "../../utils/useCheckAccess";
import { BackupRestore } from "./backupRestore";
import { useUtils } from "../../hooks/UtilsProvider";

const useCustomStyles = makeStyles((theme) => ({
  calenderContainer: {
    padding: "1rem",
    borderRadius: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  calenderOuterContainer: {
    width: "100%",
  },
  calenderInnerContainer: {
    display: "flex",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  addButton: {
    width: "max-content",
  },
  calenderPageContainer: {
    // padding: "1rem",
    // border: "1px solid gainsboro",
  },
}));

//databases refFromURL

const Settings = () => {
  // const [currentModelocal, setCurrentMode] = useState("Light");
  // const [currentColortemp, setCurrentColor] = useState("03C9D7");
  // const [currentColor, setCurrentColor] = useState("#03C9D7");

  // const [colortemp, setColortemp] = useState("#03C9D7");
  // const { setColor, setMode, currentMode, currentColor, setThemeSettings } =
  //   useStateContext();
  const customCss = useCustomStyles();
  // const [asset, setAsset] = useState({ moduleName: "", imageUrl: "" });
  // const [currentModetemp, setCurrentModetemp] = useState("Light");
  const [open, setOpen] = useState(false);
  const [logoFile, setLogoFile] = useState(null);
  // const [modules, setModules] = useState([]);
  const commonCss = sharedCss();
  // const getAllModules = async () => {
  //   await axios.get(`${dbConfig.url}/modules`).then((res) => {
  //     setModules(res.data);
  //   });
  // };
  const [user, setUser] = useState();
  const { currentUser } = useAuth();
  const { envData, isFetching } = useUtils(); // Access envData and isFetching
  const handleThemeDialogOpen = useCallback(() => setOpen(true), []);
  const handleThemeDialogClose = useCallback(() => setOpen(false), []);

  const hasUserPUTAccess = useCheckAccess("users", "PUT");

  const getuserDetails = async () => {
    const id = currentUser._id;
    await axios.get(`${dbConfig.url}/users/${id}`).then((response) => {
      setUser(response.data.data);
    });
  };

  // const updateUserTheme = async (color) => {
  //   const updatedData = { ...user, themecolor: color };
  //   const id = currentUser._id;
  //   await axios.put(`${dbConfig.url}/users/${id}`, updatedData).then(() => {
  //     toastMessageSuccess({
  //       message: "Theme Updated!",
  //     });
  //   });
  // };

  // const [cameraStatus, setCameraStatus] = useState('loading');
  // const [micStatus, setMicStatus] = useState('loading');

  // const getBrowserSettingsLink = () => {
  //   const ua = navigator.userAgent.toLowerCase();
  //   if (ua.includes('chrome')) return 'chrome://settings/content';
  //   if (ua.includes('firefox')) return 'about:preferences#privacy';
  //   if (ua.includes('safari')) return 'https://support.apple.com/en-us/HT209175';
  //   return null;
  // };

  useEffect(() => {
    // getAllModules();
    getuserDetails();
  }, []);

  // Helper to get role name from role ID
  const getRoleName = (roleValue, roles) => {
    if (!roles || roles.length === 0) return "";
    const roleObj = roles.find((r) => r.id === Number(roleValue));
    return roleObj ? roleObj.name : "";
  };

  // Check if the current user is an admin
  const isCurrentUserAdmin =
    user && envData.ROLES && !isFetching
      ? getRoleName(user.role, envData.ROLES).toLowerCase() === "admin"
      : false;

  // useEffect(() => {
  //   const divElement = document.getElementById("myDiv");
  //   if (!divElement) {
  //     return  () => {};
  //   }
  //   // Assuming colorModeTemp is a state variable that holds the temperature value (e.g., "Light")
  //   if (currentModetemp === "Dark") {
  //     // Hide or remove the element here
  //     divElement.style.display = "none";
  //   } else {
  //     divElement.style.display = "block";
  //   }
  // }, [currentModetemp]);

  // const handleImageUpload = async (e) => {
  //   const file = e.target.files[0];
  //   const base64 = await convertBase64(file);
  //   setAsset({ ...asset, imageUrl: base64 });
  // };

  // const handleDelete = async (id) => {
  //   await axios.delete(`${dbConfig.url}/modules/${id}`).then(() => {
  //     toastMessage({ message: "Deleted Assets" });
  //   });
  // };

  // const setModetemp = (e) => {
  //   setCurrentModetemp(e.target.value);
  //   localStorage.setItem("themeMode", e.target.value);
  // };
  // const setcolor = (color) => {
  //   setColortemp(color);
  //   console.log("temporarycolor", colortemp);

  //   localStorage.setItem("colorMode", color);
  // };

  // const ColorMode = () => {
  //   // setCurrentMode(currentModetemp);
  //   // setCurrentColor(colortemp);
  //   setMode(currentModetemp);
  //   setColor(colortemp);
  //   console.log("coloris", currentMode);
  //   console.log("coloris", currentColor);
  // };

  // const handleSubmit = async () => {
  //   if (asset.moduleName.length === 0 || asset.imageUrl.length === 0) {
  //     return toastMessage({
  //       message: "Form Incomplete ! Please fill in the data",
  //     });
  //   }

  //   await axios.post(`${dbConfig.url}/modules`, asset).then((response) => {
  //     console.log(response);
  //     toastMessageSuccess({ message: "Assets added successfully!" });
  //     setOpen(false);
  //   });
  // };

  // const style = {
  //   backgroundColor: "#2b2b2b",
  //   color: "#fff",
  // };

  return (
    <section className="settingSection">
      <div className={customCss.calenderPageContainer}>
        <div
          className={`${commonCss.headingContainer} border-radius-outer`}
          style={{ padding: "0.5rem 0.75rem" }}
        >
          <div className="info" style={{ alignSelf: "center" }}>
            <Typography variant="h4">Settings</Typography>
            <Typography variant="h6">
              You can change configurations of application here.
            </Typography>
          </div>

          {/* Modules Section */}
          {false && (
            <>
              {/* <div id="myDiv" style={{ alignSelf: "center" }}> */}
              {/* <p className="font-semibold text-xl ">Theme Colors</p> */}
              {/* <div className="flex gap-3"> */}
              {/* {themeColors.map((item, index) => (
                <Tooltip key={index} content={item.name} position="TopCenter">
                  <div
                    className="relative mt-2 cursor-pointer flex gap-5 items-center"
                    key={item.name}
                  >
                    <button
                      type="button"
                      className="h-10 w-10 rounded-full cursor-pointer"
                      style={{ backgroundColor: item.color }}
                      onClick={() => {
                        setcolor(item.color);
                      }}
                    >
                      <BsCheck
                        className={`ml-2 text-2xl text-white ${
                          item.color === colortemp ? "block" : "hidden"
                        }`}
                      />
                    </button>
                  </div>
                </Tooltip>
              ))} */}
              {/* </div> */}
              {/* </div> */}
              {/* <div> */}
              {/* <div> */}
              {/* <p className="font-semibold text-xl ">Theme Option</p>
              <div className={`mt-2 ${currentMode === "Light" ? 'hover:bg-slate-200' : 'hover:bg-slate-700'} px-1 rounded-2xl`}>
                <input
                  type="radio"
                  id="light"
                  name="theme"
                  value="Light"
                  className="cursor-pointer"
                  onChange={setModetemp}
                  checked={currentModetemp === "Light"}
                /> */}
              {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
              {/* <label htmlFor="light" className="ml-2 text-md cursor-pointer">
                  Light
                </label> */}
              {/* </div> */}
              {/* </div> */}

              {/* <div className={`mt-2 ${currentMode === "Light" ? 'hover:bg-slate-200' : 'hover:bg-slate-700'} px-1 rounded-2xl`}>
              <input
                type="radio"
                id="dark"
                name="theme"
                value="Dark"
                onChange={setModetemp}
                className="cursor-pointer"
                checked={currentModetemp === "Dark"}
              /> */}
              {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
              {/* <label htmlFor="dark" className="ml-2 text-md cursor-pointer">
                Dark
              </label> */}
              {/* </div> */}
              {/* </div> */}
            </>
          )}
        </div>

        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-start",
            marginTop: "3vw",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "1rem",
              alignItems: "flex-start",
            }}
          >
            <ButtonBasic
              variant={"contained"}
              onClick={handleThemeDialogOpen}
              buttonTitle={"Theme settings"}
              disabled={!hasUserPUTAccess}
            />
            <ButtonBasic
              variant={"contained"}
              onClick={handleCamera}
              buttonTitle={"Check camera permission"}
            />
            <ButtonBasic
              variant={"contained"}
              onClick={handleMicrophone}
              buttonTitle={"Check microphone permission"}
            />
            <BackupRestore />
          </div>
          {isCurrentUserAdmin && <LogoControl />}
        </Box>
        {/*<Typography variant="h6">Preview</Typography>*/}
        {/*
          <div>
            <Button
              variant="contained"
              onClick={() => {
                ColorMode();
                updateUserTheme(colortemp);
              }}
            >
              save
            </Button>
          </div>
        </Box>
          <div
            style={{
              marginTop: "1rem",
            }}
            className={commonCss.headingContainer}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "center",
              }}
            >
              {currentModetemp === "Light" && (
                <>
                  {colortemp === "#03C9D7" && (
                    <img src={lightblue} alt="light blue" />
                  )}
                  {colortemp === "#ff9100" && <img src={orange} alt="orange" />}
                  {colortemp === "#1E4DB7" && <img src={blue} alt="blue" />}
                  {colortemp === "#B80439" && <img src={maroon} alt="maroon" />}
                  {colortemp === "#0CB577" && <img src={green} alt="green" />}
                </>
              )}

              {currentModetemp === "Dark" && <img src={dark} alt="Dark" />}
            </div>
          </div>
        */}
      </div>
      {open && (
        <ThemeSettingsDialog
          open={open}
          onSave={handleThemeDialogClose}
          onCancel={handleThemeDialogClose}
        />
      )}
    </section>
  );
};

export default Settings;
