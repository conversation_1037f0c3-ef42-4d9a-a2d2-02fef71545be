import React, { useEffect, useState } from "react";
import { useParams, useLocation } from "react-router-dom";
import {
  companies,
  companyId_constant,
  maintenance,
  machines,
  hasTags,
} from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
// import "./machineData.scss";
import MachineDataHeader from "./MachineDataHeader";
import MaintenanceItem from "./MaintenanceItem";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import SearchIcon from "@mui/icons-material/Search";
import { SearchOutlined } from "@mui/icons-material";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  CircularProgress,
  <PERSON><PERSON>ield,
  Typography,
  Button,
  IconButton,
} from "@mui/material";
import TablePagination from "@mui/material/TablePagination";
import AddMaintenance from "./AddMaintenance";
import {
  useMaintenanceInfo,
  useMaintenanceInfoSeter,
} from "../../context/MaintenanceContext";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { sharedCss } from "../../styles/sharedCss";
import { makeStyles } from "@mui/styles";
import CommonDropDown from "../../components/commons/dropDown.component";
import TableHeader from "./TableHeader";
import NoDataComponent from "../../components/commons/noData.component";
import { common } from "@mui/material/colors";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";

const MaintenanceDataCalibration = () => {
  const [open, setOpen] = useState(false);
  const { mid } = useParams();
  const location = useLocation();
  const [details, setDetails] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [defaultType, setDefaultType] = useState(0);
  const [machineName, setMachineName] = useState("");
  const [hastTagsAll, setHastagsAll] = useState([]);
  const [dataLoading, setDataLoading] = useState(true);
  const [hastagsForFilter, setHastagsForFilter] = useState([]);
  const maintenanceInfoFromContext = useMaintenanceInfo();
  const maintenanceInfoSetter = useMaintenanceInfoSeter();
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  // Pagination states
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10); // Default to 10 items per page

  const hasCalibrationPOSTAccess = useCheckAccess("maintenance", "POST");
  const hasCalibrationGETAccess = useCheckAccess("maintenance", "GET");

  const mainIdFromAlarm = location.state?.mainId || null;

  const fetchAllMaintenance = async () => {
    try {
      const response = await axios.get(
        `${dbConfig.url}/maintenance/getFromMachine/${mid}`,
      );
      const maintenanceData = response?.data?.data || [];
      setDetails(maintenanceData.filter((obj) => obj.mid === mid));
    } catch (error) {
      console.error("ERROR IN MAINTENANCE:", error.message);
      setDetails([]);
    } finally {
      setDataLoading(false);
    }
  };

  const fetchCurrentMachine = async () => {
    await axios.get(`${dbConfig.url}/machines/${mid}`).then((response) => {
      setMachineName(response.data?.data?.title);
    });
  };

  const commonCss = sharedCss();
  useEffect(() => {
    fetchAllMaintenance();
    fetchCurrentMachine();
  }, [maintenanceInfoFromContext, refreshCount]);

  const onHastagSelect = (tag) => {
    let temp = [...hastagsForFilter, tag];
    setHastagsForFilter([...new Set(temp)]);
  };

  const handleOnChangeSearchTerm = (e) => {
    if (e.target.value === "" && hastagsForFilter.length > 0) {
      setSearchTerm("#");
    } else {
      setSearchTerm(e.target.value);
    }
  };

  const removeSelectedHastag = (index) => {
    let temp = hastagsForFilter;
    temp?.splice(index, 1);
    setHastagsForFilter([...temp]);
  };

  const defaultTypeSetter = (type) => {
    setDefaultType(type);
    maintenanceInfoSetter();
  };

  const filteredDetails = (data) => {
    if (data.type === defaultType) {
      if (
        (searchTerm === "" && hastagsForFilter?.length === 0) ||
        (searchTerm === "#" && hastagsForFilter?.length === 0) ||
        (searchTerm?.startsWith("#") && hastagsForFilter?.length === 0)
      ) {
        return data;
      } else if (searchTerm.startsWith("#") || hastagsForFilter?.length > 0) {
        let temp = hastagsForFilter.filter(
          (filterData) => filterData.doc_id === data.id,
        );
        if (temp?.length > 0) {
          return data;
        }
      } else if (
        data.title.toLowerCase().includes(searchTerm.toLocaleLowerCase())
      ) {
        return data;
      }
    }
  };

  const filteredData = details.filter(filteredDetails).filter(Boolean); // Ensure no undefined/null values
  const isFilteredDetailsAvailable = filteredData.length > 0;

  // Calculate the rows to display based on the current page and rows per page
  const paginatedDetails = filteredData.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage,
  );

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0); // Reset to first page when rows per page changes
  };

  return (
    <section>
      <MachineDataHeader />

      <div>
        <div
          className={`${commonCss.sectionContainer} ${commonCss.backgroundLight} border-radius-outer`}
        >
          <div className={commonCss.tableLable}>
            <Typography sx={{ ml: 2 }} fontWeight="bold" variant="h6">
              {defaultType === 0 ? "Calibration" : "Maintenance"}
            </Typography>
            <div className={commonCss.tableRightContent}
            style={{ display: "flex", alignItems: "center", gap: "1rem" }}>
              <div >
                <div>
                <TextField
                  label="Search"
                  placeholder={`Search by ${defaultType === 0 ? "sensor name" : "name"}`}
                  className={`${commonCss.searchBox} ${commonCss.inputAlignmentFix}`}
                  id="outlined-size-small"
                  size="small"
                  value={searchTerm}
                  onChange={(e) => handleOnChangeSearchTerm(e)}
                  InputProps={{
                    startAdornment: (
                      <IconButton>
                        <SearchOutlined />
                      </IconButton>
                    ),
                  }}
                />
                </div>
                <div className="flex absolute flex-wrap max-w-sm">
                  {hastagsForFilter?.map((data, index) => (
                    <div className="px-0.5 mx-0.5 mb-0.5 rounded-sm bg-cyan-50 shadow-sm text-xs">
                      #{data.title}
                      <i
                        onClick={() => removeSelectedHastag(index)}
                        className="ri-close-circle-fill hover:text-red-500 hover:cursor-pointer"
                      ></i>
                    </div>
                  ))}
                </div>
                <div className="absolute top-12 backdrop-blur-sm z-10 px-1 max-h-36 shadow-md overflow-y-scroll">
                  {searchTerm.startsWith("#") &&
                    hastTagsAll
                      ?.filter((fdata) =>
                        searchTerm?.slice(1)
                          ? fdata.title.includes(searchTerm?.slice(1))
                          : false,
                      )
                      .map((hData) => (
                        <div
                          onClick={() => onHastagSelect(hData)}
                          className="hover:cursor-pointer"
                        >
                          {hData.title}
                        </div>
                      ))}
                </div>
              </div>
              {false && (
                <div style={{ display: "flex" }}>
                  {defaultType === 0 ? (
                    <div className="text-red-500 mr-6 p-3 cursor-pointer underline font-bold">
                      Calibration
                    </div>
                  ) : (
                    <div
                      onClick={() => defaultTypeSetter(0)}
                      className="font-bold cursor-pointer mr-6 p-3"
                    >
                      Calibration
                    </div>
                  )}
                </div>
              )}
              <div style={{ display: "flex" }}>
                <CommonDropDown
                  dropDownLabel={"Select type"}
                  className={`${commonCss.dropDown} ${commonCss.inputAlignmentFix}`}
                  menuData={[{ _id: "_id_", label: "Calibration", value: 0 }]}
                  menuValue={defaultType}
                  handleChange={(event) =>
                    setDefaultType(event?.target?.value ?? defaultType)
                  }
                  menuItemDisplay={"label"}
                  menuItemValue={"value"}
                />
              </div>
              <div>
                <Button
                  variant="contained"
                  onClick={() => setOpen(true)}
                  disabled={!hasCalibrationPOSTAccess}
                >
                  Add Calibration
                </Button>
              </div>
            </div>
          </div>

          {hasCalibrationGETAccess ? (
            <div className="liveDataContainer">
              <TableContainer
                component={Paper}
                className="table border-radius-inner"
                sx={commonOuterContainerStyle}
                // sx={{ maxHeight: '500px', overflowY: 'auto' }} // Fixed height with scrollbar
              >
                <Table
                  sx={{ minWidth: 650 }}
                  // stickyHeader
                >
                  {" "}
                  {/* stickyHeader for fixed header */}
                  <TableHeader
                    currentMode={currentMode}
                    columns={[
                      {
                        label: defaultType === 0 ? "Sensor Name" : "Name",
                        align: "left",
                        width: defaultType === 0 ? "30%" : "15%",
                      },
                      { label: "Description", align: "left", width: "25%" },
                      ...(defaultType !== 1
                        ? [
                            {
                              label: "Period / Cycle",
                              align: "left",
                              width: "15%",
                            },
                          ]
                        : []),
                      { label: "Last Done", align: "left" },
                      { label: "Status", align: "center" },
                      { label: "Actions", align: "center" },
                    ]}
                  />
                  <TableBody>
                    {paginatedDetails.length > 0 ? (
                      paginatedDetails.map((data, index) => (
                        <MaintenanceItem
                          key={index}
                          data={data}
                          machineName={machineName}
                          defaultType={defaultType}
                          expandedByDefault={mainIdFromAlarm === data._id}
                        />
                      ))
                    ) : (
                      <NoDataComponent
                        cellColSpan={defaultType !== 1 ? 6 : 5} // Adjust colSpan based on columns
                        dataLoading={dataLoading}
                      />
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination Component */}
              {filteredData.length > 0 && (
              <TablePagination
                component="div"
                count={filteredData.length} // Total number of filtered rows
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                className={commonCss.tablePagination}
              />)}
            </div>
          ) : (
            <NotAccessible />
          )}
        </div>

        <Dialog open={open} fullWidth>
          <DialogTitle
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#212B36", color: "white" }
                : {}
            }
          >
            Add Calibration Schedule
          </DialogTitle>
          <DialogContent
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#212B36", color: "white" }
                : {}
            }
          >
            <AddMaintenance
              mid={mid}
              handleClose={() => setOpen(false)}
              machineName={machineName}
              useAt={"MaintenanceDataCalibration"}
            />
          </DialogContent>
        </Dialog>
      </div>
    </section>
  );
};

export default MaintenanceDataCalibration;
