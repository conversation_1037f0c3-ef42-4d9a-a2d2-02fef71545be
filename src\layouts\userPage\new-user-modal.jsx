import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Grid,
  Typography,
} from "@mui/material";
import React from "react";
import LdapConfigForm from "./ldapConfigForm";
import LDAPUsersTable from "./LdapUsersTable copy";
import UsersListTable from "./UsersListTable copy";
import PropTypes from "prop-types";

const NewUserModal = ({
  open,
  onClose,
  fetchAllUsers,
  setLdapUsers,
  setLoading,
  setGlobalConfigState,
  globalConfigState,
  allUsersList = [],
  ldapUsers,
  getAllLDAPUsersUsingConfig,
  loading = false,
  showAddUser,
  setloadingUT,
  loadingUT = false,
  handleCloseAddUser = () => {},
}) => {
  return (
    <Dialog
      open={open}
      fullWidth
      maxWidth="xl"
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: 2,
          boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
          backgroundColor: "rgb(255, 244, 229)",
        },
      }}
    >
      <DialogContent>
        <Box
          sx={{
            minHeight: "85vh",
            display: "flex",
            flexDirection: "column",
            gap: 1,
          }}
        >
          <LdapConfigForm
            setLdapUsers={setLdapUsers}
            setLoading={setLoading}
            setGlobalConfigState={setGlobalConfigState}
          />

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  backgroundColor: "#fff",
                  borderRadius: 2,
                  boxShadow: "0 2px 10px rgba(0,0,0,0.05)",
                  p: 2,
                  height: "100%",
                  transition: "all 0.3s ease",
                  "&:hover": { boxShadow: "0 4px 15px rgba(0,0,0,0.1)" },
                }}
              >
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 600,
                    mb: 2,
                    color: "#424242",
                  }}
                >
                  Available LDAP Users
                </Typography>
                <LDAPUsersTable
                  isModelOpen={showAddUser}
                  refetchUsers={fetchAllUsers}
                  refetchLdapUsersConfig={() =>
                    getAllLDAPUsersUsingConfig(
                      globalConfigState,
                      setLdapUsers,
                      setLoading,
                    )
                  }
                  ldapUsers={ldapUsers}
                  dbUsersList={allUsersList}
                  loading={loading}
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  backgroundColor: "#fff",
                  borderRadius: 2,
                  boxShadow: "0 2px 10px rgba(0,0,0,0.05)",
                  p: 2,
                  height: "100%",
                  transition: "all 0.3s ease",
                  "&:hover": { boxShadow: "0 4px 15px rgba(0,0,0,0.1)" },
                }}
              >
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 600,
                    mb: 2,
                    color: "#424242",
                  }}
                >
                  Existing Users
                </Typography>
                <UsersListTable
                  isModelOpen={showAddUser}
                  allUsersList={allUsersList}
                  fetchAllUsers={fetchAllUsers}
                  setloading={setloadingUT}
                  loading={loadingUT}
                  showActions={true}
                />
              </Box>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions sx={{ borderTop: "1px solid #e0e0e0" }}>
        <Button
          onClick={onClose}
          variant="contained" // Changed to contained from outlined
          color="error"
          sx={{
            borderRadius: 1,
            transition: "all 0.3s ease",
            bgcolor: "#d32f2f", // Solid red color
            "&:hover": { bgcolor: "#b71c1c" }, // Darker red on hover
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};
NewUserModal.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  fetchAllUsers: PropTypes.func,
  setLdapUsers: PropTypes.func,
  setLoading: PropTypes.func,
  setGlobalConfigState: PropTypes.func,
  globalConfigState: PropTypes.object,
  allUsersList: PropTypes.array,
  ldapUsers: PropTypes.array,
  getAllLDAPUsersUsingConfig: PropTypes.func,
  loading: PropTypes.bool,
  showAddUser: PropTypes.bool,
  setloadingUT: PropTypes.func,
  loadingUT: PropTypes.bool,
  handleCloseAddUser: PropTypes.func,
};
export default NewUserModal;
