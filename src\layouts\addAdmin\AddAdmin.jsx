import React, { useState, useEffect, useContext } from "react";
import "./addAdmin.scss";
import { alpha, styled } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import InputLabel from "@mui/material/InputLabel";
import { Link } from "react-router-dom";
import { Box } from "@mui/material";
import { useStorage } from "../../hooks/useStorage";
import { DropzoneArea } from "material-ui-dropzone";
import { alluser, companies, users } from "../../constants/data";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { db } from "../../firebase";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import AddUserForm from "../addUserForm/AddUserForm";
import { useStateContext } from "../../context/ContextProvider";
import TextField from "@mui/material/TextField";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";
import { useAuth } from "../../hooks/AuthProvider";
import { firebaseLooper } from "../../tools/tool";
import { AddUserContext } from "../../services2/users/addUser.context";

const AddAdmin = () => {
  const [firstName, setFirstName] = useState();
  const [lastName, setLastName] = useState();
  const [fName, setFName] = useState();
  const [lName, setLName] = useState();
  const [adminName, setAdminName] = useState();
  const [email, setEmail] = useState();
  const [phone, setPhone] = useState();
  const [address, setAddress] = useState("");
  const [file, setFile] = useState();
  const [userFor, setUserFor] = useState("lsiAdmin");
  const [password, setPassword] = useState("");
  const [allCompanyIds, setAllCompanyIds] = useState([]);
  const [allUsersList, setAllUsersList] = useState([]); // all emails
  const [emailMatches, setEmailMatches] = useState(false);
  const { currentColor, currentColorLight, currentMode } = useStateContext();
  //const {allUsers, setAllUsers} = useState([]); // all emails
  const { signup } = useAuth();
  const { companyId, setCompanyId } = useContext(AddUserContext);

  const { progress, url } = useStorage(file, "companies_dp");

  useEffect(() => {
    // db.collection(companies).onSnapshot((snapShot) => {
    //   let temp = [];
    //   snapShot.forEach((data) => temp.push(data?.id));
    //   setAllCompanyIds([...temp])
    //   // console.log(temp)
    // })
    // db.collection(alluser).onSnapshot((snap)=>{
    //  const data = firebaseLooper(snap);
    //  setAllUsersList(data);
    //  //console.log("all users: ", data)
    // })
  }, []);
  //
  const handleEmail = (e) => {
    //alert("1")
    const re =
      /^[a-z0-9][a-z0-9-_\.]+@([a-z]|[a-z0-9]?[a-z0-9-]+[a-z0-9])\.[a-z0-9]{2,10}(?:\.[a-z]{2,10})?$/;
    let emailTemp = e.target.value;
    if (emailTemp && re.test(emailTemp)) {
      allUsersList.find((data) => {
        if (data.email.search(emailTemp) === 0) {
          //console.log("true");
          setEmailMatches(true);
        }
      });
    }
  };
  //
  const handleChange = (loadedFiles) => {
    let selectedFile = loadedFiles[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  };
  //
  const addAdmin = async () => {
    //console.log(firstName,':',lastName,':',adminName,':',file,':',email,':',address,':',phone)
    // if (userFor == 'lsiAdmin') {
    //   if (firstName && lastName && adminName  && email  && phone && password && !emailMatches) {
    //     try {
    //       await signup(email, password);
    //    db.collection(alluser).add({
    //   admin: true, // use unknownon , remove after discussion
    //   lsi: true , //default static
    //   firstName: firstName,
    //   lastName: lastName,
    //   fname: firstName,//
    //   lname: lastName,//
    //   phone: phone,
    //   email: email,
    //   //address: address,
    //   username: adminName,
    //   role: 'admin', //static
    //   url: url,
    // })
    //   .then(() => {
    //     setFirstName("");
    //     setLastName("");
    //     setFName("");
    //     setLName("");
    //     setEmail("");
    //     setPhone("");
    //     setAddress("");
    //     setAdminName("");
    //     toastMessageSuccess({ message: "successfully Added", type: "success" });
    //   }
    //   )
    // } catch (error) {
    //   console.log("Add Admin error: ", error)
    //   toastMessage({ message: error, type: "error" })
    // }
    // }
    //     else {
    //       toastMessage({ message: 'Missing field' })
    //  }
    //  }
  };

  //
  const phoneSetter = (inputtxt) => {
    const phoneno = new RegExp("^[0-9]+$");
    if (inputtxt?.match(phoneno) && inputtxt?.length < 11) {
      setPhone(inputtxt);
    } else {
      if (inputtxt === "") {
        setPhone("");
      }
    }
  };

  return (
    <section className="addAdminForm">
      <div
        className="addAdminFormContainer"
        style={
          currentMode === "Dark"
            ? { backgroundColor: "#161C24", color: "white" }
            : { backgroundColor: currentColorLight }
        }
      >
        <div className="title">
          <h3>Add New Admin</h3>
        </div>
        <div className="desc">
          <p>Mandatory information</p>
        </div>

        <div className="adminFormContainer">
          <Box
            component="form"
            noValidate
            sx={{
              display: "grid",
              gridTemplateColumns: { sm: "1fr 1fr" },
              gap: 2,
            }}
          >
            <FormControl
              fullWidth
              size="small"
              className="shadow-sm shadow-yellow-600 rounded-md"
            >
              <InputLabel id="demo-simple-select-label ">For</InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={userFor}
                label="For"
                onChange={(e) => setUserFor(e.target.value)}
              >
                <MenuItem value={"lsiAdmin"}>LSI Admin</MenuItem>
                <MenuItem value={"company"}>Company</MenuItem>
              </Select>
            </FormControl>

            <FormControl
              fullWidth
              size="small"
              className={
                userFor == "company"
                  ? "shadow-sm shadow-yellow-600 rounded-md"
                  : ""
              }
              disabled={userFor == "lsiAdmin" ? true : false}
            >
              <InputLabel id="demo-simple-select-label ">Company</InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={companyId}
                label="Company"
                onChange={(e) => setCompanyId(e.target.value)}
              >
                {allCompanyIds?.map((data, index) => (
                  <MenuItem key={index} value={data}>
                    {data}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {(userFor == "lsiAdmin" || !companyId) && (
              <>
                <div className="labelFields">
                  <InputLabel shrink htmlFor="adminFirstName">
                    First Name
                  </InputLabel>
                  <TextField
                    //label="First Name"
                    id="adminFirstName"
                    placeholder="eg. Michael"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    onBlur={() => setFirstName(firstName?.trim())}
                    size="small"
                    fullWidth
                  />
                </div>

                <div className="labelFields">
                  <InputLabel shrink htmlFor="adminFirstName">
                    Last Name
                  </InputLabel>
                  <TextField
                    //label="Last Name"
                    id="adminLastName"
                    placeholder="eg. Simpson"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    onBlur={() => setLastName(lastName?.trim())}
                    size="small"
                    fullWidth
                  />
                </div>

                <div className="labelFields">
                  <InputLabel shrink htmlFor="adminFirstName">
                    Admin Name
                  </InputLabel>
                  <TextField
                    //label="Admin Name"
                    id="adminName"
                    placeholder="eg. michael123"
                    value={adminName}
                    onChange={(e) => setAdminName(e.target.value)}
                    onBlur={() => setAdminName(adminName?.trim())}
                    size="small"
                    fullWidth
                  />
                </div>

                <div className="labelFields">
                  <InputLabel shrink htmlFor="userEmail">
                    {!emailMatches && (
                      <span className={emailMatches ? "text-red-600" : ""}>
                        Email Address
                      </span>
                    )}
                    {emailMatches && (
                      <span
                        className={
                          emailMatches ? "text-red-600 animate-pulse pl-4" : ""
                        }
                      >
                        Email Already exist
                      </span>
                    )}
                  </InputLabel>
                  <TextField
                    //label="Email Address"
                    type="email"
                    id="adminEmail"
                    placeholder="eg. <EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    //onBlur={() => setEmail(email?.trim())}
                    onBlur={(e) => handleEmail(e)}
                    onFocus={() => setEmailMatches(false)}
                    size="small"
                    fullWidth
                  />
                </div>
                <div className="labelFields">
                  <InputLabel shrink htmlFor="adminFirstName">
                    Password
                  </InputLabel>
                  <TextField
                    //label="Password"
                    id="adminLastName"
                    placeholder="eg. Simpson"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    onBlur={() => setPassword(password?.trim())}
                    size="small"
                    fullWidth
                  />
                </div>

                <div className="labelFields">
                  <InputLabel shrink htmlFor="adminFirstName">
                    Phone Number
                  </InputLabel>
                  <TextField
                    //label = "Phone Number"
                    // type=''
                    id="adminMobile"
                    placeholder="eg. 8435973143"
                    value={phone}
                    onChange={(e) => phoneSetter(e.target.value)}
                    onBlur={() => setPhone(phone?.trim())}
                    size="small"
                    fullWidth
                  />
                  {/* <input
                  className={currentMode === "Dark" ? 'p-2 rounded-md border-2 border-gray-500 w-full' :
                    ' p-2 rounded-md border-2 border-gray-300 w-full'}
                    style={currentMode === 'Dark' ? { backgroundColor: '#161C24', color: 'white', marginBottom: '12px'  } :
                     { backgroundColor: currentColorLight , marginBottom: '12px' }}
                  placeholder="eg. 8435973143"
                  value={phone}
                  onChange={(e) => phoneSetter(e.target.value)}
                  onBlur={() => setPhone(phone?.trim())}
                  required
                  /> */}
                </div>

                {/* <div className="labelFields">
                <TextField
                  label="Address"
                  id="adminLocation"
                  placeholder="eg. 123 street , city"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  onBlur={() => setAddress(address?.trim())}
                  size="small"
                  fullWidth
                />
              </div> */}

                <div className="labelFields">
                  <InputLabel shrink htmlFor="companyLogo">
                    Select Avatar
                  </InputLabel>
                  <DropzoneArea
                    showFileNames
                    onChange={(loadedFiles) => handleChange(loadedFiles)}
                    dropzoneText="Drag and Drop / Click to ADD Media"
                    acceptedFiles={["image/jpeg", "image/png", "image/bmp"]}
                    showAlerts={false}
                    filesLimit={1}
                  />
                  {file ? <p> {progress} % Uploaded</p> : null}
                </div>
              </>
            )}
          </Box>

          {userFor == "lsiAdmin" && (
            <>
              <div className=" flex justify-between">
                <Link to="/admins">
                  <ButtonBasicCancel buttonTitle="Cancel" />
                </Link>
                <ButtonBasic buttonTitle="Add" onClick={() => addAdmin()} />
              </div>
            </>
          )}
        </div>
      </div>
      {userFor == "company" && companyId && (
        <div className="addAdminFormContainerForcompany">
          <AddUserForm companyIdProp={companyId} />
        </div>
      )}
    </section>
  );
};

export default AddAdmin;
