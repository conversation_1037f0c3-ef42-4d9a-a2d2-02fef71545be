import React from "react";
import { Link } from "react-router-dom";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import { themeColors } from "../../infrastructure/theme";

const CompanyHeaderContainer = () => {
  const { currentColor, currentColorLight, currentMode } = useStateContext();
  return (
    <div
      className={
        currentMode === "Dark"
          ? "card relative companyPageInfoContainer"
          : "card relative bg-gray-100 companyPageInfoContainer"
      }
      style={
        currentMode === "Dark"
          ? { backgroundColor: themeColors.dark.primary, color: "white" }
          : { backgroundColor: currentColorLight }
      }
    >
      <div className="info">
        <h3 style={currentMode === "Dark" ? { color: currentColorLight } : {}}>
          Companies List
        </h3>
        <p style={currentMode === "Dark" ? { color: currentColorLight } : {}}>
          Information about all the Companies.
        </p>
      </div>
      <div className="btn">
        <Link to="/add-company">
          {/* <button className="addCompanyBtn">Add new Company</button> */}
          <ButtonBasic buttonTitle="Add new Company" />
        </Link>
      </div>
    </div>
  );
};

export default CompanyHeaderContainer;
