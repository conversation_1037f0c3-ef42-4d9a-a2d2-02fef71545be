import React from "react";
import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import { makeStyles } from "@mui/styles";

const useStyles = makeStyles((theme) => ({
  textfieldStyle: {
    "& .MuiOutlinedInput-root": {
      "& fieldset": {
        borderColor: theme.palette.custom.textColor,
      },
      "&:hover fieldset": {
        borderColor: theme.palette.primary.main, // set the hover color based on the disabled prop
      },
      "&.Mui-focused fieldset": {
        borderColor: theme.palette.primary.main, // set the focus color based on the disabled prop
      },
    },
    "& .MuiInputLabel-root": {
      color: theme.palette.custom.textColor, // set the label color based on the disabled prop
    },
    "& .MuiInputBase-input": {
      color: theme.palette.custom.textColor, // set the input text color based on the disabled prop
    },
  },
  passwordContainer: {
    backgroundColor: theme.palette.custom.backgroundForth,
    color: theme.palette.custom.textColor,
    padding: "1.5rem",
    borderRadius: "10px",
  },
  changePasswordTitle: {
    fontSize: "1.5rem",
    marginBottom: "1.5rem",
  },
  passContainerInfo: {
    marginTop: "1.5rem",
  },
  heading: {
    marginBottom: "1rem",
  },
  desc: {
    marginBottom: "1rem",
    "& ul": {
      paddingLeft: "1rem",
      "& li": {
        listStyleType: "disc",
      },
    },
  },
}));

const ChangePasswordForm = ({
  password,
  setPassword,
  confirmPass,
  setConfirmPass,
}) => {
  const classes = useStyles();
  return (
    <Box style={{ display: "flex", gap: "1rem", marginTop: "1rem" }}>
      <TextField
        onChange={(e) => setPassword(e.target.value)}
        fullWidth
        label="New Password"
        id="new pass"
        value={password}
        className={classes.textfieldStyle}
      />
      <TextField
        onChange={(e) => setConfirmPass(e.target.value)}
        fullWidth
        value={confirmPass}
        label="Confirm Password"
        id="confirm pass"
        className={classes.textfieldStyle}
      />
    </Box>
  );
};

export default ChangePasswordForm;
