.accountContainer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  color: #344767;
  border-radius: 10px;

  // left container menu
  .leftContainer {
    position: sticky;
    top: 0;
    width: 0%;
    // box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.05);
    box-shadow: rgba(0, 0, 0, 0.05) 0rem 1.25rem 1.6875rem 0rem;
    border-radius: 10px;
    padding: 1rem;
    height: 250px;
    ul {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      li {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 0.7rem;
        margin: 0.2rem;
        border-radius: 10px;
        &:hover {
          background: #e9ecef;
          color: #344767;
          opacity: 1;
        }
        .icons {
          font-size: 1rem;
        }
        .text {
          padding-left: 0.7rem;
          p {
            font-size: 1rem;
            font-weight: 400;
            opacity: 0.85;
            text-transform: capitalize;
          }
        }
      }
    }
  }

  // right side form
  .rightContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 10px;

    // profile styles
    .profileContainer {
      width: 100%;
      box-shadow: rgba(0, 0, 0, 0.05) 0rem 1.25rem 1.6875rem 0rem;
      border-radius: 10px;
      padding: 1rem;
      height: 110px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .info {
        display: flex;
        align-items: center;
        .avatar {
          width: 80px;
          height: 70px;
          img {
            border-radius: 10px;
            width: 100%;
            height: 100%;
          }
        }
        .description {
          padding-left: 2rem;
          display: flex;
          flex-direction: column;
          .name {
            p {
              font-size: 1.3rem;
              font-weight: 500;
            }
            .position {
              p {
                font-size: 0.7rem;
              }
            }
          }
        }
      }
      .activeswitch {
        font-size: 0.9rem;
      }
    }

    .basicInfoContainer,
    .passwordContainer,
    .deleteContainer {
      margin-top: 2rem;
      width: 100%;
      box-shadow: rgba(0, 0, 0, 0.05) 0rem 1.25rem 1.6875rem 0rem;
      border-radius: 10px;
      padding: 1rem;

      .title {
        margin-bottom: 1rem;
        h3 {
          font-size: 1.3rem;
          font-weight: 500;
        }
      }
    }

    // password container styles
    .passwordContainer {
      .mb {
        margin-bottom: 1.3rem;
      }
      .passContainerInfo {
        position: relative;
        .heading {
          margin-top: 2rem;
          h3 {
            font-size: 1.4rem;
            font-weight: 500;
          }
        }
        .desc {
          margin: 1rem 0;
          h4 {
            font-size: 1.1rem;
            font-weight: 500;
            opacity: 0.9;
          }
          ul {
            padding-left: 2rem;
            margin-top: 0.6rem;
            li {
              list-style-type: circle;
              opacity: 0.9;
              font-size: 0.9rem;
            }
          }
        }

        .updateBtn {
          position: absolute;
          bottom: 0;
          right: 2rem;
          // button {
          //   background-image: linear-gradient(
          //     310deg,
          //     rgb(20, 23, 39),
          //     rgb(58, 65, 111)
          //   );
          //   color: white;
          //   text-transform: uppercase;
          //   font-weight: bold;
          //   cursor: pointer;
          //   outline: none;
          //   padding: 0.76rem 2rem;
          //   font-size: 0.75rem;
          //   border: none;
          //   border-radius: 10px;
          //   transition: all 150ms ease-in 0s;
          //   box-shadow: none;
          //   &:hover {
          //     opacity: 0.95;
          //     transform: scale(1.1);
          //   }
          // }
        }
      }
    }

    // Delete account styles

    .deleteContainer {
      margin-bottom: 1rem;
      .desc {
        margin: 1rem 0;
        p {
          font-size: 0.9rem;
          opacity: 0.95;
        }
      }
      .confirmation {
        padding: 1rem 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .leftsidebtn {
          padding-left: 0.5rem;
        }
        .rightsidebtn {
          display: flex;
          align-items: center;

          .deleteBtn {
            padding-left: 1rem;
          }
        }
      }
    }
  }
}
