import { useAuth } from "../../hooks/AuthProvider";
import { useStateContext } from "../../context/ContextProvider";
import BasicInfo from "./BasicInfo";
import ChangePassword from "./ChangePassword";
import ProfileContainer from "./ProfileContainer";
import { makeStyles } from "@mui/styles";
import { sharedCss } from "../../styles/sharedCss";

const useCustomStyles = makeStyles((theme) => ({
  accountContainer: {
    // padding: "1rem",
    // borderRadius: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  accountOuterContainer: {
    width: "100%",
  },
  accountInnerContainer: {
    display: "flex",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  addButton: {
    width: "max-content",
  },
  accountPageContainer: {
    // padding: "1rem",
    // border: "1px solid gainsboro",
  },
}));

const Accounts = () => {
  const { currentMode } = useStateContext();
  const { updatePassword } = useAuth();
  const customCss = useCustomStyles();
  const commonCss = sharedCss();


  return (
      <div className={`${commonCss.sectionContainer} border-radius-inner`}>
        {/* <ProfileContainer /> */}
        <BasicInfo />
        {/* <ChangePassword updatePassword={updatePassword} /> */}
        {/* <DeleteAccount /> */}
      </div>
  );
};

export default Accounts;
