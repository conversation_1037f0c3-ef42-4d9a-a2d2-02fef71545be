import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Card,
  CardContent,
} from "@mui/material";

const ForecastTable = ({ timestamps, values, forecastValues }) => {
  return (
    <Card sx={{ p: 2, mt: 4, textAlign: "center" }}>
      <CardContent>
        <Typography variant="h6">Machine Forecast Data</Typography>
        <TableContainer component={Paper} sx={{ mt: 2 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <strong>Time</strong>
                </TableCell>
                <TableCell>
                  <strong>Current Value</strong>
                </TableCell>
                <TableCell>
                  <strong>Forecasted Value</strong>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {timestamps.map((time, index) => (
                <TableRow
                  key={time}
                  sx={{
                    backgroundColor:
                      index < values.length - 3 ? "inherit" : "#f0f0f0",
                  }}
                >
                  <TableCell>{new Date(time).toLocaleTimeString()}</TableCell>
                  <TableCell>
                    {values[index] !== null ? values[index] : "-"}
                  </TableCell>
                  <TableCell>
                    {forecastValues[index] !== null
                      ? forecastValues[index]
                      : "-"}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );
};

export default ForecastTable;
