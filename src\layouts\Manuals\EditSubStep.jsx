import React, {useEffect, useMemo, useState} from "react";
import {DropzoneArea} from "material-ui-dropzone";
import {companies, companyId_constant} from "../../constants/data";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import {firebaseLooper} from "../../tools/tool";
import {
  Box,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  Button,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import {useStorage} from "../../hooks/useStorage";
import {db} from "../../firebase";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import {themeColors} from "../../infrastructure/theme";
import {useStateContext} from "../../context/ContextProvider";
import {convertBase64} from "../../hooks/useBase64";
import axios from "axios";
import {dbConfig} from "../../infrastructure/db/db-config";
import {useMongoRefresh} from "../../services/mongo-refresh.context";
import {ButtonBasic, ButtonBasicCancel} from "../../components/buttons/Buttons";
import {
  useDeleteMachineCfr,
  useEditMachineCfr,
} from "../../hooks/cfr/machineCfrProvider";
import {useAuth} from "../../hooks/AuthProvider";
import {useParams, useNavigate} from "react-router-dom";
import GetPreviewComponent from "../../components/commons/getPreview.component";

const EditSubStep = ({
  collectionName,
  step,
  newStep,
  parent,
  handleClose,
  onClick,
  sensorList,
  setIsEditing,
  setIsMediaPreviewOpen,
}) => {
  const {mid} = useParams();

  const {currentUser} = useAuth();
  const deletetrainingsubstepcfr = useDeleteMachineCfr();
  const edittrainingsubstepcfr = useEditMachineCfr();
  const editmaintenancesubstepcfr = useEditMachineCfr();

  const [title, setTitle] = useState(step.title ? step.title : "");
  const [desc, setDesc] = useState(step.desc ? step.desc : "");
  const [type, setType] = useState(step.type ? step.type : "");
  const [format, setFormat] = useState(step.format ? step.format : "");
  const [file, setFile] = useState(null);
  const [selectedFileForStorage, setSelectedFileForStorage] = useState(""); // for storage
  const [sensor, setSensor] = useState(newStep.sensor ? newStep.sensor : []);
  const [sensorValue, setSensorValue] = useState("");
  const typesImages = ["image/png", "image/jpeg", "image/jpg"];
  const videoTypes = ["video/mp4", "video/mkv", "video/mov"];
  const audioTypes = ["audio/mp3", "audio/mpeg"];
  const [imageUrl, setImageUrl] = useState("");
  const {currentMode} = useStateContext();
  const {refreshCount, setRefreshCount} = useMongoRefresh();
  const [errorMessage, setErrorMessage] = useState("");
  const [errorMessage2, setErrorMessage2] = useState("");

  useEffect(() => {
    setIsMediaPreviewOpen({type: file?.type?.split("/")[0] ?? format});
  }, [setIsEditing]);

  const menuItemTheme = {
    backgroundColor:
      currentMode === "Dark"
        ? themeColors.dark.primary
        : themeColors.light.primary,
  };
  function getPreview(url) {
    if (format === "image") {
      return <img width="100" src={url} />;
    } else if (format === "video") {
      return (
        <video
          style={{width: "90%", marginTop: "20px", justifyContent: "center"}}
          controls
          src={url}
          alt="First slide"
        />
      );
    } else if (format === "audio") {
      return (
        <audio
          style={{
            marginTop: "15%",
            marginRight: "50px",
            marginBottom: "20px",
          }}
          controls>
          <source src={url} />
        </audio>
      );
    }
  }
  const handleTitleChange = e => {
    const newValue = e.target.value;
    if (newValue.trim() === "") {
      setErrorMessage("Title cannot be Empty");
    } else {
      setErrorMessage("");
    }
    setTitle(e.target.value);
  };
  const handleDescChange = e => {
    const newValue = e.target.value;
    if (newValue.trim() === "") {
      setErrorMessage2("Description cannot be Empty");
    } else {
      setErrorMessage2("");
    }
    setDesc(e.target.value);
  };
  const handleChange = async loadedFiles => {
    let selectedFile = loadedFiles[0];
    const base64 = await convertBase64(selectedFile);
    setSelectedFileForStorage(selectedFile);
    setIsMediaPreviewOpen({type: selectedFile.type?.split("/")[0]});

    if (selectedFile) {
      if (type === "" || format === "") {
        toastMessage({
          message: "Please Select a Type / Format first to proceed",
        });
      }
      if (format === "image") {
        if (typesImages.includes(selectedFile.type)) {
          setFile(selectedFile);
          setImageUrl(base64);
        } else {
          setFile(null);
          toastMessage({message: "Please select an image file (png or jpg)"});
        }
      } else if (format === "video") {
        if (videoTypes.includes(selectedFile.type)) {
          setFile(selectedFile);
          setImageUrl(base64);
        } else {
          setFile(null);
          toastMessage({message: "Please select a video file (mp4 or mkv)"});
        }
      } else if (format === "audio") {
        if (audioTypes.includes(selectedFile.type)) {
          setFile(selectedFile);
          setImageUrl(base64);
        } else {
          setFile(null);
          toastMessage({message: "Please select an audio file (mp3 )"});
        }
      }
    }
  };

  const handleMediaDelete = async () => {
    setFile(null);
    setImageUrl("");
    setIsMediaPreviewOpen({type: step?.format});
  };

  // const prePopData = (data) => {
  //   setSensor((pre) => pre.filter((dataItem) => dataItem !== data))
  // }

  const prePopData = idx => {
    let temp = sensor;
    temp.splice(idx, 1);
    setSensor([...temp]);
  };

  const handleSubmit = async e => {
    setIsEditing(false);
    e.preventDefault();
    //delete the existing file from storage if we have changed thed th file
    if (imageUrl.length) {
      axios
        .post(`${dbConfig?.url_storage}/deleteImage`, {file_name: step?.url})
        .then(res1 => {
          console.log(res1.data?.message, "updated successfully");
        })
        .catch(err => {
          console.log("delte file from storage err:", err);
        });
    }
    //
    const data = {
      ...step,
      title,
      desc,
      //url: imageUrl ? imageUrl : step.url,
      type,
      format,
    };

    console.log(data);
    // uplad the file first and get the refrence
    if (selectedFileForStorage) {
      let fd = new FormData();
      fd.append("image", selectedFileForStorage);
      var resTemp = await axios
        .post(`${dbConfig?.url_storage}/upload`, fd, {})
        .then(res1 => {
          console.log("storage:", res1);
          return res1;
        })
        .catch(err => {
          console.log("storage error:", err);
        });
    }
    const date = new Date();
    const data2 = {
      activity: "substep edited",
      dateTime: date,

      description: "a substep is edited",
      machine: mid,
      module: "Training",
      username: currentUser.username,
    };

    const data3 = {
      activity: "substep edited",
      dateTime: date,
      description: "a substep is edited",
      machine: mid,
      module: "Maintenance",
      username: currentUser.username,
    };

    const localUrl =
      parent !== "Alarm SOP"
        ? `${dbConfig.url}/sub-steps/${step._id}`
        : `${dbConfig.url}/alarmSopSubStepData/${step._id}`;

    await axios
      .put(localUrl, {
        ...data,
        url: selectedFileForStorage ? resTemp?.data?.data : step?.url,
      })
      .then(res => {
        if (parent == "Maintenance") {
          editmaintenancesubstepcfr(data3);
        } else if (parent !== "Alarm SOP") {
          edittrainingsubstepcfr(data2);
        }
        toastMessageSuccess({message: `${title} has been updated`});
        handleClose();
        setRefreshCount(refreshCount + 1);
      });
  };

  const {progress, url} = useStorage(file);

  const previewContainerHeight = useMemo(() => {
    if (!file) {
      return step?.format !== "audio" && step?.format !== "text"
        ? "400px"
        : "60px";
    }

    return file?.type?.split("/")[0] !== "audio" &&
      file?.type?.split("/")[0] !== "text"
      ? "400px"
      : "60px";
  }, [format, file, step]);

  // ! here setTimeout is used because ref

  const handleCancel = () => {
    setTimeout(() => {
      handleClose();
      setIsMediaPreviewOpen(null);
    }, 1);
  };

  return (
    <div className="h-fit">
      <div className="flex justify-center mt-1">
        <div className="text-xl font-bold">Edit sub-step</div>
      </div>

      <div
        className="flex flex-col flex-wrap justify-start justify-items-center"
        style={{minHeight: "80vh", padding: "1rem"}}>
        <form onSubmit={handleSubmit}>
          <InputLabel style={{marginBottom: "10px"}}>SubStep Title</InputLabel>
          <TextField
            onChange={handleTitleChange}
            value={title}
            onBlur={() => setTitle(title.trim())}
            style={{marginBottom: "10px"}}
            variant="outlined"
            fullWidth
            required
            error={!!errorMessage}
            helperText={errorMessage}
          />
          {/* <InputLabel style={{ marginBottom: "10px" }}>
            Sensor Values
          </InputLabel>
          <section style={{ marginBottom: "10px" }} className="flex">
            <Box sx={{ width: "100%" }}>
              <FormControl fullWidth>
                <Select
                  value={sensorValue}
                  onChange={(e) => setSensorValue(e.target.value)}
                >
                  {sensorList?.map((mData) => (
                    <MenuItem value={mData.tag} sx={menuItemTheme}>
                      {" "}
                      {mData.tag}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            <IconButton
              onClick={() =>
                sensorValue
                  ? setSensor([...sensor, sensorValue])
                  : toastMessageWarning({ message: "Missing sensor value" })
              }
            >
              <span className="font-normal text-sm" data-title="Add">
                <AddIcon className="text-green-800 " />
              </span>
            </IconButton>
          </section>

          {sensor.length > 0 && (
            <div
              style={{
                marginBottom: "20px",
                display: "flex",
                flexDirection: "row",
                flexWrap: "wrap",
                columnGap: "1rem",
                zIndex: 9000,
              }}
            >
              {sensor?.map((data, idx) => (
                <div key={data + idx}>
                  <span className="font-bold">{idx + 1}. </span>
                  {data}
                  <span className="font-normal text-sm" data-title="Delete">
                    <IconButton onClick={() => prePopData(idx)}>
                      <RemoveIcon className="text-red-700" />
                    </IconButton>
                  </span>
                </div>
              ))}
            </div>
          )} */}
          <InputLabel style={{marginBottom: "10px"}}>
            SubStep Description
          </InputLabel>
          <TextField
            onChange={handleDescChange}
            onBlur={() => setDesc(desc?.trim())}
            style={{marginBottom: "10px"}}
            variant="outlined"
            value={desc}
            fullWidth
            multiline
            rows={2}
            error={!!errorMessage2}
            helperText={errorMessage2}
          />
          {/* <InputLabel style={{ marginBottom: "10px" }}>
            SubStep Description
          </InputLabel>
          {/* <TextField
              onChange={(e) => setDesc(e.target.value)}
              // onBlur={() => setDesc(desc?.trim())}
              // value={desc}
              style={{ marginBottom: "10px" }}
              variant="outlined"
              fullWidth
              multiline
              rows={4}
              required
            /> */}
          {/* <TextField
            onChange={(e) => setDesc(e.target.value)}
            style={{ marginBottom: "10px" }}
            variant="outlined"
            value={desc}
            fullWidth
            multiline
            rows={4}
            required
          /> */}

          <div
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
            }}>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                minWidth: "48%",
              }}>
              <InputLabel style={{marginBottom: "10px"}}>Format</InputLabel>
              <FormControl
                style={{marginBottom: "10px"}}
                required
                fullWidth
                variant="outlined">
                <Select
                  value={format}
                  onChange={e => {
                    setFormat(e.target.value);
                    setIsMediaPreviewOpen({
                      type:
                        e.target.value !== "text"
                          ? !file
                            ? step?.format
                            : file?.type?.split("/")[0]
                          : e.target.value,
                    });
                  }}
                  required>
                  <MenuItem value="image" sx={menuItemTheme}>
                    Image
                  </MenuItem>
                  <MenuItem value="video" sx={menuItemTheme}>
                    Video
                  </MenuItem>
                  <MenuItem value="audio" sx={menuItemTheme}>
                    Audio
                  </MenuItem>
                  <MenuItem value="text" sx={menuItemTheme}>
                    Text
                  </MenuItem>
                </Select>
              </FormControl>
            </div>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                minWidth: "48%",
              }}>
              <InputLabel style={{marginBottom: "10px"}}>Type</InputLabel>
              <FormControl
                style={{marginBottom: "10px"}}
                required
                fullWidth
                variant="outlined">
                <Select
                  value={type}
                  onChange={e => setType(e.target.value)}
                  required>
                  <MenuItem value="info" sx={menuItemTheme}>
                    Info
                  </MenuItem>
                  <MenuItem value="camera" sx={menuItemTheme}>
                    Camera
                  </MenuItem>
                  <MenuItem value="critical" sx={menuItemTheme}>
                    Critical
                  </MenuItem>
                  <MenuItem value="normal" sx={menuItemTheme}>
                    Normal
                  </MenuItem>
                </Select>
              </FormControl>
            </div>
          </div>

          {format && (format !== "text" || format === "") ? (
            <div style={{justifyContent: "center", padding: "1rem"}}>
              <InputLabel style={{marginBottom: "10px"}}>Media</InputLabel>
              <DropzoneArea
                showFileNames
                onChange={loadedFiles => handleChange(loadedFiles)}
                dropzoneText="Drag and Drop / Click to ADD Media"
                onDelete={handleMediaDelete}
                showAlerts={false}
                filesLimit={1}
                maxFileSize={50 * 1024 * 1024} // bytes :"default: 3000000"
              />
              {/* <div className="text-2xl text-gray-700 flex justify-end">
            <p> {progress} % Uploaded</p>
          </div> */}
              {imageUrl.length > 0 ? (
                <div
                  className="mt-4 mb-2 flex justify-center items-center"
                  style={{
                    height: previewContainerHeight,
                    maxHeight: previewContainerHeight,
                  }}>
                  {/*getPreview(imageUrl.length > 0 ? imageUrl : `${dbConfig?.url_storage}/${step?.url}`)*/}
                  <GetPreviewComponent
                    sourceUrl={
                      imageUrl.length > 0
                        ? imageUrl
                        : `${dbConfig?.url_storage}/${step?.url}`
                    }
                    fileFormat={format}
                    previewImageStyle={{width: "400px"}}
                    previewVideoStyle={{
                      width: "720px",
                      justifyContent: "center",
                      alignSelf: "center",
                    }}
                    previewAudioStyle={{justifyContent: "center"}}
                  />
                </div>
              ) : (
                <div
                  className="mt-6 mb-4 flex justify-center items-center"
                  style={{
                    height: previewContainerHeight,
                    maxHeight: previewContainerHeight,
                  }}>
                  {/*getPreview(`${dbConfig?.url_storage}/${step?.url}`)*/}
                  <GetPreviewComponent
                    sourceUrl={`${dbConfig?.url_storage}/${step?.url}`}
                    fileFormat={step?.format}
                    previewImageStyle={{width: "400px"}}
                    previewVideoStyle={{
                      width: "720px",
                      justifyContent: "center",
                      alignSelf: "center",
                    }}
                    previewAudioStyle={{justifyContent: "center"}}
                  />
                </div>
              )}
            </div>
          ) : (
            ""
          )}

          <div className="flex justify-center">
            <div className=" w-full" style={{padding: "1rem 5rem"}}>
              <div className="flex justify-between">
                <ButtonBasicCancel
                  buttonTitle="CANCEL"
                  width="30%"
                  variant="contained"
                  onClick={() => handleCancel()}
                />
                {title === "" || desc === "" ? (
                  <ButtonBasic
                    buttonTitle="Update SUB Step"
                    width="30%"
                    type="submit"
                    color="primary"
                    variant="contained"
                    disabled
                  />
                ) : (
                  <ButtonBasic
                    buttonTitle="Update SUB Step"
                    width="30%"
                    type="submit"
                    color="primary"
                    variant="contained"
                  />
                )}
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditSubStep;
