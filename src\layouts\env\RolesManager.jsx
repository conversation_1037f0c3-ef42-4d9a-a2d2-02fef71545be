import React, { useState } from "react";
import {
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import SaveIcon from "@mui/icons-material/Save";
import CancelIcon from "@mui/icons-material/Cancel";
import AddIcon from "@mui/icons-material/Add";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";

const RolesManager = ({ roles, setRoles }) => {
  const [editIdx, setEditIdx] = useState(null);
  const [editRole, setEditRole] = useState({ name: "", id: "" });
  const [addOpen, setAddOpen] = useState(false);
  const [newRole, setNewRole] = useState({ name: "", id: "" });
  const [filterName, setFilterName] = useState("");
  const [filterId, setFilterId] = useState("");
  const [sortBy, setSortBy] = useState("id");
  const [sortDir, setSortDir] = useState("asc");

  // Filtering
  let filteredRoles = (roles || []).filter(
    (role) =>
      role.name.toLowerCase().includes(filterName.toLowerCase()) &&
      String(role.id).includes(filterId),
  );

  // Sorting
  filteredRoles.sort((a, b) => {
    let vA = a[sortBy];
    let vB = b[sortBy];
    if (sortBy === "id") {
      vA = Number(vA);
      vB = Number(vB);
    }
    if (vA < vB) return sortDir === "asc" ? -1 : 1;
    if (vA > vB) return sortDir === "asc" ? 1 : -1;
    return 0;
  });

  const handleSort = (col) => {
    if (sortBy === col) {
      setSortDir(sortDir === "asc" ? "desc" : "asc");
    } else {
      setSortBy(col);
      setSortDir("asc");
    }
  };

  const handleAddRole = () => {
    if (!newRole.name || !newRole.id) return;
    // Prevent duplicate IDs
    if ((roles || []).some((r) => String(r.id) === String(newRole.id))) return;
    setRoles([...(roles || []), { ...newRole, id: String(newRole.id) }]);
    setNewRole({ name: "", id: "" });
    setAddOpen(false);
  };

  const handleEdit = (idx) => {
    setEditIdx(idx);
    setEditRole(filteredRoles[idx]);
  };

  const handleSave = (idx) => {
    const globalIdx = roles.findIndex(
      (r) =>
        r.id === filteredRoles[idx].id && r.name === filteredRoles[idx].name,
    );
    const updated = roles.map((role, i) =>
      i === globalIdx ? { ...editRole, id: String(editRole.id) } : role,
    );
    setRoles(updated);
    setEditIdx(null);
    setEditRole({ name: "", id: "" });
  };

  const handleDelete = (idx) => {
    const globalIdx = roles.findIndex(
      (r) =>
        r.id === filteredRoles[idx].id && r.name === filteredRoles[idx].name,
    );
    setRoles(roles.filter((_, i) => i !== globalIdx));
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        Manage Roles
      </Typography>
      <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
        <Grid item xs={5}>
          <TextField
            label="Filter by Name"
            value={filterName}
            onChange={(e) => setFilterName(e.target.value)}
            fullWidth
            size="small"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <span role="img" aria-label="search">
                    🔍
                  </span>
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={5}>
          <TextField
            label="Filter by ID"
            value={filterId}
            onChange={(e) => setFilterId(e.target.value.replace(/\D/g, ""))}
            fullWidth
            size="small"
            type="number"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <span role="img" aria-label="search">
                    🔍
                  </span>
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={() => setAddOpen(true)}
            fullWidth
            startIcon={<AddIcon />}
          >
            Add
          </Button>
        </Grid>
      </Grid>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell
                onClick={() => handleSort("name")}
                style={{ cursor: "pointer" }}
              >
                Role Name{" "}
                {sortBy === "name" &&
                  (sortDir === "asc" ? (
                    <ArrowUpwardIcon fontSize="small" />
                  ) : (
                    <ArrowDownwardIcon fontSize="small" />
                  ))}
              </TableCell>
              <TableCell
                onClick={() => handleSort("id")}
                style={{ cursor: "pointer" }}
              >
                Role ID{" "}
                {sortBy === "id" &&
                  (sortDir === "asc" ? (
                    <ArrowUpwardIcon fontSize="small" />
                  ) : (
                    <ArrowDownwardIcon fontSize="small" />
                  ))}
              </TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredRoles.map((role, idx) =>
              editIdx === idx ? (
                <TableRow key={role.id}>
                  <TableCell>
                    <TextField
                      value={editRole.name}
                      onChange={(e) =>
                        setEditRole({ ...editRole, name: e.target.value })
                      }
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      value={editRole.id}
                      onChange={(e) =>
                        setEditRole({
                          ...editRole,
                          id: e.target.value.replace(/\D/g, ""),
                        })
                      }
                      size="small"
                      type="number"
                    />
                  </TableCell>
                  <TableCell align="right">
                    <IconButton onClick={() => handleSave(idx)} color="primary">
                      <SaveIcon />
                    </IconButton>
                    <IconButton
                      onClick={() => setEditIdx(null)}
                      color="secondary"
                    >
                      <CancelIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ) : (
                <TableRow key={role.id}>
                  <TableCell>{role.name}</TableCell>
                  <TableCell>{role.id}</TableCell>
                  <TableCell align="right">
                    <IconButton onClick={() => handleEdit(idx)} color="primary">
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDelete(idx)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add Role Modal */}
      <Dialog open={addOpen} onClose={() => setAddOpen(false)}>
        <DialogTitle>Add Role</DialogTitle>
        <DialogContent>
          <TextField
            label="Role Name"
            value={newRole.name}
            onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
            fullWidth
            size="small"
            sx={{ mb: 2, mt: 1 }}
          />
          <TextField
            label="Role ID"
            value={newRole.id}
            onChange={(e) =>
              setNewRole({
                ...newRole,
                id: e.target.value.replace(/\D/g, ""),
              })
            }
            fullWidth
            size="small"
            type="number"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddOpen(false)} color="secondary">
            Cancel
          </Button>
          <Button
            onClick={handleAddRole}
            color="primary"
            variant="contained"
            disabled={!newRole.name || !newRole.id}
          >
            Add
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default RolesManager;
