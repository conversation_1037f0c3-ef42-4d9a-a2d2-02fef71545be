import React, { useContext, useState } from "react";
import axios from "axios";

import { dbConfig } from "../../infrastructure/db/db-config";

const CreateReportsContext = React.createContext();
const UpdateReportsContext = React.createContext();
const DeleteReportsContext = React.createContext();

export function useCreateReports() {
  return useContext(CreateReportsContext);
}

export function useUpdateReports() {
  return useContext(UpdateReportsContext);
}

export function useDeleteReports() {
  return useContext(DeleteMachineContext);
}

export function ReportsCfrContextProvider({ children }) {
  function handleReportsCreate(sessionData) {
    const ReportsCreate = async (sessionData) => {
      await axios
        .post(`${dbConfig.url}/reports`, sessionData)
        .then((response) => {
          console.log("cfr-createMachineData", sessionData);
        })
        .catch((error) => {
          console.error(error);
        });
    };

    ReportsCreate(sessionData);
  }

  function handleReportsUpdate(sessionData) {
    const ReportsUpdate = async (sessionData) => {
      await axios
        .post(`${dbConfig.url}/reports`, sessionData)
        .then((response) => {
          console.log("cfr-createMachineData", sessionData);
        })
        .catch((error) => {
          console.error(error);
        });
    };

    ReportsUpdate(sessionData);
  }

  function handleReportsDelete(sessionData) {
    const ReportsDelete = async (sessionData) => {
      await axios
        .post(`${dbConfig.url}/reports`, sessionData)
        .then((response) => {
          console.log("cfr-createMachineData", sessionData);
        })
        .catch((error) => {
          console.error(error);
        });
    };

    ReportsDelete(sessionData);
  }

  return (
    <CreateReportsContext.Provider value={handleReportsCreate}>
      <DeleteReportsContext.Provider value={handleReportsDelete}>
        <UpdateReportsContext.Provider value={handleReportsUpdate}>
          {children}
        </UpdateReportsContext.Provider>
      </DeleteReportsContext.Provider>
    </CreateReportsContext.Provider>
  );
}
