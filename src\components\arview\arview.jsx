import React, { useEffect, useState, useRef } from "react";
import { createRoot } from "react-dom/client";
import {
  Stage,
  Layer,
  Rect,
  Circle,
  Line,
  Transformer,
  Text,
  Image,
  Animation,
} from "react-konva";
import jsPDF from "jspdf";
import {
  FaSquare,
  FaCircle,
  FaDrawPolygon,
  FaMousePointer,
  FaPencilAlt,
  FaEraser,
  FaFont,
  FaCamera,
} from "react-icons/fa";
import Konva from "konva";
import PropTypes, { shape } from "prop-types";
import axios from "axios";
import { useParams } from "react-router-dom";
import {
  Button,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from "@mui/material";

import { dbConfig } from "../../infrastructure/db/db-config";
import NotAccessible from "../not-accessible/not-accessible";
import { useCheckAccess } from "../../utils/useCheckAccess";

// Shape Component
const Shape = ({ shapeProps, isSelected, onSelect, onChange }) => {
  const shapeRef = React.useRef();
  const trRef = React.useRef();

  React.useEffect(() => {
    if (isSelected) {
      trRef.current.nodes([shapeRef.current]);
      trRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);

  const handleTransformEnd = () => {
    const node = shapeRef.current;
    const scaleX = node.scaleX();
    const scaleY = node.scaleY();

    node.scaleX(1);
    node.scaleY(1);

    switch (shapeProps.type) {
      case "circle":
        onChange({
          ...shapeProps,
          x: node.x(),
          y: node.y(),
          radius: Math.max(5, shapeProps.radius * scaleX),
        });
        break;
      case "triangle":
        onChange({
          ...shapeProps,
          x: node.x(),
          y: node.y(),
          width: Math.max(5, shapeProps.width * scaleX),
          height: Math.max(5, shapeProps.height * scaleY),
        });
        break;
      case "rectangle":
      default:
        onChange({
          ...shapeProps,
          x: node.x(),
          y: node.y(),
          width: Math.max(5, shapeProps.width * scaleX),
          height: Math.max(5, shapeProps.height * scaleY),
        });
        break;
    }
  };

  const renderShape = () => {
    switch (shapeProps.type) {
      case "circle":
        return (
          <Circle
            onClick={onSelect}
            onTap={onSelect}
            ref={shapeRef}
            {...shapeProps}
            draggable
            onDragEnd={(e) => {
              onChange({
                ...shapeProps,
                x: e.target.x(),
                y: e.target.y(),
              });
            }}
            onTransformEnd={handleTransformEnd}
          />
        );
      case "triangle":
        return (
          <Line
            onClick={onSelect}
            onTap={onSelect}
            ref={shapeRef}
            points={[
              shapeProps.x,
              shapeProps.y,
              shapeProps.x + shapeProps.width,
              shapeProps.y,
              shapeProps.x + shapeProps.width / 2,
              shapeProps.y - shapeProps.height,
            ]}
            closed
            fill={shapeProps.fill}
            draggable
            onDragEnd={(e) => {
              onChange({
                ...shapeProps,
                x: e.target.x(),
                y: e.target.y(),
              });
            }}
            onTransformEnd={handleTransformEnd}
          />
        );
      case "rectangle":
      default:
        return (
          <Rect
            onClick={onSelect}
            onTap={onSelect}
            ref={shapeRef}
            {...shapeProps}
            draggable
            onDragEnd={(e) => {
              onChange({
                ...shapeProps,
                x: e.target.x(),
                y: e.target.y(),
              });
            }}
            onTransformEnd={handleTransformEnd}
          />
        );
    }
  };

  return (
    <>
      {renderShape()}
      {isSelected && (
        <Transformer
          ref={trRef}
          boundBoxFunc={(oldBox, newBox) => {
            if (Math.abs(newBox.width) < 5 || Math.abs(newBox.height) < 5) {
              return oldBox;
            }
            return newBox;
          }}
        />
      )}
    </>
  );
};

Shape.propTypes = {
  shapeProps: PropTypes.shape({
    type: PropTypes.oneOf(["circle", "triangle", "rectangle"]).isRequired,
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired,
    width: PropTypes.number,
    height: PropTypes.number,
    radius: PropTypes.number,
    fill: PropTypes.string,
  }).isRequired,
  isSelected: PropTypes.bool.isRequired,
  onSelect: PropTypes.func.isRequired,
  onChange: PropTypes.func.isRequired,
};

const Rectangle = ({ shapeProps, isSelected, onSelect, onChange }) => {
  const shapeRef = React.useRef();
  const trRef = React.useRef();

  React.useEffect(() => {
    if (isSelected) {
      trRef.current.nodes([shapeRef.current]);
      trRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);

  return (
    <>
      <Rect
        onClick={onSelect}
        onTap={onSelect}
        ref={shapeRef}
        {...shapeProps}
        draggable
        onDragEnd={(e) => {
          onChange({
            ...shapeProps,
            x: e.target.x(),
            y: e.target.y(),
          });
        }}
        onTransformEnd={(e) => {
          const node = shapeRef.current;
          const scaleX = node.scaleX();
          const scaleY = node.scaleY();

          node.scaleX(1);
          node.scaleY(1);
          onChange({
            ...shapeProps,
            x: node.x(),
            y: node.y(),
            width: Math.max(5, node.width() * scaleX),
            height: Math.max(5, node.height() * scaleY),
          });
        }}
      />
      {isSelected && (
        <Transformer
          ref={trRef}
          boundBoxFunc={(oldBox, newBox) => {
            if (Math.abs(newBox.width) < 5 || Math.abs(newBox.height) < 5) {
              return oldBox;
            }
            return newBox;
          }}
        />
      )}
    </>
  );
};

// Rectangle Component
Rectangle.propTypes = {
  shapeProps: PropTypes.shape({
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired,
    width: PropTypes.number.isRequired,
    height: PropTypes.number.isRequired,
    fill: PropTypes.string,
  }).isRequired,
  isSelected: PropTypes.bool.isRequired,
  onSelect: PropTypes.func.isRequired,
  onChange: PropTypes.func.isRequired,
};

const VideoUploadModal = ({ isOpen, onClose, onAddVideos }) => {
  const [videos, setVideos] = React.useState([]);

  const handleFileChange = (event) => {
    const files = event.target.files;
    const videoArray = Array.from(files).map((file) =>
      URL.createObjectURL(file),
    );
    setVideos((prev) => [...prev, ...videoArray]);
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    const videoArray = Array.from(files).map((file) =>
      URL.createObjectURL(file),
    );
    setVideos((prev) => [...prev, ...videoArray]);
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const handleAddVideos = () => {
    onAddVideos(videos);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          background: "white",
          padding: "20px",
          borderRadius: "8px",
          width: "400px",
          boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
          textAlign: "center",
        }}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        <h3>Upload Videos</h3>
        <input
          type="file"
          accept="video/*"
          multiple
          onChange={handleFileChange}
          style={{ marginBottom: "10px" }}
        />
        <div
          style={{
            border: "2px dashed #ccc",
            padding: "20px",
            marginBottom: "10px",
            height: "150px",
            overflowY: "auto",
          }}
        >
          {videos.map((src, index) => (
            <video
              key={index}
              src={src}
              style={{ width: "100px", height: "100px", margin: "5px" }}
              controls
            />
          ))}
        </div>
        <div style={{ textAlign: "right" }}>
          <button
            onClick={onClose}
            style={{
              marginRight: "10px",
              padding: "10px 15px",
              backgroundColor: "#ccc",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleAddVideos}
            style={{
              padding: "10px 15px",
              backgroundColor: "#4caf50",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Add Videos
          </button>
        </div>
      </div>
    </div>
  );
};

VideoUploadModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onAddVideos: PropTypes.func.isRequired,
};

// Initial State
const initialRectangles = [
  {
    x: 10,
    y: 10,
    width: 100,
    height: 100,
    fill: "red",
    id: "rect1",
  },
  {
    x: 150,
    y: 150,
    width: 100,
    height: 100,
    fill: "green",
    id: "rect2",
  },
];

// Main Component
/**
 * ARview Component
 *
 * This component provides an interactive canvas for drawing, adding shapes, images, and videos.
 * It includes a toolbar for selecting tools and a properties panel for adjusting selected shapes.
 * Users can draw lines, add text, images, and videos, and export the canvas as an image or PDF.
 *
 * @component
 *
 * @example
 * return (
 *   <ARview />
 * )
 *
 * @returns {JSX.Element} The ARview component.
 *
 * @state {Array} rectangles - State for rectangles.
 * @state {string|null} selectedId - State for selected shape ID.
 * @state {Array} shapes - State for shapes.
 * @state {Array} lines - State for drawn lines.
 * @state {boolean} isDrawing - State for drawing mode.
 * @state {Array} texts - State for text elements.
 * @state {boolean} isModalOpen - State for text editing modal.
 * @state {Object|null} editingText - State for the text being edited.
 * @state {string|null} selectedImageId - State for selected image ID.
 * @state {string} currentTool - State for the current tool.
 * @state {Array} images - State for images.
 * @state {boolean} isImageModalOpen - State for image upload modal.
 * @state {Array} videos - State for videos.
 * @state {boolean} isVideoModalOpen - State for video upload modal.
 *
 * @function handleAddVideosToCanvas - Adds videos to the canvas.
 * @function handleAddImagesToCanvas - Adds images to the canvas.
 * @function handleTransformEnd - Handles the end of a transform event for images.
 * @function handleMouseDown - Handles mouse down events on the canvas.
 * @function handleMouseMove - Handles mouse move events on the canvas.
 * @function handleMouseUp - Handles mouse up events on the canvas.
 * @function openTextModal - Opens the text editing modal.
 * @function closeTextModal - Closes the text editing modal.
 * @function saveText - Saves the edited text.
 * @function handleAddShape - Adds a new shape to the canvas.
 * @function handleExportAsImage - Exports the canvas as an image.
 * @function handleExportAsPDF - Exports the canvas as a PDF.
 * @function handleAddRectangle - Adds a new rectangle to the canvas.
 * @function handleDeselect - Deselects the currently selected shape.
 */
const ARview = () => {
  const [rectangles, setRectangles] = React.useState(initialRectangles);
  const [selectedId, setSelectedId] = React.useState(null);
  const [shapes, setShapes] = React.useState([]);
  const [lines, setLines] = React.useState([]); // State for drawn lines
  const [isDrawing, setIsDrawing] = React.useState(false);
  const [texts, setTexts] = React.useState([]);
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [editingText, setEditingText] = React.useState(null); // Holds the text being edited
  const [selectedImageId, setSelectedImageId] = React.useState(null); // Track selected image

  const [currentTool, setCurrentTool] = React.useState("draw"); // Default tool

  const [images, setImages] = React.useState([]); // State for images
  const [isImageModalOpen, setIsImageModalOpen] = React.useState(false);

  const { canvasId } = useParams();
  const isNew = canvasId === "new";
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [showUnsavedModal, setShowUnsavedModal] = useState(false);
  const navigateRef = useRef(null);

  const [videos, setVideos] = React.useState([]); // State for videos
  const [isVideoModalOpen, setIsVideoModalOpen] = React.useState(false);

  const hasCanvasPOSTAccess = useCheckAccess("arview", "POST");
  const hasCanvasPUTAccess = useCheckAccess("arview", "PUT");

  // Fetch canvas data for editing
  useEffect(() => {
    if (!isNew) {
      const fetchCanvas = async () => {
        try {
          const response = await axios.get(
            `${dbConfig.url}/arview/${canvasId}`,
          );
          const { title, description, jsonData } = response.data;
          setTitle(title || "");
          setDescription(description || "");
          if (jsonData) {
            setShapes(jsonData.shapes || []);
            setImages(jsonData.images || []);
            setVideos(jsonData.videos || []);
          }
        } catch (error) {
          console.error("Error fetching canvas:", error);
        }
      };
      fetchCanvas();
    }
  }, [canvasId, isNew]);

  // Warn about unsaved changes when navigating away
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (unsavedChanges) {
        e.preventDefault();
        e.returnValue = ""; // Required for modern browsers
      }
    };
    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [unsavedChanges]);

  const handleSave = async () => {
    try {
      const payload = {
        title,
        description,
        jsonData: {
          shapes,
          images,
          videos,
        },
      };
      if (isNew) {
        const response = await axios.post(`${dbConfig.url}/arview`, payload);
        window.location.href = `/arview/${response.data._id}`;
      } else {
        await axios.put(`${dbConfig.url}/arview/${canvasId}`, payload);
      }
      setUnsavedChanges(false);
    } catch (error) {
      console.error("Error saving canvas:", error);
    }
  };

  const handleNavigate = () => {
    setShowUnsavedModal(false);
    if (navigateRef.current) {
      navigateRef.current();
    }
  };

  const handleCanvasChange = (newShapes) => {
    setShapes(newShapes);
    setUnsavedChanges(true);
  };

  const handleAddVideosToCanvas = (newVideos) => {
    const yOffset = 10;
    const xOffset = 10;
    const initialY = videos.length > 0 ? videos[videos.length - 1].y + 120 : 10;

    const videoObjects = newVideos.map((src, index) => ({
      x: xOffset,
      y: initialY + index * yOffset,
      src,
      id: `video${videos.length + index + 1}`,
      width: 200,
      height: 150,
    }));
    setVideos((prev) => [...prev, ...videoObjects]);
  };

  const handleTransformEndVideo = (e, id) => {
    const node = e.target;
    const updatedVideos = videos.map((vid) =>
      vid.id === id
        ? {
            ...vid,
            x: node.x(),
            y: node.y(),
            width: node.width(),
            height: node.height(),
          }
        : vid,
    );
    setVideos(updatedVideos);
  };

  const handleAddImagesToCanvas = (newImages) => {
    const yOffset = 10;
    const xOffset = 10;
    const initialY = images.length > 0 ? images[images.length - 1].y + 120 : 10;

    const imageObjects = newImages.map((src, index) => ({
      x: xOffset,
      y: initialY + index * yOffset,
      src,
      id: `image${images.length + index + 1}`,
      width: 100,
      height: 100,
    }));
    setImages((prev) => [...prev, ...imageObjects]);
  };

  const handleTransformEnd = (e, id) => {
    const node = e.target;
    const updatedImages = images.map((img) =>
      img.id === id
        ? {
            ...img,
            x: node.x(),
            y: node.y(),
            width: node.width(),
            height: node.height(),
          }
        : img,
    );
    setImages(updatedImages);
  };

  const handleMouseDown = (e) => {
    const stage = e.target.getStage();
    const pos = stage.getPointerPosition();

    if (currentTool === "text") {
      const newText = {
        x: pos.x,
        y: pos.y,
        text: "Double-click to edit",
        fontSize: 20,
        fill: "black",
        id: `text${texts.length + 1}`,
      };
      setTexts([...texts, newText]);
      setCurrentTool("select"); // Automatically switch to select mode
    } else if (currentTool === "draw" || currentTool === "eraser") {
      setIsDrawing(true);
      setLines([
        ...lines,
        {
          points: [pos.x, pos.y],
          stroke: currentTool === "eraser" ? "white" : "black",
          strokeWidth: currentTool === "eraser" ? 20 : 2,
          globalCompositeOperation:
            currentTool === "eraser" ? "destination-out" : "source-over",
          tension: 0.5,
          lineCap: "round",
        },
      ]);
    }
  };

  const handleMouseMove = (e) => {
    if (!isDrawing) return;

    const stage = e.target.getStage();
    const pos = stage.getPointerPosition();

    const updatedLines = [...lines];
    const lastLine = updatedLines[updatedLines.length - 1];

    // Add points to the last line
    lastLine.points = lastLine.points.concat([pos.x, pos.y]);
    updatedLines[updatedLines.length - 1] = lastLine;

    setLines(updatedLines);
  };

  const handleMouseUp = () => {
    setIsDrawing(false);
  };

  const openTextModal = (textItem) => {
    setEditingText(textItem);
    setIsModalOpen(true);
  };

  const closeTextModal = () => {
    setIsModalOpen(false);
    setEditingText(null);
  };

  const saveText = (newTextValue) => {
    const updatedTexts = texts.map((textItem) =>
      textItem.id === editingText.id
        ? { ...textItem, text: newTextValue }
        : textItem,
    );
    setTexts(updatedTexts);
    closeTextModal();
  };

  // Add Shape
  const handleAddShape = (type) => {
    const newShape = {
      type,
      x: 50,
      y: 50,
      fill: type === "triangle" ? "yellow" : type === "circle" ? "blue" : "red",
      id: `shape${shapes.length + 1}`,
      ...(type === "circle" ? { radius: 50 } : { width: 100, height: 100 }),
    };
    setShapes([...shapes, newShape]);
  };

  // Export Functions
  const handleExportAsImage = () => {
    const stage = document.getElementsByTagName("canvas")[0];
    const dataURL = stage.toDataURL();
    const link = document.createElement("a");
    link.href = dataURL;
    link.download = "canvas.png";
    link.click();
  };

  const handleExportAsPDF = () => {
    const pdf = new jsPDF();
    const stage = document.getElementsByTagName("canvas")[0];
    const imgData = stage.toDataURL("image/png");
    pdf.addImage(imgData, "PNG", 10, 10, 190, 0);
    pdf.save("canvas.pdf");
  };

  // Add Rectangle
  const handleAddRectangle = () => {
    const newRect = {
      x: 50,
      y: 50,
      width: 100,
      height: 100,
      fill: "blue",
      id: `rect${rectangles.length + 1}`,
    };
    setRectangles([...rectangles, newRect]);
  };

  // Deselect Logic
  const handleDeselect = (e) => {
    if (e.target === e.target.getStage()) {
      setSelectedId(null);
    }
  };

  return (isNew ? hasCanvasPOSTAccess : hasCanvasPUTAccess) ? (
    <>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          padding: "10px",
        }}
      >
        <div style={{ display: "flex", gap: "10px" }}>
          <TextField
            label="Title"
            value={title}
            onChange={(e) => {
              setTitle(e.target.value);
              setUnsavedChanges(true);
            }}
          />
          <TextField
            label="Description"
            value={description}
            onChange={(e) => {
              setDescription(e.target.value);
              setUnsavedChanges(true);
            }}
          />
        </div>
        <Button variant="contained" color="primary" onClick={handleSave}>
          {isNew ? "Create" : "Save"}
        </Button>
      </div>
      <div style={{ display: "flex", height: "100vh" }}>
        {/* Left Toolbar */}
        <div style={{ width: "20%", padding: "10px", background: "#f4f4f4" }}>
          <h3>Tools</h3>
          <button
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "10px",
              fontSize: "16px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => setCurrentTool("select")}
          >
            <FaMousePointer style={{ marginRight: "10px" }} /> Select
          </button>

          <button
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "10px",
              fontSize: "16px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => setCurrentTool("text")}
          >
            <FaFont style={{ marginRight: "10px" }} /> Add Text
          </button>

          <button
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "10px",
              fontSize: "16px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => {
              setCurrentTool("image");
              setIsImageModalOpen(true);
            }}
          >
            <FaCamera style={{ marginRight: "10px" }} />
            Add Images
          </button>

          <button
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "10px",
              fontSize: "16px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => {
              setCurrentTool("video");
              setIsVideoModalOpen(true);
            }}
          >
            <FaCamera style={{ marginRight: "10px" }} />
            Add Videos
          </button>

          <button
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "10px",
              fontSize: "16px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => setCurrentTool("draw")}
          >
            <FaPencilAlt style={{ marginRight: "10px" }} /> Draw
          </button>

          <button
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "10px",
              fontSize: "16px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => handleAddShape("rectangle")}
          >
            <FaSquare style={{ marginRight: "10px" }} /> Rectangle
          </button>
          <button
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "10px",
              fontSize: "16px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => handleAddShape("circle")}
          >
            <FaCircle style={{ marginRight: "10px" }} /> Circle
          </button>
          <button
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "10px",
              fontSize: "16px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => handleAddShape("triangle")}
          >
            <FaDrawPolygon style={{ marginRight: "10px" }} /> Triangle
          </button>
          <button
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "10px",
              fontSize: "16px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => setCurrentTool("eraser")}
          >
            <FaEraser style={{ marginRight: "10px" }} /> Eraser
          </button>
        </div>

        {/* Canvas */}
        <div
          style={{ flex: 1, border: "1px solid #ddd", position: "relative" }}
        >
          <Stage
            // width={window.innerWidth * 0.6}
            // height={window.innerHeight}
            // onMouseDown={handleMouseDown}
            // onMouseMove={handleMouseMove}
            // onMouseUp={handleMouseUp}
            // onMouseLeave={() => setIsDrawing(false)}

            width={window.innerWidth * 0.6}
            height={window.innerHeight}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
          >
            <Layer>
              {/* Render Shapes */}
              {shapes.map((shape, i) => (
                <Shape
                  key={i}
                  shapeProps={shape}
                  isSelected={shape.id === selectedId}
                  onSelect={() => setSelectedId(shape.id)}
                  onChange={(newAttrs) => {
                    const updatedShapes = shapes.slice();
                    updatedShapes[i] = newAttrs;
                    setShapes(updatedShapes);
                  }}
                />
              ))}

              {/* Render Drawn Lines */}
              {lines.map((line, i) => (
                <Line
                  key={i}
                  points={line.points}
                  stroke={line.stroke}
                  strokeWidth={line.strokeWidth}
                  tension={line.tension}
                  lineCap={line.lineCap}
                  globalCompositeOperation={line.globalCompositeOperation}
                />
              ))}

              {/* Render Texts */}
              {/* Render Texts */}
              {texts.map((textItem, i) => (
                <Text
                  key={i}
                  {...textItem}
                  draggable
                  onDblClick={() => openTextModal(textItem)} // Open modal on double-click
                  onDragEnd={(e) => {
                    const updatedTexts = texts.slice();
                    updatedTexts[i] = {
                      ...textItem,
                      x: e.target.x(),
                      y: e.target.y(),
                    };
                    setTexts(updatedTexts);
                  }}
                />
              ))}

              {images.map((image, i) => (
                <Rect
                  key={i}
                  x={image.x}
                  y={image.y}
                  fillPatternImage={(() => {
                    const img = new window.Image();
                    img.src = image.src;
                    return img;
                  })()}
                  width={image.width}
                  height={image.height}
                  draggable
                  onClick={() => setSelectedImageId(image.id)} // Select image
                  onTransformEnd={(e) => handleTransformEnd(e, image.id)}
                  ref={(node) => {
                    if (node && selectedImageId === image.id) {
                      const transformer = new Transformer();
                      node.getLayer().add(transformer);
                      transformer.nodes([node]);
                      transformer.getLayer().batchDraw();
                    }
                  }}
                />
              ))}

              {/* Render Videos */}
              {videos.map((video, i) => (
                <KonvaVideo
                  key={video.id}
                  videoProps={{ ...video, isSelected: video.id === selectedId }}
                  onTransformEnd={(e) => handleTransformEndVideo(e, video.id)}
                  onClick={() => setSelectedId(video.id)}
                />
              ))}
            </Layer>
          </Stage>
        </div>

        {/* Right Properties Panel */}
        <div style={{ width: "20%", padding: "10px", background: "#f4f4f4" }}>
          <h3>Properties</h3>
          {selectedId && (
            <div>
              <label>
                X:
                <input
                  type="number"
                  value={
                    rectangles.find((rect) => rect.id === selectedId)?.x || 0
                  }
                  onChange={(e) => {
                    const rects = rectangles.map((rect) =>
                      rect.id === selectedId
                        ? { ...rect, x: parseInt(e.target.value, 10) }
                        : rect,
                    );
                    setRectangles(rects);
                  }}
                />
              </label>
              <label>
                Y:
                <input
                  type="number"
                  value={
                    rectangles.find((rect) => rect.id === selectedId)?.y || 0
                  }
                  onChange={(e) => {
                    const rects = rectangles.map((rect) =>
                      rect.id === selectedId
                        ? { ...rect, y: parseInt(e.target.value, 10) }
                        : rect,
                    );
                    setRectangles(rects);
                  }}
                />
              </label>
              <label>
                Width:
                <input
                  type="number"
                  value={
                    rectangles.find((rect) => rect.id === selectedId)?.width ||
                    0
                  }
                  onChange={(e) => {
                    const rects = rectangles.map((rect) =>
                      rect.id === selectedId
                        ? { ...rect, width: parseInt(e.target.value, 10) }
                        : rect,
                    );
                    setRectangles(rects);
                  }}
                />
              </label>
              <label>
                Height:
                <input
                  type="number"
                  value={
                    rectangles.find((rect) => rect.id === selectedId)?.height ||
                    0
                  }
                  onChange={(e) => {
                    const rects = rectangles.map((rect) =>
                      rect.id === selectedId
                        ? { ...rect, height: parseInt(e.target.value, 10) }
                        : rect,
                    );
                    setRectangles(rects);
                  }}
                />
              </label>
            </div>
          )}
        </div>

        {/* Bottom Export Bar */}
        <div
          style={{
            position: "absolute",
            bottom: 0,
            width: "100%",
            padding: "10px",
            background: "#f4f4f4",
            textAlign: "center",
          }}
        >
          <button onClick={handleExportAsImage}>Export as Image</button>
          <button onClick={handleExportAsPDF}>Export as PDF</button>
        </div>
        {/* Text Editing Modal */}
        <TextModal
          isOpen={isModalOpen}
          textValue={editingText?.text || ""}
          onClose={closeTextModal}
          onSave={saveText}
        />

        <ImageUploadModal
          isOpen={isImageModalOpen}
          onClose={() => setIsImageModalOpen(false)}
          onAddImages={handleAddImagesToCanvas}
        />
        <VideoUploadModal
          isOpen={isVideoModalOpen}
          onClose={() => setIsVideoModalOpen(false)}
          onAddVideos={handleAddVideosToCanvas}
        />

        <Dialog
          open={showUnsavedModal}
          onClose={() => setShowUnsavedModal(false)}
        >
          <DialogTitle>Unsaved Changes</DialogTitle>
          <DialogContent>
            <DialogContentText>
              You have unsaved changes. Do you want to save before navigating
              away?
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowUnsavedModal(false)}>Cancel</Button>
            <Button onClick={handleSave}>Save</Button>
            <Button onClick={handleNavigate} color="secondary">
              Leave Without Saving
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    </>
  ) : (
    <NotAccessible />
  );
};

// Modal for Uploading Images
const ImageUploadModal = ({ isOpen, onClose, onAddImages }) => {
  const [images, setImages] = React.useState([]);

  const handleFileChange = (event) => {
    const files = event.target.files;
    const imageArray = Array.from(files).map((file) =>
      URL.createObjectURL(file),
    );
    setImages((prev) => [...prev, ...imageArray]);
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    const imageArray = Array.from(files).map((file) =>
      URL.createObjectURL(file),
    );
    setImages((prev) => [...prev, ...imageArray]);
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const handleAddImages = () => {
    onAddImages(images);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          background: "white",
          padding: "20px",
          borderRadius: "8px",
          width: "400px",
          boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
          textAlign: "center",
        }}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        <h3>Upload Images</h3>
        <input
          type="file"
          accept="image/*"
          multiple
          onChange={handleFileChange}
          style={{ marginBottom: "10px" }}
        />
        <div
          style={{
            border: "2px dashed #ccc",
            padding: "20px",
            marginBottom: "10px",
            height: "150px",
            overflowY: "auto",
          }}
        >
          {images.map((src, index) => (
            <img
              key={index}
              src={src}
              alt="Uploaded preview"
              style={{ width: "100px", height: "100px", margin: "5px" }}
            />
          ))}
        </div>
        <div style={{ textAlign: "right" }}>
          <button
            onClick={onClose}
            style={{
              marginRight: "10px",
              padding: "10px 15px",
              backgroundColor: "#ccc",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleAddImages}
            style={{
              padding: "10px 15px",
              backgroundColor: "#4caf50",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Add Images
          </button>
        </div>
      </div>
    </div>
  );
};

ImageUploadModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onAddImages: PropTypes.func.isRequired,
};

const TextModal = ({ isOpen, onClose, textValue, onSave }) => {
  const [inputValue, setInputValue] = React.useState(textValue);

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          background: "white",
          padding: "20px",
          borderRadius: "8px",
          width: "300px",
          boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
        }}
      >
        <h3>Edit Text</h3>
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          style={{ width: "100%", padding: "10px", marginBottom: "10px" }}
        />
        <div style={{ textAlign: "right" }}>
          <button
            onClick={() => onClose()}
            style={{
              marginRight: "10px",
              padding: "10px 15px",
              backgroundColor: "#ccc",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Cancel
          </button>
          <button
            onClick={() => onSave(inputValue)}
            style={{
              padding: "10px 15px",
              backgroundColor: "#4caf50",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

TextModal.propTypes = {
  videoProps: PropTypes.shape({
    src: PropTypes.string.isRequired,
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired,
    width: PropTypes.number.isRequired,
    height: PropTypes.number.isRequired,
  }).isRequired,
  onTransformEnd: PropTypes.func.isRequired,
};

const KonvaVideo = ({ videoProps, onTransformEnd, onClick }) => {
  const videoRef = React.useRef(null);
  const shapeRef = React.useRef(null);
  const transformerRef = React.useRef(null);

  React.useEffect(() => {
    const videoElement = document.createElement("video");
    videoElement.src = videoProps.src;
    videoElement.loop = true;
    videoElement.muted = true;
    videoElement.play();
    videoRef.current = videoElement;

    const updateFrame = () => {
      if (shapeRef.current) {
        shapeRef.current.getLayer().batchDraw(); // Force canvas update
      }
      requestAnimationFrame(updateFrame); // Keep updating
    };
    updateFrame(); // Start the loop

    return () => {
      cancelAnimationFrame(updateFrame); // Cleanup animation frame on unmount
    };
  }, [videoProps.src]);

  React.useEffect(() => {
    if (transformerRef.current && videoProps.isSelected) {
      transformerRef.current.nodes([shapeRef.current]);
      transformerRef.current.getLayer().batchDraw();
    }
  }, [videoProps.isSelected]);

  const handleTransformEnd = () => {
    const node = shapeRef.current;
    const scaleX = node.scaleX();
    const scaleY = node.scaleY();

    // Reset scaling after transform
    node.scaleX(1);
    node.scaleY(1);

    onTransformEnd({
      ...videoProps,
      x: node.x(),
      y: node.y(),
      width: Math.max(5, node.width() * scaleX),
      height: Math.max(5, node.height() * scaleY),
    });
  };

  return (
    <>
      <Image
        ref={shapeRef}
        x={videoProps.x}
        y={videoProps.y}
        width={videoProps.width}
        height={videoProps.height}
        image={videoRef.current}
        draggable
        onClick={onClick}
        onTransformEnd={handleTransformEnd}
      />
      {videoProps.isSelected && (
        <Transformer
          ref={transformerRef}
          boundBoxFunc={(oldBox, newBox) => {
            // Prevent the video from being resized too small
            if (newBox.width < 50 || newBox.height < 50) {
              return oldBox;
            }
            return newBox;
          }}
        />
      )}
    </>
  );
};

KonvaVideo.propTypes = {
  videoProps: PropTypes.shape({
    src: PropTypes.string.isRequired,
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired,
    width: PropTypes.number.isRequired,
    height: PropTypes.number.isRequired,
    isSelected: PropTypes.bool.isRequired,
  }).isRequired,
  onTransformEnd: PropTypes.func.isRequired,
  onClick: PropTypes.func.isRequired,
};

export default ARview;
