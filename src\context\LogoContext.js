import React, { createContext, useContext, useEffect, useState } from "react";
import { dbConfig } from "../infrastructure/db/db-config";
import axios from "axios";
import defaultLogo from "../assets/images/logo.png";
import { convertBase64 } from "../hooks/useBase64";

const LogoContext = createContext();

export const useLogo = () => useContext(LogoContext);

export const LogoProvider = ({ children }) => {
  const initialLogoImageData = {
    id: "",
    image_url: "",
  };
  const [logoImageData, setLogoImageData] = useState(initialLogoImageData);

  const handleGetLogo = async () => {
    try {
      const getLatestLogo = await axios.get(`${dbConfig.url}/companyLogo`);

      if (getLatestLogo.data?.success) {
        setLogoImageData((_) => {
          const { _id, ...logoRes } = getLatestLogo.data?.data;

          return {
            ...logoRes,
            id: _id,
          };
        });
      }

      return getLatestLogo.data?.message;
    } catch (error) {
      return error?.response?.data?.data?.message ?? "Unable to get logo";
    }
  };

  const handleUpdateLogo = async (asset) => {
    try {
      const uploadResult = await axios.post(
        `${dbConfig.url}/companyLogo`,
        asset,
      );

      setLogoImageData(uploadResult.data?.data);

      return uploadResult.data?.message;
    } catch (error) {
      return error?.response?.data?.data?.message ?? "Unable to update logo";
    }
  };

  const handleUpdateLogoForPdfTemplates = async (file) => {
    try {
      if (!(file instanceof File)) {
        return "Invalid file";
      }

      const templateLogoFormData = new FormData();
      templateLogoFormData.append("companyLogoImage", file);

      const uploadLogoForPdfTemplateResult = await axios.post(
        `${dbConfig.url_storage}/upload/companyLogo`,
        templateLogoFormData,
      );

      return uploadLogoForPdfTemplateResult.data?.message;
    } catch (error) {
      return error?.response?.data?.data?.message;
    }
  };

  useEffect(() => {
    handleGetLogo();
  }, []);

  useEffect(() => {
    if (!logoImageData.image_url) {
      convertBase64(defaultLogo)
        .then((logoRes) =>
          setLogoImageData((prev) => ({ ...prev, image_url: logoRes })),
        )
        .catch((err) => err);
    }
  }, [logoImageData]);

  return (
    <LogoContext.Provider
      value={{
        logoImageData,
        handleGetLogo,
        handleUpdateLogo,
        handleUpdateLogoForPdfTemplates,
      }}
    >
      {children}
    </LogoContext.Provider>
  );
};
