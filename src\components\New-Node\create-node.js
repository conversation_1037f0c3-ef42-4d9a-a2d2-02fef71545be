import React, { useEffect, useState } from "react";
import TextSnippetIcon from "@mui/icons-material/TextSnippet";
import ImageIcon from "@mui/icons-material/Image";
import CodeIcon from "@mui/icons-material/Code";
import BorderColorIcon from "@mui/icons-material/BorderColor";
import Popup from "reactjs-popup";
import {
  getData,
  updateData,
} from "../../services2/issueModule/IssueModule.services";
import { toastMessageWarning, toastMessageSuccess } from "../../tools/toast";
import { ButtonBasic } from "../buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import DeleteIcon from "@mui/icons-material/Delete";
import { useNavigate, useBlocker, useLocation } from "react-router-dom";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";

import {
  IconButton,
  Button,
  Paper,
  TableContainer,
  Table,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  <PERSON>Field,
  <PERSON><PERSON><PERSON>,
  Typography,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { sharedCss } from "../../styles/sharedCss";
import {
  useIssueModuleChangeCount,
  useIssueModuleNodeData,
} from "../../services2/issueModule/IssueModule.context";
import CommonNodeModal from "./CommonNodeModal";
import CommonFieldsModal, { NODE_FIELD_TYPES } from "./CommonFieldsModal";
import { useParams } from "react-router";
const useNavigationBlocker = (blockRouteNav, message) => {
  const navigate = useNavigate();
  useBlocker((tx) => {
    if (blockRouteNav) {
      const confirmLeave = window.confirm(message);
      if (confirmLeave) {
        tx.retry();
      }
    } else {
      tx.retry();
    }
  });
};

const useCustomStyles = makeStyles((theme) => ({
  machineContainer: {
    padding: "1rem",

    borderRadius: "10px",
    backgroundColor: theme.palette.custom.background,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  machinesOuterContainer: {
    width: "100%",
  },
  machinesInnerContainer: {
    display: "flex",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  addButton: {
    width: "fit-content",
    whiteSpace: "nowrap",
  },
  machinePageContainer: {
    // padding: "1rem",
    // border: "1px solid gainsboro",
  },
}));

const NewNode = (props) => {
  const { blockRouteNav, handleRouteNavigation } = props;
  const history = useNavigate();
  const location = useLocation();
  const { id } = useParams();
  console.log("id", id); // id 67bd6225708d902413f03863
  const nodeData = useIssueModuleNodeData();
  const element = Array.isArray(nodeData)
    ? nodeData.find((node) => node._id === id)
    : null;
  console.log("element", element);
  element["text_field"] = element.text_field === true;
  element["text_field2"] = element.text_field2 === true;
  element["text_field3"] = element.text_field3 === true;
  element["text_area"] = element.text_area === true;
  element["text_area2"] = element.text_area2 === true;
  element["text_area3"] = element.text_area3 === true;
  element["image"] = element.image === true;
  element["image2"] = element.image2 === true;
  element["image2"] = element.image3 === true;
  element["code"] = element.code === true;
  element["code2"] = element.code2 === true;
  element["code3"] = element.code3 === true;
  const [nodes, setNodes] = useState(element);
  const text = element.text_field === true ? true : false;
  const text2 = element.text_field2 === true ? true : false;
  const text3 = element.text_field3 === true ? true : false;
  const area = element.text_area === true ? true : false;
  const area2 = element.text_area2 === true ? true : false;
  const area3 = element.text_area3 === true ? true : false;
  const image = element.image === true ? true : false;
  const image2 = element.image2 === true ? true : false;
  const image3 = element.image3 === true ? true : false;
  const code = element.code === true ? true : false;
  const code2 = element.code2 === true ? true : false;
  const code3 = element.code3 === true ? true : false;
  const [nodeTextField, setNodeTextField] = useState(text);
  const [nodeTextField2, setNodeTextField2] = useState(text2);
  const [nodeTextField3, setNodeTextField3] = useState(text3);
  const [nodeTextArea, setNodeTextArea] = useState(area);
  const [nodeTextArea2, setNodeTextArea2] = useState(area2);
  const [nodeTextArea3, setNodeTextArea3] = useState(area3);
  const [nodeImage, setNodeImage] = useState(image);
  const [nodeImage2, setNodeImage2] = useState(image2);
  const [nodeImage3, setNodeImage3] = useState(image3);
  const [nodeName, setNodeName] = useState(element?.name ? element?.name : "");
  const [nodeDesc, setNodeDesc] = useState(element?.desc ? element?.desc : "");
  const [codeNode, setCodeNode] = useState(code);
  const [codeNode2, setCodeNode2] = useState(code2);
  const [codeNode3, setCodeNode3] = useState(code3);
  const { currentColorLight, currentMode } = useStateContext();
  const { issueCount, setIssueCount } = useIssueModuleChangeCount();
  const [allowUpdateNode, setAllowUpdateNode] = useState(blockRouteNav);

  // Open edit node
  const [openEditNewNode, setOpenEditNewNode] = useState(false);

  // Open add fields
  const [openFieldModal, setOpenFieldModal] = useState(false);

  function lengthOfNode() {
    return Object.values(nodes).filter((el) => el === true).length;
  }
  const length = lengthOfNode();
  var number = 3;

  const saveNode = () => {
    if (nodeName === "") {
      toastMessageWarning({ message: "Please provide name of the node" });
      return;
    }
    if (nodeDesc === "") {
      toastMessageWarning({
        message: "Please provide description of the node",
      });
      return;
    }
    setNodes((el) => ({
      ...el,
      name: `${nodeName}`,
      desc: `${nodeDesc}`,
    }));
    setAllowUpdateNode(true);
  };

  const updateNode = async (id, data) => {
    await updateData("custom_nodes", id, data).then(() => {
      setIssueCount(issueCount + 1);
      handleRouteNavigation(false);
      toastMessageSuccess(
        {
          message: "Node Data has been updated successfully",
        },
        (error) => console.log(error),
      );
      history("/new-node");
    });
    setAllowUpdateNode(false);
  };

  const settingNodes = () => {
    setNodes((el) => ({
      ...el,
    }));
  };
  const customCss = useCustomStyles();
  const commonCss = sharedCss();

  const handleOpenEditNode = () => setOpenEditNewNode(true);
  const handleCloseEditNode = () => setOpenEditNewNode(false);

  const handleNodeNameChange = (evt) => {
    setNodeName(evt.target.value.trimStart());
    setNodes((el) => ({
      ...el,
      name: `${evt.target.value.trimStart()}`,
    }));
    setAllowUpdateNode(true);
  };

  const handleNodeDescriptionChange = (evt) => {
    setNodeDesc(evt.target.value.trimStart());
    setNodes((el) => ({
      ...el,
      desc: `${evt.target.value.trimStart()}`,
    }));
    setAllowUpdateNode(true);
  };

  const handleSubmit = () => {
    saveNode();
    handleCloseEditNode();
  };

  const handleOpenAddFieldModal = () => setOpenFieldModal(true);
  const handleCloseAddFieldModal = () => setOpenFieldModal(false);

  const handleTextField = () => {
    if (length < number) {
      handleRouteNavigation(true);
      if (nodeTextField === false) {
        setNodeTextField(true);
        setNodes((el) => ({
          ...el,
          text_field: true,
        }));
      }
      if (nodeTextField === true) {
        setNodeTextField2(true);
        setNodes((el) => ({
          ...el,
          text_field2: true,
        }));
      }
      if (nodeTextField2 === true) {
        setNodeTextField3(true);
        setNodes((el) => ({
          ...el,
          text_field3: true,
        }));
      }
    } else {
      toastMessageWarning({
        message: "Maximum 3 elements can be selected for one custom Node",
      });
      handleCloseAddFieldModal();
      return;
    }
    handleCloseAddFieldModal();
  };

  const handleTextArea = () => {
    if (length < number) {
      handleRouteNavigation(true);
      if (nodeTextArea === false) {
        setNodeTextArea(true);
        setNodes((el) => ({
          ...el,
          text_area: true,
        }));
      }
      if (nodeTextArea === true) {
        setNodeTextArea2(true);
        setNodes((el) => ({
          ...el,
          text_area2: true,
        }));
      }
      if (nodeTextArea2 === true) {
        setNodeTextArea3(true);
        setNodes((el) => ({
          ...el,
          text_area3: true,
        }));
      }
    } else {
      handleCloseAddFieldModal();
      toastMessageWarning({
        message: "Maximum 3 elements can be selected for one custom Node",
      });
      return;
    }
    handleCloseAddFieldModal();
  };

  const handleImageField = () => {
    if (length < number) {
      handleRouteNavigation(true);
      if (nodeImage === false) {
        setNodeImage(true);
        setNodes((el) => ({
          ...el,
          image: true,
        }));
      }
      if (nodeImage === true) {
        setNodeImage2(true);
        setNodes((el) => ({
          ...el,
          image2: true,
        }));
      }
      if (nodeImage2 === true) {
        setNodeImage3(true);
        setNodes((el) => ({
          ...el,
          image3: true,
        }));
      }
    } else {
      handleCloseAddFieldModal();
      toastMessageWarning({
        message: "Maximum 3 elements can be selected for one custom Node",
      });
      return;
    }
    handleCloseAddFieldModal();
  };

  const handleCodeField = () => {
    if (length < number) {
      handleRouteNavigation(true);
      if (codeNode === false) {
        setCodeNode(true);
        setNodes((el) => ({
          ...el,
          code: true,
        }));
      }
      if (codeNode === true) {
        setCodeNode2(true);
        setNodes((el) => ({
          ...el,
          code2: true,
        }));
      }
      if (codeNode2 === true) {
        setCodeNode3(true);
        setNodes((el) => ({
          ...el,
          code3: true,
        }));
      }
    } else {
      handleCloseAddFieldModal();
      toastMessageWarning({
        message: "Maximum 3 elements can be selected for one custom Node",
      });
      return;
    }
    handleCloseAddFieldModal();
  };

  const handleAddNodeField = (type = "") => {
    switch (type) {
      case NODE_FIELD_TYPES.text_field:
        return handleTextField();

      case NODE_FIELD_TYPES.text_area:
        return handleTextArea();

      case NODE_FIELD_TYPES.image:
        return handleImageField();

      case NODE_FIELD_TYPES.code:
        return handleCodeField();

      default:
        if (process.env.NODE_ENV === "development") {
          console.log(type, "type");
        }
        break;
    }
  };

  return (
    <div className={customCss.machinePageContainer}>
      <main>
        <header
          className={commonCss?.headingContainer}
          style={{ padding: "1rem" }}
        >
          <div
            className="back-button"
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Back to Issues">
              <IconButton onClick={() => history("/new-node")}>
                <ArrowBackIcon />
              </IconButton>
            </Tooltip>
            <Typography variant="h5">{element?.name}</Typography>
          </div>
          <div className="tab-container">
            <ButtonBasic
              buttonTitle={"Projects"}
              onClick={() => history("/issues")}
            />
            <ButtonBasic
              buttonTitle={"Nodes"}
              onClick={() => history("/new-node")}
            />
          </div>
        </header>
        <main
          className="create-new-node"
          style={
            currentMode === "Dark"
              ? {
                  backgroundColor: "#2f2f2f",
                  color: "white",
                }
              : { background: currentColorLight }
          }
        >
          <div className="project-details">
            {/*
            <div
              className="project-details-heading"
              style={
                currentMode === "Dark"
                  ? { backgroundColor: "#2f2f2f", color: "white" }
                  : {}
              }
            >
              <h1>{nodeName}</h1>
              <p>{nodeDesc}</p>
            </div>
            */}
            <div
              className="project-details-heading flex flex-row gap-x-4"
              style={
                currentMode === "Dark"
                  ? { backgroundColor: "#2f2f2f", color: "white" }
                  : {}
              }
            >
              <TextField
                label={"Name of the node"}
                value={nodeName}
                onChange={handleNodeNameChange}
                size={"small"}
              />
              <TextField
                label={"Description of the node"}
                value={nodeDesc}
                onChange={handleNodeDescriptionChange}
                size={"small"}
              />
            </div>
            <div className="action-container">
              {/*
              <Popup
                trigger={
                  <Button className={customCss.addButton} variant="contained">
                    Add Fields
                  </Button>
                }
                modal
                nested
              >
                {(close) => (
                  <div
                    className="modal"
                    style={
                      currentMode === "Dark"
                        ? { backgroundColor: "#212B36", color: "white" }
                        : {}
                    }
                  >
                    <div
                      className="header"
                      style={currentMode === "Dark" ? { color: "white" } : {}}
                    >
                      Select the Field Type you want in your Node
                    </div>
                    <div className="content">
                      <div className="node-wrapper-field-container">
                        <div
                          className="node-wrapper-field"
                          onClick={() => {
                            if (length < number) {
                              handleRouteNavigation(true);
                              if (nodeTextField === false) {
                                setNodeTextField(true);
                                setNodes((el) => ({
                                  ...el,
                                  text_field: true,
                                }));
                              }
                              if (nodeTextField === true) {
                                setNodeTextField2(true);
                                setNodes((el) => ({
                                  ...el,
                                  text_field2: true,
                                }));
                              }
                              if (nodeTextField2 === true) {
                                setNodeTextField3(true);
                                setNodes((el) => ({
                                  ...el,
                                  text_field3: true,
                                }));
                              }
                            } else {
                              toastMessageWarning({
                                message:
                                  "Maximum 3 elements can be selected for one custom Node",
                              });
                              close();
                              return;
                            }
                            close();
                          }}
                        >
                          <TextSnippetIcon />
                          <span>Text Field</span>
                        </div>
                        <div
                          className="node-wrapper-field"
                          onClick={() => {
                            if (length < number) {
                              handleRouteNavigation(true);
                              if (nodeTextArea === false) {
                                setNodeTextArea(true);
                                setNodes((el) => ({
                                  ...el,
                                  text_area: true,
                                }));
                              }
                              if (nodeTextArea === true) {
                                setNodeTextArea2(true);
                                setNodes((el) => ({
                                  ...el,
                                  text_area2: true,
                                }));
                              }
                              if (nodeTextArea2 === true) {
                                setNodeTextArea3(true);
                                setNodes((el) => ({
                                  ...el,
                                  text_area3: true,
                                }));
                              }
                            } else {
                              close();
                              toastMessageWarning({
                                message:
                                  "Maximum 3 elements can be selected for one custom Node",
                              });
                              return;
                            }
                            close();
                          }}
                        >
                          <BorderColorIcon />
                          <span>Text Area</span>
                        </div>
                        <div
                          className="node-wrapper-field"
                          onClick={() => {
                            if (length < number) {
                              handleRouteNavigation(true);
                              if (nodeImage === false) {
                                setNodeImage(true);
                                setNodes((el) => ({
                                  ...el,
                                  image: true,
                                }));
                              }
                              if (nodeImage === true) {
                                setNodeImage2(true);
                                setNodes((el) => ({
                                  ...el,
                                  image2: true,
                                }));
                              }
                              if (nodeImage2 === true) {
                                setNodeImage3(true);
                                setNodes((el) => ({
                                  ...el,
                                  image3: true,
                                }));
                              }
                            } else {
                              close();
                              toastMessageWarning({
                                message:
                                  "Maximum 3 elements can be selected for one custom Node",
                              });
                              return;
                            }
                            close();
                          }}
                        >
                          <ImageIcon />
                          <span>Image Field</span>
                        </div>
                        <div
                          className="node-wrapper-field"
                          onClick={() => {
                            if (length < number) {
                              handleRouteNavigation(true);
                              if (codeNode === false) {
                                setCodeNode(true);
                                setNodes((el) => ({
                                  ...el,
                                  code: true,
                                }));
                              }
                              if (codeNode === true) {
                                setCodeNode2(true);
                                setNodes((el) => ({
                                  ...el,
                                  code2: true,
                                }));
                              }
                              if (codeNode2 === true) {
                                setCodeNode3(true);
                                setNodes((el) => ({
                                  ...el,
                                  code3: true,
                                }));
                              }
                            } else {
                              close();
                              toastMessageWarning({
                                message:
                                  "Maximum 3 elements can be selected for one custom Node",
                              });
                              return;
                            }
                            close();
                          }}
                        >
                          <CodeIcon />
                          <span>Code Area</span>
                        </div>
                      </div>
                    </div>
                    <div className="actions">
                      <span></span>
                      <Button variant="contained" color="error" onClick={close}>
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </Popup>
              */}
              <Button
                className={customCss.addButton}
                variant="contained"
                onClick={handleOpenAddFieldModal}
              >
                Add Fields
              </Button>
              <CommonFieldsModal
                openDialog={openFieldModal}
                dialogTitleText={"Select the Field Type you want in your Node"}
                handleAddNodeField={handleAddNodeField}
                buttonCloseText={"Cancel"}
                handleClose={handleCloseAddFieldModal}
              />
              {/*
              <Popup
                trigger={
                  <Button className={customCss.addButton} variant="contained">
                    Edit Node
                  </Button>
                }
                modal
                nested
              >
                {(close) => (
                  <div
                    className="modal"
                    style={
                      currentMode === "Dark"
                        ? { backgroundColor: "#212B36", color: "white" }
                        : {}
                    }
                  >
                    <div
                      className="header"
                      style={currentMode === "Dark" ? { color: "white" } : {}}
                    >
                      {" "}
                      Edit Node{" "}
                    </div>
                    <div className="content">
                      <div className="input-field-container">
                        <label
                          style={
                            currentMode === "Dark" ? { color: "white" } : {}
                          }
                        >
                          Name of Node:
                        </label>
                        <TextField
                          fullWidth
                          variant="outlined"
                          type="text"
                          placeholder="Name of the Node"
                          value={nodeName}
                          onChange={(evt) => setNodeName(evt.target.value)}
                          style={
                            currentMode === "Dark" ? { color: "white" } : {}
                          }
                        />
                      </div>
                      <div className="input-field-container">
                        <label
                          style={
                            currentMode === "Dark" ? { color: "white" } : {}
                          }
                        >
                          Description of Node:
                        </label>
                        <TextField
                          fullWidth
                          variant="outlined"
                          type="text"
                          placeholder="Description of the Node"
                          value={nodeDesc}
                          onChange={(evt) => setNodeDesc(evt.target.value)}
                          style={
                            currentMode === "Dark" ? { color: "white" } : {}
                          }
                        />
                      </div>
                    </div>
                    <div className="actions">
                      <Button variant="contained" color="error" onClick={close}>
                        Cancel
                      </Button>
                      <Button
                        variant="contained"
                        color="success"
                        onClick={() => {
                          handleRouteNavigation(true);

                          saveNode();
                          close();
                        }}
                      >
                        Save
                      </Button>
                    </div>
                  </div>
                )}
              </Popup>
              */}
              {/*
              <Button 
                className={customCss.addButton} 
                variant="contained" 
                onClick={handleOpenEditNodeWithNodeData}>
                Edit Node
              </Button>
              <CommonNodeModal
                openDialog={openEditNewNode}
                dialogTitleText={'Edit Node'}
                textName={nodeName}
                textNameInputStyle={{
                  marginBottom: '4vh'
                }}
                textNameInputLabel={'Name of Node:'}
                textNameInputPlaceholder={'Name of the Node'}
                handleTextNameChange={handleNodeNameChange}
                textDescription={nodeDesc}
                textDescriptionInputStyle={{
                  marginBottom: '0.5vh'
                }}
                textDescriptionInputLabel={'Description of the Node:'}
                textDescriptionInputPlaceholder={'Description of the Node'}
                handleTextDescriptionChange={handleNodeDescriptionChange}
                buttonCloseText={'Cancel'}
                handleClose={handleCancel}
                isSubmitButtonDisabled={nodeName === '' || nodeDesc === ''}
                buttonSubmitText={'Save'}
                handleSubmit={handleSubmit}
              />
              */}
              <Button
                className={customCss.addButton}
                variant="contained"
                disabled={!blockRouteNav && !allowUpdateNode}
                onClick={() => {
                  updateNode(id, nodes);
                }}
              >
                Update Node
              </Button>
            </div>
          </div>
          <div className="create-node-content">
            <TableContainer
              component={Paper}
              className="tableContainer"
              style={{
                border:
                  currentMode === "Dark" ? "1px solid #fff" : "1px solid #000",
              }}
            >
              <Table
                className="insideTable"
                style={{
                  backgroundColor: currentMode === "Dark" ? "#161C24" : "#fff",
                }}
              >
                <TableHead
                  style={{
                    backgroundColor:
                      currentMode === "Dark" ? "#212B36" : "#eee",
                  }}
                >
                  <TableRow>
                    <TableCell
                      sx={
                        currentMode === "Dark"
                          ? {
                              color: "white",
                              borderBottom: "1px solid white",
                            }
                          : { borderBottom: "1px solid #000" }
                      }
                      align="left"
                    >
                      Field Type
                    </TableCell>
                    <TableCell
                      sx={
                        currentMode === "Dark"
                          ? {
                              color: "white",
                              borderBottom: "1px solid white",
                            }
                          : { borderBottom: "1px solid #000" }
                      }
                      align="left"
                    >
                      Action
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {nodes.text_field === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Text Field
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                            nodes.text_field = false;
                            setNodeTextField(false);
                            settingNodes(nodes);
                            handleRouteNavigation(true);
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}

                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.text_field = false;
                              setNodeTextField(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                  {nodes.text_field2 === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Second Text Field
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                            nodes.text_field2 = false;
                            setNodeTextField2(false);
                            settingNodes(nodes);
                            handleRouteNavigation(true);
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}

                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.text_field2 = false;
                              setNodeTextField2(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                  {nodes.text_field3 === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Third Text Field
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                            nodes.text_field3 = false;
                            setNodeTextField3(false);
                            settingNodes(nodes);
                            handleRouteNavigation(true);
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}

                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.text_field3 = false;
                              setNodeTextField3(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                  {nodes.text_area === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Text Area
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                            nodes.text_area = false;
                            setNodeTextArea(false);
                            settingNodes(nodes);
                            handleRouteNavigation(true);
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}

                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.text_area = false;
                              setNodeTextArea(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                  {nodes.text_area2 === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Second Text Area
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                            nodes.text_area2 = false;
                            setNodeTextArea2(false);
                            settingNodes(nodes);
                            handleRouteNavigation(true);
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}

                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.text_area2 = false;
                              setNodeTextArea2(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                  {nodes.text_area3 === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Third Text Area
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                            nodes.text_area3 = false;
                            setNodeTextArea3(false);
                            settingNodes(nodes);
                            handleRouteNavigation(true);
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}
                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.text_area3 = false;
                              setNodeTextArea3(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                  {nodes.image === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Image
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                            nodes.image = false;
                            setNodeImage(false);
                            settingNodes(nodes);
                            handleRouteNavigation(true);
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}

                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.image = false;
                              setNodeImage(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                  {nodes.image2 === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Second Image
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                            nodes.image2 = false;
                            setNodeImage2(false);
                            settingNodes(nodes);
                            handleRouteNavigation(true);
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}
                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.image2 = false;
                              setNodeImage2(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                  {nodes.image3 === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Third Image
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                            nodes.image3 = false;
                            setNodeImage3(false);
                            settingNodes(nodes);
                            handleRouteNavigation(true);
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}

                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.image3 = false;
                              setNodeImage3(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                  {nodes.code === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Code Area
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                            nodes.code = false;
                            setCodeNode(false);
                            settingNodes(nodes);
                            handleRouteNavigation(true);
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}

                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.code = false;
                              setCodeNode(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                  {nodes.code2 === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Second Code Area
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                            nodes.code2 = false;
                            setCodeNode2(false);
                            settingNodes(nodes);
                            handleRouteNavigation(true);
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}

                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.code2 = false;
                              setCodeNode2(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                  {nodes.code3 === true && (
                    <TableRow>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        Third Code Area
                      </TableCell>
                      <TableCell
                        sx={currentMode === "Dark" ? { color: "white" } : {}}
                        style={
                          currentMode === "Dark"
                            ? { borderBottom: "1px solid white" }
                            : {}
                        }
                        align="left"
                      >
                        {/* <Button
                          onClick={() => {
                           
                          }}
                          variant="text"
                          color="error"
                          className="delete-btn"
                        >
                          <span className="tab-text">Delete</span>
                        </Button> */}

                        <IconButton color="error">
                          <DeleteIcon
                            style={{ fontSize: "22px" }}
                            onClick={() => {
                              nodes.code3 = false;
                              setCodeNode3(false);
                              settingNodes(nodes);
                              handleRouteNavigation(true);
                            }}
                          />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        </main>
      </main>
      {/* useNavigationBlocker(
        blockRouteNav,
        "You have unsaved changes. Are you sure you want to leave this page without updating?"
      ); */}
    </div>
  );
};

export default NewNode;
