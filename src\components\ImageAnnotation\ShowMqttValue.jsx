import axios from "axios";
import React, { useState } from "react";
import { useEffect } from "react";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toastMessage } from "../../tools/toast";

const ShowMqttValue = ({ mqttId }) => {
  const [mqttValues, setMqttValues] = useState([]);

  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection("liveData2")
    //   .doc(mqttId)
    //   .onSnapshot((snap) => {
    //     const data = snap.data();
    //     setMqttValues(data);
    //   });
  }, [mqttId]);

  return <b>{mqttValues.value}</b>;
};

export const ShowMqttTag = ({ mqttId }) => {
  const [mqttValues, setMqttValues] = useState([]);

  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection("liveData2")
    //   .doc(mqttId)
    //   .onSnapshot((snap) => {
    //     const data = snap.data();
    //     setMqttValues(data);
    //   });
  }, [mqttId]);

  return <b>{mqttValues.tag}</b>;
};

export default ShowMqttValue;

export const ShowMqttExpected = ({ data }) => {

  

  return <b>{data.value}</b>;
};
