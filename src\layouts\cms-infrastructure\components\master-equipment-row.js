import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
} from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import EditIcon from "@mui/icons-material/Edit";
import InventoryIcon from "@mui/icons-material/Inventory";
import { useState } from "react";
import SapMasterForm from "../forms/cms-master.form";
import EditEquipmentForm from "../crud/edit-forms/edit-equip.form";
import DeleteIcon from "@mui/icons-material/Delete";
import Delete from "../../../components/Delete/Delete";
import { deleteDataFromCms } from "../crud/functions/cms-infra.functions";

const MasterEquipmentRow = ({ item }) => {
  const { currentMode } = useStateContext();
  const [open, setOpen] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);

  return (
    <TableRow
      key={item.id}
      // sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
      style={{
        cursor: "pointer",
        borderBottom:
          currentMode === "Dark" ? ".5px solid #444" : ".5px solid #ddd",
      }}
      className={"shadow-inner"}
    >
      <TableCell
        style={{ borderBottom: "none", textTransform: "capitalize" }}
        align="left"
      >
        {item?.name}
      </TableCell>

      <TableCell
        style={{ borderBottom: "none", textTransform: "capitalize" }}
        align="left"
      >
        {item?.make}
      </TableCell>
      <TableCell style={{ borderBottom: "none" }} align="left">
        {item?.si_num}
      </TableCell>
      <TableCell style={{ borderBottom: "none" }} align="left">
        {item?.certificate_no}
      </TableCell>
      <TableCell style={{ borderBottom: "none" }} align="left">
        {item?.validity}
      </TableCell>
      <TableCell style={{ borderBottom: "none" }} align="left">
        <IconButton onClick={() => setOpenEdit(true)}>
          <EditIcon style={{ fontSize: "20px", color: "#00f" }} />
        </IconButton>

        <IconButton onClick={() => setOpenDelete(true)}>
          <DeleteIcon style={{ fontSize: "20px", color: "red" }} />
        </IconButton>
      </TableCell>

      <Dialog
        onClose={() => setOpenEdit(false)}
        open={openEdit}
        fullWidth
        maxWidth="lg"
      >
        <DialogContent>
          <EditEquipmentForm item={item} />
        </DialogContent>
      </Dialog>

      <Dialog onClose={() => setOpenDelete(false)} open={openDelete}>
        <Delete
          onClose={() => setOpenDelete(false)}
          onDelete={() => deleteDataFromCms(item)}
        />
      </Dialog>
    </TableRow>
  );
};

export default MasterEquipmentRow;
