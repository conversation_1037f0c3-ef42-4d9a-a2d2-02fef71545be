import { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import "./Dashboard.scss";
import FileManagerSection from "./FileManagerSection";
import MachineSection from "./MachineSection";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";

import DialogActions from "@mui/material/DialogActions";
import { useStateContext } from "../../context/ContextProvider";
import { ButtonBasic } from "../../components/buttons/Buttons";
import Autocomplete from "@mui/material/Autocomplete";
import { IconButton } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import TextField from "@mui/material/TextField";
import AlertsBox from "../../components/AlertsBox/AlertsBox";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { makeStyles } from "@mui/styles";
import { HashtagContext } from "../../services2/hashtag/hashtag.context";
import { useAuth } from "../../hooks/AuthProvider";
import DashBoardHastag from "./dashBoardHastag";

//import LiveDataSection from './LiveDataSection/LiveDataSection';

const useStyles = makeStyles((theme) => ({
  dashboardPage: {
    width: "100%",
    gap: "1rem",
    display: "flex",
    color: theme.palette.custom.textColor,
  },
  leftContainer: {
    gap: "2rem",
    // padding: "0.5rem",
    // border: "1px solid gainsboro",
    display: "flex",
    flexDirection: "column",
    width: "75%",
  },
  rightContainer: {
    width: "25%",
  },
  hashtags: {
    position: "absolute",
    right: "5rem",
    bottom: "3rem",
  },
  hashtagModal: {
    backgroundColor: theme.palette.custom.backgroundSecondary,
  },
}));

const Dashboard = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const { setCurrentColor } = useStateContext();

  const getuserDetails = async () => {
    if (!currentUser || !currentUser._id) {
      console.error("Current user or user ID is undefined.");
      return;
    }

    const id = currentUser._id;
    await axios
      .get(`${dbConfig.url}/users/${id}`)
      .then((response) => {
        if (
          response?.data?.data?.themecolor &&
          response?.data?.data?.themecolor.includes("#")
        ) {
          setCurrentColor(response?.data?.data?.themecolor);
          localStorage.setItem("colorMode", response?.data?.data?.themecolor);
        } else {
          setCurrentColor("#bbb");
          localStorage.setItem("colorMode", "#bbb");
        }
      })
      .catch((error) => {
        console.error("Error fetching user details:", error);
      });
  };

  useEffect(() => {
    if (currentUser) {
      getuserDetails();
    }
  }, [currentUser]);

  const [open, setOpen] = useState(false);
  const { currentColor, currentMode, currentColorLight } = useStateContext();

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleTagSubmit = (e) => {
    e.preventDefault();
  };

  const classes = useStyles();
  return (
    <div className={classes.dashboardPage}>
      <div className={classes.leftContainer}>
        <MachineSection />
        <FileManagerSection />
      </div>
      <div className={classes.rightContainer}>
        <AlertsBox />
      </div>
      <div className={classes.hashtags}>
        <DashBoardHastag currentMode={currentMode} />
      </div>
      <>
        {/* <Dialog open={open} onClose={handleClose}>
        <DialogContent className={classes.hashtagModal}>
          <DialogTitle>Quick search</DialogTitle>
          <DialogContent sx={{ height: "100px", paddingBottom: "0px" }}>
            <form
              onSubmit={handleTagSubmit}
              className="flex  items-center h-full"
            >
              <Autocomplete
                size="small"
                inputValue={inputFindBy}
                value={inputFindBy}
                onInputChange={(event, newInputValue) =>
                  handleTagOnChange(newInputValue)
                }
                id="controllable-states-demo"
                options={inputFindBy!==""?hashArray:[]}
                sx={{ width: 396 }}
                renderInput={(params) => (
                  <TextField {...params} label="Search" />
                )}
              />
              <IconButton
                type="submit"
                className="searchIcon"
                style={
                  currentMode === "Dark"
                    ? {
                        color: "white",
                        marginLeft: "8px",
                      }
                    : { marginLeft: "8px" }
                }
              >
                <SearchIcon />
              </IconButton>
            </form>
          </DialogContent>
  
          <DialogActions sx={{ padding: "24px 16px" }}>
            <Button onClick={handleClose} variant="contained" color="error">
              Cancel
            </Button>
          </DialogActions>
        </DialogContent>
      </Dialog> */}
      </>
    </div>
  );
};

export default Dashboard;
