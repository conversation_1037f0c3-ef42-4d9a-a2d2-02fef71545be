import React from "react";
import { Lock } from "@mui/icons-material";
import { Card, CardContent, Skeleton, Stack } from "@mui/material";
import { useUtils } from "../../hooks/UtilsProvider";

export default function NotAccessible({ style }) {
  const { isFetching } = useUtils();

  if (isFetching) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          ...style,
        }}
      >
        <Stack spacing={2} width={300}>
          <Skeleton variant="rectangular" height={200} />
          <Skeleton variant="text" width="60%" />
          <Skeleton variant="text" width="80%" />
        </Stack>
      </div>
    );
  }

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        ...style,
      }}
    >
      <Card className="max-w-lg p-6 text-center shadow-md rounded-2xl">
        <CardContent>
          <Lock className="mx-auto text-red-500 mb-4" fontSize="large" />
          <h2 className="text-2xl font-semibold mb-2">Access Denied</h2>
          <p className="text-gray-600 mb-4">
            You do not have permission to access this component.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
