// LiveData >> Item >> SubItem
import React, { useEffect, useState } from "react";
import {
  companies,
  companyId_constant,
  liveData,
  maintenance,
} from "../../constants/data";
import { db } from "../../firebase";
import { toastMessageWarning } from "../../tools/toast";
import AnoItem from "./anoItem";
import {
  CircularProgress,
  IconButton,
  InputLabel,
  Tooltip,
  Button,
  MenuItem,
  Select,
  Tab,
} from "@mui/material";

export default function SubItem({
  dataItem,
  calibrationValues,
  processValues,
  annotationData,
  data,
  onClose,
}) {
  const [openCalibration, setOpenCalibration] = useState(false);
  const [mqttId, setMqttId] = useState("");
  const [calib_id, setCalibId] = useState("--");
  const [annotationDataTemp, setAnnotationDataTemp] = useState([
    ...annotationData,
  ]);
  const [isOpen, setIsOpen] = useState(false);

  //Updating process values to annotated image data

  const handleMqtt = (arrId) => {
    let tempArray = new Array();

    tempArray.push(...annotationDataTemp);
    //console.log(tempArray);
    for (let index = 0; index < tempArray.length; index++) {
      if (tempArray[index].id === arrId) {
        tempArray[index].mqttId = mqttId ? mqttId : dataItem.mqttId;
        tempArray[index].calib_id = calib_id ? calib_id : dataItem.calib_id;
        break;
      }
    }
    //console.log(tempArray)
    setAnnotationDataTemp(tempArray);
  };

  return (
    <>
      <div
        key={`Item-${dataItem.id}`}
        style={{
          padding: "1.2rem",
          border: "2px solid gray",
          marginBottom: "20px",
        }}
      >
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <div className="font-bold text-xl">{dataItem.comment}</div>
          <div>
            <Select
              onChange={(e) => {
                setMqttId(e.target.value);
                //setAnotationDataIndex(index);
              }}
              value={mqttId ? mqttId : dataItem.mqttId}
            >
              {processValues?.map((data) => (
                <MenuItem key={data.id} value={data.id}>
                  {data.id}
                </MenuItem>
              ))}
            </Select>
          </div>
        </div>

        {/* <>
                    {
                        openCalibration && (

                            <>
                                <InputLabel>Select Calibration </InputLabel>
                                <Select value={ calib_id ? calib_id : dataItem.calib_id}
                                    onChange={(e) => setCalibId(e.target.value)}
                                    fullWidth
                                    variant='outlined'
                                >
                                    {calibrationValues.map(data => (
                                        <MenuItem value={data.id} key={data.id}>{data.title}</MenuItem>
                                    ))}
                                </Select>
                                <div style={{ display: 'flex', justifyContent: 'flex-end' }}>


                                </div>
                                {dataItem.calib_id && <Manuals manual_id={dataItem.calib_id} />}
                            </>
                        )
                    }
                </> */}
        <AnoItem
          annotationDataTemp={annotationDataTemp}
          docData={data}
          key={`AnoItem-${data.id}`}
          data={dataItem}
          handleMqtt={handleMqtt}
          processValues={processValues}
          onClose={onClose}
        />
      </div>
    </>
  );
}
