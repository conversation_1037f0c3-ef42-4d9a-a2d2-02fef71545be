import React, { useState, useEffect } from "react";
import "./Serialized.scss";
import { IconButton, Dialog, DialogContent, DialogTitle } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import DocDetails from "./DocDetails";
import { db } from "../../../firebase";
import { companies, companyId_constant } from "../../../constants/data";
import { firebaseLooper } from "../../../tools/tool";
import { toastMessage } from "../../../tools/toast";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { useAuth } from "../../../hooks/AuthProvider";
import DeleteConfirmModal from "../../../components/DeleteConfirmModal/DeleteConfirmModal";
import EditSeries from "./EditSeries";
import { useStateContext } from "../../../context/ContextProvider";
import { themeColors } from "../../../infrastructure/theme";
import { useMaintenanceInfo } from "../../../context/MaintenanceContext";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import TableCell from "@mui/material/TableCell";
import TableRow from "@mui/material/TableRow";
import {
  useDeleteMachineCfr,
  useEditMachineCfr,
} from "../../../hooks/cfr/machineCfrProvider";
import { useAuditSetter } from "../../../services3/audits/AuditContext";
import {
  useContentGetter,
  useContentSetter,
} from "../../../services3/audits/ContentContext";

export default function SerializedList({
  data,
  idx,
  mid,
  type,
  machineName,
  id,
  lastItem = false,
}) {
  const [expand, setExpand] = useState(false);
  const [details, setDetails] = useState();
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const { currentUser } = useAuth();
  const [user, setUser] = useState([]);
  const { currentMode } = useStateContext();
  const maintenanceInfoFromContext = useMaintenanceInfo();
  const deleteauditseriescfr = useDeleteMachineCfr();
  const deletesatseriescfr = useDeleteMachineCfr();
  const handleAudit = useAuditSetter();
  // const handleContent= useContentSetter();
  const detailsContext = useContentGetter();
  //detailsContext to render any changes in the fat context this is a temp fix
  let date = new Date();
  const data2 = {
    activity: "audit series deleted",
    dateTime: date,

    description: "an audit series is deleted",
    machine: mid,
    module: "AUDIT",
    username: currentUser.username,
  };
  const data3 = {
    activity: "sat series deleted",
    dateTime: date,

    description: "a sat series is deleted",
    machine: mid,
    module: "SAT",
    username: currentUser.username,
  };
  function deleteData() {
    axios
      .delete(
        `${dbConfig.url}/${
          type === "AUDIT" ? "FAT"?.toLowerCase() : type?.toLowerCase()
        }lists/${data?._id}`,
      )
      .then(() => {
        if (type == "SAT") {
          deletesatseriescfr(data3);
        } else {
          deleteauditseriescfr(data2);
        }
        handleAudit(type);
        toastMessage({ message: "Deleted data successfully !" });
      });
  }

  useEffect(() => {
    if (maintenanceInfoFromContext && "fat_id" in maintenanceInfoFromContext) {
      if (maintenanceInfoFromContext?.fat_id == data._id) {
        setExpand(true);
      } else {
        setExpand(false);
      }
    } else if (
      maintenanceInfoFromContext &&
      "sat_id" in maintenanceInfoFromContext
    ) {
      if (maintenanceInfoFromContext?.sat_id == data._id) {
        setExpand(true);
      } else {
        setExpand(false);
      }
    }

    axios
      .get(`${dbConfig.url}/fatdatas`)
      .then((res) => {
        console.log("fatdatas:", res.data);
        var data = res?.data;
        data.sort(function (a, b) {
          return a.index - b.index;
        });
        let fatDataFiltered = data?.filter((fData) => fData?.fid === id); // remove this filter and get it by new API
        setDetails(fatDataFiltered);
      })
      .catch((e) => {
        console.log("error fatData:", e);
      });
    // handleContent(id);
  }, [detailsContext]); //detailsContext to render any changes in the fat context this is a temp fix

  return (
    <React.Fragment key={idx}>
      {/* this code is for old UI */}
      {/* <div className="serialized_tableRow">
                <div className="serialized_tableCell" style={{ width: "105px" }}>
                    {data?.index}
                </div>
                <div className="serialized_tableCell" >
                    {data?.protocol_no}
                </div>
                <div className="serialized_tableCell" >
                    {data?.description}
                </div>
                <div className="serialized_tableCell" style={{ textTransform: 'capitalize' }}>
                    {data?.status}
                </div>
                <div className="serialized_tableCell" >
                    <IconButton onClick={() => setOpenEdit(true)}>
                        <EditIcon style={{ fontSize: "20px", color: "#00f" }} />
                    </IconButton>
                    <IconButton onClick={() => setOpenDel(true)}>
                        <DeleteIcon style={{ fontSize: "20px", color: "#f00" }} />
                    </IconButton>
                    <IconButton onClick={() => setExpand(!expand)}
                        style={currentMode === 'Dark' ? { color: 'white' } : {}}
                    >
                        {expand ?
                            <ExpandLessIcon style={{ fontSize: "20px" }} />
                            : <ExpandMoreIcon style={{ fontSize: "20px" }} />}
                    </IconButton>
                </div>
            </div>
            {
                expand ? (
                    <div className="serialized_tableRow" style={{ margin: "24px", width: "95%" }}>
                        <DocDetails
                            type={type}
                            machineName={machineName}
                            details={details}
                            id={id}
                            user={user}
                        />
                    </div>
                ) : ""
            } */}
      <TableRow
        hover
        sx={{
          "&:last-child td, &:last-child th": { border: 0 },
          borderBottom: lastItem ? "none" : "0.05rem solid #aaa",
          "&:hover": { bgcolor: "#f5f5f5" },
        }}
        style={{ cursor: "pointer" }}
      >
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="left"
        >
          {data?.index}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="left"
        >
          {data?.protocol_no}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="left"
        >
          {data?.description}
        </TableCell>
        <TableCell
          style={
            currentMode === "Dark"
              ? {
                  color: "white",
                  borderBottom: "none",
                  textTransform: "uppercase",
                }
              : {
                  color: "black",
                  borderBottom: "none",
                  textTransform: "uppercase",
                }
          }
          align="center"
        >
          {data?.status}
        </TableCell>

        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="center"
        >
          <div className="dataBtns">
            <IconButton onClick={() => setOpenEdit(true)}>
              <EditIcon color="primary" style={{ fontSize: "20px" }} />
            </IconButton>
            <IconButton onClick={() => setOpenDel(true)}>
              <DeleteIcon color="error" style={{ fontSize: "20px" }} />
            </IconButton>
            <IconButton
              onClick={() => setExpand(!expand)}
              style={currentMode === "Dark" ? { color: "white" } : {}}
            >
              {expand ? (
                <ExpandLessIcon style={{ fontSize: "20px" }} />
              ) : (
                <ExpandMoreIcon style={{ fontSize: "20px" }} />
              )}
            </IconButton>
          </div>
        </TableCell>
      </TableRow>

      {expand ? (
        <TableRow
        //   className="serialized_tableRow"
        //   style={{ margin: "24px", width: "95%" }}
        >
          <TableCell colSpan={5}>
            <DocDetails
              type={type}
              machineName={machineName}
              details={details}
              id={id}
              user={user}
            />
          </TableCell>
        </TableRow>
      ) : (
        ""
      )}
      <DeleteConfirmModal
        handleClose={() => setOpenDel(false)}
        onDelete={deleteData}
        type={type}
        name={data?.protocol_no}
        open={openDel}
      />
      <Dialog open={openEdit} fullWidth>
        <DialogTitle
          style={
            currentMode === "Dark"
              ? { backgroundColor: themeColors.dark.secordary }
              : { backgroundColor: "white" }
          }
        >
          Edit Documentation - [{data?.protocol_no}]{" "}
        </DialogTitle>
        <DialogContent
          style={
            currentMode === "Dark"
              ? { backgroundColor: themeColors.dark.secordary }
              : { backgroundColor: "white" }
          }
        >
          <EditSeries
            handleClose={() => setOpenEdit(false)}
            type={type}
            machineName={machineName}
            user={user}
            data={data}
          />
        </DialogContent>
      </Dialog>
    </React.Fragment>
  );
}
