import React, {
  useEffect,
  useContext,
  useState,
  createContext,
  useRef,
  useCallback,
} from "react";
import Modal from "@mui/material/Modal";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import PropTypes from "prop-types";

const MODAL_TITLE = "Session Expiration Warning";
const MODAL_MESSAGE = "You will be logged out in";
const BUTTON_TEXT = "Stay Logged In";
const USER_TOKEN_KEY = "@user-token";
const LOGOUT_NOW_MESSAGE = "Logging out now...";
const USER_NOT_LOGGED_IN_MESSAGE = "User is not logged in, skipping timers.";
const STARTING_TIMERS_MESSAGE = "Starting timers...";
const SESSION_TIMEOUT_MESSAGE = "Session timeout reached.";
const SHOWING_WARNING_MODAL_MESSAGE = "Showing warning modal...";
const COUNTDOWN_ENDED_MESSAGE = "Countdown ended. Logging out.";
const CLEARING_TIMERS_MESSAGE = "Clearing all timers...";
const USER_ACTIVITY_MESSAGE = "User activity detected, resetting timers...";
const CLEANING_UP_MESSAGE = "Cleaning up timers and event listeners...";
const CLOSING_WARNING_MODAL_MESSAGE = "Closing warning modal...";
const EVENTS = ["mousemove", "mousedown", "keypress", "touchstart", "scroll"];

const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours} hour${hours > 1 ? "s" : ""}, ${minutes} minute${minutes > 1 ? "s" : ""}, and ${remainingSeconds} second${remainingSeconds > 1 ? "s" : ""}`;
  } else if (minutes > 0) {
    return `${minutes} minute${minutes > 1 ? "s" : ""} and ${remainingSeconds} second${remainingSeconds > 1 ? "s" : ""}`;
  } else {
    return `${remainingSeconds} second${remainingSeconds > 1 ? "s" : ""}`;
  }
};

const AutoLogoutContext = createContext();

const modalStyle = {
  position: "fixed",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 300,
  backgroundColor: "white",
  boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.2)",
  padding: 20,
  textAlign: "center",
  zIndex: 1300,
};

export const AutoLogoutProvider = ({
  children,
  logoutCallback,
  timeout = 3600 * 1000,
}) => {
  const [showWarning, setShowWarning] = useState(false);
  const [warningTimeLeft, setWarningTimeLeft] = useState(0);
  const isLoggedIn = Boolean(sessionStorage.getItem(USER_TOKEN_KEY)); // Check login status

  const timeoutId = useRef(null);
  const warningTimeoutId = useRef(null);
  const countdownIntervalId = useRef(null);

  // Logout function
  const handleLogout = useCallback(() => {
    console.log(LOGOUT_NOW_MESSAGE);
    logoutCallback();
  }, [logoutCallback]);

  // Start the timers
  const startTimers = useCallback(() => {
    if (!isLoggedIn) {
      console.log(USER_NOT_LOGGED_IN_MESSAGE);
      return;
    }

    console.log(STARTING_TIMERS_MESSAGE);
    clearAllTimers();

    // Set the main auto-logout timer
    timeoutId.current = setTimeout(() => {
      console.log(SESSION_TIMEOUT_MESSAGE);
      handleLogout();
    }, timeout);

    // Set the warning modal timer
    warningTimeoutId.current = setTimeout(
      () => {
        console.log(SHOWING_WARNING_MODAL_MESSAGE);
        setShowWarning(true);
        setWarningTimeLeft(10); // Start countdown from 10 seconds

        // Start countdown interval
        countdownIntervalId.current = setInterval(() => {
          setWarningTimeLeft((prev) => {
            if (prev <= 1) {
              clearInterval(countdownIntervalId.current);
              console.log(COUNTDOWN_ENDED_MESSAGE);
              handleLogout(); // Logout when countdown ends
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      },
      timeout - 10 * 1000,
    ); // Show warning 10 seconds before timeout
  }, [handleLogout, timeout, isLoggedIn]);

  // Clear all timers
  const clearAllTimers = useCallback(() => {
    console.log(CLEARING_TIMERS_MESSAGE);
    if (timeoutId.current) clearTimeout(timeoutId.current);
    if (warningTimeoutId.current) clearTimeout(warningTimeoutId.current);
    if (countdownIntervalId.current) clearInterval(countdownIntervalId.current);
  }, []);

  // Reset the timers on user activity
  const resetTimers = useCallback(() => {
    if (!isLoggedIn) {
      console.log(USER_NOT_LOGGED_IN_MESSAGE);
      return;
    }

    console.log(USER_ACTIVITY_MESSAGE);
    if (!showWarning) {
      startTimers();
    }
  }, [showWarning, startTimers, isLoggedIn]);

  // Start timers on component mount if logged in
  useEffect(() => {
    if (isLoggedIn) {
      console.log(STARTING_TIMERS_MESSAGE);
      startTimers();
    }

    EVENTS.forEach((event) => window.addEventListener(event, resetTimers));

    return () => {
      console.log(CLEANING_UP_MESSAGE);
      clearAllTimers();
      EVENTS.forEach((event) => window.removeEventListener(event, resetTimers));
    };
  }, [isLoggedIn, startTimers, clearAllTimers, resetTimers]);

  // Close the warning modal and reset the timers
  const closeWarningModal = () => {
    console.log(CLOSING_WARNING_MODAL_MESSAGE);
    setShowWarning(false);
    startTimers();
  };

  return (
    <AutoLogoutContext.Provider value={{}}>
      {children}
      <Modal open={showWarning}>
        <Box sx={modalStyle}>
          <Typography variant="h6" component="h2">
            {MODAL_TITLE}
          </Typography>
          <Typography sx={{ mt: 2 }}>
            {MODAL_MESSAGE} {formatTime(warningTimeLeft)}.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={closeWarningModal}
            sx={{ mt: 2 }}
          >
            {BUTTON_TEXT}
          </Button>
        </Box>
      </Modal>
    </AutoLogoutContext.Provider>
  );
};

AutoLogoutProvider.propTypes = {
  children: PropTypes.node.isRequired,
  logoutCallback: PropTypes.func.isRequired,
  timeout: PropTypes.number,
};

export const useAutoLogout = () => useContext(AutoLogoutContext);
