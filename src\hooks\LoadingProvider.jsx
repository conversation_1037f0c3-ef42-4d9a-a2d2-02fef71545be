import PropTypes from "prop-types";
import React, { createContext, useState, useContext } from "react";

const LoadingContext = createContext();
export const LoadingContextNonFunctionalComp = {};

/**
 * A custom hook to access the loading state within components.
 *
 * @returns {[isLoading : boolean, setIsLoading: Function]} An array containing the loading state and a function to set the loading state.
 */
export const useLoading = () => {
  return useContext(LoadingContext);
};

export const LoadingProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);

  LoadingContextNonFunctionalComp.isLoading = isLoading;
  LoadingContextNonFunctionalComp.setIsLoading = setIsLoading;

  return (
    <LoadingContext.Provider value={[isLoading, setIsLoading]}>
      {children}
    </LoadingContext.Provider>
  );
};

LoadingProvider.propTypes = {
  children: PropTypes.node,
};
