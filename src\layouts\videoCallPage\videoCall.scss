.videoCallLandingPage {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 10px;
  background-color: #f5f5f5;
  border: 3px bold black;

  .confirmationBox {
    width: 100%;
    max-width: 500px;
    animation: fadeIn 0.3s ease-in;

    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
    border-radius: 10px;

    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .confirmationTitle {
      font-weight: 600;
      opacity: 0.9;
      font-size: 1.5rem;
    }

    .confirmationBtn {
      margin-top: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      .btn {
        padding: 0.88rem 3rem;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
        outline: none;
        cursor: pointer;
        border-radius: 8px;
        border: none;
        box-shadow:
          rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
          rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
        background-color: rgba(20, 20, 94, 0.84);
        color: #fff;
        &:hover {
          opacity: 0.85;
        }
      }
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
