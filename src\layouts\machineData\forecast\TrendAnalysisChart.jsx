import React, { useRef, useEffect } from "react";
import * as d3 from "d3";
import { Card, CardContent, Typography, Box } from "@mui/material";

const TrendAnalysisChart = ({ timestamps, values }) => {
  const chartRef = useRef();

  useEffect(() => {
    if (!timestamps || !values) return;

    // Clear previous chart
    d3.select(chartRef.current).selectAll("*").remove();

    // Get the width dynamically
    const containerWidth = chartRef.current.parentElement.clientWidth;
    const width = containerWidth - 40; // Padding compensation
    const height = 250;
    const margin = { top: 20, right: 30, bottom: 30, left: 40 };

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left}, ${margin.top})`);

    // Parse timestamps
    const parsedTimestamps = timestamps.map((t) => new Date(t));

    // Define scales
    const xScale = d3
      .scaleTime()
      .domain(d3.extent(parsedTimestamps))
      .range([0, width - margin.left - margin.right]);

    const yScale = d3
      .scaleLinear()
      .domain([d3.min(values) - 5, d3.max(values) + 5])
      .range([height - margin.top - margin.bottom, 0]);

    // Define line generator
    const line = d3
      .line()
      .x((d, i) => xScale(parsedTimestamps[i]))
      .y((d) => yScale(d))
      .curve(d3.curveMonotoneX);

    // Draw line
    svg
      .append("path")
      .datum(values)
      .attr("fill", "none")
      .attr("stroke", "#1976D2")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add X axis
    svg
      .append("g")
      .attr("transform", `translate(0,${height - margin.top - margin.bottom})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat("%H:%M")));

    // Add Y axis
    svg.append("g").call(d3.axisLeft(yScale));
  }, [timestamps, values]);

  return (
    <Card sx={{ width: "100%", mt: 4 }}>
      <CardContent>
        <Typography variant="h6" align="center" gutterBottom>
          Trend Analysis
        </Typography>
        <Box ref={chartRef} sx={{ width: "100%", height: "250px" }} />
      </CardContent>
    </Card>
  );
};

export default TrendAnalysisChart;
