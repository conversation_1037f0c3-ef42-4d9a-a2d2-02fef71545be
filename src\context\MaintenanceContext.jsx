// This context is used for connecting notfications to related maintenance.

import React, { useContext, useState } from "react";

const MaintenanceInfoContext = React.createContext();
const MaintenanceInfoSeterContext = React.createContext();
//
export function useMaintenanceInfo() {
  return useContext(MaintenanceInfoContext);
}

export function useMaintenanceInfoSeter() {
  return useContext(MaintenanceInfoSeterContext);
}
//
export function MaintenanceInfoProvider({ children }) {
  const [maintenanceInfo, setMaintenanceInfo] = useState();

  function handleMaintenanceInfo(id) {
    console.log("maintenanceInfo context:", id);
    setMaintenanceInfo(id);
    //window.localStorage.setItem('companyId', id);
  }
  return (
    <MaintenanceInfoContext.Provider value={maintenanceInfo}>
      <MaintenanceInfoSeterContext.Provider value={handleMaintenanceInfo}>
        {children}
      </MaintenanceInfoSeterContext.Provider>
    </MaintenanceInfoContext.Provider>
  );
}
