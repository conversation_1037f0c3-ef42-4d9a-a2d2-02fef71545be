import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Divider,
  Box,
  InputLabel,
} from "@mui/material";
import { db } from "../../../../firebase";
import { companies, companyId_constant } from "../../../../constants/data";
import { toastMessageSuccess } from "../../../../tools/toast";

const AddSimulationTests = () => {
  const [actionSheet, setActionSheet] = useState({
    desc: "",
    mod_number: "",
    ch_number: "",
    status: "",
    result: "",
  });
  const handleAddData = () => {
    // db.collection(companies).doc(companyId_constant).collection('fatData').doc('0JSDmXSZzKpAzbbNgP8y')
    // .collection('OQ1').add(actionSheet).then((data) => {
    //     toastMessageSuccess({message: "Successfully Added a new simulation tests"})
    // })
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-evenly",
        flexWrap: "wrap",
      }}
      className="add-action-sheet"
    >
      <>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Description</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, desc: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="DEVIATION, IMPACT AND CORRECTIVE ACTION SHEET"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Mod No.</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, mod_number: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="D-0425"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Ch No.</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, ch_number: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="Protocol-R2F"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Status</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, status: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="1"
          />
        </Box>

        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <Button
            onClick={() => handleAddData()}
            fullWidth
            variant="contained"
            color="primary"
          >
            Create Simulation Test
          </Button>
        </Box>
      </>
    </div>
  );
};

export default AddSimulationTests;
