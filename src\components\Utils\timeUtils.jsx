// Convert minutes since midnight to time string (HH:MM AM/PM)
export const minutesToTime = (minutes) => {
  if (minutes === undefined || minutes === null || minutes === "") return "";

  // Handle the special case for 0 minutes (12:00 AM)
  if (minutes === 0) return "12:00 AM";

  let hours = Math.floor(minutes / 60);
  const mins = minutes % 60;

  // Convert to 12-hour format
  const period = hours >= 12 ? "PM" : "AM";
  hours = hours % 12;
  hours = hours === 0 ? 12 : hours; // Convert 0 to 12 for 12-hour format

  // Format with leading zeros
  const formattedHours = String(hours).padStart(2, "0");
  const formattedMins = String(mins).padStart(2, "0");

  return `${formattedHours}:${formattedMins} ${period}`;
};
