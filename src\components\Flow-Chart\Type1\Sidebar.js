import React, { useState } from "react";
import Syntax<PERSON>ighlighter from "react-syntax-highlighter";
import { docco } from "react-syntax-highlighter/dist/esm/styles/hljs";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import { useStateContext } from "../../../context/ContextProvider";
import imageForNode from "../../../assets/images/node.jpg";
import { useIssueModuleNodeData } from "../../../services2/issueModule/IssueModule.context";

const Sidebar = () => {
  const { currentMode } = useStateContext();
  const [toggle, setToggle] = useState(false);
  const nodes = useIssueModuleNodeData();

  console.log("nodes", nodes);

  const handleToggleSidebar = (value) => {
    setToggle(value);
  };

  const onDragStart = (event, nodeType, nodeDesign) => {
    event.dataTransfer.setData("application/reactflow", nodeType);
    event.dataTransfer.setData("application/reactflow2", nodeDesign);
    event.dataTransfer.effectAllowed = "move";
  };

  const codeString = `function foo() {
	    let name = "faaz";
		return name;
	}`;

  function lengthOfNode(node) {
    return Object.values(node).filter((el) => el === true).length;
  }

  return (
    <aside
      className={`${
        toggle
          ? "toggle-sidebar project-sidebar node-sidebar"
          : "project-sidebar node-sidebar"
      }`}
      style={currentMode === "Dark" ? { backgroundColor: "#161C24" } : {}}
    >
      <div>
        <div
          style={currentMode === "Dark" ? { color: "#fff" } : {}}
          className="toggle-btn"
          onClick={() => handleToggleSidebar(!toggle)}
        >
          {toggle ? (
            <KeyboardDoubleArrowRightIcon />
          ) : (
            <KeyboardDoubleArrowLeftIcon />
          )}
        </div>
        <div
          style={currentMode === "Dark" ? { color: "#fff" } : {}}
          className="description"
        >
          You can drag these nodes to the pane on the right.
        </div>
        <div className="sidebar-content">
          <div
            className="dndnode output"
            onDragStart={(event) =>
              onDragStart(event, "nodeWithOnlyText", "text")
            }
            draggable
          >
            Text Node
          </div>
          <div
            className="image"
            onDragStart={(event) =>
              onDragStart(event, "selectorNode", "rectangleWithImage")
            }
            draggable
          >
            <p>Image Node</p>
            <img src={imageForNode} alt="jcj" />
          </div>
          <div
            className="code"
            onDragStart={(event) =>
              onDragStart(event, "nodeWithCode", "codeNode")
            }
            draggable
          >
            <p style={{ textAlign: "center" }}>Code Node</p>
            <SyntaxHighlighter
              wrapLines={true}
              showLineNumbers={true}
              language="javascript"
              style={docco}
              className="codeNodeMain"
            >
              {codeString}
            </SyntaxHighlighter>
          </div>
          {nodes?.map((element, id) => {
            let className = "custom",
              designName = "rectangleWithImageText",
              nodeType = "";

            // Text Node
            if (element.text_field && lengthOfNode(element) === 1) {
              className = "code output";
              designName = "text";
              nodeType = "nodeWithOnlyText";
            }

            // TextArea Node
            if (element.text_area && lengthOfNode(element) === 1) {
              className = "code";
              designName = "nodeWithOnlyTextArea";
              nodeType = "nodeWithOnlyTextArea";
            }

            // Image Node
            if (element.image && lengthOfNode(element) === 1) {
              className = "only-image";
              designName = "onlyImage";
              nodeType = "nodeWithImageOnly";
            }

            // Code Node
            if (element.code && lengthOfNode(element) === 1) {
              className = "code";
              designName = "onlyCode";
              nodeType = "onlyCode";
            }

            // Text and Image Node
            if (
              element.text_field &&
              element.image &&
              lengthOfNode(element) === 2
            ) {
              className = "image";
              designName = "rectangleWithImage";
              nodeType = "selectorNode";
            }

            // Image and TextArea Node
            if (
              element.text_area &&
              element.image &&
              lengthOfNode(element) === 2
            ) {
              className = "custom-2";
              designName = "rectangleWithImageArea";
              nodeType = "nodeWithImageText";
            }

            // Text and TextArea Node
            if (
              element.text_field &&
              element.text_area &&
              lengthOfNode(element) === 2
            ) {
              className = "image";
              designName = "nodeTextAndArea";
              nodeType = "nodeWithTextAndArea";
            }

            // Text 1 and 2 Node
            if (
              element.text_field &&
              element.text_field2 &&
              lengthOfNode(element) === 2
            ) {
              className = "textfield2";
              designName = "text2";
              nodeType = "mainCustomNode";
            }

            if (
              element.text_area &&
              element.text_area2 &&
              lengthOfNode(element) === 2
            ) {
              className = "textarea2";
              designName = "textarea2";
              nodeType = "nodeWith2TextArea";
            }

            if (
              element.image &&
              element.image2 &&
              lengthOfNode(element) === 2
            ) {
              className = "image3";
              designName = "image2";
              nodeType = "nodeWith2Image";
            }

            if (
              element.code &&
              element.text_area &&
              lengthOfNode(element) === 2
            ) {
              className = "code8";
              designName = "oneCodeoneArea";
              nodeType = "oneCodeoneArea";
            }

            if (element.code && element.image && lengthOfNode(element) === 2) {
              className = "code8";
              designName = "oneCodeoneImage";
              nodeType = "oneCodeoneImage";
            }

            if (
              element.text_field &&
              element.text_field2 &&
              element.text_field3 &&
              lengthOfNode(element) === 3
            ) {
              className = "textfield3";
              designName = "text3";
              nodeType = "nodeWith3Text";
            }

            if (
              element.text_field &&
              element.text_area &&
              element.image &&
              lengthOfNode(element) === 3
            ) {
              className = "custom";
              designName = "rectangleWithImageText";
              nodeType = "customNode";
            }

            if (
              element.text_area &&
              element.text_area2 &&
              element.text_area3 &&
              lengthOfNode(element) === 3
            ) {
              className = "textarea3";
              designName = "textarea3";
              nodeType = "nodeWith3TextArea";
            }

            if (
              element.image &&
              element.image2 &&
              element.image3 &&
              lengthOfNode(element) === 3
            ) {
              className = "image3";
              designName = "image3";
              nodeType = "nodeWith3Image";
            }

            if (
              element.text_field &&
              element.text_field2 &&
              element.text_area &&
              lengthOfNode(element) === 3
            ) {
              designName = "field2area1";
              nodeType = "nodeWith2Field1Area";
            }
            if (
              element.text_field &&
              element.text_field2 &&
              element.image &&
              lengthOfNode(element) === 3
            ) {
              designName = "field2image1";
              nodeType = "nodeWith2Field1Image";
            }
            if (
              element.text_area &&
              element.text_area2 &&
              element.text_field &&
              lengthOfNode(element) === 3
            ) {
              className = "area2field1";
              designName = "area2field1";
              nodeType = "nodeWith2Area1Field";
            }

            if (
              element.text_area &&
              element.text_area2 &&
              element.image &&
              lengthOfNode(element) === 3
            ) {
              className = "area2image1";
              designName = "area2image1";
              nodeType = "nodeWith2Area1Image";
            }

            if (
              element.image &&
              element.image2 &&
              element.text_field &&
              lengthOfNode(element) === 3
            ) {
              className = "image2field1";
              designName = "image2field1";
              nodeType = "nodeWith2Image1Field";
            }

            if (
              element.image &&
              element.image2 &&
              element.text_area &&
              lengthOfNode(element) === 3
            ) {
              className = "image2field1";
              designName = "image2area1";
              nodeType = "nodeWith2Image1Area";
            }

            if (
              element.code &&
              element.code2 &&
              element.code3 &&
              lengthOfNode(element) === 3
            ) {
              className = "code3";
              designName = "threecode";
              nodeType = "codethree";
            }

            if (
              element.code &&
              element.code2 &&
              element.text_field &&
              lengthOfNode(element) === 3
            ) {
              className = "code4";
              designName = "oneTextTwoCode";
              nodeType = "oneTextTwoCode";
            }
            if (
              element.code &&
              element.code2 &&
              element.text_area &&
              lengthOfNode(element) === 3
            ) {
              className = "code4";
              designName = "oneAreaTwoCode";
              nodeType = "oneAreaTwoCode";
            }

            if (
              element.code &&
              element.code2 &&
              element.image &&
              lengthOfNode(element) === 3
            ) {
              className = "code4";
              designName = "oneImageTwoCode";
              nodeType = "oneImageTwoCode";
            }
            if (
              element.code &&
              element.text_field &&
              element.text_field2 &&
              lengthOfNode(element) === 3
            ) {
              className = "code5";
              designName = "twoTextOneCode";
              nodeType = "twoTextOneCode";
            }
            if (
              element.code &&
              element.text_area &&
              element.text_area2 &&
              lengthOfNode(element) === 3
            ) {
              className = "code4";
              designName = "twoAreaOneCode";
              nodeType = "twoAreaOneCode";
            }
            if (
              element.code &&
              element.image &&
              element.image2 &&
              lengthOfNode(element) === 3
            ) {
              className = "code4";
              designName = "twoImageOneCode";
              nodeType = "twoImageOneCode";
            }
            if (
              element.code &&
              element.text_area &&
              element.image &&
              lengthOfNode(element) === 3
            ) {
              className = "code6";
              designName = "oneConeAoneI";
              nodeType = "oneConeAoneI";
            }
            if (
              element.code &&
              element.text_field &&
              element.image &&
              lengthOfNode(element) === 3
            ) {
              className = "code6";
              designName = "oneConeToneI";
              nodeType = "oneConeToneI";
            }

            if (
              element.code &&
              element.text_field &&
              element.text_area &&
              lengthOfNode(element) === 3
            ) {
              className = "code6";
              designName = "oneConeAoneT";
              nodeType = "oneConeAoneT";
            }

            if (
              element.code &&
              element.text_field &&
              element.text_area &&
              element.image &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "fourElements";
              nodeType = "fourElements";
            }

            if (
              element.text_field &&
              element.text_field2 &&
              element.text_area &&
              element.text_area2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoTexttwoArea";
              nodeType = "twoTexttwoArea";
            }

            if (
              element.text_field &&
              element.text_field2 &&
              element.image &&
              element.image2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoTexttwoImage";
              nodeType = "twoTexttwoImage";
            }

            if (
              element.text_field &&
              element.text_field2 &&
              element.code &&
              element.code2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoTexttwoCode";
              nodeType = "twoTexttwoCode";
            }

            if (
              element.text_area &&
              element.text_area2 &&
              element.code &&
              element.code2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoAreatwoCode";
              nodeType = "twoAreatwoCode";
            }

            if (
              element.text_area &&
              element.text_area2 &&
              element.image &&
              element.image2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code11";
              designName = "twoAreatwoImage";
              nodeType = "twoAreatwoImage";
            }
            if (
              element.code &&
              element.code2 &&
              element.image &&
              element.image2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code11";
              designName = "twoImagetwoCode";
              nodeType = "twoImagetwoCode";
            }

            if (
              element.text_field &&
              element.text_field2 &&
              element.image &&
              element.text_area &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoTextOneAreaOneImage";
              nodeType = "twoTextOneAreaOneImage";
            }

            if (
              element.text_field &&
              element.text_field2 &&
              element.code &&
              element.text_area &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoTextOneAreaOneCode";
              nodeType = "twoTextOneAreaOneCode";
            }

            if (
              element.text_field &&
              element.text_field2 &&
              element.code &&
              element.image &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoTextOneImageOneCode";
              nodeType = "twoTextOneImageOneCode";
            }

            if (
              element.text_area &&
              element.text_area2 &&
              element.code &&
              element.image &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoTextOneImageOneCode";
              nodeType = "twoTextOneImageOneCode";
            }

            if (
              element.text_area &&
              element.text_area2 &&
              element.code &&
              element.text_field &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoTextOneAreaOneCode";
              nodeType = "twoTextOneAreaOneCode";
            }

            if (
              element.text_area &&
              element.text_area2 &&
              element.image &&
              element.text_field &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoTextOneAreaOneImage";
              nodeType = "twoTextOneAreaOneImage";
            }

            if (
              element.text_area &&
              element.text_field &&
              element.image &&
              element.image2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoImageoneTextoneArea";
              nodeType = "twoImageoneTextoneArea";
            }

            if (
              element.text_area &&
              element.code &&
              element.image &&
              element.image2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoImageoneTextoneCode";
              nodeType = "twoImageoneTextoneCode";
            }

            if (
              element.text_field &&
              element.code &&
              element.image &&
              element.image2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoImageoneTextoneCode";
              nodeType = "twoImageoneTextoneCode";
            }

            if (
              element.text_field &&
              element.text_area &&
              element.code &&
              element.code2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoCodeoneTextoneArea";
              nodeType = "twoCodeoneTextoneArea";
            }

            if (
              element.text_field &&
              element.image &&
              element.code &&
              element.code2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code10";
              designName = "twoCodeoneTextoneImage";
              nodeType = "twoCodeoneTextoneImage";
            }

            if (
              element.text_area &&
              element.image &&
              element.code &&
              element.code2 &&
              lengthOfNode(element) === 4
            ) {
              className = "code11";
              designName = "twoCodeoneTextoneImage";
              nodeType = "twoCodeoneTextoneImage";
            }

            if (Object.keys(element).length === 3) {
              className = "none";
            }

            return (
              <div
                className={className}
                onDragStart={(event) =>
                  onDragStart(event, `${nodeType}`, `${designName}`)
                }
                draggable
                key={element.id}
              >
                {element.text_field ? <p>{element.name}</p> : ""}
                {element.text_field2 ? <p>2nd Text Field</p> : ""}
                {element.text_field3 ? <p>3rd Text Field</p> : ""}
                {element.image ? <img src={imageForNode} alt="jcj" /> : ""}
                {element.image2 ? <img src={imageForNode} alt="jcj" /> : ""}
                {element.image3 ? <img src={imageForNode} alt="jcj" /> : ""}
                {element.text_area ? (
                  <textarea rows="3" placeholder="Text Area"></textarea>
                ) : (
                  ""
                )}
                {element.text_area2 ? (
                  <textarea rows="3" placeholder="Text Area"></textarea>
                ) : (
                  ""
                )}
                {element.text_area3 ? (
                  <textarea rows="3" placeholder="Text Area"></textarea>
                ) : (
                  ""
                )}
                {element.code ? (
                  <SyntaxHighlighter
                    wrapLines={true}
                    showLineNumbers={true}
                    language="javascript"
                    style={docco}
                    className="codeNodeMain"
                  >
                    {codeString}
                  </SyntaxHighlighter>
                ) : (
                  ""
                )}
                {element.code2 ? (
                  <SyntaxHighlighter
                    wrapLines={true}
                    showLineNumbers={true}
                    language="javascript"
                    style={docco}
                    className="codeNodeMain"
                  >
                    {codeString}
                  </SyntaxHighlighter>
                ) : (
                  ""
                )}
                {element.code3 ? (
                  <SyntaxHighlighter
                    wrapLines={true}
                    showLineNumbers={true}
                    language="javascript"
                    style={docco}
                    className="codeNodeMain"
                  >
                    {codeString}
                  </SyntaxHighlighter>
                ) : (
                  ""
                )}
              </div>
            );
          })}
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
