import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Card,
  CardContent,
} from "@mui/material";

const calculateEfficiencyLoss = (values, forecastValues) => {
  let lossData = [];

  for (let i = 0; i < values.length - 1; i++) {
    if (values[i] !== null && forecastValues[i + 1] !== null) {
      let actual = values[i];
      let forecasted = forecastValues[i + 1];

      let efficiencyLoss = ((actual - forecasted) / actual) * 100;

      lossData.push({
        time: new Date().setHours(10 + i), // Mock time series from 10 AM
        actual,
        forecasted,
        efficiencyLoss: efficiencyLoss.toFixed(2),
      });
    }
  }
  return lossData;
};

const getBackgroundColor = (loss) => {
  if (loss > 15) return "#ffebee"; // 🔴 Critical Loss
  if (loss > 5) return "#fff3e0"; // 🟠 Moderate Loss
  return "#e8f5e9"; // 🟢 Stable
};

const EfficiencyLossTable = ({ values, forecastValues }) => {
  const efficiencyLossData = calculateEfficiencyLoss(values, forecastValues);

  return (
    <Card
      sx={{ p: 2, mt: 4, height: "250px", textAlign: "center", marginTop: 2 }}
    >
      <CardContent>
        <TableContainer component={Paper} sx={{ mt: 2 }}>
          <Typography variant="h6" align="center" gutterBottom sx={{ mt: 2 }}>
            Efficiency Loss Table
          </Typography>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <strong>Time</strong>
                </TableCell>
                <TableCell>
                  <strong>Actual Value</strong>
                </TableCell>
                <TableCell>
                  <strong>Forecasted Value</strong>
                </TableCell>
                <TableCell>
                  <strong>Efficiency Loss (%)</strong>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {efficiencyLossData.map((row, index) => (
                <TableRow
                  key={index}
                  sx={{
                    backgroundColor: getBackgroundColor(row.efficiencyLoss),
                  }}
                >
                  <TableCell>
                    {new Date(row.time).toLocaleTimeString()}
                  </TableCell>
                  <TableCell>{row.actual}</TableCell>
                  <TableCell>{row.forecasted}</TableCell>
                  <TableCell>{row.efficiencyLoss} %</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );
};

export default EfficiencyLossTable;
