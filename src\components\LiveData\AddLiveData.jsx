// import React, { useState, useEffect } from "react";
// import {
//   Box,
//   FormControl,
//   InputLabel,
//   MenuItem,
//   Select,
//   TextField,
// } from "@mui/material";
// import axios from "axios";
// import {
//   toastMessage,
//   toastMessageSuccess,
//   toastMessageWarning,
// } from "../../tools/toast";
// import { useStorage } from "../../utils/useStorage";
// import { ButtonBasic, ButtonBasicCancel } from "../buttons/Buttons";
// import { useCreateMachineCfr } from "../../hooks/cfr/machineCfrProvider";
// import { dbConfig } from "../../infrastructure/db/db-config";
// import { useAuth } from "../../hooks/AuthProvider";

// const AddLiveData = ({ mid, onClose }) => {
//   const { currentUser } = useAuth();

//   const [annoData, setAnnoData] = useState({
//     title: "",
//     desc: "",
//     imgUrl: "",
//     type: "",
//     annotationData: [],
//     created: `${new Date().toISOString}`,
//   });

//   const [file, setFile] = useState(null);
//   const types = ["image/png", "image/jpeg", "image/jpg"];
//   const addlivedatacfr = useCreateMachineCfr();

//   const handleFileUpload = async () => {
//     if (!file) {
//       return;
//     }

//     try {
//       const formData = new FormData();
//       formData.append("image", file);

//       const response = await axios.post(`${dbConfig.url_storage}/upload`, formData);
//       setAnnoData({ ...annoData, imgUrl: response.data.message });
//       console.log(response.data.message)
//       return response.data.message;
//     } catch (error) {
//       console.error("Error uploading file:", error);
//     }
//   };

//   const handleChange = (e) => {
//     let selectedFile = e.target.files[0];

//     if (selectedFile) {
//       if (types.includes(selectedFile.type)) {
//         setFile(selectedFile);
//       } else {
//         setFile(null);
//         toastMessage({
//           message: "Incorrect Format - Please use (PNG , JPG , JPEG)",
//         });
//       }
//     }
//   };

//   const handleSubmit = async (e) => {
//     const date = new Date();
//     const data2 = {
//       activity: "livedata added",
//       dateTime: date,
//       description: "livedata added",
//       machine: mid,
//       module: "Live data",
//       username: currentUser.username,
//     };
//     const data = { ...annoData, mid: mid };

//     if (
//       data.title.trim() === "" ||
//       data.desc.trim() === "" ||
//       data.type.trim() === ""
//     ) {
//       return toastMessageWarning({ message: "Please fill all the details" });
//     }

//     const dataImageURL = await handleFileUpload();
//     if (!dataImageURL) {
//       return toastMessageWarning({
//         message: "Please add an image / wait for preview if already added!",
//       });
//     }

//     /*
//     if (annoData.imgUrl.length === 0) {
//       return toastMessageWarning({
//         message: "Please add an image / wait for preview if already added!",
//       });
//     }
//     */

//     // To handle edge case of unnecessary uploads for valid data and image formats
//     data.imgUrl = dataImageURL;

//     await axios
//       .post(`${dbConfig.url}/imageAnnotationModules`, data)
//       .then((response) => {
//         addlivedatacfr(data2);
//         toastMessageSuccess({
//           message:
//             "Added a new live data project successfully! View Project to start annotating",
//         });
//         onClose();
//         window.location.reload();
//       });
//   };

//   /*
//   // File upload validation message shows when all valid data added
//   useEffect(() => {
//     if (
//       annoData.title &&
//       annoData.desc &&
//       annoData.type &&
//       file
//     ) {
//       (async () => await handleFileUpload())();
//     }

//   }, [file, annoData.title, annoData.desc, annoData.type]);
//   */

//   return (
//     <Box>
//       <TextField
//         required
//         onChange={(e) => setAnnoData({ ...annoData, title: e.target.value })}
//         sx={{ mb: 3, mt: 3 }}
//         label="Title"
//         fullWidth
//         placeholder="eg: Vaccum PID"
//       />

//       <TextField
//         required
//         sx={{ mb: 3 }}
//         fullWidth
//         onChange={(e) => setAnnoData({ ...annoData, desc: e.target.value })}
//         rows={4}
//         multiline
//         label="Descrption"
//         placeholder="eg: This Module belings to Machine X with Cap 2300 hP"
//       />

//       <FormControl fullWidth>
//         <InputLabel>Type</InputLabel>
//         <Select
//           required
//           onChange={(e) => setAnnoData({ ...annoData, type: e.target.value })}
//           label="Type"
//           sx={{ mb: 3 }}
//         >
//           <MenuItem value="process">Process</MenuItem>
//           <MenuItem value="service">Service</MenuItem>
//         </Select>
//       </FormControl>

//       <InputLabel sx={{ mb: 1 }}>Image</InputLabel>
//       <input style={{ marginBottom: "20px" }} onChange={handleChange} type="file" />
//       <div style={{ marginTop: "10px", display: "flex", justifyContent: "center" }}>
//         {file ? (
//           <img src={URL.createObjectURL(file)} width="320px" height="200px" alt="preview" />
//         ) : (
//           <b>Please wait for the preview</b>
//         )}
//       </div>

//       <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
//         <ButtonBasicCancel buttonTitle="Cancel" type="button" onClick={onClose} />
//         <ButtonBasic buttonTitle="Add Live Data" onClick={handleSubmit} />
//       </Box>
//     </Box>
//   );
// };

// export default AddLiveData;

import React, { useState } from "react";
import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import axios from "axios";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import { useStorage } from "../../utils/useStorage";
import { ButtonBasic, ButtonBasicCancel, SubmitButtons } from "../buttons/Buttons";
import { useCreateMachineCfr } from "../../hooks/cfr/machineCfrProvider";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useAuth } from "../../hooks/AuthProvider";

const AddLiveData = ({ mid, onClose }) => {
  const { currentUser } = useAuth();

  const [annoData, setAnnoData] = useState({
    title: "",
    desc: "",
    img_url: "",
    type: "",
    annotation_data: [],
    created: new Date().toISOString(),
  });

  const [file, setFile] = useState(null);
  const types = ["image/png", "image/jpeg", "image/jpg"];
  const addlivedatacfr = useCreateMachineCfr();

  const handleFileUpload = async () => {
    if (!file) {
      return;
    }

    try {
      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${dbConfig.url_storage}/upload`,
        formData,
      );
      setAnnoData({ ...annoData, imgUrl: response.data.data });
      return response.data.data;
    } catch (error) {
      console.error("Error uploading file:", error);
    }
  };

  const handleChange = (e) => {
    let selectedFile = e.target.files[0];

    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG)",
        });
      }
    }
  };

  const handleSubmit = async (e) => {
    const date = new Date();
    const data2 = {
      activity: "livedata added",
      dateTime: date,
      description: "livedata added",
      machine: mid,
      module: "Live data",
      username: currentUser.username,
    };
    const data = { ...annoData, mid: mid };

    if (
      data.title.trim() === "" ||
      data.desc.trim() === "" ||
      data.type.trim() === ""
    ) {
      return toastMessageWarning({ message: "Please fill all the details" });
    }

    const dataImageURL = await handleFileUpload();
    if (!dataImageURL) {
      return toastMessageWarning({
        message: "Please add an image / wait for preview if already added!",
      });
    }

    // To handle edge case of unnecessary uploads for valid data and image formats
    data.img_url = dataImageURL;

    await axios
      .post(`${dbConfig.url}/imageAnnotationModules`, data)
      .then((response) => {
        addlivedatacfr(data2);
        toastMessageSuccess(
          {
            message: (
              <div>
                <div>
                  Added a new live data project successfully! View Project to
                  start annotating
                </div>
                <div
                  style={{
                    textAlign: "left",
                    marginTop: "15px",
                    fontSize: "0.9em",
                    color: "#666",
                  }}
                >
                  File: {file.name}
                </div>
              </div>
            ),
          },
          5000,
        );
        onClose();
      });
  };

  return (
    <Box>
      <TextField
        required
        onChange={(e) => setAnnoData({ ...annoData, title: e.target.value })}
        sx={{ mb: 3, mt: 3 }}
        label="Title"
        fullWidth
        placeholder="eg: Vaccum PID"
      />

      <TextField
        required
        sx={{ mb: 3 }}
        fullWidth
        onChange={(e) => setAnnoData({ ...annoData, desc: e.target.value })}
        rows={4}
        multiline
        label="Description"
        placeholder="eg: This Module belings to Machine X with Cap 2300 hP"
      />

      <FormControl fullWidth>
        <InputLabel>Type</InputLabel>
        <Select
          required
          onChange={(e) => setAnnoData({ ...annoData, type: e.target.value })}
          label="Type"
          sx={{ mb: 3 }}
        >
          <MenuItem value="process">Process</MenuItem>
          <MenuItem value="service">Service</MenuItem>
        </Select>
      </FormControl>

      <InputLabel sx={{ mb: 1 }}>Image</InputLabel>
      <input
        style={{ marginBottom: "20px" }}
        onChange={handleChange}
        type="file"
      />
      <div
        style={{ marginTop: "10px", display: "flex", justifyContent: "center" }}
      >
        {file ? (
          <img
            src={URL.createObjectURL(file)}
            width="320px"
            height="200px"
            alt="preview"
          />
        ) : (
          <b>Please wait for the preview</b>
        )}
      </div>

      <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
        <ButtonBasicCancel
          buttonTitle="Cancel"
          type="button"
          onClick={onClose}
        />
        <SubmitButtons buttonTitle="Add Live Data" onClick={handleSubmit} />
      </Box>
    </Box>
  );
};

export default AddLiveData;
