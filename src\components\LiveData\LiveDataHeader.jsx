import { Box, Button, Typography } from "@mui/material";
import React from "react";
import { ButtonBasic } from "../buttons/Buttons";
import { sharedCss } from "../../styles/sharedCss";
import { useCheckAccess } from "../../utils/useCheckAccess";
import { Diversity1Outlined } from "@mui/icons-material";

const LiveDataHeader = ({ setOpen }) => {
  const commonCss = sharedCss();
  const hasImageAnnotationPOSTAccess = useCheckAccess(
    "imageAnnotationModules",
    "POST",
  );

  return (
    <Box className={commonCss.tableLable}>
      <Typography sx={{ ml: 2 }} fontWeight="bold" variant="h6">
        Live Data
      </Typography>
      <div
      className={commonCss.tableRightContent}
              style={{ display: "flex", alignItems: "center", gap: "1 rem" }} >
              
      <Button
        variant="contained"
        onClick={() => setOpen(true)}
        disabled={!hasImageAnnotationPOSTAccess}
      >
        Add Live Data
      </Button>
      </div>
    </Box>
  );
};

export default LiveDataHeader;
