import React, { createContext, useEffect, useState, useContext } from "react";
import { getData } from "./IssueModule.services";

export const IssueModuleProjectContext = createContext();
export const IssueModuleNodeContext = createContext();
export const IssueModuleChangeCountContext = createContext();

export function useIssueModuleNodeData() {
  return useContext(IssueModuleNodeContext);
}

export function useIssueModuleProjectData() {
  return useContext(IssueModuleProjectContext);
}
export function useIssueModuleChangeCount() {
  return useContext(IssueModuleChangeCountContext);
}

const IssueModuleProvider = ({ children }) => {
  const [projectData, setProjectData] = useState([]);
  const [nodeData, setNodeData] = useState([]);
  const [issueCount, setIssueCount] = useState(0);
  useEffect(() => {
    getData("custom_nodes", setNodeData);
    getData("issueModule", setProjectData);
    console.log("useeffect issuemodule", issueCount);
  }, [issueCount]);

  return (
    <IssueModuleProjectContext.Provider value={projectData}>
      <IssueModuleNodeContext.Provider value={nodeData}>
        <IssueModuleChangeCountContext.Provider
          value={{ issueCount, setIssueCount }}
        >
          {children}
        </IssueModuleChangeCountContext.Provider>
      </IssueModuleNodeContext.Provider>
    </IssueModuleProjectContext.Provider>
  );
};

export default IssueModuleProvider;
