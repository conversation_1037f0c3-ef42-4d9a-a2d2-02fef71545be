import React, { useContext, useState } from "react";
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { addDataToSapMaster } from "../functions/cms-infra.functions";
import { CmsInfraContext } from "../../../../services/cms-infrastructure/cms-infra.context";
import { toastMessage } from "../../../../tools/toast";

const AddSapMaster = () => {
  const { cmsData } = useContext(CmsInfraContext);
  const [cmsId, setCmsId] = useState("");

  async function handleOnSubmit(e) {
    e.preventDefault();

    const formData = {};
    let active = false;

    // Array.from(e.currentTarget.elements).map((field) => {
    //   if (field?.value.length === 0)
    //     active = true
    //     return toastMessage({ message: "Missing fields in form" });
    // });

    if (active === false) {
      Array.from(e.currentTarget.elements).forEach((field) => {
        if (!field.name) return;
        formData[field.name] = field.value;
      });
    }

    addDataToSapMaster(cmsId, formData);
  }

  return (
    <div>
      <div>
        <FormControl sx={{ width: "35%" }} fullWidth variant="outlined">
          <InputLabel>Select CMS Infrastructure</InputLabel>
          <Select
            onChange={(e) => setCmsId(e.target.value)}
            label="Select CMS Infrastructure"
          >
            {cmsData.map((data) => (
              <MenuItem key={data?.id} value={data?.id}>
                {data?.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>

      <form
        onSubmit={handleOnSubmit}
        style={{
          display: "flex",
          justifyContent: "space-evenly",
          flexWrap: "wrap",
          padding: "25px",
        }}
      >
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Instrument Code No"
          fullWidth
          variant="outlined"
          name="code_no"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Equipment Sort Field"
          fullWidth
          variant="outlined"
          name="sort_field"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Instrument Desc.& range "
          fullWidth
          variant="outlined"
          name="desc_range"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Make"
          fullWidth
          variant="outlined"
          name="make"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Location"
          fullWidth
          variant="outlined"
          name="location"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Equipment/ Plant"
          fullWidth
          variant="outlined"
          name="plant"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Operating range"
          fullWidth
          variant="outlined"
          name="range"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Frequency"
          fullWidth
          variant="outlined"
          name="frequency"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Order"
          fullWidth
          variant="outlined"
          name="order"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Certificate No"
          fullWidth
          variant="outlined"
          name="certificate_no"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Calibration Done on"
          fullWidth
          variant="outlined"
          name="done_on"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Due Date"
          fullWidth
          variant="outlined"
          name="due_date"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Least Count"
          fullWidth
          variant="outlined"
          name="least_count"
        />

        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Unit of Measure"
          fullWidth
          variant="outlined"
          name="unit"
        />
        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Error Claimed "
          fullWidth
          variant="outlined"
          name="error"
        />

        <TextField
          sx={{ mb: 2, width: "35%" }}
          placeholder="Remarks"
          fullWidth
          variant="outlined"
          name="remarks"
        />
        <Button
          type="submit"
          fullWidth
          variant="contained"
          sx={{ width: "35%", backgroundColor: "red", color: "white" }}
        >
          {" "}
          Cancel{" "}
        </Button>
        <Button
          type="submit"
          fullWidth
          variant="contained"
          sx={{ width: "35%" }}
        >
          {" "}
          Submit{" "}
        </Button>
      </form>
    </div>
  );
};

export default AddSapMaster;
