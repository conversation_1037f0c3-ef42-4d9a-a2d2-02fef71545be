import React from "react";

const TaskCard = ({ title, desc, type, startDate, endDate, assignedTo }) => {
  return (
    <div className="w-full h-64 flex flex-col justify-between dark:bg-gray-800 bg-white dark:border-gray-700 rounded-lg border border-gray-400 mb-6 py-5 px-4">
      <div>
        <h3 className="text-gray-800 dark:text-gray-100 leading-7 font-semibold w-11/12">
          Create New Component for Stall
        </h3>
      </div>
      <div>
        <div className="mb-3 flex items-center flex-no-wrap">
          <div className="w-6 h-6 bg-cover bg-center rounded-md">
            <img
              src="https://tuk-cdn.s3.amazonaws.com/assets/components/avatars/a_4_0.png"
              alt
              className="h-full w-full overflow-hidden object-cover rounded-full border-2 border-white dark:border-gray-700 shadow"
            />
          </div>
          <div className="w-6 h-6 bg-cover rounded-md -ml-2">
            <img
              src="https://tuk-cdn.s3.amazonaws.com/assets/components/avatars/a_4_1.png"
              alt
              className="h-full w-full overflow-hidden object-cover rounded-full border-2 border-white dark:border-gray-700 shadow"
            />
          </div>
          <div className="w-6 h-6 bg-cover rounded-md bg-center -ml-2">
            <img
              src="https://tuk-cdn.s3.amazonaws.com/assets/components/avatars/a_4_2.png"
              alt
              className="h-full w-full overflow-hidden object-cover rounded-full border-2 border-white dark:border-gray-700 shadow"
            />
          </div>
        </div>
        <div className="flex items-center justify-between text-gray-800">
          <p className="dark:text-gray-100 text-sm">March 28, 2020</p>
          <div className="w-8 h-8 rounded-full dark:bg-gray-100 dark:text-gray-800 bg-gray-800 text-white flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="icon icon-tabler icon-tabler-pencil"
              width={20}
              height={20}
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" />
              <path d="M4 20h4l10.5 -10.5a1.5 1.5 0 0 0 -4 -4l-10.5 10.5v4" />
              <line x1="13.5" y1="6.5" x2="17.5" y2="10.5" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskCard;
