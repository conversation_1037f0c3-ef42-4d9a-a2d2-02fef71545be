import { TableCell, TableRow } from "@mui/material";
import React from "react";
import { EditButtons } from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";

export default function AdminCard(props) {
  const { currentColorLight, currentMode } = useStateContext();
  return (
    <TableRow
      sx={{
        "&:last-child td, &:last-child th": {
          border: 0,
        },
      }}
    >
      <TableCell
        style={{ padding: "20px 10px" }}
        sx={
          currentMode === "Dark"
            ? { color: "white", fontSize: 18 }
            : { fontSize: 18 }
        }
        align="left"
        className="self-center w-1/12"
      >
        <img
          className="h-10  rounded-md "
          width="50px"
          height="50px"
          src={props.image}
        ></img>
      </TableCell>
      <TableCell
        style={{ padding: "20px 10px" }}
        sx={
          currentMode === "Dark"
            ? { color: "white", fontSize: 18 }
            : { fontSize: 18 }
        }
        align="left"
        className="self-center w-2/12"
      >
        {props.fullName}
      </TableCell>
      <TableCell
        style={{ padding: "20px 10px" }}
        sx={
          currentMode === "Dark"
            ? { color: "white", fontSize: 18 }
            : { fontSize: 18 }
        }
        align="left"
        className="self-center w-2/12"
      >
        {props.userName}
      </TableCell>
      <TableCell
        style={{ padding: "20px 10px" }}
        sx={
          currentMode === "Dark"
            ? { color: "white", fontSize: 18 }
            : { fontSize: 18 }
        }
        align="left"
        className="self-center w-2/12"
      >
        {props.phone}
      </TableCell>
      <TableCell
        style={{ padding: "20px 10px" }}
        sx={
          currentMode === "Dark"
            ? { color: "white", fontSize: 18 }
            : { fontSize: 18 }
        }
        align="left"
        className="self-center w-4/12"
      >
        {props.email}
      </TableCell>
      <TableCell
        style={{ padding: "20px 10px" }}
        sx={
          currentMode === "Dark"
            ? { color: "white", fontSize: 18 }
            : { fontSize: 18 }
        }
        align="left"
        className="self-center w-1/12"
      >
        {" "}
        <EditButtons onClick={props.onClick} />
      </TableCell>
    </TableRow>
  );
}
