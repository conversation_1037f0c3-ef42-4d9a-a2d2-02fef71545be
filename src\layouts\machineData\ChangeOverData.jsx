import React, { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import "./machineData.scss";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import TablePagination from "@mui/material/TablePagination";
import ChangeOverItem from "./ChangeOverItem";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  CircularProgress,
  Typography,
  Button,
  TextField,
  IconButton,
} from "@mui/material";
import SearchOutlinedIcon from "@mui/icons-material/SearchOutlined";
import { useAuth } from "../../hooks/AuthProvider";
import { useStateContext } from "../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { sharedCss } from "../../styles/sharedCss";
import MachineDataHeader from "./MachineDataHeader";
import AddChangeOver from "./AddChanmgeOver";
import TableHeader from "./TableHeader";
import NoDataComponent from "../../components/commons/noData.component";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";

const ChangeOverData = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { mid } = useParams();
  const [details, setDetails] = useState([]);
  const [machineName, setMachineName] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const { currentUser } = useAuth();
  const { currentColor, currentMode } = useStateContext();
  const [dataLoading, setDataLoading] = useState(true);
  const { refreshCount } = useMongoRefresh();
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const hasPOSTAccess = useCheckAccess("changeOver", "POST");
  const hasGETAccess = useCheckAccess("changeOver", "GET");

  const getAllChangeOvers = async () => {
    try {
      const response = await axios.get(
        `${dbConfig.url}/changeOver/getFromMachine/${mid}`
      );
      const changeOverData = response?.data?.data || [];
      setDetails(changeOverData);
    } catch (error) {
      console.error("ERROR IN CHANGEOVERS:", error.message);
      setDetails([]);
    } finally {
      setDataLoading(false);
    }
  };

  const fetchCurrentMachine = async () => {
    await axios.get(`${dbConfig.url}/machines/${mid}`).then((response) => {
      setMachineName(response.data.data?.title);
    });
  };

  const commonCss = sharedCss();
  useEffect(() => {
    getAllChangeOvers();
    fetchCurrentMachine();
  }, [refreshCount]);

  const filteredChangeOverDetails = useMemo(() => {
    return details.filter((data) => {
      const isMachineMatch = data.mid === mid;
      const isSearchMatch = data.title
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      return isMachineMatch && isSearchMatch;
    });
  }, [details, mid, searchTerm]);

  const paginatedDetails = useMemo(() => {
    return filteredChangeOverDetails.slice(
      page * rowsPerPage,
      page * rowsPerPage + rowsPerPage
    );
  }, [filteredChangeOverDetails, page, rowsPerPage]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleOnChangeSearchTerm = (e) => {
    setSearchTerm(e.target.value);
    setPage(0);
  };

  return (
    <section>
      <MachineDataHeader />

      <div>
        <div
          className={`${commonCss.sectionContainer} ${commonCss.backgroundLight} border-radius-outer`}
        >
          <div className={commonCss.tableLable}>
            <Typography sx={{ ml: 2 }} fontWeight="bold" variant="h6">
              Changeover
            </Typography>
            <div
              className={commonCss.tableRightContent}
              style={{ display: "flex", alignItems: "center", gap: "1rem" }}
            >
              <TextField
                label="Search"
                value={searchTerm}
                onChange={handleOnChangeSearchTerm}
                size="small"
                className={`${commonCss.searchBox} ${commonCss.inputAlignmentFix}`}
                placeholder="Search by title"
                InputProps={{
                  startAdornment: (
                    <IconButton>
                      <SearchOutlinedIcon />
                    </IconButton>
                  ),
                }}
              />
              {currentUser?.role !== "trainee" && (
                <Button
                  variant="contained"
                  onClick={() => setIsOpen(true)}
                  disabled={!hasPOSTAccess}
                >
                  Add Changeover
                </Button>
              )}
            </div>
          </div>

          <div>
            {hasGETAccess ? (
              <>
                <TableContainer
                  component={Paper}
                  className="table border-radius-inner"
                  sx={commonOuterContainerStyle}
                >
                  <Table sx={{ minWidth: 650 }}>
                    <TableHeader
                      currentMode={currentMode}
                      columns={[
                        { label: "Title", align: "left" },
                        { label: "Description", align: "left" },
                        { label: "Actions", align: "center" },
                      ]}
                    />
                    <TableBody>
                      {dataLoading ? (
                        <TableRow>
                          <TableCell colSpan={3} align="center">
                            <CircularProgress />
                          </TableCell>
                        </TableRow>
                      ) : paginatedDetails.length > 0 ? (
                        paginatedDetails.map((data, index) => (
                          <ChangeOverItem
                            role={currentUser?.role}
                            data={data}
                            key={data._id + index}
                            mid={mid}
                            machineName={machineName}
                            lastItem={
                              filteredChangeOverDetails.length - 1 ===
                              page * rowsPerPage + index
                            }
                          />
                        ))
                      ) : (
                        <NoDataComponent cellColSpan={3} dataLoading={false} />
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
                {filteredChangeOverDetails.length > 0 && (
                <TablePagination
                  component="div"
                  count={filteredChangeOverDetails.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  className={commonCss.tablePagination}
                />
                )}
              </>
            ) : (
              <NotAccessible />
            )}
          </div>
        </div>
      </div>

      <Dialog open={isOpen} fullWidth>
        <DialogTitle
          style={
            currentMode === "Dark"
              ? { backgroundColor: "#212B36", color: "white" }
              : {}
          }
        >
          Add Change Over
        </DialogTitle>
        <DialogContent
          style={
            currentMode === "Dark"
              ? { backgroundColor: "#212B36", color: "white" }
              : {}
          }
        >
          <AddChangeOver
            mid={mid}
            machineName={machineName}
            handleClose={() => setIsOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default ChangeOverData;
