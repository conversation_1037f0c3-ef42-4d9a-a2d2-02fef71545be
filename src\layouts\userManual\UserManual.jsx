import {
  CloseCircleOutlined,
  DeleteFilled,
  EditOutlined,
} from "@ant-design/icons";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  Typography,
  Box,
  IconButton,
  Button,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import Carousel from "react-material-ui-carousel";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import React, { useEffect, useState } from "react";
import Delete from "../../components/Delete/Delete";

import "./userManual.scss";
import { useStateContext } from "../../context/ContextProvider";
import { makeStyles } from "@mui/styles";
import { sharedCss } from "../../styles/sharedCss";

const useCustomStyles = makeStyles((theme) => ({
  userContainer: {
    padding: "1rem",
    borderRadius: "10px",
    backgroundColor: theme.palette.custom.backgroundForth,
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
  },
  usersOuterContainer: {
    width: "100%",
  },
  usersInnerContainer: {
    display: "flex",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  addButton: {
    width: "max-content",
  },
  userPageContainer: {
    padding: "1rem",
    border: "1px solid gainsboro",
  },
}));

const UserManual = () => {
  const [open, setOpen] = useState(false);
  const [openStep, setOpenStep] = useState(false);
  const [steps, setSteps] = useState([]);
  const [newSteps, setNewSteps] = useState([]);
  const [title, setTitle] = useState("");
  const [desc, setDesc] = useState("");
  const [type, setType] = useState("");
  const [file, setFile] = useState(null);
  const [openDelete, setOpenDelete] = useState(false);
  const { currentMode } = useStateContext();

  const background = { background: currentMode === "Dark" ? "#2b2b2b" : "" };
  const color = { color: currentMode === "Dark" ? "#fff" : "" };
  const customCss = useCustomStyles();
  const commonCss = sharedCss();

  return (
    <section className={customCss.userPageContainer}>
      <div
        className={commonCss.headingContainer}
        style={{
          padding: "0.5rem 0.75rem",
          display: "flex",
          alignItems: "center",
        }}
      >
        <Box>
          <Typography variant="h4">User Manuals</Typography>
        </Box>
        <div>
          <Button className={customCss.addButton} variant="contained">
            Add Manuals
          </Button>
        </div>
      </div>

      <div className={`carouselContainer ${commonCss.generalBackground}`}>
        <Carousel
          NextIcon={<NavigateNextIcon />}
          PrevIcon={<NavigateBeforeIcon />}
          fullHeightHover={false}
          navButtonsProps={{
            style: {
              // backgroundColor: '#14145ed6',
              backgroundColor: "#666",
              color: "#fff",
              borderRadius: "50%",
              padding: ".4rem",
              margin: "0 1rem",
            },
          }}
          indicators={false}
          autoPlay={false}
          animation="slide"
          duration="800"
          navButtonsAlwaysVisible={true}
        >
          <div>
            <div className="p-3  carouselInner">
              <div
                className="flex items-center justify-between mt-2 carouselInnerHeaderContainer"
                style={background}
              >
                <div className="left flex items-center ">
                  <div
                    className="text-xl text-gray-600 uppercase font-bold"
                    style={color}
                  >
                    title :
                  </div>
                  <div className="text-sm ml-4 text-gray-500" style={color}>
                    Description
                  </div>
                </div>

                <div className="flex items-center justify-center">
                  <Button>
                    <EditOutlined className="text-xl" />
                  </Button>
                  <div className="ml-2"></div>
                  <Button onClick={() => setOpenDelete(true)}>
                    <DeleteFilled className="text-xl text-red-600" />
                  </Button>
                </div>
              </div>
              <div className="carouselInnerContent">
                <div
                  className="imageContainer"
                  style={{ display: "flex", justifyContent: "center" }}
                >
                  <video controls>
                    <source src="" type="video/mp4" />
                  </video>
                </div>
                <div
                  className="subBtnsContainer"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    marginTop: "0.5rem",
                    justifyContent: "center",
                  }}
                >
                  <Button
                    variant="contained"
                    onClick={() => setOpenStep(true)}
                    className="subStep"
                  >
                    Sub Steps
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => setOpen(true)}
                    style={{ marginLeft: "15px" }}
                    className="addBtn"
                  >
                    Add Sub-Steps
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </Carousel>
      </div>
    </section>
  );
};

export default UserManual;
