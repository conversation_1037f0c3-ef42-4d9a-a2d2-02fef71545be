import React, { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import {
  Paper,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  TablePagination,
  TextField,
  IconButton,
} from "@mui/material";
import { SearchOutlined } from "@mui/icons-material";
import axios from "axios";

import SerializedList from "./SerializedList";
import AddSeries from "./AddSeries";
import TableHeader from "../TableHeader";
import NoDataComponent from "../../../components/commons/noData.component";

import { useStateContext } from "../../../context/ContextProvider";
import { sharedCss } from "../../../styles/sharedCss";
import { useCommonOuterContainerStyle } from "../../../styles/useCommonOuterContainerStyle";
import { themeColors } from "../../../infrastructure/theme";
import { useAuditEditCount, useAuditGetter, useAuditSetter } from "../../../services3/audits/AuditContext";
import { dbConfig } from "../../../infrastructure/db/db-config";

const Serialized = ({ type }) => {
  const { mid } = useParams();
  const [open, setOpen] = useState(false);
  const [machineName, setMachineName] = useState("");
  const [dataLoading, setDataLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  const commonCss = sharedCss();
  const commonOuterContainerStyle = useCommonOuterContainerStyle();
  const { currentMode } = useStateContext();

  const handleAudit = useAuditSetter();
  const series = useAuditGetter();
  const auditEditCount = useAuditEditCount();

  // Pagination states
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  useEffect(() => {
    handleAudit(type);
    axios
      .get(`${dbConfig.url}/machines/${mid}`)
      .then((res) => setMachineName(res?.data?.data?.title))
      .catch((e) => console.error("Machine fetch error:", e))
      .finally(() => setDataLoading(false));
  }, [series.length, auditEditCount]);

  const handleChangePage = (event, newPage) => setPage(newPage);
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleOnChangeSearchTerm = (e) => {
    setSearchTerm(e.target.value);
    setPage(0);
  };

  const filteredSeries = useMemo(() => {
    return series.filter(
      (item) =>
        item.mid === mid &&
        (searchTerm.trim() === "" ||
          item.protocolNo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.description?.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [series, mid, searchTerm]);

  const paginatedData = useMemo(() => {
    const startIndex = page * rowsPerPage;
    return filteredSeries.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredSeries, page, rowsPerPage]);

  const hasData = filteredSeries.length > 0;

  return (
    <section>
      <div>
        <div className={`${commonCss.sectionContainer} ${commonCss.backgroundLight} border-radius-outer`}>
          <div className={commonCss.tableLable}>
            <Typography sx={{ ml: 2 }} fontWeight="bold" variant="h6">
              {type}
            </Typography>
            <div
              className={commonCss.tableRightContent}
              style={{ display: "flex", alignItems: "center", gap: "1rem" }}
            >
              <TextField
                label="Search"
                placeholder="Search by Protocol No. or Description"
                className={`${commonCss.searchBox} ${commonCss.inputAlignmentFix}`}
                id="outlined-size-small"
                size="small"
                value={searchTerm}
                onChange={handleOnChangeSearchTerm}
                InputProps={{
                  startAdornment: (
                    <IconButton>
                      <SearchOutlined />
                    </IconButton>
                  ),
                }}
              />
              <Button variant="contained" onClick={() => setOpen(true)}>
                {`Add ${type} Series`}
              </Button>
            </div>
          </div>

          <div>
            <TableContainer
              component={Paper}
              className="table border-radius-inner"
              sx={commonOuterContainerStyle}
            >
              <Table>
                <TableHeader
                  currentMode={currentMode}
                  columns={[
                    { label: "Index", align: "left" },
                    { label: "Protocol No.", align: "left" },
                    { label: "Description", align: "left" },
                    { label: "Status", align: "center" },
                    { label: "Actions", align: "center" },
                  ]}
                />
                <TableBody>
                  {dataLoading ? (
                    <TableRow>
                      <TableCell colSpan={5} align="center">
                        <CircularProgress />
                      </TableCell>
                    </TableRow>
                  ) : hasData ? (
                    paginatedData.map((data, idx) => (
                      <SerializedList
                        key={data?._id}
                        id={data?._id}
                        data={data}
                        idx={page * rowsPerPage + idx}
                        mid={mid}
                        type={type}
                        machineName={machineName}
                        lastItem={idx === paginatedData.length - 1}
                      />
                    ))
                  ) : (
                    <NoDataComponent cellColSpan={5} dataLoading={dataLoading} />
                  )}
                </TableBody>
              </Table>
            </TableContainer>

           {/* Pagination */}
           {hasData && (
              <TablePagination
                component="div"
                count={filteredSeries.length}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                className={commonCss.tablePagination}
              />
            )}
        
          </div>
        </div>
      </div>

      <Dialog open={open} fullWidth>
        <DialogTitle
          style={
            currentMode === "Dark"
              ? { backgroundColor: themeColors.dark.secordary, color: "white" }
              : {}
          }
        >
          Add {type} Series
        </DialogTitle>
        <DialogContent
          style={
            currentMode === "Dark"
              ? { backgroundColor: themeColors.dark.secordary, color: "white" }
              : {}
          }
        >
          <AddSeries
            index={filteredSeries.length + 1}
            handleClose={() => setOpen(false)}
            type={type}
            machineName={machineName}
            mid={mid}
          />
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default Serialized;
