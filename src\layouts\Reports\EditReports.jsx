import { TextField } from "@mui/material";
import { Button, Checkbox, InputLabel } from "@mui/material";
import React from "react";
import CloseIcon from "@mui/icons-material/Close";
import { useState } from "react";
import { useAuth } from "../../hooks/AuthProvider";
import { useParams } from "react-router-dom";
import BookmarkIcon from "@mui/icons-material/Bookmark";
import { companies, companyId_constant, fatReport } from "../../constants/data";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import { db } from "../../firebase";
import { updateUserOnEditDocument } from "../../utils/updateUserOnEditDocument";
import LoggingFunction from "../../components/CFR-Report/LoggingFunction";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useContentEditCount } from "../../services3/audits/ContentContext";

const EditReports = ({ details, user, machineName, type, handleClose }) => {
  const [summary, setSummary] = useState(
    details.summary ? details.summary : "",
  );
  const [comment, setComment] = useState(
    details?.comment ? details?.comment : "",
  );
  const { reportId } = useParams();
  const { currentUser } = useAuth();
  const { contentEditCount, setContentEditCount } = useContentEditCount();

  // const databaseCollection = db
  // 	.collection(companies)
  // 	.doc(companyId_constant)
  // 	.collection(fatReport)
  // 	.doc(reportId)
  // 	.collection(`fatData`);

  const handleAuditDataUpdate = async (payload) => {
    // fatData-report
    await axios
      .put(`${dbConfig.url}/fatdata-report/${details?._id}`, payload)
      .then((response) => {
        console.log("fatData report data report id:", reportId);
        console.log("fatData report data:", response?.data);
        toastMessageSuccess({
          message: "Updated details for documentation Successfully !",
        });
        handleClose();
        setContentEditCount(contentEditCount + 1);
      })
      .catch((error) => {
        console.error(error);
        toastMessage({
          message: "Updated details for documentation Failed !",
        });
      });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const docData = {
      summary,
      comment,
    };
    await handleAuditDataUpdate(docData);

    // databaseCollection
    // 	.doc(details.id)
    // 	.update(docData)
    // 	.then(() => {
    // 		handleClose();
    // 		updateUserOnEditDocument({
    // 			userEmail: currentUser.email,
    // 			docId: reportId,
    // 			fatDataId: details.id,
    // 		});
    // 		LoggingFunction(
    // 			machineName,
    // 			details?.title,
    // 			user?.fname + " " + user?.lname,
    // 			"FAT Reports",
    // 			`${details?.title} is updated from FATReports`
    // 		);
    // 		toastMessageSuccess({
    // 			message: "Updated details for documentation Successfully !",
    // 		});
    // 	});
  };
  return (
    <form onSubmit={handleSubmit}>
      <div>
        <InputLabel>Comment</InputLabel>
        <TextField
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          style={{ marginBottom: "10px" }}
          variant="outlined"
          fullWidth
          placeholder="comment"
        />
      </div>

      <InputLabel>Summary</InputLabel>
      <section style={{ marginBottom: "10px" }} className="flex">
        <div className="flex items-center justify-start gap-x-4">
          Document Complies
          <span>
            <Checkbox checked={summary} onClick={() => setSummary(true)} />
            Yes
          </span>
          <span>
            <Checkbox checked={!summary} onClick={() => setSummary(false)} />
            No
          </span>
        </div>
      </section>

      <div className="flex justify-between">
        <Button
          onClick={handleClose}
          variant="contained"
          color="secondary"
          endIcon={<CloseIcon />}
        >
          {" "}
          Cancel{" "}
        </Button>
        <Button
          type="submit"
          variant="contained"
          color="primary"
          endIcon={<BookmarkIcon />}
        >
          Save Details
        </Button>
      </div>
    </form>
  );
};

export default EditReports;
