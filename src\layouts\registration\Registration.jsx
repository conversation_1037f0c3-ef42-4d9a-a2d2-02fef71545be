import "./loginPage.scss";

import { alpha, styled } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import InputLabel from "@mui/material/InputLabel";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import { useAuth } from "../../hooks/AuthProvider";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import { toastMessage } from "../../tools/toast";
import { alluser, companies, companyId_constant } from "../../constants/data";
import logo from "../../assets/images/logo.png";

import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

const BootstrapInput = styled(InputBase)(({ theme }) => ({
  "label + &": {
    marginTop: theme.spacing(0),
  },
  "& .MuiInputBase-input": {
    borderRadius: 4,
    position: "relative",
    // backgroundColor: theme.palette.mode === 'light' ? '#fcfcfb' : '#2b2b2b',
    border: "1px solid #ced4da",
    fontSize: 14,
    color: "#344767",
    padding: "10px 12px",
    transition: theme.transitions.create([
      "border-color",
      "background-color",
      "box-shadow",
    ]),
    "&:focus": {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main,
    },
    "&:disabled": {
      //color: 'red',
      backgroundColor: "#aaa",
    },
  },
  disabled: {},
}));

const RegistrationPortal = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rePass, setRePass] = useState("");
  const [showPassword1, setShowPassword1] = useState(false);
  const [showPassword2, setShowPassword2] = useState(false);
  const [users, setusers] = useState([]);
  const [userDetailFromAll, setUserDetailFromAll] = useState([]); // A_allusers // single user info from all user list
  const { id } = useParams();
  const [userDetail, setUserDetail] = useState({});
  const { signup } = useAuth();
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);

  useEffect(() => {
    // db.collection(companies).doc(companyId_constant).collection('userData').onSnapshot(snap => {
    //   const data = firebaseLooper(snap)
    //   setusers(data)
    // })
    // db.collection(companies).doc(companyId_constant).collection('userData').doc(id).onSnapshot(snap => {
    //     const data = snap.data()
    //     setUserDetail(data)
    //   })
    //   db.collection(alluser).doc(id).onSnapshot(snap => {
    //     const data = snap.data();
    //     setEmail(data?.email)
    //     //console.log("dataa:", data)
    //     if(!data){
    //       setOpen(true)
    //     }
    //     setUserDetailFromAll(data)
    //   })
  }, []);

  //
  const handleClose = () => {
    setOpen(false);
  };
  //

  async function handleSubmit(e) {
    e.preventDefault();

    let activate = false;
    for (let index = 0; index < users.length; index++) {
      const element = users[index];
      if (email === element.email) {
        activate = true;
        break;
      }
    }
    // if (activate == false) {
    //   return toastMessage({ message: "Your email is not registered ! Please use a valid email address " })
    // }

    if (password != rePass) {
      return toastMessage({ message: "Your password doesn't match !" });
    }

    try {
      await signup(email, password);
      navigate("/company-id");
    } catch {
      toastMessage({
        message: "Failed to Register. Contact Admin To proceed ! ",
      });
    }
  }

  return (
    <section className="loginPage">
      <div className="logoContainer">
        {/* <img
          src="https://firebasestorage.googleapis.com/v0/b/lyodatatest.appspot.com/o/arizon%2Farizon.webp?alt=media&token=e1e0804e-4387-4779-bfed-6d31dcb876cf"
          alt=""
        /> */}
      </div>
      <div className="illustrationContainer">
        <img src={logo} alt="" />
      </div>

      <div className="formContainer">
        <div className="formTitle">Registration</div>
        <div className="formDesc">Welcome to Arizon</div>

        <form onSubmit={handleSubmit} className="form">
          <div className="labelFields">
            <InputLabel shrink htmlFor="userEmail">
              Email Address
            </InputLabel>
            <BootstrapInput //style={{color:'#000', backgroundColor:'#ddd' , textDecoration:'none'}}
              //onChange={(e) => setEmail(e.target.value)}
              value={userDetailFromAll?.email}
              id="userEmail"
              disabled
              //placeholder="eg. <EMAIL>"
            />
          </div>

          <div className="labelFields">
            <InputLabel shrink htmlFor="password">
              Password
            </InputLabel>
            <BootstrapInput
              type={showPassword1 ? "text" : "password"}
              onChange={(e) => setPassword(e.target.value)}
              id="password"
              placeholder="eg. password123"
            />
            <div className="flex mt-1">
              <div>
                <input
                  type="checkbox"
                  value={showPassword1}
                  onChange={(e) => setShowPassword1(e.target.checked)}
                />
              </div>
              <div className="ml-2 text-xs font-bold self-center">
                {" "}
                Show Password
              </div>
            </div>
          </div>

          <div className="labelFields">
            <InputLabel shrink htmlFor="password">
              Re Enter Password
            </InputLabel>
            <BootstrapInput
              type={showPassword2 ? "text" : "password"}
              onChange={(e) => setRePass(e.target.value)}
              id="password"
              placeholder="eg. password123"
            />
            <div className="flex mt-1">
              <div>
                <input
                  type="checkbox"
                  value={showPassword2}
                  onChange={(e) => setShowPassword2(e.target.checked)}
                />
              </div>
              <div className="ml-2 text-xs font-bold self-center">
                {" "}
                Show Password
              </div>
            </div>
          </div>

          {/* <div className="linkContainer">
            <Link to="/forgot-password" className="link">
              Forgot Password?
            </Link>
          </div> */}
          <button>Register</button>
        </form>
      </div>
      {/* // */}

      <Dialog
        open={open}
        //onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          <div className="text-red-700 animate-pulse">{"WARNING"}</div>
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            <div className="text-red-200">
              You have broken or expired link. Please ask the 'Admin' to resend
              new registration link...
            </div>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          {/* <Button onClick={handleClose} autoFocus>
            Agree
          </Button> */}
        </DialogActions>
      </Dialog>
    </section>
  );
};

export default RegistrationPortal;
