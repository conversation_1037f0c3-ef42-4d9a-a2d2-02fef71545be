import React, { useContext, useState } from "react";

const CompanyIdContext = React.createContext();
const CompanyIdSeterContext = React.createContext();
//
export function useCompanyId() {
  return useContext(CompanyIdContext);
}

export function useCompanyIdSeter() {
  return useContext(CompanyIdSeterContext);
}

//
export function CompanyIdProvider({ children }) {
  const [companyId, setCompanyId] = useState();

  function handleCompanyId(id) {
    setCompanyId(id);
    window.localStorage.setItem("companyId", id);
  }
  return (
    <CompanyIdContext.Provider value={companyId}>
      <CompanyIdSeterContext.Provider value={handleCompanyId}>
        {children}
      </CompanyIdSeterContext.Provider>
    </CompanyIdContext.Provider>
  );
}
