.reportSection {
  .reportHeader {
    width: 100%;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    color: #344767;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .tabsContainer {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 48px;
      width: "100%";
      .report_tab {
        font-size: 16px;
        font-weight: 500;

        line-height: 24px;
        cursor: pointer;
        padding-bottom: 4px;
      }
      .report_active_tab {
        border-bottom: 3px solid #2f6dda;
      }
    }
  }

  .reportMain {
    padding: 2rem 1.5rem;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    color: #344767;
    .header {
      width: 100%;
      height: fit-content;
      margin-bottom: 1.5rem;
      padding: 0rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      div {
        display: flex;
        align-items: center;
        text-align: left;
        h2 {
          text-transform: capitalize;
          font-size: 18px;
          font-weight: 700;
          margin-left: 6px;
        }
      }
    }
  }
}
