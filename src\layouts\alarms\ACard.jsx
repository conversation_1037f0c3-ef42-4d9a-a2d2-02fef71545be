/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable jsx-a11y/img-redundant-alt */
import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useStateContext } from "../../context/ContextProvider";
import { useMaintenanceInfoSeter } from "../../context/MaintenanceContext";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { Card } from "@mui/material";

const ACard = ({ alerts, machineData }) => {
  const history = useNavigate();
  const maintenanceInfoSetter = useMaintenanceInfoSeter();
  const location = useLocation();
  const { currentMode } = useStateContext();
  const pathName = location.pathname;

  const handleRedirect = (data) => {
    if (pathName == "/maintenance/" + data.mid) {
      maintenanceInfoSetter(data);
    } else {
      maintenanceInfoSetter(data);
      history.replace("/maintenance" + "/" + data.mid);
    }
  };

  return (
    <Card
      variant="outlined"
      style={
        currentMode === "Dark"
          ? { backgroundColor: "#212B36", color: "white" }
          : { backgroundColor: "#fff" }
      }
      className="alarmCard hover:shadow-xl hover:shadow-gray-500"
    >
      <div className="alarmInfo">
        <h3
          className="alertName"
          style={currentMode === "Dark" ? { color: "white" } : {}}
        >
          <b className="capitalize">{alerts.name} : </b>{" "}
          {parseFloat(alerts.value).toFixed(4)}
        </h3>
        <p className="text-base text-body-color leading-relaxed mb-7">
          {alerts?.time?.toDate().toString().substring(0, 15)}
        </p>
      </div>
      <div className="actionContainer">
        <ButtonBasic buttonTitle={"Acknowledge"} />
        <ButtonBasic
          buttonTitle={"Resolve"}
          onClick={() => handleRedirect(alerts)}
        />
      </div>
    </Card>
  );
};

export default ACard;
