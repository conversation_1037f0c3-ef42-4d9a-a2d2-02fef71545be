import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import React, { useState } from "react";
import { companies, companyId_constant } from "../../../constants/data";
import { db } from "../../../firebase";

const EditDocumentation = ({ mid, handleClose, data, type }) => {
  const [title, setTitle] = useState(data.title);
  const [desc, setDesc] = useState(data.desc);
  const [start, setStart] = useState(data.start);
  const [stop, setStop] = useState(data.stop);
  const [time, setTime] = useState(data.time);
  const [comment, setComment] = useState(data.comment);
  const [tolerance, setTolerance] = useState(data.tolerance);

  const handleSubmit = (e) => {
    e.preventDefault();
    const dataSet = {
      title,
      desc,
      lastUpdated: new Date(),
      mid: mid,
      type: type,
      start,
      stop,
      time,
      comment,
      tolerance,
    };
    // db.collection(companies).doc(companyId_constant)
    // .collection(type).doc(data.id).update(dataSet).then(() => {
    //     handleClose()
    // })
  };

  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Title</InputLabel>
      <TextField
        onChange={(e) => setTitle(e.target.value)}
        value={title}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Start Value</InputLabel>
      <TextField
        onChange={(e) => setStart(e.target.value)}
        value={start}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Stop Value</InputLabel>
      <TextField
        onChange={(e) => setStop(e.target.value)}
        value={stop}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Time</InputLabel>
      <TextField
        onChange={(e) => setTime(e.target.value)}
        value={time}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Tolerance</InputLabel>
      <TextField
        onChange={(e) => setTolerance(e.target.value)}
        value={tolerance}
        type="number"
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />
      <InputLabel style={{ marginBottom: "10px" }}>Comment</InputLabel>
      <TextField
        onChange={(e) => setComment(e.target.value)}
        value={comment}
        required
        variant="outlined"
        fullWidth
        style={{ marginBottom: "12px" }}
      />

      <div className="p-2 mt-2 flex justify-between">
        <Button onClick={handleClose} variant="outlined">
          Cancel
        </Button>
        <Button type="submit" variant="outlined">
          Submit
        </Button>
      </div>
    </form>
  );
};

export default EditDocumentation;
