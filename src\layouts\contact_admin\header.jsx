import { Card, Typography } from "@mui/material";
import { Box } from "@mui/system";
import React from "react";
import { useUtils } from "../../hooks/UtilsProvider";

const CAHeaderCard = () => {
  const HEADER_TITLE = "Messages";

  const { envData } = useUtils();
  return (
    <Card
      elevation={3}
      style={{ marginBottom: "1rem" }}
      sx={{
        py: 2,
        bgcolor: envData.CARD_BG_COLOR,
      }}
    >
      <Box
        sx={{
          py: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          px: 3,
        }}
      >
        <Box>
          <Typography variant="h4">{HEADER_TITLE}</Typography>
        </Box>
      </Box>
    </Card>
  );
};

export default CAHeaderCard;
