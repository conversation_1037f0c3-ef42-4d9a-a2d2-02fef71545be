import React from "react";
import { <PERSON>, Button, Chip, IconButton } from "@mui/material";
import FolderOpenIcon from "@mui/icons-material/FolderOpen";
import DeleteIcon from "@mui/icons-material/Delete";

const SOPFolderPicker = ({ folderFiles, uploading, onChange, onReset }) => (
  <Box sx={{ mb: 3, display: "flex", alignItems: "center", gap: 2 }}>
    <Button
      variant="outlined"
      component="label"
      startIcon={<FolderOpenIcon />}
      sx={{ minWidth: 180 }}
      disabled={uploading}
    >
      Select Folder
      <input
        type="file"
        webkitdirectory="true"
        directory="true"
        multiple
        hidden
        onChange={onChange}
      />
    </Button>
    {folderFiles.length > 0 && (
      <>
        <Chip label={`Selected: ${folderFiles.length} files`} color="primary" />
        <IconButton
          onClick={onReset}
          color="error"
          title="Reset"
          disabled={uploading}
        >
          <DeleteIcon />
        </IconButton>
      </>
    )}
  </Box>
);

export default SOPFolderPicker;
