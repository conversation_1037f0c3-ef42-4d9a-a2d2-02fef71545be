.machineDataViewPage {
  color: #344767;
  width: 100%;
  // margin-bottom: 36px;
  .machineDataHeader {
    width: 100%;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    // border-radius: 10px;
    color: #344767;
    padding: 1.5rem 1.2rem;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    ul {
      display: flex;
      align-items: center;

      a {
        margin-right: 3rem;
        li {
          padding-bottom: 0.4rem;
          font-size: 1rem;
          font-weight: 500;
        }
      }
      .dataLink.active {
        border-bottom: 3px solid #2f6dda;
      }
    }
  }
}
.allMachineDataPreviewContainer {
  .liveDataOuterContainer {
    // padding: 2rem 1.5rem;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    // border-radius: 10px;
    color: #344767;
    .liveDataHeading {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: fit-content;
      // margin-bottom: 1.5rem;
      // padding: 0rem;

      .title {
        text-transform: capitalize;
        font-size: 18px;
        font-weight: 700;
        margin-left: 6px;
      }

      .btn {
        .addBtn {
          padding: 0.7rem 1.8rem;
          font-size: 0.8rem;
          font-weight: bold;
          text-transform: uppercase;
          outline: none;
          cursor: pointer;
          border-radius: 0.7rem;
          border: none;
          color: #fff;
          box-shadow:
            rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
            rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
          &:hover {
            opacity: 0.85;
          }
        }
      }
    }

    .liveDataContainer {
      // margin-top: 1rem;
      overflow: visible !important;
      .dataListWrapper {
        display: flex;
        flex-direction: column;
        .dataList {
          // margin: 1rem 0;
          .listOfData {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .data {
              font-size: 0.9rem;
              font-weight: normal;
              opacity: 0.7;
              text-align: start;
            }
          }
        }
      }

      .dataBtns {
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 1.2rem;
          opacity: 0.7;
          cursor: pointer;
          &:hover {
            opacity: 1;
          }
        }
      }
      .subData {
        width: 100%;
        padding: 1rem;
        .subDataBtns {
          display: flex;
          align-items: center;
          justify-content: center;
          .updateBtn,
          .assignBtn,
          .showBtn {
            padding: 0.7rem 1.8rem;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
            outline: none;
            cursor: pointer;
            border-radius: 8px;
            border: none;
            background-color: #e9ecef;
            color: #344767;
            box-shadow:
              rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
              rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;
            margin-left: 1rem;
            &:hover {
              opacity: 0.85;
            }
          }
        }

        .imgBlock {
          box-sizing: border-box;
          width: 100%;
          min-height: 800px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .chartDataOuterContainer {
    width: 100%;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    color: #344767;
    padding: 1.2rem 1.2rem;
    margin: 2rem 0;
    .chartContainerFirst {
      width: 100%;
      background-color: #fff;
      box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      color: #344767;
      padding: 1.2rem 1.2rem;
      margin: 0.5rem 0;
      .chartDataHeading {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title {
          font-size: 1.5rem;
          font-weight: 500;
        }
      }
      .searchBox {
        display: flex;
      }
    }
  }

  .tableC {
    border-radius: 0.75rem;
    th,
    td {
      color: #344767;
      font-size: 0.85rem;
    }
    th {
      font-size: 0.72rem;
      text-transform: uppercase;
      opacity: 0.8;
      font-weight: 700;
    }

    .userNameImgCell {
      display: flex;
      align-items: center;
      .avatar {
        margin-right: 12px;
        width: 35px;
        height: 35px;
        img {
          width: 100%;
          height: 100%;
          border-radius: 8px;
        }
      }
    }
  }
}
