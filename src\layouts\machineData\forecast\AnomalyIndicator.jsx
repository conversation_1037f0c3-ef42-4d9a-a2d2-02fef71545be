import React from "react";
import { Card, CardContent, Typography, Box } from "@mui/material";

// Function to detect anomalies based on deviation threshold
const detectAnomalies = (values, forecastValues) => {
  const anomalies = [];

  for (let i = 1; i < values.length; i++) {
    if (values[i] !== null && values[i - 1] !== null) {
      const change = Math.abs(values[i] - values[i - 1]);
      const percentageChange = (change / values[i - 1]) * 100;

      if (percentageChange > 10) {
        anomalies.push({ index: i, level: "high", value: values[i] });
      } else if (percentageChange > 5) {
        anomalies.push({ index: i, level: "moderate", value: values[i] });
      }
    }
  }

  for (let i = 0; i < forecastValues.length; i++) {
    if (forecastValues[i] !== null && values[values.length - 1] !== null) {
      const change = Math.abs(forecastValues[i] - values[values.length - 1]);
      const percentageChange = (change / values[values.length - 1]) * 100;

      if (percentageChange > 10) {
        anomalies.push({
          index: i + values.length,
          level: "high",
          value: forecastValues[i],
        });
      } else if (percentageChange > 5) {
        anomalies.push({
          index: i + values.length,
          level: "moderate",
          value: forecastValues[i],
        });
      }
    }
  }

  return anomalies;
};

const AnomalyIndicator = ({ values, forecastValues }) => {
  const anomalies = detectAnomalies(values, forecastValues);

  let color = "green";
  let message = "No anomalies detected";

  if (anomalies.some((a) => a.level === "high")) {
    color = "red";
    message = "⚠️ High anomalies detected!";
  } else if (anomalies.some((a) => a.level === "moderate")) {
    color = "orange";
    message = "⚠️ Moderate anomalies detected.";
  }

  return (
    <Card
      sx={{
        p: 2,
        mt: 4,
        height: "150px",
        textAlign: "center",
        borderLeft: `5px solid ${color}`,
      }}
    >
      <CardContent>
        <Typography variant="h6">Anomaly Detection</Typography>
        <Typography variant="body1" sx={{ color }}>
          {message}
        </Typography>

        {/* Show Anomaly Details */}
        {anomalies.length > 0 && (
          <Box sx={{ mt: 1 }}>
            {anomalies.map((anomaly, index) => (
              <Typography key={index} variant="body2">
                📍 Anomaly at index {anomaly.index} → Value: {anomaly.value}
              </Typography>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default AnomalyIndicator;
