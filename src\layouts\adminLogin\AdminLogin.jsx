// Lsi admin login
import React, { useEffect, useState } from "react";
import "./adminLogin.scss";
import { alpha, styled } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import InputLabel from "@mui/material/InputLabel";
import { Link } from "react-router-dom";
import { useNavigate, Redirect } from "react-router-dom";
import { db } from "../../firebase";
import { alluser } from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";
import { toastMessage } from "../../tools/toast";
import { useAuth } from "../../hooks/AuthProvider";
import UserSessionLog from "../../components/CFR-Report/UserSessionLog";
import SecurityLoggingFunction from "../../components/CFR-Report/SecurityLog";
import TextField from "@mui/material/TextField";
import { useStateContext } from "../../context/ContextProvider";
import logo from "../../assets/images/logo.png";

const AdminLogin = () => {
  const history = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [lsiUserAll, setLsiUserAll] = useState([]);
  const { login, setAdminType } = useAuth();
  const { currentColorLight, currentMode } = useStateContext();

  useEffect(() => {
    // db.collection(alluser).where("lsi" ,'==', true)
    //   .onSnapshot(snap => {
    //     const data = firebaseLooper(snap);
    //     setLsiUserAll(data);
    //   })
  }, []);

  const handleLogin = async (e) => {
    e.preventDefault();
    let activate = false;
    for (let index = 0; index < lsiUserAll.length; index++) {
      const element = lsiUserAll[index];

      if (
        email === element.email &&
        (element.role === "lsi_admin" || element.role === "admin")
      ) {
        // "lsi_admin or admin" important parameter
        setAdminType("lsi_admin"); //this will loose when reloading happens
        window.localStorage.setItem("adminType", "lsi_admin"); //to save when reloading happens
        activate = true;
        break;
      }
    }
    if (activate == false) {
      return toastMessage({
        message:
          "Your email is not registered ! Please use a valid email address ",
      });
    }
    try {
      await login(email, password);
      window.localStorage.removeItem("companyId");
      history.replace("/companies");
      window.location.reload(false);
      UserSessionLog("Logged In", email.toLowerCase());
    } catch {
      SecurityLoggingFunction(email, "User Entered Incorrect Password");
      toastMessage({ message: "Failed to Login. Incorrect password ! " });
    }
    //history('/companies')
  };

  return (
    <section className="adminLoginPage">
      {/* <div className="logoContainer">
        <img
          src="https://cdn-images-1.medium.com/max/1200/1*w08ygePqBu1otQVZ60bPMg.png"
          alt=""
        />
      </div> */}
      <div className="illustrationContainer">
        <img
          src={logo} //"https://firebasestorage.googleapis.com/v0/b/lyodatatest.appspot.com/o/arizon%2Fbackground2.webp?alt=media&token=03b8f64a-dea9-4d62-b2bb-706e33d55971"
          alt=""
        />
      </div>
      <div
        className="formContainer"
        style={
          currentMode === "Dark"
            ? { backgroundColor: "#161C24", color: "white" }
            : { backgroundColor: currentColorLight }
        }
      >
        <div className="formTitle">LSI Admin</div>
        <div className="formDesc">
          Welcome back! Please login to your account
        </div>
        <form className="form">
          <div className="labelFields">
            <TextField
              label="Email Address"
              placeholder="eg. <EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              fullWidth
              size="small"
            />
          </div>
          <div className="labelFields">
            <TextField
              label="Password"
              type={showPassword ? "text" : "password"}
              placeholder="eg. password123"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              fullWidth
              size="small"
            />
            <div className="flex mt-1">
              <div>
                <input
                  type="checkbox"
                  value={showPassword}
                  onChange={(e) => setShowPassword(e.target.checked)}
                />
              </div>
              <div className="ml-2 text-xs font-bold self-center">
                {" "}
                Show Password{" "}
              </div>
            </div>
          </div>
          <div className="linkContainer">
            <Link to="/admin-forgot-password" className="link">
              Forgot Password?
            </Link>
          </div>
          <button onClick={(e) => handleLogin(e)}>Login</button>
        </form>
      </div>
    </section>
  );
};

export default AdminLogin;
