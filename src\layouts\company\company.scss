.companiesPage {
  //padding: 1.2rem;

  .companyPageInfoContainer {
    width: 100%;
    //background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    //color: #344767;
    padding: 1.5rem 1.2rem;
    // margin: 1rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .info {
      display: flex;
      flex-direction: column;

      h3 {
        font-size: 1.4rem;
        font-weight: 500;
        //margin-bottom: 0.7rem;
      }

      p {
        font-size: 0.9rem;
        opacity: 0.9;
      }
    }

    .btn {
      .addCompanyBtn {
        padding: 0.7rem 1.8rem;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
        outline: none;
        cursor: pointer;
        border-radius: 8px;
        border: none;
        background-color: rgba(20, 20, 94, 0.84);

        color: #fff;
        box-shadow:
          rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
          rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;

        &:hover {
          opacity: 0.9;
        }
      }
    }
  }

  .companiesContainerSection {
    width: 100%;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    color: #344767;
    padding: 0.2rem;
    padding-top: 1.2rem;
    margin: 1.5rem 0;

    .companiesHeading {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 1.2rem;
      padding-bottom: 1.2rem;

      .info {
        margin-bottom: 0.4rem;

        h3 {
          font-size: 1.2rem;
          font-weight: 500;
          opacity: 0.9;
        }

        p {
          margin-top: 0.5rem;
          font-size: 0.9rem;
          opacity: 0.9;
        }
      }

      .headingRightContainer {
        //background-color: rgb(135, 228, 228);
        display: flex;
      }
    }

    .cardsContainer {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;

      //background-color: aqua;
      // max-height: 91vh;
      //padding: 1rem;
      @media (max-width: 500px) {
        padding: 0.5rem 0;
      }

      .card {
        margin: 0.5rem;
        padding: 0.5rem;
        width: 350px;
        transition: transform 0.3s ease;
        border-radius: 10px;

        @media (max-width: 500px) {
          width: 280px;
          margin: 0.2rem;
        }

        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        .headerContainer {
          height: 50%;
          width: 100%;
          display: flex;
          // justify-content: center;
          // justify-items: center;
          // align-self: center;
          flex-direction: column;

          //align-items: center;
          .logo {
            display: flex;
            align-self: center;
            width: 150px;
            height: 150px;

            img {
              border-radius: 8px;
              width: 100%;
              height: 100%;
            }
          }

          .details {
            align-self: center;
            // width:200px;
            padding: 2px;
            margin-left: 2px;
            border-radius: 5px;

            // .companyName {
            //   font-size: 1rem;
            //   font-weight: bold;
            // }
            .companyColorTheme {
              //margin-top: 1rem;
              // display:flex;
              // align-self: center;
              width: 1rem;
              height: 1rem;
              border-radius: 10px;
              background: orange;
            }
          }
        }

        .secondContainer {
          // margin-top: 1rem;
          height: 50%;
          width: 100%;
          display: flex;
          // justify-content: space-between;
          // align-items: center;

          .secondHeading {
            font-size: 0.9rem;
            font-weight: 500;
            margin-right: 2rem;
          }

          .secondName {
            font-size: 0.85rem;
            font-weight: 500;
            opacity: 0.9;
          }
        }
      }
    }
  }
}
