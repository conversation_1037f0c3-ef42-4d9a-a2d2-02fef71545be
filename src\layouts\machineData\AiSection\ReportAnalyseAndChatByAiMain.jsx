/** 📓
 * Text chat:- 1. Once project is created we are not updating the file for now.
 *             2. We will create more api to update the PDF files if needed.
 *             3. We are fetching particular project based on "name" field, it need to be unique or "_id" need to
 *                be custom one for mongoDb.
 * Image chat:- We are storing image URL from openAi which is valid for 1 hr only.
 * */
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  List,
  ListItem,
  ListItemText,
  TextField,
  InputAdornment,
  Tooltip,
} from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import SendIcon from "@mui/icons-material/Send";
import { PDFDocument } from "pdf-lib";
import { Divider } from "antd";
import { v4 as uuidV4 } from "uuid";
import ImageIcon from "@mui/icons-material/Image";
import ImageDisplay from "./ImageDisplay";
import { toastMessageSuccess, toastMessageWarning } from "../../../tools/toast";
import { ToastContainer } from "react-toastify";
import VideoDisplay from "./VideoDisplay";
import OndemandVideoIcon from "@mui/icons-material/OndemandVideo";
import ChatBoxSection from "./ChatBoxSection";

const ReportAnalyseAndChatByAiMain = ({
  handleClose,
  data,
  userName,
  reportName,
  machineName,
  reportType,
}) => {
  const [reportFile, setReportFile] = useState({});
  const [sopFile, setSopFile] = useState({});
  const [sopExist, setSopExist] = useState(false);
  const [projectData, setProjectData] = useState([]);
  const [id, setId] = useState("");
  const [chatHistory, setChatHistory] = useState([]);
  const [query, setQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [loadingForStart, setLoadingForStart] = useState(false);
  const [imageMode, setImageMode] = useState(false);
  const [videoMode, setVideoMode] = useState(false);
  const divRef = useRef(null);

  // console.log("AI component props data:", data)

  const scrollToBottom = () => {
    divRef.current.scrollIntoView({ block: "end", behavior: "smooth" });
  };

  const handleReport = (e) => {
    let report = e.target.files[0];
    setReportFile(report);
    console.log("report file:", report);
  };

  const handleSop = async (e) => {
    let sop = e.target.files[0];
    setSopFile(sop);
    setSopExist(false);
  };

  // UTILs:- blob to file
  function blobToFile(blob, fileName) {
    return new Promise((resolve, reject) => {
      try {
        const file = new File([blob], fileName, { type: blob.type });
        resolve(file);
      } catch (error) {
        reject(error);
      }
    });
  }
  //

  // UTILs:- handle SOP from sop_url
  const downloadPDF = async () => {
    const sopUrl = `${dbConfig.url_python}/files/${data?.sop_url}`;

    try {
      const response = await axios({
        method: "GET",
        url: sopUrl,
        responseType: "blob",
      });
      const blob = new Blob([response.data], { type: "application/pdf" });
      const file = await blobToFile(blob, "SOP.pdf");
      setSopFile(file);
      setSopExist(true);
      console.log("Sop file from url:", file);
      //saveAs(blob, 'downloaded-file.pdf');
    } catch (error) {
      //setDownloadStatus('Download failed');
      console.error("Error downloading PDF:", error);
    }
  };

  // Create project
  const addProject = async (uploadedFiles) => {
    console.log("Uploaded files props:", uploadedFiles);
    if (!data?._id || !data?.title || !uploadedFiles) {
      console.log("Id not found. Please try again.");
      return;
    }

    try {
      // Use the filename field in the project data
      const requestData = {
        name: data?._id,
        description: `${data?._id} : ${data?.title}`,
        filenames: [uploadedFiles?.filename], // Use filename field
      };

      // Perform API request to create a new project
      const response = await axios.get(`${dbConfig.url_python}/projects`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        // If successful, fetch updated projects data
        ///fetchProjects();
      } else {
        // Handle error response
        const errorText = await response.text(); // Get the error text
        console.error("Error adding project:", errorText);
        alert("Error adding project. Please try again.");
      }
    } catch (error) {
      console.error("Error adding project:", error);
      alert("Error adding project. Please try again.");
    }
  };

  // Merge and upload PDFs
  const mergeAndUploadPdfs = async (pdf1Bytes, pdf2Bytes) => {
    // Merge PDFs
    const pdfDoc = await PDFDocument.create();
    const pdf1 = await PDFDocument.load(pdf1Bytes);
    const pdf2 = await PDFDocument.load(pdf2Bytes);
    const copiedPages1 = await pdfDoc.copyPages(pdf1, pdf1.getPageIndices());
    const copiedPages2 = await pdfDoc.copyPages(pdf2, pdf2.getPageIndices());
    copiedPages1.forEach((page) => pdfDoc.addPage(page));
    copiedPages2.forEach((page) => pdfDoc.addPage(page));
    const mergedPdfBytes = await pdfDoc.save();
    const mergedPdfBlob = new Blob([mergedPdfBytes], {
      type: "application/pdf",
    });
    // Create FormData
    const formData = new FormData();
    const fileName = data?._id; // maintenance report id
    formData.append("file", mergedPdfBlob, fileName + ".pdf");
    return await axios.post(`${dbConfig.url_python}/upload`, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  };

  /** Prepare the chat. This is the first main function where we setup the files and project in backend
   * 1. Merge and upload PDFs. (it returns filename)
   * 2. Add project
   */
  const startChat = async () => {
    if (
      reportFile?.type !== "application/pdf" ||
      sopFile?.type !== "application/pdf" ||
      loadingForStart
    ) {
      console.log("Please upload both files to start the chat.");
      toastMessageWarning({
        message: "Please upload both files to start the chat.",
      });
      return;
    }

    setLoadingForStart(true);

    try {
      const pdf1Bytes = await reportFile?.arrayBuffer();
      const pdf2Bytes = await sopFile?.arrayBuffer();
      // Merge and upload PDFs
      const uploadedPdfFile = await mergeAndUploadPdfs(pdf1Bytes, pdf2Bytes);
      // Add project
      await addProject(uploadedPdfFile?.data);
      await fetchProjectByName(data?._id);
      toastMessageSuccess({
        message: "Now ready to chat. Please type your query.",
      });
    } catch (error) {
      console.error("Upload failed:", error);
    } finally {
      setLoadingForStart(false);
    }
  };

  // Fetch project by name i.e data?._id
  const fetchProjectByName = async (projectName) => {
    try {
      const response = await axios.get(
        `${dbConfig.url_python}/project/${projectName}`,
      );
      if (response.statusText) {
        /** response is comming as project : [{project}] */
        const projectData = await response?.data;
        setProjectData(projectData?.project);
        if (projectData?.project?.length) {
          console.log("Project data:", projectData?.project[0]?._id?.$oid);
          setId(projectData?.project[0]?._id?.$oid);
          setChatHistory(projectData?.project[0]?.chat_history || []);
        }
        console.log("Project data2:", projectData);
        return projectData;
      } else {
        console.error("Error fetching project:", response.statusText);
        return null;
      }
    } catch (error) {
      console.error("Error fetching project:", error);
      return null;
    }
  };

  useEffect(() => {
    if (data?._id) {
      fetchProjectByName(data?._id);
    }

    downloadPDF();
  }, []);

  const processedChatHistory = useMemo(() => {
    return chatHistory.map((chat) => ({ ...chat, key: uuidV4() }));
  }, [chatHistory]);

  return (
    <div
      id="divRef"
      ref={divRef}
      style={{ maxHeight: "800", overflowY: "auto" }}
    >
      <form>
        {projectData?.length > 0 && (
          <div className="flex justify-center items-center max-w-2xl mx-auto border rounded-lg shadow-lg bg-gradient-to-r from-orange-400 via-pink-500 to-purple-400 p-6 border-b-4 border-purple-500 animate-gradient">
            <h1 className="text-3xl lg:text-4xl text-white font-bold">
              {" "}
              I am ready, please ask!{" "}
            </h1>
          </div>
        )}

        {projectData?.length < 1 && (
          <div className="flex justify-between max-w-2xl mx-auto p-4 border rounded-lg shadow-lg bg-white">
            <div>
              <label
                className="block mb-2 text-sm font-medium text-gray-700"
                htmlFor="file_input"
              >
                Choose SOP
                {sopExist && (
                  <span className="text-md text-green-500"> (Found a SOP)</span>
                )}
              </label>
              <input
                type="file"
                id="file_input"
                accept="application/pdf"
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                onChange={handleSop}
              />
            </div>

            <div>
              <label
                className="block mb-2 text-sm font-medium text-gray-700"
                htmlFor="file_input"
              >
                Choose Report{" "}
              </label>
              <input
                type="file"
                id="file_input"
                accept="application/pdf"
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                onChange={handleReport}
              />
            </div>

            <div className="bg-slate-400 self-center">
              <Button
                color="success"
                onClick={startChat}
                variant="contained"
                disabled={projectData?.length > 0} // || !(projectData?.length === 0 && reportFile && sopFile)
              >
                {loadingForStart && (
                  <CircularProgress size="1rem" color="inherit" />
                )}
                {!loadingForStart && <>Start</>}
              </Button>
            </div>
          </div>
        )}

        <br />
        <hr />

        {/** Chat Section */}
        <div>
          <List>
            {processedChatHistory?.map((chat, index) => (
              <React.Fragment key={uuidV4()}>
                {chat?.image && (
                  <>
                    <ListItem>
                      <ListItemText
                        primary={
                          <>
                            <b>USER : </b> {chat.user}
                          </>
                        }
                        secondary={`AR COPILOT: `}
                      />
                    </ListItem>
                    <ImageDisplay
                      imageUrl={chat.bot}
                      title="Image Title"
                      description="This is a brief description of the image."
                    />
                  </>
                )}

                {chat?.video && (
                  <>
                    <ListItem>
                      <ListItemText
                        primary={
                          <>
                            <b>USER : </b> {chat.user}
                          </>
                        }
                        secondary={`AR COPILOT: `}
                      />
                    </ListItem>
                    <VideoDisplay
                      videoUrl={chat.bot}
                      title="Image Title"
                      description="This is a brief description of the image."
                    />
                  </>
                )}

                {/* http://localhost:3009/1709652205322_01.Security%20Interlocks.mp4 */}

                {!chat?.image && !chat?.video && (
                  <ListItem>
                    <ListItemText
                      primary={
                        <>
                          <b>USER : </b> {chat.user}
                        </>
                      }
                      secondary={`AR COPILOT: ${chat.bot}`}
                    />
                  </ListItem>
                )}
                {index < chatHistory.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>

          <ChatBoxSection
            setChatHistory={setChatHistory}
            chatHistory={chatHistory}
            data={data}
            id={id}
            scrollToBottom={scrollToBottom}
            projectData={projectData}
          />

          <div className="p-2 mt-2 flex justify-between">
            <Button color="error" onClick={handleClose} variant="contained">
              Close
            </Button>
            {/* <Button color="error" type="submit" variant="contained">
                    Submit
                </Button> */}
          </div>
        </div>
      </form>
    </div>
  );
};

export default ReportAnalyseAndChatByAiMain;
