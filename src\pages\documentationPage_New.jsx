import React from "react";
import { useParams } from "react-router-dom";
import Content from "../layouts/machineData/Content";

const DocumentationPage_FAT = () => {
  const { mid, docId } = useParams();

  return (
    <section className="documentationPage">
      <Content type="fatData" docId={docId} mid={mid} />
    </section>
  );
};

const DocumentationPage_SAT = () => {
  const { mid, docId } = useParams();
  return (
    <section>
      <Content type="satData" docId={docId} mid={mid} />
    </section>
  );
};

export { DocumentationPage_FAT, DocumentationPage_SAT };
