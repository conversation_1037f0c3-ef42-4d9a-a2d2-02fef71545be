import {Dialog, DialogTitle, DialogContent, DialogActions} from "@mui/material";
import Delete from "../../../components/Delete/Delete";
import {useAlarmSopFormData} from "../../../hooks/alarmSops/useAlarmSopFormData";
import AddMaintenance from "../AddMaintenance";
import EditMaintenance from "../EditMaintenance/EditMaintenance";

const HandleAlarmSop = ({
  useAt = "Add",
  showDialog = false,
  machineId,
  handleClose,
  handleSubmitSuccess,
  handleDelete,
  alarmSOPData = {},
}) => {
  const {alarmSopFormData} = useAlarmSopFormData({
    initialAlarmSOPData: alarmSOPData,
  });
  return (
    <Dialog open={showDialog} onClose={handleClose}>
      {useAt !== "Delete" ? (
        <>
          <DialogTitle>
            {`${useAt !== "Add" ? "Update" : "Add"} Alarm SOP`}
          </DialogTitle>
          <DialogContent>
            {useAt === "Add" && (
              <AddMaintenance
                mid={machineId}
                formData={alarmSopFormData}
                handleClose={handleClose}
                handleSubmitSuccess={handleSubmitSuccess}
                useAt={"AlarmSop"}
              />
            )}
            {useAt === "Update" && (
              <EditMaintenance
                mid={machineId}
                data={alarmSopFormData}
                handleClose={handleClose}
                useAt={"AlarmSop"}
                handleSubmitSuccess={handleSubmitSuccess}
              />
            )}
          </DialogContent>
        </>
      ) : (
        <Delete onClose={handleClose} onDelete={handleDelete} />
      )}
    </Dialog>
  );
};

export default HandleAlarmSop;
