/* eslint-disable jsx-a11y/img-redundant-alt */
import React, { useContext, useEffect, useState } from "react";
import { ThemeContext } from "../../context/ThemeContext";
import "./Sidebar.scss";
import { MenuData } from "./MenuData";
import { NavLink, useNavigate } from "react-router-dom";
import logo from "../../assets/images/logo.png";
import { Box, Divider, Grid, Paper } from "@mui/material";
import { useAuth } from "../../hooks/AuthProvider";
import {
  adminType_constant_temp,
  companies,
  companyId_constant,
} from "../../constants/data";
import { useStateContext } from "../../context/ContextProvider";
import { makeStyles } from "@mui/styles";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useLogo } from "../../context/LogoContext";
/**
 * @type {'1' | '2' | '3'}
 */
export const S_TYPE = "3";

const useStyles = makeStyles((theme) => ({
  activeLink: {
    color:
      S_TYPE === "1" && theme.palette.mode !== "dark"
        ? theme.palette.primary.contrastText
        : theme.palette.primary.main,
    ...(S_TYPE === "3" && {
      backgroundColor: "#fff",
    }),
    boxShadow: "0px 0px 24px 2px rgba(0, 0, 0, 0.5) inset",
    display: "flex",
    textDecoration: "none",
    borderTopRightRadius: "8px",
    borderBottomRightRadius: "8px",
  },
  gridContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    // paddingBlock: "1rem",
    height: "5rem",
  },
  imageContainer: {
    maxHeight: "100%",
    maxWidth: "100%",
    objectFit: "contain",
    borderRadius: "5px",
  },
  gridItemCenter: {
    alignSelf: "center",
    paddingLeft: "1rem",
    textAlign: "center",
  },
  divider: {
    marginBottom: "0.5rem",
    marginTop: "0.5rem",
  },
  homeContainer: {
    display: "flex",
    flexDirection: "row",
    paddingBlock: "1rem",
  },
  homeGrid: {
    display: "grid",
    placeItems: "center",
  },
  textLeft: {
    textAlign: "left",
    alignSelf: "center",
    paddingLeft: "1rem",
  },
  listHeading: {
    padding: "0.4rem 0.5rem",
    fontSize: "large",
    fontWeight: "500",
  },
  linkContainer: {
    display: "flex",
    flexDirection: "row",
    paddingBlock: ".8rem",
  },
  iconContainer: {
    display: "grid",
    placeItems: "center",
    padding: "0.4rem 0.5rem",
  },
  sideBarContainers: {
    color: theme.palette.custom.textColor,
  },
}));

const Sidebar = ({ inactive, state }) => {
  const theme = useContext(ThemeContext);
  const darkMode = theme?.state?.darkMode || false; // Fallback to false if undefined
  const { currentUser, adminType } = useAuth();
  const [homeButton, setHomeButton] = useState(false);
  const [companyData, setCompanyData] = useState([]);
  const history = useNavigate();
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const { logoImageData } = useLogo();
  console.log("Logo Image Data:", logoImageData);
  const classes = useStyles();

  // const fetchLogoAsset = async () => {
  //   try {
  //     const res = await axios.get(`${dbConfig.url}/modules`);
  //     const data = res?.data;
  //     const logo = data?.filter((item) => item?.moduleName === "logo")?.imageUrl;
  //     setLogoImage(logo || "");
  //     console.log("Logo URL:", logo);
  //   } catch (error) {
  //     console.error("Error fetching logo:", error);
  //   }
  // };

  useEffect(() => {
    // fetchLogoAsset();
    const adminTypeTemp = adminType || adminType_constant_temp;
    setHomeButton(adminTypeTemp === "lsi_admin");
    console.log("Sidebar: adminType =", adminTypeTemp);
  }, [adminType]);

  const handleHomeButton = () => {
    window.localStorage.setItem("companyId", undefined);
    history("/");
    window.location.reload(false);
  };

  const sidebarState = state || {};

  return (
    <>
      <Paper
        className={classes.sideBarContainers}
        sx={{
          p: 2,
          px: 0,
          ...((S_TYPE === "1" || S_TYPE === "3") && {
            backgroundColor: currentMode === "Dark" ? "#1e1e1e" : currentColor,
            color: "#fff",
          }),
          ...(S_TYPE === "2" && {
            color: currentMode === "Dark" ? "#fff" : currentColor,
          }),
        }}
      >
        <Grid container className={classes.gridContainer}>
          <Grid
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
            xs={inactive ? 12 : 6}
            item
          >
            <img
              className={classes.imageContainer}
              src={logoImageData?.image_url}
              style={{
                maxHeight: "60px",
                maxWidth: `${inactive ? "180px" : "240px"}`,
              }}
              alt="no image found"
            />
          </Grid>
        </Grid>
        {MenuData.map((item, index) => (
          <Box>
            <Divider className={classes.divider} />
            {inactive ? (
              <></>
            ) : (
              <Grid xs={12} className={classes.listHeading}>
                {item.listHeading}
              </Grid>
            )}
            {item.link.map((links, index) => (
              <NavLink
                // activeClassName={`${classes.activeLink}`}
                className={({ isActive }) =>
                  `${isActive ? classes.activeLink : " text-inherit"}`
                }
                style={{
                  textDecoration: "none",
                }}
                to={links.path}
                exact
              >
                <Grid
                  container
                  className={`${classes.linkContainer} hover:text-xl transition-all duration-500 hover:text-[lightslategray]`}
                >
                  <Grid
                    xs={inactive ? 12 : 3}
                    item
                    className={classes.iconContainer}
                  >
                    {links.icon}
                  </Grid>
                  {inactive ? (
                    <></>
                  ) : (
                    <Grid xs={9} item className={classes.textLeft}>
                      {links.text}
                    </Grid>
                  )}
                </Grid>
              </NavLink>
            ))}
          </Box>
        ))}
      </Paper>
    </>
  );
};

export default Sidebar;
