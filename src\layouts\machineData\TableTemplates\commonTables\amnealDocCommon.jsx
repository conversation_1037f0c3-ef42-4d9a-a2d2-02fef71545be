import React from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "../TableTempletes.css";
import { useStateContext } from "../../../../context/ContextProvider";

export default function AmnealDocCommon() {
  const { currentMode, currentColorLight } = useStateContext();

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#161c24" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };
  return (
    <TableContainer component={Paper}>
      <Table
        sx={{ minWidth: 650, border: theme.borderDesign }}
        aria-label="simple table"
        size="small"
      >
        <TableHead
          sx={{
            border: theme.borderDesign,
            background: theme.backgroundColor,
          }}
        >
          <TableRow
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            <TableCell sx={{ border: theme.borderDesign }}>
              DD ENTERPRISES
            </TableCell>
            <TableCell sx={{ border: theme.borderDesign }}>
              AMNEAL ONCOLOGY PVT LTD
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell sx={{ border: theme.borderDesign }}>
              Checked by (Sign/Date)
            </TableCell>
            <TableCell sx={{ border: theme.borderDesign }}>
              Verified By (Sign/Date)
            </TableCell>
          </TableRow>
        </TableHead>

        <TableBody>
          <TableRow sx={{ height: "100px" }}>
            <TableCell sx={{ border: theme.borderDesign }}></TableCell>
            <TableCell sx={{ border: theme.borderDesign }}></TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );
}
