import { Box } from "@mui/material";
import React from "react";
import AddCmsInfrastructure from "../crud/add-forms/add-cms-infra.form";
import AddSapMaster from "../crud/add-forms/add-cms-master.form";
import AddSapIntimation from "../crud/add-forms/add-sap-intimation.form";
import CalibrationDataForm from "../forms/calibration-data.form";
import SapMasterForm from "../forms/cms-master.form";
import EquipmentUsedTable from "../forms/equipment-used.table";
import OperatingRangesForm from "../forms/operating-ranges.form";
import SapIntimationForm from "../forms/sap-intimation.form";
import SetPointsForm from "../forms/set-points.form";
import MasterEquipmentScreen from "./master-equipment.screen";

const RawDataScreen = () => {
  return (
    <div>
      <Box sx={{ p: 4 }}>
        <AddCmsInfrastructure />

        {/* <AddSapMaster/> */}
        {/* <SapMasterForm/>
<CalibrationDataForm/> */}
        <br />
        <hr />
        <br />
        {/* <AddSapIntimation/> */}
        {/* <SapIntimationForm/> */}
        <MasterEquipmentScreen />
      </Box>
    </div>
  );
};

export default RawDataScreen;
