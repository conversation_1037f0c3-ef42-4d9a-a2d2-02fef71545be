import React, { useState } from "react";
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import { toastMessage, toastMessageSuccess } from "../../../tools/toast";
import { useMongoRefresh } from "../../../services/mongo-refresh.context";

const EditChangeOverReport = ({
  handleClose,
  data,
  userName,
  reportName,
  machineName,
  reportType,
  refreshChangeOverReports,
}) => {
  const [localData, setLocalData] = useState(data);
  const [remarks, setRemarks] = useState(data.remarks || "");
  const [status, setStatus] = useState(data.report_status);
  const [remarksError, setRemarksError] = useState("");
  const { currentMode } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle remarks change and prevent leading space
  const handleRemarksChange = (e) => {
    const value = e.target.value;
    if (value.length === 1 && value === " ") {
      return;
    }
    const trimmedValue = value.replace(/^\s+/, "");
    setRemarks(trimmedValue);
    setRemarksError("");
  };

  const handleRemarksBlur = () => {
    const trimmedRemarks = remarks.trim();
    setRemarks(trimmedRemarks);
    if (!trimmedRemarks) {
      setRemarksError("Remarks cannot be empty or just spaces");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate remarks
    const trimmedRemarks = remarks.trim();
    if (!trimmedRemarks) {
      setRemarksError("Remarks cannot be empty or just spaces");
      toastMessage({ message: "Please enter valid remarks" });
      setIsSubmitting(false);
      return;
    }

    // Prevent update if trying to set Review Done when report_status is 0
    if (status === 2 && localData.report_status === 0) {
      toastMessage({ message: "First review all steps" });
      setIsSubmitting(false);
      return;
    }

    // Prevent update if report_status is 0 (legacy check)
    if (localData.report_status === 0) {
      toastMessage({ message: "First review all steps" });
      setIsSubmitting(false);
      return;
    }

    // Create updated data object
    const updatedData = {
      ...localData,
      report_status: status,
      remarks: trimmedRemarks,
      date: new Date(localData.date),
      last_updated: new Date().toISOString(),
      updated_by: userName,
    };

    try {
      const response = await axios.put(
        `${dbConfig.url}/changeOverReport/${localData._id}`,
        updatedData,
      );

      if (response.status === 200) {
        setRefreshCount(refreshCount + 1);
        toastMessageSuccess({ message: "Report Updated Successfully" });
        refreshChangeOverReports();
        setLocalData(updatedData);
        setRemarks(updatedData.remarks);
        setStatus(updatedData.report_status);
        handleClose();
      }
    } catch (error) {
      console.error("Update failed:", error);
      toastMessage({
        message: `Failed to update report: ${error.response?.data?.message || error.message}`,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Remarks</InputLabel>
      <TextField
        onChange={handleRemarksChange}
        onBlur={handleRemarksBlur}
        value={remarks}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{ marginBottom: "12px" }}
        error={!!remarksError}
        helperText={remarksError}
      />

      <InputLabel style={{ marginBottom: "10px" }}>Select Status</InputLabel>
      <FormControl
        style={{ marginBottom: "10px" }}
        required
        variant="outlined"
        fullWidth
      >
        <Select
          required
          value={status}
          onChange={(e) => setStatus(Number(e.target.value))}
        >
          <MenuItem
            value={2}
            disabled={localData.report_status === 0}
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#212B36", color: "white" }
                : {}
            }
          >
            Review Done
          </MenuItem>
          <MenuItem
            value={1}
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#212B36", color: "white" }
                : {}
            }
          >
            Pending
          </MenuItem>
        </Select>
      </FormControl>

      <div className="p-2 mt-2 flex justify-between">
        <Button color="error" onClick={handleClose} variant="contained">
          Cancel
        </Button>
        <Button
          color="success"
          type="submit"
          variant="contained"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Submitting..." : "Submit"}
        </Button>
      </div>
    </form>
  );
};

export default EditChangeOverReport;
