import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from "@mui/material";
import { ButtonBasicCancel } from "../../components/buttons/Buttons";

const ReportDialog = ({ open, onClose, onConfirm, title }) => {
  return (
    <Dialog open={open}>
      <DialogTitle>
        <b>Generate Report</b>
      </DialogTitle>
      <DialogContent>
        Are you sure you want to generate a report for <b>{title}</b>?
      </DialogContent>
      <DialogActions>
        <ButtonBasicCancel
          buttonTitle="Cancel"
          type="button"
          onClick={onClose}
        />
        <Button variant="contained" onClick={onConfirm}>
          Confirm
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ReportDialog;
