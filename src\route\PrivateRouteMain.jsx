import React from "react";
// import NewNode from '../components/New-Node';
import Layout from "../Layout";
import AddDetailsDocumentation from "../layouts/machineData/ContentMaster/AddDetailsDocumentation";
import MaintenanceReportData from "../layouts/machineData/MaintenanceReportData";
import TrainingReportData from "../layouts/machineData/TrainingReportData";
import Account from "../pages/account";
import AddMachine from "../pages/addMachine";
import CalendarPage from "../pages/CalendarPage";
import CFRPage from "../pages/cfrPage";
import DashboardPage from "../pages/dashboardPage";
import FATDocumentationPage from "../pages/FATDocumentationPage";
import SATDocumentationPage from "../pages/SATDocumentationPage";
import {
  DocumentationPage_FAT,
  DocumentationPage_SAT,
} from "../pages/documentationPage_New";
import FatReportPage from "../pages/FatReportPage";
import FileManagerPage from "../pages/fileManagerPage";
import MachineDataPage from "../pages/machineDataPage";
import Machines from "../pages/machines";
import MaintenancePage from "../pages/MaintenancePage";
import PreviewReportsPage from "../pages/PreviewReports";
import SettingsPage from "../pages/settingsPage";
import User from "../pages/user";
import UserManualPage from "../pages/userManualPage";
import videoCallPage from "../pages/videoCallPage";
import VideoPage from "../pages/VideoPage";
//import PrivateRoute from './PrivateRoute';
import TrainingPage from "../pages/TrainingPage";
import AddUser from "../pages/addUser";
import PrivateRoute from "./PrivateRoute";
import PrintReportsPage from "../pages/PrintReports";
import AlarmPage from "../pages/AlarmPage";
import HashTag from "../pages/hashTag";
import alarmManagerPage from "../pages/alarmManagerPage";
import Tasks from "../layouts/Tasks/Tasks";
import LiveDataAnnotation from "../pages/LiveDataAnnotation";
import LiveDataProject from "../pages/LiveDataProject";
import BatchPage from "../pages/batchPage";
import CmsPage from "../pages/cmsPage";
import { Route } from "react-router-dom";
import ModelPage from "../components/LiveData/ModelPage";
import ModelView from "../components/LiveData/ModelView";
import AlarmNewPage from "../pages/AlarmNewPage";
import MaintenancePageCalibration from "../pages/MaintenancePageCalibration";
import ChangeOver from "../pages/ChangeOver";
import TimelineComponent from "../layouts/machines/TimelineView";
import ARview from "../components/arview/arview";
import Arviews from "../components/arview/arview-list";
import VoicePage from "../pages/voice";
import MachineAnalysisPage from "../pages/MachineAnalysis";
import Gemba from "../layouts/machineData/gemba";
import LineClearance from "../layouts/machineData/lineClearance";
import DPRPage from "../pages/DPR";
import UtilsPage from "../pages/utilsPage";

export default function PrivateRouteMain() {
  return (
    <>
      <PrivateRoute
        exact
        path="/fat-reports-print/:reportId"
        component={PrintReportsPage}
      />
      <PrivateRoute exact path="/" component={DashboardPage} />
      <PrivateRoute exact path="/dashboard" component={DashboardPage} />
      <PrivateRoute path="/account" exact component={Account} />
      <PrivateRoute exact path="/calendar" component={CalendarPage} />
      <PrivateRoute path="/file-manager" exact component={FileManagerPage} />
      <PrivateRoute
        exact
        path="/file-manager/folder/:folderId"
        component={FileManagerPage}
      />
      <PrivateRoute exact path="/machines" component={Machines} />
      <PrivateRoute
        exact
        path="/maintenance/:mid"
        component={MaintenancePage}
      />
      <PrivateRoute
        exact
        path="/forecast/:mid"
        component={MachineAnalysisPage}
      />
      <PrivateRoute exact path="/OEE/:mid" component={TimelineComponent} />
      <PrivateRoute exact path="/FAT/:mid" component={FATDocumentationPage} />
      <PrivateRoute exact path="/SAT/:mid" component={SATDocumentationPage} />
      <PrivateRoute exact path="/liveData/:mid" component={MachineDataPage} />
      {/*FAT Report Page */}
      <PrivateRoute exact path="/reports" component={FatReportPage} />
      <PrivateRoute
        exact
        path="/fat-reports/:reportId"
        component={PreviewReportsPage}
      />
      <PrivateRoute
        exact
        path="/add-details-doc"
        component={AddDetailsDocumentation}
      />
      <PrivateRoute
        exact
        path="/:mid/docDetailsFat/:docId"
        component={DocumentationPage_FAT}
      />
      <PrivateRoute
        exact
        path="/:mid/docDetailsSat/:docId"
        component={DocumentationPage_SAT}
      />
      <PrivateRoute exact path="/training/:mid" component={TrainingPage} />
      <PrivateRoute
        exact
        path="/maintenanceReport/:mid"
        component={MaintenanceReportData}
      />
      <PrivateRoute
        exact
        path="/trainingReport/:mid"
        component={TrainingReportData}
      />
      <PrivateRoute exact path="/add-machine" component={AddMachine} />
      <PrivateRoute exact path="/users" component={User} />
      <PrivateRoute exact path="/add-user" component={AddUser} />

      <PrivateRoute exact path="/video" component={VideoPage} />
      <PrivateRoute exact path="/issues" component={Layout} />
      <PrivateRoute exact path="/report" component={CFRPage} />
      <PrivateRoute exact path="/settings" component={SettingsPage} />
      <PrivateRoute exact path="/user-manual" component={UserManualPage} />
      <PrivateRoute exact path="/videocall" component={videoCallPage} />
      <PrivateRoute exact path="/alarms" component={AlarmPage} />
      <PrivateRoute exact path="/hashtag" component={HashTag} />
      <PrivateRoute exact path="/alarm-manager" component={alarmManagerPage} />
      <PrivateRoute exact path="/alarmsnew/:mid" component={AlarmNewPage} />
      <PrivateRoute exact path="/tasks" component={Tasks} />
      <PrivateRoute exact path="/annotation/:mid" component={LiveDataProject} />
      <PrivateRoute exact path="/changeOver/:mid" component={ChangeOver} />
      <PrivateRoute
        exact
        path="/calibration/:mid"
        component={MaintenancePageCalibration}
      />
      <PrivateRoute
        exact
        path="/annotations/:id"
        component={LiveDataAnnotation}
      />
      <PrivateRoute exact path="/3dmodel" component={ModelPage} />
      <PrivateRoute exact path="/modelview" component={ModelView} />
      <PrivateRoute exact path="/smartScan" component={BatchPage} />
      <PrivateRoute exact path="/cms" component={CmsPage} />
      <PrivateRoute exact path="/arview/:canvasId?" component={ARview} />
      <PrivateRoute exact path="/arviews" component={Arviews} />
      <PrivateRoute exact path="/voice" component={VoicePage} />
      <PrivateRoute exact path="/gemba/:mid" component={Gemba} />
      <PrivateRoute
        exact
        path="/lineClearance/:mid"
        component={LineClearance}
      />
      <PrivateRoute exact path="/dpr" component={DPRPage} />
      <PrivateRoute exact path="/utilities" component={UtilsPage} />
    </>
  );
}
