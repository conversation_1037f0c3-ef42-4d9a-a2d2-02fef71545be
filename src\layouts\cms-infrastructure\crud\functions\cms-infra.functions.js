import {
  cmsInfra,
  companies,
  companyId_constant,
} from "../../../../constants/data";
import { db } from "../../../../firebase";
import { toastMessage, toastMessageSuccess } from "../../../../tools/toast";

export const addDataToCms = (data) => {
  // db.collection(companies)
  //   .doc(companyId_constant)
  //   .collection(cmsInfra)
  //   .doc("AIvh9SJkujEq9SxN9Us5")
  //   .collection("equipment_master")
  //   .add(data)
  //   .then((data) => {
  //     toastMessageSuccess({
  //       message: `New Infrastructure added successfully !`,
  //     });
  //   });
};

export const addDataToReports = (data) => {
  // db.collection(companies)
  //   .doc(companyId_constant)
  //   .collection(cmsInfra)
  //   .doc("AIvh9SJkujEq9SxN9Us5")
  //   .collection("sap_report")
  //   .add(data)
  //   .then((data) => {
  //     toastMessageSuccess({
  //       message: `New Report Generated added successfully !`,
  //     });
  //   });
};

export const updateDataToCms = (data) => {
  // db.collection(companies)
  //   .doc(companyId_constant)
  //   .collection(cmsInfra)
  //   .doc("AIvh9SJkujEq9SxN9Us5")
  //   .collection("equipment_master")
  //   .doc(data?.id)
  //   .update(data)
  //   .then((data) => {
  //     toastMessageSuccess({ message: `Updated ${data?.name} successfully !` });
  //   });
};

export const deleteDataFromCms = (data) => {
  // db.collection(companies)
  //   .doc(companyId_constant)
  //   .collection(cmsInfra)
  //   .doc("AIvh9SJkujEq9SxN9Us5")
  //   .collection("equipment_master")
  //   .doc(data?.id)
  //   .delete()
  //   .then((data) => {
  //     console.log("deleted EQP");
  //     toastMessageSuccess({ message: `Deleted successfully !` });
  //   });
};

export const updateDataToReports = (data) => {
  // db.collection(companies)
  //   .doc(companyId_constant)
  //   .collection(cmsInfra)
  //   .doc("AIvh9SJkujEq9SxN9Us5")
  //   .collection("sap_report")
  //   .doc(data?.id)
  //   .update(data)
  //   .then((data) => {
  //     toastMessageSuccess({ message: `Updated Reports successfully !` });
  //   });
};

export const deleteDataFromReports = (data) => {
  // db.collection(companies)
  //   .doc(companyId_constant)
  //   .collection(cmsInfra)
  //   .doc("AIvh9SJkujEq9SxN9Us5")
  //   .collection("sap_report")
  //   .doc(data?.id)
  //   .delete()
  //   .then((data) => {
  //     toastMessageSuccess({ message: `Deleted Report successfully !` });
  //   });
};

export const addDataToSapMaster = (cmsId, data) => {
  if (cmsId === "") {
    return toastMessage({ message: "CMS Infrastructure Not selected" });
  }

  // db.collection(companies)
  //   .doc(companyId_constant)
  //   .collection(cmsInfra)
  //   .doc(cmsId)
  //   .collection("sap-master")
  //   .add(data)
  //   .then((data) => {
  //     toastMessageSuccess({ message: `New SAP Master added successfully !` });
  //   });
};

export const addDataToSapIntimation = (cmsId, data) => {
  if (cmsId === "") {
    return toastMessage({ message: "CMS Infrastructure Not selected" });
  }

  // db.collection(companies)
  //   .doc(companyId_constant)
  //   .collection(cmsInfra)
  //   .doc(cmsId)
  //   .collection("sap-intimation")
  //   .add(data)
  //   .then((data) => {
  //     toastMessageSuccess({
  //       message: `New SAP Intimation added successfully !`,
  //     });
  //   });
};
