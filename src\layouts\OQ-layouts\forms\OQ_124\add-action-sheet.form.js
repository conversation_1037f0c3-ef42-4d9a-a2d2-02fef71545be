import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Divider,
  Box,
  InputLabel,
  LinearProgress,
} from "@mui/material";
import { db } from "../../../../firebase";
import { companies, companyId_constant } from "../../../../constants/data";
import { toastMessage, toastMessageSuccess } from "../../../../tools/toast";
import { DropzoneArea } from "material-ui-dropzone";
import { FileDownload } from "@mui/icons-material";
import { Empty } from "antd";
import { useStorageTablesFile } from "../../../../utils/useStorageTablesFile";
import { DeleteByUrl } from "../../../../utils/StorageOptions";
import { useStateContext } from "../../../../context/ContextProvider";

const AddActionSheet = () => {
  const [actionSheet, setActionSheet] = useState({
    title: "",
    deviation_no: "",
    protocol_ref: "",
    page_no: 0,
    item_no: 0,
    desc: "",
    impact: "",
    tester: "",
    sign_tester: "",
    action: "",
    eng_approval: "",
    sign_eng: "",
    result: "",
    valid_approval: "",
    sign_valid: "",
    cust_approval: "",
    sign_cust: "",
  });
  const { currentMode } = useStateContext();
  const [file1, setFile1] = useState(null);
  let obj = useStorageTablesFile(file1);
  const progress1 = obj.progress;
  const url1 = obj.url;
  const [file2, setFile2] = useState(null);
  obj = useStorageTablesFile(file2);
  const progress2 = obj.progress;
  const url2 = obj.url;
  const [file3, setFile3] = useState(null);
  obj = useStorageTablesFile(file3);
  const progress3 = obj.progress;
  const url3 = obj.url;
  const [file4, setFile4] = useState(null);
  obj = useStorageTablesFile(file4);
  const progress4 = obj.progress;
  const url4 = obj.url;
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];
  const [target, setTarget] = useState("");

  const handleAddData = () => {
    // db.collection(companies).doc(companyId_constant).collection('fatData').doc('0JSDmXSZzKpAzbbNgP8y')
    // .collection('actionSheet').add(actionSheet).then((data) => {
    //     toastMessageSuccess({message: "Successfully Added a new Action Sheet"})
    // })
  };

  const handleChangeImage = (file, setFile, url) => {
    let selectedFile = file[0];
    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        if (url) {
          DeleteByUrl(url);
        }
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };

  const handleDeleteDropZone = (url, setFile) => {
    DeleteByUrl(url);
    setFile(null);
  };

  useEffect(() => {
    setActionSheet({ ...actionSheet, sign_tester: url1 });
  }, [url1]);

  useEffect(() => {
    setActionSheet({ ...actionSheet, sign_eng: url2 });
  }, [url2]);

  useEffect(() => {
    setActionSheet({ ...actionSheet, sign_valid: url3 });
  }, [url3]);

  useEffect(() => {
    setActionSheet({ ...actionSheet, sign_cust: url4 });
  }, [url4]);

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-evenly",
        flexWrap: "wrap",
      }}
      className="add-action-sheet"
    >
      <>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Title</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, title: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="DEVIATION, IMPACT AND CORRECTIVE ACTION SHEET"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Deviation Number</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, deviation_no: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="D-0425"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Protocol reference</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, protocol_ref: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="Protocol-R2F"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Page Number</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, page_no: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="1"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Item Number</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, item_no: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="002"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Tester</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, tester: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="eg: John Doe"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Description</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, desc: e.target.value })
            }
            rows={4}
            multiline
            variant="outlined"
            fullWidth
            placeholder="eg: Deviation tends to be something relevant to the OQ generated"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Impact</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, impact: e.target.value })
            }
            rows={4}
            multiline
            variant="outlined"
            fullWidth
            placeholder="eg: Impact tends to be something relevant to the OQ generated"
          />
        </Box>

        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Engineer Approval</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, eng_approval: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="eg: Suresh Kumar"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Validator Approval</InputLabel>
          <TextField
            variant="outlined"
            onChange={(e) =>
              setActionSheet({ ...actionSheet, valid_approval: e.target.value })
            }
            fullWidth
            placeholder="eg: Hemant Singh"
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Corrective Action</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, action: e.target.value })
            }
            rows={4}
            multiline
            variant="outlined"
            fullWidth
            placeholder="eg: Corrective Action tends to be something relevant to the OQ generated"
          />
        </Box>

        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Result</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, result: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="eg: Results tends to be something relevant to the OQ generated"
          />
        </Box>

        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <InputLabel>Customer Approval</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, cust_approval: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="eg: LNT Oil Technology"
          />
        </Box>
        <Box sx={{ width: "40%", marginBottom: "10px" }}>
          <div className="flex justify-center">
            <div
              className={
                currentMode === "Dark"
                  ? "bg-gray-700 rounded-sm p-1 shadow-md"
                  : "bg-gray-100 rounded-sm p-1 shadow-md"
              }
            >
              <InputLabel style={{ marginBottom: "10px" }}>Media</InputLabel>
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) =>
                  handleChangeImage(loadedFiles, setFile1, url1)
                }
                dropzoneText="Drag and Drop / Click to ADD Media. MaxFileSize : 5Mb"
                showAlerts={false}
                filesLimit={1}
                maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
                onDelete={() => handleDeleteDropZone(url1, setFile1)}
                dropzoneClass={
                  currentMode === "Dark"
                    ? "dropZoneClassDark"
                    : "dropZoneClassLight"
                }
              />
              <div className="p-2 block">
                <LinearProgress
                  style={{ marginBottom: "20px" }}
                  variant="determinate"
                  value={progress1}
                />
                <div className="text-2xl text-gray-400 flex justify-end ">
                  <p> {progress1} % Uploaded</p>
                </div>
                {!url1 ? ( // url when we upload
                  <>
                    <img alt="" src={url1} />
                  </>
                ) : (
                  <Empty
                    description={<span>Please Wait for Preview ...</span>}
                  />
                )}
              </div>
            </div>
          </div>
        </Box>
        <Box sx={{ width: "40%", marginBottom: "10px" }}>
          <div className="flex justify-center">
            <div
              className={
                currentMode === "Dark"
                  ? "bg-gray-700 rounded-sm p-1 shadow-md"
                  : "bg-gray-100 rounded-sm p-1 shadow-md"
              }
            >
              <InputLabel style={{ marginBottom: "10px" }}>Media</InputLabel>
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) =>
                  handleChangeImage(loadedFiles, setFile2, url2)
                }
                dropzoneText="Drag and Drop / Click to ADD Media. MaxFileSize : 5Mb"
                showAlerts={false}
                filesLimit={1}
                maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
                onDelete={() => handleDeleteDropZone(url2, setFile2)}
                dropzoneClass={
                  currentMode === "Dark"
                    ? "dropZoneClassDark"
                    : "dropZoneClassLight"
                }
              />
              <div className="p-2 block">
                <LinearProgress
                  style={{ marginBottom: "20px" }}
                  variant="determinate"
                  value={progress2}
                />
                <div className="text-2xl text-gray-400 flex justify-end ">
                  <p> {progress2} % Uploaded</p>
                </div>
                {!url2 ? ( // url when we upload
                  <>
                    <img alt="" src={url2} />
                  </>
                ) : (
                  <Empty
                    description={<span>Please Wait for Preview ...</span>}
                  />
                )}
              </div>
            </div>
          </div>
        </Box>
        <Box sx={{ width: "40%", marginBottom: "10px" }}>
          <div className="flex justify-center">
            <div
              className={
                currentMode === "Dark"
                  ? "bg-gray-700 rounded-sm p-1 shadow-md"
                  : "bg-gray-100 rounded-sm p-1 shadow-md"
              }
            >
              <InputLabel style={{ marginBottom: "10px" }}>Media</InputLabel>
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) =>
                  handleChangeImage(loadedFiles, setFile3, url3)
                }
                dropzoneText="Drag and Drop / Click to ADD Media. MaxFileSize : 5Mb"
                showAlerts={false}
                filesLimit={1}
                maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
                onDelete={() => handleDeleteDropZone(url3, setFile3)}
                dropzoneClass={
                  currentMode === "Dark"
                    ? "dropZoneClassDark"
                    : "dropZoneClassLight"
                }
              />
              <div className="p-2 block">
                <LinearProgress
                  style={{ marginBottom: "20px" }}
                  variant="determinate"
                  value={progress3}
                />
                <div className="text-2xl text-gray-400 flex justify-end ">
                  <p> {progress3} % Uploaded</p>
                </div>
                {!url3 ? ( // url when we upload
                  <>
                    <img alt="" src={url3} />
                  </>
                ) : (
                  <Empty
                    description={<span>Please Wait for Preview ...</span>}
                  />
                )}
              </div>
            </div>
          </div>
        </Box>
        <Box sx={{ width: "40%", marginBottom: "10px" }}>
          <div className="flex justify-center">
            <div
              className={
                currentMode === "Dark"
                  ? "bg-gray-700 rounded-sm p-1 shadow-md"
                  : "bg-gray-100 rounded-sm p-1 shadow-md"
              }
            >
              <InputLabel style={{ marginBottom: "10px" }}>Media</InputLabel>
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) =>
                  handleChangeImage(loadedFiles, setFile4, url4)
                }
                dropzoneText="Drag and Drop / Click to ADD Media. MaxFileSize : 5Mb"
                showAlerts={false}
                filesLimit={1}
                maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
                onDelete={() => handleDeleteDropZone(url4, setFile4)}
                dropzoneClass={
                  currentMode === "Dark"
                    ? "dropZoneClassDark"
                    : "dropZoneClassLight"
                }
              />
              <div className="p-2 block">
                <LinearProgress
                  style={{ marginBottom: "20px" }}
                  variant="determinate"
                  value={progress4}
                />
                <div className="text-2xl text-gray-400 flex justify-end ">
                  <p> {progress4} % Uploaded</p>
                </div>
                {!url4 ? ( // url when we upload
                  <>
                    <img alt="" src={url4} />
                  </>
                ) : (
                  <Empty
                    description={<span>Please Wait for Preview ...</span>}
                  />
                )}
              </div>
            </div>
          </div>
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "40%" }}>
          <Button
            onClick={() => handleAddData()}
            fullWidth
            variant="contained"
            color="primary"
          >
            Create Action Sheet
          </Button>
        </Box>
      </>
    </div>
  );
};

export default AddActionSheet;
