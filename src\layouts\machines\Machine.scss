.machineContainer {
  margin-top: 1rem;
  width: 100%;
  background-color: #fff;
  box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  color: #344767;

  .title {
    padding: 0 1.2rem;
    padding-top: 1.5rem;
    margin-bottom: 1rem;

    h3 {
      font-size: 1.2rem;
      font-weight: 500;
      opacity: 0.9;
    }
  }

  .desc {
    padding: 0 1.2rem;

    p {
      font-size: 0.9rem;
      opacity: 0.7;
    }
  }

  .machinesOuterContainer {
    padding: 1.2rem 0;

    .machinesInnerContainer {
      //smooth loading
      animation: fadeInAnimation ease 1s;
      animation-iteration-count: 1;
      animation-fill-mode: forwards;

      @keyframes fadeInAnimation {
        0% {
          opacity: 0;
        }

        100% {
          opacity: 1;
        }
      }

      //
      width: 100%;
      min-width: 70vw;
      display: flex;
      flex-flow: row wrap;
      align-items: center;
      justify-content: center;

      .singleMachineContainer {
        width: 30%;
        height: 240px;
        padding: 1rem;
        border-radius: 16px;
        margin: 0.75rem;

        .machineInfoContainer {
          .machineImageContainer {
            width: 100%;
            height: 38%;

            img {
              width: 100%;
              height: 100%;
              border-radius: 18px;
              box-shadow:
                rgba(20, 20, 20, 0.12) 0px 4px 6px -1px,
                rgba(20, 20, 20, 0.07) 0px 2px 4px -1px;
            }
          }

          .machineDescContainer {
            margin-top: 1rem;
            padding-left: 0.5rem;

            .machineBlock {
              //margin: 1rem 0;
              font-size: 0.7rem;
              font-weight: 400;
              opacity: 0.95;
              text-transform: capitalize;
            }

            .machineCommon {
              //background-color: #17c1e8;
              align-self: flex-start;
              //width: 80%;
              font-size: 0.7rem;
              font-weight: 800;
              text-transform: capitalize;
            }

            .machineModel {
              //margin: 1rem 0;
              font-size: 0.7rem;
              font-weight: 400;
              opacity: 0.95;
              text-transform: capitalize;
            }

            .machineEquipmentId {
              //margin: 1rem 0;
              font-size: 0.7rem;
              font-weight: 400;
              opacity: 0.95;
              text-transform: capitalize;
            }

            .machineName {
              font-size: 1.3rem;
              font-weight: 500;
            }

            .machineDesc {
              margin: 0.6rem 0;
              font-size: 0.9rem;
              font-weight: 400;
              opacity: 0.85;
              letter-spacing: 1px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              /* number of lines to show */
              -webkit-box-orient: vertical;
            }

            .machineBtns {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-top: 1.5rem;

              .right {
                .rightBtn {
                  border: none;
                  background: #fff;
                  cursor: pointer;
                  margin: 0 0.25rem;

                  i {
                    font-size: 1.2rem;
                  }

                  &:hover {
                    transform: scale(1.1);
                  }
                }

                .editBtn {
                  i {
                    color: rgb(68, 75, 118);
                  }
                }

                .delBtn {
                  i {
                    color: rgba(240, 2, 2, 0.801);
                  }
                }
              }
            }
          }
        }
      }

      .addMachineContainer {
        width: 25%;
        min-height: 240px;
        border-radius: 16px;
        border: 1px solid rgba(0, 0, 0, 0.3);
        margin: 1rem 0;
        margin-left: 1.2rem;
        display: flex;
        align-items: center;
        justify-content: center;

        .formLink {
          width: 100%;
          height: 100%;
          color: #344767;
          opacity: 0.8;
          text-decoration: none;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }

        user-select: none;

        .addBtn {
          i {
            font-size: 2rem;
          }
        }

        .text {
          font-size: 1.2rem;
          font-weight: 500;
        }
      }
    }
  }
}
