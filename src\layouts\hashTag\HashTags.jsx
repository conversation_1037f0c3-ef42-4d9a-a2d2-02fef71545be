import React, { useEffect, useState, useRef, useContext } from "react";
import { db } from "../../firebase";
import { companies, companyId_constant } from "../../constants/data";
import { <PERSON>ton, Card, IconButton, Typography } from "@mui/material";
import Box from "@mui/material/Box";
import { firebaseLooper } from "../../tools/tool";
import TextField from "@mui/material/TextField";
import SearchIcon from "@mui/icons-material/Search";
import Autocomplete from "@mui/material/Autocomplete";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import InputLabel from "@mui/material/InputLabel";
import "./hashTag.scss";
import { ButtonBasic } from "../../components/buttons/Buttons";
import ModalDiv from "./modalDiv";
import MicOffIcon from "@mui/icons-material/MicOff";
import MicIcon from "@mui/icons-material/Mic";
import { useStateContext } from "../../context/ContextProvider";
import Accord from "./Accord";
import PageHeader from "../../components/commons/page-header.component";
import usePagination from "../../usePagination";
import { Pagination } from "@mui/material";
import HashtagProvider, {
  HashtagContext,
} from "../../services2/hashtag/hashtag.context";
import AccordProvider from "../../services2/hashtag/accord.context";
import ModalProvider from "../../services2/hashtag/modal.context";
import "../alarms/Alarms.scss";
import { sharedCss } from "../../styles/sharedCss";
import { makeStyles } from "@mui/styles";
import NoDataComponent from "../../components/commons/noData.component";
import CommonDropDownComponent from "../../components/commons/dropDown.component";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";

const SpeechRecognition =
  window.SpeechRecognition || window.webkitSpeechRecognition;
const mic = new SpeechRecognition();
mic.continuous = true;
mic.interimResults = true;
mic.lang = "en-US";

const HashTags = () => {
  const [mType] = useState([
    "Calibration",
    "Machine Breakdown",
    "Routine",
    "Preventive",
  ]);
  const { currentMode, currentColorLight } = useStateContext();
  const {
    hashes,
    tempHash,
    setTempHash,
    tempHashes,
    setTempHashes,
    hashArray,
    setHashArray,
    machines,
    maint,
    stepArray,
    mapForVis,
    temptemp,
    setTempTemp,
    open,
    setOpen,
    loading, // Add loading from context
  } = useContext(HashtagContext);
  const [sortBy, setSortBy] = useState("none");
  const [findBy, setFindBy] = useState("");
  const [inputFindBy, setInputFindBy] = useState("");
  const [micOn, setMicOn] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const searchInput = useRef(null);
  const [page, setPage] = useState(1);
  const PER_PAGE = 6;
  const count = Math.ceil(tempHashes.length / PER_PAGE);
  const _DATA = usePagination(tempHashes, PER_PAGE);
  const [curPage, setCurPage] = useState(1);

  const hasHashtagPOSTAccess = useCheckAccess("hashtags", "POST");
  const hasHashtagGETAccess = useCheckAccess("hashtags", "GET");

  const useCustomStyles = makeStyles((theme) => ({
    hashtagsContainer: {
      // padding: "1rem",
      // borderRadius: "10px",
      backgroundColor: theme.palette.custom.backgroundForth,
      boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
    },
    hashtagsOuterContainer: {
      width: "100%",
    },
    hashtagsInnerContainer: {
      display: "flex",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    addButton: {
      width: "max-content",
    },
    hashtagsPageContainer: {
      // padding: "1rem",
      // border: "1px solid gainsboro",
    },
  }));

  const handleTagSubmit = (e) => {
    e.preventDefault();
    handleChanges(inputFindBy);
  };

  const handleSort = (target) => {
    if (target === "ascending") {
      const newObj = [...tempHash];
      newObj.sort((a, b) => {
        const nameA = a.id.toUpperCase();
        const nameB = b.id.toUpperCase();
        if (nameA < nameB) {
          return -1;
        }
        if (nameA > nameB) {
          return 1;
        }
        return 0;
      });
      setTempHash([...newObj]);
    } else if (target === "descending") {
      const newObj = [...tempHash];
      newObj.sort((a, b) => {
        const nameA = a.id.toUpperCase();
        const nameB = b.id.toUpperCase();
        if (nameA < nameB) {
          return 1;
        }
        if (nameA > nameB) {
          return -1;
        }
        return 0;
      });
      setTempHash([...newObj]);
    }
  };

  const handleChanges = (target) => {
    setSortBy("");
    setInputFindBy(target);
    setFindBy(target);
    const arr = temptemp.filter((options) =>
      options.id.toLowerCase().includes(target.toLowerCase()),
    );
    if (target.trim() !== "") setTempHashes([...arr]);
    else setTempHashes(temptemp);
  };

  const handleMicOn = (target) => {
    if (target === true) {
      mic.start();
      mic.onend = () => {
        mic.start();
      };
    } else {
      mic.stop();
      mic.onend = () => {};
    }
    mic.onstart = () => {};
    mic.onresult = (event) => {
      let str = "";
      const transcript = Array.from(event.results)
        .map((result) => result[0])
        .map((result) => result.transcript)
        .join("");
      const finalString = handleJoin(transcript, "");
      handleChanges(finalString);
      mic.onerror = (event) => {};
      searchInput.current.focus();
      setTimeout(() => {
        setMicOn(false);
        handleMicOn(false);
      }, 1000);
    };
  };

  const handleJoin = (string, str) => {
    [...string].forEach(
      (character) => (str += character != " " ? character : ""),
    );
    return str;
  };

  const handleOpen = () => setOpen(true);

  const startMic = () => setMicOn(true);

  const stopMic = () => setMicOn(false);

  const handlePageChange = (e, p) => {
    console.log(p);
    setPage(p);
    _DATA.jump(p);
    setCurPage(p);
  };

  useEffect(() => {
    setTempHash(_DATA.currentData());
  }, [tempHashes]);

  useEffect(() => {
    setTempHash(_DATA.currentData());
  }, [curPage]);

  const commonCss = sharedCss();
  const customCss = useCustomStyles();
  return (
    <div className={customCss.hashtagsPageContainer}>
      <header
        // style={{
        // 	backgroundColor:
        // 		currentMode === "Dark" ? "#161C24" : currentColorLight,
        // 	color: currentMode === "Dark" ? "white" : "#344767",
        // 	padding: 0,
        // 	boxShadow: "-4px 25px 40px -30px rgba(0, 0, 0, 0.5)",
        // 	borderRadius: "10px",
        // 	display: "flex",
        // 	alignItems: "center",
        // 	justifyContent: "space-between",
        // }}
        // className='mb-8'
        className={`${commonCss.headingContainer}  ${commonCss.backgroundLight} border-radius-inner`}
      >
        <Box sx={{ padding: "0.5rem" }}>
          <Typography variant="h4">Hashtags</Typography>
          <Typography variant="h6">Hashtags & Associated Modules</Typography>
        </Box>
        {/* <div style={{ padding: "10px", marginLeft: "0px" }} className='title'>
					<PageHeader
						title='Hashtags'
						subText='Hashtags & Associated Modules'
					/>
				</div> */}
        <div
          className="action-container mr-4"
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flexWrap: "nowrap",
            gap: "24px",
            marginRight: "24px",
          }}
        >
          <form onSubmit={handleTagSubmit} className="flex">
            <Autocomplete
              size="small"
              value={findBy}
              onChange={(event, newValue) =>
                setFindBy(newValue === null ? "" : newValue)
              }
              inputValue={inputFindBy}
              onInputChange={(event, newInputValue) =>
                handleChanges(newInputValue)
              }
              id="controllable-states-demo"
              options={hashArray}
              renderOption={(props, option) => {
                return (
                  <span
                    {...props}
                    // style={
                    // 	currentMode === "Dark"
                    // 		? { background: "#161C24", color: "white" }
                    // 		: { background: currentColorLight }
                    // }
                  >
                    {option}
                  </span>
                );
              }}
              sx={{ width: 400 }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  inputRef={searchInput}
                  label="Search"
                  className={commonCss.searchBox}
                />
              )}
            />
            {}
            <IconButton
              onClick={() => {
                if (micOn) {
                  stopMic();
                  handleMicOn(false);
                } else {
                  startMic();
                  handleMicOn(true);
                }
              }}
              // style={currentMode === "Dark" ? { color: "white" } : {}}>
            >
              {micOn ? <MicIcon /> : <MicOffIcon />}
            </IconButton>
            {/* <IconButton
              className="searchIcon"
            // style={currentMode === "Dark" ? { color: "white" } : {}}>
            >
              <SearchIcon />
            </IconButton> */}
          </form>

          {/*
          <FormControl size="small" sx={{ minWidth: 125 }}>
            <InputLabel id="demo-simple-select-label-filter">Sort</InputLabel>
            <Select
              labelId="demo-simple-select-label-filter"
              id="demo-simple-select-filter"
              value={sortBy}
              label="Sort"
              onChange={(e) => {
                setSortBy(e.target.value);
                handleSort(e.target.value);
              }}
            >
              <MenuItem
                // sx={
                // 	currentMode === "Dark"
                // 		? { background: "#161C24", color: "white" }
                // 		: { background: currentColorLight }
                // }
                value="none"
              >
                None
              </MenuItem>
              <MenuItem
                // sx={
                // 	currentMode === "Dark"
                // 		? { background: "#161C24", color: "white" }
                // 		: { background: currentColorLight }
                // }
                value="ascending"
              >
                Ascending
              </MenuItem>
              <MenuItem
                // sx={
                // 	currentMode === "Dark"
                // 		? { background: "#161C24", color: "white" }
                // 		: { background: currentColorLight }
                // }
                value="descending"
              >
                Descending
              </MenuItem>
            </Select>
          </FormControl>
          */}
          <CommonDropDownComponent
            dropDownContainerStyle={{ minWidth: "150px" }}
            dropDownLabel={"Sort"}
            menuValue={sortBy}
            handleChange={(e) => {
              setSortBy(e.target.value);
              handleSort(e.target.value);
            }}
            menuData={[
              { _id: "none", label: "None", value: "none" },
              { _id: "ascending", label: "Ascending", value: "ascending" },
              { _id: "descending", label: "Descending", value: "descending" },
            ]}
            menuItemDisplay={"label"}
            menuItemValue={"value"}
          />

          {/* <ButtonBasic
						buttonTitle='Add HashTag'
						onClick={handleOpen}></ButtonBasic> */}
          <Button
            className={customCss.addButton}
            variant="contained"
            onClick={handleOpen}
            disabled={!hasHashtagPOSTAccess}
          >
            Add HashTag
          </Button>
        </div>
      </header>

      <div
        // className='allHashtagContainer'
        // style={
        // 	currentMode === "Dark"
        // 		? {
        // 				background: "#161C24",
        // 				color: "white",
        // 				borderRadius: "10px",
        // 				padding: "48px 32px",
        // 		  }
        // 		: {
        // 				background: currentColorLight,
        // 				borderRadius: "10px",
        // 				padding: "48px 32px",
        // 		  }
        // }
        className={`${commonCss.sectionContainer} border-radius-inner`}
        style={
          !!tempHashes?.length
            ? { justifyContent: "center", paddingTop: "0.75rem" }
            : { alignItems: "center", paddingTop: "2rem" }
        }
      >
        {hasHashtagGETAccess ? (
          <>
            {tempHash.map((options, idx) => {
              return (
                <Card
                  key={options.id}
                  style={{
                    margin: "2px 0",
                    // 	borderRadius: "15px",
                    // 	overflow: "hidden",
                  }}
                  // className='hover:shadow-xl hover:shadow-gray-500'
                >
                  <AccordProvider>
                    <Accord
                      object={options}
                      setExpanded={setExpanded}
                      expanded={expanded}
                      map={mapForVis}
                    />
                  </AccordProvider>
                </Card>
              );
            })}
            {!tempHashes?.length && (
              <>
                <NoDataComponent useAtTable={false} paddText={true} />
              </>
            )}
            {!!tempHashes?.length && (
              <Pagination
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: "0.25rem",
                }}
                count={count}
                page={page}
                onChange={handlePageChange}
              />
            )}
          </>
        ) : (
          <NotAccessible />
        )}
      </div>
      <ModalProvider>
        <ModalDiv
          stepArray={stepArray}
          mType={mType}
          machines={machines}
          open={open}
          setOpen={setOpen}
          maint={maint}
        />
      </ModalProvider>
    </div>
  );
};

export default HashTags;
