import { TextField } from "@mui/material";
import { Button, Select, MenuItem, InputLabel } from "@mui/material";
import React from "react";
import AddIcon from "@mui/icons-material/Add";
import RefreshIcon from "@mui/icons-material/Refresh";
import RemoveIcon from "@mui/icons-material/Remove";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CloseIcon from "@mui/icons-material/Close";
import { useState, useEffect } from "react";
import { db } from "../../../firebase";
import { companies, companyId_constant } from "../../../constants/data";
import {
  toastMessageSuccess,
  toastMessage,
  toastMessageWarning,
} from "../../../tools/toast";
import { useParams } from "react-router-dom";
import { firebaseLooper } from "../../../tools/tool";
import { Box } from "@mui/system";
import { themeColors } from "../../../infrastructure/theme";
import { useStateContext } from "../../../context/ContextProvider";

const EditTableValues = ({ collectionId, type, handleClose, data }) => {
  const { docId } = useParams();
  const [checkpoint, setCheckpoint] = useState("");
  const [acceptanceCriteria, setAcceptanceCriteria] = useState("");
  const [observation, setObservation] = useState("");
  const [deviation, setDeviation] = useState("");
  const [confirm, setConfirm] = useState("");
  const [tableType, setTableType] = useState("");
  const [pType, setPType] = useState("");
  const [input, setInput] = useState("");
  const [limit, setLimit] = useState("");
  const [pft0, setPFT0] = useState("");
  const [pft1, setPFT1] = useState({
    from: "",
    to: "",
    time: "",
  });
  const [pft2, setPFT2] = useState("");
  const [sensor, setSensor] = useState("");
  const [waitTime, setWTime] = useState("");
  const [sensors, setSensors] = useState([]);
  const [ori, setOri] = useState({});

  const { currentColor, currentMode, currentColorLight } = useStateContext();

  const menuItemTheme = {
    backgroundColor:
      currentMode === "Dark"
        ? themeColors.dark.primary
        : themeColors.light.primary,
  };

  useEffect(() => {
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection('liveData2')
    //   .onSnapshot(snap => {
    //     const data = firebaseLooper(snap)
    //     const arr = data.map(options => options.id)
    //     setSensors([...arr])
    //   })
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(type)
    //   .doc(collectionId)
    //   .collection('table')
    //   .doc(data.id)
    //   .get()
    //   .then(options => {
    //     setOri(options.data())
    //   })
  }, []);

  useEffect(() => {
    if (JSON.stringify(ori) === "{}") return;
    setCheckpoint(ori?.check);
    setAcceptanceCriteria(ori?.acceptance);
    setObservation(ori?.observation);
    setDeviation(ori?.dev);
    setConfirm(ori?.confirm);
    setTableType(ori?.tableType);
    setSensor(JSON.stringify(ori?.sensor) === "[]" ? "" : ori?.sensor);
    handlePType();
  }, [ori]);

  const handleSubmit = (e) => {
    e.preventDefault();
    let docdata = {
      acceptance: acceptanceCriteria,
      check: checkpoint,
      observation: observation,
      dev: deviation,
      confirm: confirm,
      pType: pType ? pType : "",
      tableType,
      sensor: sensor,
      condition: limit,
      start: parseInt(pft1.from),
      stop: parseInt(pft1.to),
      acceptanceValue: parseInt(pft0) | parseInt(pft1.time) | parseInt(pft2),
      waitingTime: parseInt(waitTime),
    };
    let flag = 0;
    if (waitTime !== "" && isNaN(parseInt(waitTime))) {
      toastMessage({ message: "Enter a numerical value for waiting time" });
      flag = 1;
    }
    if (
      (pft0 !== "" && isNaN(parseInt(pft0))) ||
      (pft1.time !== "" && isNaN(parseInt(pft1.time))) ||
      (pft2 !== "" && isNaN(parseInt(pft2)))
    ) {
      toastMessage({
        message: "Enter a numerical value for Acceptance criteria value",
      });
      flag = 1;
    }
    setAcceptanceCriteria("");
    setDeviation("");
    setObservation("");
    setConfirm("");
    setCheckpoint("");
    setLimit("");
    setPFT0("");
    setPFT1("");
    setPFT2("");
    setPType("");
    setTableType("");
    setWTime("");
    setSensor("");
    if (flag === 1) return;
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(type)
    //   .doc(collectionId)
    //   .collection('table')
    //   .doc(data.id)
    //   .update(docdata)
    //   .then((dataReturn) => {

    //     handleClose()
    //     toastMessageSuccess({ message: "Updated details for documentation Successfully !" })
    //   })
    //   .catch(err => toastMessage({ message: err }))
  };

  const handleMid = (e) => {
    setPFT1((options) => ({
      ...options,
      [e.target.name]: e.target.value,
    }));
  };

  const handlePType = () => {
    if (ori?.pType !== "process" && ori?.pType !== "set_point") {
      setPType(ori?.pType);
      if (ori?.pType === "PFT0") {
        setPFT0("acceptanceValue" in ori ? ori.acceptanceValue : "");
        setLimit("condition" in ori ? ori.condition : "");
      } else if (ori?.pType === "PFT1") {
        const obj = {
          from: "start" in ori ? ori.start : "",
          to: "stop" in ori ? ori.stop : "",
          time: "acceptanceValue" in ori ? ori.acceptanceValue : "",
        };
        setPFT1(obj);
      } else {
        setPFT2("acceptanceValue" in ori ? ori.acceptanceValue : "");
        setLimit("condition" in ori ? ori.condition : "");
        setWTime("waitingTime" in ori ? ori.waitingTime : "");
      }
    } else setPType("");
  };

  const handleReset = () => {
    setSensor("");
    handlePType();
    setCheckpoint(ori.check);
    setAcceptanceCriteria(ori.acceptance);
    setObservation(ori.observation);
    setDeviation(ori.dev);
    setConfirm(ori.confirm);
    setTableType(ori.tableType);
    setConfirm(ori.confirm);
    setLimit("condition" in ori ? ori.condition : "");
    setSensor(JSON.stringify(ori.sensor) === "[]" ? "" : ori.sensor);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Box
        sx={{ display: "grid", gap: 1, gridTemplateColumns: "repeat(2, 1fr)" }}
      >
        <Box>
          <InputLabel>Checkpoint</InputLabel>
          <TextField
            value={checkpoint}
            onChange={(e) => setCheckpoint(e.target.value)}
            style={{ marginBottom: "10px" }}
            variant="outlined"
            fullWidth
            placeholder="Enter Checkpoint"
            required
          />
        </Box>

        <Box>
          <InputLabel>Acceptance Criteria</InputLabel>
          <TextField
            value={acceptanceCriteria}
            onChange={(e) => setAcceptanceCriteria(e.target.value)}
            style={{ marginBottom: "10px" }}
            variant="outlined"
            fullWidth
            placeholder="Enter Acceptance Criteria"
            required
          />
        </Box>

        <Box>
          <InputLabel>Observation</InputLabel>
          <TextField
            value={observation}
            onChange={(e) => setObservation(e.target.value)}
            style={{ marginBottom: "10px" }}
            variant="outlined"
            fullWidth
            placeholder="Enter Observation"
          />
        </Box>

        <Box>
          <InputLabel>Deviation</InputLabel>
          <TextField
            value={deviation}
            onChange={(e) => setDeviation(e.target.value)}
            style={{ marginBottom: "10px" }}
            variant="outlined"
            fullWidth
            placeholder="Enter Deviation"
          />
        </Box>

        <Box>
          <InputLabel>Select Confrimation (YES | NO)</InputLabel>
          <Select
            name="type"
            value={confirm}
            placeholder="Select Confirmation"
            onChange={(e) => setConfirm(e.target.value)}
            variant="outlined"
            fullWidth
            style={{ marginBottom: "10px" }}
          >
            <MenuItem sx={menuItemTheme} value="yes">
              Yes
            </MenuItem>
            <MenuItem sx={menuItemTheme} value="no">
              No
            </MenuItem>
          </Select>
        </Box>

        <Box>
          <InputLabel htmlFor="type">Process Type</InputLabel>
          <Select
            name="type"
            value={tableType}
            placeholder="Select Type"
            onChange={(e) => setTableType(e.target.value)}
            variant="outlined"
            fullWidth
            style={{ marginBottom: "10px" }}
            required
          >
            <MenuItem value="auto">Auto</MenuItem>
            <MenuItem value="manual">Manual</MenuItem>
          </Select>
        </Box>

        {tableType === "auto" ? (
          <>
            <Box>
              <InputLabel htmlFor="pType">Process Function Type</InputLabel>
              <Select
                name="pType"
                value={pType}
                placeholder="Select Process Type"
                onChange={(e) => {
                  setPType(e.target.value);
                  setLimit("");
                  setPFT0("");
                  setPFT1({
                    from: "",
                    to: "",
                    time: "",
                  });
                  setPFT2("");
                }}
                variant="outlined"
                fullWidth
                style={{ marginBottom: "10px" }}
                required
              >
                <MenuItem value="PFT0">
                  Condition check function, checking if a value is less than or
                  greater than a given value
                </MenuItem>
                <MenuItem value="PFT1">
                  Time period function, the time it takes to reach from the
                  start point to an end point
                </MenuItem>
                <MenuItem value="PFT2">
                  Wait function, the maximum or minimum value it can reach in a
                  given time
                </MenuItem>
              </Select>
            </Box>

            <Box>
              <InputLabel htmlFor="type">Select Sensor</InputLabel>
              <Select
                name="sensor"
                value={sensor}
                placeholder="Select Sensor"
                onChange={(e) => setSensor(e.target.value)}
                variant="outlined"
                fullWidth
                style={{ marginBottom: "10px" }}
                // required
              >
                {sensors.map((options) => {
                  return (
                    <MenuItem value={options} key={options}>
                      {options}
                    </MenuItem>
                  );
                })}
              </Select>
              {/* <InputLabel htmlFor="pType">Select the Input parameter</InputLabel>
          <Select
            name="input"
            value={input}
            placeholder="Select Input parameter"
            onChange={(e) => setInput(e.target.value)}
            variant='outlined'
            fullWidth
            style={{ marginBottom: '10px' }}
            required
          >
            <MenuItem value="time" disabled={pType !== "PFT0"}>
              Time
            </MenuItem>
            <MenuItem value="temp">
              Temperature
            </MenuItem>
            <MenuItem value="press">
              Pressure
            </MenuItem>
          </Select> */}
              {pType === "PFT0" ? (
                <>
                  <InputLabel htmlFor="pType">
                    Select condition (LESS | EQUAL | GREAT)
                  </InputLabel>
                  <Select
                    name="limit"
                    value={limit}
                    placeholder="Select LESS | EQUAL | GREAT sign"
                    onChange={(e) => setLimit(e.target.value)}
                    variant="outlined"
                    fullWidth
                    style={{ marginBottom: "10px" }}
                    required
                  >
                    <MenuItem value="less">{"<"}</MenuItem>
                    <MenuItem value="equal">{"="}</MenuItem>
                    <MenuItem value="great">{">"}</MenuItem>
                  </Select>
                  <InputLabel>
                    Acceptance Criteria Value (Condition point)
                  </InputLabel>
                  <TextField
                    value={pft0}
                    onChange={(e) => setPFT0(e.target.value)}
                    style={{ marginBottom: "10px" }}
                    variant="outlined"
                    fullWidth
                    placeholder="Enter Value"
                    required
                  />
                  {/* <InputLabel>Acceptance Criteria</InputLabel>
              <TextField value={acceptance} onChange={(e) => setAcceptance(e.target.value)} style={{ marginBottom: '10px' }} variant='outlined' fullWidth placeholder='Enter Acceptance Criteria' /> */}
                </>
              ) : (
                <>
                  {pType === "PFT1" ? (
                    <>
                      <InputLabel htmlFor="pType">Enter a range</InputLabel>
                      <InputLabel>From: </InputLabel>
                      <TextField
                        value={pft1.from}
                        name="from"
                        onChange={(e) => handleMid(e)}
                        style={{ marginBottom: "10px" }}
                        variant="outlined"
                        fullWidth
                        placeholder="Enter the FROM point"
                        required
                      />
                      <InputLabel>To: </InputLabel>
                      <TextField
                        value={pft1.to}
                        name="to"
                        onChange={(e) => handleMid(e)}
                        style={{ marginBottom: "10px" }}
                        variant="outlined"
                        fullWidth
                        placeholder="Enter the TO point"
                        required
                      />
                      <InputLabel>
                        Acceptance Criteria Value (Time in minutes)
                      </InputLabel>
                      <TextField
                        value={pft1.time}
                        name="time"
                        onChange={(e) => handleMid(e)}
                        style={{ marginBottom: "10px" }}
                        variant="outlined"
                        fullWidth
                        placeholder="Enter Value"
                        required
                      />
                    </>
                  ) : (
                    <>
                      {pType === "PFT2" ? (
                        <>
                          <InputLabel htmlFor="pType">
                            Select condition (LESS | EQUAL | GREAT)
                          </InputLabel>
                          <Select
                            name="limit"
                            value={limit}
                            placeholder="Select LESS | EQUAL | GREAT sign"
                            onChange={(e) => setLimit(e.target.value)}
                            variant="outlined"
                            fullWidth
                            style={{ marginBottom: "10px" }}
                            required
                          >
                            <MenuItem value="less">{"<"}</MenuItem>
                            <MenuItem value="equal">{"="}</MenuItem>
                            <MenuItem value="great">{">"}</MenuItem>
                          </Select>
                          <InputLabel>Waiting Time</InputLabel>
                          <TextField
                            value={waitTime}
                            onChange={(e) => setWTime(e.target.value)}
                            style={{ marginBottom: "10px" }}
                            variant="outlined"
                            fullWidth
                            placeholder="Enter Waiting Time"
                            required
                          />
                          <InputLabel>
                            Acceptance Criteria Value (Ultimate point)
                          </InputLabel>
                          <TextField
                            value={pft2}
                            onChange={(e) => setPFT2(e.target.value)}
                            style={{ marginBottom: "10px" }}
                            variant="outlined"
                            fullWidth
                            placeholder="Enter Value"
                            required
                          />
                          {/* <InputLabel>Acceptance Criteria</InputLabel>
                      <TextField value={ac} onChange={(e) => setAc(e.target.value)} style={{ marginBottom: '10px' }} variant='outlined' fullWidth placeholder='Enter Acceptance Criteria' /> */}
                        </>
                      ) : (
                        <></>
                      )}
                    </>
                  )}
                </>
              )}
              {/* <InputLabel>Check Point</InputLabel>
          <TextField value={check} onChange={(e) => setCheck(e.target.value)} style={{ marginBottom: '10px' }} variant='outlined' fullWidth placeholder='Enter Document Title' />
          <InputLabel>Acceptance Criteria</InputLabel>
          <TextField value={acceptance} onChange={(e) => setAcceptance(e.target.value)} style={{ marginBottom: '10px' }} variant='outlined' fullWidth placeholder='Enter Document Description' /> */}
            </Box>
          </>
        ) : (
          <></>
        )}
      </Box>

      <div className="mt-10 flex justify-between">
        <Button
          onClick={handleClose}
          variant="contained"
          color="error"
          endIcon={<CloseIcon />}
        >
          Cancel{" "}
        </Button>
        <Button type="submit" variant="contained" endIcon={<AddIcon />}>
          Update{" "}
        </Button>
        <Button
          onClick={handleReset}
          variant="contained"
          color="warning"
          endIcon={<RefreshIcon />}
        >
          Reset{" "}
        </Button>
      </div>
    </form>
  );
};

export default EditTableValues;
