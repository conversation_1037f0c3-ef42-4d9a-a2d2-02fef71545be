import React, { useContext, useEffect, useState } from "react";
import {
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  TableCell,
  TableRow,
  Button,
  Tooltip,
  Menu,
  MenuItem as MuiMenuItem,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import DeleteIcon from "@mui/icons-material/Delete";
import ReportIcon from "@mui/icons-material/Report";
import EditIcon from "@mui/icons-material/Edit";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { useAuth } from "../../hooks/AuthProvider";
import { useStateContext } from "../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { useDeleteMachineCfr } from "../../hooks/cfr/machineCfrProvider";
import EditChangeOver from "./EditMaintenance/EditChangeOver";
import Manuals from "../Manuals/Manuals";
import Delete from "../../components/Delete/Delete";
import { makeStyles } from "@mui/styles";
import { HashtagContext } from "../../services2/hashtag/hashtag.context";
import { useMaintenanceInfo } from "../../context/MaintenanceContext";
import AddStepDialog from "./AddStepDialog";
import { DialogActions } from "@material-ui/core";
import { ButtonBasicCancel } from "../../components/buttons/Buttons";
import {
  ReceiptLong,
  AddCircleOutlineOutlined,
  PictureAsPdf,
  ViewList
} from "@mui/icons-material";
import ReportDialog from "./ReportDialog";
import SopPreviewDialog from "./SopPreviewDialog";
import ArrangeStepsDialog from "./ArrangeStepsDialog";
import { useCheckAccess } from '../../utils/useCheckAccess';

const useCustomStyles = makeStyles((theme) => ({
  manualsContainer: {
    padding: "0.5rem",
    backgroundColor: theme.palette.custom.backgroundForth,
    height: "auto",
  },
}));

const ChangeOverItem = ({ data, mid, machineName, role, lastItem = false }) => {
  var ind = 0;
  const manual_id = data?._id;
  const { currentUser } = useAuth();
  const [open, setOpen] = useState(false);
  const [steps, setSteps] = useState([]);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [stepIndex, setStepIndex] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openArrangeSteps, setOpenArrangeSteps] = useState(false); // New state for ArrangeStepsDialog
  const customCss = useCustomStyles();
  const { currentMode } = useStateContext();
  const deleteChangeOverCfr = useDeleteMachineCfr();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const { hashes, handleHastageData, setTempHashes, hashArray, temptemp } =
    useContext(HashtagContext);
  const maintenanceInfoFromContext = useMaintenanceInfo();
  const [openReportConfirm, setOpenReportConfirm] = useState(false);
  const [openSop, setOpenSop] = useState(false);

  const [anchorEl, setAnchorEl] = useState(null);
  const openMenu = Boolean(anchorEl);

  const hasChangeOverPUTAccess = useCheckAccess("changeOver", "PUT");
  const hasChangeOverDELETEAccess = useCheckAccess("changeOver", "DELETE");
  const hasChangeOverReportPOSTAccess = useCheckAccess(
    "changeOverReport",
    "POST",
  );
  const hasStepPOSTAccess = useCheckAccess("stepdata", "POST");

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleGenerateReport = async () => {
    try {
      const reportData = {
        title: data.title,
        desc: data.desc,
        sop_url: data.sop_url,
        mid: data.mid,
        issue_id: data.issue_id,
        main_id: data._id,
        email: currentUser.email,
      };

      const response = await axios.post(
        `${dbConfig.url}/changeOverReport/create-report`,
        reportData,
      );

      if (response.data.success) {
        toastMessageSuccess({
          message: response.data.message || "Report generated successfully!",
        });
        setRefreshCount(refreshCount + 1);
      }
      setOpenReportConfirm(false);
    } catch (error) {
      toastMessage({
        message: error.response?.data?.message || "Failed to generate report",
      });
    }
  };

  const deleteData = async () => {
    const date = new Date();
    const data2 = {
      activity: "ChangeOver deleted",
      dateTime: date,
      description: "A ChangeOver is deleted",
      machine: mid,
      module: "ChangeOver",
      username: currentUser.username,
    };
    await axios.delete(`${dbConfig.url}/changeOver/${data._id}`).then(() => {
      toastMessageSuccess({
        message: `${data?.title} has been deleted successfully!`,
      });
      deleteChangeOverCfr(data2);
      setOpenDel(false);
      setRefreshCount(refreshCount + 1);
    });
  };

  return (
    <>
      <TableRow
        hover
        sx={{
          "&:last-child td, &:last-child th": { border: 0 },
          borderBottom: lastItem ? "none" : "0.05rem solid #e0e0e0",
          "&:hover": { bgcolor: "#f5f5f5" },
        }}
        style={{ cursor: "pointer" }}
      >
        <TableCell
          style={
            currentMode === "Dark"
              ? {
                  color: "white",
                  borderBottom: "none",
                  maxWidth: "12.5vw",
                  wordBreak: "break-word",
                }
              : {
                  color: "black",
                  borderBottom: "none",
                  maxWidth: "12.5vw",
                  wordBreak: "break-word",
                }
          }
          align="left"
        >
          <b className="capitalize">{data?.title}</b>
        </TableCell>

        <TableCell
          style={
            currentMode === "Dark"
              ? {
                  color: "white",
                  borderBottom: "none",
                  maxWidth: "50vw",
                  wordBreak: "break-word",
                }
              : {
                  color: "black",
                  borderBottom: "none",
                  maxWidth: "50vw",
                  wordBreak: "break-word",
                }
          }
          align="left"
        >
          {data?.desc}
        </TableCell>

        <TableCell
          style={
            currentMode === "Dark"
              ? { color: "white", borderBottom: "none" }
              : { color: "black", borderBottom: "none" }
          }
          align="center"
        >
          <div className="dataBtns">
            {role !== "trainee" && (
              <>
                <IconButton
                  onClick={() => setOpenEdit(true)}
                  disabled={!hasChangeOverPUTAccess}
                  sx={{
                    color: !hasChangeOverPUTAccess
                      ? "grey.500"
                      : "primary.main",
                  }}
                >
                  <EditIcon style={{ fontSize: "20px" }} />
                </IconButton>
              </>
            )}

            <IconButton onClick={handleMenuOpen}>
              <MoreVertIcon
                style={{
                  fontSize: "20px",
                  color: currentMode === "Dark" ? "#fff" : "#000",
                }}
              />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={openMenu}
              onClose={handleMenuClose}
              PaperProps={{
                style: {
                  backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
                  color: currentMode === "Dark" ? "#fff" : "#000",
                },
              }}
            >
              <MuiMenuItem
                onClick={() => {
                  setOpenDel(true);
                  handleMenuClose();
                }}
                disabled={!hasChangeOverDELETEAccess}
              >
                <DeleteIcon
                  style={{
                    fontSize: "20px",
                    color: "#f00",
                    marginRight: "8px",
                  }}
                />
                Delete
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpen(true);
                  handleMenuClose();
                }}
                disabled={!hasStepPOSTAccess}
              >
                <AddCircleOutlineOutlined
                  style={{ fontSize: "20px", marginRight: "8px" }}
                />
                Add Step
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenArrangeSteps(true);
                  handleMenuClose();
                }}
              >
                <ViewList style={{ fontSize: "20px", marginRight: "8px" }} />
                Arrange steps
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenReportConfirm(true);
                  handleMenuClose();
                }}
                disabled={!hasChangeOverReportPOSTAccess}
              >
                <ReceiptLong style={{ fontSize: "20px", marginRight: "8px" }} />
                Create Report
              </MuiMenuItem>
              <MuiMenuItem
                onClick={() => {
                  setOpenSop(true);
                  handleMenuClose();
                }}
                disabled={!data?.sop_url}
              >
                <PictureAsPdf
                  style={{
                    fontSize: "20px",
                    marginRight: "8px",
                    color: data?.sop_url
                      ? currentMode === "Dark"
                        ? "#fff"
                        : "red"
                      : "#ccc",
                  }}
                />
                View SOP
              </MuiMenuItem>
            </Menu>
            <IconButton
              onClick={() => setIsOpen(!isOpen)}
              style={currentMode === "Dark" ? { color: "white" } : {}}
            >
              {isOpen ? (
                <ExpandLessIcon style={{ fontSize: "20px" }} />
              ) : (
                <ExpandMoreIcon style={{ fontSize: "20px" }} />
              )}
            </IconButton>
          </div>
        </TableCell>
      </TableRow>
      <>
        {isOpen && (
          <TableRow
            sx={{
              "&:last-child td, &:last-child th": {
                border: 0,
              },
            }}
          >
            <TableCell
              style={
                currentMode === "Dark"
                  ? { color: "white", borderBottom: "none" }
                  : { color: "black", borderBottom: "none" }
              }
              className="subData"
              align="center"
              colSpan={4}
            >
              <div className={customCss.manualsContainer}>
                <Manuals
                  parent="ChangeOver"
                  moduleName="ChangeOverItem"
                  recData={data}
                  manual_id={data._id}
                  machineName={machineName}
                  name={data.comment}
                  userName={`${currentUser.fname} ${currentUser.lname}`}
                  onStepIndexChange={setCurrentStepIndex}
                />
              </div>
            </TableCell>
          </TableRow>
        )}
      </>
      <Dialog open={openDel}>
        <Delete onClose={() => setOpenDel(false)} onDelete={deleteData} />
      </Dialog>

      <Dialog open={openEdit} fullWidth>
        <DialogTitle>Edit ChangeOver - [{data.title}]</DialogTitle>
        <DialogContent>
          <EditChangeOver
            mid={data.mid}
            handleClose={() => setOpenEdit(false)}
            data={data}
            machineName={machineName}
            userName={`${currentUser.fname} ${currentUser.lname}`}
          />
        </DialogContent>
      </Dialog>

      <AddStepDialog
        open={open}
        setOpen={setOpen}
        data={data?.mid}
        machinetype={data?.type}
        manual_id={manual_id}
        machineName={machineName}
        handleHastageData={handleHastageData}
        steps={steps}
        setRefreshCount={setRefreshCount}
        refreshCount={refreshCount}
        currentMode={currentMode}
        parent={"Changeover"}
        setTempHashes={setTempHashes}
        hashArray={hashArray}
        temptemp={temptemp}
        currentStepIndex={currentStepIndex} 
      />

      <ReportDialog
        open={openReportConfirm}
        onClose={() => setOpenReportConfirm(false)}
        onConfirm={handleGenerateReport}
        title={data.title}
      />

      <SopPreviewDialog
        open={openSop}
        onClose={() => setOpenSop(false)}
        sopUrl={data?.sop_url}
        title={data?.title}
        currentMode={currentMode}
      />

      <ArrangeStepsDialog
        open={openArrangeSteps}
        onClose={() => setOpenArrangeSteps(false)}
        steps={data?.steps}
        currentMode={currentMode}
        maintenanceId={data?._id}
        parent="Changeover"
      />
    </>
  );
};

export default ChangeOverItem;