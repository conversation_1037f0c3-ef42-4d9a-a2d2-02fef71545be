import React, { useState } from "react";
import {
  Box,
  Button,
  Container,
  CssBaseline,
  Grid,
  InputLabel,
  Paper,
  TextField,
  Typography,
  FormControl,
  Chip,
  IconButton,
  InputAdornment,
  OutlinedInput,
  Dialog,
  DialogContent,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import arizonLogo from "../../assets/images/arizon.png";
import fulllogo from "../../assets/images/logoArizon.png";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { useAuth } from "../../hooks/AuthProvider";
import { toastMessage } from "../../tools/toast";
import { useUserActivity } from "../../hooks/cfr/userCfrProvider";
import SettingsIcon from "@mui/icons-material/Settings";
import ServerConfigDialog from "../../infrastructure/db/ServerConfigDialog";

const useStyles = makeStyles(() => ({
  passwordContainer: {
    backgroundColor: "#f5f5f5",
    color: "black",
    borderRadius: "10px",
    display: "flex",
    flexDirection: "column",
    position: "relative",
    alignContent: "center",
  },
  changePasswordTitle: {
    fontSize: "1.5rem",
  },
}));

const LoginMongoScreen = () => {
  const classes = useStyles();
  const userActivity = useUserActivity();
  const { login, users } = useAuth();
  const [userDetails, setUserDetails] = useState({ email: "", password: "" });
  const [errorEmail, setErrorEmail] = useState(false);
  const [errorPass, setErrorPass] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    let currentDateTime = new Date();

    if (userDetails.email.trim() === "") {
      toastMessage({ message: "Please enter a valid email" });
      setErrorEmail(true);
      setLoading(false);
      return;
    }

    setErrorEmail(false);

    if (userDetails.password.trim() === "") {
      toastMessage({ message: "Please enter a valid password" });
      setLoading(false);
      return;
    }

    try {
      await login(userDetails.email, userDetails.password);
      const value = sessionStorage.getItem("@user-creds");
      const parsedValue = JSON.parse(value);
      const data = {
        activity: "login",
        dateTime: currentDateTime,
        role: parsedValue.role,
        userName_email: parsedValue.email,
      };
      userActivity(data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container
      style={{
        display: "flex",
        alignContent: "center",
        justifyContent: "center",
        height: "100vh",
        width: "100vw",
      }}
    >
      <div style={{ display: "flex", alignItems: "center" }}>
        <CssBaseline />
        <Grid
          item
          component={Paper}
          elevation={6}
          borderRadius={"20px"}
          sm={4}
          md={7}
          sx={{
            backgroundRepeat: "no-repeat",
            background:
              "linear-gradient(90deg, rgba(35,29,105,0.9) 0%, rgba(115,72,149,1) 100%)",
            backgroundSize: "cover",
            backgroundPosition: "center",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flexDirection: "column",
            width: "70vw",
          }}
          padding={"6rem 8rem"}
        >
          <Grid
            item
            xs={12}
            sm={8}
            md={5}
            component={Paper}
            elevation={6}
            borderRadius={"20px"}
            sx={{
              background: "white",
              backgroundSize: "cover",
              backgroundPosition: "center",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              flexDirection: "column",
            }}
          >
            <Grid
              item
              xs={12}
              sm={8}
              md={5}
              component={Paper}
              elevation={6}
              borderRadius={"20px"}
              width={"30vw"}
              sx={{
                position: "relative",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                flexDirection: "column",
              }}
            >
              <Grid
                container
                justifyContent="center"
                component={Paper}
                borderRadius={"20px"}
                width={"30vw"}
                padding={"1rem"}
              >
                <Grid
                  item
                  xs={4}
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                >
                  <img width="120px" src={arizonLogo} alt="LOGO" />
                </Grid>
              </Grid>

              <Grid
                item
                xs={4}
                display="flex"
                flexDirection="column"
                justifyContent="center"
                alignItems="center"
              >
                <Grid
                  item
                  xs={4}
                  display="flex"
                  flexDirection="row"
                  justifyContent="center"
                  alignItems="center"
                >
                  <Typography
                    variant="h4"
                    component="div"
                    marginTop={"1rem"}
                    style={{
                      fontWeight: "bold",
                      color: "#40367C",
                      fontFamily: "Times New Roman, Times, serif",
                    }}
                  >
                    AR Smart
                  </Typography>
                  <Typography
                    fontSize={"0.8rem"}
                    component="div"
                    marginTop={"1rem"}
                    alignSelf={"flex-end"}
                    style={{
                      textAlign: "bottom",
                      color: "#757F88",
                      textShadow: "1px 1px 2px rgba(0, 0, 0, 0.3)",
                      fontFamily: "Arial, sans-serif",
                    }}
                  >
                    V 1.0.0
                  </Typography>
                </Grid>

                <Typography
                  variant="h6"
                  component="div"
                  style={{
                    textAlign: "center",
                    color: "#40367C",
                    textShadow: "1px 1px 2px rgba(0, 0, 0, 0.3)",
                    fontFamily: "Arial, sans-serif",
                  }}
                >
                  Arizon Systems Pvt. Ltd.
                </Typography>
              </Grid>
              <Grid
                item
                xs={12}
                sm={8}
                md={5}
                component={Paper}
                elevation={6}
                borderRadius={"20px"}
                width={"30vw"}
                sx={{
                  position: "relative",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  flexDirection: "column",
                  "&::before": {
                    content: '""',
                    position: "absolute",
                    top: 0,
                    right: 0,
                    bottom: 60,
                    left: 0,
                    background: `url(${fulllogo}) no-repeat center center`,
                    backgroundSize: "60%",
                    backgroundPosition: "center",
                    opacity: 0.5, // Adjust the opacity to make the background image faded
                    zIndex: 1, // Ensure the background image is behind the content
                    borderRadius: "20px", // Ensure the border radius matches the parent container
                    pointerEvents: "none", // Allow interaction with inner content
                  },
                  zIndex: 2, // Ensure the content is above the background image
                }}
                // maxWidth={"maxContent"}
              ></Grid>

              <Box
                sx={{
                  mb: 2,
                  mx: 4,
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  marginTop: "2rem",
                }}
              >
                <Box component="form" noValidate sx={{ mt: 1 }}>
                  <TextField
                    onChange={(e) => {
                      setUserDetails({ ...userDetails, email: e.target.value });
                      setErrorEmail(false);
                    }}
                    size="medium"
                    placeholder="User ID"
                    fullWidth
                    label="User ID"
                    required
                    autoFocus
                    margin="normal"
                    id="email"
                    error={errorEmail}
                    helperText={errorEmail ? "Invalid User Email!" : ""}
                  />
                  {/* {errorEmail && <Chip label="Invalid User Email!" sx={{ backgroundColor: "#E49393", color: "#E21818", mt: 2 }} />} */}

                  <FormControl sx={{ width: "100%" }} variant="outlined">
                    <InputLabel htmlFor="outlined-adornment-password">
                      Password
                    </InputLabel>
                    <OutlinedInput
                      id="outlined-adornment-password"
                      type={showPassword ? "text" : "password"}
                      size="medium"
                      label="Password"
                      endAdornment={
                        <InputAdornment position="end">
                          <IconButton
                            aria-label="toggle password visibility"
                            onClick={() => setShowPassword(!showPassword)}
                            onMouseDown={(e) => e.preventDefault()}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      }
                      onChange={(e) =>
                        setUserDetails({
                          ...userDetails,
                          password: e.target.value,
                        })
                      }
                      placeholder="Password"
                      style={{ backgroundColor: "white", borderRadius: "5px" }}
                      fullWidth
                    />
                  </FormControl>
                  {errorPass && (
                    <Chip
                      label="Password is Incorrect"
                      sx={{
                        backgroundColor: "#E49393",
                        color: "#E21818",
                        mt: 2,
                      }}
                    />
                  )}

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    disabled={
                      loading || !userDetails.email || !userDetails.password
                    }
                    onClick={handleSubmit}
                    sx={{
                      mt: 3,
                      mb: 2,
                      backgroundColor: "black",
                      color: "white",
                      textTransform: "none",
                    }}
                  >
                    {loading ? "Loading..." : "Log In"}
                  </Button>
                </Box>
              </Box>
            </Grid>
          </Grid>
          <Typography
            component="div"
            marginTop={"0.5rem"}
            textAlign={"end"}
            align="right"
            style={{
              fontSize: "12px",
              fontWeight: "normal",
              color: "#ffffff",
              marginBottom: "10px",
              fontFamily: "Arial, sans-serif",
            }}
          >
            @Arizon Systems Pvt. Ltd.
          </Typography>
        </Grid>
      </div>

      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="sm">
        <DialogContent>
          <ServerConfigDialog />
        </DialogContent>
      </Dialog>
    </Container>
  );
};

export default LoginMongoScreen;
