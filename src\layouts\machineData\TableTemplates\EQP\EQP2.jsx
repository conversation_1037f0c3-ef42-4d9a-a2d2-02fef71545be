import React, { useState, useMemo, useContext } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "../TableTempletes.css";
import PreviewIcon from "@mui/icons-material/Preview";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import TextField from "@mui/material/TextField";

import { useStateContext } from "../../../../context/ContextProvider";
import { db, storage } from "../../../../firebase";
import { companies, companyId_constant } from "../../../../constants/data";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../../../components/buttons/Buttons";

import { useStorageTablesFile } from "../../../../utils/useStorageTablesFile";
import { InputLabel, LinearProgress, Link, Typography } from "@mui/material";
import { Empty } from "antd";
import { FileDownload, ImageSearchOutlined } from "@mui/icons-material";
import {
  FileImageFilled,
  FilePdfFilled,
  PictureFilled,
} from "@ant-design/icons";
import { DeleteByUrl } from "../../../../utils/StorageOptions";
import { DropzoneArea } from "material-ui-dropzone";
import { FileManagerSelectorContext } from "../../../../services/fileManager/file-manager-select.context";
import FileSelector from "../../../FileSelector/screens/FileSelector";

const EQP2 = ({ rowData, type, machineName, fatDataDocId, useAt }) => {
  const [open, setOpen] = useState(false);
  const { currentMode, currentColorLight } = useStateContext();
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#161c24" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }}>
                Description
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Page Number
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Compliance (Complies OR Not)
              </TableCell>
              {useAt !== "tableMain" && (
                <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                  Image
                </TableCell>
              )}
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  sx={{
                    border: theme.borderDesign,
                    background: currentMode === "Dark" ? "#212b36" : "",
                  }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[2]}
                  </TableCell>
                  {useAt !== "tableMain" && (
                    <TableCell
                      sx={{ border: theme.borderDesign }}
                      align="center"
                    >
                      {data[3] === null ? (
                        "Add One"
                      ) : (
                        <>
                          {data[3]?.includes(".pdf") ? (
                            <FilePdfFilled
                              className="text-red-700"
                              onContextMenu={() =>
                                (window.location.href = data[3])
                              }
                              title="Press right click to open file"
                            />
                          ) : (
                            <PictureFilled
                              onContextMenu={() =>
                                (window.location.href = data[3])
                              }
                              title="Press right click to open file"
                            />
                          )}
                        </>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
          <EditTableRow
            rowDataSelected={rowDataSelected}
            type={type}
            fatDataDocId={fatDataDocId}
            machineName={machineName}
            handleClose={handleClose}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

const EditTableRow = ({
  rowDataSelected,
  type,
  machineName,
  fatDataDocId,
  handleClose,
}) => {
  // console.log(rowDataSelected, tableType)
  const { currentMode } = useStateContext();
  const [desc, setDesc] = useState(rowDataSelected[0]);
  const [page_number, setPage_number] = useState(rowDataSelected[1]);
  const [compliance, setCompliance] = useState(rowDataSelected[2]);
  const [urlData, setUrlData] = useState(rowDataSelected[3]);
  const [fileType, setFileType] = useState("");
  const [file, setFile] = useState(null);
  const { fileUrl, changeFileUrl } = useContext(FileManagerSelectorContext);
  const { progress, url } = useStorageTablesFile(file);
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];
  const [index, setIndex] = useState(rowDataSelected[5]);
  const [fType, setFType] = useState("index" in rowDataSelected ? true : false); // this may not work
  console.log("eqp2:", rowDataSelected);

  const handleChangeImage = (file) => {
    let selectedFile = file[0];
    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        if (url) {
          DeleteByUrl(url);
        }
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };

  const handleUpdateRow = () => {
    if (fType && isNaN(parseInt(index))) {
      toastMessageWarning({ message: "The input of index should be a Number" });
      return;
    }
    let data = {
      desc: desc,
      page_number: page_number,
      compliance: compliance,
      index: index,
      url: fileUrl === null ? urlData : fileUrl,
    };
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(type)
    //   .doc(fatDataDocId)
    //   .collection("tableEQP2")
    //   .doc(rowDataSelected[4])
    //   .update(fType ? {...data, index} : data)
    //   .then(() => {
    //     setFile(null)
    //     toastMessageSuccess({ message: "Row updated Successfully" });
    //     handleClose();
    //   })
    //   .catch((e) => {
    //     toastMessageWarning({ message: "Error ", e });
    //   });
  };

  const handleCancel = () => {
    if (url) {
      DeleteByUrl(url);
    }
    handleClose();
  };
  const handleDeleteDropZone = (url) => {
    DeleteByUrl(url);
    setFile(null);
  };

  return (
    <>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          <div className="flex justify-between py-2">
            <div className="w-3/12">
              <TextField
                label="Description"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={desc}
                onChange={(e) => setDesc(e.target.value)}
              />
            </div>
            <div className="w-3/12">
              <TextField
                fullWidth
                label="Page Number"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={page_number}
                onChange={(e) => setPage_number(e.target.value)}
              />
            </div>
            <div className="w-3/12">
              <TextField
                label="Compliance"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={compliance}
                onChange={(e) => setCompliance(e.target.value)}
              />
            </div>
            {/* {fType */}
            {(url?.includes(".pdf") ||
              fileUrl?.includes(".pdf") ||
              urlData?.includes(".pdf")) && (
              <div className="w-2/12">
                <TextField
                  label="Index"
                  id="outlined-size-small"
                  defaultValue="Na"
                  size="small"
                  value={index}
                  onChange={(e) => setIndex(e.target.value)}
                />
              </div>
            )}
          </div>
        </DialogContentText>
        <FileSelector setFType={setFType} />
        <Typography sx={{ ml: "20px" }} align="left">
          {" "}
          <PreviewIcon /> Preview
        </Typography>
        <img width="250px" src={urlData} />
      </DialogContent>
      <DialogActions>
        <ButtonBasic buttonTitle="Update" onClick={handleUpdateRow} />
      </DialogActions>
    </>
  );
};

export default EQP2;
