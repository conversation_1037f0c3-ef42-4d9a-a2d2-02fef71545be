/*
  .env will now [2025-04-01] be untracked by version control due to the possibility of running the backend servers on different ports
*/

// const ip = window.localStorage.getItem("server_ip");
const baseUrl = `http://${process.env.REACT_APP_IP}:${process.env.REACT_APP_BACKEND_PORT}/api/v1`;

const baseStorageUrl = `http://${process.env.REACT_APP_IP}:${process.env.REACT_APP_STORAGE_PORT}`;

const baseForecastUrl = `http://${process.env.REACT_APP_IP}:${process.env.REACT_APP_FORECAST_PORT}`;

const baseCmsUrl = `https://${process.env.REACT_APP_CMS_IP}${
  process.env.REACT_APP_CMS_PORT === "443"
    ? ""
    : `:${process.env.REACT_APP_CMS_PORT}`
}`;
const baseSmartScanUrl = `https://${process.env.REACT_APP_SMART_SCAN_IP}:${process.env.REACT_APP_SMART_SCAN_PORT}`;

export const dbConfig = {
  url: baseUrl,
  url_storage: baseStorageUrl,
  url_python: baseForecastUrl,
  url_cms: baseCmsUrl,
  url_smart_scan: baseSmartScanUrl,
};

/*
var ip = window.localStorage.getItem("server_ip");
export const dbConfig = {
  url: ip ? `http://${ip}:8000/api/v1` : `http://localhost:8000/api/v1`,
  url_storage: ip ? `http://${ip}:3009` : `http://localhost:3009`,
  url_python: ip ? `http://${ip}:8080` : `http://localhost:8080`
};
*/
