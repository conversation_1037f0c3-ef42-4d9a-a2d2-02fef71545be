// @mixin row {
// 	display: flex;
// 	align-items: center;
// 	justify-content: space-between;
// }
// @mixin cell {
// 	display: flex;
// 	align-items: center;
// 	justify-content: flex-start;
// 	padding: 16px;
// 	font-size: 13.5px;
// 	text-transform: uppercase;
// 	opacity: 0.8;
// 	font-weight: 700;
// 	width: 250px;
// }
// .serialized_table {
// 	width: 100%;
// 	color: #344767;
// 	text-align: left;
// 	box-sizing: border-box;
// 	.serialized_tableHead {
// 		width: 100%;
// 		border-bottom: 1px solid #ccc;
// 		.serialized_tableRow {
// 			@include row;
// 			width: 100%;
// 			.serialized_tableCell {
// 				@include cell;
// 				text-transform: none;
// 			}
// 		}
// 	}
// 	.serialized_tableBody {
// 		width: 100%;
// 		display: flex;
// 		justify-content: center;
// 		align-items: flex-start;
// 		flex-direction: column;
// 		.serialized_tableRow {
// 			@include row;
// 			width: 100%;
// 			.serialized_tableCell {
// 				@include cell;
// 				gap: 16px;
// 				opacity: 1;
// 				font-weight: 500;
// 				font-size: 12px;
// 				text-transform: none;
// 			}
// 		}
// 	}
// }
