import {
  <PERSON>,
  <PERSON>rid,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TextField,
  InputAdornment,
  IconButton,
  CircularProgress,
} from "@mui/material";
import { useState, useEffect } from "react";
import { getAllLDAPUsersUsingConfig } from "./Users";
import { toast } from "react-toastify";
import { makeStyles } from "@mui/styles";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import PropTypes from "prop-types";
import { toastMessageWarning } from "../../tools/toast";

const INITIAL_CONFIG = {
  username: "",
  password: "",
};

const useStyles = makeStyles(() => ({
  passwordContainer: {
    backgroundColor: "#f5f5f5",
    color: "black",
    borderRadius: "10px",
    display: "flex",
    flexDirection: "column",
    position: "relative",
    alignContent: "center",
  },
  changePasswordTitle: {
    fontSize: "1.5rem",
  },
}));

const LdapConfigForm = ({ setLdapUsers, setLoading, setGlobalConfigState }) => {
  const [configState, setConfigState] = useState(INITIAL_CONFIG);
  const [submitting, setSubmitting] = useState(false);
  const classes = useStyles();
  const [showPassword, setShowPassword] = useState(false);
  const [authenticatedUser, setAuthenticatedUser] = useState(null);

  // Logout handler
  const handleLogout = () => {
    setAuthenticatedUser(null);
    setConfigState(INITIAL_CONFIG);
    setGlobalConfigState(INITIAL_CONFIG);
    setLdapUsers([]); // Optionally clear users
  };

  // Clear authentication on unmount (simulate logout)
  useEffect(() => {
    return () => {
      handleLogout();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleChange = (e) => {
    setConfigState((prev) => ({
      ...prev,
      [e.target.name]: e.target.value.trim(),
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setGlobalConfigState(configState);
    try {
      await getAllLDAPUsersUsingConfig(configState, setLdapUsers);
      setAuthenticatedUser(configState.username); // Save username after auth
      setConfigState(INITIAL_CONFIG);
    } catch (error) {
      console.error("LDAP Error:", error?.response?.data || error.message);
      // toast.error(error.message || "Failed to fetch LDAP users.");
    }
    setSubmitting(false);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        mb: 2,
      }}
    >
      <Typography
        variant="h5"
        sx={{
          mb: 2,
          textAlign: "left",
          fontWeight: 600,
          color: "#424242",
        }}
      >
        Add new users to AR Smart
      </Typography>
      {authenticatedUser ? (
        <Box
          sx={{
            width: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 2,
            p: 2,
            backgroundColor: "#f5f5f5",
            borderRadius: "8px",
          }}
        >
          <Typography variant="subtitle1" sx={{ color: "#007bff" }}>
            Authenticated as: <b>{authenticatedUser}</b>
          </Typography>
          <Button
            variant="outlined"
            color="error"
            onClick={handleLogout}
            sx={{ ml: 2 }}
          >
            Logout
          </Button>
        </Box>
      ) : (
        <Box
          sx={{
            width: "100%",
            display: "flex",
            justifyContent: "center",
          }}
        >
          <form
            style={{
              maxWidth: "720px",
              width: "100%",
            }}
            onSubmit={handleSubmit}
          >
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={5}>
                <TextField
                  id="mui-textfield-username"
                  label="Username"
                  variant="outlined"
                  size="small"
                  name="username"
                  autoComplete="username"
                  onChange={handleChange}
                  value={configState.username}
                  fullWidth
                  sx={{
                    backgroundColor: "white",
                    borderRadius: "5px",
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={5}>
                <TextField
                  id="mui-textfield-password"
                  label="Password"
                  variant="outlined"
                  size="small"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  onChange={handleChange}
                  value={configState.password}
                  fullWidth
                  sx={{
                    backgroundColor: "white",
                    borderRadius: "5px",
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={() => setShowPassword(!showPassword)}
                          onMouseDown={handleMouseDownPassword}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={2}>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={
                    submitting ||
                    configState.username === "" ||
                    configState.password === ""
                  }
                  sx={{
                    width: "120%",
                    bgcolor: "#007bff",
                    "&:hover": { bgcolor: "#0056b3" },
                  }}
                >
                  {submitting ? (
                    <CircularProgress size={20} sx={{ color: "white" }} />
                  ) : (
                    "Authenticate"
                  )}
                </Button>
              </Grid>
            </Grid>
          </form>
        </Box>
      )}
    </Box>
  );
};

LdapConfigForm.propTypes = {
  setLdapUsers: PropTypes.func,
  setLoading: PropTypes.func,
  setGlobalConfigState: PropTypes.func,
};

export default LdapConfigForm;
