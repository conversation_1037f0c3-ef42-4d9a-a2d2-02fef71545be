// import {
//   Box,
//   Button,
//   FormControl,
//   InputLabel,
//   LinearProgress,
//   MenuItem,
//   Select,
//   TextField,
// } from "@mui/material";
// import axios from "axios";
// import React, { useEffect, useState } from "react";
// import { companies, companyId_constant, machines } from "../../constants/data";
// import { db } from "../../firebase";
// import { convertBase64 } from "../../hooks/useBase64";
// import { dbConfig } from "../../infrastructure/db/db-config";
// import { useMongoRefresh } from "../../services/mongo-refresh.context";
// import {
//   toastMessage,
//   toastMessageSuccess,
//   toastMessageWarning,
// } from "../../tools/toast";
// import { firebaseLooper } from "../../tools/tool";
// import { useStorage } from "../../utils/useStorage";
// import { ButtonBasic, ButtonBasicCancel } from "../buttons/Buttons";
// import { useAuth } from "../../hooks/AuthProvider";
// import { useEditMachineCfr } from "../../hooks/cfr/machineCfrProvider";
// import GetPreviewComponent from "../../components/commons/getPreview.component";
// const EditLiveData = ({ onClose, annData }) => {
//   const editlivedatacfr = useEditMachineCfr();
//   const { currentUser } = useAuth();

//   const [annoData, setAnnoData] = useState({ ...annData });

//   const [fileurl, setFileurl] = useState(null);
//   const [newimage, setNewImage] = useState("");
//   const types = ["image/png", "image/jpeg", "image/jpg"];
//   const { refreshCount, setRefreshCount } = useMongoRefresh();
//   console.log("adaaaaaa,", annoData);

//   const uploadImage = async (e) => {
//     const file = e.target.files[0];
//     const base64 = await convertBase64(file);
//     if (file) {
//       if (types.includes(file.type)) {
//         setNewImage(file);
//         setFileurl(base64);
//       } else {
//         toastMessage({
//           message: "Incorrect Format - Please use (PNG , JPG , JPEG)",
//         });
//       }
//     }
//   };

//   function getPreview(url) {
//     // console.log(url)
//     try {
//       return <img width="450" src={url} />;
//     } catch (error) {
//       return console.log(error);
//     }
//   }

//   const { progress, url } = useStorage(fileurl);
//   const handleSubmit = async (e) => {
//     if (newimage?.length) {
//       await axios
//         .post(`${dbConfig?.url_storage}/deleteImage`, {
//           file_name: annoData?.imgUrl,
//         })
//         .then((res1) => {
//           console.log(res1.data?.message, "updated successfully");
//         })
//         .catch((err) => {
//           console.log("delte file from storage err:", err);
//         });
//     }

//     const date = new Date();
//     const data2 = {
//       activity: "live data edited",
//       dateTime: date,

//       description: "a live data is edited",
//       machine: annData.mid,
//       module: "Live data",
//       username: currentUser.username,
//     };
//     const data = { ...annoData };

//     if (
//       data.title.trim() === "" ||
//       data.desc.trim() === "" ||
//       data.type.trim() === ""
//     ) {
//       return toastMessageWarning({ message: "Please fill all the details" });
//     }

//     if (newimage) {
//       let fd = new FormData();
//       // console.log("newimage", newimage);
//       fd.append("image", newimage);
//       console.log(fd, "fd");
//       var restemp = await axios
//         .post(`${dbConfig?.url_storage}/upload`, fd, {})
//         .then((res1) => {
//           console.log("storage:", res1);
//           return res1;
//         })
//         .catch((err) => {
//           console.log(err);
//           toastMessage({ message: err.message });
//         });
//     }

//     await axios
//       .put(`${dbConfig.url}/imageAnnotationModules/${annData._id}`, {
//         ...annoData,
//         img_url: newimage ? restemp?.data?.data : annoData?.img_url,
//       })
//       .then(() => {
//         toastMessageSuccess({ message: "Updated project successfully! " });
//         editlivedatacfr(data2);
//         onClose();
//         setNewImage("");
//         setFileurl("");
//         setRefreshCount(refreshCount + 1);
//       })
//       .catch((err) => {
//         console.log(err);
//       });
//   };

//   return (
//     <Box>
//       <TextField
//         value={annoData.title}
//         onChange={(e) => setAnnoData({ ...annoData, title: e.target.value })}
//         sx={{ mb: 3, mt: 3 }}
//         label="Title"
//         fullWidth
//         placeholder="eg: Vaccum PID"
//       />

//       <TextField
//         value={annoData.desc}
//         sx={{ mb: 3 }}
//         fullWidth
//         onChange={(e) => setAnnoData({ ...annoData, desc: e.target.value })}
//         rows={4}
//         multiline
//         label="Description"
//         placeholder="eg: This Module belings to Machine X with Cap 2300 hP"
//       />

//       <FormControl fullWidth>
//         <InputLabel>Type</InputLabel>
//         <Select
//           value={annoData.type}
//           onChange={(e) => setAnnoData({ ...annoData, type: e.target.value })}
//           label="Type"
//           sx={{ mb: 3 }}
//         >
//           <MenuItem value="process">Process</MenuItem>
//           <MenuItem value="service">Service</MenuItem>
//         </Select>
//       </FormControl>

//       <InputLabel sx={{ mb: 1 }}>Image</InputLabel>
//       <input
//         style={{ marginBottom: "20px" }}
//         onChange={uploadImage}
//         type="file"
//       />
//       <LinearProgress variant="determinate" value={progress} />

//       <div
//         style={{ marginTop: "10px", display: "flex", justifyContent: "center" }}
//       >
//         {/*getPreview(
//           fileurl?.length > 0
//           ? fileurl
//           : `${dbConfig?.url_storage}/${annData?.imgUrl}`
//         )*/}
//         <GetPreviewComponent
//           sourceUrl={
//             fileurl?.length > 0
//               ? fileurl
//               : `${dbConfig?.url_storage}/${annData?.img_url}`
//           }
//           fileFormat={"image"}
//           previewImageStyle={{ width: "300px" }}
//         />
//       </div>

//       <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
//         {/* <Button onClick={onClose} variant="contained" color="error">cancel</Button> */}
//         <ButtonBasicCancel buttonTitle="Cancel" onClick={onClose} />
//         {/* <Button onClick={handleSubmit} variant="contained" color="primary" >Update Project</Button> */}
//         <ButtonBasic buttonTitle="Update Live Data" onClick={handleSubmit} />
//       </Box>
//     </Box>
//   );
// };

// export default EditLiveData;



// import {
//   Box,
//   Button,
//   FormControl,
//   InputLabel,
//   LinearProgress,
//   MenuItem,
//   Select,
//   TextField,
// } from "@mui/material";
// import axios from "axios";
// import React, { useEffect, useState } from "react";
// import { companies, companyId_constant, machines } from "../../constants/data";
// import { db } from "../../firebase";
// import { convertBase64 } from "../../hooks/useBase64";
// import { dbConfig } from "../../infrastructure/db/db-config";
// import { useMongoRefresh } from "../../services/mongo-refresh.context";
// import {
//   toastMessage,
//   toastMessageSuccess,
//   toastMessageWarning,
// } from "../../tools/toast";
// import { firebaseLooper } from "../../tools/tool";
// import { useStorage } from "../../utils/useStorage";
// import { ButtonBasic, ButtonBasicCancel } from "../buttons/Buttons";
// import { useAuth } from "../../hooks/AuthProvider";
// import { useEditMachineCfr } from "../../hooks/cfr/machineCfrProvider";
// import GetPreviewComponent from "../../components/commons/getPreview.component";
// const EditLiveData = ({ onClose, annData }) => {
//   const editlivedatacfr = useEditMachineCfr();
//   const { currentUser } = useAuth();

//   const [annoData, setAnnoData] = useState({ ...annData });

//   const [fileurl, setFileurl] = useState(null);
//   const [newimage, setNewImage] = useState("");
//   const types = ["image/png", "image/jpeg", "image/jpg"];
//   const { refreshCount, setRefreshCount } = useMongoRefresh();
//   console.log("adaaaaaa,", annoData);

//   const uploadImage = async (e) => {
//     const file = e.target.files[0];
//     const base64 = await convertBase64(file);
//     if (file) {
//       if (types.includes(file.type)) {
//         setNewImage(file);
//         setFileurl(base64);
//       } else {
//         toastMessage({
//           message: "Incorrect Format - Please use (PNG , JPG , JPEG)",
//         });
//       }
//     }
//   };

//   function getPreview(url) {
//     // console.log(url)
//     try {
//       return <img width="450" src={url} />;
//     } catch (error) {
//       return console.log(error);
//     }
//   }

//   const { progress, url } = useStorage(fileurl);
//   const handleSubmit = async (e) => {
//     if (newimage?.length) {
//       await axios
//         .post(`${dbConfig?.url_storage}/deleteImage`, {
//           file_name: annoData?.img_url,
//         })
//         .then((res1) => {
//           console.log(res1.data?.message, "updated successfully");
//         })
//         .catch((err) => {
//           console.log("delte file from storage err:", err);
//         });
//     }

//     const date = new Date();
//     const data2 = {
//       activity: "live data edited",
//       dateTime: date,

//       description: "a live data is edited",
//       machine: annData.mid,
//       module: "Live data",
//       username: currentUser.username,
//     };
//     const data = { ...annoData };

//     if (
//       data.title.trim() === "" ||
//       data.desc.trim() === "" ||
//       data.type.trim() === ""
//     ) {
//       return toastMessageWarning({ message: "Please fill all the details" });
//     }

//     if (newimage) {
//       let fd = new FormData();
//       // console.log("newimage", newimage);
//       fd.append("image", newimage);
//       console.log(fd, "fd");
//       var restemp = await axios
//         .post(`${dbConfig?.url_storage}/upload`, fd, {})
//         .then((res1) => {
//           console.log("storage:", res1);
//           return res1;
//         })
//         .catch((err) => {
//           console.log(err);
//           toastMessage({ message: err.message });
//         });
//     }

//     await axios
//       .put(`${dbConfig.url}/imageAnnotationModules/${annData._id}`, {
//         ...annoData,
//         img_url: newimage ? restemp?.data?.data : annoData?.img_url,
//       })
//       .then(() => {
//         toastMessageSuccess({ message: "Updated project successfully! " });
//         editlivedatacfr(data2);
//         onClose();
//         setNewImage("");
//         setFileurl("");
//         setRefreshCount(refreshCount + 1);
//       })
//       .catch((err) => {
//         console.log(err);
//       });
//   };

//   return (
//     <Box>
//       <TextField
//         value={annoData.title}
//         onChange={(e) => setAnnoData({ ...annoData, title: e.target.value })}
//         sx={{ mb: 3, mt: 3 }}
//         label="Title"
//         fullWidth
//         placeholder="eg: Vaccum PID"
//       />

//       <TextField
//         value={annoData.desc}
//         sx={{ mb: 3 }}
//         fullWidth
//         onChange={(e) => setAnnoData({ ...annoData, desc: e.target.value })}
//         rows={4}
//         multiline
//         label="Description"
//         placeholder="eg: This Module belings to Machine X with Cap 2300 hP"
//       />

//       <FormControl fullWidth>
//         <InputLabel>Type</InputLabel>
//         <Select
//           value={annoData.type}
//           onChange={(e) => setAnnoData({ ...annoData, type: e.target.value })}
//           label="Type"
//           sx={{ mb: 3 }}
//         >
//           <MenuItem value="process">Process</MenuItem>
//           <MenuItem value="service">Service</MenuItem>
//         </Select>
//       </FormControl>

//       <InputLabel sx={{ mb: 1 }}>Image</InputLabel>
//       <input
//         style={{ marginBottom: "20px" }}
//         onChange={uploadImage}
//         type="file"
//       />
//       <LinearProgress variant="determinate" value={progress} />

//       <div
//         style={{ marginTop: "10px", display: "flex", justifyContent: "center" }}
//       >
//         {/*getPreview(
//           fileurl?.length > 0
//           ? fileurl
//           : `${dbConfig?.url_storage}/${annData?.imgUrl}`
//         )*/}
//         <GetPreviewComponent
//           sourceUrl={
//             fileurl?.length > 0
//               ? fileurl
//               : `${dbConfig?.url_storage}/${annData?.img_url}`
//           }
//           fileFormat={"image"}
//           previewImageStyle={{ width: "300px" }}
//         />
//       </div>

//       <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
//         {/* <Button onClick={onClose} variant="contained" color="error">cancel</Button> */}
//         <ButtonBasicCancel buttonTitle="Cancel" onClick={onClose} />
//         {/* <Button onClick={handleSubmit} variant="contained" color="primary" >Update Project</Button> */}
//         <ButtonBasic buttonTitle="Update Live Data" onClick={handleSubmit} />
//       </Box>
//     </Box>
//   );
// };

// export default EditLiveData;








import {
  Box,
  FormControl,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import axios from "axios";
import React, { useState } from "react";
import { convertBase64 } from "../../hooks/useBase64";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import { ButtonBasic, ButtonBasicCancel, SubmitButtons } from "../buttons/Buttons";
import { useAuth } from "../../hooks/AuthProvider";
import { useEditMachineCfr } from "../../hooks/cfr/machineCfrProvider";
import GetPreviewComponent from "../../components/commons/getPreview.component";

const EditLiveData = ({ onClose, annData }) => {
  const editlivedatacfr = useEditMachineCfr();
  const { currentUser } = useAuth();
  const [annoData, setAnnoData] = useState({ ...annData });

  const [fileurl, setFileurl] = useState(null); // base64 for preview
  const [newimage, setNewImage] = useState(null); // actual file object
  const [progress, setProgress] = useState(0);

  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const types = ["image/png", "image/jpeg", "image/jpg"];

  const uploadImage = async (e) => {
    const file = e.target.files[0];
    if (file) {
      if (types.includes(file.type)) {
        const base64 = await convertBase64(file);
        setNewImage(file);
        setFileurl(base64);
      } else {
        toastMessage({
          message: "Incorrect Format - Please use PNG, JPG, or JPEG",
        });
      }
    }
  };

  const handleSubmit = async () => {
    try {
      if (
        annoData.title.trim() === "" ||
        annoData.desc.trim() === "" ||
        annoData.type.trim() === ""
      ) {
        return toastMessageWarning({ message: "Please fill all the details" });
      }

      // Delete previous image if a new one is selected
      if (newimage) {
        await axios.post(`${dbConfig?.url_storage}/deleteImage`, {
          file_name: annoData?.img_url,
        });
      }

      // Upload new image if selected
      let newImageUrl = annoData.img_url;
      if (newimage) {
        const fd = new FormData();
        fd.append("image", newimage);

        const response = await axios.post(
          `${dbConfig?.url_storage}/upload`,
          fd,
          {
            onUploadProgress: (progressEvent) => {
              const percentCompleted = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              );
              setProgress(percentCompleted);
            },
          }
        );
        newImageUrl = response.data?.data;
      }

      // Save changes to backend
      await axios.put(`${dbConfig.url}/imageAnnotationModules/${annData._id}`, {
        ...annoData,
        img_url: newImageUrl,
      });

      // Log activity
      const activityLog = {
        activity: "live data edited",
        dateTime: new Date(),
        description: "A live data is edited",
        machine: annData.mid,
        module: "Live data",
        username: currentUser.username,
      };

      editlivedatacfr(activityLog);
      toastMessageSuccess({ message: "Updated project successfully!" });
      onClose();
      setNewImage(null);
      setFileurl(null);
      setProgress(0);
      setRefreshCount(refreshCount + 1);
    } catch (err) {
      console.error(err);
      toastMessage({ message: "Something went wrong while saving." });
    }
  };

  return (
    <Box>
      <TextField
        value={annoData.title}
        onChange={(e) => setAnnoData({ ...annoData, title: e.target.value })}
        sx={{ mb: 3, mt: 3 }}
        label="Title"
        fullWidth
        placeholder="eg: Vaccum PID"
      />

      <TextField
        value={annoData.desc}
        sx={{ mb: 3 }}
        fullWidth
        onChange={(e) => setAnnoData({ ...annoData, desc: e.target.value })}
        rows={4}
        multiline
        label="Description"
        placeholder="eg: This Module belongs to Machine X with Cap 2300 HP"
      />

      <FormControl fullWidth>
        <InputLabel>Type</InputLabel>
        <Select
          value={annoData.type}
          onChange={(e) => setAnnoData({ ...annoData, type: e.target.value })}
          label="Type"
          sx={{ mb: 3 }}
        >
          <MenuItem value="process">Process</MenuItem>
          <MenuItem value="service">Service</MenuItem>
        </Select>
      </FormControl>

      <InputLabel sx={{ mb: 1 }}>Image</InputLabel>
      <input
        style={{ marginBottom: "20px" }}
        onChange={uploadImage}
        type="file"
      />
      <LinearProgress
        variant="determinate"
        value={progress}
        sx={{ mb: 2 }}
      />

      <div
        style={{ marginTop: "10px", display: "flex", justifyContent: "center" }}
      >
        <GetPreviewComponent
          sourceUrl={
            fileurl?.length > 0
              ? fileurl
              : `${dbConfig?.url_storage}/${annData?.img_url}`
          }
          fileFormat={"image"}
          previewImageStyle={{ width: "300px" }}
        />
      </div>

      <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
        <ButtonBasicCancel buttonTitle="Cancel" onClick={onClose} />
        <SubmitButtons buttonTitle="Update Live Data" onClick={handleSubmit} />
      </Box>
    </Box>
  );
};

export default EditLiveData;

