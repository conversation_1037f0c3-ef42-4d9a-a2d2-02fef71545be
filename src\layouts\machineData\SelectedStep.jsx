import React from "react";
import {
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Typography,
  CardMedia,
  TextareaAutosize,
} from "@mui/material";
import AttachFileIcon from "@mui/icons-material/AttachFile";

function SelectedStep({
  item,
  currentMode,
  setEnlarge,
  setEValue,
  dbConfig,
  comment,
  setComment,
  uOrA,
}) {
  return (
    <Grid container spacing={2}>
      <Grid item xs={7}>
        <Card>
          <CardContent>
            <Typography variant="h5" component="div">
              {item.title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {item.desc}
            </Typography>
          </CardContent>
          <CardActions>
            <Button
              onClick={() => {
                setEnlarge(true);
                setEValue(item.url);
              }}
              variant="outlined"
              color="primary"
            >
              View
            </Button>
          </CardActions>
        </Card>
      </Grid>
      <Grid item xs={5}>
        <Card>
          <CardContent>
            <Typography variant="h6" component="div">
              Comment:
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {item.comment}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Last Updated/Commented By: {item.userName?.toUpperCase()}
            </Typography>
            <TextareaAutosize
              maxRows={3}
              minRows={3}
              style={{
                background: "transparent",
                width: "100%",
                borderRadius: "5px",
                border:
                  currentMode === "Dark"
                    ? "1px solid rgba(255, 255, 255, .23)"
                    : "1px solid rgba(0, 0, 0, .23)",
                padding: "16px 12px",
              }}
              id="name"
              aria-label="Comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
            />
          </CardContent>
          <CardActions>
            <Button
              onClick={() => uOrA(item)}
              variant="outlined"
              color="primary"
            >
              {comment ? "Update Comment" : "Add Comment"}
            </Button>
          </CardActions>
        </Card>
      </Grid>
      {item.format === "image" && (
        <Grid item xs={7}>
          <Card>
            <CardMedia
              component="img"
              alt={item.url}
              height="350"
              image={`${dbConfig.url_storage}/${item.url}`}
            />
          </Card>
        </Grid>
      )}
      {item.format === "video" && (
        <Grid item xs={7}>
          <Card>
            <CardMedia
              component="video"
              alt={item.url}
              height="350"
              src={`${dbConfig.url_storage}/${item.url}`}
              controls
            />
          </Card>
        </Grid>
      )}
      {item.format === "audio" && (
        <Grid item xs={7}>
          <Card>
            <CardMedia
              component="audio"
              src={`${dbConfig.url_storage}/${item.url}`}
              controls
            />
          </Card>
        </Grid>
      )}
      <Grid item xs={5}>
        <Card>
          <CardContent>
            <Typography variant="h6" component="div">
              Attachments - {item.images ? item.images.length || 0 : 0}
            </Typography>
            <div style={{ display: "flex" }}>
              {item.images &&
                item.images.map((againOptions, index) => (
                  <div key={index} className="my-5">
                    <a
                      href={`${dbConfig.url_storage}/images/${againOptions}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Button
                        variant="outlined"
                        color="primary"
                        startIcon={<AttachFileIcon />}
                      >
                        Attachment {index + 1}
                      </Button>
                    </a>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}

export default SelectedStep;
