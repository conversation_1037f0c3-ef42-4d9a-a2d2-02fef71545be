import { Box, Container, Typography } from "@mui/material";
import React from "react";
import ReportHead from "../Head/ReportHead";

const indexItems = [
  "PRE-APPROVAL SIGNATURES",
  "PURPOSE",
  "SCOPE",
  "PLC INPUT / OUTPUT SIMULATION TEST",
  "DEVIATION, IMPACT AND CORRECTIVE ACTION SHEET",
  "POST APPROVAL SIGNATURES",
];

const IndexPage = () => {
  return (
    <Container sx={{ width: "80%" }}>
      <Box sx={{ padding: 4, border: "1px solid black", marginTop: "10px" }}>
        <ReportHead />
        <br />
        <Typography sx={{ fontWeight: "bold" }} align="center">
          TABLE OF CONTENTS
        </Typography>
        <br />
        <br />
        {indexItems.map((item, i) => (
          <div style={{ display: "flex", justifyContent: "left" }}>
            <b style={{ marginRight: "30px", marginBottom: "30px" }}>
              {i + 1}.0
            </b>
            <Typography>{item}</Typography>
          </div>
        ))}
      </Box>
    </Container>
  );
};

export default IndexPage;
