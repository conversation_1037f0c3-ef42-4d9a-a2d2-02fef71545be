import { I<PERSON><PERSON>utton, ListItemIcon, Menu } from "@mui/material";
import React, { useEffect, useState } from "react";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import SpeakerNotesIcon from "@mui/icons-material/SpeakerNotes";
import NoteAddIcon from "@mui/icons-material/NoteAdd";
import { Dialog, DialogContent, DialogTitle, MenuItem } from "@mui/material";
import NotesDialog from "./NotesDialog";
import NotesPreview from "./NotesPreview";
// import { db } from "../../../firebase";
import { companies, companyId_constant } from "../../../constants/data";
import { firebaseLooper } from "../../../tools/tool";

function NotesButtons() {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const [openAddNotes, setOpenAddNotes] = React.useState(false);
  const [openPreview, setOpenPreview] = React.useState(false);
  const [notes, setNotes] = useState([]);

  useEffect(() => {
    // db.collection(companies)
    // .doc(companyId_constant).collection('cfrNotes')
    // .onSnapshot(snap => {
    //     const data = firebaseLooper(snap)
    //     setNotes(data)
    // })
  }, []);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <IconButton
        aria-controls={open ? "basic-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
      >
        <MoreVertIcon />
      </IconButton>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
        style={{ display: "flex", flexDirection: "column" }}
      >
        <MenuItem onClick={() => setOpenAddNotes(true)}>
          <ListItemIcon>
            <NoteAddIcon />
          </ListItemIcon>
          Add New Note
        </MenuItem>
        <MenuItem onClick={() => setOpenPreview(true)}>
          <ListItemIcon>
            <SpeakerNotesIcon />
          </ListItemIcon>
          Notes
        </MenuItem>
      </Menu>

      <Dialog
        open={openAddNotes}
        onClose={() => setOpenAddNotes(false)}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>Add New Notes</DialogTitle>
        <DialogContent>
          <NotesDialog handleClose={() => setOpenAddNotes(false)} />
        </DialogContent>
      </Dialog>
      <Dialog
        open={openPreview}
        onClose={() => setOpenPreview(false)}
        fullWidth
        maxWidth="lg"
      >
        <DialogTitle>Notes</DialogTitle>
        <DialogContent>
          <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {notes.map((item, idx) => (
              <NotesPreview note={item} key={item.id + idx} />
            ))}

            <NotesPreview />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default NotesButtons;
