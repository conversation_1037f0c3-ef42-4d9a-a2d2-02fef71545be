import { createContext, useEffect, useState } from "react";
import {
  alluser,
  companies,
  companyId_constant,
  users,
} from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import emailjs from "@emailjs/browser";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { v4 as uuidv4 } from "uuid";

export const AddUserContext = createContext();

const newUserId = uuidv4();

const AddUserProvider = ({ children }) => {
  const [companyId, setCompanyId] = useState();
  const companyIds = companyId ? companyId : companyId_constant; //This component is used in two places , This is required for creating from LSI
  const [allUsersList, setAllUsersList] = useState([]); // all emails
  const [emailMatches, setEmailMatches] = useState(false);

  const [userDetails, setUserDetails] = useState({
    fname: "",
    lname: "",
    email: "",
    avatar: "",
    username: "",
    role: "",
    phone: 0,
    invite_url: `https://arizon.vercel.app/user-invitation/${newUserId}`,
    company_id: companyIds,
  });

  // only removed

  // useEffect(() => {
  //   db.collection(alluser).onSnapshot(snap => {
  //     const data = firebaseLooper(snap);
  //     console.log("all users: ", data);
  //     setAllUsersList(data);
  //   })
  // }, [])

  //
  const handleEmail = (e) => {
    //alert("1")
    const re =
      /^[a-z0-9][a-z0-9-_\.]+@([a-z]|[a-z0-9]?[a-z0-9-]+[a-z0-9])\.[a-z0-9]{2,10}(?:\.[a-z]{2,10})?$/;
    let emailTemp = e.target.value;
    if (emailTemp && re.test(emailTemp)) {
      allUsersList.find((data) => {
        if (data.email.search(emailTemp) === 0) {
          console.log("true");
          setEmailMatches(true);
        }
      });
    }
  };

  const sendEmail = (e) => {
    e.preventDefault();
    // console.log(e.target)
    if (!emailMatches) {
      //   // updating A-alluser collection
      //   db.collection(alluser).add({
      //     lsi: false, //default static
      //     phone: userDetails.phone,
      //     email: userDetails.email,
      //     company_id: userDetails.company_id
      //   }).then((result)=>{
      //      //console.log("userTest", result.id)
      //      var newUrl = `https://arizon.vercel.app/user-invitation/${result?.id}`
      //      var newDocId = result.id;
      //     emailjs.send('gmail', 'template_3a12ff8', {
      //       to_name: `${userDetails.fname + " " + userDetails.lname}`,
      //       to_email: `${userDetails.email}`,
      //       // message: `You have been invited to LYO IMS Web Portal :   ${userDetails.invite_url}`
      //       message: `You have been invited to LYO IMS Web Portal :  https://arizon.vercel.app/user-invitation/${result?.id}`
      //     }, 'user_gqmHsTLEHxh06fhWlDnqq')
      //       .then(() => {
      //         toastMessageSuccess({ message: "Users has been invited to the portal successfully. Please be patient till the user is active." })
      //         db.collection(companies).doc(companyIds).collection(users).doc(newDocId).set({...userDetails, invite_url: newUrl}).then((result) => {
      //          // result.update("invite_url", newUrl)
      //           toastMessageSuccess({ message: "User added to the portal!" })
      //         })
      //       }, (error) => {
      //         toastMessage({ message: error.text })
      //       });
      //   })
      //   e.target.reset()
    }
  };

  return (
    <AddUserContext.Provider
      value={{
        allUsersList,
        setAllUsersList,
        emailMatches,
        setEmailMatches,
        userDetails,
        setUserDetails,
        companyId,
        setCompanyId,
        handleEmail,
        sendEmail,
      }}
    >
      {children}
    </AddUserContext.Provider>
  );
};

export default AddUserProvider;
