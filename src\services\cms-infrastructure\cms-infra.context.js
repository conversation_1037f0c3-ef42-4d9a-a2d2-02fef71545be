import React, { createContext, useEffect, useState } from "react";
import { cmsInfra, companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import { toastMessageSuccess } from "../../tools/toast";
import { firebaseLooper } from "../../tools/tool";

export const CmsInfraContext = createContext();

const CmsProvider = ({ children }) => {
  const [cmsData, setCmsData] = useState([]);
  const [instruments, setInstruments] = useState([]);
  const [reports, setReports] = useState([]);

  // only removed

  // useEffect(() => {
  //     db.collection(companies).doc(companyId_constant).collection(cmsInfra).doc('AIvh9SJkujEq9SxN9Us5').collection('equipment_master')
  //     .onSnapshot(snap => {
  //         const data = firebaseLooper(snap)
  //         setCmsData(data)
  //     })

  //     db.collection(companies).doc(companyId_constant).collection(cmsInfra).doc('AIvh9SJkujEq9SxN9Us5').collection('sap_master')
  //     .onSnapshot(snap => {
  //         const data = firebaseLooper(snap)
  //         setInstruments(data)
  //     })

  //     db.collection(companies).doc(companyId_constant).collection(cmsInfra).doc('AIvh9SJkujEq9SxN9Us5').collection('sap_report')
  //     .onSnapshot(snap => {
  //         const data = firebaseLooper(snap)
  //         console.log("Reports : ", data)
  //         setReports(data)
  //     })
  // }, [])

  return (
    <CmsInfraContext.Provider value={{ cmsData, instruments, reports }}>
      {children}
    </CmsInfraContext.Provider>
  );
};

export default CmsProvider;
