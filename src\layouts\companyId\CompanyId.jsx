import React, { useState, useEffect } from "react";
import "./companyIdPage.scss";
import {
  useCompanyId,
  useCompanyIdSeter,
} from "../../context/CompanyIdContext";
import { db } from "../../firebase";
import { useNavigate } from "react-router-dom";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { TextField, Box } from "@mui/material";
import { NavLink } from "react-router-dom";
import { toastMessageWarning } from "../../tools/toast";
import { useStateContext } from "../../context/ContextProvider";
import logo from "../../assets/images/logo.png";
import {
  adminType_constant_temp,
  companyId_constant,
  lsiAdmin,
} from "../../constants/data";

const CompanyId = () => {
  const history = useNavigate();

  // const companyIdFromContext = useCompanyId();
  const [allCompany, setAllCompany] = useState([]);

  const companyIdSetterFun = useCompanyIdSeter();
  const [companyId, setCompanyId] = useState();
  const { currentColorLight, currentMode } = useStateContext();

  useEffect(() => {
    if (
      adminType_constant_temp === lsiAdmin ||
      window.localStorage.getItem("adminType") === lsiAdmin
    ) {
      window.localStorage.removeItem("companyId");
      window.localStorage.removeItem("adminType");
      window.location.reload(false); //To update the constants. It prevents from login error for the first timee.
    }

    let temp = [];
    // const userData = db.collection('A_companyData').get()
    //   .then(querySnapshot => {
    //     //console.log('Total usersssss: ', querySnapshot.size);
    //     querySnapshot.forEach(documentSnapshot => {
    //       //console.log('company from welocome: ', documentSnapshot.id);
    //       temp.push(documentSnapshot.id);
    //     });
    //   })
    //   .then(() => {
    //     setAllCompany(temp);
    //     //console.log(allCompany[0])
    //   }
    //   );
  }, []);

  const companyMatch = (e) => {
    e.preventDefault();

    if (companyId) {
      let companyMatch = true;
      let matchedCompany = allCompany?.find((data) => data === companyId);
      if (matchedCompany) {
        companyIdSetterFun(companyId);
        window.localStorage.setItem("adminType", "admin");
        history?.push("/login");
      } else {
        companyMatch = false;
      }
      // })

      if (!companyMatch) {
        toastMessageWarning({ message: "Please provide correct Id" });
      }
    } else {
      toastMessageWarning({ message: "Please provide an ID" });
    }
  };
  //
  const companyMatchLsi = () => {
    history("/admin-login");
  };

  return (
    <section className="companyIdPage">
      <div className="illustrationContainer">
        <img
          src={logo} //"https://firebasestorage.googleapis.com/v0/b/lyodatatest.appspot.com/o/arizon%2Flogo.png?alt=media&token=1bfe7c2e-5910-4253-80c3-4c3f23f0ba69"
          alt=""
        />
      </div>

      <div
        className="formContainer"
        style={
          currentMode === "Dark"
            ? { backgroundColor: "#161C24", color: "white" }
            : { backgroundColor: currentColorLight }
        }
      >
        <button
          onClick={() => companyMatchLsi()}
          className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
        >
          Go to Arizon <i className="ri-arrow-right-line"></i>
        </button>
        <div className="self-center">
          {" "}
          <b>Or</b>{" "}
        </div>
        <div
          className={
            currentMode === "Dark"
              ? "bg-gray-900 rounded-md p-8 "
              : "bg-orange-200 rounded-md p-8 "
          }
        >
          <div className="formTitle">Login</div>
          <div
            className={
              currentMode === "Dark"
                ? "formDesc text-white"
                : "formDesc text-gray-900"
            }
          >
            Enter Your company Id here to proceed further.
          </div>

          <form onSubmit={companyMatch} className="form">
            <div className="labelFields">
              {/* <Box
              component="form"
              sx={{
                '& > :not(style)': { width: '25ch' },
              }}
            > */}
              <TextField
                label="Company Id"
                id="outlined-size-small"
                placeholder="eg. companyXYZ23234234"
                value={companyId}
                onChange={(e) => setCompanyId(e.target.value)}
                fullWidth
                size="small"
              />
              {/* </Box> */}
            </div>

            <button
              type="submit"
              className=" flex w-full justify-center bg-gray-600 hover:bg-gray-700 hover:cursor-pointer text-white font-bold py-2 px-4 rounded"
            >
              Continue <i className="ri-arrow-right-line"></i>
            </button>
          </form>
        </div>
      </div>
      <ToastContainer />
    </section>
  );
};

export default CompanyId;
