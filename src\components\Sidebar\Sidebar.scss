.aside {
  width: 18%;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  padding-right: 1rem; /* Reduced from 1.5rem */
  padding-left: 1rem; /* Reduced from 1.5rem */
  position: fixed;
  top: 1rem;
  left: 1rem;
  bottom: 1rem;
  overflow-y: scroll;
  overflow-x: hidden;
  transition: width 0.2s ease-out;

  .logo {
    display: flex;
    align-items: center;

    .logoImg {
      width: 80px;
      height: 80px;
      padding-top: 0.2rem;
      img {
        width: 100%;
        height: 100%;
      }
    }
    h4 {
      font-size: 1.1rem;
      font-weight: 500;
      margin-left: 1rem;
    }
  }

  hr {
    height: 1px;
    margin: 15px 0; /* Reduced from 25px */
    background: linear-gradient(
      to left,
      rgba(0, 0, 0, 0),
      rgba(51, 51, 51, 0.575),
      rgba(0, 0, 0, 0)
    );
    border: 0;
  }
  hr:after {
    display: block;
    content: "";
    height: 20px; /* Reduced from 30px */
    background-image: radial-gradient(
      farthest-side at center top,
      #cccccc 0%,
      rgba(255, 255, 255, 0) 100%
    );
  }

  .menu {
    .listHeading {
      padding: 8px; /* Reduced from 13px */
      font-size: 0.78rem;
      text-transform: uppercase;
      font-weight: 500;
      opacity: 0.6;
    }
    ul {
      display: flex;
      flex-direction: column;

      .active {
        .link {
          box-shadow: rgba(0, 0, 0, 0.05) 0px 20px 27px 0px;
          t {
            opacity: 0.95;
          }
        }
      }

      .navLink {
        text-decoration: none;
        .link {
          display: flex;
          align-items: center;
          margin: 0.05rem 0; /* Reduced from 0.1rem */
          cursor: pointer;
          padding: 8px 10px; /* Reduced from 11px 13px 11px 16px */
          border-radius: 8px;

          &:hover {
            box-shadow: rgba(0, 0, 0, 0.05) 0px 20px 27px 0px;
          }

          .icon {
            border-radius: 8px;
            padding: 0.3rem 0.4rem; /* Reduced from 0.4rem 0.5rem */
            box-shadow:
              rgba(20, 20, 20, 0.12) 0px 4px 6px -1px,
              rgba(20, 20, 20, 0.07) 0px 2px 4px -1px;
            transition: margin 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .text {
            font-size: 0.9rem;
            opacity: 0.7;
            font-weight: 500;
            padding-left: 0.8rem; /* Reduced from 1rem */
          }
        }
      }
    }
  }
}

.drawer.inactive {
  width: 8%;
  padding: 0.8rem; /* Reduced from 1rem */
  .logo {
    h4 {
      display: none;
    }
  }
  .menu {
    .listHeading {
      display: none;
    }

    .link {
      .text {
        display: none;
      }
    }
  }
}

// LightMode
.aside-light {
  color: #344767;

  .menu {
    ul {
      .active {
        .link {
          background: #fff;
          .icon {
            background-color: #045164;
            color: #f7f9fb;
          }
        }
      }

      .navLink {
        color: #344767;
        .link {
          &:hover {
            background: #fff;
          }
        }
      }
    }
  }
}

// Dark Mode
.aside-dark {
  color: #fff;

  .menu {
    ul {
      .active {
      }

      .navLink {
        color: #fff;
        .link {
          &:hover {
            background: #a0bcef9e;
            .icon {
              background-color: #fff;
            }
          }
        }
      }
    }
  }
}
