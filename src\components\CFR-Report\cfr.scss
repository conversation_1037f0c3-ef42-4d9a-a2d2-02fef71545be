.cfrMain {
  padding: 2rem 1.5rem;
  // background-color: #fff;
  box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
  // border-radius: 10px;
  color: #344767;
  .machineListHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: fit-content;
    width: 100%;
    margin-bottom: 1.5rem;
    padding: 0rem;
    .tabsContainer {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      text-align: left;
      gap: 32px;
      width: "100%";
      margin-left: 6px;
      .cfr_tab {
        // padding-bottom: 4px;
        font-size: 18px;
        font-weight: 700;
        color: rgb(52, 71, 103);
        cursor: pointer;
        text-transform: capitalize;
      }
      .cfr_active_tab {
        border-bottom: 3px solid #2f6dda;
      }
    }
  }
  .machineItems {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: white;
    // border-radius: 0.5rem;
    .machineItem__noData {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      // border: 1px solid #000;
      // border-radius: 0 0 0.5rem 0.5rem;
    }
    .machineItems_tableHead {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      // border: 1px solid #000;
      background: #e0e0e0;
      border-radius: 5px 5px 0px 0px;
      .machineItems_tableCell {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 16px;
        text-transform: uppercase;
        opacity: 0.8;
        font-weight: 700;
        padding: 1rem 2rem;
        width: fit-content;
      }
    }
  }
}

.machine-filter {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
  width: 456px;
  span {
    font-weight: 700;
    font-size: 15px;
    white-space: nowrap;
  }
}

.machine__item {
  width: 100%;
  // color: #344767;
  padding: 0.5rem 2rem;
  border: 1px solid #000;
  // border-bottom: 1px solid rgba(224, 224, 224, 1);
  border-top: none;
  // background-color: #fff;
  cursor: pointer; /* Add cursor pointer for the entire section */
  &:hover {
    background-color: #f5f5f5; /* Hover background color */
  }
  &:last-of-type {
    border-radius: 0px 0px 5px 5px;
  }
  /* If you want to apply the last-child border: 0 to table rows inside EventTable/AlarmTable */
  .contentTable {
    table {
      tr {
        &:last-child {
          td,
          th {
            border: 0;
          }
        }
      }
    }
  }
  .machine__item__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: left;
    .machine__title {
      width: 220px;
      font-size: 15px;
      font-weight: 500;
      text-transform: capitalize;
      opacity: 0.8;
    }
    .data-time__change {
      font-size: 1.2rem;
      font-weight: 600;
    }
    .toggle__btn {
      width: fit-content;
      height: fit-content;
    }
  }
  .machine__item__main {
    width: 100%;
    .actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      .filter {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 4px;
        span {
          width: 120px;
        }
        div {
          width: 260px;
        }
      }
    }
    .contentTable {
      margin-top: 16px;
    }
  }
}

.tableContainer {
  // border-radius: 0.75rem !important;
  .insideTable {
    // border-radius: 0.75rem !important;
    min-width: 650;
    width: 100%;
  }
}
