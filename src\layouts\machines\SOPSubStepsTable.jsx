import React from "react";
import {
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  IconButton,
  Collapse,
  Box,
  Pagination,
  Tooltip,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { Typography } from "@mui/material";
import { red } from "@mui/material/colors";
import { formatIcons, typeIcons } from "./SOPStepsTable";

const SOPSubStepsTable = ({
  steps = [],
  page = 1,
  perPage = 5,
  onPageChange,
  localObjectUrlMap,
}) => {
  const [expandedStep, setExpandedStep] = React.useState(null);
  const [expandedSubStep, setExpandedSubStep] = React.useState(null);

  const hasAllSubStepData = (steps) => {
    return !!steps.title && !!steps.desc && !!steps.url;
  };
  const paged = (arr, page, perPage) =>
    arr.slice((page - 1) * perPage, page * perPage);
  const pageCount = Math.ceil(steps.length / perPage);

  return (
    <>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              Title
            </TableCell>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              Description
            </TableCell>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              URL
            </TableCell>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              Format
            </TableCell>
            <TableCell
              sx={{ fontWeight: "bold", backgroundColor: "rgb(224, 224, 224)" }}
            >
              Type
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {paged(steps, page, perPage).map((step, idx) => (
            <React.Fragment key={step._id || idx}>
              <TableRow
                sx={{
                  "&:last-child td, &:last-child th": { border: 0 },
                  borderBottom: "1px solid #e0e0e0",
                  "&:hover": { bgcolor: "#f5f5f5" },
                  cursor: "pointer",
                  border: hasAllSubStepData(step)
                    ? "1px solid #E6F4EA"
                    : "1px solid #F87171",
                }}
              >
                <TableCell>{step.title}</TableCell>
                <TableCell>{step.desc}</TableCell>
                <TableCell>{step.url}</TableCell>
                <TableCell>
                  {step.format ? (
                    <Tooltip
                      title={
                        step.format.charAt(0).toUpperCase() +
                        step.format.slice(1)
                      }
                    >
                      {formatIcons[step.format] || step.format}
                    </Tooltip>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell>
                  {step.type ? (
                    <Tooltip
                      title={
                        step.type.charAt(0).toUpperCase() + step.type.slice(1)
                      }
                    >
                      {typeIcons[step.type] || step.type}
                    </Tooltip>
                  ) : (
                    "-"
                  )}
                </TableCell>
              </TableRow>
              {/* {Array.isArray(step.subSteps) && step.subSteps.length > 0 && (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    sx={{ p: 0, border: 0, backgroundColor: "#FEF7E6" }} // Soft cream for nested sub-steps
                  >
                    <Collapse
                      in={expandedStep === idx}
                      timeout="auto"
                      unmountOnExit
                    >
                      <Box sx={{ p: 2 }}>
                        <Typography
                          variant="h6"
                          sx={{ mb: 2, fontWeight: "bold", color: "#2B6CB0" }}
                        >
                          Nested Sub-Steps
                        </Typography>
                        <Table size="small" sx={{ backgroundColor: "#FEF7E6" }}>
                          <TableHead>
                            <TableRow sx={{ backgroundColor: "#2D3748" }}>
                              <TableCell sx={{ fontWeight: "bold" }}>Index</TableCell>
                              <TableCell sx={{ fontWeight: "bold" }}>Sub Step Title</TableCell>
                              <TableCell sx={{ fontWeight: "bold" }}>Sub Step Description</TableCell>
                              <TableCell sx={{ fontWeight: "bold" }}>Format</TableCell>
                              <TableCell sx={{ fontWeight: "bold" }}>Type</TableCell>
                              <TableCell sx={{ fontWeight: "bold" }}>URL</TableCell>
                              <TableCell sx={{ fontWeight: "bold" }}>Expand</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {step.subSteps.map((subStep, subIdx) => (
                              <React.Fragment key={subStep._id || subIdx}>
                                <TableRow
                                  sx={{
                                    "&:hover": {
                                      backgroundColor: "#D1E9FF", // Light blue on hover
                                    },
                                  }}
                                >
                                  <TableCell>{subStep.index ?? subIdx}</TableCell>
                                  <TableCell>{subStep.title}</TableCell>
                                  <TableCell>{subStep.desc}</TableCell>
                                  <TableCell>{subStep.format}</TableCell>
                                  <TableCell>{subStep.type}</TableCell>
                                  <TableCell>{subStep.url}</TableCell>
                                  <TableCell>
                                    {Array.isArray(subStep.subSteps) &&
                                      subStep.subSteps.length > 0 && (
                                        <IconButton
                                          onClick={() =>
                                            setExpandedSubStep(
                                              expandedSubStep === subIdx
                                                ? null
                                                : subIdx
                                            )
                                          }
                                        >
                                          {expandedSubStep === subIdx ? (
                                            <ExpandLessIcon />
                                          ) : (
                                            <ExpandMoreIcon />
                                          )}
                                        </IconButton>
                                      )}
                                  </TableCell>
                                </TableRow>
                                {Array.isArray(subStep.subSteps) &&
                                  subStep.subSteps.length > 0 && (
                                    <TableRow>
                                      <TableCell
                                        colSpan={7}
                                        sx={{ p: 0, border: 0, backgroundColor: "#FEF7E6" }}
                                      >
                                        <Collapse
                                          in={expandedSubStep === subIdx}
                                          timeout="auto"
                                          unmountOnExit
                                        >
                                          <Box sx={{ p: 2 }}>
                                            <Typography
                                              variant="h6"
                                              sx={{
                                                mb: 2,
                                                fontWeight: "bold",
                                                color: "#2B6CB0",
                                              }}
                                            >
                                              Nested Sub-Steps
                                            </Typography>
                                            <Table
                                              size="small"
                                              sx={{ backgroundColor: "#FEF7E6" }}
                                            >
                                              <TableHead>
                                                <TableRow sx={{ backgroundColor: "#2D3748" }}>
                                                  <TableCell sx={{ fontWeight: "bold" }}>
                                                    Index
                                                  </TableCell>
                                                  <TableCell sx={{ fontWeight: "bold" }}>
                                                    Sub Step Title
                                                  </TableCell>
                                                  <TableCell sx={{ fontWeight: "bold" }}>
                                                    Sub Step Description
                                                  </TableCell>
                                                  <TableCell sx={{ fontWeight: "bold" }}>
                                                    Format
                                                  </TableCell>
                                                  <TableCell sx={{ fontWeight: "bold" }}>
                                                    Type
                                                  </TableCell>
                                                  <TableCell sx={{ fontWeight: "bold" }}>
                                                    URL
                                                  </TableCell>
                                                </TableRow>
                                              </TableHead>
                                              <TableBody>
                                                {subStep.subSteps.map(
                                                  (subSubStep, subSubIdx) => (
                                                    <TableRow
                                                      key={subSubStep._id || subSubIdx}
                                                      sx={{
                                                        "&:hover": {
                                                          backgroundColor: "#D1E9FF", // Light blue on hover
                                                        },
                                                      }}
                                                    >
                                                      <TableCell>
                                                        {subSubStep.index ?? subSubIdx}
                                                      </TableCell>
                                                      <TableCell>
                                                        {subSubStep.title}
                                                      </TableCell>
                                                      <TableCell>
                                                        {subSubStep.desc}
                                                      </TableCell>
                                                      <TableCell>
                                                        {subSubStep.format}
                                                      </TableCell>
                                                      <TableCell>
                                                        {subSubStep.type}
                                                      </TableCell>
                                                      <TableCell>
                                                        {subSubStep.url}
                                                      </TableCell>
                                                    </TableRow>
                                                  )
                                                )}
                                              </TableBody>
                                            </Table>
                                          </Box>
                                        </Collapse>
                                      </TableCell>
                                    </TableRow>
                                  )}
                              </React.Fragment>
                            ))}
                          </TableBody>
                        </Table>
                      </Box>
                    </Collapse>
                  </TableCell>
                </TableRow>
              )} */}
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
      {steps.length > perPage && (
        <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
          <Pagination
            count={pageCount}
            page={page}
            onChange={(_, value) => onPageChange(value)}
            size="small"
            sx={{ "& .MuiPaginationItem-root": { color: "#2B6CB0" } }}
          />
        </Box>
      )}
    </>
  );
};

export default SOPSubStepsTable;
