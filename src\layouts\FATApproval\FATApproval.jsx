import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import {
  companies,
  companyId_constant,
  fatReport,
  machines,
} from "../../constants/data";
import { db } from "../../firebase";
import { toastMessage } from "../../tools/toast";
import AddUsers from "./AddUsers";
import Approval from "./Approval";
import FATLogin from "./FATLogin";

const FATApproval = () => {
  const [login, setLogin] = useState(false);
  const [approvalData, setApprovalData] = useState([]);
  const [reportDetails, setReportDetails] = useState([]);
  const [otp, setOtp] = useState("");
  const [mDetail, setMDetail] = useState([]);
  const { companyId, approvalId, fid, eid } = useParams();

  useEffect(() => {
    // db.collection(companies).doc(companyId)
    // .collection(fatReport).doc(fid).onSnapshot(snap => {
    //   const data = snap.data()
    //   setReportDetails(data)
    //   db.collection(companies).doc(companyId).collection(machines)
    //   .doc(data.mid).onSnapshot(event => {
    //     const machineDetails = event.data()
    //     setMDetail(machineDetails)
    //   })
    // })
    // db.collection(companies).doc(companyId)
    // .collection(fatReport).doc(fid).collection('fatData')
    // .doc(approvalId).collection('approval').doc(eid)
    // .onSnapshot(snap => {
    //   const data = snap.data()
    //   setApprovalData(data)
    // })
  }, []);

  const handleLogin = () => {
    const emailOtp = approvalData.otp;
    if (otp == emailOtp) {
      setLogin(true);
    } else {
      toastMessage({ message: "Incorrect OTP . Please try again" });
    }
  };

  return (
    <>
      {login ? (
        <Approval
          companyId={companyId}
          mDetail={mDetail}
          reportDetails={reportDetails}
          approvalData={approvalData}
        />
      ) : (
        <FATLogin otp={otp} setOtp={setOtp} handleLogin={handleLogin} />
      )}
    </>
  );
};

export default FATApproval;
