import React from "react";
import CircularProgress from "@mui/material/CircularProgress";
import Box from "@mui/material/Box";
import { makeStyles } from "@mui/styles";
import PropTypes from "prop-types";

const useStyles = makeStyles(() => ({
  overlayContainer: {
    position: "relative",
    width: "100%",
    height: "100%",
  },
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: "rgba(0, 0, 0, 0.1)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 99,
  },
}));

const LoadingOverlayWrapper = ({ active, children }) => {
  const classes = useStyles();

  return (
    <Box className={classes.overlayContainer}>
      {active && (
        <Box className={classes.overlay}>
          <CircularProgress color="inherit" />
        </Box>
      )}
      {children}
    </Box>
  );
};

LoadingOverlayWrapper.propTypes = {
  active: PropTypes.bool.isRequired, // Determines if the loading overlay is active
  children: PropTypes.node.isRequired, // The wrapped children components
};

LoadingOverlayWrapper.defaultProps = {
  active: false, // Default state for the loading overlay
};

export default LoadingOverlayWrapper;
