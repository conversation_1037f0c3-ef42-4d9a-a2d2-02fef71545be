import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  IconButton,
} from "@mui/material";
import { BorderColor, Code, Image, TextSnippet } from "@mui/icons-material";

/**
 * @enum
 *
 */
export const NODE_FIELD_TYPES = {
  text_field: "text_field",
  text_area: "text_area",
  image: "image",
  code: "code",
};

const CommonFieldsModal = ({
  openDialog = false,
  dialogTitleText = "",
  buttonCloseText = "",
  handleAddNodeField = () => {},
  handleClose = () => {},
}) => {
  const buttons = [
    {
      icon: <TextSnippet />,
      label: "Text Field",
      type: NODE_FIELD_TYPES.text_field,
    },
    {
      icon: <BorderColor />,
      label: "Text Area",
      type: NODE_FIELD_TYPES.text_area,
    },
    { icon: <Image />, label: "Image Field", type: NODE_FIELD_TYPES.image },
    { icon: <Code />, label: "Code Area", type: NODE_FIELD_TYPES.code },
  ];

  return (
    <Dialog open={openDialog} fullWidth>
      <DialogTitle>
        <Typography variant={"h5"} component={"h5"}>
          {dialogTitleText}
        </Typography>
      </DialogTitle>
      <DialogContent>
        <Box>
          <Box>
            {buttons.map((btn) => (
              <Button
                key={btn.type}
                onClick={() => handleAddNodeField(btn.type)}
                mode={"outlined"}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                  gap: 1,
                }}
              >
                <IconButton>{btn.icon}</IconButton>
                <Typography>{btn.label}</Typography>
              </Button>
            ))}
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button variant={"contained"} color={"error"} onClick={handleClose}>
          {buttonCloseText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CommonFieldsModal;
