import { createContext, useContext, useState } from "react";
import axios from "axios";
import { dbUrl } from "../constants/db";
import { useLoading } from "./LoadingProvider";
import PropTypes from "prop-types";

const CalMasterContext = createContext();
const EqpMasterContext = createContext();
const ProcedureContext = createContext();
const SetPointContext = createContext();
const CalReportContext = createContext();
const ExternalContecxt = createContext();

export function useCalMasterDeleteAll() {
  return useContext(CalMasterContext);
}
export function useEqpMasterDeleteAll() {
  return useContext(EqpMasterContext);
}
export function useProcedureDeleteAll() {
  return useContext(ProcedureContext);
}
export function useSetPointDeleteAll() {
  return useContext(SetPointContext);
}
export function useCalReportDeleteAll() {
  return useContext(CalReportContext);
}
export function useExternalReportDeleteAll() {
  return useContext(ExternalContecxt);
}

export function DeleteAllContextProvider({ children }) {
  // eslint-disable-next-line no-unused-vars
  const [isLoading, setIsLoading] = useLoading();
  const [calMaster, setCalMaster] = useState("");
  const [eqpMaster, setEqpMaster] = useState("");
  const [procedure, setProcedure] = useState("");
  const [setPoints, setSetPoints] = useState("");
  const [calReport, setCalReport] = useState("");
  const [externalReport, setExternalReport] = useState("");
  //
  const handleCalMasterDelete = async () => {
    setIsLoading(true);
    await axios
      .delete(`${dbUrl}/inc-cal/deleteall`)
      .then((res) => {
        //
        setCalMaster(res?.data);
      })
      .catch((e) => {})
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleEqpMasterDelete = async () => {
    setIsLoading(true);
    await axios
      .delete(`${dbUrl}/ins-master/deleteall`)
      .then((res) => {
        //
        setEqpMaster(res?.data);
      })
      .catch((e) => {})
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleProcedureDelete = async () => {
    setIsLoading(true);
    await axios
      .delete(`${dbUrl}/cal-procedure/deleteall`)
      .then((res) => {
        //
        setProcedure(res?.data);
      })
      .catch((e) => {})
      .finally(() => {
        setIsLoading(false);
      });
  };
  const handleSetPointsDelete = async () => {
    setIsLoading(true);
    await axios
      .delete(`${dbUrl}/set-points/deleteall`)
      .then((res) => {
        //
        setSetPoints(res?.data);
      })
      .catch((e) => {})
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleReportDelete = async () => {
    setIsLoading(true);
    await axios
      .delete(`${dbUrl}/report/deleteall`)
      .then((res) => {
        //
        setCalReport(res?.data);
      })
      .catch((e) => {})
      .finally(() => {
        setIsLoading(false);
      });
  };
  const handleExternaReportDelete = () => {
    setIsLoading(true);
    axios
      .delete(`${dbUrl}/external-report/deleteall`)
      .then((res) => {
        //
        setExternalReport(res?.data);
      })
      .catch((e) => {})
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <CalMasterContext.Provider value={[calMaster, handleCalMasterDelete]}>
      <EqpMasterContext.Provider value={[eqpMaster, handleEqpMasterDelete]}>
        <ProcedureContext.Provider value={[procedure, handleProcedureDelete]}>
          <SetPointContext.Provider value={[setPoints, handleSetPointsDelete]}>
            <CalReportContext.Provider value={[calReport, handleReportDelete]}>
              <ExternalContecxt.Provider
                value={[externalReport, handleExternaReportDelete]}
              >
                {children}
              </ExternalContecxt.Provider>
            </CalReportContext.Provider>
          </SetPointContext.Provider>
        </ProcedureContext.Provider>
      </EqpMasterContext.Provider>
    </CalMasterContext.Provider>
  );
}

DeleteAllContextProvider.propTypes = {
  children: PropTypes.node,
};
