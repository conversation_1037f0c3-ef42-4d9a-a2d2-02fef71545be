import Modal from "@mui/material/Modal";
import axios from "axios";
import { useEffect, useState, useContext } from "react";
import { toastMessageSuccess, toastMessage } from "../../tools/toast";
import {
  Button,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  TextField,
  IconButton,
} from "@mui/material";
import { useStateContext } from "../../context/ContextProvider";
import { HashtagContext } from "../../services2/hashtag/hashtag.context";
import { dbConfig } from "../../infrastructure/db/db-config";
import CloseIcon from "@mui/icons-material/Close";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";

const ModalDiv = ({ manual_id, open, setOpen, maintenance, machine, step }) => {
  const { currentMode, currentColorLight } = useStateContext();
  const { hashes, handleHastageData } = useContext(HashtagContext);

  const [hashtagAdded, setHA] = useState("");
  const [loading, setLoading] = useState(false);

  const mType = [
    "Calibration",
    "Machine Breakdown",
    "Routine",
    "Preventive",
    "Gemba",
    "Line Clearance",
  ];

  const hasHashtagPOSTAccess = useCheckAccess("hashtags", "POST");
  const hasHashtagGETAccess = useCheckAccess("hashtags", "GET");

  const handleAddHashTag = async (e) => {
    e.preventDefault();
    if (hashtagAdded.trim() === "") {
      toastMessage({ message: "Input cannot be empty!" });
      return;
    }

    setLoading(true);
    try {
      // 1. Add hashtag to DB
      const hashtagRes = await axios.post(`${dbConfig.url}/hashtags`, {
        maintenance_id: manual_id,
        step_id: step._id,
        title: hashtagAdded.trim(),
        type: maintenance.type,
      });

      console.log("hashhhhhhhhh", hashtagRes);
      const newHashtagId = hashtagRes.data.data._id;

      // 2. Update stepData with the new hashtag ID
      const updatedStepData = {
        ...step,
        hashtag: [...(step?.hashtag || []), newHashtagId], // Ensure hashtags are stored properly
      };

      await axios.put(`${dbConfig.url}/stepdata/${step._id}`, updatedStepData);

      handleHastageData(); // Refresh hashtag list
      toastMessageSuccess({ message: "Hashtag successfully added!" });
      setHA("");
    } catch (error) {
      console.error(error);
      toastMessage({ message: "Failed to add hashtag" });
    }
    setLoading(false);
  };

  return (
    <Dialog
      open={open}
      onClose={() => setOpen(false)}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "12px",
          background: currentMode === "Dark" ? "#161C24" : "white",
          color: currentMode === "Dark" ? "white" : "black",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <span>Machine: {machine}</span>
        <IconButton onClick={() => setOpen(false)}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <DialogContentText
          sx={{ fontSize: "18px", fontWeight: "bold", marginBottom: "10px" }}
        >
          Maintenance Type: {mType[maintenance.type]}
        </DialogContentText>

        <DialogContent
          sx={{
            background: currentMode === "Dark" ? "#212B36" : "#F5F5F5",
            padding: "10px",
            borderRadius: "8px",
            marginBottom: "20px",
          }}
        >
          {hasHashtagGETAccess ? (
            <>
              <DialogContentText sx={{ fontWeight: "bold" }}>
                {maintenance.title}
              </DialogContentText>
              <DialogContentText>
                {Array.isArray(step?.hashtag) && step.hashtag.length > 0
                  ? step.hashtag.map((tagId) => {
                      const tag = hashes.find((h) => h._id === tagId);
                      return tag ? `#${tag.title} ` : "";
                    })
                  : "No hashtags available"}
              </DialogContentText>
            </>
          ) : (
            <NotAccessible />
          )}
        </DialogContent>

        <form
          onSubmit={handleAddHashTag}
          style={{ display: "flex", flexDirection: "column", gap: "10px" }}
        >
          <TextField
            type="text"
            value={hashtagAdded}
            onChange={(e) => setHA(e.target.value)}
            placeholder="Enter hashtag"
            fullWidth
            sx={{ background: "white", borderRadius: "5px" }}
          />
          <Button
            type="submit"
            variant="contained"
            color="primary"
            // fullWidth
            disabled={loading || !hasHashtagPOSTAccess}
            sx={{ borderRadius: "8px", width: "20%", alignItems: "right" }}
          >
            {loading ? (
              <CircularProgress size={24} color="inherit" />
            ) : (
              "Submit"
            )}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ModalDiv;
