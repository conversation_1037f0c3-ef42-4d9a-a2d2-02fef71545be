import {
  <PERSON>,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import DescriptionIcon from "@mui/icons-material/Description";
import PivotTableChartIcon from "@mui/icons-material/PivotTableChart";
import AddActionSheet from "./forms/OQ_124/add-action-sheet.form";
import { db } from "../../firebase";
import { companies, companyId_constant } from "../../constants/data";
import { firebaseLooper } from "../../tools/tool";
import ActionSheet from "./tables/OQ_124/action-sheet.form";
import AddSimulationTests from "./forms/OQ_124/add-simulation-test.form";
import SimulationTestTable from "./tables/OQ_124/simulation-test.table";

const OQLayout = ({ docId }) => {
  const [openActionSheet, setOpenActionSheet] = useState(false);
  const [openOQ1, setOpenOQ1] = useState(false);
  const [actionSheets, setActionSheets] = useState([]); //OQ 124 Action Sheets
  const [simulationTests, setSimulationTests] = useState([]); //OQ 124 Simulation Tests

  useEffect(() => {
    // db.collection(companies).doc(companyId_constant).collection('fatData').doc(docId) // 0JSDmXSZzKpAzbbNgP8y
    // .collection('actionSheet').onSnapshot(snap => {
    //     const data = firebaseLooper(snap)
    //     setActionSheets(data)
    // })
    // db.collection(companies).doc(companyId_constant).collection('fatData').doc('0JSDmXSZzKpAzbbNgP8y')
    // .collection('OQ1').onSnapshot(snap => {
    //     const data = firebaseLooper(snap)
    //     setSimulationTests(data)
    // })
  }, []);

  return (
    <div className="oq-layout">
      {/* {
            actionSheets.map((data) => (
                
            ))
        } */}
      {actionSheets.length > 0 && (
        <ActionSheet
          actionSheetData={actionSheets[0]}
          key={actionSheets.id}
          docId={docId}
        />
      )}
    </div>
  );
};

export default OQLayout;
