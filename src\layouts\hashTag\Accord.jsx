import { useState, useEffect, useContext } from "react";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useNavigate } from "react-router-dom";
import { useMaintenanceInfoSeter } from "../../context/MaintenanceContext";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { useStateContext } from "../../context/ContextProvider";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  InputLabel,
  TextField,
  Menu,
  MenuItem,
} from "@mui/material";
import { AccordContext } from "../../services2/hashtag/accord.context";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import EditHashtag from "./EditHashtag";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import Delete from "../../components/Delete/Delete";
import { makeStyles } from "@material-ui/core/styles";
import { toast } from "react-toastify";
import { useCheckAccess } from "../../utils/useCheckAccess";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { HashtagContext } from "../../services2/hashtag/hashtag.context";

const useStyles = makeStyles((theme) => ({
  summary: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    "& .MuiAccordionSummary-content": {
      marginRight: 0,
    },
    // Prevent pointer events on the summary to disable default click behavior
    pointerEvents: "none",
  },
  editIcon: {
    marginLeft: "auto",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    pointerEvents: "auto", // Re-enable pointer events for the menu button
  },
  expandButton: {
    pointerEvents: "auto", // Re-enable pointer events for the expand button
  },
  itemActions: {
    display: "flex",
    alignItems: "center",
  },
}));

const Accord = ({ object, setExpanded, expanded, map }) => {
  const { refreshHashtagData } = useContext(HashtagContext);
  const { currentMode } = useStateContext();
  const [mType] = useState([
    "Calibration",
    "Machine Breakdown",
    "Routine",
    "Preventive",
  ]);
  const [hashItems, setHI] = useState([]);
  console.log("hashItems", hashItems);
  const { handleGoTo } = useContext(AccordContext);
  const [openDel, setOpenDel] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [hashtitle, setHashtagtitle] = useState("");
  const [hashtag, setHashtag] = useState(object.id);
  const [originalHashtag, setOriginalHashtag] = useState(object.id);
  const [deleteItemId, setDeleteItemId] = useState(null);
  const classes = useStyles();
  const [titles, setTitles] = useState({});
  const [errorMessage, setErrorMessage] = useState("");
  const hasHashtagPUTAccess = useCheckAccess("hashtags", "PUT");
  const hasHashtagDELETEAccess = useCheckAccess("hashtags", "DELETE");
  const [anchorEl, setAnchorEl] = useState(null);
  const [itemAnchorEl, setItemAnchorEl] = useState(null);
  const [selectedItemId, setSelectedItemId] = useState(null);

  useEffect(() => {
    sortWithVisit(object.value);
    console.log("object.value", object.value);
  }, [object]);

  const sortWithVisit = (arr) => {
    const newObj = [...arr];
    newObj.sort((a, b) => {
      const nameA = map.get(a.maintenance_id);
      const nameB = map.get(b.maintenance_id);
      if (nameA < nameB) {
        return 1;
      }
      if (nameA > nameB) {
        return -1;
      }
      return 0;
    });
    setHI([...newObj]);
    fetchTitles(newObj);
  };

  const fetchTitles = async (items) => {
    const titleMap = {};
    await Promise.all(
      items.map(async (item) => {
        try {
          // Initialize titleMap for this item
          titleMap[item.step_id] = {
            stepTitle: "N/A",
            maintTitle: "Loading...",
          };

          // Fetch maintenance title
          const maintResponse = await axios.get(
            `${dbConfig.url}/maintenance/${item.maintenance_id}`,
          );
          titleMap[item.step_id].maintTitle =
            maintResponse?.data?.data?.title || "N/A";

          // Only fetch step title if step_id is present and not equal to maintenance_id
          if (item.step_id && item.step_id !== item.maintenance_id) {
            try {
              const stepResponse = await axios.get(
                `${dbConfig.url}/stepdata/${item.step_id}`,
              );
              titleMap[item.step_id].stepTitle =
                stepResponse?.data?.data?.title || "N/A";
            } catch (stepError) {
              console.error(
                `Error fetching step data for step_id ${item.step_id}:`,
                stepError.message,
              );
              titleMap[item.step_id].stepTitle = "N/A";
            }
          }
        } catch (error) {
          console.error(
            `Error fetching titles for maintenance_id ${item.maintenance_id}:`,
            error.message,
          );
          titleMap[item.step_id].maintTitle = "N/A";
        }
      }),
    );
    setTitles(titleMap);
  };

  console.log("fetchTitles", fetchTitles);

  console.log("titles", titles);

  const handleSubmit = async (options) => {
    const arr = object.value[0];
    try {
      const response = await axios.put(`${dbConfig.url}/hashtags/${arr._id}`, {
        maintenance_id: arr.maintenance_id,
        step_id: arr.step_id,
        title: hashtitle,
        type: arr.type,
      });
      object.id = hashtitle;
      setOriginalHashtag(hashtitle);
      toast.success("Hashtag updated successfully!");
    } catch (error) {
      console.error(error);
      toast.error("Failed to update hashtag.");
    }
    setOpenEdit(false);
  };

  const handleDelete = async () => {
    setOpenDel(false);
    const arr = object.value[0];
    try {
      // Check if it's a maintenance-level hashtag (no step_id or step_id equals maintenance_id)
      if (!arr.step_id || arr.step_id === arr.maintenance_id) {
        await axios.delete(`${dbConfig.url}/hashtags/${arr._id}`);
        toastMessageSuccess({ message: `${arr.title} deleted` });
        refreshHashtagData();
        return;
      }
      // For step-level hashtags, update the step's hashtag array
      let data = {};
      try {
        const response = await axios.get(
          `${dbConfig.url}/stepdata/${arr.step_id}`,
        );
        data = response.data?.data;
      } catch (error) {
        console.error("Error fetching step data:", error.message);
      }
      data.hashtag = [];
      await axios.put(`${dbConfig.url}/stepdata/${arr.step_id}`, { ...data });
      await axios.delete(`${dbConfig.url}/hashtags/${arr._id}`);
      toastMessageSuccess({ message: `${arr.title} deleted` });
      refreshHashtagData();
    } catch (error) {
      console.error("Error deleting hashtag:", error);
      toastMessage({ message: `Failed to delete hashtag: ${error.message}` });
    }
  };

  const handleItemDelete = async (item) => {
    setOpenDel(false);
    try {
      // Check if it's a maintenance-level hashtag
      if (!item.step_id || item.step_id === item.maintenance_id) {
        await axios.delete(`${dbConfig.url}/hashtags/${item._id}`);
        toastMessageSuccess({ message: `${item.title} deleted` });
        setHI(hashItems.filter((hashItem) => hashItem._id !== item._id));
        refreshHashtagData();
        return;
      }
      // For step-level hashtags, update the step's hashtag array
      let data = {};
      try {
        const response = await axios.get(
          `${dbConfig.url}/stepdata/${item.step_id}`,
        );
        data = response.data?.data;
      } catch (error) {
        console.error("Error fetching step data:", error.message);
      }
      data.hashtag = [];
      await axios.put(`${dbConfig.url}/stepdata/${item.step_id}`, { ...data });
      await axios.delete(`${dbConfig.url}/hashtags/${item._id}`);
      toastMessageSuccess({ message: `${item.title} deleted` });
      setHI(hashItems.filter((hashItem) => hashItem._id !== item._id));
      refreshHashtagData();
    } catch (error) {
      console.error("Error deleting hashtag:", error);
      toastMessage({ message: `Failed to delete hashtag: ${error.message}` });
    }
  };

  const handletextfieldchange = (e) => {
    const newValue = e.target.value;
    if (newValue.trim() === "") {
      setErrorMessage("Hashtag cannot be Empty");
    } else {
      setErrorMessage("");
    }
    setHashtagtitle(newValue);
    setHashtag(newValue);
  };

  const handleCancel = () => {
    setHashtag(originalHashtag);
    setErrorMessage("");
    setOpenEdit(false);
  };

  const handleOpenEdit = () => {
    setHashtag(originalHashtag);
    setErrorMessage("");
    setOpenEdit(true);
    setAnchorEl(null);
  };

  const handleOpenItemDelete = (itemId) => {
    setDeleteItemId(itemId);
    setOpenDel(true);
    setItemAnchorEl(null);
  };

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleItemMenuOpen = (event, itemId) => {
    setItemAnchorEl(event.currentTarget);
    setSelectedItemId(itemId);
  };

  const handleItemMenuClose = () => {
    setItemAnchorEl(null);
    setSelectedItemId(null);
  };

  const handleToggleExpand = () => {
    setExpanded(expanded === object.id ? false : object.id);
  };

  return (
    <Accordion expanded={expanded === object.id}>
      <AccordionSummary className={classes?.summary}>
        <div>
          <p>{object.id}</p>
          <br />
          <p>No. of records: {object.value.length}</p>
        </div>
        <div className={classes.editIcon}>
          {(hasHashtagPUTAccess || hasHashtagDELETEAccess) && (
            <>
              <IconButton onClick={handleMenuOpen}>
                <MoreVertIcon style={{ fontSize: "20px" }} />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
              >
                {hasHashtagPUTAccess && (
                  <MenuItem onClick={handleOpenEdit}>
                    <EditIcon
                      style={{
                        fontSize: "20px",
                        marginRight: "8px",
                      }}
                    />
                    Edit
                  </MenuItem>
                )}
                {hasHashtagDELETEAccess && (
                  <MenuItem onClick={() => setOpenDel(true)}>
                    <DeleteIcon
                      style={{
                        fontSize: "20px",
                        color: "#f00",
                        marginRight: "8px",
                      }}
                    />
                    Delete
                  </MenuItem>
                )}
              </Menu>
            </>
          )}
          <IconButton
            onClick={handleToggleExpand}
            className={classes.expandButton}
          >
            <ExpandMoreIcon />
          </IconButton>
        </div>
      </AccordionSummary>
      <AccordionDetails>
        {hashItems.map((options, idx) => (
          <div key={options.maintenance_id + "" + idx} className="accordItem">
            <div className="left">
              <p>
                Maintenance Name:{" "}
                {titles[options.step_id]?.maintTitle || "Loading..."}
              </p>
              {"step_id" in options && (
                <p>
                  Step Name:{" "}
                  {titles[options.step_id]?.stepTitle || "Loading..."}
                </p>
              )}
              <p>Type: {mType[options.type]}</p>
            </div>
            <div className={classes.itemActions}>
              {hasHashtagDELETEAccess && (
                <>
                  <IconButton
                    onClick={(e) => handleItemMenuOpen(e, options._id)}
                  >
                    <MoreVertIcon style={{ fontSize: "20px" }} />
                  </IconButton>
                  <Menu
                    anchorEl={itemAnchorEl}
                    open={
                      Boolean(itemAnchorEl) && selectedItemId === options._id
                    }
                    onClose={handleItemMenuClose}
                  >
                    <MenuItem onClick={() => handleOpenItemDelete(options._id)}>
                      <DeleteIcon
                        style={{
                          fontSize: "20px",
                          color: "#f00",
                          marginRight: "8px",
                        }}
                      />
                      Delete
                    </MenuItem>
                  </Menu>
                </>
              )}
              <IconButton
                onClick={() => {
                  if (!options.maintenance_id) {
                    console.error("Invalid maintenance_id:", options);
                    toastMessageWarning({ message: "Invalid maintenance ID" });
                    return;
                  }
                  // Allow navigation for maintenance-level hashtags (no step_id)
                  handleGoTo(
                    options,
                    options.maintenance_id,
                    options.step_id || null,
                  );
                }}
              >
                <ChevronRightIcon />
              </IconButton>
            </div>
            <Dialog open={openEdit} fullWidth>
              <DialogTitle
                style={
                  currentMode === "Dark"
                    ? { backgroundColor: "#212B36", color: "white" }
                    : {}
                }
              >
                Edit Hashtag
              </DialogTitle>
              <DialogContent
                style={
                  currentMode === "Dark"
                    ? { backgroundColor: "#212B36", color: "white" }
                    : {}
                }
              >
                <InputLabel style={{ marginBottom: "10px" }}>
                  Hashtag
                </InputLabel>
                <TextField
                  onChange={handletextfieldchange}
                  onBlur={() => {
                    if (!hashtag.trim()) {
                      handletextfieldchange({ target: { value: "" } });
                    }
                  }}
                  style={{ marginBottom: "10px" }}
                  variant="outlined"
                  value={hashtag}
                  fullWidth
                  multiline
                  error={!!errorMessage}
                  helperText={errorMessage}
                />
                <div className="p-2 mt-2 flex justify-between">
                  <ButtonBasicCancel
                    buttonTitle="Cancel"
                    type="button"
                    onClick={handleCancel}
                    variant="outlined"
                  />
                  <ButtonBasic
                    buttonTitle="Submit"
                    type="submit"
                    variant="outlined"
                    onClick={() => handleSubmit(options)}
                    disabled={hashtag.trim() === ""}
                  />
                </div>
              </DialogContent>
            </Dialog>
            <Dialog open={openDel && deleteItemId === options._id}>
              <Delete
                onClose={() => {
                  setOpenDel(false);
                  setDeleteItemId(null);
                }}
                onDelete={() => handleItemDelete(options)}
              />
            </Dialog>
          </div>
        ))}
        <Dialog open={openDel && !deleteItemId}>
          <Delete
            onClose={() => {
              setOpenDel(false);
              setDeleteItemId(null);
            }}
            onDelete={() => handleDelete()}
          />
        </Dialog>
      </AccordionDetails>
    </Accordion>
  );
};

export default Accord;
