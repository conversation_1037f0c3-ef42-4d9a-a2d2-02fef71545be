import "./App.scss";
import "./index.css";
import { useState, useEffect } from "react";
import {
  BrowserRouter as Router,
  Switch,
  Route,
  useNavigate,
} from "react-router-dom";

// Components
import Sidebar from "./components/Sidebar/Sidebar";
import Header from "./components/Header/Header";
import CompanyIdPage from "./pages/companyIdPage";
import ForgotPasswordPage from "./pages/forgotPasswordPage";
import LoginPage from "./pages/loginPage";
import AdminForgotPasswordPage from "./pages/adminForgotPasswordPage";
import AdminLoginPage from "./pages/adminLoginPage";
import PrivateRoute from "./route/PrivateRoute";
import FlowChartType1 from "./components/Flow-Chart/Type1/index";
import ExistingFlowChart from "./components/Flow-Chart/Existing-Node/type-1";
import CreateNewNode from "./components/New-Node/create-node";
import NewNode from "./components/New-Node/index";
import UserLeaveConfirmation from "./components/RouteLeavingGuard/UserLeaveConfirmation";
import PrintReportsPage from "./pages/PrintReports";
import PrintFatSingle from "./layouts/machineData/printForMachineData/PrintFatSingle";
import { RoleProvider } from "./context/RoleContext";
import { UserProvider } from "./context/UserContext";
import Whiteboard from "./components/whiteboard/Whiteboard";
import RegistrationPortal from "./layouts/registration/Registration";
import PrintSatSingle from "./layouts/machineData/printForMachineData/PrintSatSingle";
import PrivateRouteMain from "./route/PrivateRouteMain";
import PrivateRoutesForLsiAdminMain from "./route/PrivateRoutesForLsiAdminMain";
import { useAuth } from "./hooks/AuthProvider";
import { adminType_constant_temp } from "./constants/data";
import { MaintenanceInfoProvider } from "./context/MaintenanceContext";
import { useStateContext } from "./context/ContextProvider";
import FATApproval from "./layouts/FATApproval/FATApproval";
import AddUsers from "./layouts/FATApproval/AddUsers";
import TestingPage from "./pages/testing";
import IssueModuleProvider from "./services2/issueModule/IssueModule.context";

// material ui v4 and v5 compatibility with theme
import { createTheme as createThemeV4 } from "@material-ui/core/styles";
import {
  createTheme as createThemeV5,
  lighten,
  ThemeProvider as ThemeProviderV5,
} from "@mui/material/styles";
import {
  createGenerateClassName,
  ThemeProvider as ThemeProviderV4,
  StylesProvider,
} from "@material-ui/core/styles";

//
import FileManagerContextProvider from "./services/fileManager/file-manager-select.context";
import CmsProvider from "./services/cms-infrastructure/cms-infra.context";
import HashtagProvider from "./services2/hashtag/hashtag.context";
import { ToastContainer } from "react-toastify";
import AddUserProvider from "./services2/users/addUser.context";
import MachinesProvder from "./services/machines/MachineContext";
import MaintenanceProvider from "./services/maintenance/maintenance.context";
import SubStepProvider from "./services2/SubStep/SubStep.context";
import LoginMongoScreen from "./layouts/auth-mongo/login-mongo.screen";
import AddUser from "./pages/addUser";
import { Grid } from "@mui/material";
import { ThemeProvider, createTheme, darken } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import { UserCfrContextProvider } from "./hooks/cfr/userCfrProvider";
import { MachineCfrContextProvider } from "./hooks/cfr/machineCfrProvider";
import MachinesProvider2 from "./services3/machines/MachineContext2";
import EditMachinesProvider from "./services3/machines/EditMachineContext";
import HashtagcontextProvider from "./services3/hashtag/hashtagcontext";
import AuditProvider from "./services3/audits/AuditContext";
import ContentProvider from "./services3/audits/ContentContext";
import { baseUrl } from "./infrastructure/db/db-config";
import MongoRefreshProvider from "./services/mongo-refresh.context";
import UtilsProvider from "./hooks/UtilsProvider";
import { LogoProvider } from "./context/LogoContext";

const loader = document.getElementById("spinner");

function App() {
  const [inactive, setInactive] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(true);
  const [blockRouteNav, setBlockRouteNav] = useState(false);
  const [lsiRoute, setLsiRoute] = useState(false); // Define lsiRoute state
  const handleRouteNavigation = (value) => {
    setBlockRouteNav(value);
  };
  const history = useNavigate();

  const { currentUser } = useAuth();

  const {
    setCurrentMode,
    setCurrentColor,
    setCurrentColorLight,
    setCurrentColorLightDark,
    currentMode,
    currentColor,
  } = useStateContext();

  const isDarkMode = currentMode === "Dark";

  useEffect(() => {
    loader.classList.add("loader-hide");
    const currentThemeColor = localStorage.getItem("colorMode");
    const currentThemeMode = localStorage.getItem("themeMode");
    const currentThemeColorLight = localStorage.getItem("colorLight");
    const currentThemeColorLightDark = localStorage.getItem("colorLightDark");

    if (currentThemeColor) {
      setCurrentMode?.(currentThemeMode);
      setCurrentColor?.(currentThemeColor ? currentThemeColor : "#1e1e1e");
      setCurrentColorLight?.(
        currentThemeColorLight ? currentThemeColorLight : "hsl(184, 97%, 96%)",
      );
      setCurrentColorLightDark?.(
        currentThemeColorLightDark
          ? currentThemeColorLightDark
          : "hsl(184, 97%, 96%)",
      );
    }
  }, [
    setCurrentMode,
    setCurrentColor,
    setCurrentColorLight,
    setCurrentColorLightDark,
  ]);

  return (
    <ThemeProvider
      theme={createTheme({
        palette: {
          mode: isDarkMode ? "dark" : "light",
          primary: {
            main: isDarkMode ? "#00ffcb" : currentColor,
            contrastText: isDarkMode ? "#1e1e1e" : "#ffffff",
          },
          secondary: {
            main: isDarkMode ? "#ff0000" : currentColor,
          },
          custom: {
            backgroundColor: isDarkMode
              ? "#1e1e1e"
              : lighten(currentColor, 0.9),
            textColor: isDarkMode ? "#fff" : "#000",
            backgroundSecondary: isDarkMode ? "#282828" : "#fff",
            backgroundThird: isDarkMode ? "#1e1e1e" : "#fff",
            backgroundForth: isDarkMode
              ? "#2f2f2f"
              : lighten(currentColor, 0.9),
            backgroundFifth: isDarkMode ? "#2f2f2f" : "#fff",
          },
        },
        components: {
          MuiTable: {
            styleOverrides: {
              root: {
                backgroundColor: isDarkMode ? "#282828" : "#fff",
              },
            },
          },
          MuiTableCell: {
            styleOverrides: {
              root: {
                padding: ".2rem .5rem",
                borderBottom: "none",
                color: (theme) => theme.palette.custom.textColor,
              },
              head: {
                backgroundColor: isDarkMode ? "#282828" : "#fff",
                color: (theme) => theme.palette.custom.textColor,
                fontWeight: "bold",
                borderBottom: "1px solid grey",
              },
            },
          },
          MuiInputLabel: {
            styleOverrides: {
              root: {
                color: (theme) => theme.palette.custom.textColor,
              },
            },
          },
          MuiInputBase: {
            styleOverrides: {
              input: {
                color: (theme) => theme.palette.custom.textColor,
              },
            },
          },
        },
      })}
    >
      <section
        style={{
          backgroundColor: isDarkMode ? "#1e1e1e" : "#fff",
          textColor: isDarkMode ? "#fff" : "#000",
        }}
      >
        <CssBaseline />
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme={isDarkMode ? "dark" : "light"}
        />
        <MongoRefreshProvider>
          <MachinesProvder>
            <LogoProvider>
              <MaintenanceProvider>
                <MaintenanceInfoProvider>
                  <AddUserProvider>
                    <FileManagerContextProvider>
                      <CmsProvider>
                        <RoleProvider>
                          <UserProvider>
                            <HashtagProvider>
                              <SubStepProvider>
                                <IssueModuleProvider>
                                  <UserCfrContextProvider>
                                    <MachineCfrContextProvider>
                                      <MachinesProvider2>
                                        <EditMachinesProvider>
                                          <HashtagcontextProvider>
                                            <AuditProvider>
                                              <ContentProvider>
                                                <UtilsProvider>
                                                  <MongoRefreshProvider>
                                                    <PrivateRouteMain
                                                      confirmOpen={confirmOpen}
                                                      setConfirmOpen={
                                                        setConfirmOpen
                                                      }
                                                      handleRouteNavigation={
                                                        handleRouteNavigation
                                                      }
                                                      inactive={inactive}
                                                      setInactive={setInactive}
                                                      blockRouteNav={
                                                        blockRouteNav
                                                      }
                                                      currentUser={currentUser}
                                                      lsiRoute={lsiRoute} // Pass lsiRoute as a prop
                                                      currentMode={currentMode}
                                                      currentColor={
                                                        currentColor
                                                      }
                                                    />
                                                  </MongoRefreshProvider>
                                                </UtilsProvider>
                                              </ContentProvider>
                                            </AuditProvider>
                                          </HashtagcontextProvider>
                                        </EditMachinesProvider>
                                      </MachinesProvider2>
                                    </MachineCfrContextProvider>
                                  </UserCfrContextProvider>
                                </IssueModuleProvider>
                              </SubStepProvider>
                            </HashtagProvider>
                          </UserProvider>
                        </RoleProvider>
                      </CmsProvider>
                    </FileManagerContextProvider>
                  </AddUserProvider>
                </MaintenanceInfoProvider>
              </MaintenanceProvider>
            </LogoProvider>
          </MachinesProvder>
        </MongoRefreshProvider>
      </section>
    </ThemeProvider>
  );
}

export default App;
