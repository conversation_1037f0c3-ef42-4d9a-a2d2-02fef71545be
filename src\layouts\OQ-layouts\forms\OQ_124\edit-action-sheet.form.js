import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Divider,
  Box,
  InputLabel,
  LinearProgress,
} from "@mui/material";
import { db } from "../../../../firebase";
import { companies, companyId_constant } from "../../../../constants/data";
import { toastMessage, toastMessageSuccess } from "../../../../tools/toast";
import { DropzoneArea } from "material-ui-dropzone";
import { Empty } from "antd";
import { DeleteByUrl } from "../../../../utils/StorageOptions";
import { useStorageTablesFile } from "../../../../utils/useStorageTablesFile";
import { useStateContext } from "../../../../context/ContextProvider";

const EditActionSheet = ({ actionSheetData, docId }) => {
  const [actionSheet, setActionSheet] = useState({
    title: actionSheetData.title,
    deviation_no: actionSheetData.deviation_no,
    protocol_ref: actionSheetData.protocol_ref,
    page_no: actionSheetData.page_no,
    item_no: actionSheetData.item_no,
    desc: actionSheetData.desc,
    impact: actionSheetData.impact,
    tester: actionSheetData.tester,
    sign_tester: actionSheetData.sign_tester,
    action: actionSheetData.action,
    eng_approval: actionSheetData.eng_approval,
    sign_eng: actionSheetData.sign_eng,
    result: actionSheetData.result,
    valid_approval: actionSheetData.valid_approval,
    sign_valid: actionSheetData.sign_valid,
    cust_approval: actionSheetData.cust_approval,
    sign_cust: actionSheetData.sign_cust,
  });

  const [file, setFile] = useState(null);
  const { currentMode } = useStateContext();
  const [urlData1, setUrlData1] = useState(actionSheetData.sign_tester);
  const [file1, setFile1] = useState(null);
  let obj = useStorageTablesFile(file1);
  const progress1 = obj.progress;
  const url1 = obj.url;
  const [urlData2, setUrlData2] = useState(actionSheetData.sign_eng);
  const [file2, setFile2] = useState(null);
  obj = useStorageTablesFile(file2);
  const progress2 = obj.progress;
  const url2 = obj.url;
  const [urlData3, setUrlData3] = useState(actionSheetData.sign_valid);
  const [file3, setFile3] = useState(null);
  obj = useStorageTablesFile(file3);
  const progress3 = obj.progress;
  const url3 = obj.url;
  const [urlData4, setUrlData4] = useState(actionSheetData.sign_cust);
  const [file4, setFile4] = useState(null);
  obj = useStorageTablesFile(file4);
  const progress4 = obj.progress;
  const url4 = obj.url;
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];

  const handleAddData = () => {
    // db.collection(companies).doc(companyId_constant).collection('fatData').doc(docId)
    // .collection('actionSheet').doc(actionSheetData.id).update(actionSheet).then((data) => {
    //     toastMessageSuccess({message: "Successfully Updated an Action Sheet"})
    // })
  };

  const handleChangeImage = (file, setFile, url) => {
    let selectedFile = file[0];
    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        if (url) {
          DeleteByUrl(url);
        }
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };

  const handleDeleteDropZone = (url, setFile) => {
    DeleteByUrl(url);
    setFile(null);
  };

  useEffect(() => {
    setActionSheet({
      ...actionSheet,
      sign_tester: url1 === null ? urlData1 : url1,
    });
  }, [url1]);

  useEffect(() => {
    setActionSheet({
      ...actionSheet,
      sign_eng: url2 === null ? urlData2 : url2,
    });
  }, [url2]);

  useEffect(() => {
    console.log(url3);
    setActionSheet({
      ...actionSheet,
      sign_valid: url3 === null ? urlData3 : url3,
    });
  }, [url3]);

  useEffect(() => {
    console.log(url4);
    setActionSheet({
      ...actionSheet,
      sign_cust: url4 === null ? urlData4 : url4,
    });
  }, [url4]);

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-evenly",
      }}
      className="add-action-sheet"
    >
      {/* <> */}
      <Box sx={{ width: "100%", display: "flex", flexWrap: "wrap" }}>
        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Title</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, title: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="DEVIATION, IMPACT AND CORRECTIVE ACTION SHEET"
            value={actionSheet.title}
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Deviation Number</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, deviation_no: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="D-0425"
            value={actionSheet.deviation_no}
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Protocol reference</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, protocol_ref: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="Protocol-R2F"
            value={actionSheet.protocol_ref}
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Page Number</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, page_no: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="1"
            value={actionSheet.page_no}
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Item Number</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, item_no: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="002"
            value={actionSheet.item_no}
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Tester</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, tester: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="eg: John Doe"
            value={actionSheet.tester}
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Description</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, desc: e.target.value })
            }
            rows={4}
            multiline
            variant="outlined"
            fullWidth
            placeholder="eg: Deviation tends to be something relevant to the OQ generated"
            value={actionSheet.desc}
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Impact</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, impact: e.target.value })
            }
            rows={4}
            multiline
            variant="outlined"
            fullWidth
            placeholder="eg: Impact tends to be something relevant to the OQ generated"
            value={actionSheet.impact}
          />
        </Box>

        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Engineer Approval</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, eng_approval: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="eg: Suresh Kumar"
            value={actionSheet.eng_approval}
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Validator Approval</InputLabel>
          <TextField
            variant="outlined"
            onChange={(e) =>
              setActionSheet({ ...actionSheet, valid_approval: e.target.value })
            }
            fullWidth
            placeholder="eg: Hemant Singh"
            value={actionSheet.valid_approval}
          />
        </Box>
        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Corrective Action</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, action: e.target.value })
            }
            rows={4}
            multiline
            variant="outlined"
            fullWidth
            placeholder="eg: Corrective Action tends to be something relevant to the OQ generated"
            value={actionSheet.action}
          />
        </Box>

        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Result</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, result: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="eg: Results tends to be something relevant to the OQ generated"
            value={actionSheet.result}
          />
        </Box>

        <Box sx={{ mt: 2, px: 2, width: "45%" }}>
          <InputLabel>Customer Approval</InputLabel>
          <TextField
            onChange={(e) =>
              setActionSheet({ ...actionSheet, cust_approval: e.target.value })
            }
            variant="outlined"
            fullWidth
            placeholder="eg: LNT Oil Technology"
            value={actionSheet.cust_approval}
          />
        </Box>
      </Box>
      <Box sx={{ marginTop: "20px", display: "flex" }}>
        <Box sx={{ marginBottom: "10px", marginRight: "10px" }}>
          <div className="flex justify-center">
            <div
              className={
                currentMode === "Dark"
                  ? "bg-gray-700 rounded-sm p-1 shadow-md"
                  : "bg-gray-100 rounded-sm p-1 shadow-md"
              }
            >
              <InputLabel style={{ marginBottom: "10px" }}>
                Tester Signature
              </InputLabel>
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) =>
                  handleChangeImage(loadedFiles, setFile1, file1)
                }
                dropzoneText="Drag and Drop / Click to ADD Media. MaxFileSize : 5Mb"
                showAlerts={false}
                filesLimit={1}
                maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
                onDelete={() => handleDeleteDropZone(url1, setFile1)}
                dropzoneClass={
                  currentMode === "Dark"
                    ? "dropZoneClassDark"
                    : "dropZoneClassLight"
                }
              />
              <div className="p-2 block">
                <LinearProgress
                  style={{ marginBottom: "20px" }}
                  variant="determinate"
                  value={progress1}
                />
                <div className="text-2xl text-gray-400 flex justify-end ">
                  <p> {progress1} % Uploaded</p>
                </div>
                {!url1 ? ( // url when we upload
                  <>
                    {url1 ? (
                      <img alt="" src={url1} />
                    ) : (
                      <>
                        <a target="_blank" href={urlData1} rel="noreferrer">
                          {" "}
                          <img alt="" src={urlData1} />{" "}
                        </a>
                      </>
                    )}
                  </>
                ) : (
                  <Empty
                    description={<span>Please Wait for Preview ...</span>}
                  />
                )}
              </div>
            </div>
          </div>
        </Box>
        <Box sx={{ marginBottom: "10px", marginRight: "10px" }}>
          <div className="flex justify-center">
            <div
              className={
                currentMode === "Dark"
                  ? "bg-gray-700 rounded-sm p-1 shadow-md"
                  : "bg-gray-100 rounded-sm p-1 shadow-md"
              }
            >
              <InputLabel style={{ marginBottom: "10px" }}>
                Engineer Signature
              </InputLabel>
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) =>
                  handleChangeImage(loadedFiles, setFile2, url2)
                }
                dropzoneText="Drag and Drop / Click to ADD Media. MaxFileSize : 5Mb"
                showAlerts={false}
                filesLimit={1}
                maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
                onDelete={() => handleDeleteDropZone(url2, setFile2)}
                dropzoneClass={
                  currentMode === "Dark"
                    ? "dropZoneClassDark"
                    : "dropZoneClassLight"
                }
              />
              <div className="p-2 block">
                <LinearProgress
                  style={{ marginBottom: "20px" }}
                  variant="determinate"
                  value={progress2}
                />
                <div className="text-2xl text-gray-400 flex justify-end ">
                  <p> {progress2} % Uploaded</p>
                </div>
                {!url2 ? ( // url when we upload
                  <>
                    {url2 ? (
                      <img alt="" src={url2} />
                    ) : (
                      <>
                        <a target="_blank" href={urlData2} rel="noreferrer">
                          {" "}
                          <img alt="" src={urlData2} />{" "}
                        </a>
                      </>
                    )}
                  </>
                ) : (
                  <Empty
                    description={<span>Please Wait for Preview ...</span>}
                  />
                )}
              </div>
            </div>
          </div>
        </Box>
        <Box sx={{ marginBottom: "10px", marginRight: "10px" }}>
          <div className="flex justify-center">
            <div
              className={
                currentMode === "Dark"
                  ? "bg-gray-700 rounded-sm p-1 shadow-md"
                  : "bg-gray-100 rounded-sm p-1 shadow-md"
              }
            >
              <InputLabel style={{ marginBottom: "10px" }}>
                Validator Signature
              </InputLabel>
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) =>
                  handleChangeImage(loadedFiles, setFile3, url3)
                }
                dropzoneText="Drag and Drop / Click to ADD Media. MaxFileSize : 5Mb"
                showAlerts={false}
                filesLimit={1}
                maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
                onDelete={() => handleDeleteDropZone(url3, setFile3)}
                dropzoneClass={
                  currentMode === "Dark"
                    ? "dropZoneClassDark"
                    : "dropZoneClassLight"
                }
              />
              <div className="p-2 block">
                <LinearProgress
                  style={{ marginBottom: "20px" }}
                  variant="determinate"
                  value={progress3}
                />
                <div className="text-2xl text-gray-400 flex justify-end ">
                  <p> {progress3} % Uploaded</p>
                </div>
                {!url3 ? ( // url when we upload
                  <>
                    {url3 ? (
                      <img alt="" src={url3} />
                    ) : (
                      <>
                        <a target="_blank" href={urlData3} rel="noreferrer">
                          {" "}
                          <img alt="" src={urlData3} />{" "}
                        </a>
                      </>
                    )}
                  </>
                ) : (
                  <Empty
                    description={<span>Please Wait for Preview ...</span>}
                  />
                )}
              </div>
            </div>
          </div>
        </Box>
        <Box sx={{ marginBottom: "10px" }}>
          <div className="flex justify-center">
            <div
              className={
                currentMode === "Dark"
                  ? "bg-gray-700 rounded-sm p-1 shadow-md"
                  : "bg-gray-100 rounded-sm p-1 shadow-md"
              }
            >
              <InputLabel style={{ marginBottom: "10px" }}>
                Customer Signature
              </InputLabel>
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) =>
                  handleChangeImage(loadedFiles, setFile4, url4)
                }
                dropzoneText="Drag and Drop / Click to ADD Media. MaxFileSize : 5Mb"
                showAlerts={false}
                filesLimit={1}
                maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
                onDelete={() => handleDeleteDropZone(url4, setFile4)}
                dropzoneClass={
                  currentMode === "Dark"
                    ? "dropZoneClassDark"
                    : "dropZoneClassLight"
                }
              />
              <div className="p-2 block">
                <LinearProgress
                  style={{ marginBottom: "20px" }}
                  variant="determinate"
                  value={progress4}
                />
                <div className="text-2xl text-gray-400 flex justify-end ">
                  <p> {progress4} % Uploaded</p>
                </div>
                {!url4 ? ( // url when we upload
                  <>
                    {url4 ? (
                      <img alt="" src={url4} />
                    ) : (
                      <>
                        <a target="_blank" href={urlData4} rel="noreferrer">
                          {" "}
                          <img alt="" src={urlData4} />{" "}
                        </a>
                      </>
                    )}
                  </>
                ) : (
                  <Empty
                    description={<span>Please Wait for Preview ...</span>}
                  />
                )}
              </div>
            </div>
          </div>
        </Box>
      </Box>
      <Box sx={{ mt: 2, px: 2, width: "40%" }}>
        <Button
          onClick={() => handleAddData()}
          fullWidth
          variant="contained"
          color="primary"
        >
          Update Action Sheet
        </Button>
      </Box>
    </div>
  );
};

export default EditActionSheet;
