import React, { useEffect, useState } from "react";
import { TextField } from "@mui/material";
import { Button, InputLabel } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import RemoveIcon from "@mui/icons-material/Remove";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { db } from "../../../firebase";
import { firebaseLooper } from "../../../tools/tool";
import { companies, companyId_constant } from "../../../constants/data";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableContainer from "@mui/material/TableContainer";
import Paper from "@mui/material/Paper";
import { toastMessageWarning, toastMessageSuccess } from "../../../tools/toast";
import DynamicInput from "./DynamicInput";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { useAuth } from "../../../hooks/AuthProvider";
import { useStateContext } from "../../../context/ContextProvider";
import { themeColors } from "../../../infrastructure/theme";

const AddDynamicTableValues = ({
  fid,
  mid,
  docId,
  collectionId,
  type,
  handleClose,
  reportName,
  machineName,
}) => {
  const [dynamicTableName, setDynamicTableName] = useState("New Table");
  const [columnsNumber, setColumnsNumber] = useState(null);
  const [next, setNext] = useState(false);
  const [header, setHeader] = useState(false);
  const [dynamicTableData, setDynamicTableData] = useState([]);
  const [row, setRow] = useState([]);
  const [tile, setTile] = useState({});
  const [editRow, setEditRow] = useState([]);
  const [editTile, setEditTile] = useState({});
  const [openEdit, setOpenEdit] = useState([false]);
  const [editTableName, setEditTableName] = useState(false);
  const [inputTableValue, setInputTableValue] = useState();
  const { currentUser } = useAuth();
  const [user, setUser] = useState([]);
  const { currentMode } = useStateContext();
  const moduleName = type === "fatData" ? "FAT" : "SAT";

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = {
      tableName: dynamicTableName,
      dynamicTable: JSON.stringify(dynamicTableData),
    };
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(type)
    //     .doc(collectionId)
    //     .collection("dynamic_table")
    //     .add(data)
    //     .then(() => {
    //         handleClose()
    //         LoggingFunction(
    //             machineName,
    //             reportName,
    //             `${user?.fname} ${user?.lname}`,
    //             moduleName,
    //             `New Custom Table is added to ${reportName}`
    //         )
    //         toastMessageSuccess({ message: "Added details for documentation Successfully !" })
    //     })
  };

  const handleEdit = (index) => {
    const array = openEdit.map((value, idx) =>
      idx === index ? !value : value,
    );
    setOpenEdit(array);
    setEditRow(dynamicTableData[index]);
  };
  const handleDelete = (index) => {
    const array = dynamicTableData.filter((value, idx) => {
      return idx !== index;
    });
    setDynamicTableData(array);
  };
  const handleSave = (index) => {
    const array = dynamicTableData.map((value, idx) => {
      return idx === index ? editRow : value;
    });
    setDynamicTableData(array);
    handleEdit(index);
  };

  const addColumns = () => {
    let array = dynamicTableData;
    array = array.map((row, idx) => {
      if (idx === 0)
        row.push({ id: parseInt(columnsNumber), value: "Empty Head" });
      else row.push({ id: parseInt(columnsNumber), value: "Empty data" });
      return row;
    });
    array = array.map((row) => row.map((tile) => tile));
    setColumnsNumber(parseInt(columnsNumber) + 1);
    setDynamicTableData(array);
  };

  const validateHeaders = (array) => {
    let is_empty = false;
    if (JSON.stringify(row) === "[]") is_empty = true;
    array?.forEach((element) => {
      if (element.value === "") is_empty = true;
    });
    return is_empty;
  };

  useEffect(() => {
    if (JSON.stringify(row) === "[]") {
      const array = Array.apply(null, { length: columnsNumber }).fill({
        id: "",
        value: "",
      });
      array[tile.id] = tile;
      setRow(array);
    } else {
      const array = row.map((elements) => elements);
      array[tile.id] = tile;
      setRow(array);
    }
  }, [tile]);

  useEffect(() => {
    const array = editRow.map((elements) => elements);
    array[editTile.id] = editTile;
    setEditRow(array);
  }, [editTile]);

  useEffect(() => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection("userData")
    //     .where("email", "==", currentUser.email)
    //     .onSnapshot((snap) => {
    //         const data = firebaseLooper(snap);
    //         setUser(data[0]);
    //     });
  }, []);
  return (
    <form onSubmit={handleSubmit}>
      {!next && !header && (
        <>
          <InputLabel>Name of Table</InputLabel>
          <TextField
            value={dynamicTableName}
            onChange={(e) => setDynamicTableName(e.target.value)}
            style={{ marginBottom: "10px" }}
            variant="outlined"
            fullWidth
            placeholder="Enter Name of Table"
          />
          <InputLabel>Number of Columns</InputLabel>
          <TextField
            type="number"
            value={columnsNumber ? columnsNumber : ""}
            onChange={(e) => setColumnsNumber(e.target.value)}
            style={{ marginBottom: "10px" }}
            variant="outlined"
            fullWidth
            placeholder="Enter Number of Columns Needed"
          />
          <div className="mt-10 flex justify-between">
            <Button
              onClick={handleClose}
              variant="contained"
              color="success"
              endIcon={<CloseIcon />}
            >
              Cancel{" "}
            </Button>
            <Button
              onClick={() => {
                if (parseInt(columnsNumber) > 0) {
                  if (columnsNumber.toString().match(/^[0-9]+$/))
                    setNext(parseInt(columnsNumber) ? true : false);
                  else {
                    toastMessageWarning({
                      message: "Please Enter Valid Number",
                    });
                  }
                } else {
                  toastMessageWarning({
                    message: "Column Number cannot be Empty or 0",
                  });
                }
              }}
              variant="contained"
              endIcon={<ChevronRightIcon />}
            >
              Next{" "}
            </Button>
          </div>
        </>
      )}
      {next && !header && (
        <>
          {Array.apply(null, { length: columnsNumber }).map((val, idx) => (
            <DynamicInput
              key={idx}
              headerValue={`Enter Header Value for Columns ${idx + 1}`}
              prevValue=""
              column_idx={idx}
              setTile={setTile}
            />
          ))}
          <div className="mt-1 flex justify-center space-x-8">
            <Button
              onClick={() => {
                if (parseInt(columnsNumber) === 1) {
                  toastMessageWarning({
                    message: "Columns cannot be less than 1",
                  });
                } else {
                  setColumnsNumber(parseInt(columnsNumber) - 1);
                  setRow(row.slice(0, row.length - 2));
                }
              }}
              variant="contained"
              color="warning"
              size="small"
              endIcon={<RemoveIcon />}
            >
              Remove Column
            </Button>
            <Button
              onClick={() => {
                setColumnsNumber(parseInt(columnsNumber) + 1);
              }}
              variant="contained"
              color="warning"
              size="small"
              endIcon={<AddIcon />}
            >
              Add Column
            </Button>
          </div>
          <div className="mt-10 flex justify-between">
            <div className="flex justify-center space-x-4">
              <Button
                onClick={() => {
                  handleClose();
                  setColumnsNumber(0);
                }}
                variant="contained"
                color="success"
                endIcon={<CloseIcon />}
              >
                Cancel{" "}
              </Button>
              <Button
                onClick={() => {
                  setNext(false);
                }}
                color="error"
                variant="contained"
                endIcon={<ChevronLeftIcon />}
              >
                {" "}
                Back{" "}
              </Button>
            </div>
            <Button
              onClick={() => {
                if (!validateHeaders(row)) {
                  setHeader(true);
                  setDynamicTableData([row]);
                } else {
                  toastMessageWarning({
                    message: "Header cannot be Empty",
                  });
                }
              }}
              variant="contained"
              endIcon={<ChevronRightIcon />}
            >
              {" "}
              Next{" "}
            </Button>
          </div>
        </>
      )}
      {header && (
        <>
          {Array.apply(null, { length: columnsNumber }).map((val, idx) => (
            <div key={idx}>
              <InputLabel>{dynamicTableData[0][idx]?.value}</InputLabel>
              <TextField
                onChange={(e) =>
                  setTile({
                    ...tile,
                    id: idx,
                    value: e.target.value,
                  })
                }
                style={{ marginBottom: "10px" }}
                variant="outlined"
                fullWidth
                placeholder={`Enter Column ${idx + 1} Data`}
              />
            </div>
          ))}
          <div className="mt-1 flex justify-center">
            <Button
              onClick={() => {
                setDynamicTableData((dynamicTableData) => [
                  ...dynamicTableData,
                  row,
                ]);
                setOpenEdit([...openEdit, false]);
              }}
              variant="contained"
              color="warning"
              size="small"
              endIcon={<AddIcon />}
            >
              Add Row
            </Button>
          </div>
          <div className="mt-10 flex justify-around">
            {dynamicTableData.length > 1 && (
              <div style={{ minWidth: 650, width: "100%" }}>
                <div className="table_title mb-4 flex items-baseline">
                  <h1 className="font-bold underline mr-8">
                    {dynamicTableName}
                  </h1>
                  <Button onClick={() => setEditTableName(!editTableName)}>
                    <i className="ri-pencil-fill"></i>
                  </Button>
                </div>
                {editTableName && (
                  <div className="mt-4 mb-8">
                    <TextField
                      onChange={(e) => setInputTableValue(e.target.value)}
                      style={{ marginBottom: "10px" }}
                      variant="outlined"
                      fullWidth
                      placeholder={`Enter New Table Name Data`}
                    />
                    <div className="mt-2 flex justify-center">
                      <Button
                        variant="contained"
                        onClick={() => {
                          setDynamicTableName(inputTableValue);
                          setEditTableName(!editTableName);
                        }}
                        size="small"
                        endIcon={<AddIcon />}
                      >
                        {" "}
                        Save{" "}
                      </Button>
                    </div>
                  </div>
                )}
                <TableContainer component={Paper} className="table">
                  <Table sx={{ minWidth: 650, width: "100%" }}>
                    <TableHead
                      style={{
                        backgroundColor:
                          currentMode === "Dark" ? "#161C24" : "#D9D9D9",
                        fontWeight: "bold",
                        color: currentMode === "Dark" ? "white" : "black",
                        textTransform: "uppercase",
                      }}
                    >
                      <TableRow>
                        {dynamicTableData[0].map((data) => (
                          <TableCell
                            key={data?.id}
                            style={{
                              border:
                                currentMode === "Dark"
                                  ? "1px solid white"
                                  : "1px solid black",
                            }}
                            align="center"
                          >
                            {data?.value}
                          </TableCell>
                        ))}
                        <TableCell
                          align="right"
                          style={{
                            border:
                              currentMode === "Dark"
                                ? "1px solid white"
                                : "1px solid black",
                          }}
                        >
                          <Button onClick={() => handleEdit(0)}>
                            <i className="ri-pencil-fill"></i>
                          </Button>
                          <Button onClick={() => addColumns()}>
                            <i className="ri-add-line text-green-500 font-bold text-xl"></i>
                          </Button>
                        </TableCell>
                      </TableRow>
                      {openEdit[0] && (
                        <TableRow className="my-6 mx-auto">
                          <TableCell colSpan={columnsNumber}>
                            {dynamicTableData[0].map((val, column_idx) => (
                              <DynamicInput
                                className="mx-8"
                                key={column_idx}
                                headerValue={val.value}
                                prevValue={val.value}
                                column_idx={column_idx}
                                setTile={setEditTile}
                              />
                            ))}
                            <div className="mt-2 mb-4 flex justify-center">
                              <Button
                                variant="contained"
                                onClick={() => handleSave(0)}
                                size="small"
                                endIcon={<AddIcon />}
                              >
                                {" "}
                                Save{" "}
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableHead>
                    <TableBody>
                      {dynamicTableData.map((row, idx) => {
                        if (idx !== 0) {
                          return (
                            <React.Fragment key={idx}>
                              <TableRow
                                key={idx}
                                sx={{
                                  backgroundColor:
                                    currentMode === "Dark"
                                      ? themeColors.dark.primary
                                      : "#fff",
                                  color:
                                    currentMode === "Dark" ? "white" : "black",
                                  textTransform: "capitalize",
                                }}
                              >
                                {row.map((cellData, cellIdx) => (
                                  <TableCell
                                    key={cellIdx}
                                    style={{
                                      border:
                                        currentMode === "Dark"
                                          ? "1px solid white"
                                          : "1px solid black",
                                    }}
                                    align="center"
                                  >
                                    {cellData.value}
                                  </TableCell>
                                ))}
                                <TableCell
                                  key={row.length}
                                  style={{
                                    border:
                                      currentMode === "Dark"
                                        ? "1px solid white"
                                        : "1px solid black",
                                  }}
                                  align="right"
                                >
                                  <Button onClick={() => handleEdit(idx)}>
                                    <i className="ri-pencil-fill"></i>
                                  </Button>
                                  <Button onClick={() => handleDelete(idx)}>
                                    <i className="ri-delete-bin-6-fill text-red-700"></i>
                                  </Button>
                                </TableCell>
                              </TableRow>
                              {openEdit[idx] && (
                                <TableRow className="my-6">
                                  <TableCell colSpan={columnsNumber}>
                                    {dynamicTableData[idx].map(
                                      (val, column_idx) => (
                                        <DynamicInput
                                          key={column_idx}
                                          className="mx-8"
                                          headerValue={
                                            dynamicTableData[0][column_idx]
                                              .value
                                          }
                                          prevValue={val.value}
                                          column_idx={column_idx}
                                          setTile={setEditTile}
                                        />
                                      ),
                                    )}
                                    <div className="mt-2 mb-4 flex justify-center">
                                      <Button
                                        variant="contained"
                                        onClick={() => handleSave(idx)}
                                        size="small"
                                        endIcon={<AddIcon />}
                                      >
                                        {" "}
                                        Save{" "}
                                      </Button>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              )}
                            </React.Fragment>
                          );
                        }
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </div>
            )}
          </div>
          <div className="mt-8 flex justify-between">
            <div className="flex justify-center space-x-4">
              <Button
                onClick={() => {
                  handleClose();
                  setColumnsNumber(0);
                  setHeader(false);
                }}
                variant="contained"
                color="success"
                endIcon={<CloseIcon />}
              >
                Cancel{" "}
              </Button>
              <Button
                onClick={() => {
                  setHeader(false);
                }}
                color="error"
                variant="contained"
                endIcon={<ChevronLeftIcon />}
              >
                {" "}
                Back{" "}
              </Button>
            </div>
            <Button type="submit" variant="contained" endIcon={<AddIcon />}>
              {" "}
              Save{" "}
            </Button>
          </div>
        </>
      )}
    </form>
  );
};

export default AddDynamicTableValues;
