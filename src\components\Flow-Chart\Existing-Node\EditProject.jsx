import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  InputLabel,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Select,
  MenuItem,
} from "@mui/material";
import { toastMessageWarning, toastMessageSuccess } from "../../../tools/toast";
import { updateData } from "../../../services2/issueModule/IssueModule.services";
import { useStateContext } from "../../../context/ContextProvider";
import { useEditMachineCfr } from "../../../hooks/cfr/machineCfrProvider";
import { useAuth } from "../../../hooks/AuthProvider";
import { useIssueModuleChangeCount } from "../../../services2/issueModule/IssueModule.context";

function EditProject(props) {
  const {
    openEdit,
    handleClose,
    machinesData,
    id,
    name,
    midprop,
    machine,
    userName,
    values,
  } = props;
  const editissuecfr = useEditMachineCfr();
  const { currentUser } = useAuth();

  const { issueCount, setIssueCount } = useIssueModuleChangeCount();

  const [title, setTitle] = useState(name ? name : "");
  const [mid, setMid] = useState("");

  const [filteredMachine, setFilteredMachine] = useState({});
  const { currentMode } = useStateContext();
  console.log(" machine", machine, title, values);

  const handleFilter = (value) => {
    setFilteredMachine(value);
    setMid(filteredMachine._id);
  };

  useEffect(() => {
    setTitle(name ? name : "");
  }, [name, openEdit]);

  const handleSubmit = (e) => {
    console.log("filtered machine", midprop);
    e.preventDefault();
    const data = {
      mid: filteredMachine.length > 0 ? filteredMachine._id : midprop,
      name: title,

      values: values,
      date: new Date().toISOString(),
    };
    const date = new Date();
    const data2 = {
      activity: "issue module edited",
      dateTime: date,
      description: "an issue module is edited",
      machine: filteredMachine?._id,
      module: "Machine",
      username: currentUser.username,
    };
    if (!title) {
      toastMessageWarning({
        message: "Project Name Cannot be empty",
      });
    } else if (!filteredMachine) {
      toastMessageWarning({
        message: "Project Machine Cannot be empty",
      });
    } else {
      updateData("issueModule", id, data).then(() => {
        //editissuecfr(data2);
        setIssueCount(issueCount + 1);
        toastMessageSuccess(
          {
            message: "Project details has been updated successfully",
          },
          (error) => console.log(error),
        );
      });

      handleClose();
    }
  };

  // To autopopulate name in edit modal if previously name field was empty
  useEffect(() => {
    if (title === "" && openEdit) {
      setTitle(name || "");
    }
  }, [openEdit]);

  return (
    <Dialog open={openEdit} fullWidth>
      <DialogTitle
        style={
          currentMode === "Dark"
            ? { backgroundColor: "#212B36", color: "white" }
            : {}
        }
      >
        Edit Project Details
      </DialogTitle>
      <DialogContent
        style={
          currentMode === "Dark"
            ? { backgroundColor: "#212B36", color: "white" }
            : {}
        }
      >
        <InputLabel style={{ marginBottom: "10px" }}>Project Title</InputLabel>
        <TextField
          onChange={(e) => setTitle(e.target.value)}
          onBlur={() => setTitle(title?.trim())}
          value={title}
          required
          variant="outlined"
          fullWidth
          style={{ marginBottom: "12px" }}
        />
        <InputLabel style={{ marginBottom: "10px" }}>
          Select Machines
        </InputLabel>
        <Select
          value={filteredMachine}
          className="select-machine"
          name="machines"
          fullWidth
          disabled
          renderValue={(selected) =>
            selected?.title
              ? selected?.title
              : machine?.title
                ? machine?.title
                : "No Machine Selected"
          }
          onChange={(e) => handleFilter(e.target.value)}
        >
          {machinesData.map((machine) => {
            return (
              <MenuItem
                key={machine?._id}
                className="option-machine"
                value={machine}
              >
                {machine?.title}
              </MenuItem>
            );
          })}
        </Select>
        {/* 
        <DialogActions className="p-2 mt-2 flex justify-between" style={{display: 'flex',justifyContent:'space-between'}}> 
          */}
        <DialogActions>
          <Button color="error" onClick={handleClose} variant="contained">
            Cancel
          </Button>
          <Button
            color="primary"
            type="submit"
            variant="contained"
            onClick={handleSubmit}
          >
            Submit
          </Button>
        </DialogActions>
      </DialogContent>
    </Dialog>
  );
}

export default EditProject;
