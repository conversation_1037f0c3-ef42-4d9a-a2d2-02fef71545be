import {
  <PERSON>ir<PERSON><PERSON>rogress,
  <PERSON><PERSON><PERSON><PERSON>on,
  <PERSON><PERSON>ation,
  <PERSON>ack,
  Typography,
} from "@mui/material";
import { v4 as uuidv4 } from "uuid";
import React, { useContext, useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { companies, companyId_constant, machines } from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import "./Machine.scss";
import MachineItem from "./MachineItem";
import ManageSOP from "./ManageSOP";
import SettingsIcon from "@mui/icons-material/Settings";
import Box from "@mui/material/Box";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import { useStateContext } from "../../context/ContextProvider";
import {
  Input<PERSON>abel,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
} from "@mui/material";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";
import { themeColors } from "../../infrastructure/theme";
import PageHeader from "../../components/commons/page-header.component";
import { SearchOutlined } from "@mui/icons-material";
import { MachinesContext } from "../../services/machines/MachineContext";
import { updateDocument } from "../../utils/firebase-utils";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import NoData from "../../components/no-data/no-data.component";
import { makeStyles } from "@mui/styles";
import Button from "@mui/material/Button";
import { sharedCss } from "../../styles/sharedCss";
import {
  useEditMachinesCountGetter,
  useMachinesAllGetter,
  useMachinesSetter,
} from "../../services3/machines/MachineContext2";
import {
  useEditMachines,
  useEditMachinesGetter,
} from "../../services3/machines/EditMachineContext";
import { useAuth } from "../../hooks/AuthProvider";
import { useUtils } from "../../hooks/UtilsProvider";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";

const Machine = () => {
  // const [machines, setMachines] = useState([])

  const [openSOP, setOpenSOP] = useState(false);
  const [selectedMachine, setSelectedMachine] = useState(null);

  const handleOpenSOP = (machine) => {
    setSelectedMachine(machine);
    setOpenSOP(true);
  };
  const [allMachinesData, setAllMachinesData] = useState([]);
  const [machinesInView, setMachinesInView] = useState([]);
  const [loading, setLoading] = useState(true);
  const [machinesInViewContainerCount, setMachinesInViewContainerCount] =
    useState(0); // number of pages
  const [page, setPage] = useState(1);
  const [numberOfMachineInView, setNumberOfMachinesInView] = useState(6);
  const [sortBy, setSortBy] = useState("none");
  const [sortDirection, setSortDirection] = useState("up");
  const [searchKey, setSearchKey] = useState("");
  const [searchedMachines, setSearchedMachines] = useState([]);
  const { currentMode, currentColorLight } = useStateContext();
  const { setRefreshCount, refreshCount } = useMongoRefresh();

  const canPost = useCheckAccess("machines", "POST");

  const useCustomStyles = makeStyles((theme) => ({
    machineContainer: {
      padding: "1rem",
      // borderRadius: "10px",
      backgroundColor: theme.palette.custom.backgroundForth,
      boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
    },
    machinesOuterContainer: {
      width: "100%",
    },
    machinesInnerContainer: {
      display: "flex",
      flexWrap: "wrap",
      justifyContent: "space-between",
    },
    addButton: {
      width: "max-content",
    },
    machinePageContainer: {
      // padding: "1rem",
      // border: "1px solid gainsboro",
    },
  }));

  //u.m.2
  const [title, setTitle] = useState();
  const [desc, setDesc] = useState();
  const [location, setLocation] = useState();
  const [equipmentId, setEquipmentId] = useState();
  const [serialNo, setSerialNo] = useState();
  const [model, setModel] = useState();
  const [block, setBlock] = useState();
  const [warranty, setWarranty] = useState();
  const [status, setStatus] = useState();
  const [currentMachineId, setCurrentMachineId] = useState("");
  const [openEdit, setOpenEdit] = useState(false);
  const [machineId, setMachineId] = useState();
  const editCount = useEditMachinesGetter();
  const [oeeData, setOeeData] = useState([]);
  const editMachineCount = useEditMachinesCountGetter();
  // const handleMachineData = async () => {
  //   await axios.get(`${dbConfig.url}/machines`).then((response) => {
  //    setMachines(response.data)
  //   }).catch((error) => {
  //     console.error(error);
  //   })
  // };
  const handleMachineData = useMachinesSetter();
  const machines = useMachinesAllGetter();
  // useEffect(() => {

  //console.log("machines", machines);
  // }, []); //refreshCount

  useEffect(() => {
    console.log("editMachineCount", editMachineCount);
    console.log("editMachineCount2", machines);
    handleMachineData();
    if (machines.length) {
      setLoading(false);
      const data = machines;
      let machineActivePageNum = window.localStorage.getItem(
        "machineActivePageNum",
      ); // for preventing pagination mismatch while editing 2/3
      let numOfMachineInViewSession =
        window.localStorage.getItem("numOfMachineInView"); //
      setAllMachinesData(data?.reverse());
      setMachinesInView(data.slice(0, numberOfMachineInView));
      machinesPageInfoContainerCounter(data.length, numberOfMachineInView);
      // for preventing pagination mismatch while editing 3/3
      if (machineActivePageNum != 1 && machineActivePageNum) {
        let machineInViewContainerCountTemp;
        let mp = machinesPageInfoContainerCounter(
          data.length,
          numOfMachineInViewSession,
        ); //if it is last page
        if (machineActivePageNum == machineInViewContainerCountTemp) {
          setMachinesInView(
            data.slice((machineActivePageNum - 1) * numOfMachineInViewSession),
          );
          setNumberOfMachinesInView(numOfMachineInViewSession);
          setPage(machineActivePageNum);
          setNumberOfMachinesInView(numOfMachineInViewSession);
        } else {
          // if it's not the last page
          setMachinesInView(
            data.slice(
              (machineActivePageNum - 1) * numOfMachineInViewSession,
              machineActivePageNum * numOfMachineInViewSession,
            ),
          );
          setNumberOfMachinesInView(numOfMachineInViewSession);
          setPage(machineActivePageNum);
          setNumberOfMachinesInView(numOfMachineInViewSession);
        }
      }
    }
    // }, [machines.length, editMachineCount, numberOfMachineInView.length]);
  }, [machines.length, editMachineCount, numberOfMachineInView.length]);

  const getOeeData = async () => {
    await axios.get(`${dbConfig.url}/oee`).then((response) => {
      setOeeData(response.data);
      console.log("OEEE >>  ", response.data);
    });
  };

  useEffect(() => {
    getOeeData();
  }, []);

  //u.m.4
  const handleOnClikEditMachine = (machine) => {
    setTitle(machine.title);
    setDesc(machine.desc);
    setLocation(machine.location);
    setEquipmentId(machine.equipmentId);
    setSerialNo(machine.serialNo);
    setModel(machine.model);
    setBlock(machine.block);
    setWarranty(machine.warranty);
    setStatus(machine.status);
    setMachineId(machine._id);
    setCurrentMachineId(machine.createdBy);
    setOpenEdit(true);
  };

  const machinesPageInfoContainerCounter = (
    allMachinesCount,
    numberOfMachineInViewProp,
  ) => {
    var temp = Math.ceil(allMachinesCount / numberOfMachineInViewProp);
    setMachinesInViewContainerCount(temp);
    return temp;
  };

  const handlePageChange = (event, value) => {
    window.localStorage.setItem("numOfMachineInView", numberOfMachineInView);
    setPage(value);

    if (searchedMachines.length > 0) {
      if (value == machinesInViewContainerCount) {
        setMachinesInView(
          searchedMachines.slice((value - 1) * numberOfMachineInView),
        );
      } else {
        setMachinesInView(
          searchedMachines.slice(
            (value - 1) * numberOfMachineInView,
            value * numberOfMachineInView,
          ),
        );
      }
    } else {
      if (value == machinesInViewContainerCount) {
        setMachinesInView(
          allMachinesData.slice((value - 1) * numberOfMachineInView),
        );
      } else {
        setMachinesInView(
          allMachinesData.slice(
            (value - 1) * numberOfMachineInView,
            value * numberOfMachineInView,
          ),
        );
      }
    }
  };

  const handleNumberOfMachineInView = (e) => {
    setNumberOfMachinesInView(e.target.value);
    machinesPageInfoContainerCounter(allMachinesData?.length, e.target.value);
    setMachinesInView(allMachinesData.slice(0, e.target.value));
  };
  const handleSort = (e) => {
    setSortBy(e.target.value);

    if (e.target.value == "name") {
      let temp = machinesInView;

      if (sortDirection == "up") {
        temp.sort((a, b) => {
          return a.title?.toLowerCase() > b.title?.toLowerCase() ? 1 : -1;
        });
      } else if (sortDirection == "down") {
        temp.sort((a, b) => {
          return a.title?.toLowerCase() < b.title?.toLowerCase() ? 1 : -1;
        });
      }

      setMachinesInView([...temp]);
    } else if (e.target.value == "equip_id") {
      let temp = machinesInView;

      if (sortDirection == "up") {
        temp.sort((a, b) => {
          return a.equipmentId?.toLowerCase() > b.equipmentId?.toLowerCase()
            ? 1
            : -1;
        });
      } else if (sortDirection == "down") {
        temp.sort((a, b) => {
          return a.equipmentId?.toLowerCase() < b.equipmentId?.toLowerCase()
            ? 1
            : -1;
        });
      }

      setMachinesInView([...temp]);
    } else if (e.target.value == "model") {
      let temp = machinesInView;

      if (sortDirection == "up") {
        temp.sort((a, b) => {
          return a.model?.toLowerCase() > b.model?.toLowerCase() ? 1 : -1;
        });
      } else if (sortDirection == "down") {
        temp.sort((a, b) => {
          return a.model?.toLowerCase() < b.model?.toLowerCase() ? 1 : -1;
        });
      }

      setMachinesInView([...temp]);
    } else if (e.target.value == "last_updated") {
      let temp = machinesInView;

      if (sortDirection == "up") {
        temp.sort((a, b) => {
          return a.lastUpdated > b.lastUpdated ? 1 : -1;
        });
      } else if (sortDirection == "down") {
        temp.sort((a, b) => {
          return a.lastUpdated < b.lastUpdated ? 1 : -1;
        });
      }

      setMachinesInView([...temp]);
    } else if (e.target.value == "none") {
      // setMachinesInView(allMachinesData.slice(0, numberOfMachineInView));
      e.target.value = searchKey;
      searchMachines(e);
    }
  };
  const handleSortDirection = (direction) => {
    let temp = machinesInView;
    if (direction == "up") {
      temp.sort((a, b) => {
        return a.title?.toLowerCase() < b.title?.toLowerCase() ? 1 : -1;
      });
      setSortDirection("down");
    } else if (direction == "down") {
      temp.sort((a, b) => {
        return a.title?.toLowerCase() > b.title?.toLowerCase() ? 1 : -1;
      });
      setSortDirection("up");
    }
    setMachinesInView([...temp]);
  };
  //
  const searchMachines = (e) => {
    setSearchKey(e.target.value);
    console.log("searchMachines", e.target.value);
    const temp = allMachinesData;
    const temp2 = temp?.filter(
      (a) =>
        a.title?.toUpperCase().search(e.target.value.toUpperCase()) >= 0 || //  for dictionary search use (=== 0)
        a.equipment_id?.toUpperCase().search(e.target.value.toUpperCase()) >= 0,
    );
    setSearchedMachines([...temp2]); // this data is to handle pagination after searching
    setMachinesInView(temp2?.slice(0, numberOfMachineInView));
    machinesPageInfoContainerCounter(temp2?.length, numberOfMachineInView);
  };

  const commonCss = sharedCss();
  const customCss = useCustomStyles();

  const handleExportMachine = (machine) => {
    const json = JSON.stringify(machine, null, 2);
    const blob = new Blob([json], { type: "application/json" });
    const url = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.download = `machine_${machine._id}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <Box className={customCss.machinePageContainer}>
      <section
        className={`${commonCss.headingContainer} border-radius-outer`}
        style={{ padding: "0.5rem 1rem" }}
      >
        <Box>
          <Typography variant="h4">Machine</Typography>
          <Typography variant="h6 ">List of all Machines & Details</Typography>
        </Box>
        <Box
          style={{
            display: "flex",
            flexDirection: "row",
            gap: "1rem",
            placeItems: "center",
          }}
        >
          <Box
            component="form"
            sx={{
              "& > :not(style)": { width: "35ch" },
            }}
          >
            <TextField
              label="Search"
              className={commonCss.searchBox}
              id="outlined-size-small"
              placeholder="Name, EQP ID"
              size="small"
              value={searchKey}
              onChange={(e) => searchMachines(e)}
              InputProps={{
                startAdornment: (
                  <IconButton>
                    <SearchOutlined />
                  </IconButton>
                ),
              }}
            />
          </Box>
          <FormControl fullWidth size="small">
            <InputLabel id="demo-simple-select-label">Sort</InputLabel>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              value={sortBy}
              label="Sort"
              onChange={(e) => handleSort(e)}
            >
              <MenuItem value="none">None</MenuItem>
              <MenuItem value="name">By Name</MenuItem>
              <MenuItem value="model">By Model</MenuItem>
              <MenuItem value="last_updated">By Last Updated</MenuItem>
              <MenuItem value="equip_id">By Equipment</MenuItem>
            </Select>
          </FormControl>
          <FormControl fullWidth size="small">
            <InputLabel id="demo-simple-select-label">Items</InputLabel>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              value={numberOfMachineInView}
              label="Items"
              onChange={(e) => handleNumberOfMachineInView(e)}
            >
              <MenuItem value={6}>6</MenuItem>
              <MenuItem value={12}>12</MenuItem>
              <MenuItem value={18}>18</MenuItem>
            </Select>
          </FormControl>
          <Link
            to="/add-machine"
            className="formLink "
            onClick={(e) => {
              if (!canPost) e.preventDefault();
            }}
          >
            <Button
              className={customCss.addButton}
              variant="contained"
              disabled={!canPost}
            >
              Add Machine
            </Button>
          </Link>
        </Box>
      </section>
      <section className={`${customCss.machineContainer} border-radius-inner`}>
        <div
          style={{
            display: "flex",
            gap: "1rem",
            justifyContent: "center",
            flexWrap: "wrap",
          }}
          className="machinesInnerContainer "
        >
          {machinesInView.length > 0
            ? loading && <CircularProgress variant="indeterminate" />
            : ""}
          {useCheckAccess("machines", "GET") ? (
            machinesInView.length > 0 ? (
              machinesInView.map((machine, index) => (
                <div key={uuidv4()} style={{ position: "relative" }}>
                  <MachineItem
                    machine={machine}
                    onClickEdit={() => handleOnClikEditMachine(machine)}
                    oee={oeeData?.filter((item) => item.mid === machine._id)}
                    onClickSettings={() => handleOpenSOP(machine)}
                  />
                  <Button
                    variant="outlined"
                    size="small"
                    style={{ position: "absolute", top: 8, right: 8, zIndex: 2 }}
                    onClick={() => handleExportMachine(machine)}
                  >
                    Export JSON
                  </Button>
                </div>
              ))
            ) : (
              <Typography variant="h5">NO MACHINES</Typography>
            )
          ) : (
            <NotAccessible />
          )}
        </div>
        {!!machinesInView.length && (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              marginTop: "1rem",
            }}
          >
            <Stack spacing={2}>
              <Pagination
                count={machinesInViewContainerCount}
                page={page}
                color="primary"
                onChange={handlePageChange}
              />
            </Stack>
          </div>
        )}
      </section>
      <ManageSOP
        open={openSOP}
        machine={selectedMachine}
        onClose={() => setOpenSOP(false)}
      />
    </Box>
  );
};

export default Machine;
