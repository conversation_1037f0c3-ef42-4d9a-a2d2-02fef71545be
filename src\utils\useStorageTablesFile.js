import { auth } from "../firebase"; // Replace 'storage' with 'auth' or correct export
import { useState, useEffect, useContext } from "react";
import { FileManagerSelectorContext } from "../services/fileManager/file-manager-select.context";

export const useStorageTablesFile = (file) => {
  const [progress, setProgress] = useState(0);
  const { changeFileUrl } = useContext(FileManagerSelectorContext);
  const [error, setError] = useState(null);
  const [url, setUrl] = useState(null);

  // runs every time the file value changes
  useEffect(() => {
    if (file) {
      // storage ref
      const storageRef = auth.ref(`table/${file.name}`);
      storageRef.put(file).on(
        "state_changed",
        (snap) => {
          // track the upload progress
          let percentage = Math.round(
            (snap.bytesTransferred / snap.totalBytes) * 100,
          );
          setProgress(percentage);
        },
        (err) => {
          setError(err);
        },
        async () => {
          // get the public download img url
          const downloadUrl = await storageRef.getDownloadURL();

          // save the url to local state
          setUrl(downloadUrl);
          changeFileUrl(downloadUrl);
        },
      );
    } else {
      setProgress(0);
      setUrl(null);
    }
  }, [file]);

  return { progress, url, error };
};
