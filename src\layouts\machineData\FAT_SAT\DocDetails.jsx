import React, { useState } from "react";
import { useParams } from "react-router-dom";
import "../machineData.scss";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { Button, Dialog, DialogContent, DialogTitle } from "@mui/material";
import AddDoc from "./AddDoc";
import DocItem from "./DocItem";
import { ButtonBasic } from "../../../components/buttons/Buttons";
import { useStateContext } from "../../../context/ContextProvider";
import { themeColors } from "../../../infrastructure/theme";
import { makeStyles } from "@mui/styles";
import { useContentGetter } from "../../../services3/audits/ContentContext";

const useCustomStyles = makeStyles((theme) => ({
  fatInnerContent: {
    padding: "1.5rem !important",
    borderRadius: "0px !important",
    backgroundColor: `${theme.palette.custom.backgroundForth} !important`,
  },
}));
const DocDetails = ({ type, machineName, details, id, user }) => {
  const { mid } = useParams();
  const [open, setOpen] = useState(false);
  const { currentMode } = useStateContext();
  const menuItemTheme = {
    backgroundColor:
      currentMode === "Dark"
        ? themeColors.dark.primary
        : themeColors.light.primary,
  };
  const customCss = useCustomStyles();
  // const details= useContentGetter();
  return (
    <section className="machineDataViewPage">
      <div className="allMachineDataPreviewContainer">
        <div className={`${customCss.fatInnerContent} liveDataOuterContainer `}>
          <div className="liveDataHeading">
            <div className="title"></div>
            <div className="btn">
              <Button variant="contained" onClick={() => setOpen(true)}>
                Add Content
              </Button>
            </div>
          </div>

          <div className="liveDataContainer">
            <TableContainer
              component={Paper}
              className="table"
              style={
                currentMode === "Dark"
                  ? {
                      backgroundColor: "#161C24",
                      color: "white",
                      border: "1px solid white",
                    }
                  : { border: "1px solid black" }
              }
            >
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow>
                    <TableCell
                      sx={{
                        maxWidth: 150,
                        width: 150,
                      }}
                      style={currentMode === "Dark" ? { color: "white" } : {}}
                      align="left"
                    >
                      Index
                    </TableCell>
                    <TableCell
                      align="left"
                      style={currentMode === "Dark" ? { color: "white" } : {}}
                    >
                      Name
                    </TableCell>
                    <TableCell
                      sx={{
                        maxWidth: 400,
                        width: 400,
                      }}
                      style={currentMode === "Dark" ? { color: "white" } : {}}
                      align="center"
                    >
                      Actions
                    </TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {details?.length > 0 ? (
                    details
                      ?.filter((element) => element.mid === mid)
                      .map((data, idx) => (
                        <DocItem
                          idx={idx + 1}
                          key={data._id}
                          id={data._id}
                          fatSeriesId={id}
                          mid={mid}
                          type={type}
                          data={data}
                          user={user}
                          machineName={machineName}
                        />
                      ))
                  ) : (
                    <TableRow
                      sx={{
                        "&:last-child td, &:last-child th": {
                          border: 0,
                        },
                      }}
                    >
                      <TableCell
                        style={{ borderBottom: "none" }}
                        align="center"
                        colSpan={8}
                      >
                        {!details?.length && (
                          <div
                            style={
                              currentMode === "Dark" ? { color: "white" } : {}
                            }
                          >
                            No Data
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        </div>
      </div>
      <Dialog open={open} fullWidth>
        <DialogTitle style={menuItemTheme}>Add {type} Details </DialogTitle>
        <DialogContent style={menuItemTheme}>
          <AddDoc
            mid={mid}
            handleClose={() => setOpen(false)}
            type={type}
            machineName={machineName}
            id={id}
            index={details?.filter((element) => element.mid === mid).length + 1}
          />
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default DocDetails;
