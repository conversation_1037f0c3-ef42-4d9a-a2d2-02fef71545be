import React from "react";

const ImageDisplay = ({ imageUrl, title, description }) => {
  return (
    <div className="max-w-sm mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-lg">
      <img className="w-full object-cover" src={imageUrl} alt={title} />
      {/* <div className="p-4">
        <div className="uppercase tracking-wide text-sm text-indigo-500 font-semibold">{title}</div>
        <p className="mt-2 text-gray-500">{description}</p>
      </div> */}
    </div>
  );
};

export default ImageDisplay;
