import React from "react";
import { useStateContext } from "../../context/ContextProvider";

const LeftContainer = () => {
  const { currentMode } = useStateContext();
  const background = {
    backgroundColor: currentMode === "Dark" ? "#212B36" : "",
  };
  return (
    <div className="leftContainer" style={background}>
      <ul>
        <li>
          <div className="icons">
            <i className="ri-rocket-2-fill"></i>
          </div>
          <div className="text">
            <p>Profile</p>
          </div>
        </li>
        <li>
          <div className="icons">
            <i className="ri-file-info-fill"></i>
          </div>
          <div className="text">
            <p>Basic Info</p>
          </div>
        </li>
        <li>
          <div className="icons">
            <i className="ri-lock-2-fill"></i>
          </div>
          <div className="text">
            <p>Change Password</p>
          </div>
        </li>
        {/* <li>
          <div className="icons">
            <i className="ri-bank-card-fill"></i>
          </div>
          <div className="text">
            <p>Delete Account</p>
          </div>
        </li> */}
      </ul>
    </div>
  );
};

export default LeftContainer;
