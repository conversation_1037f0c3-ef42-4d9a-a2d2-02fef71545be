/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable jsx-a11y/no-redundant-roles */
import React, { useState } from "react";
import CheckIcon from "@mui/icons-material/Check";
import QuestionMarkIcon from "@mui/icons-material/QuestionMark";
import { useStorage } from "../../utils/useStorage";
import { companies, companyId_constant, fatReport } from "../../constants/data";
import { db } from "../../firebase";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import { useParams } from "react-router-dom";

const Approval = ({ companyId, mDetail, reportDetails, approvalData }) => {
  const [file, setFile] = useState(null);
  const today = new Date();
  const { approvalId, fid, eid } = useParams();

  const handleChange = (e) => {
    let selectedFile = e.target.files[0];
    setFile(selectedFile);
  };

  const { progress, url } = useStorage(file);

  const handleApproval = (e) => {
    // db.collection(companies).doc(companyId)
    // .collection(fatReport).doc(fid).collection('fatData')
    // .doc(approvalId).collection('approval').doc(eid).update({status: true, date: new Date()})
    // .then(() => {
    //     toastMessageSuccess({message: 'You have successfully approved this document . Thank you!'})
    // })
  };

  return (
    <div
      style={{ height: "100vh" }}
      className="bg-gradient-to-b  from-green-100 to-gray-200 w-full"
    >
      <div className="container m-auto px-6 py-20 md:px-14 lg:px-22">
        <div className="mt-12 m-auto -space-y-4 items-center justify-center md:flex md:space-y-0 md:-space-x-4 xl:w-10/12">
          <div className="relative z-10 -mx-4 group md:w-6/12 md:mx-0 lg:w-5/12">
            <div
              aria-hidden="true"
              className="absolute top-0 w-full h-full rounded-2xl bg-white shadow-xl transition duration-500 group-hover:scale-105 lg:group-hover:scale-110"
            ></div>
            <div className="relative p-6 space-y-6 lg:p-8">
              <h3 className="text-3xl text-gray-700 font-semibold text-center">
                Approve Document
              </h3>
              {/* <h5 className="text-xl text-gray-700 font-semibold text-center">  {reportDetails?.title}</h5> */}

              <ul
                role="list"
                className="w-max space-y-4 py-6 m-auto text-gray-600"
              >
                <li className="space-x-2">
                  <span className="text-teal-500 font-semibold">
                    <CheckIcon />
                  </span>
                  <span>{reportDetails?.description}</span>
                </li>
                <li className="space-x-2">
                  <span className="text-teal-500 font-semibold">
                    <CheckIcon />
                  </span>
                  <span> {mDetail.model} </span>
                </li>
                {/* <li className="space-x-2">
                          {url ? 
                           <span className="text-teal-500 font-semibold">
                           <CheckIcon/>
                           </span>
                           
                          :  <span className="text-amber-500 font-semibold">
                                <QuestionMarkIcon/>
                            </span>}
                            <span>Upload Signature</span>
                        </li> */}
                {/* {url ? 
                       <img width="300px" src={url}/>
                       :  <li className="space-x-3">
                           <input type="file" onChange={handleChange}/>
                           <h6>{progress} % Uploaded</h6>
                        </li>} */}
              </ul>

              <button
                onClick={handleApproval}
                title="Submit"
                className="block w-full py-3 px-6 text-center rounded-xl transition bg-sky-600 hover:bg-sky-700 active:bg-sky-800 focus:bg-teal-600"
              >
                <span className="text-white font-semibold">
                  Approve Document
                </span>
              </button>
            </div>
          </div>

          <div className="relative group md:w-6/12 lg:w-7/12">
            <div
              aria-hidden="true"
              className="absolute top-0 w-full h-full rounded-2xl bg-white shadow-lg transition duration-500 group-hover:scale-105"
            ></div>
            <div className="relative p-6 pt-16 md:p-8 md:pl-12 md:rounded-r-2xl lg:pl-20 lg:p-16">
              <ul role="list" className="space-y-4 py-6 text-gray-600">
                <li className="space-x-2">
                  <span className="text-teal-500 font-semibold">
                    <CheckIcon />
                  </span>
                  <span>
                    <b>Name :</b> {approvalData.name}
                  </span>
                </li>
                <li className="space-x-2">
                  <span className="text-teal-500 font-semibold">
                    <CheckIcon />
                  </span>
                  <span>
                    <b>Protocol No : </b> {reportDetails.protocol_no}{" "}
                  </span>
                </li>
                <li className="space-x-2">
                  <span className="text-teal-500 font-semibold">
                    <CheckIcon />
                  </span>
                  <span>
                    <b>Serial No : </b> {mDetail.serialNo}{" "}
                  </span>
                </li>

                <li className="space-x-2">
                  <span className="text-teal-500 font-semibold">
                    <CheckIcon />
                  </span>
                  <span>
                    <b>Email :</b> {approvalData.email}
                  </span>
                </li>
                <li className="space-x-2">
                  <span className="text-teal-500 font-semibold">
                    <CheckIcon />
                  </span>
                  <span>
                    <b>Approval ID : </b> {approvalData.approvalId}{" "}
                  </span>
                </li>

                <li className="space-x-2">
                  <span className="text-teal-500 font-semibold">
                    <CheckIcon />
                  </span>
                  <span>
                    <b> Approval Date : </b> {today.toString().substring(0, 15)}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Approval;
