import { Button, Dialog, DialogActions, DialogTitle } from "@mui/material";
import React from "react";
import PropTypes from "prop-types";

const StdNotAvailModal = ({
  open = false,
  setOpen = () => {},
  desc = "Not Available for Standard.",
  pending = false,
}) => {
  return (
    <Dialog open={open} maxWidth="xs">
      <DialogTitle>{desc}</DialogTitle>
      <DialogActions>
        {/* <Button onClick={action} color='warning' disabled={pending} >Yes</Button> */}
        <Button onClick={() => setOpen(false)} disabled={pending}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

StdNotAvailModal.propTypes = {
  open: PropTypes.bool, // Indicates whether the modal is open
  setOpen: PropTypes.func.isRequired, // Function to toggle the modal's open state
  desc: PropTypes.string, // Description to be displayed in the modal title
  pending: PropTypes.bool, // Indicates if the close button should be disabled
};

StdNotAvailModal.defaultProps = {
  open: false, // Default modal state is closed
  desc: "Not Available for Standard.", // Default description text
  pending: false, // Default pending state is false
};

export default StdNotAvailModal;
