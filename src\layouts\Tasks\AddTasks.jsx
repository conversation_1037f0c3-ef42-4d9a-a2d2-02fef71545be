import React, { useState } from "react";
import {
  Box,
  Menu,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";

const AddTasks = () => {
  const [taskName, setTaskName] = useState("");
  const [taskDesc, setTaskDesc] = useState("");
  const [assignTo, setAssignTo] = useState("");
  const [taskType, setTaskType] = useState("");
  const [taskStartDate, setTaskStartDate] = useState(null);
  const [taskEndDate, setTaskEndDate] = useState(null);

  return (
    <Box>
      <Typography>Add Task</Typography>
      <Select
        fullWidth
        sx={{
          mt: "20px",
          mb: "10px",
        }}
      >
        <MenuItem>Active</MenuItem>
        <MenuItem>Pending</MenuItem>
        <MenuItem>Closed</MenuItem>
      </Select>
      <TextField
        fullWidth
        sx={{
          mt: "20px",
          mb: "10px",
        }}
        placeholder="Task Name"
        onChange={(e) => setTaskName(e.target.value)}
      />
      <TextField
        fullWidth
        sx={{
          mt: "20px",
          mb: "10px",
        }}
        onChange={(e) => setTaskDesc(e.target.value)}
        placeholder="Task Description"
      />

      <Box
        maxWidth="300px"
        justifyContent="space-between"
        sx={{ display: "flex" }}
      >
        <Typography>Start Date</Typography>
        {/* Start Date picker */}
        <Typography> End Date</Typography>
        {/* ENd date picker */}
      </Box>
      <TextField
        type="email"
        fullWidth
        sx={{
          mt: "20px",
          mb: "10px",
        }}
        placeholder="Assign To"
      />
    </Box>
  );
};

export default AddTasks;
