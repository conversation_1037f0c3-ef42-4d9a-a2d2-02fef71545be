const filterTableArrayBasedOnTitleForTraining = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      company_name: mData[1],
      designation: mData[2],
      department: mData[3],
      sign_date: mData[4],
    });
    //console.log("index",index,":",mData)
  });
  // console.log("tempData:",tempTableData);
  return tempTableData;
};

const filterTableArrayBasedOnTitleForReferenceDocuments = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      document_name: mData[1],
      document_no: mData[2],
      rev_no: mData[3],
    });
    //console.log("index",index,":",mData)
  });
  // console.log("tempData:",tempTableData);
  return tempTableData;
};

const filterTableArrayBasedOnTitleForReferenceInstruments = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      measuring_device: mData[1],
      certificate_no: mData[2],
      calibration_done_on: mData[3],
      calibration_due_on: mData[4],
    });
    //console.log("index",index,":",mData)
  });
  // console.log("tempData:",tempTableData);
  return tempTableData;
};

const filterTableArrayBasedOnTitleForPostApproval = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      responsibility: mData[0],
      name: mData[1],
      department: mData[2],
      signature: mData[3],
      date: mData[4],
    });
    //console.log("index",index,":",mData)
  });
  // console.log("tempData:",tempTableData);
  return tempTableData;
};

const filterTableArrayBasedOnTitleForSummaryConclusion = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      installation_report_summary: mData[1],
      obserbation: mData[2],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForPreRequisite = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      pre_requisites: mData[1],
      availability: mData[2],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForExecutionTablePID = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      test_criteria: mData[0],
      method_of_verification: mData[1],
      acceptance_criteria: mData[2],
      obserbation: mData[3],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForTestResult = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      test_criteria: mData[0],
      method_of_verification: mData[1],
      acceptance_criteria: mData[2],
      obserbation: mData[3],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForExecutionTable_12_1_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      standard_ins_reading: mData[0],
      indicator_reading: mData[1],
      measuring_error: mData[2],
      obserbation: mData[3],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForExecutionTable_12_2_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      set_val: mData[0],
      indicator_reading: mData[1],
      tachometer_reading: mData[2],
      measuring_error: mData[3],
      max_allowable_err: mData[4],
      obserbation: mData[5],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForExecutionTable_12_3_6 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      calibration_point: mData[0],
      reference_reading: mData[1],
      indicator_reading: mData[2],
      measuring_error: mData[3],
      obserbation: mData[4],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForExecutionTable_12_5_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      test_criteria: mData[0],
      acceptance_criteria: mData[1],
      obserbation: mData[2],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForExecutionTable_12_6_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      from: mData[0],
      to: mData[1],
      acceptance_criteria: mData[2],
      obserbation: mData[3],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForExecutionTable_12_8_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      pass_details: mData[0],
      settalable_range: mData[1],
      acceptance_criteria: mData[2],
      obserbation: mData[3],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForExecutionTable_12_9_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      event_audit_trail: mData[1],
      acceptance_criteria: mData[2],
      obserbation: mData[3],
    });
  });

  return tempTableData;
};
const filterTableArrayBasedOnTitleForObservationTable_12_10_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      condition: mData[1],
      acceptance_criteria: mData[2],
      actual: mData[3],
      obserbation: mData[4],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForObservationTable_12_11_4 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      plc: mData[1],
      acceptance_criteria: mData[2],
      obserbation: mData[3],
      result: mData[4],
    });
  });

  return tempTableData;
};
const filterTableArrayBasedOnTitleForObservationTable_12_12_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      plc: mData[1],
      condition: mData[2],
      acceptance_criteria: mData[3],
      expected_result: mData[4],
      obserbation: mData[5],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForObservationTable_13_2_4 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      cip_sequence: mData[1],
      obserbation: mData[2],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForObservationTable_13_1_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      pressure_hold_test: mData[1],
      obserbation: mData[2],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForObservationTable_13_5_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      process_sequence: mData[1],
      obserbation: mData[2],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForSUMMARY_NON_CONFORMANCE = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      name_of_test: mData[1],
      ncr_no: mData[2],
      status: mData[3],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForExecutionTable_13_3_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      sip_sequence_name: mData[1],

      obserbation: mData[2],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForExecutionTable_13_4_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      serial_no: mData[0],
      pressure_hold_test: mData[1],

      obserbation: mData[2],
    });
  });

  return tempTableData;
};

const filterTableArrayBasedOnTitleForExecutionTable_13_6_5 = (tData) => {
  const tableData = [...tData]; // [[]]
  const tempTableData = [];
  [...tableData][0]?.forEach((mData, index) => {
    tempTableData?.push({
      requirement: mData[0],

      obserbation: mData[1],
    });
  });

  return tempTableData;
};
