import React from "react";
import { But<PERSON>, Switch } from "@mui/material";

const DeleteAccount = () => {
  return (
    <div className="deleteContainer">
      <div className="title">
        <h3>Delete Account</h3>
      </div>
      <div className="desc">
        <p>
          Once you delete your account, there is no going back. Please be
          certain.
        </p>
      </div>

      <div className="confirmation">
        <div className="leftsidebtn">
          <div className="confirmBtn">
            <Switch />
          </div>
        </div>

        {/* <div className="rightsidebtn">
          <div className="deactiveBtn">
            <Button variant="outlined">Deactivate</Button>
          </div>
          <div className="deleteBtn">
            <Button variant="contained" style={{backgroundColor:'red', color:'white'}}>
              Delete Account
            </Button>
          </div>
        </div> */}
      </div>
    </div>
  );
};

export default DeleteAccount;
