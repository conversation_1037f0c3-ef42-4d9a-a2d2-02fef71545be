import React, { useState } from "react";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import { QRScanner } from "./scanner";
import { IconButton } from "@material-ui/core";
import { useCheckAccess } from "../../utils/useCheckAccess";
export function ScannerModal({
  children,
  onScanComplete = (result) => {},
  qrCodes = [],
}) {
  const [open, setOpen] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const hasChangeOverReportStepPUTAccess = useCheckAccess(
    "changeOverReportSteps",
    "PUT",
  );

  return (
    <>
      <IconButton
        onClick={handleClickOpen}
        disabled={!hasChangeOverReportStepPUTAccess}
      >
        {children}
      </IconButton>
      <Dialog
        open={open}
        // TransitionComponent={Transition}
        keepMounted
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>Scanner</DialogTitle>
        <DialogContent>
          {open && (
            <QRScanner
              qrCodes={qrCodes}
              onScanComplete={(result) => {
                onScanComplete(result);
                handleClose();
              }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
