import React, { useEffect, useRef, useState } from "react";
import QrScanner from "qr-scanner";
import { Box, Button, Stack } from "@mui/material";

export const QRScanner = ({ onScanComplete, qrCodes = [] }) => {
  const [isScanning, setIsScanning] = useState(true);
  const [qrResult, setQrResult] = useState(null);
  const videoRef = useRef(null);
  const scannerRef = useRef(null); // <-- to hold scanner instance

  useEffect(() => {
    if (!videoRef.current) return;

    scannerRef.current = new QrScanner(
      videoRef.current,
      (result) => {
        console.log("QR Code detected:", result);
        setQrResult(result.data);
        setIsScanning(false);
        scannerRef.current?.stop(); // stop scanning on successful read
      },
      { highlightScanRegion: true },
    );

    if (isScanning) {
      scannerRef.current.start().catch((e) => {
        console.error("Scanner start error:", e);
      });
    }

    return () => {
      scannerRef.current?.stop();
      scannerRef.current?.destroy();
    };
  }, []);

  useEffect(() => {
    if (scannerRef.current) {
      if (isScanning) {
        scannerRef.current.start().catch(console.error);
      } else {
        scannerRef.current.stop();
      }
    }
  }, [isScanning]);

  const isValidCode = (code) => {
    if (qrCodes.length > 0 && !qrCodes.includes(code)) {
      console.error("QR Code not in the required list");
      return false;
    }
    return true;
  };

  const handleRescan = () => {
    setQrResult(null);
    setIsScanning(true);
  };

  const handleConfirm = () => {
    if (!isValidCode(qrResult)) return;
    onScanComplete?.(qrResult);
    setQrResult(null);
    setIsScanning(true);
  };

  return (
    <div>
      <video
        ref={videoRef}
        style={{ width: "100%", display: isScanning ? "block" : "none" }}
      />
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          marginTop: 2,
        }}
      >
        {qrResult && (
          <Stack spacing={2}>
            <p>
              <strong>Scanned QR Code:</strong> {qrResult}
            </p>
            {!isValidCode(qrResult) && (
              <p className="text-red-500 my-1">
                Invalid QR Code (Scanned QR Code not in the required list)
              </p>
            )}
            <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleRescan}
              >
                Re Scan
              </Button>
              {isValidCode(qrResult) && (
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={handleConfirm}
                >
                  Confirm
                </Button>
              )}
            </Box>
          </Stack>
        )}
      </Box>
    </div>
  );
};
