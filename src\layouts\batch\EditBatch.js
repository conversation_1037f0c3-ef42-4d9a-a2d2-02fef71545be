import { Input<PERSON>abel, <PERSON><PERSON>ield } from "@mui/material";
import React, { useState } from "react";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../components/buttons/Buttons";
import { batch, companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import { toastMessageSuccess, toastMessageWarning } from "../../tools/toast";

export default function EditBatch({ handleClose, data }) {
  const [name, setName] = useState(data?.product_name);
  const [batchNo, setBatchNo] = useState(data?.batch_no);
  const [count, setCount] = useState(data?.count);

  const handleSubmit = (e) => {
    e.preventDefault();
    const dataSet = {
      product_name: name,
      batch_no: batchNo,
      count,
      lastUpdated: new Date(),
    };
    // db.collection(companies).doc(companyId_constant)
    //     .collection(batch).doc(data.id).update(dataSet).then(() => {
    //         // LoggingFunction(
    //         //     machineName,
    //         //     MaintenanceTitle,
    //         //     userName,
    //         //     "Maintenance",
    //         //     `${MaintenanceTitle} is updated`
    //         // )
    //         toastMessageSuccess({ message: 'updated successfully' })
    //         handleClose()
    //     })
    // .catch((e) => toastMessageWarning({ message: "!" + e }));
  };

  return (
    <>
      <form onSubmit={handleSubmit}>
        <InputLabel style={{ marginBottom: "10px" }}>Product Name</InputLabel>
        <TextField
          onChange={(e) => setName(e.target.value)}
          onBlur={() => setName(name?.trim())}
          value={name}
          required
          variant="outlined"
          fullWidth
          style={{ marginBottom: "12px" }}
        />
        <InputLabel style={{ marginBottom: "10px" }}>Batch No.</InputLabel>
        <TextField
          onChange={(e) => setBatchNo(e.target.value)}
          onBlur={() => setBatchNo(batchNo?.trim())}
          value={batchNo}
          required
          variant="outlined"
          fullWidth
          style={{ marginBottom: "12px" }}
        />
        <InputLabel style={{ marginBottom: "10px" }}>Count</InputLabel>
        <TextField
          onChange={(e) => setCount(e.target.value)}
          onBlur={() => setCount(count?.trim())}
          value={count}
          required
          variant="outlined"
          fullWidth
          style={{ marginBottom: "12px" }}
        />

        <div className="p-2 mt-2 flex justify-between">
          <ButtonBasicCancel
            buttonTitle="Cancel"
            type="button"
            onClick={() => handleClose()}
            variant="outlined"
          />
          <ButtonBasic buttonTitle="Submit" type="submit" variant="outlined" />
        </div>
      </form>
    </>
  );
}
