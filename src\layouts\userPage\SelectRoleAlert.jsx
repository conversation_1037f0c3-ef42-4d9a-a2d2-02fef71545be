import {
  Button,
  Dialog,
  DialogActions,
  DialogTitle,
  DialogContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import PropTypes from "prop-types";
import userRoles from "./UserRole";
import AcceptRejectAlertModal from "./UI_Components/alert-ui";

const SelectRoleAlert = ({
  open = false,
  setOpen = () => {},
  action = () => {},
  selectedUser,
  setSelectedUser,
  isSubmitting,
}) => {
  const [openConfirm, setOpenConfirm] = useState(false);

  if (!selectedUser) return null;

  return (
    <>
      <Dialog
        open={open}
        maxWidth="xs"
        sx={{ transition: "opacity 0.3s ease-in-out" }}
      >
        <DialogTitle sx={{ textAlign: "center", fontWeight: "bold" }}>
          Select User Role
        </DialogTitle>
        <DialogContent
          sx={{
            padding: "2rem",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 2,
          }}
        >
          <Typography variant="body1" color="textSecondary" textAlign="center">
            Assign a role to the user before adding them.
          </Typography>
          <FormControl sx={{ width: "75%" }}>
            <InputLabel htmlFor="role">Role</InputLabel>
            <Select
              id="role"
              value={selectedUser.role}
              label="Role"
              onChange={(e) =>
                setSelectedUser((prev) => ({ ...prev, role: e.target.value }))
              }
              sx={{ textTransform: "capitalize", background: "#fff" }}
            >
              {userRoles.map((role) => (
                <MenuItem
                  key={role.id}
                  value={role.id}
                  sx={{ textTransform: "capitalize" }}
                >
                  {role.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions
          sx={{ justifyContent: "flex-end", gap: 1.5, paddingBottom: 2 }}
        >
          <Button
            onClick={() => setOpen(false)}
            color="error"
            variant="outlined"
            sx={{ width: "20%" }}
          >
            Close
          </Button>
          <Button
            onClick={() => setOpenConfirm(true)}
            variant="contained"
            color="primary"
            disabled={isSubmitting}
            sx={{ width: "20%" }}
          >
            {isSubmitting ? (
              <CircularProgress size={24} color="inherit" />
            ) : (
              "ADD"
            )}
          </Button>
        </DialogActions>
      </Dialog>

      <AcceptRejectAlertModal
        open={openConfirm}
        setOpen={setOpenConfirm}
        handleAccept={action}
        handleReject={() => {
          setOpenConfirm(false);
          setOpen(false);
        }}
        desc="Are you sure you want to add this user?"
        pending={false}
        hideRemark={false}
        hideQnn={true}
      />
    </>
  );
};

SelectRoleAlert.propTypes = {
  open: PropTypes.bool,
  setOpen: PropTypes.func,
  action: PropTypes.func,
  selectedUser: PropTypes.object,
  setSelectedUser: PropTypes.func,
  isSubmitting: PropTypes.bool,
};

export default SelectRoleAlert;
