import axios from "axios";
import React, { useEffect, useState } from "react";

import { useNavigate } from "react-router-dom";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { companies, companyId_constant, machines } from "../../constants/data";
import { useStateContext } from "../../context/ContextProvider";
import { db } from "../../firebase";
import { dbConfig } from "../../infrastructure/db/db-config";
import { firebaseLooper } from "../../tools/tool";
import MachineItem from "../machines/MachineItem";
import { makeStyles } from "@mui/styles";
import { Button, Typography } from "@mui/material";
import { useAuth } from "../../hooks/AuthProvider";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { useMachinesSetter } from "../../services3/machines/MachineContext2";
import ManageSOP from "../machines/ManageSOP";

const useStyles = makeStyles((theme) => ({
  machineSectionHeading: {
    display: "flex",
    justifyContent: "space-between",
    paddingBottom: "2rem",
  },
  machinesSection: {
    backgroundColor: theme.palette.custom.backgroundForth,
    padding: "2rem",
    boxShadow: "5px 5px 7px -4px rgba(0,0,0,0.75)",
    borderRadius: "5px",
  },
  buttonConatiner: {
    alignSelf: "center",
  },
  machinesInnerContainer: {
    display: "flex",
    gap: "1rem",
    justifyContent: "center",
    flexWrap: "wrap",
  },
}));

const MachineSection = () => {
  const { currentUser } = useAuth();
  const [machinesData, setMachines] = useState([]);
  const { currentMode, currentColorLight } = useStateContext();
  const navigate = useNavigate();
  const { refreshCount } = useMongoRefresh();
  const allMachinesSetter = useMachinesSetter();
  const [openSOP, setOpenSOP] = useState(false);
  const [selectedMachine, setSelectedMachine] = useState(null);

  const handleOpenSOP = (machine) => {
    setSelectedMachine(machine);
    setOpenSOP(true);
  };
  const fetchAllMachines = async () => {
    await axios.get(`${dbConfig.url}/machines`).then((response) => {
      console.log(response, "fetched machines");
      setMachines(response.data.data);
    });
  };

  useEffect(() => {
    if (currentUser) {
      fetchAllMachines();
    }
  }, [currentUser, refreshCount, allMachinesSetter]);

  const classes = useStyles();
  return (
    <div className={classes.machinesSection}>
      <div className={classes.machineSectionHeading}>
        <div className="info">
          <Typography variant="h5">Machines</Typography>
          <Typography variant="subtitle2">Some live Machines.</Typography>
        </div>

        <div className={classes.buttonConatiner}>
          {!!machinesData.length && machinesData.length > 0 ? (
            <Button onClick={() => navigate("/machines")} variant="contained">
              View All Machines
            </Button>
          ) : null}
        </div>
      </div>

      <div className="machinesOuterContainer">
        <div className={classes.machinesInnerContainer}>
          {useCheckAccess("machines", "GET") ? (
            machinesData.length > 0 ? (
              machinesData.slice(0, 6).map((machine, index) => (
                <MachineItem
                  machine={machine}
                  key={index}
                  useAt={"dashboard"}
                  onClickSettings={() => handleOpenSOP(machine)} // Pass this prop
                />
              ))
            ) : (
              "NO MACHINES AVAILABLE"
            )
          ) : (
            <NotAccessible />
          )}
        </div>
      </div>
      <ManageSOP
        open={openSOP}
        machine={selectedMachine}
        onClose={() => setOpenSOP(false)}
      />
    </div>
  );
};

export default MachineSection;
