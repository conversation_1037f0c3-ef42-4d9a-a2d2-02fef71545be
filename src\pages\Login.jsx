import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../hooks/AuthProvider";

const Login = () => {
  const { currentUser, login } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Removed auto-redirect logic from here
  }, [currentUser, navigate]);

  const handleLogin = () => {
    const user = { _id: "123", name: "<PERSON>" }; // Replace with actual login logic
    login(user); // Use the login function from AuthProvider
    navigate("/dashboard"); // Redirect to dashboard after login
  };

  return (
    <div>
      <h1>Login</h1>
      <button onClick={handleLogin}>Login</button>
    </div>
  );
};

export default Login;
