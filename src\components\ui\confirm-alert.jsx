import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from "@mui/material";
import React from "react";
import { Box } from "@mui/system";
import PropTypes from "prop-types";

const YES_BUTTON_LABEL = "Yes";
const NO_BUTTON_LABEL = "No";
const DIALOG_WIDTH = "24rem";

const ConfirmAlert = ({
  open = false,
  handleYesClick = () => {},
  handleNoClick = () => {},
  desc = "field",
}) => {
  return (
    <Dialog open={open}>
      <Box sx={{ px: 4, pt: 1, pb: 2.5, width: DIALOG_WIDTH }}>
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            position: "relative",
            fontWeight: "bold",
          }}
        >
          <Typography variant="h6">{desc}</Typography>
        </DialogTitle>
        <DialogContent></DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              handleYesClick();
            }}
            color="success"
            variant="contained"
          >
            {YES_BUTTON_LABEL}
          </Button>
          <Button
            onClick={() => {
              handleNoClick();
            }}
            color="warning"
            variant="contained"
          >
            {NO_BUTTON_LABEL}
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};
ConfirmAlert.propTypes = {
  open: PropTypes.bool, // Controls whether the dialog is open
  handleYesClick: PropTypes.func, // Function for "Yes" button click
  handleNoClick: PropTypes.func, // Function for "No" button click
  desc: PropTypes.string, // Description text displayed in the dialog
};
export default ConfirmAlert;
