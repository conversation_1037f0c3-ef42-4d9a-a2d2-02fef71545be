import React, { useState } from "react";
import { TextField, Button, Grid, Typography, Box } from "@mui/material";
import axios from "axios";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelIcon from "@mui/icons-material/Cancel";
import { dbUrl } from "../../constants/db";

const TestForm = () => {
  const [ldapUsername, setLdapUsername] = useState("");
  const [ldapPassword, setLdapPassword] = useState("");
  const [recipientEmail, setRecipientEmail] = useState("");
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async () => {
    setLoading(true);
    setError("");
    setResults(null);

    try {
      const response = await axios.post(`${dbUrl}/envdata/run_tests`, {
        ldapUsername,
        ldapPassword,
        recipientEmail,
      });

      setResults(response.data);
    } catch (err) {
      setError("An error occurred while running the tests.");
    } finally {
      setLoading(false);
    }
  };

  const renderResultIcon = (passed) => {
    return passed ? (
      <CheckCircleIcon style={{ color: "green" }} />
    ) : (
      <CancelIcon style={{ color: "red" }} />
    );
  };

  return (
    <Box sx={{ mt: 4 }}>
      <Typography variant="h5" gutterBottom>
        Run Connection Tests
      </Typography>
      <Grid container spacing={3} justifyContent="center">
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="LDAP Username"
            value={ldapUsername}
            onChange={(e) => setLdapUsername(e.target.value)}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            type="password"
            label="LDAP Password"
            value={ldapPassword}
            onChange={(e) => setLdapPassword(e.target.value)}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Recipient Email"
            value={recipientEmail}
            onChange={(e) => setRecipientEmail(e.target.value)}
          />
        </Grid>
        <Grid item xs={12}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? "Running Tests..." : "Run Tests"}
          </Button>
        </Grid>
      </Grid>

      {error && (
        <Typography variant="body1" color="error" align="center" sx={{ mt: 3 }}>
          {error}
        </Typography>
      )}

      {results && (
        <Box sx={{ mt: 4 }}>
          <Typography variant="h5" gutterBottom>
            Test Results
          </Typography>
          <Grid container spacing={2}>
            {Object.entries(results).map(([test, result]) => (
              <Grid item xs={12} key={test}>
                <Typography variant="body1">
                  {test.charAt(0).toUpperCase() + test.slice(1)}:{" "}
                  {renderResultIcon(result.status)}
                  {!result.status && (
                    <span style={{ color: "red", marginLeft: "10px" }}>
                      {result.error}
                    </span>
                  )}
                </Typography>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default TestForm;
