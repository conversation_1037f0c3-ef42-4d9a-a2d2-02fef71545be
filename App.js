import React from "react";
import { MongoRefreshProvider } from "./src/services/mongo/refresh/context";
// ...existing imports...

function App() {
  console.log("App component is rendering.");

  return (
    <MongoRefreshProvider>
      {console.log("MongoRefreshProvider is wrapping the component tree.")}
      {/* Ensure all components using useMongoRefresh are wrapped */}
      {/* ...existing component tree... */}
    </MongoRefreshProvider>
  );
}

export default App;
