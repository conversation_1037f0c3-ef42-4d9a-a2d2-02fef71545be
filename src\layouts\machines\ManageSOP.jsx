import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  IconButton,
  Alert,
  DialogContentText,
  Snackbar,
  Typography,
} from "@mui/material";
import { keyframes } from "@mui/system";
import SOPFolderPicker from "./SOPFolderPicker";
import SOPCategoryTable from "./SOPCategoryTable";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import * as XLSX from "xlsx";
import { dbConfig } from "../../infrastructure/db/db-config";
import axios from "axios";

const isValidObjectId = (id) => /^[0-9a-fA-F]{24}$/.test(id);

const validateIds = (rows, sheetName, idFields) => {
  const errors = [];
  rows.forEach((row, index) => {
    idFields.forEach((field) => {
      const id = row[field];
      if (id && !isValidObjectId(id)) {
        errors.push(
          `Invalid ${field} in ${sheetName} at row ${index + 2}: "${id}"`,
        );
      }
    });
  });
  return errors;
};

const findEmptyRows = (rows, sheetName) => {
  const emptyRows = [];
  for (let i = 0; i < rows.length; i++) {
    const isEmpty = Object.values(rows[i]).every(
      (val) => val === null || val === undefined || String(val).trim() === "",
    );
    // console.log(`Row ${i + 2} in ${sheetName}: isEmpty = ${isEmpty}, values =`, Object.values(rows[i]));
    if (isEmpty) {
      const hasNonEmptyAfter = rows
        .slice(i + 1)
        .some((row) =>
          Object.values(row).some(
            (val) =>
              val !== null && val !== undefined && String(val).trim() !== "",
          ),
        );
      if (hasNonEmptyAfter) {
        emptyRows.push(
          `Empty row in "${sheetName}" sheet at Excel row ${i + 2} (between data rows)`,
        );
      }
    }
  }
  return emptyRows;
};

const checkDuplicateIds = (rows, sheetName, idFields) => {
  const errors = [];
  const idSet = new Set();
  const duplicates = new Set();

  rows.forEach((row, index) => {
    idFields.forEach((field) => {
      const id = row[field];
      if (id) {
        if (idSet.has(id)) {
          duplicates.add(id);
        } else {
          idSet.add(id);
        }
      }
    });
  });

  if (duplicates.size > 0) {
    duplicates.forEach((dupId) => {
      errors.push(
        `Duplicate ${idFields.join(" or ")} found in ${sheetName}: "${dupId}"`,
      );
    });
  }
  return errors;
};

const pulseAnimation = keyframes`
  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
  70% { transform: scale(1.05); box-shadow: 0 0 10px 15px rgba(0, 123, 255, 0); }
  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
`;

const ALLOW_MISSING_ASSETS = true;

const getAllAssetPaths = (stepsByCategory) => {
  const paths = new Set();
  Object.values(stepsByCategory).forEach((manuals) => {
    manuals.forEach((manual) => {
      (manual.steps || []).forEach((step) => {
        const stepUrl = step.url ? step.url.replace(/\\/g, "/") : "";
        if (stepUrl.toLowerCase().startsWith("assets/")) {
          paths.add(stepUrl);
        }
        (step.subSteps || []).forEach((sub) => {
          const subUrl = sub.url ? sub.url.replace(/\\/g, "/") : "";
          if (subUrl.toLowerCase().startsWith("assets/")) {
            paths.add(subUrl);
          }
        });
      });
    });
  });
  return Array.from(paths);
};

const ManageSOP = ({ open, machine, onClose }) => {
  const [folder, setFolder] = useState(null);
  const [folderFiles, setFolderFiles] = useState([]);
  const [categories, setCategories] = useState([]);
  const [stepsByCategory, setStepsByCategory] = useState({});
  const [expandedCat, setExpandedCat] = useState(null);
  const [expandedStep, setExpandedStep] = useState({});
  const [catPage, setCatPage] = useState(1);
  const [stepPage, setStepPage] = useState({});
  const [subStepPage, setSubStepPage] = useState({});
  const [submitDialog, setSubmitDialog] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [submissionError, setSubmissionError] = useState("");
  const [frontendValidationError, setFrontendValidationError] = useState(null);
  const [toastInfo, setToastInfo] = useState({
    open: false,
    message: "",
    severity: "info",
  });
  const [localObjectUrlMap, setLocalObjectUrlMap] = useState({});
  const [infoModalOpen, setInfoModalOpen] = useState(false);
  const [highlightInfoBtn, setHighlightInfoBtn] = useState(false);

  const handleFolderChange = async (e) => {
    const files = Array.from(e.target.files);
    setFolder(e.target.webkitdirectory ? e.target : null);
    setFolderFiles(files);

    const categoriesFile = files.find(
      (f) => f.name.toLowerCase() === "categories.xlsx",
    );
    if (!categoriesFile) {
      setFrontendValidationError(
        "categories.xlsx not found in selected folder.",
      );
      setCategories([]);
      setStepsByCategory({});
      return;
    }

    try {
      const data = await categoriesFile.arrayBuffer();
      const workbook = XLSX.read(data, { type: "array" });

      const categoriesRows = XLSX.utils.sheet_to_json(
        workbook.Sheets["Categories"] || {},
        { defval: "" },
      );
      const manualsRows = XLSX.utils.sheet_to_json(
        workbook.Sheets["Manuals"] || {},
        { defval: "" },
      );
      const stepsRows = XLSX.utils.sheet_to_json(
        workbook.Sheets["Steps"] || {},
        { defval: "" },
      );

      // Validate IDs
      const idValidationErrors = [
        ...validateIds(manualsRows, "Manuals", ["_id"]),
        ...validateIds(stepsRows, "Steps", ["_id", "manual_id"]),
      ];

      // For sub-steps, validate manual_id (used as step_id)
      const subStepIdErrors = stepsRows
        .filter((row) => row["step-type"] === "sub-step")
        .filter((row) => row["manual_id"] && !isValidObjectId(row["manual_id"]))
        .map(
          (row) =>
            `Invalid manual_id (used as step_id) in Steps (sub-step) at row ${row.__rowNum__}: "${row["manual_id"]}"`,
        );

      idValidationErrors.push(...subStepIdErrors);

      const duplicateIdErrors = [
        ...checkDuplicateIds(categoriesRows, "Categories", ["_id"]),
        ...checkDuplicateIds(manualsRows, "Manuals", ["_id"]),
        ...checkDuplicateIds(stepsRows, "Steps", ["_id"]),
      ];

      const emptyRowErrors = [
        ...findEmptyRows(categoriesRows, "Categories"),
        ...findEmptyRows(manualsRows, "Manuals"),
        ...findEmptyRows(stepsRows, "Steps"),
      ];

      console.log("Empty row errors:", emptyRowErrors);

      // Validate that manual_id (step_id) in sub-steps references a step's _id
      const stepIds = new Set(
        stepsRows
          .filter((row) => row["step-type"] === "step")
          .map((row) => row["_id"]),
      );
      const invalidStepIdErrors = stepsRows
        .filter((row) => row["step-type"] === "sub-step" && row["manual_id"])
        .filter((row) => !stepIds.has(row["manual_id"]))
        .map(
          (row) =>
            `Invalid manual_id (used as step_id) in Steps (sub-step) at row ${row.__rowNum__}: "${row["manual_id"]}" (must reference a step's _id)`,
        );

      const allValidationErrors = [
        ...idValidationErrors,
        ...duplicateIdErrors,
        ...invalidStepIdErrors,
        ...emptyRowErrors,
      ];

      if (allValidationErrors.length > 0) {
        console.log("Validation errors:", allValidationErrors);
        setFrontendValidationError(
          `Validation errors:\n${allValidationErrors.join("\n")}`,
        );
        setCategories([]);
        setStepsByCategory({});
        return;
      }

      const newCategories = categoriesRows.map((catRow) => ({
        name: catRow["Category Name"] || "",
        description: catRow["Description"] || "",
      }));

      const manualsByCategory = {};
      manualsRows.forEach((manual) => {
        const cat = (manual.category || "").trim().toLowerCase();
        if (!cat) return;
        if (!manualsByCategory[cat]) manualsByCategory[cat] = [];
        manualsByCategory[cat].push(manual);
      });

      // Separate steps and sub-steps based on step-type
      const stepsByManual = {};
      const subStepsByStep = {};
      stepsRows.forEach((row) => {
        if (row["step-type"] === "step") {
          const manualId = row.manual_id;
          if (!manualId) return;
          if (!stepsByManual[manualId]) stepsByManual[manualId] = [];
          stepsByManual[manualId].push(row);
        } else if (row["step-type"] === "sub-step") {
          // Use manual_id as step_id for sub-steps
          const stepId = row.manual_id;
          if (!stepId) return;
          if (!subStepsByStep[stepId]) subStepsByStep[stepId] = [];
          subStepsByStep[stepId].push(row);
        }
      });

      const newStepsByCategory = {};
      for (const cat of newCategories) {
        const catName = (cat.name || "").trim().toLowerCase();
        const manuals = manualsByCategory[catName] || [];
        newStepsByCategory[cat.name] = manuals.map((manual) => {
          const manualId = manual._id;
          const steps = (stepsByManual[manualId] || []).map((step) => {
            const stepId = step._id;
            const hasSubSteps =
              step.sub_steps === true ||
              step.sub_steps === "TRUE" ||
              step.sub_steps === "true";
            let subSteps = [];
            if (hasSubSteps) {
              subSteps = (subStepsByStep[stepId] || []).map((sub, idx) => ({
                ...sub,
                index: typeof sub.index === "number" ? sub.index : idx,
                url: (sub.url || "").trim(),
                createdAt: sub.createdAt || new Date().toISOString(),
                lastUpdated: sub.lastUpdated || new Date().toISOString(),
              }));
            }
            return {
              ...step,
              subSteps,
              createdAt: step.createdAt || new Date().toISOString(),
              lastUpdated: step.lastUpdated || new Date().toISOString(),
            };
          });
          return {
            ...manual,
            steps,
          };
        });
      }

      setCategories(newCategories);
      setStepsByCategory(newStepsByCategory);
      setExpandedCat(null);
      setExpandedStep({});
      setCatPage(1);
      setStepPage({});
      setSubStepPage({});
      setSubmissionError("");
      Object.values(localObjectUrlMap).forEach((url) => {
        if (url.startsWith("blob:")) URL.revokeObjectURL(url);
      });
      setLocalObjectUrlMap({});
      setFrontendValidationError(null);
    } catch (err) {
      setFrontendValidationError(
        "Failed to parse categories.xlsx: " + err.message,
      );
      setCategories([]);
      setStepsByCategory({});
    }
  };

  useEffect(() => {
    const newObjectUrlMap = { ...localObjectUrlMap };
    let urlsCreatedInThisRun = {};

    if (
      folderFiles.length > 0 &&
      stepsByCategory &&
      Object.keys(stepsByCategory).length > 0
    ) {
      const assetPaths = getAllAssetPaths(stepsByCategory);
      assetPaths.forEach((assetPath) => {
        if (!newObjectUrlMap[assetPath]) {
          const file = folderFiles.find(
            (f) =>
              f.webkitRelativePath &&
              f.webkitRelativePath.replace(/\\/g, "/").endsWith(assetPath),
          );
          if (
            file &&
            (file.type.startsWith("image/") || file.type.startsWith("video/"))
          ) {
            const objectUrl = URL.createObjectURL(file);
            newObjectUrlMap[assetPath] = objectUrl;
            urlsCreatedInThisRun[assetPath] = objectUrl;
          }
        }
      });
    }
    setLocalObjectUrlMap(newObjectUrlMap);

    return () => {
      Object.values(urlsCreatedInThisRun).forEach((url) => {
        if (url.startsWith("blob:")) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [stepsByCategory, folderFiles]);

  useEffect(() => {
    if (open) {
      setHighlightInfoBtn(true);
      const timer = setTimeout(() => {
        setHighlightInfoBtn(false);
      }, 5000);
      return () => clearTimeout(timer);
    } else {
      handleReset();
      setHighlightInfoBtn(false);
    }
  }, [open]);

  const CATS_PER_PAGE = 5;
  const STEPS_PER_PAGE = 5;

  const handleReset = () => {
    setFolder(null);
    setFolderFiles([]);
    setCategories([]);
    Object.values(localObjectUrlMap).forEach((url) => {
      if (url.startsWith("blob:")) URL.revokeObjectURL(url);
    });
    setLocalObjectUrlMap({});
    setStepsByCategory({});
    setExpandedCat(null);
    setExpandedStep({});
    setCatPage(1);
    setStepPage({});
    setSubStepPage({});
    setSubmissionError("");
    setFrontendValidationError(null);
  };

  const handleSubmit = async () => {
    setSubmitDialog(true);
  };

  const handleConfirmSubmit = async () => {
    setSubmitDialog(false);
    setUploading(true);
    setSubmissionError("");
    setToastInfo({ open: false, message: "", severity: "info" });

    try {
      const assetPaths = getAllAssetPaths(stepsByCategory);
      const uploadedMap = {};
      for (const f of folderFiles) {
        let relPath = f.webkitRelativePath
          ? f.webkitRelativePath.replace(/\\/g, "/")
          : "";
        const assetsIndex = relPath.indexOf("assets/");
        if (assetsIndex !== -1) {
          relPath = relPath.substring(assetsIndex);
        }
        if (relPath.startsWith("assets/")) {
          const fd = new FormData();
          fd.append("image", f);
          let uploadUrl = "";
          try {
            const resTemp = await axios.post(
              `${dbConfig?.url_storage}/upload`,
              fd,
            );
            if (resTemp?.data?.data) {
              uploadUrl = resTemp.data.data;
            } else {
              throw new Error("Failed to upload file to storage server.");
            }
          } catch (err) {
            setUploading(false);
            setSubmissionError(
              "Failed to upload file: " + (err?.message || "Unknown error"),
            );
            setToastInfo({
              open: true,
              message: "Failed to upload file",
              severity: "error",
            });
            return;
          }
          uploadedMap[relPath] = uploadUrl;
        }
      }

      const updatedStepsByCategory = {};
      Object.entries(stepsByCategory).forEach(([cat, manuals]) => {
        updatedStepsByCategory[cat] = manuals.map((manual) => ({
          ...manual,
          steps: (manual.steps || []).map((step) => {
            const normalizeAssetPath = (url) => {
              if (!url) return "";
              let norm = url.replace(/\\/g, "/");
              const idx = norm.indexOf("assets/");
              if (idx !== -1) norm = norm.substring(idx);
              return norm;
            };

            let normalizedStepUrl = normalizeAssetPath(step.url);
            let replacedStepUrl = normalizedStepUrl;
            if (normalizedStepUrl.toLowerCase().startsWith("assets/")) {
              replacedStepUrl = uploadedMap.hasOwnProperty(normalizedStepUrl)
                ? uploadedMap[normalizedStepUrl]
                : ALLOW_MISSING_ASSETS
                  ? "NA"
                  : normalizedStepUrl;
            }
            return {
              ...step,
              url: replacedStepUrl,
              sub_steps: (step.subSteps || []).map((sub) => {
                const normalizedUrl = normalizeAssetPath(sub.url);
                const replacedUrl = uploadedMap.hasOwnProperty(normalizedUrl)
                  ? uploadedMap[normalizedUrl]
                  : ALLOW_MISSING_ASSETS
                    ? "NA"
                    : normalizedUrl;
                return {
                  ...sub,
                  url: replacedUrl,
                };
              }),
            };
          }),
        }));
      });

      if (!machine?._id) {
        throw new Error("Machine ID is missing. Cannot submit SOPs.");
      }

      await axios.put(
        `${dbConfig.url}/machines/${machine._id}/all-sops`,
        updatedStepsByCategory,
      );

      setToastInfo({
        open: true,
        message: "SOPs and assets uploaded successfully!",
        severity: "success",
      });
      setTimeout(() => {
        handleReset();
        onClose();
      }, 2000);

      setUploading(false);
    } catch (err) {
      setUploading(false);
      const displayMessage =
        err?.response?.data?.message ||
        err.message ||
        "An unexpected error occurred during submission.";
      setToastInfo({ open: true, message: displayMessage, severity: "error" });
      setSubmissionError(displayMessage);
    }
  };

  const handleExpandCat = (catName) => {
    setExpandedCat(expandedCat === catName ? null : catName);
    setExpandedStep({});
    setStepPage((prev) => ({ ...prev, [catName]: 1 }));
  };

  const handleExpandStep = (catName, idx) => {
    setExpandedStep((prev) => ({
      ...prev,
      [catName]: prev[catName] === idx ? null : idx,
    }));
    setSubStepPage((prev) => ({
      ...prev,
      [`${catName}_${idx}`]: 1,
    }));
  };

  const handleStepPageChange = (catName, value) => {
    setStepPage((prev) => ({ ...prev, [catName]: value }));
    setExpandedStep({});
  };

  const handleSubStepPageChange = (catName, idx, value) => {
    setSubStepPage((prev) => ({
      ...prev,
      [`${catName}_${idx}`]: value,
    }));
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="xl"
      PaperProps={{ style: { minHeight: "90vh" } }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        Manage SOP Data for Machine: {machine?.title}
        <IconButton
          aria-label="info"
          onClick={() => {
            setInfoModalOpen(true);
            setHighlightInfoBtn(false);
          }}
          sx={{
            animation: highlightInfoBtn
              ? `${pulseAnimation} 2s infinite`
              : "none",
            borderRadius: "50%",
            padding: "8px",
          }}
        >
          <InfoOutlinedIcon />
        </IconButton>
      </DialogTitle>

      <Dialog
        open={infoModalOpen}
        onClose={() => setInfoModalOpen(false)}
        maxWidth="md"
      >
        <DialogTitle>About Managing SOP Data</DialogTitle>
        <DialogContent>
          <DialogContentText component="div">
            <Typography variant="h6" gutterBottom>
              Purpose:
            </Typography>
            <Typography paragraph>
              This tool allows you to import and manage Standard Operating
              Procedures (SOPs) for the selected machine. SOP data is defined in
              an Excel spreadsheet, with associated media files organized in a
              local folder structure.
            </Typography>
            <Typography variant="h6" gutterBottom>
              How to Use:
            </Typography>
            <ol>
              <li>
                <Typography component="span" paragraph>
                  <strong>Select Folder:</strong> Choose the main folder
                  containing <b>categories.xlsx</b> and an "assets" subfolder.
                </Typography>
              </li>
              <li>
                <Typography component="span" paragraph>
                  <strong>Data Loading & Preview:</strong> The system parses the
                  Excel file with sheets for Categories, Manuals, and Steps.
                  Steps sheet contains both steps and sub-steps, differentiated
                  by the 'step-type' column. For sub-steps, the 'manual_id'
                  column is used as 'step_id' to link to the parent step.
                </Typography>
              </li>
              <li>
                <Typography component="span" paragraph>
                  <strong>Review & Validate:</strong> Ensure all asset paths in
                  the Steps sheet exist in the "assets" subfolder.
                </Typography>
              </li>
              <li>
                <Typography component="span" paragraph>
                  <strong>Submit Data:</strong> Click "Submit" to upload assets
                  and save SOP data.
                </Typography>
              </li>
            </ol>
            <Typography variant="h6" gutterBottom>
              Data Structure:
            </Typography>
            <Typography paragraph>
              SOPs are organized hierarchically:
              <ul>
                <li>
                  <strong>Categories:</strong> Broad classifications.
                </li>
                <li>
                  <strong>Manuals:</strong> Procedures within a category.
                </li>
                <li>
                  <strong>Steps:</strong> Actions within a manual, with optional
                  sub-steps if sub_steps is true.
                </li>
              </ul>
            </Typography>
            <Typography variant="h6" gutterBottom>
              Important Notes:
            </Typography>
            <ul>
              <li>
                <Typography component="span">
                  Ensure Excel data follows the expected structure.
                </Typography>
              </li>
              <li>
                <Typography component="span">
                  For sub-steps, the 'manual_id' column in the Steps sheet must
                  contain the parent step's _id.
                </Typography>
              </li>
              <li>
                <Typography component="span">
                  All media files must exist in the "assets" subfolder.
                </Typography>
              </li>
              <li>
                <Typography component="span">
                  Submission is irreversible.
                </Typography>
              </li>
            </ul>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setInfoModalOpen(false)} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <DialogContent>
        <SOPFolderPicker
          folderFiles={folderFiles}
          uploading={uploading}
          onChange={handleFolderChange}
          onReset={handleReset}
        />
        {folderFiles.length > 0 && (
          <>
            {frontendValidationError && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                {frontendValidationError}
              </Alert>
            )}
            {submissionError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {submissionError}
              </Alert>
            )}
            <SOPCategoryTable
              categories={categories}
              stepsByCategory={stepsByCategory}
              localObjectUrlMap={localObjectUrlMap}
              catPage={catPage}
              setCatPage={setCatPage}
              expandedCat={expandedCat}
              onExpandCat={handleExpandCat}
              stepPage={stepPage}
              onStepPageChange={handleStepPageChange}
              expandedStep={expandedStep}
              onExpandStep={handleExpandStep}
              subStepPage={subStepPage}
              onSubStepPageChange={handleSubStepPageChange}
              CATS_PER_PAGE={CATS_PER_PAGE}
              STEPS_PER_PAGE={STEPS_PER_PAGE}
            />
          </>
        )}
      </DialogContent>
      <DialogActions
        sx={{
          flexDirection: "column",
          alignItems: "stretch",
          gap: 1,
          px: 3,
          pb: 2,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          <Button
            variant="contained"
            color="secondary"
            onClick={onClose}
            disabled={uploading}
          >
            Close
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            disabled={
              uploading ||
              !folderFiles.length ||
              !!frontendValidationError ||
              Object.keys(stepsByCategory).length === 0
            }
          >
            {uploading ? "Uploading..." : "Submit"}
          </Button>
        </Box>
      </DialogActions>
      <Dialog open={submitDialog} onClose={() => setSubmitDialog(false)}>
        <DialogTitle>Confirm Submission</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to submit? This action is <b>irreversible</b>.
            Asset files will be uploaded.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSubmitDialog(false)} color="secondary">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmSubmit}
            color="primary"
            variant="contained"
            data-testid="confirm-upload-button"
          >
            {uploading ? "Uploading Assets..." : "Confirm & Upload"}
          </Button>
        </DialogActions>
      </Dialog>
      <Snackbar
        open={toastInfo.open}
        autoHideDuration={6000}
        onClose={() => setToastInfo({ ...toastInfo, open: false })}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert
          onClose={() => setToastInfo({ ...toastInfo, open: false })}
          severity={toastInfo.severity}
          sx={{ width: "100%" }}
          variant="filled"
        >
          {toastInfo.message}
        </Alert>
      </Snackbar>
    </Dialog>
  );
};

export default ManageSOP;
