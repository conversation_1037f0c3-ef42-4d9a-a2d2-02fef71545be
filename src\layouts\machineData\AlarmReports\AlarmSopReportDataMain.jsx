import {useEffect, useMemo, useState} from "react";
import "./../machineData.scss";
import {
  Table,
  TableBody,
  TableContainer,
  Paper,
  TablePagination, // Import TablePagination
} from "@mui/material";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import {useStateContext} from "../../../context/ContextProvider";
import axios from "axios";
import {dbConfig} from "../../../infrastructure/db/db-config";
import {makeStyles} from "@mui/styles";
import MachineDropdown from "../../Reports/MachineDropdown";
import TableHeader from "../TableHeader";
import NoDataComponent from "../../../components/commons/noData.component";
import useDateFilter from "../useDateFilter";
import BasicMenu from "../../../components/menus/BasicMenu";
import {DateRangePicker} from "react-date-range";
import moment from "moment";
import {format} from "date-fns";
import {useAuth} from "../../../hooks/AuthProvider";
import CommonDropDownComponent from "../../../components/commons/dropDown.component";
import DateRangeMenu from "../../../components/menus/DateRangeMenu";
import {useCommonOuterContainerStyle} from "../../../styles/useCommonOuterContainerStyle";
import NotAccessible from "../../../components/not-accessible/not-accessible";
import {useCheckAccess} from "../../../utils/useCheckAccess";
import AlarmSopReportDataItem from "./AlarmSopReportDataItem";

const useCustomStyles = makeStyles(theme => ({
  fatInnerContent: {
    padding: "0.5rem !important",
    backgroundColor: `${theme.palette.custom.backgroundForth} !important`,
  },
}));

function AlarmSopReportDataMain({machineData, machineId, handleChangeId}) {
  const {currentUser} = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [alarmSopReportDataAll, setAlarmSopReportDataAll] = useState([]);
  const {currentMode} = useStateContext();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState([]);
  const [stepData, setSD] = useState([]);
  const [reset, setReset] = useState(0);

  // State for user filter
  const [selectedUser, setSelectedUser] = useState("All");

  // Pagination states
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10); // Fixed at 10 reports per page

  const {date, shadowOnDate, colorOnDate, dateFilter, resetDateFilter} =
    useDateFilter();

  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  const hasAlarmSopReportGETAccess = useCheckAccess("alarmReport", "GET");;

  // Format the date range for display
  const formattedDateRange = useMemo(() => {
    const start = date[0]?.startDate
      ? format(new Date(date[0].startDate), "dd MMM yyyy")
      : "N/A";
    const end = date[0]?.endDate
      ? format(new Date(date[0].endDate), "dd MMM yyyy")
      : "N/A";
    return `Date Range: ${start} - ${end}`;
  }, [date]);

  // Derive user options from currentUser list, filtered by report creators
  const userOptions = useMemo(() => {
    const reportEmails = new Set(alarmSopReportDataAll.map(data => data.email));
    const filteredcurrentUser = reportEmails.has(currentUser.email)
      ? [currentUser]
      : [];
    return [
      {label: "All", value: "All"},
      ...filteredcurrentUser
        .map(user => ({
          label: user.email.split("@")[0],
          value: user.email,
        }))
        .sort((a, b) => a.label.localeCompare(b.label)),
    ];
  }, [currentUser, alarmSopReportDataAll]);

  useEffect(() => {
    const getAllAlarmSopData = async () => {
      if (!loading) setLoading(true);
      try {
        await getAllAlarmSopReports();
      } finally {
        setLoading(false);
      }
    };

    getAllAlarmSopData();
  }, []);

  const getAllAlarmSopReports = async () => {
    await axios
      .get(`${dbConfig.url}/alarmReport`)
      .then(response => {
        setAlarmSopReportDataAll(response?.data?.data);
        console.log(
          "alarm SOP report data from mongo : data:",
          response?.data?.data,
        );
      })
      .catch(e => {
        console.log("error alarm SOP report data from mongo : data:", e);
      });
  };

  const customCss = useCustomStyles();

  const filterAlarmSopReportData = data => {
    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    const lowerCaseEmail = data?.email?.split("@")[0].toLowerCase();

    const matchesSearchTerm =
      searchTerm === "" ||
      data?.title.toLowerCase().includes(lowerCaseSearchTerm) ||
      lowerCaseEmail.includes(lowerCaseSearchTerm) ||
      data?.comment.toLowerCase().includes(lowerCaseSearchTerm);

    const matchesMachineId = machineId === "All" || data?.mid === machineId;

    const startOfDay = moment(date[0]?.startDate).startOf("day");
    const endOfDay = moment(date[0]?.endDate).endOf("day");
    const isWithinDateRange = moment(data?.date).isBetween(
      startOfDay,
      endOfDay,
      undefined,
      "[]",
    );

    // User filter
    const matchesUser = selectedUser === "All" || data.email === selectedUser;

    return (
      matchesSearchTerm && matchesMachineId && isWithinDateRange && matchesUser
    );
  };

  // Compute paginated reports
  const paginatedReports = useMemo(() => {
    const filtered = alarmSopReportDataAll.filter(filterAlarmSopReportData);
    const startIndex = page * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    return filtered.slice(startIndex, endIndex);
  }, [
    alarmSopReportDataAll,
    page,
    rowsPerPage,
    searchTerm,
    machineId,
    date,
    selectedUser,
  ]);

  // Compute filtered report count
  const filteredReportCount = useMemo(
    () => alarmSopReportDataAll.filter(filterAlarmSopReportData).length,
    [alarmSopReportDataAll, searchTerm, machineId, date, selectedUser],
  );

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change (optional, included for completeness)
  const handleChangeRowsPerPage = event => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0); // Reset to first page
  };

  return (
    <main className="allMachineDataPreviewContainer">
      <div
        className={`${customCss.fatInnerContent} liveDataOuterContainer border-radius-outer`}>
        <div className="liveDataHeading">
          <div
            className="title"
            style={{
              color: currentMode === "Dark" ? "#fff" : "#000",
            }}>
            Alarm SOP Reports
          </div>
          <div style={{display: "flex", alignItems: "center", gap: "1rem"}}>
            {/* Interactive Date Range Picker */}
            <DateRangeMenu
              textShadow={shadowOnDate}
              color={colorOnDate}
              fontSize="0.9rem"
              displayText={formattedDateRange}
              items={
                <DateRangePicker
                  onChange={item => dateFilter(item)}
                  showSelectionPreview={true}
                  moveRangeOnFirstSelection={false}
                  months={1}
                  ranges={date}
                  direction="horizontal"
                  maxDate={new Date()}
                />
              }
              resetDateFilter={resetDateFilter}
            />
            {/* User Dropdown */}
            <CommonDropDownComponent
              dropDownLabel={"Select User"}
              menuValueDefault={selectedUser}
              menuValue={selectedUser}
              menuItemValue={"value"}
              menuData={userOptions}
              menuItemDisplay={"label"}
              handleChange={e => setSelectedUser(e.target.value)}
              dropDownContainerStyle={{minWidth: 200}}
            />
            <MachineDropdown
              machineId={machineId}
              machineData={machineData}
              handleChangeId={handleChangeId}
            />
          </div>
        </div>

        <div className="liveDataContainer">
          {hasAlarmSopReportGETAccess ? (
            <>
              <TableContainer
                component={Paper}
                className="table border-radius-inner"
                sx={commonOuterContainerStyle}>
                <Table
                  style={{
                    minWidth: 650,
                    width: "100%",
                    backgroundColor:
                      currentMode === "Dark" ? "#161C24" : "#fff",
                  }}>
                  <TableHeader
                    currentMode={currentMode}
                    columns={[
                      {label: "Title", align: "left", width: "30%"},
                      {
                        label: `Performed on`,
                        align: "left",
                        width: "15%",
                        filterComponent: (
                          <BasicMenu
                            textShadow={shadowOnDate}
                            color={colorOnDate}
                            fontSize="0.75rem"
                            items={
                              <DateRangePicker
                                onChange={item => dateFilter(item)}
                                showSelectionPreview={true}
                                moveRangeOnFirstSelection={false}
                                months={1}
                                ranges={date}
                                direction="horizontal"
                              />
                            }
                          />
                        ),
                      },
                      {label: "Done By", align: "left", width: "15%"},
                      {label: "Remarks", align: "left", width: "10%"},
                      {label: "Status", align: "center", width: "15%"},
                      {label: "Actions", align: "center"},
                    ]}
                  />

                  <TableBody>
                    {loading ? (
                      <NoDataComponent
                        cellColSpan={7}
                        cellRowSpan={4}
                        dataLoading={loading}
                      />
                    ) : filteredReportCount > 0 ? (
                      paginatedReports.map((data, index) => (
                        <AlarmSopReportDataItem
                          key={data._id + index}
                          data={data}
                          userName={`${user?.fname} ${user?.lname}`}
                          stepData={stepData
                            .filter(d => d.manual_id === data._id)
                            .sort((a, b) => a.index - b.index)}
                          machineData={machineData}
                          reset={reset}
                          setReset={setReset}
                          setAlarmSopReportDataAll={setAlarmSopReportDataAll} // Make sure this is passed
                        />
                      ))
                    ) : (
                      <NoDataComponent
                        cellColSpan={7}
                        cellRowSpan={4}
                        dataLoading={loading}
                      />
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination Component */}
              <TablePagination
                // rowsPerPageOptions={[10]} // Only allow 10 rows per page
                component="div"
                count={filteredReportCount}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="Reports per page:"
              />
            </>
          ) : (
            <NotAccessible />
          )}
        </div>
      </div>
    </main>
  );
}

export default AlarmSopReportDataMain;
