import {
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import React, { useState } from "react";
import { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  companies,
  companyId_constant,
  fatReport,
  machines,
} from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import ContentTableItem from "../machineData/ContentTableItem";
import "../machineData/contentPage.scss";
import DataTableItem from "./DataTableItem";
import ApprovalTableItem from "../machineData/ApprovalTable/ApprovalTable";
import ReportItem from "./ReportItem";
import { useStateContext } from "../../context/ContextProvider";
import "../machineData/contentPage.scss";
import headerImage from "../../assets/images/logo.png";
import { dbConfig } from "../../infrastructure/db/db-config";
import axios from "axios";
import { useContentEditCount } from "../../services3/audits/ContentContext";

const DataReports = ({ type }) => {
  const { reportId } = useParams(); //ID of reports from which the fatData has to be fetched
  //const [fatListReportData, setFatListReportData] = useState({});
  const [reportDetails, setReportDetails] = useState([]);
  const [approvalTable, setApprovalTable] = useState([]);
  const [reportInfo, setReportInfo] = useState({});
  const [machineAll, setMachineAll] = useState([]);
  const [companyInfo, setCompanyInfo] = useState({});
  const history = useNavigate();
  const { currentMode } = useStateContext();
  const { contentEditCount, setContentEditCount } = useContentEditCount();

  // const databaseCollection = db.collection(companies).doc(companyId_constant)
  //     .collection(fatReport).doc(reportId).collection(`fatData`);

  // const DatabaseReportInfo = db.collection(companies).doc(companyId_constant)
  //     .collection(fatReport).doc(reportId);

  // const DatabaseMachines = db.collection(companies).doc(companyId_constant)
  //     .collection(machines);

  // const DatabaseCompanyInfo = db.collection(companies).doc(companyId_constant);

  //Fetch fat Data from the collection fatReportData

  //2nd lavel // Fat data Report datas
  const handleAuditData = async () => {
    // fatData-report
    await axios
      .post(`${dbConfig.url}/fatdata-report/findsome`, { fid: reportId })
      .then((response) => {
        console.log("fatData report data report id:", reportId);
        console.log("fatData report data:", response?.data);
        setReportDetails(response.data);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  // Mchine data Report datas
  const handleMachineData = async () => {
    // fatData-report
    await axios
      .get(`${dbConfig.url}/machines`)
      .then((response) => {
        console.log("Machine data:", response?.data?.data);
        setMachineAll(response.data?.data);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  // fatlist report  data
  const handleFatListData = async () => {
    // fatList-report
    await axios
      .get(`${dbConfig.url}/fatlist-report/${reportId}`)
      .then((response) => {
        //console.log("fatList report data report id:", reportId)
        console.log("fatList report data:", response?.data);
        //setFatListReportData(response.data);
        setReportInfo(response.data);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  useEffect(() => {
    handleAuditData();
    handleMachineData();
    handleFatListData();
    // Fetching all the data from the fatData collection inside reportId document and storing in a state
    // DatabaseReportInfo.get().then((snap) => {
    //     setReportInfo(snap.data());
    //     // console.log("dataReport:", snap.data())
    // })
    // DatabaseMachines.onSnapshot((snap) => {
    //     const data = firebaseLooper(snap);
    //     setMachineAll(data);
    //     // console.log("dataReport machines:", data)
    // })
    // DatabaseCompanyInfo.get().then((snap) => {
    //     setCompanyInfo(snap.data())
    // })
    // databaseCollection
    //     .onSnapshot(snap => {
    //         const data = firebaseLooper(snap)
    //         data.sort(function (a, b) { return (a.index - b.index) })
    //         setReportDetails(data);
    //         //console.log("reportDetails:", data);
    //         //using snap : fetch table details of the collection i.e
    //         snap.forEach((record) => {
    //             //catching each record in the fn
    //             databaseCollection.doc(record.id).collection('approvalTable') //Approval table for only 1 section - Pre approvals
    //                 .onSnapshot(snap => {
    //                     const data = firebaseLooper(snap)
    //                     setApprovalTable(data)
    //                 })
    //         })
    //     })
  }, [contentEditCount]); //reportId changes automatically reIterate data from the db

  const background = {
    background: currentMode === "Dark" ? "#161C24" : "#fff",
    color: currentMode === "Dark" ? "#fff" : "#2b2b2b",
    border: currentMode === "Dark" ? "2px solid #fff" : "#2b2b2b",
  };

  return (
    <>
      <section className="contentViewPage min-h-0">
        <div className="allContentPreviewContainer">
          <header
            className="contentPageHeading font-thin text-xs"
            style={background}
          >
            <div className="contentHeading_left">
              <div className="contentViewPageLogo">
                <img src={headerImage} width="100" />
              </div>
              <div className="contentHeadingInfoLeft">
                <div className="pl-1">
                  <div className="contentHeadingTitle">
                    {
                      machineAll?.filter(
                        (data) => data?._id == reportInfo?.mid,
                      )[0]?.title
                    }
                  </div>
                  {/* <div>Operational Qualification</div>
                                        <div>Performance test (FAT)</div> */}
                  <div>
                    {" "}
                    <b>Model:</b>{" "}
                    {
                      machineAll?.filter(
                        (data) => data?._id == reportInfo?.mid,
                      )[0]?.model
                    }{" "}
                  </div>
                  <div>
                    {" "}
                    <b>Serial No:</b>{" "}
                    {
                      machineAll?.filter(
                        (data) => data?._id == reportInfo?.mid,
                      )[0]?.serialNo
                    }{" "}
                  </div>
                  <div>
                    {" "}
                    <b>Protocol No:</b> {reportInfo?.protocol_no}{" "}
                  </div>
                  <div>
                    {" "}
                    <b>Date:</b>{" "}
                    {reportInfo.date?.toString().substring(0, 15)}{" "}
                  </div>
                </div>
              </div>
            </div>
            <div className="contentHeading_right">
              <div className="contentHeadingInfoRight">
                {/* <div className="modelNo">
                                    <span>Model No:</span> LYOMAX 9S
                                </div> */}

                {/* <div className="dataTime">
                                    <Button onClick={() => { history(`/${reportId}/add-users-fat`) }} variant='outlined' color="info" > Add Users to Document</Button>
                                </div> */}
              </div>
            </div>
          </header>
        </div>
      </section>

      {reportDetails
        ?.sort((a, b) => a["index"] - b["index"])
        .map((data) => (
          <ReportItem type={type} data={data} />
        ))}
    </>
  );
};

export default DataReports;
