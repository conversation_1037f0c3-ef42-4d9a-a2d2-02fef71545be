import React, { useEffect, useState } from "react";
import { db } from "../../../firebase";
import { companies, companyId_constant } from "../../../constants/data";
import { firebaseLooper } from "../../../tools/tool";
import DynamicTable from "./DynamicTable";
import { Button } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";

const EditDynamicTableValues = ({
  mid,
  fid,
  docId,
  type,
  handleClose,
  handleNewTable,
  reportName,
  machineName,
}) => {
  const [dynamicTableDetail, setDynamicTableDetail] = useState([]);

  useEffect(() => {
    // db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection(type)
    //     .where("docId", "==", docId)
    //     .where("mid", "==", mid)
    //     .where("fid", "==", fid)
    //     .onSnapshot((snap) => {
    //         snap.forEach((snap) => {
    //             db.collection(companies)
    //                 .doc(companyId_constant)
    //                 .collection(type)
    //                 .doc(snap.id)
    //                 .collection("dynamic_table")
    //                 .onSnapshot((snap) => {
    //                     const data = firebaseLooper(snap);
    //                     setDynamicTableDetail(data);
    //                 });
    //         });
    //     });
  }, []);

  return (
    <>
      {dynamicTableDetail?.map((value, idx) => (
        <div key={value.id} className="content_sub-section">
          <DynamicTable
            key={value.id}
            docId={docId}
            type={type}
            idx={idx}
            dynamicTable={JSON.parse(value?.dynamicTable)}
            tableName={dynamicTableDetail[idx]?.tableName}
            dynamicTablesDocId={value.id}
            handleClose={handleClose}
            reportName={reportName}
            machineName={machineName}
          />
        </div>
      ))}
      <div className="mt-8 flex justify-between">
        <Button
          onClick={() => {
            handleClose();
          }}
          variant="contained"
          color="success"
          endIcon={<CloseIcon />}
        >
          Close{" "}
        </Button>
        <Button
          onClick={() => {
            handleClose();
            setTimeout(() => {
              handleNewTable();
            }, 400);
          }}
          type="submit"
          variant="contained"
          endIcon={<AddIcon />}
        >
          {" "}
          Add More Table{" "}
        </Button>
      </div>
    </>
  );
};

export default EditDynamicTableValues;
