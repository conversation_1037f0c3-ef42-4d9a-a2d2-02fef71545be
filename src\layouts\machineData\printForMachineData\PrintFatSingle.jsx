import React, { useEffect, useState } from "react";
import {
  companies,
  companyId_constant,
  machines,
  pType,
  pFType,
  pInput,
} from "../../../constants/data";
import { db } from "../../../firebase";
import { firebaseLooper } from "../../../tools/tool";
import "../contentPage.scss";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Fab,
  Button,
  CircularProgress,
  Tooltip,
  Typography,
  Dialog,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import ClearIcon from "@mui/icons-material/Clear";
import BackupTableIcon from "@mui/icons-material/BackupTable";
import FontDownloadIcon from "@mui/icons-material/FontDownload";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import AppRegistrationIcon from "@mui/icons-material/AppRegistration";
import ContentTableItem from "../ContentTableItem";
import AddDetailsDocumentation from "../ContentMaster/AddDetailsDocumentation";
import AddTableValues from "../ContentMaster/AddTableValues";
import AddDynamicTableValues from "../ContentMaster/AddDynamicTableValues";
import EditDynamicTableValues from "../ContentMaster/EditDynamicTableValues";
import EditDetailsDocumentation from "../ContentMaster/EditMaster";
import { useNavigate, useParams } from "react-router-dom";
import AddApproval from "../ContentMaster/AddApproval";
import ApprovalTableItem from "../ApprovalTable/ApprovalTable";
import Modal from "@mui/material/Modal";
import { useStateContext } from "../../../context/ContextProvider";
import Formatting from "../../../components/Editor/Formatting";

const PrintFatSingle = () => {
  const [details, setDetails] = useState([]);
  const [tableDetails, setTableDetails] = useState([]);
  const [dynamicTableDetail, setDynamicTableDetail] = useState();
  //const [open, setOpen] = useState(false);
  const [openAddDetails, setOpenAdd] = useState(false);
  const [openTable, setOpenTable] = useState(false);
  const [openDynamicTable, setOpenDynamicTable] = useState(false);
  const [openEditDynamicTable, setOpenEditDynamicTable] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openAppr, setOpenAppr] = useState(false);
  const [approvalTable, setApprovalTable] = useState([]);
  const [customerApprovalTable, setCustomerApprovalTable] = useState([]);
  const [companyApprovalTable, setCompanyApprovalTable] = useState([]);
  const [dataLoading, setDataLoading] = useState(true);
  const today = new Date();
  const [machineName, setMachineName] = useState("");
  const history = useNavigate();
  const [legend, setLegend] = useState(false);
  const [process] = useState([pType, pFType, pInput]);
  const [title] = useState([
    "Process Type",
    "Process Function Type",
    "Process Input Parameter",
  ]);
  const { currentColor, currentMode } = useStateContext();
  const [companyInfo, setCompanyInfo] = useState({});

  const { docId, mid } = useParams();
  //const { mid } = useParams()
  const type = "fatData";
  //  const mid = 'zVQdHBL3SJfOvZ6vN4ds'
  const [printButtonVisivility, setPrintButtonVisibility] = useState(true);

  useEffect(() => {
    // db.collection(companies).doc(companyId_constant).get().then((snap) => {
    // 	setCompanyInfo(snap.data())
    // })
    // db.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(machines)
    // 	.doc(mid)
    // 	.onSnapshot((snap) => {
    // 		const data = snap.data();
    // 		setMachineName(data)
    // 	})
    // db.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(type)
    // 	.doc(docId)
    // 	.onSnapshot((snap) => {
    // 		const data = snap.data();
    // 		setDetails({
    // 			id: snap.id,
    // 			...data
    // 		});
    // 		setDataLoading(false);
    // 	})
    // db.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(type)
    // 	.doc(docId)
    // 	.collection("table")
    // 	.onSnapshot((snap) => {
    // 		const data = firebaseLooper(snap);
    // 		data.sort(function (a, b) {
    // 			return (a.index - b.index)
    // 		})
    // 		setTableDetails(data);
    // 	});
    // db.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(type)
    // 	.doc(docId)
    // 	.collection("approvalTable")
    // 	.onSnapshot((snap) => {
    // 		const data = firebaseLooper(snap);
    // 		data.sort(function (a, b) {
    // 			return (a.index - b.index)
    // 		})
    // 		const customerData = data.filter((elem) => elem.approverType === "customer")
    // 		const companyData = data.filter((elem) => elem.approverType === "company")
    // 		setApprovalTable(data);
    // 		setCustomerApprovalTable(customerData);
    // 		setCompanyApprovalTable(companyData);
    // 	});
    // db.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(type)
    // 	.doc(docId)
    // 	.collection("dynamic_table")
    // 	.onSnapshot((snap) => {
    // 		const data = firebaseLooper(snap);
    // 		setDynamicTableDetail(data);
    // 	});
  }, []);

  //
  const handlePrint = () => {
    let promise = new Promise((resolve, reject) => {
      resolve();
    });
    promise
      .then(() => {
        setPrintButtonVisibility(false);
      })
      .then(() => {
        return new Promise((resolve, reject) => {
          window.print();
          resolve();
        });
        //window.print();
      })
      .then(() => {
        window.close();
      });
  };

  return (
    <section className="contentViewPage bg-white text-black">
      <Modal open={legend} onClose={() => setLegend(false)}>
        <div className="legend">
          <p>Legend</p>
          {process.map((options, index) => (
            <ProcessTable title={title[index]} value={options} key={index} />
          ))}
        </div>
      </Modal>
      <div className="allContentPreviewContainer">
        <header className="contentPageHeading">
          <div className="contentHeading_left">
            <div className="contentViewPageLogo">
              <img src={companyInfo.url} width="100" />
            </div>
            <div className="contentHeadingInfoLeft">
              <div className="contentHeadingTitle">
                {details ? (
                  details.title
                ) : (
                  <Button
                    onClick={() => setOpenAdd(true)}
                    variant="contained"
                    color="primary"
                  >
                    Add details
                  </Button>
                )}
              </div>
              <div>Operational Qualification</div>
              <div>Performance test (FAT)</div>
            </div>
          </div>
          <div className="contentHeading_right">
            <div className="contentHeadingInfoRight">
              <div className="modelNo">
                <span>Model No:</span> {machineName?.model}
              </div>
              <div className="serialNo">
                <span>Document ID :</span> {details?.id}
              </div>
              <div className="dataTime">
                {details?.createdAt?.toDate().toString().substring(0, 25)}
              </div>
            </div>
          </div>
        </header>
        {/* {details?.length > 0 ? ( */}
        {details ? (
          <main className="contentPageMain ">
            <div className="contentMainContainer">
              {details?.title ? (
                <div className="content_sub-section">
                  <header className="font-bold text-lg mb-3">
                    <div className="capitalize">
                      {details.index + +1 + `.0 `} {details?.title}
                    </div>
                  </header>
                  <div className="italic font-medium">
                    {details?.desc?.blocks?.length &&
                      details?.desc.blocks.map((options) => (
                        <Formatting key={options.id} data={options} />
                      ))}
                  </div>
                </div>
              ) : (
                ""
              )}
              {details?.objective ? (
                <div className="content_sub-section">
                  <div className="content_subtitle flex">
                    <h4 className="font-bold  mr-3"> OBJECTIVE:</h4>
                    <p>{details?.objective}</p>
                  </div>
                </div>
              ) : (
                ""
              )}
              {details.method ? (
                <div className="content_sub-section">
                  <div className="content_subtitle flex">
                    <h4 className="font-bold  mr-3"> METHOD:</h4>
                    <p>{details?.method}</p>
                  </div>
                </div>
              ) : (
                ""
              )}
              {details?.pre?.length > 0 ? (
                <div className="content_sub-section">
                  <div className="content_subtitle">
                    <h4 className="font-bold  mr-3 mb-2"> PREREQUISITES:</h4>
                    <div style={{ marginLeft: "10%" }}>
                      {details.pre.map((point, idx) => (
                        <p className="mb-2" key={idx}>
                          <span className="font-bold ">{idx + 1}.</span> {point}
                        </p>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                ""
              )}
              {details?.procedure?.length > 0 ? (
                <div className="content_sub-section">
                  <div className="content_subtitle">
                    <h4 className="font-bold  mr-3 mb-2"> TEST PROCEDURE:</h4>
                    <div style={{ marginLeft: "10%" }}>
                      {details.procedure.map((point, idx) => (
                        <p className="mb-2" key={idx}>
                          <span className="font-bold ">{idx + 1}.</span> {point}
                        </p>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                ""
              )}

              {tableDetails?.length > 0 ? (
                <div className="content_sub-section">
                  <div className="table_title">
                    <h4 className="font-bold  mr-3 mb-4">TEST EXECUTION</h4>
                  </div>
                  <TableContainer component={Paper} className="table">
                    <Table
                      sx={{
                        minWidth: 650,
                        width: "100%",
                        backgroundColor: "#fff",
                      }}
                    >
                      <TableHead>
                        <TableRow
                          style={{
                            border: "1px solid black",
                            fontWeight: "bold",
                          }}
                        >
                          <TableCell
                            style={{ border: "1px solid black", color: "#111" }}
                            align="left"
                          >
                            Check Point
                          </TableCell>
                          <TableCell
                            style={{ border: "1px solid black", color: "#111" }}
                            align="center"
                          >
                            Observation
                          </TableCell>
                          <TableCell
                            style={{ border: "1px solid black", color: "#111" }}
                            align="center"
                          >
                            Acceptance Criteria
                          </TableCell>
                          <TableCell
                            style={{ border: "1px solid black", color: "#111" }}
                            align="center"
                          >
                            Confirm YES/NO
                          </TableCell>
                          <TableCell
                            style={{ border: "1px solid black", color: "#111" }}
                            align="center"
                          >
                            Deviation
                          </TableCell>
                          {/* <TableCell style={{ border: '1px solid black' }} align="center">Actions</TableCell> */}
                        </TableRow>
                      </TableHead>

                      <TableBody>
                        {tableDetails.map((data) => (
                          <ContentTableItem
                            forComponent="print"
                            collectionId={details?.id}
                            index={tableDetails?.length}
                            type={type}
                            key={data.id}
                            data={data}
                          />
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </div>
              ) : (
                ""
              )}

              {dynamicTableDetail?.map((value, idx) => {
                return (
                  <div className="content_sub-section">
                    <div className="table_title">
                      <h4 className="font-bold  mr-3 mb-4">
                        {dynamicTableDetail[idx]?.tableName}
                      </h4>
                    </div>
                    <TableContainer component={Paper} className="table">
                      <Table sx={{ minWidth: 650, width: "100%" }}>
                        <TableHead>
                          <TableRow>
                            {JSON.parse(
                              dynamicTableDetail[idx]?.dynamicTable,
                            )[0]?.map((data) => (
                              <TableCell key={data.id} align="center">
                                {data.value}
                              </TableCell>
                            ))}
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {JSON.parse(
                            dynamicTableDetail[idx]?.dynamicTable,
                          ).map((row, idx) => {
                            if (idx !== 0) {
                              return (
                                <TableRow
                                  key={idx}
                                  sx={{
                                    "&:last-child td, &:last-child th": {
                                      border: 0,
                                    },
                                  }}
                                  style={{ cursor: "pointer" }}
                                >
                                  {row.map((cellData, cellIdx) => (
                                    <TableCell
                                      key={cellIdx}
                                      style={{ border: "1px solid black" }}
                                      align="center"
                                    >
                                      {cellData.value}
                                    </TableCell>
                                  ))}
                                </TableRow>
                              );
                            }
                          })}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </div>
                );
              })}
              {companyApprovalTable?.length > 0 ? (
                <div className="content_sub-section">
                  <TableContainer component={Paper} className="table">
                    <div className="table_superHeader">Company</div>
                    <Table sx={{ minWidth: 650, width: "100%" }}>
                      <TableHead>
                        <TableRow
                          style={{
                            color: "black",
                            backgroundColor: "#D9D9D9",
                            border: "1px solid black",
                            fontWeight: "bold",
                          }}
                        >
                          <TableCell
                            style={{
                              border: "1px solid black",
                              color: "black",
                            }}
                            align="left"
                          >
                            Name
                          </TableCell>
                          <TableCell
                            style={{
                              border: "1px solid black",
                              color: "black",
                            }}
                            align="left"
                          >
                            Signature
                          </TableCell>
                          <TableCell
                            style={{
                              border: "1px solid black",
                              color: "black",
                            }}
                            align="center"
                          >
                            Date
                          </TableCell>
                        </TableRow>
                      </TableHead>

                      <TableBody>
                        {companyApprovalTable.map((data) => (
                          <ApprovalTableItem
                            useAt="print"
                            key={data.id}
                            data={data}
                          />
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </div>
              ) : (
                ""
              )}
              {customerApprovalTable?.length > 0 ? (
                <div className="content_sub-section">
                  {/* <div className="table_title">
										<h4 className="font-bold  mr-3 mb-4">PRE APPROVAL SIGNATURE</h4>
									</div> */}
                  <TableContainer component={Paper} className="table">
                    <div className="table_superHeader">Customer</div>
                    <Table sx={{ minWidth: 650, width: "100%" }}>
                      <TableHead>
                        <TableRow
                          style={{
                            color: "black",
                            backgroundColor: "#D9D9D9",
                            border: "1px solid black",
                            fontWeight: "bold",
                          }}
                        >
                          <TableCell
                            style={{
                              border: "1px solid black",
                              color: "black",
                            }}
                            align="left"
                          >
                            Name{" "}
                          </TableCell>
                          <TableCell
                            style={{
                              border: "1px solid black",
                              color: "black",
                            }}
                            align="left"
                          >
                            Signature
                          </TableCell>
                          <TableCell
                            style={{
                              border: "1px solid black",
                              color: "black",
                            }}
                            align="center"
                          >
                            Date
                          </TableCell>
                        </TableRow>
                      </TableHead>

                      <TableBody>
                        {customerApprovalTable.map((data) => (
                          <ApprovalTableItem
                            useAt="print"
                            key={data.id}
                            data={data}
                          />
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </div>
              ) : (
                ""
              )}
            </div>
          </main>
        ) : (
          <main className="contentPageMain flex justify-center">
            {dataLoading && <CircularProgress />}
            <Typography variant="h5" style={{ marginLeft: "30px" }}>
              NO DATA{" "}
            </Typography>
          </main>
        )}

        {printButtonVisivility && (
          <div className=" fixed bottom-1 right-2">
            <button
              className="bg-gray-400 hover:bg-gray-500 text-white font-bold py-1 px-2 m-1 rounded"
              onClick={() => handlePrint()}
            >
              Print
            </button>
          </div>
        )}

        {/* <Fab className="faBtn" color={"primary"} onClick={() => setOpen(!open)}>
					{open ? (
						<ClearIcon sx={{ color: "#fff" }} />
					) : (
						<AddIcon sx={{ color: "#fff" }} />
					)}
				</Fab>
				<Fab className="faBtnLeft" color={"primary"} onClick={() => history.go(-1)}>
					<ArrowBackIcon />
				</Fab> */}

        {/* {
					open && (
						<div className="fabDrawer">
							
							<Tooltip title="Approval Signatures">
								<Button color='warning' onClick={() => setOpenAppr(true)}>
									<FontDownloadIcon />
								</Button>
							</Tooltip>
							<Tooltip title="Test Execution Table Template">
								<Button color='success' onClick={() => setOpenTable(true)}>
									<BackupTableIcon />
								</Button>
							</Tooltip>
							<Tooltip title="Custom Table Generation">
								<Button color='info' onClick={() => setOpenDynamicTable(true)}>
									<BackupTableIcon />
								</Button>
							</Tooltip>

							<Tooltip title="Edit Custom Table Values">
								<Button color='info' onClick={() => setOpenEditDynamicTable(true)}>
									<AppRegistrationIcon />
								</Button>
							</Tooltip>
							<Tooltip title="Edit Master Details">
								<Button onClick={() => setOpenEdit(true)}>
									<i className="ri-pencil-fill text-blue-500"></i>
								</Button>
							</Tooltip>

						
							<Tooltip title="Print this page">
								<Button onClick={() => printAFour()}>
									<i class="ri-printer-line"></i>
								</Button>
							</Tooltip>

						</div>
					)
				}
                 */}
      </div>
      {/* Add details when details length is zero */}
      <Dialog open={openAddDetails} fullWidth maxWidth="lg">
        <DialogTitle>Add Details to Master Copy</DialogTitle>
        <DialogContent>
          <AddDetailsDocumentation
            type={type}
            handleClose={() => setOpenAdd(false)}
            machineName={machineName.title}
          />
        </DialogContent>
      </Dialog>
      {/* Add Table details to master copy */}
      <Dialog open={openTable} fullWidth maxWidth="lg">
        <DialogTitle>Add Table Row to Master Copy</DialogTitle>
        <DialogContent>
          <AddTableValues
            collectionId={details?.id}
            index={tableDetails?.length}
            type={type}
            handleClose={() => setOpenTable(false)}
          />
        </DialogContent>
      </Dialog>
      {/* Add Dynamic Table Detail */}
      <Dialog open={openDynamicTable} fullWidth maxWidth="lg">
        <DialogTitle>Add Dynamic Table to Master Copy</DialogTitle>
        <DialogContent>
          <AddDynamicTableValues
            collectionId={details?.id}
            type={type}
            handleClose={() => setOpenDynamicTable(false)}
            reportName={details?.title}
            machineName={machineName.title}
          />
        </DialogContent>
      </Dialog>
      {/* Edit Dynamic Table Detail */}
      <Dialog open={openEditDynamicTable} fullWidth maxWidth="lg">
        <DialogTitle>Edit Dynamic Table to Master Copy</DialogTitle>
        <DialogContent>
          <EditDynamicTableValues
            docId={docId}
            collectionId={details?.id}
            type={type}
            handleClose={() => setOpenEditDynamicTable(false)}
            handleNewTable={() => setOpenDynamicTable(true)}
            reportName={details?.title}
            machineName={machineName.title}
          />
        </DialogContent>
      </Dialog>
      {/* Edit details */}
      <Dialog open={openEdit} fullWidth maxWidth="lg">
        <DialogTitle>Edit Details</DialogTitle>
        <DialogContent>
          <EditDetailsDocumentation
            details={details}
            type={type}
            handleClose={() => setOpenEdit(false)}
            machineName={machineName.title}
          />
        </DialogContent>
      </Dialog>
      {/* Add Approval signatures table */}
      <Dialog open={openAppr} fullWidth maxWidth="lg">
        <DialogTitle>
          Add Approval Signatures -{" "}
          <span className="text-gray-500" style={{ fontSize: "14px" }}>
            {today?.toString().substring(0, 15)}
          </span>
        </DialogTitle>
        <DialogContent>
          <AddApproval
            collectionId={details?.id}
            index={approvalTable?.length}
            type={type}
            machineName={machineName.title}
            handleClose={() => setOpenAppr(false)}
            reportTitle={details?.title}
          />
        </DialogContent>
      </Dialog>
    </section>
  );
};

const ProcessTable = ({ title, value }) => {
  // console.log(value, title)
  return (
    <TableContainer
      component={Paper}
      className="table"
      sx={{ margin: "5px 0" }}
    >
      <h1>{title}</h1>
      <Table sx={{ minWidth: 650, width: "100%" }}>
        <TableHead>
          <TableRow
            style={{
              color: "white",
              backgroundColor: "#D9D9D9",
              border: "1px solid black",
              fontWeight: "bold",
            }}
          >
            <TableCell style={{ border: "1px solid black" }} align="left">
              KEY
            </TableCell>
            <TableCell style={{ border: "1px solid black" }} align="center">
              VALUE
            </TableCell>
          </TableRow>
        </TableHead>

        <TableBody>
          {value.map((data) => (
            <TableRow
              sx={{
                "&:last-child td, &:last-child th": { border: 0 },
              }}
              style={{ cursor: "pointer" }}
            >
              <TableCell style={{ border: "1px solid black" }} align="left">
                {data.key}
              </TableCell>
              <TableCell style={{ border: "1px solid black" }} align="center">
                {data.value}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default PrintFatSingle;
