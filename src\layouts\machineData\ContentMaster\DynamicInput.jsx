import React, { useState, useEffect } from "react";
import { InputLabel } from "@mui/material";
import { TextField } from "@mui/material";

function DynamicInput({ headerValue, prevValue, column_idx, setTile }) {
  const [inputValue, setInputValue] = useState(prevValue ? prevValue : "");

  useEffect(() => {
    setTile({
      id: column_idx,
      value: inputValue,
    });
  }, [inputValue]);

  return (
    <div>
      <InputLabel>{headerValue}</InputLabel>
      <TextField
        value={inputValue}
        onChange={(e) => {
          setInputValue(e.target.value);
        }}
        style={{ marginBottom: "10px" }}
        variant="outlined"
        fullWidth
        placeholder={`Enter Column ${column_idx + 1} Data`}
      />
    </div>
  );
}

export default DynamicInput;
