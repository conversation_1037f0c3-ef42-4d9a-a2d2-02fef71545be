// Notification Container
.notificationBoxContainer {
  margin: 0px;
  box-sizing: border-box;
  width: 100%;
  max-width: 22vw;
  max-height: 100vh;
  scroll-behavior: smooth;
  contain: content;
  overflow-y: scroll;
  overflow-x: hidden;
  border-radius: 10px;
  padding-left: 1.6rem;
  padding-right: 1.6rem;
  min-height: 300px;
  box-shadow: rgba(0, 0, 0, 0.25) 0rem 1.25rem 1.6875rem 0rem;

  .heading {
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.9;
    padding-left: 4px;
    margin-bottom: 0.4rem;
    width: 100%;
    position: sticky;
    display: flex;
    justify-content: center;
    top: 0px;
  }

  .notificationBoxWrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding-bottom: 16px;

    .notificationList {
      display: flex;
      align-items: center;
      padding: 0.35rem 0.55rem;
      margin: 0.75rem 0;
      cursor: pointer;
      border-radius: 10px;

      .iconContainer {
        width: 35px;
        height: 35px;
        margin-right: 0.5rem;

        img {
          width: 100%;
          height: 100%;
          border-radius: 8px;
        }
      }

      .notificationInfoContainer {
        display: flex;
        flex-direction: column;

        .title {
          span {
            font-size: 0.9rem;
            font-weight: 500;
          }

          font-size: 0.8rem;
        }

        .time {
          display: flex;
          align-items: center;
          padding-top: 0.11rem;

          span {
            margin-right: 0.4rem;
            font-size: 0.9rem;
            font-weight: 500;
          }

          font-size: 0.8rem;
        }
      }
    }
  }
}
