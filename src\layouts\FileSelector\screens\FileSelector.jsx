import {
  <PERSON>,
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  LinearProgress,
  Typography,
} from "@mui/material";
import { DropzoneArea } from "material-ui-dropzone";
import React, { useState } from "react";
import { ButtonBasic } from "../../../components/buttons/Buttons";
import FoldersList from "../components/folder.component";
import CloudSyncIcon from "@mui/icons-material/CloudSync";
import ComputerIcon from "@mui/icons-material/Computer";
import { DeleteByUrl } from "../../../utils/StorageOptions";
import { useStorageTablesFile } from "../../../utils/useStorageTablesFile";
import { toastMessage } from "../../../tools/toast";
import { useStateContext } from "../../../context/ContextProvider";

const FileSelector = ({ setFType }) => {
  const [open, setOpen] = useState(false);
  const [file, setFile] = useState(null);
  const [option, setOption] = useState("computer");
  const { currentMode } = useStateContext();

  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];

  const { progress, url } = useStorageTablesFile(file);

  const handleChangeImage = (file) => {
    let selectedFile = file[0];
    if (selectedFile) {
      if (selectedFile.type === "application/pdf" && setFType) setFType(true);
      if (types.includes(selectedFile.type)) {
        if (url) {
          DeleteByUrl(url);
        }
        setFile(selectedFile);
      } else {
        if (setFType) setFType(false);
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    } else {
      if (setFType) setFType(false);
    }
  };

  const handleDeleteDropZone = (url) => {
    DeleteByUrl(url);
    setFile(null);
  };

  const handleCancel = () => {
    if (url) {
      DeleteByUrl(url);
    }
  };

  return (
    <>
      <Box sx={{ p: 2, m: 2 }}>
        <Box>
          <Box
            onClick={() => setOption("files")}
            sx={{
              border: option === "files" ? "#17D7A0" : "1px solid black",
              p: 1.2,
              cursor: "pointer",
              bgcolor: option === "files" ? "#17D7A0" : "white",
              color: option === "files" ? "white" : "black",
            }}
          >
            <Typography align="center">
              <CloudSyncIcon /> Add From Library
            </Typography>
          </Box>
          {/* <Button sx={{bgcolor: '#6E85B7', color: 'white'}} fullWidth onClick={() => setOpen(true)}>Add From File Manager</Button> */}
        </Box>
        <br />
        <Typography align="center" gutterBottom>
          -- OR --
        </Typography>
        <br />
        <Box>
          <Box
            onClick={() => setOption("computer")}
            sx={{
              border: option === "computer" ? "#17D7A0" : "1px solid black",
              p: 1.2,
              cursor: "pointer",
              bgcolor: option === "computer" ? "#17D7A0" : "white",
              color: option === "computer" ? "white" : "black",
            }}
          >
            <Typography align="center">
              <ComputerIcon /> Add From Computer
            </Typography>
          </Box>
          {/* <Button sx={{bgcolor: '#6E85B7', color: 'white'}} fullWidth onClick={() => setOpen(true)}>Add From File Manager</Button> */}
        </Box>
        <Box></Box>

        {/* File Manager Option = ""files */}

        {option === "files" && (
          <Box>
            <FoldersList />
          </Box>
        )}

        {option === "computer" && (
          <Box>
            <br />
            <Typography align="left" gutterBottom>
              Add From Computer (MAX FILE SIZE : 5mb)
            </Typography>
            <DropzoneArea
              showFileNames
              onChange={(loadedFiles) => handleChangeImage(loadedFiles)}
              dropzoneText="Drag and Drop / Click to ADD Media"
              showAlerts={false}
              filesLimit={1}
              maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
              onDelete={() => handleDeleteDropZone(url)}
              dropzoneClass={
                currentMode === "Dark"
                  ? "dropZoneClassDark"
                  : "dropZoneClassLight"
              }
            />
            <br />
            <LinearProgress variant="determinate" value={progress} />
            <Typography>Uploaded : {progress} %</Typography>
          </Box>
        )}
      </Box>

      <Dialog open={open} onClose={() => setOpen(false)} fullWidth>
        <DialogTitle>Select File from Library</DialogTitle>
        <DialogContent>
          <FoldersList setFType={setFType} />
        </DialogContent>
        <DialogActions>
          <ButtonBasic buttonTitle="Confirm & Exit" />
        </DialogActions>
      </Dialog>
    </>
  );
};

export default FileSelector;
