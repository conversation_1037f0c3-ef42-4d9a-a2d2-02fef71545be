import React, { useRef, useEffect } from "react";
import * as d3 from "d3";
import { Card, CardContent, Typography, Box } from "@mui/material";

const ControlChart = ({ timestamps, values }) => {
  const chartRef = useRef();

  useEffect(() => {
    if (!timestamps || !values || values.length < 5) return;

    // Clear previous chart
    d3.select(chartRef.current).selectAll("*").remove();

    // Get dynamic width
    const containerWidth = chartRef.current.parentElement.clientWidth;
    const width = containerWidth - 40; // Adjust for padding
    const height = 250;
    const margin = { top: 20, right: 30, bottom: 30, left: 50 };

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left}, ${margin.top})`);

    // Parse timestamps
    const parsedTimestamps = timestamps.map((t) => new Date(t));

    // Compute mean and control limits
    const mean = d3.mean(values);
    const stdDev = d3.deviation(values);
    const UCL = mean + 2 * stdDev; // Upper Control Limit
    const LCL = mean - 2 * stdDev; // Lower Control Limit

    // Define scales
    const xScale = d3
      .scaleTime()
      .domain(d3.extent(parsedTimestamps))
      .range([0, width - margin.left - margin.right]);

    const yScale = d3
      .scaleLinear()
      .domain([d3.min([LCL, ...values]) - 5, d3.max([UCL, ...values]) + 5])
      .range([height - margin.top - margin.bottom, 0]);

    // Define line generator
    const line = d3
      .line()
      .x((d, i) => xScale(parsedTimestamps[i]))
      .y((d) => yScale(d))
      .curve(d3.curveMonotoneX);

    // Draw control limits
    const drawControlLine = (value, color, label) => {
      svg
        .append("line")
        .attr("x1", 0)
        .attr("x2", width - margin.left - margin.right)
        .attr("y1", yScale(value))
        .attr("y2", yScale(value))
        .attr("stroke", color)
        .attr("stroke-dasharray", "5,5")
        .attr("stroke-width", 1.5);

      svg
        .append("text")
        .attr("x", width - margin.left - margin.right - 5)
        .attr("y", yScale(value) - 5)
        .attr("text-anchor", "end")
        .attr("font-size", "12px")
        .attr("fill", color)
        .text(label);
    };

    drawControlLine(UCL, "red", "UCL");
    drawControlLine(mean, "blue", "CL");
    drawControlLine(LCL, "red", "LCL");

    // Draw the process data line
    svg
      .append("path")
      .datum(values)
      .attr("fill", "none")
      .attr("stroke", "#000")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add X axis
    svg
      .append("g")
      .attr("transform", `translate(0,${height - margin.top - margin.bottom})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat("%H:%M")));

    // Add Y axis
    svg.append("g").call(d3.axisLeft(yScale));

    // Highlight points outside control limits
    svg
      .selectAll(".dot")
      .data(values)
      .enter()
      .append("circle")
      .attr("cx", (d, i) => xScale(parsedTimestamps[i]))
      .attr("cy", (d) => yScale(d))
      .attr("r", 4)
      .attr("fill", (d) => (d > UCL || d < LCL ? "red" : "black"));
  }, [timestamps, values]);

  return (
    <Card sx={{ width: "100%", mt: 4 }}>
      <CardContent>
        <Typography variant="h6" align="center" gutterBottom>
          SPC - Control Chart
        </Typography>
        <Box ref={chartRef} sx={{ width: "100%", height: "250px" }} />
      </CardContent>
    </Card>
  );
};

export default ControlChart;
