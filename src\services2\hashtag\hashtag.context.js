import { Construction } from "@mui/icons-material";
import { data } from "autoprefixer";
import axios from "axios";
import { createContext, useEffect, useState } from "react";
import { companies, companyId_constant } from "../../constants/data";
import { db } from "../../firebase";
import { dbConfig } from "../../infrastructure/db/db-config";
import { firebaseLooper } from "../../tools/tool";

export const HashtagContext = createContext();

const HashtagProvider = ({ children }) => {
  const [hashes, setHashes] = useState([]);
  const [tempHash, setTempHash] = useState([]);
  const [tempHashes, setTempHashes] = useState([]);
  const [hashArray, setHashArray] = useState([]);
  const [machines, setMachines] = useState([]);
  const [maint, setMaint] = useState([]);
  const [stepArray, setStepArray] = useState([]);
  const [mapForVis, setMapFV] = useState(new Map());
  const [temptemp, setTempTemp] = useState([]);
  const [open, setOpen] = useState(false);

  const handleHastageData = async () => {
    await axios
      .get(`${dbConfig.url}/hashtags`)
      .then((response) => {
        setHashes(response?.data?.data);
        let map = new Map();
        // console.log("e",hashes)
        response?.data?.data.forEach((data) => {
          // console.log(data)
          map.set(data?.maintenance_id, data?.visits);
          setMapFV(map);
          // console.log(mapForVis)
        });
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const handleStepData = async () => {
    await axios
      .get(`${dbConfig.url}/stepdata`)
      .then((response) => {
        setStepArray(response.data?.data);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const maintenanceData = async () => {
    await axios
      .get(`${dbConfig.url}/maintenance`)
      .then((response) => {
        // console.log("testing maint data",response.data)
        var data = [];
        response?.data?.data.forEach(
          (options) =>
            (data = [...data, { maintenance_id: options?._id, data: options }]),
        );
        setMaint(data);
        // console.log(data)
        // console.log(maint)
      })
      .catch((error) => {
        console.log(error);
      });
  };
  const machineData = async () => {
    await axios
      .get(`${dbConfig.url}/machines`)
      .then((response) => {
        let arr = [];
        // console.log("API se ye data aaya",response?.data)
        response.data?.data?.forEach((options) => {
          arr = [...arr, { mid: options?._id, data: options }];
        });
        setMachines(arr);
        // console.log(arr)
        // console.log("ye hastag me mid set ho rha hai",arr)
      })
      .catch((error) => {
        console.log(error);
      });
  };

  // 🔄 Added refresh function
  const refreshHashtagData = async () => {
    await Promise.all([
      handleHastageData(),
      machineData(),
      maintenanceData(),
      handleStepData(),
    ]);
  };

  useEffect(() => {
    let isMount = true;
    if (isMount) {
      handleHastageData(); //API Call For Hastags

      // dbref.collection("Hastags").onSnapshot((snap) => {
      //   // let data = firebaseLooper(snap);
      //   // setHashes([...data]);
      //                                                      // after integration more work is required
      //   let map = new Map();
      //   snap.forEach(async (options) => {
      //     const rec = await dbref
      //       .collection("maintenanceData")
      //       .doc(options.data().maintenance_id)
      //       .get()
      //       .catch((err) => alert(err));
      //     map.set(options.data().maintenance_id, options.data().visits);
      //     setMapFV(map);
      //   });
      // });
      // console.log(hashArray)

      machineData(); //API Call For Machine Data
      // dbref.collection("machineData").onSnapshot((snap) => {
      //   let data = [];
      //   snap.forEach(
      //     (options) =>
      //       (data = [...data, { mid: options.id, data: options.data() }])
      //   );
      //   setMachines([...data]);
      // });

      maintenanceData(); //API Call For maintenance Data
      // dbref.collection("maintenanceData").onSnapshot((snap) => {
      //   let data = [];
      //   snap.forEach(
      //     (options) =>
      //       (data = [
      //         ...data,
      //         { maintenance_id: options.id, data: options.data() },
      //       ])
      //   );
      //   setMaint([...data]);
      // });

      handleStepData(); //API Call For StepData
      // dbref.collection("stepData").onSnapshot((snap) => {
      //   let data = firebaseLooper(snap);
      //   setStepArray([...data]);
      // });
    }
    return () => {
      isMount = false;
    };
  }, []);

  useEffect(() => {
    let arr = [],
      map = new Map();
    hashes?.forEach((options) => {
      if (options?.title !== "") {
        if (!arr.includes(options.title)) arr = [...arr, options.title];
        let array = map?.has(options.title) ? [...map?.get(options.title)] : [];
        array = [...array, options];
        map?.set(options.title, array);
      }
    });
    let temp = [];
    for (const [key, value] of map?.entries()) {
      temp = [
        ...temp,
        {
          id: key,
          value: value,
        },
      ];
    }
    setTempHash([...temp]);
    setTempHashes([...temp]);
    setTempTemp([...temp]);
    setHashArray([...arr]);

    return () => {
      setTempHash([]);
      setTempHashes([]);
      setHashArray([]);
    };
  }, [hashes]);

  return (
    <HashtagContext.Provider
      value={{
        hashes,
        setHashes,
        handleHastageData,
        tempHash,
        setTempHash,
        tempHashes,
        setTempHashes,
        hashArray,
        setHashArray,
        machines,
        maint,
        stepArray,
        mapForVis,
        temptemp,
        setTempTemp,
        open,
        setOpen,
        refreshHashtagData,
      }}
    >
      {children}
    </HashtagContext.Provider>
  );
};

export default HashtagProvider;
