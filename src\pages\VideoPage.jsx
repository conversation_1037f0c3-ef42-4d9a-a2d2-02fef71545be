import React, { useEffect, useState } from "react";
import Video from "../layouts/video/Video";
//import config from '../config';
import { db } from "../firebase";
import {
  companies,
  companyId_constant,
  openTalk,
  openTalkRelayed,
} from "../constants/data";
import { dbConfig } from "../infrastructure/db/db-config";
import axios from "axios";

const VideoPage = () => {
  const [apiKey, setApiKey] = useState(null);
  const [sessionId, setSessionId] = useState(null);
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refresh, setRefresh] = useState(false); // Forcing re-render

  useEffect(() => {
    // Fetch the videocall configuration from the backend
    const fetchVideoConfig = async () => {
      try {
        const response = await axios.get(`${dbConfig.url}/videocall`);

        if (!Array.isArray(response.data.data) || response.data.data.length === 0) {
          throw new Error(
            "Invalid API response format: Expected an array with data.",
          );
        }

        // Extract the first item from the array
        const videoData = response.data.data[0];

        if (!videoData.apiKey || !videoData.sessionId || !videoData.token) {
          throw new Error(
            "Missing required fields (apiKey, sessionId, token) in response.",
          );
        }

        // Update state
        setApiKey(videoData.apiKey);
        setSessionId(videoData.sessionId);
        setToken(videoData.token);
        setRefresh((prev) => !prev); // Trigger re-render
      } catch (err) {
        console.error("Error fetching video call config:", err);
        setError("Failed to load video call configuration");
      } finally {
        setLoading(false);
      }
    };

    fetchVideoConfig();
  }, []);

  useEffect(() => {
    console.log("Updated State:", { apiKey, sessionId, token });
  }, [apiKey, sessionId, token]);

  if (loading) {
    return <div>Loading video call configuration...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <>
      {apiKey && sessionId && token ? (
        <Video
          key={refresh}
          apiKey={apiKey}
          sessionId={sessionId}
          token={token}
        />
      ) : (
        <div>Waiting for video configuration...</div>
      )}
    </>
  );
};

export default VideoPage;
