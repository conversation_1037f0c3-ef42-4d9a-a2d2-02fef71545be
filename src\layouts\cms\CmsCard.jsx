import { Error, Settings, WatchLater } from "@mui/icons-material";
import React from "react";
import LinearProgress from "@mui/material/LinearProgress";
import Box from "@mui/material/Box";
import { themeColors } from "../../infrastructure/theme";
import { useStateContext } from "../../context/ContextProvider";

export default function CmsCard(props) {
  const { currentMode, currentColorLight } = useStateContext();
  return (
    <>
      <div
        className={
          props.active
            ? "w-3/12 border p-2 rounded-md shadow-xl shadow-gray-500 "
            : "w-3/12 border p-2 rounded-md hover:shadow-xl hover:shadow-gray-500 active:shadow-gray-300 "
        }
        style={
          currentMode === "Dark"
            ? { backgroundColor: themeColors.dark.secordary, color: "white" }
            : { backgroundColor: "#fff" }
        }
        onClick={props.onClick}
      >
        <div className="flex p-2">
          {props.iconName === "Settings" && (
            <Settings style={{ fontSize: "2rem" }} color={props.color} />
          )}
          {props.iconName === "Error" && (
            <Error style={{ fontSize: "2rem" }} color={props.color} />
          )}
          {props.iconName === "WatchLater" && (
            <WatchLater style={{ fontSize: "2rem" }} color={props.color} />
          )}

          <div className="pl-1">{props.cardText}</div>
        </div>
        <div>
          <Box sx={{ width: "100%" }}>
            <LinearProgress
              variant="determinate"
              value={props.percentage}
              color={props.color}
            />
          </Box>
        </div>
      </div>
    </>
  );
}
