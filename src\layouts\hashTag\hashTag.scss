.allHashtagContainer {
  width: 100%;
  background-color: #fff;
  box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  color: #344767;
  padding: 1.2rem;
  margin: 0.5rem 0;
  .hashtagHeading {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.head {
  padding: 20px 5px;
  margin: 10px 0 20px;
  align-items: center;
  background: #fff;
  border-radius: 10px;
  box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.3);
}

.head span Box .searchIcon {
  margin: 0 5px;
  height: 40px;
}

.arrowForHashTag {
  cursor: pointer;
}

.arrowForHashTag:hover {
  background: whitesmoke;
}

.machineItem {
  cursor: pointer;
  // border: 2px solid lightgray;
  border-radius: 5px;
  margin: 10px 0;
  border-radius: 10px;
}

.form form {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.form form TextField {
  margin: 0 0 10px 0;
}

.accordItem {
  border: 2px solid gray;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 10px;
  margin: 1rem 0;
  padding: 20px 24px;
}
.actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
  padding: 20px 24px;
}
