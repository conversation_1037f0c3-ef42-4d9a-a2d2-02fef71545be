import axios from "axios";
import React, { createContext, useEffect, useState, useContext } from "react";
import { dbConfig } from "../../infrastructure/db/db-config";
// import { useMongoRefresh } from "../mongo-refresh.context";

// for machines
export const MachinesContext = createContext([]);
export const MachinesContextSetter = createContext([]);
export const MachinesEditCountContextSetter = createContext([]);
export const MachinesEditCountContextGetter = createContext([]);

//for training(steps)
export const TrainingStepContext = createContext([]);
export const TrainingStepContextSetter = createContext([]);
// for substeps
export const TrainingSubStepContext = createContext([]);
export const TrainingSubStepContextSetter = createContext([]);
//dependancy array for steps
export const DependancyEditSteparray = createContext([]);
//for machines
export function useMachinesAllGetter() {
  return useContext(MachinesContext);
}
export function useMachinesSetter() {
  return useContext(MachinesContextSetter);
}
//for dependancy array
export function useDependancyEditStepArray() {
  return useContext(DependancyEditSteparray);
}
export function useEditMachinesCountSetter() {
  return useContext(MachinesEditCountContextSetter);
}
export function useEditMachinesCountGetter() {
  return useContext(MachinesEditCountContextGetter);
}

//for training(steps)
export function useStepsAllGetter() {
  return useContext(TrainingStepContext);
}
export function useStepsSetter() {
  return useContext(TrainingStepContextSetter);
}

// for substeps
export function useSubStepsAllGetter() {
  return useContext(TrainingSubStepContext);
}
export function useSubStepsSetter() {
  return useContext(TrainingSubStepContextSetter);
}

const MachinesProvider2 = ({ children }) => {
  const [machines, setMachines] = useState([]);
  const [subSteps, setSubSteps] = useState([]);
  const [steps, setSteps] = useState([]);
  const [depEditArray, setDepEditArray] = useState(0);
  const [editMachineCount, seteditMachineCount] = useState(0);

  const handleMachineData = async () => {
    await axios
      .get(`${dbConfig.url}/machines`)
      .then((response) => {
        console.log("handleMachinecontext", response.data);
        setMachines(response.data.data);
      })
      .catch((error) => {
        console.error(error);
      });
  };
  const handleEditMachineCount = () => {
    function machineCounter() {
      let promise = new Promise((resolve, regect) => {
        resolve(seteditMachineCount(editMachineCount + 1));
      });
      return promise;
    }

    async function callPromises() {
      await handleMachineData(); // order is important
      await machineCounter();
    }

    callPromises();
  };
  const handleStepData = async () => {
    await axios
      .get(`${dbConfig.url}/stepdata`)
      .then((response) => {
        setSteps(response?.data?.data);
        // console.log("step context",response?.data)
        response?.data.sort(function (a, b) {
          return a.index - b.index;
        });
      })
      .catch((error) => {
        console.error(error);
      });
  };
  const handleSubStepData = async (step) => {
    await axios.get(`${dbConfig.url}/sub-steps`).then((response) => {
      setSubSteps(response?.data?.filter((x) => x?.step_id === step?._id));
    });
  };

  const handledepArray = () => {
    setDepEditArray(depEditArray + 1);
    // console.log(depEditArray)
  };
  useEffect(() => {
    // handleMachineData();
    setDepEditArray(0);
  }, []);

  return (
    <MachinesContext.Provider value={machines}>
      <MachinesContextSetter.Provider value={handleMachineData}>
        <TrainingStepContext.Provider value={steps}>
          <TrainingStepContextSetter.Provider value={handleStepData}>
            <TrainingSubStepContext.Provider value={subSteps}>
              <TrainingSubStepContextSetter.Provider value={handleSubStepData}>
                <MachinesEditCountContextSetter.Provider
                  value={handleEditMachineCount}
                >
                  <MachinesEditCountContextGetter.Provider
                    value={editMachineCount}
                  >
                    <DependancyEditSteparray.Provider
                      value={[depEditArray, handledepArray]}
                    >
                      {children}
                    </DependancyEditSteparray.Provider>
                  </MachinesEditCountContextGetter.Provider>
                </MachinesEditCountContextSetter.Provider>
              </TrainingSubStepContextSetter.Provider>
            </TrainingSubStepContext.Provider>
          </TrainingStepContextSetter.Provider>
        </TrainingStepContext.Provider>
      </MachinesContextSetter.Provider>
    </MachinesContext.Provider>
  );
};

export default MachinesProvider2;
