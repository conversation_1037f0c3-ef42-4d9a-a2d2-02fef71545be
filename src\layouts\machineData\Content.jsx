// note1:- approvalTable is updated (commented). now it has three static collumns in each. (from line: 305 aprox)
// note2:- major changes made onwards.
import React, { useEffect, useState } from "react";
import {
  companies,
  companyId_constant,
  machines,
  pType,
  pFType,
  pInput,
} from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import "./contentPage.scss";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Fab,
  Button,
  CircularProgress,
  Tooltip,
  Typography,
  Dialog,
  DialogContent,
  DialogTitle,
  Checkbox,
  DialogActions,
} from "@mui/material";
import { v4 as uuidv4 } from "uuid";
import AddIcon from "@mui/icons-material/Add";
import ClearIcon from "@mui/icons-material/Clear";
import BackupTableIcon from "@mui/icons-material/BackupTable";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import AppRegistrationIcon from "@mui/icons-material/AppRegistration";
import ContentTableItem from "./ContentTableItem";
import AddDetailsDocumentation from "./ContentMaster/AddDetailsDocumentation";
import AddTableValues from "./ContentMaster/AddTableValues";
import AddDynamicTableValues from "./ContentMaster/AddDynamicTableValues";
import EditDynamicTableValues from "./ContentMaster/EditDynamicTableValues";
import EditDetailsDocumentation from "./ContentMaster/EditMaster";
import { useNavigate } from "react-router-dom";
import AddApproval from "./ContentMaster/AddApproval";
import ApprovalTableItem from "./ApprovalTable/ApprovalTable";
import { useStateContext } from "../../context/ContextProvider";
import Formatting from "../../components/Editor/Formatting";
import { UploadFileRounded } from "@mui/icons-material";
import { ButtonBasic } from "../../components/buttons/Buttons";
import {
  T1,
  T1Head,
  T2,
  T2Head,
  T3,
  T3Head,
  T4,
} from "./TableTemplates/TableTemplates";
import TableMain from "./ContentMaster/TableMain";
import IQ1 from "./TableTemplates/iq/IQ1";
import IQ2 from "./TableTemplates/iq/IQ2";
import IQ3 from "./TableTemplates/iq/IQ3";
import IQ4 from "./TableTemplates/iq/IQ4";
import IQ5 from "./TableTemplates/iq/IQ5";
import IQ6 from "./TableTemplates/iq/IQ6";
import IQ7 from "./TableTemplates/iq/IQ7";
import IQ8 from "./TableTemplates/iq/IQ8";
import IQ9 from "./TableTemplates/iq/IQ9";
import IQ10 from "./TableTemplates/iq/IQ10";
import IQ11 from "./TableTemplates/iq/IQ11";
import IQ12 from "./TableTemplates/iq/IQ12";
import IQ13 from "./TableTemplates/iq/IQ13";
import IQ14 from "./TableTemplates/iq/IQ14";
import MFG1 from "./TableTemplates/manufacturing/MFG1";

import EQP1 from "./TableTemplates/EQP/EQP1";
import EQP2 from "./TableTemplates/EQP/EQP2";
import EQP3 from "./TableTemplates/EQP/EQP3";
import EQP4 from "./TableTemplates/EQP/EQP4";
import EQP5 from "./TableTemplates/EQP/EQP5";
import OQ1 from "./TableTemplates/OQ/OQ1";
import OQ2 from "./TableTemplates/OQ/OQ2";
import OQ3 from "./TableTemplates/OQ/OQ3";
import OQ4 from "./TableTemplates/OQ/OQ4";
import OQ5 from "./TableTemplates/OQ/OQ5";
import OQ6 from "./TableTemplates/OQ/OQ6";
import OQ7 from "./TableTemplates/OQ/OQ7";
import OQ8 from "./TableTemplates/OQ/OQ8";
import OQ9 from "./TableTemplates/OQ/OQ9";
import OQ10 from "./TableTemplates/OQ/OQ10";
import OQ11 from "./TableTemplates/OQ/OQ11";
import OQLayout from "../OQ-layouts/OQLayout"; //
import NonConfirmity from "../Non-conformity/NonConfirmity"; //
import { useMaintenanceInfoSeter } from "../../context/MaintenanceContext";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useContentEditCount } from "../../services3/audits/ContentContext";
import Abbreviation from "./TableTemplates/Abbreviation/Abbreviation";
import Reference_Document from "./TableTemplates/Reference_Documents/Reference_Documents";
import Reference_Instruments from "./TableTemplates/Reference_Instruments/Reference_instruments";
import Training from "./TableTemplates/Training/Training";
import AmnealDocCommon from "./TableTemplates/commonTables/amnealDocCommon";
import RemarksCommon from "./TableTemplates/commonTables/remarksCommon";
import Post_Approval from "./TableTemplates/Post_Approval/post_approval";
import Summary_Conclusion from "./TableTemplates/Summary&Conclusion/summary_conclusion";
import Pre_Requisites from "./TableTemplates/Pre_Requisites/pre_requisites";
import Test_Result from "./TableTemplates/Test_Result/test_result";
import Execution_Table_PID from "./TableTemplates/Execution_Table_PID/execution_table_p&id";
import Execution_Table_12_6_5 from "./TableTemplates/ExecutionTables/execution_table_12.6.5";
import Execution_Table_12_5_5 from "./TableTemplates/ExecutionTables/execution_table_12.5.5";
import Execution_Table_12_3_6 from "./TableTemplates/ExecutionTables/execution_table_12.3.6";
import Execution_Table_12_2_5 from "./TableTemplates/ExecutionTables/execution_table_12.2.5";
import Execution_Table_12_1_5 from "./TableTemplates/ExecutionTables/execution_Table_12.1.5";
import Observation_Table_13_2_4 from "./TableTemplates/ObservationTables/observation_table_13.2.4";
import Observation_Table_12_12_5 from "./TableTemplates/ObservationTables/observation_table_12.12.5";
import Observation_Table_12_11_4 from "./TableTemplates/ObservationTables/observation_table_12.11.4";
import Observation_Table_12_10_5 from "./TableTemplates/ObservationTables/observation_table_12.10.5";
import Execution_Table_12_9_5 from "./TableTemplates/ExecutionTables/execution_table_12.9.5";
import Execution_Table_12_8_5 from "./TableTemplates/ExecutionTables/execution_table_12.8.5";
import Observation_Table_13_1_5 from "./TableTemplates/ObservationTables/observation_table_13.1.5";
import Observation_Table_13_5_5 from "./TableTemplates/ObservationTables/observation_table_13.5.5";
import SUMMARY_NON_CONFORMANCE from "./TableTemplates/Summary_Non_Conformace/summary_non_conformance";
import Execution_Table_13_3_5 from "./TableTemplates/ExecutionTables/execution_table_13.3.5";
import Execution_Table_13_4_5 from "./TableTemplates/ExecutionTables/execution_table_13.4.5";
import Execution_Table_13_6_5 from "./TableTemplates/ExecutionTables/execution_table_13.6.5";
import PreApproval from "./TableTemplates/Pre_approval/PreApproval";
import PQ1 from "./TableTemplates/PQ/PQ1";
import PQ2 from "./TableTemplates/PQ/PQ2";
import PQ3 from "./TableTemplates/PQ/PQ3";
import PQ4 from "./TableTemplates/PQ/PQ4";
import PQ5 from "./TableTemplates/PQ/PQ5";
import PostApprovalGilPq from "./TableTemplates/Post_Approval/Post_ApprovalGilPq";

//import filterTableArrayBasedOnTitleForReferenceDocuments from "./TableTemplates/TableFunctions"
//import filterTableArrayBasedOnTitleForTraining from "./TableTemplates/TableFunctions"
const Content = ({ type, docId, mid }) => {
  const [details, setDetails] = useState([]);
  const { contentEditCount, setContentEditCount } = useContentEditCount();
  const [tableDetails, setTableDetails] = useState([]);
  const [dynamicTableDetail, setDynamicTableDetail] = useState();
  const [open, setOpen] = useState(false);
  const [openAddDetails, setOpenAdd] = useState(false);
  const [openTable, setOpenTable] = useState(false);
  const [openDynamicTable, setOpenDynamicTable] = useState(false);
  const [openEditDynamicTable, setOpenEditDynamicTable] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openAppr, setOpenAppr] = useState(false);

  const [customerApprovalTable, setCustomerApprovalTable] = useState([]);
  const [companyApprovalTable, setCompanyApprovalTable] = useState([]);
  const [dataLoading, setDataLoading] = useState(true);
  const today = new Date();
  const [machineName, setMachineName] = useState("");
  const history = useNavigate();
  const [legend, setLegend] = useState(false);
  const [process] = useState([pType, pFType, pInput]);
  const [title] = useState([
    "Process Type",
    "Process Function Type",
    "Process Input Parameter",
  ]);
  const { currentMode, currentColorLight } = useStateContext();
  const [openUpladedTable, setOpenUploadTable] = useState(false);

  const [approvalTable, setApprovalTable] = useState([]);

  const [table1, setTable1] = useState([]);
  const [table2, setTable2] = useState([]);
  const [table3, setTable3] = useState([]);
  const [table4, setTable4] = useState([]);
  const [training, setTraining] = useState([]);
  const [referenceDocument, setReferenceDocument] = useState([]);
  const [referenceInstruments, setReferenceInstruments] = useState([]);
  const [postApproval, setPostApproval] = useState([]);
  const [summaryConclusion, setSummaryConclusion] = useState([]);
  const [preRequisites, setPreRequisites] = useState([]);
  const [testResult, setTestResult] = useState([]);
  const [execution_Table_PID, setExecution_Table_PID] = useState([]);
  const [executionTable_12_1_5, setExecutionTable_12_1_5] = useState([]);
  const [
    executionTable_12_1_5_staticData,
    setExecutionTable_12_1_5_staticData,
  ] = useState([]); //these extra data is avilable in few tables only
  const [executionTable_12_2_5, setExecutionTable_12_2_5] = useState([]);
  const [
    executionTable_12_2_5_staticData,
    setExecutionTable_12_2_5_staticData,
  ] = useState([]); //these extra data is avilable in few tables only
  const [executionTable_12_3_6, setExecutionTable_12_3_6] = useState([]);
  const [
    executionTable_12_3_6_staticData,
    setExecutionTable_12_3_6_staticData,
  ] = useState([]); //these extra data is avilable in few tables only
  const [executionTable_12_5_5, setExecutionTable_12_5_5] = useState([]);
  const [
    executionTable_12_5_5_staticData,
    setExecutionTable_12_5_5_staticData,
  ] = useState([]); //these extra data is avilable in few tables only
  const [executionTable_12_6_5, setExecutionTable_12_6_5] = useState([]);
  const [
    executionTable_12_6_5_staticData,
    setExecutionTable_12_6_5_staticData,
  ] = useState([]); //these extra data is avilable in few tables only
  const [executionTable_12_8_5, setExecutionTable_12_8_5] = useState([]);
  const [
    executionTable_12_8_5_staticData,
    setExecutionTable_12_8_5_staticData,
  ] = useState([]); //these extra data is avilable in few tables only
  const [executionTable_12_9_5, setExecutionTable_12_9_5] = useState([]);
  const [
    executionTable_12_9_5_staticData,
    setExecutionTable_12_9_5_staticData,
  ] = useState([]); //these extra data is avilable in few tables only
  const [executionTable_13_3_5, setExecutionTable_13_3_5] = useState([]);
  const [
    executionTable_13_3_5_staticData,
    setExecutionTable_13_3_5_staticData,
  ] = useState([]);
  const [executionTable_13_4_5, setExecutionTable_13_4_5] = useState([]);
  const [
    executionTable_13_4_5_staticData,
    setExecutionTable_13_4_5_staticData,
  ] = useState([]);
  const [executionTable_13_6_5, setExecutionTable_13_6_5] = useState([]);
  const [
    executionTable_13_6_5_staticData,
    setExecutionTable_13_6_5_staticData,
  ] = useState([]);

  const [observationTable_12_10_5, setObservationTable_12_10_5] = useState([]);
  const [
    observationTable_12_10_5_staticData,
    setObservationTable_12_10_5_staticData,
  ] = useState([]); //these extra data is avilable in few tables only
  const [observationTable_12_11_4, setObservationTable_12_11_4] = useState([]);
  const [
    observationTable_12_11_4_staticData,
    setObservationTable_12_11_4_staticData,
  ] = useState([]); //these extra data is avilable in few tables only
  const [observationTable_12_12_5, setObservationTable_12_12_5] = useState([]);
  const [
    observationTable_12_12_5_staticData,
    setObservationTable_12_12_5_staticData,
  ] = useState([]); //these extra data is avilable in few tables only
  const [observationTable_13_2_4, setObservationTable_13_2_4] = useState([]);
  const [
    observationTable_13_2_4_staticData,
    setObservationTable_13_2_4_staticData,
  ] = useState([]); //these extra data is avilable in few tables only
  const [observationTable_13_1_5, setObservationTable_13_1_5] = useState([]);
  const [
    observationTable_13_1_5_staticData,
    setObservationTable_13_1_5_staticData,
  ] = useState([]);
  const [observationTable_13_5_5, setObservationTable_13_5_5] = useState();
  const [
    observationTable_13_5_5_staticData,
    setObservationTable_13_5_5_staticData,
  ] = useState([]);
  const [summaryNonConformance, setSummaryNonConformance] = useState([]);
  const [
    summaryNonConformance_staticData,
    setSummaryNonConformance_staticData,
  ] = useState([]);

  const [abbreviation, setAbbreviation] = useState([]);

  const [pq1, setPq1] = useState([]);
  const [pq2, setPq2] = useState([]);
  const [pq2_staticData, setPq2_staticData] = useState([]);
  const [pq3, setPq3] = useState([]);
  const [pq3_staticData, setPq3_staticData] = useState([]);
  const [pq4, setPq4] = useState([]);
  const [pq4_staticData, setPq4_staticData] = useState([]);
  const [pq5, setPq5] = useState([]);

  const [iq1, setIq1] = useState([]);
  const [iq2, setIq2] = useState([]);
  const [iq3, setIq3] = useState([]);
  const [iq4, setIq4] = useState([]);
  const [iq5, setIq5] = useState([]);
  const [iq6, setIq6] = useState([]);
  const [iq7, setIq7] = useState([]);
  const [iq8, setIq8] = useState([]);
  const [iq9, setIq9] = useState([]);
  const [iq10, setIq10] = useState([]);
  const [iq11, setIq11] = useState([]);
  const [iq12, setIq12] = useState([]);
  const [iq13, setIq13] = useState([]);
  const [iq14, setIq14] = useState([]);
  const [mfg1, setMfg1] = useState([]);

  const [eqp1, setEQP1] = useState([]);
  const [eqp2, setEQP2] = useState([]);
  const [eqp3, setEQP3] = useState([]);
  const [eqp4, setEQP4] = useState([]);
  const [eqp5, setEQP5] = useState([]);

  const [oq1, setOq1] = useState([]);
  const [oq2, setOq2] = useState([]);
  const [oq3, setOq3] = useState([]);
  const [oq4, setOq4] = useState([]);
  const [oq5, setOq5] = useState([]);
  const [oq6, setOq6] = useState([]);
  const [oq7, setOq7] = useState([]);
  const [oq8, setOq8] = useState([]);
  const [oq9, setOq9] = useState([]);
  const [oq10, setOq10] = useState([]);
  const [oq11, setOq11] = useState([]);
  const [fatsat, setFatsat] = useState([]);
  const [model, setModel] = useState([]);
  // const tableRef = db
  //   .collection(companies)
  //   .doc(companyId_constant)
  //   .collection(type)
  //   .doc(docId);
  const maintenanceInfoSetter = useMaintenanceInfoSeter();
  const [serialNo, setSerialNo] = useState("");
  const [enlarge, setEnlarge] = useState(false);
  const [enlargeValue, setEnlargeValue] = useState("");

  //
  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#161c24" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  //

  const handleFatSeriesData = (fat_series_id) => {
    axios
      .get(`${dbConfig.url}/fatlists/${fat_series_id}`)
      .then((res) => {
        console.log("fatlist:", res?.data);
        setFatsat(res?.data);
      })
      .catch((err) => {
        console.log("fatlist error:", err);
      });
  };
  const getmachinesData = () => {
    axios
      .get(`${dbConfig.url}/machines/${mid}`)
      .then((res) => {
        console.log("machinedetails:", res?.data);
        setSerialNo(res?.data.serialNo);
        setModel(res?.data.title);
      })
      .catch((err) => {
        console.log("fatlist error:", err);
      });
  };
  useEffect(() => {
    //handleFatSeriesData();
    getmachinesData();
    var fatDetails = {};
    // db.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(type)
    // 	.doc(docId)
    // 	.onSnapshot((snap) => {
    // 		const data = snap.data();
    // 		setDetails({
    // 			id: snap.id,
    // 			...data
    // 		});

    axios.get(`${dbConfig.url}/fatdatas`).then((res) => {
      console.log("fatdatas in content.jsx:", res.data);

      var data = res?.data;
      data.sort(function (a, b) {
        return a.index - b.index;
      });
      let fatDataFiltered = data?.filter((fData) => fData?._id === docId); // remove this filter and get it by new API // this call is also in serialisedList.jsx
      setDetails({
        id: fatDataFiltered[0]?._id,
        ...fatDataFiltered[0],
      });
      console.log("fatdatas in content.jsx filtered:", {
        id: fatDataFiltered[0]?._id,
        ...fatDataFiltered[0],
      });

      fatDetails = {
        // id: snap.id,
        // ...data
        id: fatDataFiltered?._id,
        ...fatDataFiltered,
      };
      handleFatSeriesData(fatDataFiltered[0]?.fid); // temprory solution// look diffrent way to fetch fatseries data here

      // let tableCollectionList = data?.temp?.map((dataTemp) => { // curently no use
      // 	return dataTemp?.collection
      // })

      //t1
      // tableRef
      // 	.collection("table1")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = [];
      // 					temp.push(dataM['serial_no']); // order is important
      // 					temp.push(dataM['tag_no']); //
      // 					temp.push(dataM['desc']); //
      // 					temp.push(dataM['make_vendor']); //
      // 					temp.push(dataM['details']); //
      // 					temp.push(dataM['size']); //
      // 					temp.push(dataM['documents']); //
      // 					temp.push(dataM['result']); //
      // 					temp.push(dataM['url']); //
      // 					temp.push(dataM['id']); ////
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //

      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setTable1(tablesArray);
      // 	});
      // //t2
      // tableRef
      // 	.collection("table2")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = [];
      // 					temp.push(dataM['serial_no']); // order is important
      // 					temp.push(dataM['tag_no']); //
      // 					temp.push(dataM['desc']); //
      // 					temp.push(dataM['type_size']); //
      // 					temp.push(dataM['documents']); //
      // 					temp.push(dataM['result']); //
      // 					temp.push(dataM['url']); //
      // 					temp.push(dataM['id']); ////
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //

      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setTable2(tablesArray);
      // 	});
      // //t3
      // tableRef
      // 	.collection("table3")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = [];
      // 					temp.push(dataM['serial_no']); // order is important
      // 					temp.push(dataM['drawing_title']); //
      // 					temp.push(dataM['drawing_no']); //
      // 					temp.push(dataM['compliance']); //
      // 					temp.push(dataM['url']); //
      // 					temp.push(dataM['id']); ////
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']);

      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setTable3(tablesArray);
      // 	});

      // //t4
      // tableRef
      // 	.collection("table4")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = [];
      // 					temp.push(dataM['serial_no']); // order is important
      // 					temp.push(dataM['tag_no']); //
      // 					temp.push(dataM['desc']); //
      // 					temp.push(dataM['make_vendor']); //
      // 					temp.push(dataM['details']); //
      // 					temp.push(dataM['doc_avail']); //
      // 					temp.push(dataM['sr_avail']); //
      // 					temp.push(dataM['type']); // document type
      // 					temp.push(dataM['result']);
      // 					temp.push(dataM['url']); //
      // 					temp.push(dataM['id']); ////
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //

      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setTable4(tablesArray);
      // 	});

      // IQ1
      //function
      // function tableDataGetter(tableCollection, tableTitle) {
      //    tableRef.collection(tableTitle).onSnapshot((snap)=>{
      // 	return snap;
      //    })
      // }

      // Abbreviation
      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Abbreviation",
        })
        .then((res) => {
          console.log("Abbrebiation data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData?.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["abbreviation"]); //
              temp.push(dataM["expanded_definition"]); //
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setAbbreviation(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ1 data get:", e);
        });
      // training
      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Training",
        })
        .then((res) => {
          console.log("Training data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData?.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["name"]);
              temp.push(dataM["company_name"]); //
              temp.push(dataM["designation"]); //
              temp.push(dataM["department"]); //
              temp.push(dataM["sign_date"]); //
              temp.push(dataM["table_title"]);
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setTraining(tablesArray);
        })
        .catch((e) => {
          console.log("error Training data get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Reference_Documents",
        })
        .then((res) => {
          console.log("Reference_Documents:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData?.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["document_name"]); //
              temp.push(dataM["document_no"]); //
              temp.push(dataM["rev_no"]); //
              temp.push(dataM["table_title"]);

              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setReferenceDocument(tablesArray);
        })
        .catch((e) => {
          console.log("error Reference_Document data get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Reference_Instruments",
        })
        .then((res) => {
          console.log("Reference_Instruments get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData?.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["measuring_device"]); //
              temp.push(dataM["certificate_no"]); //
              temp.push(dataM["calibration_done_on"]); //
              temp.push(dataM["calibration_due_on"]); //
              temp.push(dataM["table_title"]);
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setReferenceInstruments(tablesArray);
        })
        .catch((e) => {
          console.log("error Reference_Instruments data get:", e);
        });
      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Post_Approval",
        })
        .then((res) => {
          console.log("Post Approval get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["responsibility"]); // 0 order is important
              temp.push(dataM["name"]); //
              temp.push(dataM["department"]); //
              temp.push(dataM["signature"]); //
              temp.push(dataM["date"]); //
              temp.push(dataM["table_title"]);
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setPostApproval(tablesArray);
        })
        .catch((e) => {
          console.log("error Post Approval data get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Summary_Conclusion",
        })
        .then((res) => {
          console.log("Summary & Conclusion get:", res?.data);
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["installation_report_summary"]); //
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setSummaryConclusion(tablesArray);
        })
        .catch((e) => {
          console.log("error Summary & Conclusion data get:", e);
        });
      // for pre requisite
      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Pre_Requisites",
        })
        .then((res) => {
          console.log("Pre Requisitesget:", res?.data);
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["pre_requisites"]); //
              temp.push(dataM["availability"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setPreRequisites(tablesArray);
        })
        .catch((e) => {
          console.log("error Summary & Pre Requisites getget:", e);
        });

      // for execution table p & id

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Execution_Table_PID",
        })
        .then((res) => {
          console.log("Execution_Table_PID:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["test_criteria"]); // 0 order is important
              temp.push(dataM["method_of_verification"]); //
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setExecution_Table_PID(tablesArray);
        })
        .catch((e) => {
          console.log("error Execution_Table_PID get:", e);
        });
      // for test result

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Test_Result",
        })
        .then((res) => {
          console.log("Test_Result:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["test_criteria"]); // 0 order is important
              temp.push(dataM["method_of_verification"]); //
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setTestResult(tablesArray);
        })
        .catch((e) => {
          console.log("error Test_Result get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Execution_Table_12_1_5",
        })
        .then((res) => {
          console.log("Execution_Table_12_1_5:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["standard_ins_reading"]); // 0 order is important
              temp.push(dataM["indicator_reading"]); //
              temp.push(dataM["measuring_error"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setExecutionTable_12_1_5(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res222:", res2?.data);
              setExecutionTable_12_1_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error Execution_Table_12_1_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Execution_Table_12_1_5 get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Execution_Table_12_2_5",
        })
        .then((res) => {
          console.log("Execution_Table_12_2_5:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["set_val"]); // 0 order is important
              temp.push(dataM["indicator_reading"]); //
              temp.push(dataM["tachometer_reading"]);
              temp.push(dataM["measuring_error"]);
              temp.push(dataM["max_allowable_err"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setExecutionTable_12_2_5(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              //console.log("res 12.2.5:", res2?.data)
              setExecutionTable_12_2_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error Execution_Table_12_2_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Execution_Table_12_2_5 get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Execution_Table_12_3_6",
        })
        .then((res) => {
          console.log("Execution_Table_12_3_6:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["calibration_point"]); // 0 order is important
              temp.push(dataM["reference_reading"]); //
              temp.push(dataM["indicator_reading"]);
              temp.push(dataM["measuring_error"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setExecutionTable_12_3_6(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              //console.log("res 12.3.5:", res2?.data)
              setExecutionTable_12_3_6_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error Execution_Table_12_3_6_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Execution_Table_12_3_6 get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Execution_Table_12_5_5",
        })
        .then((res) => {
          console.log("Execution_Table_12_5_5 :", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["test_criteria"]); // 0 order is important
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setExecutionTable_12_5_5(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              //console.log("res 12.5.5:", res2?.data)
              setExecutionTable_12_5_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error Execution_Table_12_5_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Execution_Table_12_5_5 get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Execution_Table_12_6_5",
        })
        .then((res) => {
          console.log("Execution_Table_12_6_5:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["from"]); // 0 order is important
              temp.push(dataM["to"]); //
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setExecutionTable_12_6_5(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res 12.6.5:", res2?.data);
              setExecutionTable_12_6_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error Execution_Table_12_6_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Execution_Table_12_6_5 get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Execution_Table_12_8_5",
        })
        .then((res) => {
          console.log("Execution_Table_12_8_5:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["pass_details"]); // 0 order is important
              temp.push(dataM["settalable_range"]); //
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setExecutionTable_12_8_5(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res 12.8.5:", res2?.data);
              setExecutionTable_12_8_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error Execution_Table_12_8_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Execution_Table_12_8_5 get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Execution_Table_12_9_5",
        })
        .then((res) => {
          console.log("Execution_Table_12_9_5:", res?.data);
          // let resData = res?.data
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["event_audit_trail"]); //
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setExecutionTable_12_9_5(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res 12.9.5:", res2?.data);
              setExecutionTable_12_9_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error Execution_Table_12_9_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Execution_Table_12_9_5 get:", e);
        });

      // Execution_Table_13_3_5

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Execution_Table_13_3_5",
        })
        .then((res) => {
          console.log("Execution_Table_13_3_5:", res?.data);
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["sip_sequence_name"]); //
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setExecutionTable_13_3_5(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res 13.3.5:", res2?.data);
              setExecutionTable_13_3_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error Execution_Table_13_3_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Execution_Table_13_3_5 get:", e);
        });

      //Execution_Table_13_4_5
      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Execution_Table_13_4_5",
        })
        .then((res) => {
          console.log("Execution_Table_13_4_5:", res?.data);
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["pressure_hold_test"]); //
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setExecutionTable_13_4_5(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res 13.4.5:", res2?.data);
              setExecutionTable_13_4_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error Execution_Table_13_4_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Execution_Table_13_4_5 get:", e);
        });

      // Execution_Table_13_6_5

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Execution_Table_13_6_5",
        })
        .then((res) => {
          console.log("Execution_Table_13_6_5:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["requirement"]); //
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setExecutionTable_13_6_5(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res 13.6.5:", res2?.data);
              setExecutionTable_13_6_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error Execution_Table_13_6_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Execution_Table_13_6_5 get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Observation_Table_12_10_5",
        })
        .then((res) => {
          console.log("Observation_Table_12_10_5:", res?.data);
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["condition"]); //
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["actual"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setObservationTable_12_10_5(tablesArray);

          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res222:", res2?.data);
              setObservationTable_12_10_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error observationTable_12_10_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Observation_Table_12_10_5 get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Observation_Table_12_11_4",
        })
        .then((res) => {
          console.log("Observation_Table_12_11_4:", res?.data);
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["plc"]); //
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["result"]);
              temp.push(dataM["table_title"]); // this should be put in last index

              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setObservationTable_12_11_4(tablesArray);

          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res222:", res2?.data);
              setObservationTable_12_11_4_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error observationTable_12_11_4_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Observation_Table_12_11_4 get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Observation_Table_12_12_5",
        })
        .then((res) => {
          console.log("Observation_Table_12_12_5", res?.data);
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];

              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["plc"]); //
              temp.push(dataM["condition"]);
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["expected_result"]);
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index

              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setObservationTable_12_12_5(tablesArray);

          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res222:", res2?.data);
              setObservationTable_12_12_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error observationTable_12_12_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Observation_Table_12_12_5 get:", e);
        });

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Observation_Table_13_2_4",
        })
        .then((res) => {
          console.log("Observation_Table_13_2_4", res?.data);
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["cip_sequence"]); //
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index

              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setObservationTable_13_2_4(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res222:", res2?.data);
              setObservationTable_13_2_4_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error observationTable_13_2_4_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Observation_Table_13_2_4 get:", e);
        });

      // Observation_Table_13_1_5
      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Observation_Table_13_1_5",
        })
        .then((res) => {
          console.log("Observation_Table_13_1_5", res?.data);
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["pressure_hold_test"]); //
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index

              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setObservationTable_13_1_5(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res222:", res2?.data);
              setObservationTable_13_1_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error observationTable_13_1_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Observation_Table_13_1_5 get:", e);
        });

      //Observation_Table_13_5_5

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "Observation_Table_13_5_5",
        })
        .then((res) => {
          console.log("Observation_Table_13_5_5", res?.data);
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["process_sequence"]); //
              temp.push(dataM["obserbation"]);
              temp.push(dataM["table_title"]); // this should be put in last index

              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setObservationTable_13_5_5(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res222:", res2?.data);
              setObservationTable_13_5_5_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error observationTable_13_5_5_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error Observation_Table_13_5_5 get:", e);
        });

      // SUMMARY_NON_CONFORMANCE

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "SUMMARY_NON_CONFORMANCE",
        })
        .then((res) => {
          console.log("SUMMARY_NON_CONFORMANCE", res?.data);
          let resData = res?.data?.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData;
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["name_of_test"]); //
              temp.push(dataM["ncr_no"]);
              temp.push(dataM["status"]);
              temp.push(dataM["table_title"]); // this should be put in last index

              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setSummaryNonConformance(tablesArray);
          //
          axios
            .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
              fat_id: docId,
              table_title: res?.data[0]["table_title"],
            })
            .then((res2) => {
              console.log("res222:", res2?.data);
              setSummaryNonConformance_staticData(res2?.data);
            })
            .catch((e) => {
              console.log("error SummaryNonConformance_staticData get:", e);
            });
          //
        })
        .catch((e) => {
          console.log("error SummaryNonConformance_staticData get:", e);
        });
      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ1",
        })
        .then((res) => {
          console.log("IQ1 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData?.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // 0 order is important
              temp.push(dataM["desc"]); //
              temp.push(dataM["make_vendor"]); //
              temp.push(dataM["type"]); //
              temp.push(dataM["size"]); //
              temp.push(dataM["documents"]); //
              temp.push(dataM["result"]); //
              temp.push(dataM["url"]); // 7
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq1(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ1 data get:", e);
        });

      // IQ2

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ2",
        })
        .then((res) => {
          console.log("IQ2 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData?.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // order is important
              temp.push(dataM["tag_no"]);
              temp.push(dataM["desc"]);
              temp.push(dataM["make_vendor"]);
              temp.push(dataM["type"]);
              temp.push(dataM["size"]);
              temp.push(dataM["documents"]);
              temp.push(dataM["result"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]);
              temp.push(dataM["table_title"]);
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq2(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ2 data get:", e);
        });

      // IQ3

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ3",
        })
        .then((res) => {
          console.log("IQ3 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData?.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted?.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // order is important
              temp.push(dataM["tag_no"]); //
              temp.push(dataM["desc"]); //
              temp.push(dataM["make_vendor"]); //
              temp.push(dataM["model"]); //
              temp.push(dataM["type"]); //
              temp.push(dataM["size"]); //
              temp.push(dataM["documents"]); //
              temp.push(dataM["result"]); //
              temp.push(dataM["url"]); //
              temp.push(dataM["id"]); //
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq3(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ3 data get:", e);
        });

      // IQ4

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ4",
        })
        .then((res) => {
          console.log("IQ4 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // order is important
              temp.push(dataM["tag_no"]); //
              temp.push(dataM["desc"]); //
              temp.push(dataM["make_vendor"]); //
              temp.push(dataM["model"]); //
              temp.push(dataM["type"]); //
              temp.push(dataM["rating"]); // IQ3:Size
              temp.push(dataM["documents"]); //
              temp.push(dataM["result"]); //
              temp.push(dataM["url"]); //
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq4(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ4 data get:", e);
        });

      // IQ5

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ5",
        })
        .then((res) => {
          console.log("IQ5 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // order is important
              temp.push(dataM["desc"]); //
              temp.push(dataM["make_vendor"]); //
              temp.push(dataM["model"]); //
              temp.push(dataM["type"]); //
              temp.push(dataM["rating"]); // IQ3:Size
              temp.push(dataM["documents"]); //
              temp.push(dataM["result"]); //
              temp.push(dataM["url"]); //
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq5(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ5 data get:", e);
        });

      // IQ6

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ6",
        })
        .then((res) => {
          console.log("IQ6 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // order is important
              temp.push(dataM["tag_no"]); //
              temp.push(dataM["desc"]); //
              temp.push(dataM["make_vendor"]); //
              temp.push(dataM["model"]); //
              temp.push(dataM["calibration_certificate_number"]); //
              temp.push(dataM["result"]); //
              temp.push(dataM["url"]); //
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq6(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ6 data get:", e);
        });

      // IQ7

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ7",
        })
        .then((res) => {
          console.log("IQ7 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // order is important
              temp.push(dataM["desc"]); //
              temp.push(dataM["module_type"]); //
              temp.push(dataM["position"]); //
              temp.push(dataM["application"]); //
              temp.push(dataM["calibration_certificate_number"]); //
              temp.push(dataM["result"]); //
              temp.push(dataM["url"]); //
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq7(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ7 data get:", e);
        });

      // IQ8

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ8",
        })
        .then((res) => {
          console.log("IQ8 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // order is important
              temp.push(dataM["tag_no"]); //
              temp.push(dataM["desc"]); //
              temp.push(dataM["make_vendor"]); //
              temp.push(dataM["model"]); //
              temp.push(dataM["test_report_no"]); //
              //temp.push(dataM['calibration_certificate_number']); //
              temp.push(dataM["result"]); //
              temp.push(dataM["url"]); //
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq8(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ8 data get:", e);
        });

      // IQ9

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ9",
        })
        .then((res) => {
          console.log("IQ9 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // order is important
              temp.push(dataM["tag_no"]); //
              temp.push(dataM["desc"]); //
              temp.push(dataM["make_vendor"]); //
              temp.push(dataM["model"]); //
              temp.push(dataM["type"]); //
              temp.push(dataM["calibration_certificate_number"]); //
              temp.push(dataM["result"]); //
              temp.push(dataM["url"]); //
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq9(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ9 data get:", e);
        });

      // IQ10

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ10",
        })
        .then((res) => {
          console.log("IQ10 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["software_name"]); // order is important
              temp.push(dataM["make"]); //
              temp.push(dataM["version"]); //
              temp.push(dataM["application"]); //
              temp.push(dataM["remarks"]); //
              temp.push(dataM["result"]); //
              temp.push(dataM["url"]); //
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq10(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ10 data get:", e);
        });

      // IQ11

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ11",
        })
        .then((res) => {
          console.log("IQ11 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]); // order is important
              temp.push(dataM["tag_no"]); //
              temp.push(dataM["desc"]); //
              temp.push(dataM["make"]); //
              temp.push(dataM["model"]); //
              temp.push(dataM["range"]); //
              temp.push(dataM["documents"]); //
              temp.push(dataM["result"]); //
              temp.push(dataM["url"]); //
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq11(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ11 data get:", e);
        });

      // IQ12

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ12",
        })
        .then((res) => {
          console.log("IQ12 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["drawing_title"]);
              temp.push(dataM["drawing_no"]);
              temp.push(dataM["compliance"]);
              temp.push(dataM["url"]); //
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq12(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ12 data get:", e);
        });

      // IQ13

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ13",
        })
        .then((res) => {
          console.log("IQ13 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["s_no"]);
              temp.push(dataM["tag_no"]);
              temp.push(dataM["desc"]);
              temp.push(dataM["model_vendor"]);
              temp.push(dataM["model_s_no"]);
              temp.push(dataM["typeTable"]);
              temp.push(dataM["size"]);
              temp.push(dataM["docs"]);
              temp.push(dataM["result"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]);
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq13(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ13 data get:", e);
        });

      // IQ14

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "IQ14",
        })
        .then((res) => {
          console.log("IQ14 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["s_no"]);
              temp.push(dataM["desc"]);
              temp.push(dataM["model_vendor"]);
              temp.push(dataM["model_s_no"]);
              temp.push(dataM["typeTable"]);
              temp.push(dataM["docs"]);
              temp.push(dataM["result"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]);
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setIq14(tablesArray);
        })
        .catch((e) => {
          console.log("error IQ14 data get:", e);
        });

      // MFG1
      // tableRef
      // 	.collection("tableMFG1")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = [];
      // 					temp.push(dataM['desc']); // order is important
      // 					temp.push(dataM['document_number']); //
      // 					temp.push(dataM['yes_no']); //
      // 					temp.push(dataM['url']); //
      // 					temp.push(dataM['id']); ////
      // 					temp.push(dataM['index']); //// newly added for pdf page
      // 					temp.push(dataM['table_title']); //

      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setMfg1(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "MFG1",
        })
        .then((res) => {
          console.log("MFG1 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["desc"]); // order is important
              temp.push(dataM["document_number"]); //
              temp.push(dataM["yes_no"]); //
              temp.push(dataM["url"]); //
              temp.push(dataM["id"]); ////
              temp.push(dataM["index"]); //// newly added for pdf page
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setMfg1(tablesArray);
        })
        .catch((e) => {
          console.log("error MFG1 data get:", e);
        });

      // EQP1
      // tableRef
      // 	.collection("tableEQP1")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			data.forEach(data => console.log(data, titleData))
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['desc'])
      // 					temp.push(dataM['protocol_number'])
      // 					temp.push(dataM['compliance'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setEQP1(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "EQP1",
        })
        .then((res) => {
          console.log("EQP1 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["desc"]);
              temp.push(dataM["protocol_number"]);
              temp.push(dataM["compliance"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setEQP1(tablesArray);
        })
        .catch((e) => {
          console.log("error EQP1 data get:", e);
        });

      // EQP2
      // tableRef
      // 	.collection("tableEQP2")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['desc'])
      // 					temp.push(dataM['page_number'])
      // 					temp.push(dataM['compliance'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id'])
      // 					temp.push(dataM['index']);//
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setEQP2(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "EQP2",
        })
        .then((res) => {
          console.log("EQP1 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["desc"]);
              temp.push(dataM["page_number"]);
              temp.push(dataM["compliance"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setEQP2(tablesArray);
        })
        .catch((e) => {
          console.log("error EQP2 data get:", e);
        });

      // EQP3
      // tableRef
      // 	.collection("tableEQP3")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['doc'])
      // 					temp.push(dataM['test_report_number'])
      // 					temp.push(dataM['compliance'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setEQP3(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "EQP3",
        })
        .then((res) => {
          console.log("EQP3 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["doc"]);
              temp.push(dataM["test_report_number"]);
              temp.push(dataM["compliance"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setEQP3(tablesArray);
        })
        .catch((e) => {
          console.log("error EQP3 data get:", e);
        });

      //EQP4
      // tableRef
      // 	.collection("tableEQP4")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['s_no'])
      // 					temp.push(dataM['qualification_document_name'])
      // 					temp.push(dataM['page_number'])
      // 					temp.push(dataM['nature_of_deviation'])
      // 					temp.push(dataM['major'])
      // 					temp.push(dataM['minor'])
      // 					temp.push(dataM['action_taken'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setEQP4(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "EQP4",
        })
        .then((res) => {
          console.log("EQP4 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["s_no"]);
              temp.push(dataM["qualification_document_name"]);
              temp.push(dataM["page_number"]);
              temp.push(dataM["nature_of_deviation"]);
              temp.push(dataM["major"]);
              temp.push(dataM["minor"]);
              temp.push(dataM["action_taken"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setEQP4(tablesArray);
        })
        .catch((e) => {
          console.log("error EQP4 data get:", e);
        });

      // EQP5
      // tableRef
      // 	.collection("tableEQP5")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['comapnyName'])
      // 					temp.push(dataM['name'])
      // 					temp.push(dataM['signature'])
      // 					temp.push(dataM['date'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setEQP5(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "EQP5",
        })
        .then((res) => {
          console.log("EQP5 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["comapnyName"]);
              temp.push(dataM["name"]);
              temp.push(dataM["signature"]);
              temp.push(dataM["date"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setEQP5(tablesArray);
        })
        .catch((e) => {
          console.log("error EQP5 data get:", e);
        });

      // OQ1
      // tableRef
      // 	.collection("tableOQ1")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['serial_no'])
      // 					temp.push(dataM['desc'])
      // 					temp.push(dataM['desc_sec'])
      // 					temp.push(dataM['mod_number'])
      // 					temp.push(dataM['ch_number'])
      // 					temp.push(dataM['signal'])
      // 					temp.push(dataM['result'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setOq1(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "OQ1",
        })
        .then((res) => {
          console.log("OQ1 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]);
              temp.push(dataM["desc"]);
              temp.push(dataM["desc_sec"]);
              temp.push(dataM["mod_number"]);
              temp.push(dataM["ch_number"]);
              temp.push(dataM["signal"]);
              temp.push(dataM["result"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setOq1(tablesArray);
        })
        .catch((e) => {
          console.log("error OQ1 data get:", e);
        });

      // OQ2
      // tableRef
      // 	.collection("tableOQ2")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['serial_no'])
      // 					temp.push(dataM['desc'])
      // 					temp.push(dataM['performance_parameters'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setOq2(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "OQ2",
        })
        .then((res) => {
          console.log("OQ2 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]);
              temp.push(dataM["desc"]);
              temp.push(dataM["desc_sec"]);
              temp.push(dataM["mod_number"]);
              temp.push(dataM["ch_number"]);
              temp.push(dataM["signal"]);
              temp.push(dataM["result"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setOq2(tablesArray);
        })
        .catch((e) => {
          console.log("error OQ2 data get:", e);
        });

      // OQ3
      // tableRef
      // 	.collection("tableOQ3")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['serial_no'])
      // 					temp.push(dataM['desc'])
      // 					temp.push(dataM['required'])
      // 					temp.push(dataM['actuals'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setOq3(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "OQ3",
        })
        .then((res) => {
          console.log("OQ3 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["serial_no"]);
              temp.push(dataM["desc"]);
              temp.push(dataM["required"]);
              temp.push(dataM["actuals"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setOq3(tablesArray);
        })
        .catch((e) => {
          console.log("error OQ3 data get:", e);
        });

      // OQ4
      // tableRef
      // 	.collection("tableOQ4")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['desc'])
      // 					temp.push(dataM['general_condition'])
      // 					temp.push(dataM['actual_condition'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setOq4(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "OQ4",
        })
        .then((res) => {
          console.log("OQ4 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["desc"]);
              temp.push(dataM["general_condition"]);
              temp.push(dataM["actual_condition"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setOq4(tablesArray);
        })
        .catch((e) => {
          console.log("error OQ4 data get:", e);
        });

      // OQ5
      // tableRef
      // 	.collection("tableOQ5")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['desc'])
      // 					temp.push(dataM['acceptance_criteria'])
      // 					temp.push(dataM['observation'])
      // 					temp.push(dataM['pass_fail'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setOq5(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "OQ5",
        })
        .then((res) => {
          console.log("OQ5 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["desc"]);
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["observation"]);
              temp.push(dataM["pass_fail"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setOq5(tablesArray);
        })
        .catch((e) => {
          console.log("error OQ5 data get:", e);
        });

      // OQ6
      // tableRef
      // 	.collection("tableOQ6")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['check_point'])
      // 					temp.push(dataM['acceptance_criteria'])
      // 					temp.push(dataM['observation'])
      // 					temp.push(dataM['pass_fail'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setOq6(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "OQ6",
        })
        .then((res) => {
          console.log("OQ6 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["desc"]);
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["observation"]);
              temp.push(dataM["pass_fail"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setOq6(tablesArray);
        })
        .catch((e) => {
          console.log("error OQ6 data get:", e);
        });

      // OQ7
      // tableRef
      // 	.collection("tableOQ7")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['check_point'])
      // 					temp.push(dataM['acceptance_criteria'])
      // 					temp.push(dataM['min'])
      // 					temp.push(dataM['max'])
      // 					temp.push(dataM['avg'])
      // 					temp.push(dataM['pass_fail'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setOq7(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "OQ7",
        })
        .then((res) => {
          console.log("OQ7 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["check_point"]);
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["min"]);
              temp.push(dataM["max"]);
              temp.push(dataM["avg"]);
              temp.push(dataM["pass_fail"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setOq7(tablesArray);
        })
        .catch((e) => {
          console.log("error OQ7 data get:", e);
        });

      // OQ8
      // tableRef
      // 	.collection("tableOQ8")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				let dataSorted = filterdData.sort((a, b) => a['serial_no'] - b['serial_no']);
      // 				const tempArr2d = [];
      // 				dataSorted.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['temprature_set_point'])
      // 					temp.push(dataM['acceptance_criteria'])
      // 					temp.push(dataM['average_time'])
      // 					temp.push(dataM['variation'])
      // 					temp.push(dataM['status']) // ok/ Not ok
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setOq8(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "OQ8",
        })
        .then((res) => {
          console.log("OQ8 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["temprature_set_point"]);
              temp.push(dataM["acceptance_criteria"]);
              temp.push(dataM["average_time"]);
              temp.push(dataM["variation"]);
              temp.push(dataM["status"]); // ok/ Not ok
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setOq8(tablesArray);
        })
        .catch((e) => {
          console.log("error OQ8 data get:", e);
        });

      // OQ9
      // tableRef
      // 	.collection("tableOQ9")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				const tempArr2d = [];
      // 				filterdData.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['selfSP'])
      // 					temp.push(dataM['acceptance'])
      // 					temp.push(dataM['shelfNo'])
      // 					temp.push(dataM['shelfAverage'])
      // 					temp.push(dataM['allShelvesAverage']) // ok/ Not ok
      // 					temp.push(dataM['deviation']);
      // 					temp.push(dataM['passFail']); //
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setOq9(tablesArray);
      // 	});
      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "OQ9",
        })
        .then((res) => {
          console.log("OQ9 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["selfSP"]);
              temp.push(dataM["acceptance"]);
              temp.push(dataM["shelfNo"]);
              temp.push(dataM["shelfAverage"]);
              temp.push(dataM["allShelvesAverage"]); // ok/ Not ok
              temp.push(dataM["deviation"]);
              temp.push(dataM["passFail"]); //
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setOq9(tablesArray);
        })
        .catch((e) => {
          console.log("error OQ9 data get:", e);
        });

      // OQ10
      // tableRef
      // 	.collection("tableOQ10")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				const tempArr2d = [];
      // 				filterdData.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['checkPoint'])
      // 					temp.push(dataM['results'])
      // 					temp.push(dataM['confirm'])
      // 					temp.push(dataM['deviation'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setOq10(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "OQ10",
        })
        .then((res) => {
          console.log("OQ10 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["checkPoint"]);
              temp.push(dataM["results"]);
              temp.push(dataM["confirm"]);
              temp.push(dataM["deviation"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setOq10(tablesArray);
        })
        .catch((e) => {
          console.log("error OQ10 data get:", e);
        });

      // OQ11
      // tableRef
      // 	.collection("tableOQ11")  // table
      // 	.onSnapshot((snap) => {
      // 		const data = firebaseLooper(snap);
      // 		const tablesArray = []; // array of tables
      // 		fatDetails?.table_list?.map((titleData) => {
      // 			let filterdData = data?.filter((fdata) => fdata?.table_title === titleData?.table_title);
      // 			if (filterdData?.length > 0) {
      // 				const tempArr2d = [];
      // 				filterdData.map((dataM) => {
      // 					const temp = []
      // 					temp.push(dataM['recipe'])
      // 					temp.push(dataM['range'])
      // 					temp.push(dataM['value'])
      // 					temp.push(dataM['acceptanceCriteria'])
      // 					temp.push(dataM['chamberLead'])
      // 					temp.push(dataM['confirm'])
      // 					temp.push(dataM['url']);
      // 					temp.push(dataM['id']);
      // 					temp.push(dataM['index']); //
      // 					temp.push(dataM['table_title']); //
      // 					tempArr2d.push(temp)
      // 				})
      // 				tablesArray.push(tempArr2d);
      // 			}
      // 		})
      // 		setOq11(tablesArray);
      // 	});

      axios
        .post(`${dbConfig.url}/generalTable/findsome`, {
          fat_id: docId,
          table_type: "OQ11",
        })
        .then((res) => {
          console.log("OQ11 data get:", res?.data);
          let resData = res?.data;
          const tablesArray = []; // array of tables
          if (res?.data?.length > 0) {
            let dataSorted = resData.sort(
              (a, b) => a["serial_no"] - b["serial_no"],
            );
            const tempArr2d = [];
            dataSorted.map((dataM) => {
              const temp = [];
              temp.push(dataM["recipe"]);
              temp.push(dataM["range"]);
              temp.push(dataM["value"]);
              temp.push(dataM["acceptanceCriteria"]);
              temp.push(dataM["chamberLead"]);
              temp.push(dataM["confirm"]);
              temp.push(dataM["url"]);
              temp.push(dataM["id"]);
              temp.push(dataM["index"]); //
              temp.push(dataM["table_title"]); //
              tempArr2d.push(temp);
            });
            tablesArray.push(tempArr2d);
          }
          setOq11(tablesArray);
        })
        .catch((e) => {
          console.log("error OQ11 data get:", e);
        });

      // tabletype data end

      //   db.collection(companies)
      //     .doc(companyId_constant)
      //     .collection(machines)
      //     .doc(mid)
      //     .onSnapshot((snap) => {
      //       const data = snap.data();
      //       setMachineName(data?.title);
      //     });
      //   db.collection(companies)
      //     .doc(companyId_constant)
      //     .collection(type)
      //     .doc(docId)
      //     .collection("table") // table
      //     .onSnapshot((snap) => {
      //       const data = firebaseLooper(snap);
      //       data.sort(function (a, b) {
      //         return a.index - b.index;
      //       });
      //       setTableDetails(data);
      //     });
      //   db.collection(companies)
      //     .doc(companyId_constant)
      //     .collection(type)
      //     .doc(docId)
      //     .collection("approvalTable")
      //     .onSnapshot((snap) => {
      //       const data = firebaseLooper(snap);
      //       data.sort(function (a, b) {
      //         return a.index - b.index;
      //       });
      //       const customerData = data.filter(
      //         (elem) => elem.approverType === "customer"
      //       );
      //       const companyData = data.filter(
      //         (elem) => elem.approverType === "company"
      //       );
      //       setApprovalTable(data);
      //       setCustomerApprovalTable(customerData);
      //       setCompanyApprovalTable(companyData);
      //     });
      //   db.collection(companies)
      //     .doc(companyId_constant)
      //     .collection(type)
      //     .doc(docId)
      //     .collection("dynamic_table")
      //     .onSnapshot((snap) => {
      //       const data = firebaseLooper(snap);
      //       setDynamicTableDetail(data);
      //     });
      //   setDataLoading(false);
      // });
      // })
      // .catch((e)=>{
      // 	console.log("error fatData:", e)
      // })
    }, []);

    //PQ start
    axios
      .post(`${dbConfig.url}/generalTable/findsome`, {
        fat_id: docId,
        table_type: "PQ1",
      })
      .then((res) => {
        console.log("PQ1 data get:", res?.data);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"],
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            temp.push(dataM["serial_no"]);
            temp.push(dataM["speed_of_machine"]);
            temp.push(dataM["speed_in_rpm"]);
            //temp.push(dataM["url"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPq1(tablesArray);
      })
      .catch((e) => {
        console.log("error PQ1 data get:", e);
      });
    //PQ2
    axios
      .post(`${dbConfig.url}/generalTable/findsome`, {
        fat_id: docId,
        table_type: "PQ2",
      })
      .then((res) => {
        console.log("PQ2 data get:", res?.data);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"], //
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            // temp.push(dataM["serial_no"]);
            temp.push(dataM["parameter"]);
            temp.push(dataM["acceptance_criteria"]);
            temp.push(dataM["initial"]);
            temp.push(dataM["min10"]);
            temp.push(dataM["min20"]);
            temp.push(dataM["min30"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPq2(tablesArray);
        //
        axios
          .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
            fat_id: docId,
            // table_title: res?.data[0]["table_title"],
          })
          .then((res2) => {
            console.log("res222:", res2?.data);
            setPq2_staticData(res2?.data);
          })
          .catch((e) => {
            console.log("error Pq2_staticData get:", e);
          });
        //
      })
      .catch((e) => {
        console.log("error PQ2 data get:", e);
      });

    //PQ3
    axios
      .post(`${dbConfig.url}/generalTable/findsome`, {
        fat_id: docId,
        table_type: "PQ3",
      })
      .then((res) => {
        console.log("PQ3 data get:", res?.data);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"], //
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            // temp.push(dataM["serial_no"]);
            temp.push(dataM["parameter"]);
            temp.push(dataM["specification"]);
            temp.push(dataM["rpm10"]);
            temp.push(dataM["rpm20"]);
            temp.push(dataM["rpm40"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPq3(tablesArray);
        //
        axios
          .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
            fat_id: docId,
            // table_title: res?.data[0]["table_title"],
          })
          .then((res2) => {
            console.log("res222:", res2?.data);
            setPq3_staticData(res2?.data);
          })
          .catch((e) => {
            console.log("error Pq3_staticData get:", e);
          });
        //
      })
      .catch((e) => {
        console.log("error PQ3 data get:", e);
      });

    //PQ4
    axios
      .post(`${dbConfig.url}/generalTable/findsome`, {
        fat_id: docId,
        table_type: "PQ4",
      })
      .then((res) => {
        console.log("PQ4 data get:", res?.data);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"], //
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            temp.push(dataM["serial_no"]); // it has serial no
            temp.push(dataM["parameter"]);
            temp.push(dataM["limits"]);
            temp.push(dataM["rpm40"]);
            temp.push(dataM["rpm45"]);
            temp.push(dataM["rpm50"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPq4(tablesArray);
        //
        axios
          .post(`${dbConfig.url}/generalTableStaticData/findsome`, {
            fat_id: docId,
            // table_title: res?.data[0]["table_title"],
          })
          .then((res2) => {
            console.log("res222:", res2?.data);
            setPq4_staticData(res2?.data);
          })
          .catch((e) => {
            console.log("error Pq4_staticData get:", e);
          });
        //
      })
      .catch((e) => {
        console.log("error PQ4 data get:", e);
      });

    //PQ5
    axios
      .post(`${dbConfig.url}/generalTable/findsome`, {
        fat_id: docId,
        table_type: "PQ5",
      })
      .then((res) => {
        console.log("PQ5 data get:", res?.data);
        let resData = res?.data;
        const tablesArray = []; // array of tables
        if (res?.data?.length > 0) {
          let dataSorted = resData.sort(
            (a, b) => a["serial_no"] - b["serial_no"], //
          );
          const tempArr2d = [];
          dataSorted.map((dataM) => {
            const temp = [];
            temp.push(dataM["serial_no"]); // it has serial no
            temp.push(dataM["eqp_name"]);
            temp.push(dataM["eqp_id"]);
            temp.push(dataM["make"]);
            temp.push(dataM["test_param"]);
            temp.push(dataM["design_range"]);
            temp.push(dataM["qualification_range"]);
            temp.push(dataM["product_qualification_range"]);
            temp.push(dataM["tolerance"]);
            temp.push(dataM["_id"]); // for update ,it should be at this order only
            temp.push(dataM["index"]); // for those which has no serial number
            temp.push(dataM["table_title"]); //
            tempArr2d.push(temp);
          });
          tablesArray.push(tempArr2d);
        }
        setPq5(tablesArray);
        //
        // axios.post(`${dbConfig.url}/generalTableStaticData/findsome`, {
        //   fat_id: docId,
        //   // table_title: res?.data[0]["table_title"],
        // }).then((res2) => {
        //   console.log("res222:", res2?.data)
        //   setPq5_staticData(res2?.data)
        // }).catch((e) => {
        //   console.log("error Pq5_staticData get:", e)
        // })
        //
      })
      .catch((e) => {
        console.log("error PQ5 data get:", e);
      });

    // // for heading info
    // useEffect(() => {
    // 	if ('fid' in details) {
    // 		db.collection(companies)
    // 			.doc(companyId_constant)
    // 			.collection(type.substring(0, 3) + "List")
    // 			.doc(details?.fid)
    // 			.get()
    // 			.then((data) => setFatSat(data?.data()))
    // 		db.collection(companies)
    // 			.doc(companyId_constant)
    // 			.collection('machineData')
    // 			.doc(mid)
    // 			.get()
    // 			.then((data) => {
    // 				setModel(data?.data()?.model)
    // 				setSerialNo(data?.data()?.serialNo)
    // 			})
    // 	}
  }, [contentEditCount]); // [details]

  const printAFour = () => {
    var strWindowFeatures =
      "location=yes,height=800,width=800,scrollbars=yes,status=yes";
    if (type === "fatData") {
      var URL = "/" + mid + "/printFatSingle/" + docId;
      window.open(URL, "_blank", strWindowFeatures);
    } else if (type === "satData") {
      var URL = "/" + mid + "/printSatSingle/" + docId;
      window.open(URL, "_blank", strWindowFeatures);
    }
  };

  const background = {
    background: currentMode === "Dark" ? "#161C24" : "",
    color: currentMode === "Dark" ? "#fff" : "",
    border: currentMode === "Dark" ? "1px solid #fff" : "1px solid black",
  };

  const handleEnlarge = (data) => {
    setEnlarge(true);
    setEnlargeValue(data);
  };
  console.log("machine name", details);

  const filterTableArrayBasedOnTitle = (tData, module) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        abbreviation: mData[1],
        expanded_definition: mData[2],
        table_title: mData[3],
      });
      //console.log("index",index,":",mData)
    });
    // console.log("tempData:",tempTableData);
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForTraining = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      console.log("gg", mData);
      tempTableData?.push({
        serial_no: mData[0],
        name: mData[1],
        company_name: mData[2],
        designation: mData[3],
        department: mData[4],
        sign_date: mData[5],
        table_title: mData[6],
      });
      //console.log("index",index,":",mData)
    });
    console.log("tempData:", tempTableData);
    return tempTableData;
  };
  const filterTableArrayBasedOnTitleForReferenceDocuments = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        document_name: mData[1],
        document_no: mData[2],
        rev_no: mData[3],
        table_title: mData[4],
      });
      //console.log("index",index,":",mData)
    });
    // console.log("tempData:",tempTableData);
    return tempTableData;
  };
  const filterTableArrayBasedOnTitleForReferenceInstruments = (tData) => {
    const tableData = [...tData]; // [[]]
    console.log("yy", tData);
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        measuring_device: mData[1],
        certificate_no: mData[2],
        calibration_done_on: mData[3],
        calibration_due_on: mData[4],
        table_title: mData[5],
      });
      //console.log("index",index,":",mData)
    });
    console.log("tempData2:", tempTableData);
    return tempTableData;
  };
  const filterTableArrayBasedOnTitleForPostApproval = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        responsibility: mData[0],
        name: mData[1],
        department: mData[2],
        signature: mData[3],
        date: mData[4],
        table_title: mData[5],
      });
      //console.log("index",index,":",mData)
    });
    // console.log("tempData:",tempTableData);
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForSummaryConclusion = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        installation_report_summary: mData[1],
        obserbation: mData[2],
        table_title: mData[3],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPreRequisite = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        pre_requisites: mData[1],
        availability: mData[2],
        table_title: mData[3],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForExecutionTablePID = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        test_criteria: mData[0],
        method_of_verification: mData[1],
        acceptance_criteria: mData[2],
        obserbation: mData[3],
        table_title: mData[4],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForTestResult = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        test_criteria: mData[0],
        method_of_verification: mData[1],
        acceptance_criteria: mData[2],
        obserbation: mData[2],
        table_title: mData[3],
      });
    });

    return tempTableData;
  };

  // functions for execution tables
  const filterTableArrayBasedOnTitleForExecutionTable_12_1_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        standard_ins_reading: mData[0],
        indicator_reading: mData[1],
        measuring_error: mData[2],
        obserbation: mData[3],
        table_title: mData[4],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForExecutionTable_12_2_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        set_val: mData[0],
        indicator_reading: mData[1],
        tachometer_reading: mData[2],
        measuring_error: mData[3],
        max_allowable_err: mData[4],
        obserbation: mData[5],
        table_title: mData[6],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForExecutionTable_12_3_6 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        calibration_point: mData[0],
        reference_reading: mData[1],
        indicator_reading: mData[2],
        measuring_error: mData[3],
        obserbation: mData[4],
        table_title: mData[5],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForExecutionTable_12_5_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        test_criteria: mData[0],
        acceptance_criteria: mData[1],
        obserbation: mData[2],
        table_title: mData[3],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForExecutionTable_12_6_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        from: mData[0],
        to: mData[1],
        acceptance_criteria: mData[2],
        obserbation: mData[3],
        table_title: mData[4],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForExecutionTable_12_8_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        pass_details: mData[0],
        settalable_range: mData[1],
        acceptance_criteria: mData[2],
        obserbation: mData[3],
        table_title: mData[4],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForExecutionTable_12_9_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        event_audit_trail: mData[1],
        acceptance_criteria: mData[2],
        obserbation: mData[3],
        table_title: mData[4],
      });
    });

    return tempTableData;
  };
  const filterTableArrayBasedOnTitleForObservationTable_12_10_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        condition: mData[1],
        acceptance_criteria: mData[2],
        actual: mData[3],
        obserbation: mData[4],
        table_title: mData[5],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForObservationTable_12_11_4 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        plc: mData[1],
        acceptance_criteria: mData[2],
        obserbation: mData[3],
        result: mData[4],
        table_title: mData[5],
      });
    });

    return tempTableData;
  };
  const filterTableArrayBasedOnTitleForObservationTable_12_12_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        plc: mData[1],
        condition: mData[2],
        acceptance_criteria: mData[3],
        expected_result: mData[4],
        obserbation: mData[5],
        table_title: mData[6],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForObservationTable_13_2_4 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        cip_sequence: mData[1],
        obserbation: mData[2],
        table_title: mData[3],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForObservationTable_13_1_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        pressure_hold_test: mData[1],
        obserbation: mData[2],
        table_title: mData[3],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForObservationTable_13_5_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        process_sequence: mData[1],
        obserbation: mData[2],
        table_title: mData[3],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForSUMMARY_NON_CONFORMANCE = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        name_of_test: mData[1],
        ncr_no: mData[2],
        status: mData[3],
        table_title: mData[4],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForExecutionTable_13_3_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        sip_sequence_name: mData[1],

        obserbation: mData[2],
        table_title: mData[3],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForExecutionTable_13_4_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        pressure_hold_test: mData[1],

        obserbation: mData[2],
        table_title: mData[3],
      });
    });

    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForExecutionTable_13_6_5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        requirement: mData[0],

        obserbation: mData[1],
        table_title: mData[2],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq1 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        speed_of_machine: mData[1],
        "speed_in_rpm:": mData[2],
        id: mData[3],
        table_title: mData[5],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq2 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        parameter: mData[0],
        acceptance_criteria: mData[1],
        initial: mData[2],
        min10: mData[3],
        min20: mData[4],
        min30: mData[5],
        id: mData[6],
        table_title: mData[8],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq3 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        parameter: mData[0],
        specification: mData[1],
        rpm10: mData[2],
        rpm20: mData[3],
        rpm40: mData[4],
        id: mData[5],
        table_title: mData[7],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq4 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        parameter: mData[1],
        limits: mData[2],
        rpm40: mData[3],
        rpm45: mData[4],
        rpm50: mData[5],
        id: mData[6],
        table_title: mData[8],
      });
    });
    return tempTableData;
  };

  const filterTableArrayBasedOnTitleForPq5 = (tData) => {
    const tableData = [...tData]; // [[]]
    const tempTableData = [];
    [...tableData][0]?.forEach((mData, index) => {
      tempTableData?.push({
        serial_no: mData[0],
        eqp_name: mData[1],
        eqp_id: mData[2],
        make: mData[3],
        test_param: mData[4],
        design_range: mData[5],
        qualification_range: mData[6],
        product_qualification_range: mData[7],
        tolerance: mData[8],
        id: mData[9],
        table_title: mData[11],
      });
    });
    return tempTableData;
  };

  return (
    <section className="contentViewPage">
      <div className="allContentPreviewContainer">
        <header className="contentPageHeading" style={background}>
          <div className="contentHeading_left">
            <div className="contentViewPageLogo"></div>
            <div className="contentHeadingInfoLeft">
              <div className="contentHeadingTitle">
                {details ? (
                  machineName
                ) : (
                  <Button
                    onClick={() => setOpenAdd(true)}
                    variant="contained"
                    color="primary"
                  >
                    Add details
                  </Button>
                )}
              </div>
              <div>{details?.title}</div>
              <div className="modelNo">
                <span>Model:</span> {model}
              </div>
              <div className="serialNo">
                <span>Serial Number:</span> {serialNo}
              </div>
              <div className="protocolNo">
                <span>Protocol Number:</span> {fatsat?.protocol_no}
              </div>
            </div>
          </div>
        </header>
        {details ? (
          <main className="contentPageMain " style={background}>
            <div className="contentMainContainer">
              {details?.title ? (
                <div className="content_sub-section">
                  <header className="font-bold text-lg mb-3">
                    <div className="capitalize">
                      {details.index + `.0 `} {details?.title}
                    </div>
                  </header>
                  <div>
                    {details?.desc?.blocks?.length
                      ? details?.desc?.blocks?.map((options) => (
                          <Formatting key={options.id} data={options} />
                        ))
                      : ""}
                  </div>
                  <div style={{ marginTop: "0.5rem" }}>
                    {details?.purpose && (
                      <>
                        <div style={{ fontWeight: "bolder" }}>PURPOSE: </div>
                        <div>{details?.purpose}</div>
                      </>
                    )}
                  </div>
                  <div>
                    {details?.addDesc?.blocks?.length
                      ? details?.addDesc.blocks.map((options) => (
                          <Formatting key={options.id} data={options} />
                        ))
                      : ""}
                  </div>
                  <div style={{ marginTop: "0.5rem" }}>
                    {details?.scope && (
                      <>
                        <div style={{ fontWeight: "bolder" }}>SCOPE: </div>
                        <div>{details?.scope}</div>
                      </>
                    )}
                  </div>
                </div>
              ) : (
                ""
              )}

              {/* //DEVIATION, IMPACT AND CORRECTIVE ACTION SHEET */}
              {details?.type === 5 && <OQLayout docId={docId} />}
              {/* // */}

              {/* //DEVIATION, IMPACT AND CORRECTIVE ACTION SHEET */}
              {details?.type === 6 && (
                <NonConfirmity type={type} docId={docId} />
              )}
              {/* // */}

              {details?.objective ? (
                <div className="content_sub-section">
                  <div className="content_subtitle flex">
                    <h4 className="font-bold  mr-3"> OBJECTIVE:</h4>
                    <p>{details?.objective}</p>
                  </div>
                </div>
              ) : (
                <></>
              )}
              {details.method ? (
                <div className="content_sub-section">
                  <div className="content_subtitle flex">
                    <h4 className="font-bold  mr-3"> METHOD:</h4>
                    <p>{details?.method}</p>
                  </div>
                </div>
              ) : (
                <></>
              )}
              {details?.pre?.length > 0 ? (
                <div className="content_sub-section">
                  <div className="content_subtitle">
                    <h4 className="font-bold  mr-3 mb-2"> PREREQUISITES:</h4>
                    <div style={{ marginLeft: "10%" }}>
                      {details.pre.map((point, idx) => (
                        <p className="mb-2" key={idx}>
                          <span className="font-bold ">{idx + 1}.</span> {point}
                        </p>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <></>
              )}
              {details?.procedure?.length > 0 ? (
                <div className="content_sub-section">
                  <div className="content_subtitle">
                    <h4 className="font-bold  mr-3 mb-2"> TEST PROCEDURE:</h4>
                    <div style={{ marginLeft: "10%" }}>
                      {details.procedure.map((point, idx) => (
                        <p className="mb-2" key={idx}>
                          <span className="font-bold ">{idx + 1}.</span> {point}
                        </p>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <></>
              )}

              {table1?.length > 0 && (
                <>
                  {table1?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <T1
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {table2?.length > 0 && (
                <>
                  {table2?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <T2
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {table3?.length > 0 && (
                <>
                  {table3?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <T3
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {table4?.length > 0 && (
                <>
                  {table4?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <T4
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {abbreviation?.length > 0 && (
                <>
                  {Object.values(
                    Object?.groupBy(
                      [...filterTableArrayBasedOnTitle(abbreviation)],
                      ({ table_title }) => table_title,
                    ),
                  )?.map((tData) => (
                    <>
                      {/* {console.log("tdata:", tData?.map(Object?.values))} */}
                      <div>
                        {
                          tData?.map(Object?.values)[0][
                            tData?.map(Object?.values)[0]?.length - 1
                          ]
                        }
                      </div>
                      <Abbreviation
                        rowData={tData?.map(Object?.values)}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {/* <AmnealDocCommon/>
<RemarksCommon/> */}

              {training?.length > 0 && (
                <>
                  {Object.values(
                    Object?.groupBy(
                      [...filterTableArrayBasedOnTitleForTraining(training)],
                      ({ table_title }) => table_title,
                    ),
                  )?.map((tData) => (
                    <>
                      {/* {console.log("tdata:", tData?.map(Object?.values))} */}
                      <div>
                        {
                          tData?.map(Object?.values)[0][
                            tData?.map(Object?.values)[0]?.length - 1
                          ]
                        }
                      </div>
                      <Training
                        rowData={tData?.map(Object?.values)}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {referenceDocument?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForReferenceDocuments(
                            referenceDocument,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Reference_Document
                          rowData={tData?.map(Object?.values)}
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}
              {referenceInstruments?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForReferenceInstruments(
                            referenceInstruments,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Reference_Instruments
                          rowData={tData?.map(Object?.values)}
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {postApproval?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForPostApproval(
                            postApproval,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Post_Approval
                          rowData={tData?.map(Object?.values)}
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {preRequisites?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForPreRequisite(
                            preRequisites,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Pre_Requisites
                          rowData={tData?.map(Object?.values)}
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {execution_Table_PID?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForExecutionTablePID(
                            execution_Table_PID,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Execution_Table_PID
                          rowData={tData?.map(Object?.values)}
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {testResult?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForTestResult(
                            testResult,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Test_Result
                          rowData={tData?.map(Object?.values)}
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {executionTable_12_1_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForExecutionTable_12_1_5(
                            executionTable_12_1_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Execution_Table_12_1_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            executionTable_12_1_5_staticData?.length
                              ? executionTable_12_1_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}
              {executionTable_12_2_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForExecutionTable_12_2_5(
                            executionTable_12_2_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Execution_Table_12_2_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            executionTable_12_2_5_staticData?.length
                              ? executionTable_12_2_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {executionTable_12_3_6?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForExecutionTable_12_3_6(
                            executionTable_12_3_6,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Execution_Table_12_3_6
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            executionTable_12_3_6_staticData?.length
                              ? executionTable_12_3_6_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {executionTable_12_5_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForExecutionTable_12_5_5(
                            executionTable_12_5_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Execution_Table_12_5_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            executionTable_12_5_5_staticData?.length
                              ? executionTable_12_5_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}
              {executionTable_12_6_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForExecutionTable_12_6_5(
                            executionTable_12_6_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Execution_Table_12_6_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            executionTable_12_6_5_staticData?.length
                              ? executionTable_12_6_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}
              {executionTable_12_8_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForExecutionTable_12_8_5(
                            executionTable_12_8_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Execution_Table_12_8_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            executionTable_12_8_5_staticData?.length
                              ? executionTable_12_8_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {executionTable_12_9_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForExecutionTable_12_9_5(
                            executionTable_12_9_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Execution_Table_12_9_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            executionTable_12_9_5_staticData?.length
                              ? executionTable_12_9_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {console.log("tt", executionTable_13_3_5)}
              {executionTable_13_3_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForExecutionTable_13_3_5(
                            executionTable_13_3_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Execution_Table_13_3_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            executionTable_13_3_5_staticData?.length
                              ? executionTable_13_3_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {executionTable_13_4_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForExecutionTable_13_4_5(
                            executionTable_13_4_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Execution_Table_13_4_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            executionTable_13_4_5_staticData?.length
                              ? executionTable_13_4_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {executionTable_13_6_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForExecutionTable_13_6_5(
                            executionTable_13_6_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Execution_Table_13_6_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            executionTable_13_6_5_staticData?.length
                              ? executionTable_13_6_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {observationTable_12_10_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForObservationTable_12_10_5(
                            observationTable_12_10_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Observation_Table_12_10_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            observationTable_12_10_5_staticData?.length
                              ? observationTable_12_10_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {observationTable_12_11_4?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForObservationTable_12_11_4(
                            observationTable_12_11_4,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Observation_Table_12_11_4
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            observationTable_12_11_4_staticData?.length
                              ? observationTable_12_11_4_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}
              {observationTable_12_12_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForObservationTable_12_12_5(
                            observationTable_12_12_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Observation_Table_12_12_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            observationTable_12_12_5_staticData?.length
                              ? observationTable_12_12_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {observationTable_13_2_4?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForObservationTable_13_2_4(
                            observationTable_13_2_4,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Observation_Table_13_2_4
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            observationTable_13_2_4_staticData?.length
                              ? observationTable_13_2_4_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {observationTable_13_1_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForObservationTable_13_1_5(
                            observationTable_13_1_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Observation_Table_13_1_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            observationTable_13_1_5_staticData?.length
                              ? observationTable_13_1_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {observationTable_13_5_5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForObservationTable_13_5_5(
                            observationTable_13_5_5,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Observation_Table_13_5_5
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            observationTable_13_5_5_staticData?.length
                              ? observationTable_13_5_5_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {summaryNonConformance?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForSUMMARY_NON_CONFORMANCE(
                            summaryNonConformance,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <SUMMARY_NON_CONFORMANCE
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            summaryNonConformance_staticData?.length
                              ? summaryNonConformance_staticData[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}
              {iq1?.length > 0 && (
                <>
                  {iq1?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <IQ1
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq2?.length > 0 && (
                <>
                  {iq2?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <IQ2
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq3?.length > 0 && (
                <>
                  {iq3?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <IQ3
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq4?.length > 0 && (
                <>
                  {iq4?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <IQ4
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq5?.length > 0 && (
                <>
                  {iq5?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <IQ5
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq6?.length > 0 && (
                <>
                  {iq6?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <IQ6
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq7?.length > 0 && (
                <>
                  {iq7?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <IQ7
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq8?.length > 0 && (
                <>
                  {iq8?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <IQ8
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq9?.length > 0 && (
                <>
                  {iq9?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <IQ9
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq10?.length > 0 && (
                <>
                  {iq10?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <IQ10
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq11?.length > 0 && (
                <>
                  {iq11?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <IQ11
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq12?.length > 0 && (
                <>
                  {iq12?.map((tData) => (
                    <>
                      <div>{tData[0][tData[0]?.length - 1]}</div>
                      <IQ12
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq13?.length > 0 && (
                <>
                  {iq13?.map((tData) => (
                    <>
                      <div>{tData[0][tData[0]?.length - 1]}</div>
                      <IQ13
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {iq14?.length > 0 && (
                <>
                  {iq14?.map((tData) => (
                    <>
                      <div>{tData[0][tData[0]?.length - 1]}</div>
                      <IQ14
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {mfg1?.length > 0 && (
                <>
                  {mfg1?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <MFG1
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {eqp1?.length > 0 && (
                <>
                  {eqp1?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <EQP1
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {eqp2?.length > 0 && (
                <>
                  {eqp2?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <EQP2
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {eqp3?.length > 0 && (
                <>
                  {eqp3?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <EQP3
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {eqp4?.length > 0 && (
                <>
                  {eqp4?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <EQP4
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {eqp5?.length > 0 && (
                <>
                  {eqp5?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <EQP5
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {oq1?.length > 0 && (
                <>
                  {oq1?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <OQ1
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {oq2?.length > 0 && (
                <>
                  {oq2?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <OQ2
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {oq3?.length > 0 && (
                <>
                  {oq3?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <OQ3
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {oq4?.length > 0 && (
                <>
                  {oq4?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <OQ4
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {oq5?.length > 0 && (
                <>
                  {oq5?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <OQ5
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {oq6?.length > 0 && (
                <>
                  {oq6?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <OQ6
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {oq7?.length > 0 && (
                <>
                  {oq7?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <OQ7
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {oq8?.length > 0 && (
                <>
                  {oq8?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <OQ8
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {oq9?.length > 0 && (
                <>
                  {oq9?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <OQ9
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {oq10?.length > 0 && (
                <>
                  {oq10?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <OQ10
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}
              {oq11?.length > 0 && (
                <>
                  {oq11?.map((tData) => (
                    <>
                      <div>
                        {/* tableTitle */}
                        {tData[0][tData[0]?.length - 1]}
                      </div>
                      <OQ11
                        rowData={tData}
                        type={type}
                        machineName={machineName}
                        fatDataDocId={details.id}
                      />
                      <br />
                    </>
                  ))}
                </>
              )}

              {/* PQ start */}
              {pq1?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [...filterTableArrayBasedOnTitleForPq1(pq1)],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <PQ1
                          rowData={tData?.map(Object?.values)}
                          // tableStaticData={pq1?.length ? // it has no header and footer
                          //   pq1[0] : {}}
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {pq2?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [...filterTableArrayBasedOnTitleForPq2(pq2)],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <PQ2
                          key={uuidv4()}
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            pq2_staticData?.length
                              ? pq2_staticData?.filter(
                                  (fData) =>
                                    fData?.table_title ==
                                    tData?.map(Object?.values)[0][
                                      tData?.map(Object?.values)[0]?.length - 1
                                    ],
                                )[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {pq3?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [...filterTableArrayBasedOnTitleForPq3(pq3)],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <PQ3
                          key={uuidv4()}
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            pq3_staticData?.length
                              ? pq3_staticData?.filter(
                                  (fData) =>
                                    fData?.table_title ==
                                    tData?.map(Object?.values)[0][
                                      tData?.map(Object?.values)[0]?.length - 1
                                    ],
                                )[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {pq4?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [...filterTableArrayBasedOnTitleForPq4(pq4)],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <PQ4
                          key={uuidv4()}
                          rowData={tData?.map(Object?.values)}
                          tableStaticData={
                            pq4_staticData?.length
                              ? pq4_staticData?.filter(
                                  (fData) =>
                                    fData?.table_title ==
                                    tData?.map(Object?.values)[0][
                                      tData?.map(Object?.values)[0]?.length - 1
                                    ],
                                )[0]
                              : {}
                          }
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {pq5?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [...filterTableArrayBasedOnTitleForPq5(pq5)],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <PQ5
                          key={uuidv4()}
                          rowData={tData?.map(Object?.values)}
                          // tableStaticData={pq5_staticData?.length ?
                          //   pq5_staticData?.filter((fData) => fData?.table_title ==
                          //     tData?.map(Object?.values)[0][tData?.map(Object?.values)[0]?.length - 1])[0]
                          //   : {}}
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {summaryConclusion?.length > 0 && (
                <>
                  {
                    // console.log("ccccc",abbreviation?.map((Mdata,index)=> [{'table_title':Mdata[1]}]))

                    // Here we want to use same table, multiple times. So we first make the array of array [[1,2,3],[1,3,5]]
                    // as array of object [{"a":1,"b":2},{"a":1,"b":3,"c":5}] by a FUNCTION
                    // Then we group them and again make them array of array
                    Object.values(
                      Object?.groupBy(
                        [
                          ...filterTableArrayBasedOnTitleForSummaryConclusion(
                            summaryConclusion,
                          ),
                        ],
                        ({ table_title }) => table_title,
                      ),
                    )?.map((tData) => (
                      <>
                        {console.log("tdata:", tData?.map(Object?.values))}
                        <div>
                          {/* tableTitle */}
                          {
                            tData?.map(Object?.values)[0][
                              tData?.map(Object?.values)[0]?.length - 1
                            ]
                          }
                        </div>
                        <Summary_Conclusion
                          rowData={tData?.map(Object?.values)}
                          type={type}
                          machineName={machineName}
                          fatDataDocId={details.id}
                        />
                        <br />
                      </>
                    ))
                  }
                </>
              )}

              {tableDetails?.length > 0 ? (
                <div className="content_sub-section">
                  <div className="table_title">
                    <h4 className="font-bold  mr-3 mb-4">TEST EXECUTION</h4>
                  </div>
                  <TableContainer component={Paper} className="table">
                    <Table sx={{ minWidth: 650, width: "100%" }}>
                      <TableHead>
                        <TableRow
                          style={{
                            backgroundColor:
                              currentMode === "Dark" ? "#161C24" : "#D9D9D9",
                            fontWeight: "bold",
                            color: currentMode === "Dark" ? "white" : "black",
                            textTransform: "uppercase",
                          }}
                        >
                          <TableCell
                            style={{
                              border:
                                currentMode === "Dark"
                                  ? "1px solid white"
                                  : "1px solid black",
                            }}
                            align="left"
                          >
                            Check Point
                          </TableCell>
                          <TableCell
                            style={{
                              border:
                                currentMode === "Dark"
                                  ? "1px solid white"
                                  : "1px solid black",
                            }}
                            align="center"
                          >
                            Observation
                          </TableCell>
                          <TableCell
                            style={{
                              border:
                                currentMode === "Dark"
                                  ? "1px solid white"
                                  : "1px solid black",
                            }}
                            align="center"
                          >
                            Acceptance Criteria
                          </TableCell>
                          <TableCell
                            style={{
                              border:
                                currentMode === "Dark"
                                  ? "1px solid white"
                                  : "1px solid black",
                            }}
                            align="center"
                          >
                            Confirm YES/NO
                          </TableCell>
                          <TableCell
                            style={{
                              border:
                                currentMode === "Dark"
                                  ? "1px solid white"
                                  : "1px solid black",
                            }}
                            align="center"
                          >
                            Deviation
                          </TableCell>
                          <TableCell
                            style={{
                              border:
                                currentMode === "Dark"
                                  ? "1px solid white"
                                  : "1px solid black",
                            }}
                            align="center"
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>

                      <TableBody>
                        {tableDetails.map((data) => (
                          <ContentTableItem
                            collectionId={details?.id}
                            index={tableDetails?.length}
                            type={type}
                            key={data.id}
                            data={data}
                          />
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </div>
              ) : (
                ""
              )}
              {dynamicTableDetail?.map((value, idx) => {
                return (
                  <div className="content_sub-section">
                    <div className="table_title">
                      <h4 className="font-bold uppercase mr-3 mb-4">
                        {dynamicTableDetail[idx]?.tableName}
                      </h4>
                    </div>
                    <TableContainer component={Paper} className="table">
                      <Table sx={{ minWidth: 650, width: "100%" }}>
                        <TableHead>
                          <TableRow
                            style={{
                              backgroundColor:
                                currentMode === "Dark" ? "#161C24" : "#D9D9D9",
                              fontWeight: "bold",
                              color: currentMode === "Dark" ? "white" : "black",
                              textTransform: "uppercase",
                            }}
                          >
                            {JSON.parse(
                              dynamicTableDetail[idx]?.dynamicTable,
                            )[0]?.map((data) => (
                              <TableCell
                                key={data.id}
                                style={{
                                  border:
                                    currentMode === "Dark"
                                      ? "1px solid white"
                                      : "1px solid black",
                                }}
                                align="center"
                              >
                                {data.value}
                              </TableCell>
                            ))}
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {JSON.parse(
                            dynamicTableDetail[idx]?.dynamicTable,
                          ).map((row, idx) => {
                            if (idx !== 0) {
                              return (
                                <TableRow
                                  key={idx}
                                  sx={{
                                    backgroundColor:
                                      currentMode === "Dark"
                                        ? "#212B36"
                                        : "#fff",
                                    color:
                                      currentMode === "Dark"
                                        ? "white"
                                        : "black",
                                    textTransform: "capitalize",
                                  }}
                                >
                                  {row.map((cellData, cellIdx) => (
                                    <TableCell
                                      key={cellIdx}
                                      style={{
                                        border:
                                          currentMode === "Dark"
                                            ? "1px solid white"
                                            : "1px solid black",
                                      }}
                                      align="center"
                                    >
                                      {cellData.value}
                                    </TableCell>
                                  ))}
                                </TableRow>
                              );
                            }
                          })}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </div>
                );
              })}
              {/* // pre approval start : type === 0 */}
              {details?.template === "0" ||
                (details?.type === 0 && (
                  <PreApproval
                    rowData={[]} //{tData}
                    type={type}
                    machineName={machineName}
                    fatDataDocId={details.id}
                  />
                ))}

              {/* //Pre aproval end */}

              {/* //post Aproval */}
              {
                details?.template === "7" ||
                  (details?.type === 7 && (
                    <PostApprovalGilPq
                      rowData={[]} //{tData}
                      type={type}
                      machineName={machineName}
                      fatDataDocId={details._id}
                    />
                  ))

                // details?.template === "7" ||
                //   (details?.type === 7 && (
                //     <div className="content_sub-section">
                //       <TableContainer component={Paper} className="table">

                //         <Table sx={{ minWidth: 650, width: "100%" }}>
                //           <TableHead>
                //             <TableRow>
                //               <TableCell colSpan={2} style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",
                //                 backgroundColor: '#ddd'
                //               }}
                //                 align="center">
                //                 DD ENTERPRISES
                //               </TableCell>
                //             </TableRow>

                //           </TableHead>

                //           <TableBody>
                //             {/* {[1, 2, 3].map((data, index) => (
                //               <ApprovalTableItem key={index} />
                //             ))} */}
                //             <TableRow>
                //               <TableCell width="20%" style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",
                //                 backgroundColor: '#ddd'
                //               }}>
                //                 Name
                //               </TableCell>
                //               <TableCell width="80%" style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",

                //               }}>
                //                 {""}
                //               </TableCell>
                //             </TableRow>

                //             <TableRow>
                //               <TableCell width="20%" style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",
                //                 backgroundColor: '#ddd'
                //               }}>
                //                 Signature
                //               </TableCell>
                //               <TableCell width="80%" style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",

                //               }}>
                //                 {""}
                //               </TableCell>
                //             </TableRow>

                //             <TableRow>
                //               <TableCell width="20%" style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",
                //                 backgroundColor: '#ddd'
                //               }}>
                //                 Date
                //               </TableCell>
                //               <TableCell width="80%" style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",

                //               }}>
                //                 {""}
                //               </TableCell>
                //             </TableRow>

                //           </TableBody>
                //         </Table>
                //       </TableContainer>
                //     </div>
                //   ))}

                // {details?.template === "7" ||
                //   (details?.type === 7 && (
                //     <div className="content_sub-section">
                //       <TableContainer component={Paper} className="table">

                //         <Table sx={{ minWidth: 650, width: "100%", border: theme.borderDesign }}>
                //           <TableHead>
                //             <TableRow>
                //               <TableCell colSpan={5} style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",
                //                 backgroundColor: '#ddd'
                //               }}
                //                 align="center">
                //                 AMNEAL ONCOLOGY PVT LTD
                //               </TableCell>
                //             </TableRow>

                //             <TableRow>
                //               <TableCell width="10%" style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",
                //                 backgroundColor: '#ddd'
                //               }}
                //                 align="center">
                //                 RESPONSIBILITY
                //               </TableCell>
                //               <TableCell style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",
                //                 backgroundColor: '#ddd'
                //               }}
                //                 width="30%"
                //                 align="center">
                //                 NAME
                //               </TableCell>

                //               <TableCell width="20%" style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",
                //                 backgroundColor: '#ddd'
                //               }}
                //                 align="center">
                //                 DEPARTMENT
                //               </TableCell>

                //               <TableCell width="20%" style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",
                //                 backgroundColor: '#ddd'
                //               }}
                //                 align="center">
                //                 SIGNATURE
                //               </TableCell>

                //               <TableCell width="20%" style={{
                //                 border:
                //                   currentMode === "Dark"
                //                     ? "1px solid white"
                //                     : "1px solid black",
                //                 backgroundColor: '#ddd'
                //               }}
                //                 align="center">
                //                 DATE
                //               </TableCell>
                //             </TableRow>

                //           </TableHead>

                //           <TableBody>
                //             {/* {[1, 2, 3].map((data, index) => (
                //               <ApprovalTableItem key={index} />
                //             ))} */}
                //             {[1, 2, 3, 4, 5].map((data, index) => (
                //               <TableRow key={uuidv4()}>

                //                 <TableCell style={{
                //                   backgroundColor: '#ddd'
                //                 }}>
                //                   {/* // This First TableCell is static */}
                //                   {index === 0 ? "Reviewed by" : ""}
                //                 </TableCell>

                //                 <TableCell height='25vh' style={{
                //                   border:
                //                     currentMode === "Dark"
                //                       ? "1px solid white"
                //                       : "1px solid black",
                //                 }}>
                //                   {""}
                //                 </TableCell>

                //                 <TableCell height='25vh' style={{
                //                   border:
                //                     currentMode === "Dark"
                //                       ? "1px solid white"
                //                       : "1px solid black",

                //                 }}>
                //                   {""}
                //                 </TableCell>

                //                 <TableCell height='25vh' style={{
                //                   border:
                //                     currentMode === "Dark"
                //                       ? "1px solid white"
                //                       : "1px solid black",

                //                 }}>
                //                   {""}
                //                 </TableCell>

                //                 <TableCell height='25vh' style={{
                //                   border:
                //                     currentMode === "Dark"
                //                       ? "1px solid white"
                //                       : "1px solid black",

                //                 }}>
                //                   {""}
                //                 </TableCell>
                //               </TableRow>
                //             ))}

                //             {[1].map((data, index) => (
                //               <TableRow key={uuidv4()}>
                //                 <TableCell style={{
                //                   borderTop: index === 0 ?
                //                     currentMode === "Dark"
                //                       ? "1px solid white"
                //                       : "1px solid black" :
                //                     "none",
                //                   backgroundColor: '#ddd'
                //                 }}>
                //                   {/* // First TableCell is static */}
                //                   {index === 0 ? "Approved by" : ""}
                //                 </TableCell>

                //                 <TableCell height='25vh' style={{
                //                   border:
                //                     currentMode === "Dark"
                //                       ? "1px solid white"
                //                       : "1px solid black",

                //                 }}>
                //                   {""}
                //                 </TableCell>

                //                 <TableCell height='25vh' style={{
                //                   border:
                //                     currentMode === "Dark"
                //                       ? "1px solid white"
                //                       : "1px solid black",

                //                 }}>
                //                   {""}
                //                 </TableCell>

                //                 <TableCell height='25vh' style={{
                //                   border:
                //                     currentMode === "Dark"
                //                       ? "1px solid white"
                //                       : "1px solid black",

                //                 }}>
                //                   {""}
                //                 </TableCell>

                //                 <TableCell height='25vh' style={{
                //                   border:
                //                     currentMode === "Dark"
                //                       ? "1px solid white"
                //                       : "1px solid black",

                //                 }}>
                //                   {""}
                //                 </TableCell>
                //               </TableRow>
                //             ))}

                //           </TableBody>
                //         </Table>
                //       </TableContainer>
                //     </div>
                //   ))
              }

              {/* //Post aproval end */}
              {details?.title ? (
                <div className="content_sub-section">
                  <div className="flex flex-wrap">
                    {details?.urls?.length
                      ? details?.urls.map((options) => (
                          <div
                            onClick={() => handleEnlarge(options)}
                            style={{ cursor: "pointer", margin: "30px" }}
                          >
                            <img
                              style={{
                                margin: "5px 0",
                                width: "350px",
                                height: "350px",
                              }}
                              key={options}
                              src={options}
                            />
                            {/* <ButtonBasic  buttonTitle="Enlarge"></ButtonBasic> */}
                          </div>
                        ))
                      : ""}
                  </div>
                  <Dialog
                    open={enlarge}
                    onClose={() => setEnlarge(false)}
                    fullWidth
                    maxWidth="lg"
                  >
                    <DialogTitle>Enlarged Image</DialogTitle>
                    <DialogContent>
                      <img
                        src={enlargeValue}
                        alt={"Image"}
                        style={{ width: "100%", height: "100%" }}
                      />
                    </DialogContent>
                  </Dialog>
                </div>
              ) : (
                ""
              )}
              {/* {customerApprovalTable?.length > 0 ? ( */}

              {details?.template !== "0" ||
                (details?.type === 0 && (
                  <div className="content_sub-section">
                    <div className="content_subtitle">
                      <h4 className="font-bold  mr-3"> TEST SUMMARY:</h4>
                      <div className="flex items-center justify-start gap-x-4">
                        Document Complies
                        <span>
                          <Checkbox checked={details?.summary} />
                          Yes
                        </span>
                        <span>
                          <Checkbox checked={!details?.summary} />
                          No
                        </span>
                      </div>
                    </div>
                  </div>
                ))}

              {details.comment &&
              (details?.template !== "0" || details?.type === 0) ? (
                <div className="content_sub-section">
                  <div className="content_subtitle flex">
                    <h4 className="font-bold  mr-3"> COMMENT:</h4>
                    <p>{details?.comment}</p>
                  </div>
                </div>
              ) : (
                ""
              )}
            </div>
          </main>
        ) : (
          <main className="contentPageMain flex justify-center">
            {dataLoading && <CircularProgress />}
            <Typography variant="h5" style={{ marginLeft: "30px" }}>
              NO DATA{" "}
            </Typography>
          </main>
        )}
        {details?.type !== 0 && (
          <Fab
            className="faBtn"
            color={"primary"}
            onClick={() => setOpen(!open)}
          >
            {open ? (
              <ClearIcon sx={{ color: "#fff" }} />
            ) : (
              <AddIcon sx={{ color: "#fff" }} />
            )}
          </Fab>
        )}

        <Fab
          className="faBtnLeft"
          color={"primary"}
          onClick={() => {
            const what_id = type.substring(0, 3) + "_id";
            maintenanceInfoSetter({ [what_id]: details.fid });
            history.go(-1);
          }}
        >
          <ArrowBackIcon sx={{ color: "#fff" }} />
        </Fab>

        {open && (
          <div className="fabDrawer">
            {details?.type !== 1 && (
              <Tooltip title="Test Execution Table Upload by Excel Sheet">
                <Button
                  color="success"
                  onClick={() => setOpenUploadTable(true)}
                >
                  <UploadFileRounded />
                </Button>
              </Tooltip>
            )}

            {details?.type !== 1 && (
              <Tooltip title="Test Execution Table Template">
                <Button color="success" onClick={() => setOpenTable(true)}>
                  <BackupTableIcon />
                </Button>
              </Tooltip>
            )}

            {/* {
								details?.type !== 1 && <Tooltip title="Custom Table Generation">
									<Button color='info' onClick={() => setOpenDynamicTable(true)}>
										<BackupTableIcon />
									</Button>
								</Tooltip>
							} */}

            {/* {
								details?.type !== 1 && <Tooltip title="Edit Custom Table Values">
									<Button color='info' onClick={() => setOpenEditDynamicTable(true)}>
										<AppRegistrationIcon />
									</Button>
								</Tooltip>
							} */}

            {details?.type !== -1 && (
              <Tooltip title="Edit Master Details">
                <Button onClick={() => setOpenEdit(true)}>
                  <i className="ri-pencil-fill text-blue-500"></i>
                </Button>
              </Tooltip>
            )}

            {/* {
								details?.type !== 1 && <Tooltip title="Print this page">
									<Button onClick={() => printAFour()}>
										<i className="ri-printer-line"></i>
									</Button>
								</Tooltip>
							} */}
          </div>
        )}
      </div>
      {/* Add details when details length is zero */}
      <Dialog open={openAddDetails} fullWidth maxWidth="lg">
        <DialogTitle>Add Details to Master Copy</DialogTitle>
        <DialogContent>
          <AddDetailsDocumentation
            type={type}
            handleClose={() => setOpenAdd(false)}
            machineName={machineName}
          />
        </DialogContent>
      </Dialog>
      {/* Add Table details to master copy */}
      <Dialog open={openTable} fullWidth maxWidth="lg">
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle>Add Table Row to Master Copy</DialogTitle>
          <AddTableValues
            collectionId={details?._id}
            index={tableDetails?.length}
            type={type}
            handleClose={() => setOpenTable(false)}
          />
        </DialogContent>
      </Dialog>
      {/* Add Dynamic Table Detail */}
      <Dialog open={openDynamicTable} fullWidth maxWidth="lg">
        <DialogTitle>Add Dynamic Table to Master Copy</DialogTitle>
        <DialogContent>
          <AddDynamicTableValues
            fid={details?.fid}
            mid={mid}
            docId={docId}
            collectionId={details?._id}
            type={type}
            handleClose={() => setOpenDynamicTable(false)}
            reportName={details?.title}
            machineName={machineName}
          />
        </DialogContent>
      </Dialog>
      {/* Edit Dynamic Table Detail */}
      <Dialog open={openEditDynamicTable} fullWidth maxWidth="lg">
        <DialogTitle>Edit Dynamic Table to Master Copy</DialogTitle>
        <DialogContent>
          <EditDynamicTableValues
            fid={details?.fid}
            mid={mid}
            src
            docId={docId}
            collectionId={details?.id}
            type={type}
            handleClose={() => setOpenEditDynamicTable(false)}
            handleNewTable={() => setOpenDynamicTable(true)}
            reportName={details?.title}
            machineName={machineName}
          />
        </DialogContent>
      </Dialog>
      {/* Edit details */}
      <Dialog
        open={openEdit}
        fullWidth
        maxWidth="lg"
        onClose={() => setOpenEdit(false)}
      >
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle>Edit Details</DialogTitle>
          <EditDetailsDocumentation
            details={details}
            type={type}
            handleClose={() => setOpenEdit(false)}
            machineName={machineName}
            setOpenUploadTable={setOpenUploadTable}
            setOpenTable={setOpenTable}
            setOpenEdit={setOpenEdit}
          />
        </DialogContent>
      </Dialog>
      {/* Add Approval signatures table */}
      <Dialog open={openAppr} fullWidth maxWidth="lg">
        <DialogTitle>
          Add Approval Signatures -{" "}
          <span className="text-gray-500" style={{ fontSize: "14px" }}>
            {today?.toString().substring(0, 15)}
          </span>
        </DialogTitle>
        <DialogContent>
          <AddApproval
            collectionId={details?.id}
            index={approvalTable?.length}
            type={type}
            machineName={machineName}
            handleClose={() => setOpenAppr(false)}
            reportTitle={details?.title}
          />
        </DialogContent>
      </Dialog>

      {/* Excell upload */}
      <Dialog open={openUpladedTable} fullWidth maxWidth="lg">
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle>Add Details to Master Copy</DialogTitle>
          <TableMain
            handleClose={() => setOpenUploadTable(false)}
            type={type}
            machineName={machineName}
            fatDataDocId={details.id}
            fatData={details}
          />
          <DialogActions>
            <ButtonBasic
              buttonTitle="Cancel"
              onClick={() => setOpenUploadTable(false)}
            />
          </DialogActions>
        </DialogContent>
      </Dialog>
    </section>
  );
};

const ProcessTable = ({ title, value }) => {
  return (
    <TableContainer
      component={Paper}
      className="table"
      sx={{ margin: "5px 0" }}
    >
      <h1>{title}</h1>
      <Table sx={{ minWidth: 650, width: "100%" }}>
        <TableHead>
          <TableRow
            style={{
              color: "white",
              backgroundColor: "#D9D9D9",
              border: "1px solid black",
              fontWeight: "bold",
            }}
          >
            <TableCell style={{ border: "1px solid black" }} align="left">
              KEY
            </TableCell>
            <TableCell style={{ border: "1px solid black" }} align="center">
              VALUE
            </TableCell>
          </TableRow>
        </TableHead>

        <TableBody>
          {value.map((data) => (
            <TableRow
              sx={{
                "&:last-child td, &:last-child th": { border: 0 },
              }}
              style={{ cursor: "pointer" }}
            >
              <TableCell style={{ border: "1px solid black" }} align="left">
                {data.key}
              </TableCell>
              <TableCell style={{ border: "1px solid black" }} align="center">
                {data.value}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default Content;
