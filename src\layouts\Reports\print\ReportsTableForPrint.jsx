import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import React, { useState } from "react";
import { useEffect } from "react";
import { useParams } from "react-router-dom";
import {
  companies,
  companyId_constant,
  fatReport,
} from "../../../constants/data";
import { db } from "../../../firebase";
import { firebaseLooper } from "../../../tools/tool";
import ApprovalTableItem from "../../machineData/ApprovalTable/ApprovalTable";
import { useStateContext } from "../../../context/ContextProvider";
import { T1 } from "../../machineData/TableTemplates/TableTemplates";

const ReportsTableForPrint = ({ fatDataId }) => {
  const { reportId } = useParams(); //ID of reports from which the fatData has to be fetched

  // const databaseCollection = db.collection(companies).doc(companyId_constant)
  //     .collection(fatReport).doc(reportId).collection(`fatData`)

  const [reportTableDetails, setReportTableDetails] = useState([]);
  const [approvalTable, setApprovalTable] = useState([]);
  const { currentMode } = useStateContext();

  const [sections, setSections] = useState([]); //  array with length (approvalTable.length/4) eg: [0,1].  // for aprovalTable print 1/2:-

  useEffect(() => {
    // databaseCollection.doc(fatDataId).collection('table') //every data from collection table will be fetched
    //     .onSnapshot(snap => {
    //         const data = firebaseLooper(snap)
    //         setReportTableDetails(data)
    //         // console.log(data)
    //     })
    // databaseCollection.doc(fatDataId).collection('approval') //Approval table for only 1 section - Pre approvals
    //     .onSnapshot(snap => {
    //         const data = firebaseLooper(snap)
    //         setApprovalTable(data);
    //         // for aprovalTable print 2/2:-
    //         let temp = data?.length;
    //         let sectionsTempLength = Math.ceil(temp / 4);
    //         const sectionsTemp = [];
    //         for (let i = 0; i < sectionsTempLength; i++) {
    //             sectionsTemp.push(i);
    //         }
    //         setSections([...sectionsTemp]);
    //     })
  }, []);

  if (reportTableDetails.length > 0) {
    // reportTableDetails.length > 0
    return (
      <div>
        {/* (JSON.parse(window.localStorage.getItem('table1'))).slice(0,1) */}

        <TableContainer component={Paper} className="table">
          <Table sx={{ minWidth: 650, width: "100%" }}>
            <TableHead>
              <TableRow
                style={{
                  backgroundColor: "#ddd",
                  color: "black",
                  border: "1px solid black",
                  fontWeight: "bold",
                }}
              >
                <TableCell
                  style={{ border: "1px solid black", color: "black" }}
                  align="left"
                >
                  Check Point
                </TableCell>
                <TableCell
                  style={{ border: "1px solid black", color: "black" }}
                  align="center"
                >
                  Observation
                </TableCell>
                <TableCell
                  style={{ border: "1px solid black", color: "black" }}
                  align="center"
                >
                  Acceptance Criteria
                </TableCell>
                <TableCell
                  style={{ border: "1px solid black", color: "black" }}
                  align="center"
                >
                  Confirm YES/NO
                </TableCell>
                <TableCell
                  style={{ border: "1px solid black", color: "black" }}
                  align="center"
                >
                  Deviation
                </TableCell>
              </TableRow>
            </TableHead>

            <TableBody>
              {reportTableDetails.map((data) => (
                <TableRow
                  sx={{
                    "&:last-child td, &:last-child th": { border: 0 },
                  }}
                  style={{ cursor: "pointer" }}
                >
                  <TableCell
                    style={{
                      border: "1px solid black",
                      backgroundColor: "#fff",
                      color: "black",
                    }}
                    align="left"
                  >
                    {data.check}
                  </TableCell>
                  <TableCell
                    style={{
                      border: "1px solid black",
                      backgroundColor: "#fff",
                      color: "black",
                    }}
                    align="center"
                  >
                    {data.observation}
                  </TableCell>
                  <TableCell
                    style={{
                      border: "1px solid black",
                      backgroundColor: "#fff",
                      color: "black",
                    }}
                    align="center"
                  >
                    {data.acceptance}
                  </TableCell>
                  <TableCell
                    style={{
                      border: "1px solid black",
                      backgroundColor: "#fff",
                      color: "black",
                    }}
                    align="center"
                  >
                    {data.confirm}
                  </TableCell>
                  <TableCell
                    style={{
                      border: "1px solid black",
                      backgroundColor: "#fff",
                      color: "black",
                    }}
                    align="center"
                  >
                    {data.dev}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* <div>vvvvvvvvvvvvvvvbb</div>
            <div>vvvvvvvvvvvvvvvbb</div>
            <div>vvvvvvvvvvvvvvvbb</div> */}
        {/* <T1 rowData={(JSON.parse(window.localStorage.getItem('table1'))).slice(0,2) }/>  */}
      </div>
    );
  } else {
    return (
      <>
        {sections?.map((dataSection) => (
          <div className={sections?.length > 1 ? "height80Vh " : ""}>
            {approvalTable.length > 0 && (
              <TableContainer component={Paper} className="table">
                <Table sx={{ minWidth: 650, width: "100%" }}>
                  <TableHead>
                    <TableRow
                      style={{
                        backgroundColor: "#ddd",
                        color: "black",
                        border: "1px solid black",
                        fontWeight: "bold",
                      }}
                    >
                      <TableCell
                        style={{ border: "1px solid black", color: "black" }}
                        align="left"
                      >
                        Name{" "}
                      </TableCell>
                      <TableCell
                        style={{ border: "1px solid black", color: "black" }}
                        align="left"
                      >
                        Signature
                      </TableCell>
                      <TableCell
                        style={{ border: "1px solid black", color: "black" }}
                        align="center"
                      >
                        Date
                      </TableCell>
                    </TableRow>
                  </TableHead>

                  <TableBody>
                    {approvalTable
                      ?.slice(dataSection * (1 * 4), (dataSection + 1) * 4)
                      .map((data) => (
                        <ApprovalTableItem
                          key={data.id}
                          data={data}
                          useAt="print"
                        />
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </div>
        ))}
      </>
    );
  }
};

export default ReportsTableForPrint;
