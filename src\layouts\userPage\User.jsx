import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Button,
  Paper,
  CircularProgress,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import UsersListTable from "./UsersListTable copy";
import ADusers from "./ADusers copy";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { getAllLDAPUsersUsingConfig } from "./Users";
import NewUserModal from "./new-user-modal";
import AddIcon from "@mui/icons-material/Add";
import UserListTableAlt from "./UserListTableAlt";

const INITIAL_CONFIG = {
  url: "",
  baseDN: "",
  username: "",
  password: "",
};

const User = () => {
  const [viewAdusers, setViewAdusers] = useState(false);
  const [allUsersList, setAllUsersList] = useState([]);
  const [ldapUsers, setLdapUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingUT, setLoadingUT] = useState(true);
  const [showAddUser, setShowAddUser] = useState(false);
  const [globalConfigState, setGlobalConfigState] = useState(INITIAL_CONFIG);

  const fetchAllUsers = async () => {
    setLoadingUT(true);
    try {
      const response = await axios.get(`${dbConfig.url}/users`);
      setAllUsersList(response?.data?.data);
      console.log("api:-", response?.data?.data);
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoadingUT(false);
    }
  };

  useEffect(() => {
    fetchAllUsers();
  }, []);

  const handleAddUser = () => setShowAddUser(true);
  const handleCloseAddUser = () => setShowAddUser(false);

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper
            elevation={3}
            sx={{
              overflow: "hidden",
              height: "100%",
              backgroundColor: "#f8f9fa",
            }}
          >
            {loadingUT ? (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "50vh",
                }}
              >
                <CircularProgress />
              </Box>
            ) : viewAdusers ? (
              <Box sx={{ p: 3 }}>
                <ADusers />
              </Box>
            ) : (
              <Box sx={{ backgroundColor: "rgb(255, 244, 229)" }}>
                <UserListTableAlt
                  allUsersList={allUsersList}
                  fetchAllUsers={fetchAllUsers}
                  setloading={setLoadingUT}
                  loading={loading}
                  isModelOpen={false} // Add this if not already present
                  setShowAddUser={setShowAddUser} // Add this prop
                  sx={{
                    "& .MuiTable-root": {
                      border: "none",
                    },
                    "& .MuiTableCell-head": {
                      bgcolor: "#f8f9fa",
                      fontWeight: 600,
                    },
                  }}
                />
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      <NewUserModal
        fetchAllUsers={fetchAllUsers}
        getAllLDAPUsersUsingConfig={getAllLDAPUsersUsingConfig}
        globalConfigState={globalConfigState}
        ldapUsers={ldapUsers}
        onClose={handleCloseAddUser}
        open={showAddUser}
        setGlobalConfigState={setGlobalConfigState}
        setLdapUsers={setLdapUsers}
        setLoading={setLoading}
        showAddUser={showAddUser}
        loadingUT={loadingUT}
        allUsersList={allUsersList}
        handleCloseAddUser={handleCloseAddUser}
        setloadingUT={setLoadingUT}
        loading={loading}
      />
    </Box>
  );
};

export default User;
