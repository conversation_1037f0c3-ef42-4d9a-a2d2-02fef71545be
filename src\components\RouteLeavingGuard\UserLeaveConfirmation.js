import { unmountComponentAtNode, render } from "react-dom";
import React from "react";
import WarningDialog from "./WarningDialog";
import { Dialog } from "@mui/material";

export default function UserLeaveConfirmation(
  message,
  callback,
  confirmOpen,
  setConfirmOpen,
  handleRouteNavigation,
) {
  const { header, content } = JSON.parse(message);

  const container = document.createElement("div");
  container.setAttribute("custom-confirm-view", "");
  const handleConfirm = (callbackState) => {
    unmountComponentAtNode(container);
    callback(callbackState);
    setConfirmOpen(false);
    handleRouteNavigation(false);
  };
  const handleCancel = () => {
    unmountComponentAtNode(container);
    callback();
    setConfirmOpen(false);
  };
  document.querySelector("#root").appendChild(container);
  render(
    <Dialog open={confirmOpen}>
      <WarningDialog
        titleText={header}
        contentText={content}
        cancelButtonText="Dismiss"
        confirmButtonText="Confirm"
        onCancel={handleCancel}
        onConfirm={handleConfirm}
      />
    </Dialog>,
    container,
  );
}
