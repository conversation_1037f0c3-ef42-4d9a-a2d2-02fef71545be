import React, { createContext, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
// import { getEnvData } from '../services/env';
import PropTypes from "prop-types";

/**
 * An object containing all available roles.
 * @property {string} ADMIN - The role for an admin user.
 * @property {string} USER - The role for a regular user.
 * @property {string} PLANNER - The role for a planner user.
 * @property {string} PERFORMER - The role for a performer user.
 * @property {string} APPROVER - The role for an approver user.
 */
export const allAvailableRoles = {
  ADMIN: "admin",
  USER: "user",
  PLANNER: "planner",
  PERFORMER: "performer",
  APPROVER: "approver",
};

const UtilsContext = createContext({
  currUser: null,
  // eslint-disable-next-line no-empty-pattern
  haveAccess: ([]) => {},
  // eslint-disable-next-line no-empty-pattern
  notHaveAccess: ([]) => {},
});

/**
 * Provides the current user's credentials and functions to check if the current user
 * has access based on the provided array of roles.
 *
 * @returns {{
 *   currUser: null|Object,
 *   envData: null|{
 *     PAGINATION_TYPE: 'Sequential'|'Numeric',
 *     CARD_BG_COLOR: string,
 *     ROW_PER_PAGE: number|string,
 *     USERS_ACCESS: Array<string>,
 *     PLANNERS_ACCESS: Array<string>,
 *     APPROVERS_ACCESS: Array<string>,
 *     PERFORMERS_ACCESS: Array<string>,
 *     ADMINS_ACCESS: Array<string>,
 *     USERS_RSF_ACCESS: Array<string>,
 *     PLANNERS_RSF_ACCESS: Array<string>,
 *     APPROVERS_RSF_ACCESS: Array<string>,
 *     PERFORMERS_RSF_ACCESS: Array<string>,
 *     ADMINS_RSF_ACCESS: Array<string>,
 *   },
 *   isFetching: boolean,
 *   haveAccess: (arrOfRoles: Array<string>) => boolean,
 *   notHaveAccess: (arrOfRoles: Array<string>) => boolean
 * }} An object containing the current user's credentials and functions to check access.
 * @property {Object} currUser - The current user's credentials.
 * @property {Object} envData - The environment variables.
 * @property {boolean} isFetching - A boolean indicating if the environment variables are being fetched.
 * @property {function} haveAccess - A function that takes an array of roles and returns true if the current user has access, false otherwise.
 * @property {function} notHaveAccess - A function that takes an array of roles and returns true if the current user does not have access, false otherwise.
 */
export const useUtils = () => {
  return useContext(UtilsContext);
};

const UtilsProvider = ({ children }) => {
  const [envData, setEnvData] = useState(null);
  const [isFetching, setIsFetching] = useState(true);

  useEffect(() => {
    const getAndSetEnvData = async () => {
      // const envData = (await getEnvData()).data;
      const envData = {};
      setEnvData(envData);
    };

    getAndSetEnvData().then(() => setIsFetching(false));
  }, []);

  /**
   * The current user's credentials stored in localStorage.
   * @type {Object}
   */
  const currentUser = JSON.parse(localStorage.getItem("@user-creds"));

  /**
   * Checks if the current user has access based on the provided array of roles.
   *
   * @param {Array} arrOfRoles - The array of roles to check against the current user's role.
   * @return {boolean} Returns true if the current user has access, false otherwise.
   */
  const haveAccess = (arrOfRoles) => {
    if (!currentUser) {
      toast.error("You Session Expired. Please Login Again");
      return false;
    }
    if (arrOfRoles.includes(currentUser.role)) {
      return true;
    }
    return false;
  };

  /**
   * Checks if the current user does not have access based on the provided array of roles.
   *
   * @param {Array} arrOfRoles - The array of roles to check against the current user's role.
   * @return {boolean} Returns true if the current user does not have access, false otherwise.
   */
  const notHaveAccess = (arrOfRoles) => {
    if (!currentUser) {
      toast.error("You Session Expired. Please Login Again");
      return false;
    }
    if (!arrOfRoles.includes(currentUser.role)) {
      return true;
    }
    return false;
  };

  return (
    <UtilsContext.Provider
      value={{
        currUser: currentUser,
        envData: {
          ...envData,
          CARD_BG_COLOR: envData?.CARD_BG_COLOR
            ? envData.CARD_BG_COLOR
            : "#fff",
          ROW_PER_PAGE: envData?.ROW_PER_PAGE ? envData.ROW_PER_PAGE : 10,
          USERS_ACCESS: envData?.USERS_ACCESS ? envData.USERS_ACCESS : [],
          PLANNERS_ACCESS: envData?.PLANNERS_ACCESS
            ? envData.PLANNERS_ACCESS
            : [],
          APPROVERS_ACCESS: envData?.APPROVERS_ACCESS
            ? envData.APPROVERS_ACCESS
            : [],
          PERFORMERS_ACCESS: envData?.PERFORMERS_ACCESS
            ? envData.PERFORMERS_ACCESS
            : [],
          ADMINS_ACCESS: envData?.ADMINS_ACCESS
            ? envData.ADMINS_ACCESS
            : ["/ENV"],
          USERS_RSF_ACCESS: envData?.USERS_RSF_ACCESS
            ? envData.USERS_RSF_ACCESS
            : [],
          PLANNERS_RSF_ACCESS: envData?.PLANNERS_RSF_ACCESS
            ? envData.PLANNERS_RSF_ACCESS
            : [],
          APPROVERS_RSF_ACCESS: envData?.APPROVERS_RSF_ACCESS
            ? envData.APPROVERS_RSF_ACCESS
            : [],
          PERFORMERS_RSF_ACCESS: envData?.PERFORMERS_RSF_ACCESS
            ? envData.PERFORMERS_RSF_ACCESS
            : [],
          ADMINS_RSF_ACCESS: envData?.ADMINS_RSF_ACCESS
            ? envData.ADMINS_RSF_ACCESS
            : [],
        },
        isFetching: isFetching,
        haveAccess: haveAccess,
        notHaveAccess: notHaveAccess,
      }}
    >
      {children}
    </UtilsContext.Provider>
  );
};
UtilsProvider.propTypes = {
  children: PropTypes.node,
};
export default UtilsProvider;
