import React, { useState } from "react";
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { companies, companyId_constant } from "../../../constants/data";
import { db } from "../../../firebase";
import LoggingFunction from "../../../components/CFR-Report/LoggingFunction";
import { useStateContext } from "../../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../../infrastructure/db/db-config";
import { toastMessage, toastMessageSuccess } from "../../../tools/toast";
import { useMongoRefresh } from "../../../services/mongo-refresh.context";

const EditTrainingReport = ({
  handleClose,
  data,
  userName,
  reportName,
  machineName,
  reportType,
}) => {
  console.log("data", data);
  const [status, setStatus] = useState(data.report_status);
  const [remarks, setRemarks] = useState(data.remarks);
  const { currentMode } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const [remarksError, setRemarksError] = useState("");

  // Handle remarks change and prevent leading space
  const handleRemarksChange = (e) => {
    const value = e.target.value;
    // Prevent space as the first character
    if (value.length === 1 && value === " ") {
      return; // Ignore the input if it's just a space
    }
    // Remove leading spaces if they somehow get through (e.g., pasting)
    const trimmedValue = value.replace(/^\s+/, "");
    setRemarks(trimmedValue);
    setRemarksError(""); // Clear error when user types valid input
  };

  const handleRemarksBlur = () => {
    const trimmedRemarks = remarks.trim();
    setRemarks(trimmedRemarks);
    if (!trimmedRemarks) {
      setRemarksError("Remarks cannot be empty or just spaces");
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate remarks
    const trimmedRemarks = remarks.trim();
    if (!trimmedRemarks) {
      setRemarksError("Remarks cannot be empty or just spaces");
      toastMessage({ message: "Please enter valid remarks" });
      return; // Prevent submission
    }
    const dataSet = {
      ...data,
      report_status: status,
      remarks,
      date: new Date(data.date), // Convert date to a Date object
    };
    await axios
      .put(`${dbConfig.url}/manualReport/${data._id}`, dataSet)
      .then((res) => {
        setRefreshCount(refreshCount + 1); //ues RTK // Parent component is "MaintenanceReportDataMain"
        toastMessageSuccess({ message: "Report Updated Successfully" });
        data.report_status = status;
        data.remarks = remarks;
        handleClose();
      })
      .catch((err) => {
        console.log("ERR", err.message);
      });
  };

  return (
    <form onSubmit={handleSubmit}>
      <InputLabel style={{ marginBottom: "10px" }}>Report</InputLabel>
      <TextField
        onChange={handleRemarksChange}
        onBlur={handleRemarksBlur}
        value={remarks}
        required
        variant="outlined"
        fullWidth
        multiline
        style={{ marginBottom: "12px" }}
        error={!!remarksError}
        helperText={remarksError}
      />

      <InputLabel style={{ marginBottom: "10px" }}>Select Status</InputLabel>
      <FormControl
        style={{ marginBottom: "10px" }}
        required
        variant="outlined"
        fullWidth
      >
        <Select
          required
          value={status}
          onChange={(e) => setStatus(e.target.value)}
        >
          <MenuItem
            value={2}
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#212B36", color: "white" }
                : {}
            }
          >
            Review Done
          </MenuItem>
          <MenuItem
            value={1}
            style={
              currentMode === "Dark"
                ? { backgroundColor: "#212B36", color: "white" }
                : {}
            }
          >
            Pending
          </MenuItem>
        </Select>
      </FormControl>

      <div className="p-2 mt-2 flex justify-between">
        <Button color="error" onClick={handleClose} variant="contained">
          Cancel
        </Button>
        <Button color="success" type="submit" variant="contained">
          Submit
        </Button>
      </div>
    </form>
  );
};

export default EditTrainingReport;
