.addAdminForm {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  flex-direction: column;

  .addAdminFormContainer {
    margin: 1rem 0;
    width: 70%;
    background-color: #fff;
    box-shadow: -4px 25px 40px -30px rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    //color: #344767;

    .title {
      padding: 0 1.2rem;
      padding-top: 1.5rem;
      margin-bottom: 0.8rem;

      h3 {
        font-size: 1.3rem;
        font-weight: 500;
        opacity: 0.98;
      }
    }

    .desc {
      padding: 0 1.2rem;

      p {
        font-size: 0.9rem;
        opacity: 0.7;
      }
    }

    .adminFormContainer {
      padding: 1.2rem;
      width: 100%;

      .labelFields {
        margin: 0.8rem 0.5rem;

        label {
          font-size: 1.1rem;
          font-weight: 500;
          //color: #344767;
        }

        .MuiInputBase-root {
          width: 100%;
        }

        .MuiAutocomplete-input {
          font-size: 0.9rem;
          color: #344767;
          opacity: 0.8;
        }
      }

      .buttons {
        margin: 1rem 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .btn {
          padding: 0.88rem 2rem;
          font-size: 0.8rem;
          font-weight: bold;
          text-transform: uppercase;
          outline: none;
          cursor: pointer;
          border-radius: 8px;
          border: none;
          box-shadow:
            rgba(0, 0, 0, 0.11) 0px 4px 7px -1px,
            rgba(0, 0, 0, 0.07) 0px 2px 4px -1px;

          &:hover {
            opacity: 0.85;
          }
        }

        .cancelBtn {
          background-color: #e9ecef;
          color: #344767;
        }

        .createBtn {
          background-image: linear-gradient(
            310deg,
            rgb(33, 82, 255),
            rgb(33, 212, 253)
          );
          color: #fff;
          margin-left: 1rem;
        }
      }
    }
  }
  .addAdminFormContainerForcompany {
    width: 100%;
    margin: 0;
    padding: 0;
  }
}
