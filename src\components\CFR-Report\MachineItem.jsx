import React, { useState } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import {
  FormControl,
  Select,
  InputLabel,
  MenuItem,
  IconButton,
  Checkbox,
  ListItemText,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import EventTable from "./EventTable";
import AlarmTable from "./AlarmTable";
import { useStateContext } from "../../context/ContextProvider";
import NoDataComponent from "../commons/noData.component";
import { commonRowStyle } from "../../layouts/machineData/MaintenanceReportDataMain";

const BootstrapInput = styled(Select)(({ theme }) => ({
  "& .MuiInputBase-input": {
    position: "relative",
    fontSize: 13,
    padding: "8px 18px",
  },
}));

const MODULE_LIST = [
  "AUDIT",
  "Issue Module",
  "Live data",
  "Maintenance",
  "Maintenance Report",
  //"SAT",
  "Training",
  "Machine",
];

function MachineItem(props) {
  const { machineDetails, data, load, cfrType, alert, alertLoad, mid } = props;
  const [expand, setExpand] = useState(false);
  const [filteredModule, setFilteredModule] = useState(MODULE_LIST);
  const [filteredModuleSet, setFilteredModuleSet] = useState(
    new Set(MODULE_LIST),
  );
  const { currentMode } = useStateContext();

  const handleFilter = (event) => {
    const {
      target: { value },
    } = event;
    const myValue = typeof value === "string" ? value.split(",") : value;
    setFilteredModule(myValue);
    setFilteredModuleSet(new Set(myValue));
    console.log("data module", data);
  };

  return (
    <section className="machine__item" style={commonRowStyle}>
      <div className="machine__item__header">
        <div className="machine__title">{machineDetails}</div>
        {/* <div className='data-time__change'>
                    17 March 2022 02:30PM
                </div> */}
        <div className="toggle__btn">
          <IconButton
            onClick={() => setExpand(!expand)}
            style={currentMode === "Dark" ? { color: "white" } : {}}
          >
            {expand ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </div>
      </div>
      {expand ? (
        cfrType === "events" ? (
          <div className="machine__item__main">
            <div className="actions" style={{ padding: 0 }}>
              <div
                className="filter"
                style={currentMode === "Dark" ? { color: "white" } : {}}
              >
                <span>Select Module: </span>
                <div>
                  <FormControl fullWidth>
                    <InputLabel htmlFor="modules">Modules</InputLabel>
                    <BootstrapInput
                      multiple
                      className="select-module"
                      name="modules"
                      value={filteredModule}
                      label="Modules"
                      onChange={handleFilter}
                      renderValue={(selected) => {
                        if (selected.length === MODULE_LIST.length)
                          return "All";
                        else return selected.join(",");
                      }}
                    >
                      {MODULE_LIST.map((module) => {
                        return (
                          <MenuItem
                            className="module-option"
                            key={module}
                            value={module}
                          >
                            <Checkbox
                              checked={filteredModule.indexOf(module) > -1}
                            />
                            <ListItemText primary={module} />
                          </MenuItem>
                        );
                      })}
                    </BootstrapInput>
                  </FormControl>
                </div>
              </div>
            </div>
            <div className="contentTable">
              {data && (
                <EventTable
                  machineDetails={mid}
                  data={data}
                  filteredModuleSet={filteredModuleSet}
                  load={load}
                />
              )}
              {!data && (
                <div className={"flex items-center justify-center"}>
                  <NoDataComponent useAtTable={false} />
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="machine__item__main">
            <div className="contentTable">
              {alert && (
                <AlarmTable
                  machineDetails={machineDetails}
                  mid={mid}
                  data={alert}
                  load={alertLoad}
                />
              )}
              {!alert && (
                <div className={"flex items-center justify-center"}>
                  <NoDataComponent useAtTable={false} />
                </div>
              )}
            </div>
          </div>
        )
      ) : (
        ""
      )}
    </section>
  );
}

export default MachineItem;
