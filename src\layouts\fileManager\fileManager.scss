.fileManager {
  width: 100%;
  display: "flex";
  .folderContainer,
  .fileContainer {
    // width: 75%;
    width: 100%;
    align-items: center;

    .infoHeaderContainer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1.5rem 0;

      .btnContainer {
        display: flex;
        align-items: center;
        justify-content: center;

        .addFolderBtn {
          margin-left: 1.2rem;
        }
        i {
          cursor: pointer;
          font-size: 1.1rem;
          opacity: 0.4;
          &:hover {
            opacity: 0.95;
          }
        }
      }
    }

    .foldersCardContainer,
    .filesCardContainer {
      margin-top: 2.5rem;
      padding: 0 2rem 2rem;
      width: 100%;
      align-items: center;
      justify-content: center;
      flex-flow: row wrap;
      .folderCard,
      .fileCard {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        // border-radius: 10px;
        // margin: 1rem 1rem;
        // margin-left: 0;
        // box-shadow: rgba(20, 20, 20, 0.12) 0px 1px 6px -1px,
        //   rgba(20, 20, 20, 0.07) 0px 2px 6px -1px;
        padding: 0.125rem 1rem;

        &:last-child {
          margin-right: 0;
        }

        .folderInfoContainer {
          width: "100%";
          display: flex;
          align-items: center;
          .folderImgContainer {
            margin-right: 1rem;
            user-select: none;
            img {
              user-select: none;
              //width: 80%;
              // height: 100%;
              cursor: pointer;
            }
          }
          .folderTitleContainer {
            padding: 0.8rem 0rem 0.8rem 0.2rem;
            text-align: left;
            font-size: 0.9rem;
            font-weight: 500;
            flex-wrap: wrap;
          }
        }

        .folderBtnContainer {
          width: "35%";
          padding-top: 1rem;
          padding-bottom: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .info {
            display: flex;
            flex-direction: column;
            margin-right: 1.2rem;
            h4 {
              font-size: 0.8rem;
              opacity: 0.9;
            }
            p {
              margin-top: 0.3rem;
              font-size: 0.75rem;
              opacity: 0.9;
            }
          }

          .btns {
            display: flex;
            align-items: center;
            .btn {
              cursor: pointer;
              padding: 0.3rem;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 5px;
              color: #fff;
              i {
                font-size: 0.8rem;
              }
            }
            .editBtn {
              background: #098def;
            }
            .deleteBtn {
              background: rgba(245, 29, 29, 0.884);
              margin-left: 0.6rem;
            }
          }
        }
      }
    }

    .noFilesOrFolders {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 1vh;
      padding-bottom: 1vh;
    }
  }

  // .folderPreviewContainer {
  //   position: sticky;
  //   top: 0;
  //   height: calc(100vh - 1rem);
  //   border-top-right-radius: 10px;
  //   border-bottom-right-radius: 10px;
  //   width: 25%;
  //   background: #fff;
  //   padding: 0 2rem;

  //   .infoHeaderContainer {
  //     display: flex;
  //     align-items: center;
  //     justify-content: space-between;
  //     padding: 1.5rem 0;
  //     .title {
  //       display: flex;
  //       align-items: center;
  //       .icon {
  //         padding-top: 0.25rem;
  //         i {
  //           font-size: 1rem;
  //           opacity: 0.5;
  //           margin-right: 0.8rem;
  //         }
  //       }
  //       h4 {
  //         color: #000;
  //         font-weight: 500;
  //         opacity: 0.8;
  //         font-size: 0.97rem;
  //       }
  //     }
  //   }

  //   .filePreviewInfoContainer {
  //     display: flex;
  //     flex-direction: column;
  //     padding: 1.5rem 0;
  //     .fileIcon {
  //       display: flex;
  //       align-items: center;
  //       justify-content: center;
  //       height: 25%;
  //       user-select: none;
  //       img {
  //         user-select: none;
  //         width: 100%;
  //         height: 100%;
  //       }
  //     }

  //     .fileTitle {
  //       font-size: 0.9rem;
  //       font-weight: 500;
  //     }
  //     .fileDesc {
  //       font-size: 0.75rem;
  //       font-weight: 400;
  //       padding-bottom: 1rem;
  //     }
  //   }

  //   .fileDescContainer {
  //     padding: 1.5rem 0;
  //     .fileDescTitle {
  //       font-size: 0.9rem;
  //       font-weight: 500;
  //       padding-bottom: 0.7rem;
  //     }
  //     .fileDesc {
  //       font-size: 0.78rem;
  //       font-weight: 400;
  //     }
  //   }

  //   .fileBtnContainer {
  //     padding-top: 3rem;
  //     .btns {
  //       display: flex;
  //       align-items: center;
  //       justify-content: center;
  //       .btn {
  //         padding: 0.5rem 0.7rem;
  //         background-color: #098def;
  //         margin: 0 2rem;
  //         color: #fff;
  //         border-radius: 8px;
  //         cursor: pointer;
  //       }
  //     }
  //   }
  // }
}
