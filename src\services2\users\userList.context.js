import axios from "axios";
import { createContext, useEffect, useState } from "react";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { firebaseLooper } from "../../tools/tool";
import { usersData } from "../../utils/Users";

export const UserListContext = createContext();

const UserListProvider = ({ children }) => {
  const [usersInView, setUsersInView] = useState([]);
  const [numberOfUsersInView, setNumberOfUsersInView] = useState(6);
  const [page, setPage] = useState(1);
  const [users, setUsers] = useState([]);
  const [usersInViewContainerCount, setUsersInViewContainerCount] = useState(0); // number of pages
  const { refreshCount, setRefreshCount } = useMongoRefresh();

  const usersPageInfoContainerCounter = (
    allUsersCount,
    numberOfUserInViewProp,
  ) => {
    var temp = Math.ceil(allUsersCount / numberOfUserInViewProp);
    setUsersInViewContainerCount(temp);
    return temp;
  };

  const fetchAllUsers = async () => {
    await axios.get(`${dbConfig.url}/users`).then((response) => {
      const data = response.data?.data;

      let userActivePageNum = window.localStorage.getItem("userActivePageNum"); // for preventing pagination mismatch while editing 2/3
      let numOfUserInViewSession =
        window.localStorage.getItem("numOfUserInView"); //
      setUsers(data?.reverse());
      setUsersInView(data.slice(0, numberOfUsersInView));
      usersPageInfoContainerCounter(data.length, numberOfUsersInView);
      // for preventing pagination mismatch while editing 3/3
      if (userActivePageNum != 1 && userActivePageNum) {
        let machineInViewContainerCountTemp = users(
          data.length,
          numOfUserInViewSession,
        ); //if it is last page
        if (userActivePageNum == machineInViewContainerCountTemp) {
          setUsersInView(
            data.slice((userActivePageNum - 1) * numOfUserInViewSession),
          );
          setNumberOfUsersInView(numOfUserInViewSession);
          setPage(userActivePageNum);
          setNumberOfUsersInView(numOfUserInViewSession);
        } else {
          // if it's not the last page
          setUsersInView(
            data.slice(
              (userActivePageNum - 1) * numOfUserInViewSession,
              userActivePageNum * numOfUserInViewSession,
            ),
          );
          setNumberOfUsersInView(numOfUserInViewSession);
          setPage(userActivePageNum);
          setNumberOfUsersInView(numOfUserInViewSession);
        }
      }
    });
  };

  useEffect(() => {
    fetchAllUsers();
  }, [refreshCount]);

  return (
    <UserListContext.Provider
      value={{
        usersInView,
        setUsersInView,
        numberOfUsersInView,
        setNumberOfUsersInView,
        page,
        setPage,
        users,
        setUsers,
        usersInViewContainerCount,
        setUsersInViewContainerCount,
        usersPageInfoContainerCounter,
      }}
    >
      {children}
    </UserListContext.Provider>
  );
};

export default UserListProvider;
