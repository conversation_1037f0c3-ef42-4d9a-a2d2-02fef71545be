import {
  <PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>le,
  Box,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputLabel,
  Menu,
  MenuItem,
  TextField,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import fileFrom from "../../assets/images/analytics.svg";
import { toastMessage, toastMessageSuccess } from "../../tools/toast";
import RemoveCircleIcon from "@mui/icons-material/RemoveCircle";
import Delete from "../../components/Delete/Delete";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useStateContext } from "../../context/ContextProvider";
import { companies, companyId_constant } from "../../constants/data";
import { ButtonBasic } from "../../components/buttons/Buttons";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import imageIcon from "../../assets/images/image.png";
import videoIcon from "../../assets/images/video.png";
import audioIcon from "../../assets/images/audio.png";
import pdfIcon from "../../assets/images/pdf-file.png";
import docxIcon from "../../assets/images/docx.png";
import spreadsheetIcon from "../../assets/images/spreadsheet.svg";
import pptxIcon from "../../assets/images/pptx.png";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { Viewer, Worker } from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import { triggerBase64Download } from "common-base64-downloader-react";
import { sharedCss } from "../../styles/sharedCss";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import DownloadIcon from "@mui/icons-material/Download";
import FileSaver from "file-saver";
import { useCheckAccess } from "../../utils/useCheckAccess";

// ... (other imports and code remain unchanged)

const FileCard = ({ grid, file, readOnly }) => {
  const [name, setName] = useState(file.name);
  const [openRename, setOpenRename] = useState(false);
  const [openDel, setOpenDel] = useState(false);
  const [openPreview, setOpenPreview] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const defaultLayoutPluginInstance = defaultLayoutPlugin();
  const hasFileDELETEAccess = useCheckAccess("files", "DELETE");
  const hasFilePUTAccess = useCheckAccess("files", "PUT");

  const createdAt = file.created_at;
  const createdDate = new Date(createdAt);
  const formattedDate = createdDate.toLocaleDateString("en-US", {
    weekday: "short",
    year: "numeric",
    month: "short",
    day: "numeric",
  });

  useEffect(() => {
    setName(file.name);
  }, [file.name, openRename]);

  const style = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: 400,
    bgcolor: "background.paper",
    boxShadow: 24,
    border: "none",
    borderRadius: "8px",
    p: 4,
  };

  function getPreview(format) {
    if (format === "image") {
      return (
        <img
          style={{ width: "80vw", height: "80vh", objectFit: "contain" }}
          title={`${dbConfig?.url_storage}/${file.url}`}
          src={`${dbConfig?.url_storage}/${file.url}`}
        />
      );
    } else if (format === "video") {
      return (
        <video
          style={{ width: "80vw", height: "80vh", objectFit: "contain" }}
          controls
          src={`${dbConfig?.url_storage}/${file.url}`}
          alt="First slide"
        />
      );
    } else if (format === "audio") {
      return (
        <audio
          style={{
            width: "80vw",
            height: "40vh",
          }}
          controls
        >
          <source src={`${dbConfig?.url_storage}/${file.url}`} />
        </audio>
      );
    } else if (format === "pdf" || format === "application/pdf") {
      return (
        <Box sx={{ width: "80vw", height: "80vh", mt: 2 }}>
          <iframe
            src={`${dbConfig?.url_storage}/${file.url}`}
            style={{
              width: "100%",
              height: "100%",
              border: "none",
              borderRadius: "5px",
            }}
            title={`${file.name} Preview`}
          />
        </Box>
      );
    } else {
      const documentFileMap = {
        "application/pdf": "PDF",
        "application/msword": "Document",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
          "Document",
        "application/vnd.ms-excel": "Spreadsheet",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
          "Spreadsheet",
        "application/vnd.ms-powerpoint": "Presentation",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation":
          "Presentation",
      };

      return (
        <Card sx={{ mt: "12%", bgcolor: currentMode === "Dark" ? "#333" : "#fff" }}>
          <Alert>
            <AlertTitle>Please Download to View this file!</AlertTitle>
            {`${
              documentFileMap?.[format] || "Document"
            } files cannot be previewed. In order to preview, please download this file.`}
          </Alert>
          <Box>
            <Button
              onClick={() =>
                FileSaver.saveAs(
                  `${dbConfig?.url_storage}/${file.url}`,
                  file.name,
                )
              }
              sx={{ mt: 4 }}
              endIcon={<DownloadIcon />}
              fullWidth
              variant="outlined"
              color="inherit"
            >
              Download File
            </Button>
          </Box>
        </Card>
      );
    }
  }

  const FileTypeImage = ({ type, size }) => {
    const extensionIconMap = {
      image: imageIcon,
      video: videoIcon,
      audio: audioIcon,
      pdf: pdfIcon,
      doc: docxIcon,
      docx: docxIcon,
      xls: spreadsheetIcon,
      xlsx: spreadsheetIcon,
      ppt: pptxIcon,
      pptx: pptxIcon,
    };

    const mimeTypeIconMap = {
      "image/jpg": imageIcon,
      "image/png": imageIcon,
      "video/mp4": videoIcon,
      "video/mkv": videoIcon,
      "video/mov": videoIcon,
      "audio/mp3": audioIcon,
      "application/pdf": pdfIcon,
      "application/msword": docxIcon,
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        docxIcon,
      "application/vnd.ms-excel": spreadsheetIcon,
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        spreadsheetIcon,
      "application/vnd.ms-powerpoint": pptxIcon,
      "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        pptxIcon,
    };

    const getIconFromExtension = (fileType) => {
      const extension = fileType.split(".").pop().toLowerCase();
      return extensionIconMap[extension] || fileFrom;
    };

    const getIconFromMimeType = (mime) => {
      return mimeTypeIconMap[mime] || fileFrom;
    };

    const iconSrc = type.includes("application")
      ? getIconFromMimeType(type)
      : getIconFromExtension(type);

    return (
      <img
        style={{
          width: size,
          height: size,
          objectFit: "contain",
          display: "block",
        }}
        src={iconSrc}
        alt="File type icon"
      />
    );
  };

  const handleSave = async () => {
    if (name.trim() === "") {
      toastMessage({ message: "Input cannot be empty!" });
      return;
    }

    await axios
      .put(`${dbConfig.url}/files/${file._id}`, { ...file, name: name })
      .then(() => {
        toastMessageSuccess({ message: "File renamed successfully !" });
        setOpenRename(false);
        setRefreshCount(refreshCount + 1);
      });
  };

  const handleDelete = async () => {
    if (file?.url) {
      axios
        .post(`${dbConfig?.url_storage}/deleteImage`, { file_name: file?.url })
        .then((res1) => {
          console.log(res1.data.data?.message, "updated successfully");
        })
        .catch((err) => {
          console.log("delete file from storage err:", err);
        });
    }
    await axios.delete(`${dbConfig.url}/files/${file._id}`).then(() => {
      toastMessage({ message: "File deleted successfully !" });
      setOpenDel(false);
      setRefreshCount(refreshCount + 1);
    });
  };

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDeleteOption = () => {
    setOpenDel(true);
    handleMenuClose();
  };

  const commonCss = sharedCss();

  return (
    <>
      <div
        style={
          grid
            ? {
                width: "23%",
                margin: "1% 2% 1% 0%",
                borderRadius: "10px",
                overflow: "hidden",
              }
            : {
                justifyContent: "center",
                alignItems: "center",
                borderBottom:
                  currentMode === "Dark"
                    ? "1px solid white"
                    : "0.05rem solid #e0e0e0",
              }
        }
      >
        <section
          className={`folderCard ${commonCss.backgroundLight2} ${
            currentMode === "Dark" ? "hover:bg-slate-800" : "hover:bg-slate-100"
          }`}
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            padding: 0,
          }}
        >
          {grid ? (
            <div
              style={{
                width: "100%",
                display: "flex",
                alignItems: "center",
                padding: "0 0 0 1rem",
              }}
              className="folderBtnContainer"
            >
              <div
                style={{ flex: "0 1 auto" }}
                onClick={() => setOpenPreview(true)}
                className="folderInfoContainer cursor-pointer"
              >
                <div
                  className="folderImgContainer"
                  style={{
                    width: "30px",
                    height: "30px",
                    overflow: "hidden",
                  }}
                >
                  <FileTypeImage size="30px" type={file.file_type} />
                </div>
                <div
                  style={{
                    marginLeft: "0.5rem",
                    textAlign: "left",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    maxWidth: "150px",
                  }}
                  className="folderTitleContainer"
                >
                  {file?.name?.length > 9
                    ? `${file?.name.substring(0, 9)}...`
                    : file?.name}
                </div>
              </div>
              <div
                style={{
                  flex: "0 0 auto",
                  marginLeft: "auto",
                  paddingRight: "0.5rem",
                }}
              >
                {!readOnly && (
                  <>
                    <IconButton
                      onClick={handleMenuClick}
                      color="default"
                      variant="contained"
                      aria-label="File options"
                    >
                      <MoreVertIcon />
                    </IconButton>
                    <Menu
                      anchorEl={anchorEl}
                      open={Boolean(anchorEl)}
                      onClose={handleMenuClose}
                    >
                      <MenuItem
                        disabled={!hasFileDELETEAccess}
                        onClick={handleDeleteOption}
                      >
                        <DeleteIcon
                          style={{
                            fontSize: "20px",
                            color: "#f00",
                            marginRight: "8px",
                          }}
                        />
                        Delete
                      </MenuItem>
                      <MenuItem
                        onClick={() => {
                          handleMenuClose();
                          setOpenRename(true);
                        }}
                        disabled={!hasFilePUTAccess}
                      >
                        <EditIcon
                          style={{
                            fontSize: "20px",
                            marginRight: "8px",
                          }}
                        />
                        Edit
                      </MenuItem>
                    </Menu>
                  </>
                )}
              </div>
            </div>
          ) : (
            <>
              <div
                style={{ flex: 1, paddingLeft: "1rem" }}
                onClick={() => setOpenPreview(true)}
                className="folderInfoContainer cursor-pointer"
              >
                <div
                  className="folderImgContainer"
                  style={{
                    width: "25px",
                    height: "25px",
                    overflow: "hidden",
                  }}
                >
                  <FileTypeImage size="25px" type={file.file_type} />
                </div>
                <div
                  style={{
                    justifyContent: "flex-start",
                    marginLeft: "0.5rem",
                  }}
                  className="folderTitleContainer"
                >
                  {file?.name}
                </div>
              </div>
              <div
                style={{ flex: 1.5, display: "flex", justifyContent: "center" }}
              >
                <Typography variant="body2">{file?.creator}</Typography>
              </div>
              <div
                style={{ flex: 1, display: "flex", justifyContent: "center" }}
              >
                <Typography variant="body2">{formattedDate}</Typography>
              </div>
              <div
                style={{
                  flex: 1,
                  display: "flex",
                  justifyContent: "flex-end",
                  paddingRight: "1rem",
                }}
              >
                {!readOnly && (
                  <>
                    <IconButton
                      onClick={handleMenuClick}
                      color="default"
                      variant="contained"
                      aria-label="File options"
                    >
                      <MoreVertIcon />
                    </IconButton>
                    <Menu
                      anchorEl={anchorEl}
                      open={Boolean(anchorEl)}
                      onClose={handleMenuClose}
                    >
                      <MenuItem
                        onClick={handleDeleteOption}
                        disabled={!hasFileDELETEAccess}
                      >
                        <DeleteIcon
                          style={{
                            fontSize: "20px",
                            color: "#f00",
                            marginRight: "8px",
                          }}
                        />
                        Delete
                      </MenuItem>
                      <MenuItem
                        onClick={() => {
                          handleMenuClose();
                          setOpenRename(true);
                        }}
                        disabled={!hasFilePUTAccess}
                      >
                        <EditIcon
                          style={{
                            fontSize: "20px",
                            marginRight: "8px",
                          }}
                        />
                        Edit
                      </MenuItem>
                    </Menu>
                  </>
                )}
              </div>
            </>
          )}

          <Dialog
            PaperProps={{
              style: {
                backgroundColor: "#000", // Black background for media preview
                boxShadow: "none",
                width: "100%",
                maxWidth: "80vw",
                height: "100%",
                maxHeight: "95vh",
                borderRadius: "5px",
                overflow: "hidden",
              },
            }}
            fullScreen
            open={openPreview}
            onClose={() => setOpenPreview(false)}
          >
            <DialogTitle
              sx={{
                bgcolor: currentMode === "Dark" ? "#1a1a1a" : "#f5f5f5",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                padding: "16px 24px",
                borderBottom:
                  currentMode === "Dark"
                    ? "1px solid #333"
                    : "1px solid #e0e0e0",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <IconButton
                  onClick={() => setOpenPreview(false)}
                  sx={{
                    color: currentMode === "Dark" ? "#fff" : "#000",
                    "&:hover": {
                      bgcolor:
                        currentMode === "Dark"
                          ? "rgba(255,255,255,0.1)"
                          : "rgba(0,0,0,0.1)",
                    },
                  }}
                >
                  <ArrowBackIcon />
                </IconButton>
                <FileTypeImage size="40px" type={file.file_type} />
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    color: currentMode === "Dark" ? "#fff" : "#000",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    maxWidth: { xs: "200px", sm: "400px", md: "600px" },
                  }}
                >
                  {file.name}
                </Typography>
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                {(file?.file_type === "image" ||
                  file?.file_type === "video" ||
                  file?.file_type === "audio") && (
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={() =>
                      FileSaver.saveAs(
                        `${dbConfig?.url_storage}/${file.url}`,
                        file.name,
                      )
                    }
                    sx={{
                      color: currentMode === "Dark" ? "#fff" : "#000",
                      borderColor: currentMode === "Dark" ? "#fff" : "#000",
                      "&:hover": {
                        borderColor: currentMode === "Dark" ? "#ccc" : "#333",
                        bgcolor:
                          currentMode === "Dark"
                            ? "rgba(255,255,255,0.1)"
                            : "rgba(0,0,0,0.1)",
                      },
                    }}
                  >
                    Download
                  </Button>
                )}
                <IconButton
                  onClick={() => setOpenRename(true)}
                  disabled={!hasFilePUTAccess}
                  sx={{
                    color: currentMode === "Dark" ? "#fff" : "#000",
                    "&:hover": {
                      bgcolor:
                        currentMode === "Dark"
                          ? "rgba(255,255,255,0.1)"
                          : "rgba(0,0,0,0.1)",
                    },
                  }}
                >
                  <EditIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent
              sx={{
                bgcolor: "#000", // Black background for media content
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                padding: '5px',
                overflow: "auto",
              }}
            >
              {getPreview(file.file_type)}
            </DialogContent>
          </Dialog>

          <Dialog open={openRename} onClose={() => setOpenRename(false)}>
            <DialogContent>
              <div className="w-full">
                <Typography
                  sx={
                    currentMode === "Dark"
                      ? { color: "white" }
                      : { color: "black" }
                  }
                  gutterBottom
                >
                  File Name:
                </Typography>
                <TextField
                  size="small"
                  type="text"
                  fullWidth
                  placeholder="eg. File 1"
                  required
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                />
              </div>
            </DialogContent>
            <DialogActions>
              <Button
                size="medium"
                variant="contained"
                color="error"
                onClick={() => setOpenRename(false)}
              >
                Close
              </Button>
              <ButtonBasic
                onClick={() => handleSave()}
                style={{ marginLeft: "20px" }}
                buttonTitle="Update"
              />
            </DialogActions>
          </Dialog>
          <Dialog open={openDel} onClose={() => setOpenDel(false)}>
            <Delete
              onClose={() => setOpenDel(false)}
              onDelete={() => handleDelete()}
            />
          </Dialog>
        </section>
      </div>
    </>
  );
};

export default FileCard;