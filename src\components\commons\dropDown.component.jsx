import { memo } from "react";
import { Select, MenuItem, FormControl, InputLabel } from "@mui/material";

/**
 * CommonDropDown Component
 *
 * A reusable dropdown component providing flexibility for customization and can be used to render dynamic menu items.
 *
 * @component
 *
 * @param {Object} props - The properties passed to the component.
 * @param {string} [props.dropDownLabel=''] - The label text for the dropdown.
 * @param {Array<Object>} [props.menuData=[]] - Array of menu items to populate the dropdown. Each item is expected to be an object.
 * @param {string | number} [props.menuValue=''] - The currently selected value of the dropdown.
 * @param {string | number} [props.menuValueDefault=''] - The default selected value for the dropdown (used for uncontrolled components).
 * @param {string} [props.menuItemDisplay=''] - The key of the object in `menuData` that will be displayed as the label for each menu item.
 * @param {string} [props.menuItemValue=''] - The key of the object in `menuData` that will be used as the value for each menu item.
 * @param {Object} [props.dropDownContainerStyle={}] - Custom styles for the dropdown's container (applies to `FormControl`).
 * @param {Function} [props.handleClick=()=>{}] - Callback function triggered on clicking the dropdown.
 * @param {Function} [props.handleChange=()=>{}] - Callback function triggered when the value of the dropdown changes.
 * @param {Function} [props.handleBlur=()=>{}] - Callback function triggered when the dropdown loses focus.
 * @param {string} [props.dropDownSize="small"] - Size of the dropdown (supports Material-UI's sizes like "small", "medium").
 *
 * @returns {JSX.Element} A dropdown component with dynamic menu items and an "All" option.
 *
 * @example
 * const menuData = [
 *   { id: 1, name: "Option 1" },
 *   { id: 2, name: "Option 2" },
 * ];
 *
 * <CommonDropDown
 *   dropDownLabel="Select an option"
 *   menuData={menuData}
 *   menuValue={1}
 *   menuItemDisplay="name"
 *   menuItemValue="id"
 *   handleChange={(event) => console.log(event.target.value)}
 * />
 */
const CommonDropDown = ({
  dropDownLabel = "",
  menuData = [],
  menuValue = "",
  menuValueDefault = "",
  menuItemDisplay = "",
  menuItemValue = "",
  dropDownContainerStyle = {},
  handleClick = () => {},
  handleChange = () => {},
  handleBlur = () => {},
  dropDownSize = "small",
  className,
}) => {
  return (
    <FormControl
      style={dropDownContainerStyle}
      size={dropDownSize}
      variant={"outlined"}
      className={className}
    >
      {!!dropDownLabel && (
        <InputLabel id={`${dropDownLabel?.toString()}-id-`}>
          {dropDownLabel}
        </InputLabel>
      )}
      <Select
        onClick={handleClick}
        onChange={handleChange}
        onBlur={handleBlur}
        labelId={`${dropDownLabel?.toString()}-id-`}
        label={dropDownLabel?.toString()}
        value={menuValue ?? ""}
        defaultValue={menuValueDefault}
        fullWidth
      >
        {menuData &&
          Array.isArray(menuData) &&
          menuData?.map((menuData, index) => (
            <MenuItem
              key={menuData?.id ?? menuData?._id ?? index}
              value={menuData[menuItemValue]}
            >
              {menuData[menuItemDisplay]}
            </MenuItem>
          ))}
      </Select>
    </FormControl>
  );
};

export default memo(CommonDropDown);
