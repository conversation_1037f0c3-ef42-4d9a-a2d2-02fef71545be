import React, { createContext, useState } from "react";
import { toastMessageSuccess } from "../../tools/toast";

export const FileManagerSelectorContext = createContext();

const FileManagerContextProvider = ({ children }) => {
  const [fileUrl, setFileUrl] = useState(null);

  const changeFileUrl = (url) => {
    setFileUrl(url);
    toastMessageSuccess({ message: "File changed and ready to upload" });
  };

  return (
    <FileManagerSelectorContext.Provider value={{ fileUrl, changeFileUrl }}>
      {children}
    </FileManagerSelectorContext.Provider>
  );
};

export default FileManagerContextProvider;
