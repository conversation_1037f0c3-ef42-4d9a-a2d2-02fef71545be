import "./addUserForm.scss";

import { alpha, styled } from "@mui/material/styles";
import InputBase from "@mui/material/InputBase";
import InputLabel from "@mui/material/InputLabel";
import { Link } from "react-router-dom";
import { Avatar, Box, MenuItem, Select, TextField } from "@mui/material";
import Autocomplete, { createFilterOptions } from "@mui/material/Autocomplete";
import { useContext, useEffect, useState } from "react";
import emailjs from "@emailjs/browser";
import axios from "axios";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import { db } from "../../firebase";
import { useStateContext } from "../../context/ContextProvider";
import { firebaseLooper } from "../../tools/tool";
import { AddUserContext } from "../../services2/users/addUser.context";
import { useAuth } from "../../hooks/AuthProvider";
import { convertBase64 } from "../../hooks/useBase64";
import { sharedCss } from "../../styles/sharedCss";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useNavigate } from "react-router-dom";
///

const BootstrapInput = styled(InputBase)(({ theme }) => ({
  "label + &": {
    marginTop: theme.spacing(0),
  },
  "& .MuiInputBase-input": {
    borderRadius: 4,
    position: "relative",
    backgroundColor: theme.palette.mode === "light" ? "#fff" : "#161C24",
    border:
      theme.palette.mode === "light"
        ? "1px solid #c4c4c4"
        : "1px solid rgb(76,81,87)",
    fontSize: 14,
    padding: "10px 12px",
    transition: theme.transitions.create([
      "border-color",
      "background-color",
      "box-shadow",
    ]),
    "&:hover": {
      border:
        theme.palette.mode === "light"
          ? "1px solid #000"
          : "1px solid rgb(229, 231, 235)",
    },
    "&:focus": {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      borderColor: theme.palette.primary.main,
    },
  },
}));

// Roles options dropdown menu
const roles = [
  { role: "Admin" },
  { role: "Operator" },
  { role: "Supervisor" },
  { role: "Trainee" },
  { role: "Validator" },
];

const filterOptions = createFilterOptions({
  matchFrom: "start",
  stringify: (option) => option.role,
});

const AddUserForm = ({ companyIdProp }) => {
  //console.log("companyId:" , companyIdProp);
  const { currentColor, currentMode } = useStateContext();
  const { emailMatches, setEmailMatches, handleEmail, sendEmail } =
    useContext(AddUserContext);
  const [filesizeAlert, setFilesizeAlert] = useState(false);
  const [userDetails, setUserDetails] = useState({
    fname: "",
    lname: "",
    email: "",
    avatar: "",
    username: "",
    role: "",
    phone: "",
    password: "",
    themecolor: "",
  });
  const history = useNavigate();
  const { signup } = useAuth();
  const router = useNavigate();
  const types = ["image/png", "image/jpeg", "image/jpg"];

  const handleSubmit = async (e) => {
    e.preventDefault();
    const phoneNo = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;

    if (userDetails?.phone.length !== 10) {
      toastMessageWarning({ message: "Phone No must be 10 digits" });
      return; // Stop the function here
    } else {
      axios.post(`${dbConfig.url}/users`, userDetails).then(() => {
        toastMessageSuccess({ message: "User has been added successfully!" });
        setUserDetails({
          fname: "",
          lname: "",
          email: "",
          avatar: "",
          username: "",
          role: "",
          phone: "",
          password: "",
          themecolor: "",
        });
        history("/users");
      });
    }
  };
  const handleChange = async (e) => {
    let selectedFile = e.target.files[0];
    if (selectedFile.size <= 10 * 1024 * 1024) {
      setFilesizeAlert(false);

      const base64 = await convertBase64(selectedFile);
      if (selectedFile) {
        if (types.includes(selectedFile.type)) {
          setUserDetails({ ...userDetails, avatar: base64 });
        } else {
          toastMessage({ message: "Please select an image file (png or jpg)" });
        }
      }
    } else {
      setFilesizeAlert(true);
      setUserDetails({
        ...userDetails,
        avatar: "",
      });
    }
  };
  const commonCss = sharedCss();
  const isUsernameEmpty = userDetails.username.trim() === "";
  return (
    <section className="addUserForm">
      <div className={`addUserFormContainer ${commonCss.backgroundLight} `}>
        <div className="title">
          <h3>Invite New User</h3>
        </div>
        <div className="desc">
          <p>Mandatory information</p>
        </div>

        <div className="userFormContainer">
          <form onSubmit={handleSubmit}>
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: { sm: "1fr 1fr" },
                gap: 2,
              }}
            >
              <div className="labelFields">
                <InputLabel shrink htmlFor="userFirstName">
                  First Name
                </InputLabel>
                <TextField
                  // label="First Name"
                  size="small"
                  fullWidth
                  required
                  value={userDetails?.fname}
                  onChange={(e) => {
                    setUserDetails({
                      ...userDetails,
                      fname: e.target.value,
                    });
                  }}
                  placeholder="eg. Michael"
                />
              </div>

              <div className="labelFields">
                <InputLabel shrink htmlFor="userLastName">
                  Last Name
                </InputLabel>
                <TextField
                  // label="Last Name"
                  size="small"
                  fullWidth
                  required
                  value={userDetails?.lname}
                  onChange={(e) => {
                    setUserDetails({
                      ...userDetails,
                      lname: e.target.value,
                    });
                  }}
                  placeholder="eg. Simpson"
                />
              </div>

              <div className="labelFields">
                <InputLabel shrink htmlFor="username">
                  User Name
                </InputLabel>
                <TextField
                  // label="User Name"
                  size="small"
                  fullWidth
                  value={userDetails?.username}
                  required
                  onChange={(e) => {
                    setUserDetails({
                      ...userDetails,
                      username: e.target.value,
                    });
                  }}
                  placeholder="eg. michael123"
                />
              </div>

              <div className="labelFields">
                <InputLabel shrink htmlFor="username">
                  Password
                </InputLabel>
                <TextField
                  // label="Password"
                  size="small"
                  type="password"
                  fullWidth
                  value={userDetails?.password}
                  required
                  onChange={(e) => {
                    setUserDetails({
                      ...userDetails,
                      password: e.target.value,
                    });
                  }}
                  placeholder="Password"
                />
              </div>

              <div className="labelFields">
                <InputLabel shrink htmlFor="userEmail">
                  {!emailMatches && (
                    <span className={emailMatches ? "text-red-600" : ""}>
                      Email Address
                    </span>
                  )}
                  {emailMatches && (
                    <span
                      className={
                        emailMatches ? "text-red-600 animate-pulse pl-4" : ""
                      }
                    >
                      Email Already exist
                    </span>
                  )}
                </InputLabel>
                <TextField
                  size="small"
                  fullWidth
                  value={userDetails?.email}
                  required
                  type="email"
                  onChange={(e) => {
                    setUserDetails({
                      ...userDetails,
                      email: e.target.value,
                    });
                  }}
                  placeholder="eg. <EMAIL>"
                  name="to_email"
                />
              </div>

              <div className="labelFields">
                <InputLabel shrink htmlFor="userMobile">
                  Phone Number
                </InputLabel>
                <TextField
                  // label="Phone Number"
                  size="small"
                  fullWidth
                  required
                  value={userDetails?.phone}
                  inputProps={{
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    maxLength: 10,
                  }}
                  onChange={(e) => {
                    setUserDetails({
                      ...userDetails,
                      phone: e.target.value,
                    });
                  }}
                  placeholder="eg. 8435973143"
                />
              </div>

              <div className="labelFields">
                <Avatar src={userDetails.avatar} />
                <InputLabel shrink htmlFor="userImg">
                  Upload Image
                </InputLabel>
                <input
                  type="file"
                  className="form-control"
                  onChange={handleChange}
                  required
                  accept="image/*"
                ></input>

                {/* {filesizeAlert && <span>Max File Size is 100MB</span>} */}
              </div>

              <div className="labelFields">
                <InputLabel shrink htmlFor="userRole">
                  Select Role
                </InputLabel>
                <Select
                  required
                  value={userDetails?.role}
                  onChange={(e) =>
                    setUserDetails({ ...userDetails, role: e.target.value })
                  }
                  size="small"
                  fullWidth
                >
                  {roles.map((data) => (
                    <MenuItem key={data.role} value={data.role.toLowerCase()}>
                      {" "}
                      {data.role}{" "}
                    </MenuItem>
                  ))}
                </Select>
              </div>
              {/* <div className="labelFields">
              <InputLabel shrink htmlFor="userUrl">
                Invite URL
              </InputLabel>
              <TextField name="invite_url" size="small" fullWidth disabled value={userDetails.invite_url} />
            </div> */}
            </Box>

            <div className="buttons">
              <Link to={companyIdProp ? "/admins" : "/users"}>
                <button className="cancelBtn btn">Cancel </button>
              </Link>
              <button
                type="submit"
                disabled={isUsernameEmpty}
                className="createBtn btn"
                style={{ background: currentColor }}
              >
                Add User
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default AddUserForm;
