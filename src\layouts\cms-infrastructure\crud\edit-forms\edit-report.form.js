import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import {
  addDataToReports,
  updateDataToReports,
} from "../functions/cms-infra.functions";
import { useContext, useState } from "react";
import { CmsInfraContext } from "../../../../services/cms-infrastructure/cms-infra.context";

const EditReportForm = ({ item, closeEdit }) => {
  const { instruments, cmsData } = useContext(CmsInfraContext);
  const [currentItem, setCurrentItem] = useState(item);
  const [code, setCode] = useState("");
  const [ecode, setECode] = useState("");

  async function handleOnSubmit(e) {
    e.preventDefault();

    const formData = {
      ...currentItem,
      sap_master_key: code.length === 0 ? currentItem?.sap_master_key : code,
      date: new Date(),
      eqp_master_key: ecode.length === 0 ? currentItem?.eqp_master_key : ecode,
    };

    Array.from(e.currentTarget.elements).forEach((field) => {
      if (!field.name) return;
      formData[field.name] = field.value;
    });

    try {
      updateDataToReports(formData);
      closeEdit();
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <div>
      <form onSubmit={handleOnSubmit}>
        <TextField
          defaultValue={currentItem?.cal_p_no}
          sx={{ mb: 2 }}
          placeholder="Calibration Procedure Number"
          fullWidth
          variant="outlined"
          name="cal_p_no"
        />
        <TextField
          defaultValue={currentItem?.calibrated_by}
          sx={{ mb: 2 }}
          placeholder="Calibrated By"
          fullWidth
          variant="outlined"
          name="calibrated_by"
        />
        <TextField
          defaultValue={currentItem?.created_by}
          sx={{ mb: 2 }}
          placeholder="Created By"
          fullWidth
          variant="outlined"
          name="created_by"
        />
        <TextField
          defaultValue={currentItem?.criticality}
          sx={{ mb: 2 }}
          placeholder="Criticality"
          fullWidth
          variant="outlined"
          name="criticality"
        />
        <TextField
          defaultValue={currentItem?.remarks}
          sx={{ mb: 2 }}
          placeholder="Remarks"
          fullWidth
          variant="outlined"
          name="remarks"
        />
        <FormControl
          defaultValue={currentItem?.sap_master_key}
          sx={{ mb: 3 }}
          fullWidth
        >
          <InputLabel>Select Instrument</InputLabel>
          <Select
            defaultValue={currentItem?.sap_master_key}
            label="Select Instrument"
            onChange={(e) => setCode(e.target.value)}
          >
            {instruments.map((item, i) => (
              <MenuItem value={item?.id} key={i}>
                {item?.code_no}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl
          defaultValue={currentItem?.eqp_master_key}
          sx={{ mb: 3 }}
          fullWidth
        >
          <InputLabel>Select Equipment</InputLabel>
          <Select
            defaultValue={currentItem?.eqp_master_key}
            label="Select Instrument"
            onChange={(e) => setECode(e.target.value)}
          >
            {cmsData.map((item, i) => (
              <MenuItem value={item?.id} key={i}>
                {item?.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Button type="submit" variant="contained" fullWidth>
          Submit
        </Button>
      </form>
    </div>
  );
};

export default EditReportForm;
