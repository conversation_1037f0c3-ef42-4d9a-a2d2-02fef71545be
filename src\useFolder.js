import { useMongoRefresh } from "./services/mongo/refresh/context"; // Import useMongoRefresh

const useFolder = () => {
  const { refreshCount, setRefreshCount } = useMongoRefresh(); // Destructure refreshCount and setRefreshCount

  console.log("useFolder is using refreshCount:", refreshCount); // Debug log for refreshCount

  // Example logic
  const refreshFolder = () => {
    console.log("refreshFolder is called"); // Debug log for refreshFolder
    setRefreshCount((prev) => {
      console.log("Previous refreshCount:", prev); // Debug log for previous refreshCount
      const newCount = prev + 1;
      console.log("New refreshCount:", newCount); // Debug log for new refreshCount
      return newCount;
    });
  };

  return { refreshCount, refreshFolder };
};

export default useFolder;
