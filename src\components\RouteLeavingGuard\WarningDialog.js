import React from "react";

const WarningDialog = ({
  titleText,
  contentText,
  cancelButtonText,
  confirmButtonText,
  onCancel,
  onConfirm,
}) => {
  // const { titleText, contentText, cancelButtonText, confirmButtonText, onCancel, onConfirm } = props;
  return (
    <div>
      <div className="w-full  max-w-lg p-5 relative mx-auto my-auto rounded-xl shadow-lg  bg-white ">
        <div className="">
          <div className="text-center p-5 flex-auto justify-center">
            <h2 className="text-xl font-bold py-4 ">{titleText}</h2>
            <p className="text-sm text-gray-500 px-8">{contentText}</p>
          </div>

          <div className="p-3  mt-2 text-center space-x-4 md:block">
            <button
              onClick={() => onCancel()}
              className="mb-2 md:mb-0 bg-white px-5 py-2 text-sm shadow-sm font-medium tracking-wider border text-gray-600 rounded-full hover:shadow-lg hover:bg-gray-100"
            >
              {cancelButtonText}
            </button>
            <button
              onClick={() => onConfirm()}
              className="mb-2 md:mb-0 bg-red-500 border border-red-500 px-5 py-2 text-sm shadow-sm font-medium tracking-wider text-white rounded-full hover:shadow-lg hover:bg-red-600"
            >
              {confirmButtonText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WarningDialog;
