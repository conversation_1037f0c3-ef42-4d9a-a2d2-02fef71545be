import React, { useState, useContext, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import "./Header.scss";
import NotificationBox from "../NotificationBox/NotificationBox";
import Avatar from "@mui/material/Avatar";
import AccountCircleRoundedIcon from "@mui/icons-material/AccountCircleRounded";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import PersonAdd from "@mui/icons-material/PersonAdd";
import Settings from "@mui/icons-material/Settings";
import Logout from "@mui/icons-material/Logout";
import { ThemeContext } from "../../context/ThemeContext";
import { ReactComponent as MoonIcon } from "../../icons/moon.svg";
import { ReactComponent as SunIcon } from "../../icons/sun.svg";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useUserActivity } from "../../hooks/cfr/userCfrProvider";
import {
  adminType_constant_temp,
  alluser,
  companies,
  companyId_constant,
} from "../../constants/data";
import { useAuth } from "../../hooks/AuthProvider";
import { firebaseLooper } from "../../tools/tool";
import { toastMessage } from "../../tools/toast";
import UserSessionLog from "../CFR-Report/UserSessionLog";
import { useStateContext } from "../../context/ContextProvider";
import ThemeSettings from "../ThemeSettings";
import NotificationsIcon from "@mui/icons-material/Notifications";
import { Box, IconButton } from "@mui/material";
import { RgbaColorPicker } from "react-colorful";

const Header = ({ setInactive, inactive }) => {
  const userActivity = useUserActivity();
  const [openNotification, setOpenNotification] = useState(false);
  const { currentUser, logout } = useAuth();
  const id = currentUser._id;
  const [anchorEl, setAnchorEl] = React.useState(null);
  const history = useNavigate();
  const open = Boolean(anchorEl);
  const [anchor2, setAnchor2] = useState(null);
  const openNotifications = Boolean(anchor2);
  const [user, setUser] = useState([]);
  const {
    setCurrentColor,
    setCurrentMode,
    currentMode,
    activeMenu,
    currentColor,
    themeSettings,
    setThemeSettings,
    setMode,
  } = useStateContext();
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleClickNotify = (event) => {
    setAnchor2(event.currentTarget);
  };
  const handleCloseNotify = () => {
    setAnchor2(null);
  };

  const handleNotification = (data) => {
    if (data == "dashboard") {
      setOpenNotification(false);
    } else {
      setOpenNotification(!openNotification);
    }
  };

  const location = useLocation();
  const pathName = location.pathname;

  console.log(pathName.substring(1).split("/"));
  // const newPathName = pathName.replace(/\\|\//g, '');
  const [newPathName, setNewPathName] = useState(pathName.substring(1));
  // theme switchers

  const getMachineData = async () => {
    await axios
      .get(`${dbConfig.url}/machines/${pathName.substring(1).split("/")[1]}`)
      .then((response) => {
        console.log("pathName", pathName);
        console.log("response", response);
        console.log("title", pathName.substring(1).split("/")[1]);
        setNewPathName(response?.data?.data?.title);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  const getNodeData = async (nodeId) => {
    try {
      const response = await axios.get(
        `${dbConfig.url}/IssueModule/nodes/${nodeId}`,
      );
      console.log("Node response", response?.data?.data?.name);
      setNewPathName(response?.data?.name); // Set node name
    } catch (error) {
      console.error("Error fetching node data:", error);
    }
  };

  const getProjectData = async (projectId) => {
    try {
      const response = await axios.get(
        `${dbConfig.url}/IssueModule/projects/${projectId}`,
      );
      console.log("Node response", response?.data?.data?.name);
      setNewPathName(response?.data?.name); // Set node name
    } catch (error) {
      console.error("Error fetching node data:", error);
    }
  };

  useEffect(() => {
    let headerpath = pathName.split("/");
    headerpath.shift(); // Remove empty string from leading slash
    console.log("headerpath", headerpath);

    if (headerpath.length > 1) {
      const id = headerpath[1]; // e.g., "67bd6225708d902413f03863"
      if (headerpath[0] === "create-node") {
        // Fetch node data for node routes
        getNodeData(id);
      } else if (headerpath[0] === "edit") {
        // Fetch node data for node routes
        getProjectData(id);
      } else {
        // Fetch machine data for machine routes
        getMachineData(id);
      }
    } else {
      // Set the header path as sidebar options
      setNewPathName(headerpath[0] || ""); // Fallback to empty string if no path
    }
  }, [location]);

  const toggleTheme = () => {
    if (currentMode === "Dark") {
      let e = "Light"; // setMode has taken value from checkbox in new theme settings which is taking e as prop
      setMode(e);
    } else {
      let e = "Dark";
      setMode(e);
    }
  };

  useEffect(() => {
    // Theme switching on time. this will work when some activity will be going on.
    // let hour = new Date().getHours();
    // if (hour >= 18 || hour <= 6) {
    //   theme.dispatch({ type: 'DARKMODE' });
    // }
    // else {
    //   theme.dispatch({ type: 'LIGHTMODE' });
    // }

    // if (pathName == "/") {
    //   setOpenNotification(false);
    // }
    // const profilePicture = currentUser.avatar;
    // console.log("profilepicture ", profilePicture);
    // if (currentUser) {
    //console.log("lsi: ", adminType_constant_temp )
    //   if (adminType_constant_temp === "admin")
    //     // Info: this condition required as LSI admin can view all company and now heading will have LSI admin info
    //     db.collection(companies)
    //       .doc(companyId_constant)
    //       .collection("userData")
    //       .where("email", "==", currentUser.email)
    //       .onSnapshot((snap) => {
    //         const data = firebaseLooper(snap);
    //         setUser(data[0]);
    //         console.log(pathName);
    //         if (pathName === "/") {
    //           setNewPathName("DashBoard");
    //         } else if (pathName.includes("/annotation/")) {
    //           setNewPathName("Live Data");
    //         } else if (pathName.includes("/cms")) {
    //           setNewPathName("CMS");
    //         }
    //       });
    //   else if (adminType_constant_temp === "lsi_admin") {
    //     db.collection(alluser)
    //       .where("email", "==", currentUser.email)
    //       .onSnapshot((snap) => {
    //         const data = firebaseLooper(snap);
    //         setUser(data[0]);

    //         if (pathName === "/") {
    //           // these conditions are same but can be diffrent on login type.(if asked)
    //           setNewPathName("DashBoard");
    //         } else if (pathName.includes("/annotation/")) {
    //           //
    //           setNewPathName("Live Data");
    //         } else if (pathName.includes("/cms")) {
    //           //
    //           setNewPathName("CMS");
    //         }
    //       });
    //   }

    //   let companyId;
    //   if (pathName.substring(1).split("/").length === 3) {
    //     // Info: this is for document pages (FAT and SAT)
    //     companyId = pathName.substring(1).split("/")[0];
    //   } else {
    //     companyId = pathName.substring(1).split("/")[1];
    //   }

    //   db.collection(companies)
    //     .doc(companyId_constant)
    //     .collection("machineData")
    //     .doc(companyId)
    //     .get()
    //     .then((snap) => {
    //       let machineTitle = snap.data().title;
    //       if (pathName.substring(1).split("/").length === 3) {
    //         // this is for document pages (FAT and SAT)
    //         setNewPathName(machineTitle + " : " + "Document"); // pathName.substring(1).split("/")[0]
    //       } else {
    //         if (pathName.substring(1).split("/")[0] === "annotation") {
    //           setNewPathName(machineTitle + " : " + "Live Data");
    //         } else {
    //           setNewPathName(
    //             machineTitle + " : " + pathName.substring(1).split("/")[0]
    //           );
    //         }
    //         //
    //       }
    //     })
    //     .catch((e) => console.log("No machine selected"));
    //   setNewPathName(pathName.substring(1).split("/")[0]); // if no special case then do this
    // }

    const fetchUserdetails = async () => {
      try {
        console.log("id", id);
        const res = await axios.get(`${dbConfig.url}/users/${id}`);
        const data = res.data?.data;
        console.log("user data", data);
        setUser(data);
      } catch (error) {
        console.error("Error fetching user details:", error);
      }
    };
    fetchUserdetails();
  }, []);

  async function handleLogout() {
    let currentDateTime = new Date();

    const data = {
      activity: "logout",
      dateTime: currentDateTime,
      role: currentUser.role,
      userName_email: currentUser.email,
    };
    try {
      await logout();
      // window.localStorage.removeItem("companyId");
      // window.localStorage.removeItem("adminType");

      // UserSessionLog(
      //   "Logged Out",
      //   currentUser.email,
      //   user.role,
      //   `${user?.fname} ${user?.lname}`
      // );
      userActivity(data);
    } catch {
      toastMessage({ message: "Failed to log out" });
    }
  }

  return (
    <>
      <div
        style={
          currentMode === "Dark" ? { backgroundColor: "", color: "white" } : {}
        }
        className="header"
      >
        {/* Left side */}
        <div className="headerLeft">
          <div
            className="menu"
            onClick={() => {
              setInactive(!inactive);
            }}
          >
            <i
              className={inactive ? "ri-menu-unfold-fill" : "ri-menu-line"}
            ></i>
          </div>
          <div className="themeSwitcher">
            <button
              onClick={toggleTheme}
              className={`themeBtn ${
                currentMode === "Dark" ? "darkThemeBtn" : "lightThemeBtn"
              }`}
            >
              <SunIcon />
              <MoonIcon />
            </button>
          </div>
          <div className="text-sm font-bold uppercase">
            {newPathName === "file-manager" ? "library" : newPathName}
          </div>
        </div>

        {/* Right side */}
        <div className="headerRight">
          <div className="notification">
            <IconButton onClick={handleClickNotify} className="notification-btn">
              <NotificationsIcon 
                sx={{ color: "primary.main", fontSize: 26, transition: "transform 0.15s" }} 
                className="notification-icon"
              />
            </IconButton>
          </div>
          <Menu
            anchorEl={anchor2}
            id="demo-positioned-button"
            open={openNotifications}
            onClose={handleCloseNotify}
            onClick={handleCloseNotify}
            PaperProps={{
              style: {
                // this style is for avoiding horizontal menu items
                width: "420px",
                padding: 1,
              },

              elevation: 0,
              sx: {
                overflow: "visible",
                filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
                mt: 1.5,
                "&:before": {
                  content: '""',
                  display: "block",
                  position: "absolute",
                  top: 0,
                  right: 30,
                  width: 10,
                  height: 10,
                  bgcolor: "background.paper",
                  transform: "translateY(-50%) rotate(45deg)",
                  zIndex: 0,
                },
              },
            }}
            //transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
          >
            <Box
              sx={{
                p: 2,
                backgroundColor: currentMode === "Dark" ? '"#161C24"' : "",
              }}
            >
              <NotificationBox />
            </Box>
          </Menu>
          <div
            className="signin"
            onClick={handleClick}
            style={{
              display: "flex",
              alignItems: "center",
              height: 40, // match Avatar size (default MUI Avatar is 40px)
            }}
          >
            <Avatar size="small" src={`${dbConfig.url_storage}/images/${user?.avatar}`} />
            <p
              className="text"
              style={{
                marginLeft: 3,
                marginTop: 0,
                marginBottom: 0,
                height: 40,
                lineHeight: "40px",
                display: "flex",
                alignItems: "center",
              }}
            >
              {user?.fname + " " + user?.lname}
            </p>
          </div>
          <Menu
            anchorEl={anchorEl}
            id="demo-positioned-button"
            open={open}
            onClose={handleClose}
            onClick={handleClose}
            PaperProps={{
              style: {
                // this style is for avoiding horizontal menu items
                width: 180,
                padding: 1,
              },

              elevation: 0,
              sx: {
                overflow: "visible",
                filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
                mt: 1.5,
                "& .MuiAvatar-root": {
                  width: 32,
                  height: 32,
                  ml: -0.5,
                  mr: 1,
                },
                "&:before": {
                  content: '""',
                  display: "block",
                  position: "absolute",
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: "background.paper",
                  transform: "translateY(-50%) rotate(45deg)",
                  zIndex: 0,
                },
              },
            }}
            //transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
          >
            <MenuItem>
              <ListItemIcon>
                <AccountCircleRoundedIcon fontSize="small" />
              </ListItemIcon>
              <Link style={{ textDecoration: "none" }} to="/account">
                {" "}
                My account
              </Link>
            </MenuItem>
            {/* <MenuItem>
              <ListItemIcon>
                <PersonAdd fontSize="small" />
              </ListItemIcon>
              Tasks
            </MenuItem> */}

            <MenuItem onClick={() => setThemeSettings(true)}>
              <ListItemIcon>
                <Settings fontSize="small" />
              </ListItemIcon>
              Settings
            </MenuItem>

            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <Logout fontSize="small" />
              </ListItemIcon>
              Logout
            </MenuItem>
          </Menu>
        </div>
      </div>
      <div className="fixed right-4 bottom-4" style={{ zIndex: "1000" }}>
        {/* <Tooltip
              content="Settings"
              position="Top"
            >
              <button
                type="button"
                
                style={{ background: currentColor, borderRadius: '50%' }}
                className="text-3xl text-white p-3 hover:drop-shadow-xl hover:bg-light-gray"
              >
                <FiSettings />
              </button>
            </Tooltip> */}
        {themeSettings && <ThemeSettings />}
      </div>
    </>
  );
};

export default Header;
