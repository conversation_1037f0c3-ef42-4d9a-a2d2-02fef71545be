import { createContext, useContext, useState } from "react";
import axios from "axios";
import dayjs from "dayjs";
// import { dbUrl } from "../../src/constants/db";
import PropTypes from "prop-types";

const CfrContextAdd = createContext();
const CfrContextSetterAll = createContext();
const CfrContextGetterAll = createContext();

// export function useIpSetter () {
//     return useContext(IpSetterContext);
// }
// export function useIpGetter () {
//     return useContext(IpGetterContext);
// }

// export function usePortSetter () {
//     return useContext(PortSetterContext);
// }
export function useCfrSetterAll() {
  return useContext(CfrContextSetterAll);
}
export function useCfrGetterAll() {
  return useContext(CfrContextGetterAll);
}

export function useCfrAdd() {
  return useContext(CfrContextAdd);
}

export function CfrContextProvider({ children }) {
  const [cfrAll, setCfrAll] = useState([]);
  const currentUser = JSON.parse(window.localStorage.getItem("@user-creds"));
  async function cfrAdd(data) {
    data.timestamp = dayjs().valueOf();
    if (currentUser) {
      data.user_name = currentUser?.username;
      data.email = currentUser?.email;
      data.role = currentUser?.role;
    }
    //🌟 SEND THIS AS PARAMETER
    // var data = {
    //     timestamp:dayjs(),
    //     user_name: JSON?.parse(window.localStorage.getItem('@user-creds'))?.username ,
    //     user_name: JSON?.parse(window.localStorage.getItem('@user-creds'))?.email ,
    //     module: 'report',
    //     activity: 'report (ins. code no,: 123123) has been updated',
    //     description: 'what has been updated "IF POSSIBLE THEN ADD"' ,
    //     method: "PUT",
    // }
    //
    axios({
      method: "post",
      headers: { "Content-Type": "application/json" },
      url: `/cfr`,
      data: { ...data },
    })
      .then((res) => {
        //
      })
      .catch((err) => {});
  }

  async function cfrGetterAll() {
    axios
      .get(`/cfr`)
      .then((res) => {
        //
        setCfrAll(res?.data);
      })
      .catch((err) => {});
  }

  return (
    <CfrContextAdd.Provider value={cfrAdd}>
      <CfrContextSetterAll.Provider value={cfrGetterAll}>
        <CfrContextGetterAll.Provider value={cfrAll}>
          {children}
        </CfrContextGetterAll.Provider>
      </CfrContextSetterAll.Provider>
    </CfrContextAdd.Provider>
  );
}

CfrContextProvider.propTypes = {
  children: PropTypes.node,
};
