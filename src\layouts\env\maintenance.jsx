// MaintenancePopup.js
import React from "react";
import { useMaintenance } from "../../hooks/AxiosErrorHandler";

const MaintenancePopup = () => {
  const TITLE = "Maintenance Mode";
  const REFRESH_BUTTON_TEXT = "Refresh Page";

  // eslint-disable-next-line no-unused-vars
  const { error, setError } = useMaintenance();

  if (!error) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        color: "#fff",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 9999,
      }}
    >
      <div style={{ maxWidth: "400px", textAlign: "center" }}>
        <h2>{TITLE}</h2>
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          style={{ marginTop: "20px", padding: "10px 20px" }}
        >
          {REFRESH_BUTTON_TEXT}
        </button>
      </div>
    </div>
  );
};

export default MaintenancePopup;
