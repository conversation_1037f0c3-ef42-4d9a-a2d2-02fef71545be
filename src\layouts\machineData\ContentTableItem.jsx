import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  IconButton,
  TableCell,
  TableRow,
} from "@mui/material";
import React, { useState } from "react";
import ModeEditIcon from "@mui/icons-material/ModeEdit";
import DeleteIcon from "@mui/icons-material/Delete";
import EditTableValues from "./ContentMaster/EditTableValues";
import Delete from "../../components/Delete/Delete";
import { db } from "../../firebase";
import { companies, companyId_constant } from "../../constants/data";
import { toastMessage } from "../../tools/toast";
import { useStateContext } from "../../context/ContextProvider";

const ContentTableItem = ({
  forComponent,
  data,
  collectionId,
  type,
  index,
}) => {
  const [openEdit, setOpenEdit] = useState(false);
  const [openDel, setOpenDel] = useState(false);
  const [tableData, setTableData] = useState({});
  const { currentMode, currentColorLight } = useStateContext();

  const handleDelete = () => {
    // db.collection(companies)
    // 	.doc(companyId_constant)
    // 	.collection(type)
    // 	.doc(collectionId)
    // 	.collection('table')
    // 	.doc(data.id)
    // 	.delete().then(() => {
    // 		toastMessage({ message: "Deleted document row successfully !" })
    // 	})
  };

  return (
    <>
      <TableRow
        sx={{
          backgroundColor:
            currentMode === "Dark" && forComponent != "print"
              ? "#212B36"
              : "#fff",
          color:
            currentMode === "Dark" && forComponent != "print"
              ? "white"
              : "black",
          textTransform: "capitalize",
        }}
      >
        <TableCell
          style={{
            border:
              currentMode === "Dark" && forComponent != "print"
                ? "1px solid white"
                : "1px solid black",
            color: forComponent === "print" ? "#111" : " ",
          }}
          align="left"
        >
          {data.check}
        </TableCell>
        <TableCell
          style={{
            border:
              currentMode === "Dark" && forComponent != "print"
                ? "1px solid white"
                : "1px solid black",
            color: forComponent === "print" ? "#111" : " ",
          }}
          align="center"
        >
          {data.observation}
        </TableCell>
        <TableCell
          style={{
            border:
              currentMode === "Dark" && forComponent != "print"
                ? "1px solid white"
                : "1px solid black",
            color: forComponent === "print" ? "#111" : " ",
          }}
          align="center"
        >
          {data.acceptance}
        </TableCell>
        <TableCell
          style={{
            border:
              currentMode === "Dark" && forComponent != "print"
                ? "1px solid white"
                : "1px solid black",
            color: forComponent === "print" ? "#111" : " ",
          }}
          align="center"
        >
          {data.confirm}
        </TableCell>
        <TableCell
          style={{
            border:
              currentMode === "Dark" && forComponent != "print"
                ? "1px solid white"
                : "1px solid black",
            color: forComponent === "print" ? "#111" : " ",
          }}
          align="center"
        >
          {data.dev}
        </TableCell>
        {forComponent != "print" && (
          <TableCell
            style={{
              border:
                currentMode === "Dark" ? "1px solid white" : "1px solid black",
            }}
            align="center"
          >
            <IconButton onClick={() => setOpenEdit(true)}>
              <ModeEditIcon />
            </IconButton>
            <IconButton onClick={() => setOpenDel(true)}>
              <DeleteIcon />
            </IconButton>
          </TableCell>
        )}
      </TableRow>
      <Dialog open={openEdit} fullWidth maxWidth="lg">
        <DialogContent
          sx={{
            background: currentMode === "Dark" ? "#161C24" : currentColorLight,
          }}
        >
          <DialogTitle>Edit Details</DialogTitle>
          <EditTableValues
            handleClose={() => setOpenEdit(false)}
            collectionId={collectionId}
            type={type}
            data={data}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={openDel}>
        <Delete onDelete={handleDelete} onClose={() => setOpenDel(false)} />
      </Dialog>
    </>
  );
};
export default ContentTableItem;
