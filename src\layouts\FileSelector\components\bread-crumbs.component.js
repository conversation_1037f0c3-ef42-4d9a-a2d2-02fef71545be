import React from "react";
import { Breadcrum<PERSON>, <PERSON> } from "@mui/material";
// import { ROOT_FOLDER } from '../../hooks/useFolder';
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import { NavLink } from "react-router-dom";
import { ROOT_FOLDER } from "../../../hooks/useFolder";

export default function FolderBreadCrumbsSelector({
  currentFolder,
  setFolderIdSelected,
}) {
  let path = currentFolder === ROOT_FOLDER ? [] : [ROOT_FOLDER];
  if (currentFolder) path = [...path, ...currentFolder.path];
  return (
    <div role="presentation">
      <Breadcrumbs
        aria-label="breadcrumb"
        separator={<NavigateNextIcon fontSize="small" />}
      >
        {path.map((folder) => (
          <div
            underline="hover"
            color="inherit"
            style={{ cursor: "pointer" }}
            onClick={() => setFolderIdSelected(folder?.id ? folder?.id : null)}
          >
            {folder.name}
          </div>
        ))}
        {currentFolder && (
          <Link
            underline="none"
            style={{
              color: "#00C897",
              textTransform: "capitalize",
              fontWeight: "500",
            }}
          >
            {currentFolder.name}
          </Link>
        )}
      </Breadcrumbs>
    </div>
  );
}
