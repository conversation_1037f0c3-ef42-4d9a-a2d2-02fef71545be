import React, { useEffect, useMemo, useState } from "react";
import { useParams, useLocation } from "react-router-dom";
import {
  companies,
  companyId_constant,
  maintenance,
  machines,
  hasTags,
} from "../../constants/data";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import MachineDataHeader from "./MachineDataHeader";
import MaintenanceItem from "./MaintenanceItem";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import TablePagination from "@mui/material/TablePagination";
import SearchIcon from "@mui/icons-material/Search";
import { SearchOutlined } from "@mui/icons-material";
import {
  Dialog,
  DialogContent,
  Dialog<PERSON><PERSON>le,
  CircularProgress,
  TextField,
  Typography,
  Button,
  IconButton,
} from "@mui/material";
import AddMaintenance from "./AddMaintenance";
import {
  useMaintenanceInfo,
  useMaintenanceInfoSeter,
} from "../../context/MaintenanceContext";
import { ButtonBasic } from "../../components/buttons/Buttons";
import { useStateContext } from "../../context/ContextProvider";
import axios from "axios";
import { dbConfig } from "../../infrastructure/db/db-config";
import { useMongoRefresh } from "../../services/mongo-refresh.context";
import { sharedCss } from "../../styles/sharedCss";
import { makeStyles } from "@mui/styles";
import CommonDropDown from "../../components/commons/dropDown.component";
import TableHeader from "./TableHeader";
import NoDataComponent from "../../components/commons/noData.component";
import { useCommonOuterContainerStyle } from "../../styles/useCommonOuterContainerStyle";
import { useCheckAccess } from "../../utils/useCheckAccess";
import NotAccessible from "../../components/not-accessible/not-accessible";

const MaintenanceData = () => {
  const [open, setOpen] = useState(false);
  const { mid } = useParams();
  const location = useLocation();
  const [details, setDetails] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [defaultType, setDefaultType] = useState(location.state?.type ?? 1);
  const [machineName, setMachineName] = useState("");
  const [hastTagsAll, setHastagsAll] = useState([]);
  const [dataLoading, setDataLoading] = useState(true);
  const [hastagsForFilter, setHastagsForFilter] = useState([]);
  const maintenanceInfoFromContext = useMaintenanceInfo();
  const maintenanceInfoSetter = useMaintenanceInfoSeter();
  const { currentColor, currentMode, currentColorLight } = useStateContext();
  const { refreshCount, setRefreshCount } = useMongoRefresh();
  const commonOuterContainerStyle = useCommonOuterContainerStyle();

  // Pagination states
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10); // Default to 10 items per page

  const mainIdFromAlarm = location.state?.mainId || null;
  const expandedByDefault = (data) => mainIdFromAlarm === data._id;

  const hasMainPOSTAccess = useCheckAccess("maintenance", "POST");
  const hasMainGETAccess = useCheckAccess("maintenance", "GET");

  const handleDropDownValueChange = (event = {}) => {
    const newType = event?.target?.value ?? defaultType;
    setDefaultType(newType);
    maintenanceInfoSetter({ type: newType });
    setPage(0); // Reset to first page when type changes
  };

  const fetchAllMaintenance = async () => {
    try {
      const response = await axios.get(
        `${dbConfig.url}/maintenance/getFromMachine/${mid}`
      );
      const maintenanceData = response?.data?.data || [];
      setDetails(maintenanceData.filter((obj) => obj.mid === mid));
    } catch (error) {
      console.error("ERROR IN MAINTENANCE:", error.message);
      setDetails([]);
    } finally {
      setDataLoading(false);
    }
  };

  const fetchCurrentMachine = async () => {
    await axios.get(`${dbConfig.url}/machines/${mid}`).then((response) => {
      setMachineName(response.data?.data?.title);
    });
  };

  const commonCss = sharedCss();

  useEffect(() => {
    if (maintenanceInfoFromContext?.type != undefined) {
      setDefaultType(maintenanceInfoFromContext?.type);
    }
    fetchAllMaintenance();
    fetchCurrentMachine();
  }, [maintenanceInfoFromContext, refreshCount]);

  const onHastagSelect = (tag) => {
    let temp = [...hastagsForFilter, tag];
    setHastagsForFilter([...new Set(temp)]);
    setPage(0); // Reset to first page when filter changes
  };

  const handleOnChangeSearchTerm = (e) => {
    if (e.target.value === "" && hastagsForFilter.length > 0) {
      setSearchTerm("#");
    } else {
      setSearchTerm(e.target.value);
    }
    setPage(0); // Reset to first page when search term changes
  };

  const removeSelectedHastag = (index) => {
    let temp = [...hastagsForFilter];
    temp.splice(index, 1);
    setHastagsForFilter([...temp]);
    setPage(0); // Reset to first page when hashtag filter changes
  };

  const defaultTypeSetter = (type) => {
    setDefaultType(type);
    maintenanceInfoSetter({ type: type });
    setPage(0); // Reset to first page when type changes
  };

  const filterDetails = (
    details,
    defaultType,
    searchTerm,
    hastagsForFilter
  ) => {
    return details.filter((data) => {
      if (data.type !== defaultType) return false;

      if (
        (searchTerm === "" && hastagsForFilter?.length === 0) ||
        (searchTerm === "#" && hastagsForFilter?.length === 0) ||
        (searchTerm?.startsWith("#") && hastagsForFilter?.length === 0)
      ) {
        return true;
      }

      if (searchTerm.startsWith("#") || hastagsForFilter?.length > 0) {
        return hastagsForFilter.some(
          (filterData) => filterData.doc_id === data.id
        );
      }

      return data.title.toLowerCase().includes(searchTerm.toLowerCase());
    });
  };

  const filteredData = useMemo(
    () => filterDetails(details, defaultType, searchTerm, hastagsForFilter),
    [details, defaultType, searchTerm, hastagsForFilter]
  );

  const filterDetailsLength = filteredData.length;

  // Paginate the filtered data
  const paginatedData = useMemo(() => {
    const startIndex = page * rowsPerPage;
    return filteredData.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredData, page, rowsPerPage]);

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0); // Reset to first page when rows per page changes
  };

  return (
    <section>
      <MachineDataHeader />
      <div>
        <div
          className={`${commonCss.sectionContainer} ${commonCss.backgroundLight} border-radius-outer`}
        >
          <div className={commonCss.tableLable}>
            <Typography sx={{ ml: 2 }} fontWeight="bold" variant="h6">
              Maintenance
            </Typography>
            <div
              className={commonCss.tableRightContent}
              style={{ display: "flex", alignItems: "center", gap: "1 rem" }}
            >
              <div>
                <TextField
                  label="Search"
                  placeholder="Search by title"
                  className={`${commonCss.searchBox} ${commonCss.inputAlignmentFix}`}
                  id="outlined-size-small"
                  size="small"
                  value={searchTerm}
                  onChange={(e) => handleOnChangeSearchTerm(e)}
                  InputProps={{
                    startAdornment: (
                      <IconButton>
                        <SearchOutlined />
                      </IconButton>
                    ),
                  }}
                />
              </div>
              <div className="flex absolute flex-wrap max-w-sm">
                {hastagsForFilter?.map((data, index) => (
                  <div
                    className="px-0.5 mx-0.5 mb-0.5 rounded-sm bg-cyan-50 shadow-sm text-xs"
                    key={index}
                  >
                    #{data.title}
                    <i
                      onClick={() => removeSelectedHastag(index)}
                      className="ri-close-circle-fill hover:text-red-500 hover:cursor-pointer"
                    ></i>
                  </div>
                ))}
              </div>
              <div className="absolute top-12 backdrop-blur-sm z-10 px-1 max-h-36 shadow-md overflow-y-scroll">
                {searchTerm.startsWith("#") &&
                  hastTagsAll
                    ?.filter((fdata) =>
                      searchTerm?.slice(1)
                        ? fdata.title.includes(searchTerm?.slice(1))
                        : false
                    )
                    .map((hData) => (
                      <div
                        key={hData.doc_id}
                        onClick={() => onHastagSelect(hData)}
                        className="hover:cursor-pointer"
                      >
                        {hData.title}
                      </div>
                    ))}
              </div>
              <div style={{ display: "flex" }}>
                <CommonDropDown
                  dropDownLabel={"Select type"}
                  className={`${commonCss.dropDown} ${commonCss.inputAlignmentFix}`}
                  menuData={[
                    { _id: "_id", label: "Machine Breakdown", value: 1 },
                    { id: "id_", label: "Routine", value: 2 },
                    { _id: "_id_", label: "Preventive", value: 3 },
                  ]}
                  menuValue={defaultType}
                  handleChange={handleDropDownValueChange}
                  menuItemDisplay={"label"}
                  menuItemValue={"value"}
                />
              </div>
              <div>
                <Button
                  variant="contained"
                  onClick={() => setOpen(true)}
                  disabled={!hasMainPOSTAccess}
                >
                  Add Maintenance
                </Button>
              </div>
            </div>
          </div>

          {hasMainGETAccess ? (
            <div>
              <TableContainer
                component={Paper}
                className="table border-radius-inner"
                sx={commonOuterContainerStyle}
                // sx={{ maxHeight: "500px", overflowY: "auto" }} // Adjusted maxHeight for scrolling
              >
                <Table sx={{ minWidth: 650 }}>
                  <TableHeader
                    currentMode={currentMode}
                    columns={[
                      {
                        label: defaultType === 0 ? "Sensor Name" : "Title",
                        align: "left",
                        width: defaultType === 0 ? "20%" : "30%",
                      },
                      { label: "Description", align: "left", width: "30%" },
                      ...(defaultType !== 1
                        ? [{ label: "Period / Cycle", align: "left" }]
                        : []),
                      { label: "Last Done", align: "left" },
                      { label: "Status", align: "center" },
                      { label: "Actions", align: "center" },
                    ]}
                  />
                  <TableBody>
                    {dataLoading ? (
                      <TableRow>
                        <TableCell
                          colSpan={defaultType !== 1 ? 6 : 5}
                          align="center"
                        >
                          <CircularProgress />
                        </TableCell>
                      </TableRow>
                    ) : filterDetailsLength > 0 ? (
                      paginatedData.map((data, index) => (
                        <MaintenanceItem
                          key={index}
                          data={data}
                          machineName={machineName}
                          defaultType={defaultType}
                          lastItem={index === paginatedData.length - 1}
                          expandedByDefault={expandedByDefault(data)}
                        />
                      ))
                    ) : (
                      <NoDataComponent />
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              {filterDetailsLength > 0 && (
                <TablePagination
                  component="div"
                  count={filterDetailsLength}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  className={commonCss.tablePagination}
                />
              )}
            </div>
          ) : (
            <NotAccessible />
          )}
        </div>
      </div>
      <Dialog open={open} fullWidth>
        <DialogTitle
          style={
            currentMode === "Dark"
              ? { backgroundColor: "#212B36", color: "white" }
              : {}
          }
        >
          Add Maintenance Schedule
        </DialogTitle>
        <DialogContent
          style={
            currentMode === "Dark"
              ? { backgroundColor: "#212B36", color: "white" }
              : {}
          }
        >
          <AddMaintenance
            mid={mid}
            handleClose={() => setOpen(false)}
            machineName={machineName}
            useAt={"MaintenanceData"}
          />
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default MaintenanceData;
