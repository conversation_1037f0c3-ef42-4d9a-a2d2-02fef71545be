import React, { useState, useMemo, useContext } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "../TableTempletes.css";

import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import TextField from "@mui/material/TextField";

import { useStateContext } from "../../../../context/ContextProvider";
import { db, storage } from "../../../../firebase";
import { companies, companyId_constant } from "../../../../constants/data";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../../../components/buttons/Buttons";

import { useStorageTablesFile } from "../../../../utils/useStorageTablesFile";
import { InputLabel, LinearProgress, Link, Typography } from "@mui/material";
import { Empty } from "antd";
import { FileDownload, ImageSearchOutlined } from "@mui/icons-material";
import {
  FileImageFilled,
  FilePdfFilled,
  PictureFilled,
} from "@ant-design/icons";
import { DeleteByUrl } from "../../../../utils/StorageOptions";
import { DropzoneArea } from "material-ui-dropzone";
import { FileManagerSelectorContext } from "../../../../services/fileManager/file-manager-select.context";
import FileSelector from "../../../FileSelector/screens/FileSelector";
import PreviewIcon from "@mui/icons-material/Preview";

const IQ14 = ({ rowData, type, machineName, fatDataDocId, useAt }) => {
  const [open, setOpen] = useState(false);
  const { currentMode, currentColorLight } = useStateContext();
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#161c24" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                S.No
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={4}>
                Specification
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Documents
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Result
              </TableCell>
              {useAt !== "tableMain" && (
                <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                  Image
                </TableCell>
              )}
            </TableRow>

            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }}>
                Description
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Model Vendor
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Model Serial No
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>Type</TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  sx={{
                    border: theme.borderDesign,
                    background: currentMode === "Dark" ? "#212b36" : "",
                  }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[2]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[3]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[4]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[5]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[6]}
                  </TableCell>
                  {useAt !== "tableMain" && (
                    <TableCell
                      sx={{ border: theme.borderDesign }}
                      align="center"
                    >
                      {data[7] === null ? (
                        "Add One"
                      ) : (
                        <>
                          {data[7]?.includes(".pdf") ? (
                            <FilePdfFilled
                              className="text-red-700"
                              onContextMenu={() =>
                                (window.location.href = data[7])
                              }
                              title="Press right click to open file"
                            />
                          ) : (
                            <PictureFilled
                              onContextMenu={() =>
                                (window.location.href = data[7])
                              }
                              title="Press right click to open file"
                            />
                          )}
                        </>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent
          sx={
            currentMode === "Dark"
              ? { background: "#161C24" }
              : { background: currentColorLight }
          }
        >
          <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
          <EditTableRow
            rowDataSelected={rowDataSelected}
            type={type}
            fatDataDocId={fatDataDocId}
            machineName={machineName}
            handleClose={handleClose}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

const EditTableRow = ({
  rowDataSelected,
  type,
  machineName,
  fatDataDocId,
  handleClose,
}) => {
  // console.log(rowDataSelected, tableType)
  const { currentMode } = useStateContext();
  const [s_no, setS_No] = useState(rowDataSelected[0]);
  const [desc, setDesc] = useState(rowDataSelected[1]);
  const [model_vendor, setModel_Vendor] = useState(rowDataSelected[2]);
  const [model_s_no, setModel_S_No] = useState(rowDataSelected[3]);
  const [typeTable, setTypeTable] = useState(rowDataSelected[4]);
  const [docs, setDocs] = useState(rowDataSelected[5]);
  const [result, setResult] = useState(rowDataSelected[6]);
  const [urlData, setUrlData] = useState(rowDataSelected[7]);
  const [file, setFile] = useState(null);
  const { progress, url } = useStorageTablesFile(file);
  const [fileType, setFileType] = useState("");
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];
  const { fileUrl, changeFileUrl } = useContext(FileManagerSelectorContext);
  const [fType, setFType] = useState("index" in rowDataSelected ? true : false);
  const [index, setIndex] = useState(rowDataSelected[9]);
  const handleChangeImage = (file) => {
    let selectedFile = file[0];
    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        if (url) {
          DeleteByUrl(url);
        }
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };

  const handleUpdateRow = () => {
    if (fType && isNaN(parseInt(index))) {
      toastMessageWarning({ message: "The input of index should be a Number" });
      return;
    }
    let data = {
      s_no: s_no,
      desc: desc,
      model_vendor: model_vendor,
      model_s_no: model_s_no,
      typeTable: typeTable,
      docs: docs,
      result: result,
      index: index,
      url: fileUrl === null ? urlData : fileUrl,
    };
    // db.collection(companies)
    //   .doc(companyId_constant)
    //   .collection(type)
    //   .doc(fatDataDocId)
    //   .collection("tableIQ14")
    //   .doc(rowDataSelected[8])
    //   .update(data)
    //   .then(() => {
    //     toastMessageSuccess({ message: "Row updated Successfully" });
    //     handleClose();
    //   })
    //   .catch((e) => {
    //     toastMessageWarning({ message: "Error ", e });
    //   });
  };

  const handleCancel = () => {
    if (url) {
      DeleteByUrl(url);
    }
    handleClose();
  };
  const handleDeleteDropZone = (url) => {
    DeleteByUrl(url);
    setFile(null);
  };

  return (
    <>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          <div className="flex justify-between py-2">
            <div className="w-3/12">
              <TextField
                label="S.No"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={s_no}
                onChange={(e) => setS_No(e.target.value)}
              />
            </div>
            <div className="w-3/12">
              <TextField
                label="Description"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={desc}
                onChange={(e) => setDesc(e.target.value)}
              />
            </div>
            <div className="w-3/12">
              <TextField
                label="Make Vendor"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={model_vendor}
                onChange={(e) => setModel_Vendor(e.target.value)}
              />
            </div>
            <div className="w-3/12">
              <TextField
                label="Model Serial No"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={model_s_no}
                onChange={(e) => setModel_S_No(e.target.value)}
              />
            </div>
            <div className="w-3/12">
              <TextField
                label="Type"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={typeTable}
                onChange={(e) => setTypeTable(e.target.value)}
              />
            </div>
            <div className="w-3/12">
              <TextField
                label="Documents"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={docs}
                onChange={(e) => setDocs(e.target.value)}
              />
            </div>
            <div className="w-3/12">
              <TextField
                label="Result"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={result}
                onChange={(e) => setResult(e.target.value)}
              />
            </div>
            {/* {fType  */}
            {(url?.includes(".pdf") ||
              fileUrl?.includes(".pdf") ||
              urlData?.includes(".pdf")) && (
              <div className="w-2/12">
                <TextField
                  label="Index"
                  id="outlined-size-small"
                  defaultValue="Na"
                  size="small"
                  value={index}
                  onChange={(e) => setIndex(e.target.value)}
                />
              </div>
            )}
          </div>
        </DialogContentText>
        <FileSelector />
        <Typography sx={{ ml: "20px" }} align="left">
          {" "}
          <PreviewIcon /> Preview
        </Typography>
        <img width="250px" src={urlData} />
      </DialogContent>
      <DialogActions>
        <ButtonBasic buttonTitle="Update" onClick={handleUpdateRow} />
      </DialogActions>
    </>
  );
};

export default IQ14;
