import { useState, useEffect } from "react";
import { storage } from "../config/firebase.config";
import PropTypes from "prop-types";

export const useStorage = (file, parent) => {
  const [progress, setProgress] = useState(0);
  const [progressForSignature, setProgressForSignature] = useState(0);

  const [error, setError] = useState(null);
  const [url, setUrl] = useState(null);
  // eslint-disable-next-line no-unused-vars
  const [urlForSignature, setUrlForSignature] = useState(null);

  // runs every time the file value changes
  useEffect(() => {
    if (file) {
      // storage ref
      const storageRef = storage.ref(`liveData/${file.name}`);
      storageRef.put(file).on(
        "state_changed",
        (snap) => {
          // track the upload progress
          let percentage = Math.round(
            (snap.bytesTransferred / snap.totalBytes) * 100,
          );
          parent === "profile"
            ? setProgress(percentage)
            : setProgressForSignature(percentage);
        },
        (err) => {
          //
          setError(err);
        },
        async () => {
          // get the public download img url
          const downloadUrl = await storageRef.getDownloadURL();
          //
          // save the url to local state
          parent === "profile"
            ? setUrl(downloadUrl)
            : setUrlForSignature(downloadUrl);
        },
      );
    } else {
      if (parent === "profile") {
        setProgress(0);
        setUrl(null);
        //
      } else {
        setProgressForSignature(0);
        setUrlForSignature(null);
        //
      }
    }
  }, [file]);
  return { progress, url, error, progressForSignature, setUrlForSignature };
};
useStorage.propTypes = {
  file: PropTypes.object,
  parent: PropTypes.string,
};
