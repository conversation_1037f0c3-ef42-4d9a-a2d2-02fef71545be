import React, { useState, useMemo } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "./TableTempletes.css";

import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

import TextField from "@mui/material/TextField";

import { useStateContext } from "../../../context/ContextProvider";
// import { db, storage } from "../../../firebase";
import { companies, companyId_constant } from "../../../constants/data";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../../tools/toast";
import {
  ButtonBasic,
  ButtonBasicCancel,
} from "../../../components/buttons/Buttons";

import { useStorageTablesFile } from "../../../utils/useStorageTablesFile";
import { InputLabel, LinearProgress, Link } from "@mui/material";
import { Empty } from "antd";
import { FileDownload, ImageSearchOutlined } from "@mui/icons-material";
import {
  FileImageFilled,
  FilePdfFilled,
  PictureFilled,
} from "@ant-design/icons";
import { DeleteByUrl } from "../../../utils/StorageOptions";
import { DropzoneArea } from "material-ui-dropzone";

const EditTableRow = ({
  rowDataSelected,
  tableType,
  type,
  machineName,
  fatDataDocId,
  handleClose,
}) => {
  const { currentMode } = useStateContext();

  const [sNo, setSNo] = useState(rowDataSelected[0]); // t1,t2,t3,t4
  const [tagNo, setTagNo] = useState(rowDataSelected[1]); // t1,t2,t4
  const [description, setDescription] = useState(rowDataSelected[2]); // t1,t2,t4
  const [makeVendor, setMakeVendor] = useState(rowDataSelected[3]); // t1,t4
  const [details, setDetails] = useState(rowDataSelected[4]); // t1,t4
  const [size, setSize] = useState(rowDataSelected[5]); // t1
  const [documents, setDocuments] = useState(rowDataSelected[6]); // t1
  const [result, setResult] = useState(rowDataSelected[7]); // t1
  const [urlOldT1, setUrlOldT1] = useState(rowDataSelected[8]);
  const [table1Title, setTable1Title] = useState(rowDataSelected[10]); // 8+2

  const [typeSize, setTypeSize] = useState(rowDataSelected[3]); // t2
  const [documentsT2, setDocumentsT2] = useState(rowDataSelected[4]); // t2
  const [resultT2, setResultT2] = useState(rowDataSelected[5]); // t2
  const [urlOldT2, setUrlOldT2] = useState(rowDataSelected[6]);

  const [drawingTitle, setDrawingTitle] = useState(rowDataSelected[1]); // t3
  const [drawingNo, setDrawingNo] = useState(rowDataSelected[2]); // t3
  const [compliance, setCompliance] = useState(rowDataSelected[3]); // t3
  const [urlOldT3, setUrlOldT3] = useState(rowDataSelected[4]);

  const [document_avail, setDocument_avail] = useState(rowDataSelected[5]); // t4
  const [sr_avail, setSr_avail] = useState(rowDataSelected[6]); // t4
  const [documentType, setDocumentType] = useState(rowDataSelected[7]); //t4
  const [resultT4, setResultT4] = useState(rowDataSelected[8]); // t4
  const [urlOldT4, setUrlOldT4] = useState(rowDataSelected[9]); // t4

  const [fileType, setFileType] = useState("");
  const [file, setFile] = useState(null);
  const types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"];

  //
  const handleChangeImage = (file) => {
    let selectedFile = file[0];
    //console.log(file[0]) //(selectedFile?.size/1024))

    if (selectedFile) {
      if (types.includes(selectedFile.type)) {
        // delete last uploaded file via url if image changes
        // if (url) {
        //   DeleteByUrl(url);
        // }
        //
        setFile(selectedFile);
      } else {
        setFile(null);
        toastMessage({
          message: "Incorrect Format - Please use (PNG , JPG , JPEG, Pdf)",
        });
      }
    }
  };
  // const { currentColor, currentMode } = useStateContext();
  // const { progress, url } = useStorageTablesFile(file);

  //
  const handleUpdateRow = () => {
    let dataT1 = {
      serial_no: sNo,
      tag_no: tagNo,
      desc: description,
      make_vendor: makeVendor,
      details: details,
      size: size,
      documents: documents,
      result: result,
      // url: url === null ? urlOldT1 : url,
      table_title: table1Title,
    };
    let dataT2 = {
      serial_no: sNo,
      tag_no: tagNo,
      desc: description,
      type_size: typeSize,
      documents: documentsT2,
      result: resultT2,
      // url: url === null ? urlOldT2 : url,
    };
    let dataT3 = {
      serial_no: sNo,
      tag_no: tagNo,
      drawing_title: drawingTitle,
      drawing_no: drawingNo,
      compliance: compliance,
      // url: url === null ? urlOldT3 : url,
    };
    let dataT4 = {
      serial_no: sNo,
      tag_no: tagNo,
      desc: description,
      make_vendor: makeVendor,
      details: details,
      doc_avail: document_avail,
      sr_avail,
      type: documentType,
      result: resultT4,
      // url: url === null ? urlOldT4 : url,
    };
    switch (tableType) {
      case 1:
        // db.collection(companies)
        //   .doc(companyId_constant)
        //   .collection(type)
        //   .doc(fatDataDocId)
        //   .collection("table" + tableType)
        //   .doc(rowDataSelected[9]) // rowDataSelected[9] = id of the document
        //   .update(dataT1)
        //   .then(() => {
        //     toastMessageSuccess({ message: "Row updated Successfully" });
        //     handleClose();
        //   })
        //   .catch((e) => {
        //     toastMessageWarning({ message: "Error ", e });
        //   });
        break;
      case 2:
        // db.collection(companies)
        //   .doc(companyId_constant)
        //   .collection(type)
        //   .doc(fatDataDocId)
        //   .collection("table" + tableType)
        //   .doc(rowDataSelected[7]) // rowDataSelected[7] = id of the document
        //   .update(dataT2)
        //   .then(() => {
        //     toastMessageSuccess({ message: "Row updated Successfully" });
        //     handleClose();
        //   })
        //   .catch((e) => {
        //     toastMessageWarning({ message: "Error ", e });
        //     console.log("TableTemplates: error: ", e);
        //   });
        break;
      case 3:
        // db.collection(companies)
        //   .doc(companyId_constant)
        //   .collection(type)
        //   .doc(fatDataDocId)
        //   .collection("table" + tableType)
        //   .doc(rowDataSelected[5]) // rowDataSelected[5] = id of the document
        //   .update(dataT3)
        //   .then(() => {
        //     toastMessageSuccess({ message: "Row updated Successfully" });
        //     handleClose();
        //   })
        //   .catch((e) => {
        //     toastMessageWarning({ message: "Error ", e });
        //     console.log("TableTemplates: error: ", e);
        //   });
        break;
      case 4:
        // db.collection(companies)
        //   .doc(companyId_constant)
        //   .collection(type)
        //   .doc(fatDataDocId)
        //   .collection("table" + tableType)
        //   .doc(rowDataSelected[10]) // rowDataSelected[10] = id of the document
        //   .update(dataT4)
        //   .then(() => {
        //     toastMessageSuccess({ message: "Row updated Successfully" });
        //     handleClose();
        //   })
        //   .catch((e) => {
        //     toastMessageWarning({ message: "Error ", e });
        //   });
        break;
      default:
        console.log("TableTemplates.js : default case");
    }
  };

  const handleCancel = () => {
    // if (url) {
    //   DeleteByUrl(url);
    // }
    handleClose();
  };

  const handleDeleteDropZone = (url) => {
    // DeleteByUrl(url); // to delete the file from storage
    setFile(null); // to remove the preview
  };

  return (
    <>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          <div className="flex justify-between py-2">
            <div className="w-1/12">
              {" "}
              {/* common in t1,t2,t3 */}
              <TextField
                label="S.No"
                id="outlined-size-small"
                defaultValue="Na"
                size="small"
                value={sNo}
                onChange={(e) => setSNo(e.target.value)}
              />
            </div>

            {tableType === 1 && (
              <>
                <div className="w-1/12">
                  <TextField
                    label="Tag No"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={tagNo}
                    onChange={(e) => setTagNo(e.target.value)}
                  />
                </div>
                <div className="w-2/12">
                  <TextField
                    label="Description"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                  />
                </div>
                <div className="w-1/12">
                  <TextField
                    label="Make/Vendor"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={makeVendor}
                    onChange={(e) => setMakeVendor(e.target.value)}
                  />
                </div>
                <div className="4/12">
                  <TextField
                    title={rowDataSelected[4]}
                    label="Details"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={details}
                    onChange={(e) => setDetails(e.target.value)}
                  />
                </div>
                <div className="w-1/12">
                  <TextField
                    label="Size"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={size}
                    onChange={(e) => setSize(e.target.value)}
                  />
                </div>
                <div className="w-1/12">
                  <TextField
                    label="Document"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={documents}
                    onChange={(e) => setDocuments(e.target.value)}
                  />
                </div>
                <div className="w-1/12">
                  <TextField
                    label="Result"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={result}
                    onChange={(e) => setResult(e.target.value)}
                  />
                </div>
              </>
            )}

            {tableType === 2 && (
              <>
                <div className="w-1/12">
                  <TextField
                    label="Tag No"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={tagNo}
                    onChange={(e) => setTagNo(e.target.value)}
                  />
                </div>
                <div className="w-2/12">
                  <TextField
                    label="Description"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                  />
                </div>
                <div className="w-4/12">
                  <TextField
                    fullWidth
                    label="Type/Size"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={typeSize}
                    onChange={(e) => setTypeSize(e.target.value)}
                  />
                </div>
                <div className="w-1/12">
                  <TextField
                    label="Document"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={documentsT2}
                    onChange={(e) => setDocumentsT2(e.target.value)}
                  />
                </div>
                <div className="w-1/12">
                  <TextField
                    label="Result"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={resultT2}
                    onChange={(e) => setResultT2(e.target.value)}
                  />
                </div>
              </>
            )}

            {tableType === 3 && (
              <>
                <div className="w-2/12">
                  <TextField
                    fullWidth
                    label="Drawing Title"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={drawingTitle}
                    onChange={(e) => setDrawingTitle(e.target.value)}
                  />
                </div>
                <div className="w-3/12">
                  <TextField
                    fullWidth
                    label="Drawing No"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={drawingNo}
                    onChange={(e) => setDrawingNo(e.target.value)}
                  />
                </div>
                <div className="w-2/12">
                  <TextField
                    fullWidth
                    label="Compliance (YES/NO)"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={compliance}
                    onChange={(e) => setCompliance(e.target.value)}
                  />
                </div>
              </>
            )}
            {tableType === 4 && (
              <>
                <div className="w-1/12">
                  <TextField
                    label="Tag No"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={tagNo}
                    onChange={(e) => setTagNo(e.target.value)}
                  />
                </div>
                <div className="w-2/12">
                  <TextField
                    label="Description"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                  />
                </div>
                <div className="w-1/12">
                  <TextField
                    label="Make/Vendor"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={makeVendor}
                    onChange={(e) => setMakeVendor(e.target.value)}
                  />
                </div>
                <div className="4/12">
                  <TextField
                    title={rowDataSelected[4]}
                    label="Details"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={details}
                    onChange={(e) => setDetails(e.target.value)}
                  />
                </div>
                <div className="w-1/12">
                  <TextField
                    label="Document Avl."
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={document_avail}
                    onChange={(e) => setDocument_avail(e.target.value)}
                  />
                </div>
                <div className="w-1/12">
                  <TextField
                    label="Sr No (Yes/No)"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={sr_avail}
                    onChange={(e) => setSr_avail(e.target.value)}
                  />
                </div>
                <div className="w-1/12">
                  <TextField
                    label="Document Type"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={documentType}
                    onChange={(e) => setDocumentType(e.target.value)}
                  />
                </div>
                <div className="w-1/12">
                  <TextField
                    label="Result"
                    id="outlined-size-small"
                    defaultValue="Na"
                    size="small"
                    value={resultT4}
                    onChange={(e) => setResultT4(e.target.value)}
                  />
                </div>
              </>
            )}
          </div>
          {/* image section */}
          <div className="flex justify-center">
            <div
              className={
                currentMode === "Dark"
                  ? "w-2/6 bg-gray-700 rounded-sm p-1 shadow-md"
                  : "w-2/6  bg-gray-100 rounded-sm p-1 shadow-md"
              }
            >
              <InputLabel style={{ marginBottom: "10px" }}>Media</InputLabel>
              <DropzoneArea
                showFileNames
                onChange={(loadedFiles) => handleChangeImage(loadedFiles)}
                dropzoneText="Drag and Drop / Click to ADD Media. MaxFileSize : 5Mb"
                showAlerts={false}
                filesLimit={1}
                maxFileSize={5 * 1024 * 1024} // bytes :"default: 3000000"
                // onDelete={() => handleDeleteDropZone(url)}
                dropzoneClass={
                  currentMode === "Dark"
                    ? "dropZoneClassDark"
                    : "dropZoneClassLight"
                }
              />

              {/* <div className="p-2 block">
                <LinearProgress
                  style={{ marginBottom: "20px" }}
                  variant="determinate"
                  value={progress}
                />
                <div className="text-2xl text-gray-400 flex justify-end ">
                  <p> {progress} % Uploaded</p>
                </div>
                {url ||
                urlOldT1 !== null ||
                urlOldT3 !== null ||
                urlOldT3 !== null ? (
                  <>
                    {url ? (
                      <img alt="" src={url} />
                    ) : (
                      <>
                        {urlOldT1 && (
                          <a target="_blank" href={urlOldT1}>
                            {" "}
                            <img alt="" src={urlOldT1} />{" "}
                          </a>
                        )}
                        {urlOldT1 && (
                          <a target="_blank" href={urlOldT2}>
                            {" "}
                            <img alt="" src={urlOldT2} />{" "}
                          </a>
                        )}
                        {urlOldT1 && (
                          <a target="_blank" href={urlOldT3}>
                            {" "}
                            <img alt="" src={urlOldT3} />{" "}
                          </a>
                        )}
                        {urlOldT4 && (
                          <a target="_blank" href={urlOldT4}>
                            {" "}
                            <img alt="" src={urlOldT4} />{" "}
                          </a>
                        )}

                        {urlOldT1?.includes(".pdf") && (
                          <a
                            target="_blank"
                            href={urlOldT1}
                            data-title="download"
                          >
                            <FileDownload className=" animate-bounce bg-slate-400 shadow-md rounded-sm hover:bg-slate-500" />
                            Pdf
                          </a>
                        )}
                        {urlOldT2?.includes(".pdf") && (
                          <a
                            target="_blank"
                            href={urlOldT2}
                            data-title="download"
                          >
                            <FileDownload className=" animate-bounce bg-slate-400 shadow-md rounded-sm hover:bg-slate-500" />
                            Pdf
                          </a>
                        )}
                        {urlOldT3?.includes(".pdf") && (
                          <a
                            target="_blank"
                            href={urlOldT3}
                            data-title="download"
                          >
                            <FileDownload className=" animate-bounce bg-slate-400 shadow-md rounded-sm hover:bg-slate-500" />
                            Pdf
                          </a>
                        )}
                        {urlOldT4?.includes(".pdf") && (
                          <a
                            target="_blank"
                            href={urlOldT4}
                            data-title="download"
                          >
                            <FileDownload className=" animate-bounce bg-slate-400 shadow-md rounded-sm hover:bg-slate-500" />
                            Pdf
                          </a>
                        )}
                      </>
                    )}
                  </>
                ) : (
                  <Empty
                    description={<span>Please Wait for Preview ...</span>}
                  />
                )}
              </div> */}
            </div>
          </div>
          {/* image section close */}
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <ButtonBasic buttonTitle="Update" onClick={handleUpdateRow} />
        <ButtonBasicCancel
          buttonTitle="Cancel &#x2716;"
          onClick={handleCancel}
        />
      </DialogActions>
    </>
  );
};

function T1({ rowData, type, machineName, fatDataDocId, useAt }) {
  const { currentMode } = useStateContext();
  const [open, setOpen] = useState(false);
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");

  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useMemo(() => {
    // when theme changes then S.No array got reversed. So to stop that This Memonisation is used.
    setSortDirection("");
    //console.log("TableTemplates usememo")
  }, [currentMode]);

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#000" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            {/* <TableRow  sx={{ border: theme.borderDesign }}>
            <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
          </TableRow>

          <TableRow  sx={{ border: theme.borderDesign }}>
            <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
          </TableRow> */}

            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                <span className="pr-1">S.No</span>
                {useAt !== "tableMain" ? (
                  <>
                    {sortDirection === "down" || sortDirection === "" ? (
                      <span
                        className={theme.arrow}
                        onClick={() => setSortDirection("up")}
                      >
                        &#x21E9;{" "}
                      </span>
                    ) : (
                      <span
                        className={theme.arrow}
                        onClick={() => setSortDirection("down")}
                      >
                        &#x21E7;{" "}
                      </span>
                    )}{" "}
                  </>
                ) : null}
              </TableCell>
              <TableCell
                sx={{ border: theme.borderDesign }}
                colSpan={3}
                align="center"
              >
                specification
              </TableCell>
              <TableCell
                sx={{ border: theme.borderDesign }}
                rowSpan={2}
                align="center"
              >
                Details
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Size
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Documents
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Result
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Image
              </TableCell>
            </TableRow>

            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }}>Tag No</TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Description
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Make/Vendor
              </TableCell>
              {/* <TableCell sx={{ border: theme.borderDesign }} colSpan='2'></TableCell> */}
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  sx={{ border: theme.borderDesign }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[2]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[3]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[4]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[5]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[6]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[7]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }} align="center">
                    {data[8] === null ? (
                      "Add One"
                    ) : (
                      <>
                        {data[8]?.includes(".pdf") ? (
                          <FilePdfFilled
                            className="text-red-700"
                            onContextMenu={() =>
                              (window.location.href = data[8])
                            }
                            title="Press right click to open file"
                          />
                        ) : (
                          <PictureFilled
                            onContextMenu={() =>
                              (window.location.href = data[8])
                            }
                            title="Press right click to open file"
                          />
                        )}
                      </>
                    )}
                  </TableCell>
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
        <EditTableRow
          rowDataSelected={rowDataSelected}
          tableType={1}
          type={type}
          fatDataDocId={fatDataDocId}
          machineName={machineName}
          handleClose={handleClose}
        />
      </Dialog>
    </>
  );
}

function T2({ rowData, type, machineName, fatDataDocId, useAt }) {
  const { currentMode } = useStateContext();
  const [open, setOpen] = useState(false);
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");

  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useMemo(() => {
    // when theme changes then S.No array got reversed. So to stop that This Memonisation is used.
    setSortDirection("");
    //console.log("TableTemplates usememo")
  }, [currentMode]);

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#000" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };
  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={7} className='uppercase'>product chamber and door</TableCell>
            </TableRow>
  
            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={7}>Ref. Drawing no: 2861-40</TableCell>
            </TableRow>

            <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={7} className='uppercase'> Make : Lsi </TableCell>
            </TableRow> */}

            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                <span className=" pr-1">S.No</span>
                {useAt !== "tableMain" ? (
                  <>
                    {sortDirection === "down" || sortDirection === "" ? (
                      <span
                        className={theme.arrow}
                        onClick={() => setSortDirection("up")}
                      >
                        &#x21E9;{" "}
                      </span>
                    ) : (
                      <span
                        className={theme.arrow}
                        onClick={() => setSortDirection("down")}
                      >
                        &#x21E7;{" "}
                      </span>
                    )}{" "}
                  </>
                ) : null}
              </TableCell>
              <TableCell
                sx={{ border: theme.borderDesign }}
                colSpan={3}
                align="center"
              >
                specification
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Documents
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Result
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} rowSpan={2}>
                Image
              </TableCell>
            </TableRow>

            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign, maxWidth: "6rem" }}>
                Tag No / Drawing No
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign, maxWidth: "6rem" }}>
                Description
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign, maxWidth: "6rem" }}>
                Type and Size
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  sx={{ border: theme.borderDesign }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[2]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[3]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[4]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[5]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }} align="center">
                    {data[6] === null ? (
                      "Add One"
                    ) : (
                      <>
                        {data[6]?.includes(".pdf") ? (
                          <FilePdfFilled
                            onContextMenu={() =>
                              (window.location.href = data[6])
                            }
                            title="Press right click to open file"
                          />
                        ) : (
                          <PictureFilled
                            onContextMenu={() =>
                              (window.location.href = data[6])
                            }
                            title="Press right click to open file"
                          />
                        )}
                      </>
                    )}
                  </TableCell>
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
        <EditTableRow
          rowDataSelected={rowDataSelected}
          tableType={2}
          type={type}
          fatDataDocId={fatDataDocId}
          machineName={machineName}
          handleClose={handleClose}
        />
      </Dialog>
    </>
  );
}

function T3({ rowData, type, machineName, fatDataDocId, useAt }) {
  const { currentMode } = useStateContext();
  const [open, setOpen] = useState(false);
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");

  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useMemo(() => {
    // when theme changes then S.No array got reversed. So to stop that This Memonisation is used.
    setSortDirection("");
    //console.log("TableTemplates usememo")
  }, [currentMode]);

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#000" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            {/* <TableRow  sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }} colSpan={4} className='uppercase'> Drawing review & verification</TableCell>
            </TableRow> */}

            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }}>
                <span className=" pr-1">S.No</span>
                {useAt !== "tableMain" ? (
                  <>
                    {sortDirection === "down" || sortDirection === "" ? (
                      <span
                        className={theme.arrow}
                        onClick={() => setSortDirection("up")}
                      >
                        &#x21E9;{" "}
                      </span>
                    ) : (
                      <span
                        className={theme.arrow}
                        onClick={() => setSortDirection("down")}
                      >
                        &#x21E7;{" "}
                      </span>
                    )}{" "}
                  </>
                ) : null}
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                {" "}
                Drawing Title{" "}
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                {" "}
                Drawing No{" "}
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                {" "}
                Compliance (YES / NO){" "}
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }} align="center">
                Image
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  sx={{ border: theme.borderDesign }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[2]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[3]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }} align="center">
                    {data[4] === null ? (
                      "Add One"
                    ) : (
                      <>
                        {data[4]?.includes(".pdf") ? (
                          <FilePdfFilled
                            onContextMenu={() =>
                              (window.location.href = data[4])
                            }
                            title="Press right click to open file"
                          />
                        ) : (
                          <PictureFilled
                            onContextMenu={() =>
                              (window.location.href = data[4])
                            }
                            title="Press right click to open file"
                          />
                        )}
                      </>
                    )}
                  </TableCell>
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
        <EditTableRow
          rowDataSelected={rowDataSelected}
          tableType={3}
          type={type}
          fatDataDocId={fatDataDocId}
          machineName={machineName}
          handleClose={handleClose}
        />
      </Dialog>
    </>
  );
}

//////////////////////
function T4({ rowData, type, machineName, fatDataDocId, useAt }) {
  const { currentMode } = useStateContext();
  const [open, setOpen] = useState(false);
  const [rowDataSelected, setRowDataSelected] = useState([]);
  const [sortDirection, setSortDirection] = useState("");

  const handleClickOpen = (data) => {
    setRowDataSelected(data);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useMemo(() => {
    // when theme changes then S.No array got reversed. So to stop that This Memonisation is used.
    setSortDirection("");
    //console.log("TableTemplates usememo")
  }, [currentMode]);

  const theme = {
    borderDesign: currentMode === "Dark" ? "1px solid white" : "1px solid gray",
    backgroundColor: currentMode === "Dark" ? "#000" : "#ddd",
    hoverEffect:
      currentMode === "Dark"
        ? "hover:cursor-pointer hover:bg-gray-800 hover:animate-pulse"
        : "hover:cursor-pointer hover:bg-gray-200 hover:animate-pulse",
    arrow:
      currentMode === "Dark"
        ? "p-1 bg-gray-900 shadow-sm shadow-red-500 rounded-md hover:cursor-pointer hover:shadow-red-500  hover:shadow-md "
        : "p-1 bg-gray-300 shadow-sm  shadow-gray-500 rounded-md hover:cursor-pointer hover:shadow-gray-500  hover:shadow-md",
  };

  return (
    <>
      <TableContainer component={Paper}>
        <Table
          sx={{ minWidth: 650, border: theme.borderDesign }}
          aria-label="simple table"
          size="small"
        >
          <TableHead
            sx={{
              border: theme.borderDesign,
              background: theme.backgroundColor,
            }}
          >
            {/* <TableRow  sx={{ border: theme.borderDesign }}>
            <TableCell sx={{ border: theme.borderDesign }} colSpan={8} className='uppercase'>VACUM CIRCUIT sYstem</TableCell>
          </TableRow>

          <TableRow  sx={{ border: theme.borderDesign }}>
            <TableCell sx={{ border: theme.borderDesign }} colSpan={8}>Ref. Drawing no: 2861-40</TableCell>
          </TableRow> */}

            <TableRow sx={{ border: theme.borderDesign }}>
              <TableCell sx={{ border: theme.borderDesign }}>
                <span className="pr-1">S.No</span>
                {useAt !== "tableMain" ? (
                  <>
                    {sortDirection === "down" || sortDirection === "" ? (
                      <span
                        className={theme.arrow}
                        onClick={() => setSortDirection("up")}
                      >
                        &#x21E9;{" "}
                      </span>
                    ) : (
                      <span
                        className={theme.arrow}
                        onClick={() => setSortDirection("down")}
                      >
                        &#x21E7;{" "}
                      </span>
                    )}{" "}
                  </>
                ) : null}
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign, maxWidth: "5rem" }}>
                Tag No / Drawing No
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Description
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Make/Vendor
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Type & Details
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign, maxWidth: "5rem" }}>
                Document Aavilable (Yes/No)
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign, maxWidth: "5rem" }}>
                Sr.No (Y/No)
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>
                Type of doc
              </TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>Result</TableCell>
              <TableCell sx={{ border: theme.borderDesign }}>Image</TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {(sortDirection === "" ? rowData : rowData?.reverse()).map(
              (data) => (
                <TableRow
                  sx={{ border: theme.borderDesign }}
                  onClick={() =>
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? handleClickOpen(data)
                      : null
                  }
                  className={
                    useAt !== "tableMain" && useAt !== "reportItem"
                      ? theme.hoverEffect
                      : ""
                  }
                >
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[0]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[1]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[2]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[3]}
                  </TableCell>
                  {/* in following we are keeping the data of first row in col 2,5,6,7 */}
                  {/* {data[3] ? <TableCell sx={{ border: theme.borderDesign }} rowSpan={rowData?.length} align = "center">{data[3]}</TableCell> : null } */}

                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[4]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[5]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[6]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[7]}
                  </TableCell>
                  {/* {data[5] ? <TableCell sx={{ border: theme.borderDesign }} rowSpan={rowData?.length} align = "center">{data[5]}</TableCell> : null }
                {data[6] ? <TableCell sx={{ border: theme.borderDesign }} rowSpan={rowData?.length} align = "center">{data[6]}</TableCell> : null }
                {data[7] ? <TableCell sx={{ border: theme.borderDesign }} rowSpan={rowData?.length} align = "center">{data[7]}</TableCell> : null } */}
                  <TableCell sx={{ border: theme.borderDesign }}>
                    {data[8]}
                  </TableCell>
                  <TableCell sx={{ border: theme.borderDesign }} align="center">
                    {data[9] === null ? (
                      "Add One"
                    ) : (
                      <>
                        {data[9]?.includes(".pdf") ? (
                          <FilePdfFilled
                            className="text-red-700"
                            onContextMenu={() =>
                              (window.location.href = data[9])
                            }
                            title="Press right click to open file"
                          />
                        ) : (
                          <PictureFilled
                            onContextMenu={() =>
                              (window.location.href = data[9])
                            }
                            title="Press right click to open file"
                          />
                        )}
                      </>
                    )}
                  </TableCell>
                </TableRow>
              ),
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        maxWidth="lg"
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{"Edit row"}</DialogTitle>
        <EditTableRow
          rowDataSelected={rowDataSelected}
          tableType={4}
          type={type}
          fatDataDocId={fatDataDocId}
          machineName={machineName}
          handleClose={handleClose}
        />
      </Dialog>
    </>
  );
}

export { T1, T2, T3, T4 };
