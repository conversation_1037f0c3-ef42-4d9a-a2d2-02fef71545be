import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { updateDataToCms } from "../functions/cms-infra.functions";
import { useContext, useState } from "react";
import { CmsInfraContext } from "../../../../services/cms-infrastructure/cms-infra.context";

const EditEquipmentForm = ({ item }) => {
  const [currentItem, setCurrentItem] = useState({ ...item });
  const { instruments } = useContext(CmsInfraContext);
  const [code, setCode] = useState("");

  async function handleOnSubmit(e) {
    e.preventDefault();

    const formData = {
      ...currentItem,
      ref_id: "001",
      instrument_code: code.length === 0 ? currentItem?.instrument_code : code,
    };
    let active = false;

    Array.from(e.currentTarget.elements).forEach((field) => {
      if (!field.name) return;
      formData[field.name] = field.value;
    });

    updateDataToCms(formData);
  }

  return (
    <div>
      <form onSubmit={handleOnSubmit}>
        <TextField
          defaultValue={item?.name}
          sx={{ mb: 2 }}
          placeholder="Equipment Name"
          fullWidth
          variant="outlined"
          name="name"
        />
        <TextField
          defaultValue={item?.make}
          sx={{ mb: 2 }}
          placeholder="Make / Model"
          fullWidth
          variant="outlined"
          name="make"
        />
        <TextField
          defaultValue={item?.si_num}
          sx={{ mb: 2 }}
          placeholder="SI Number"
          fullWidth
          variant="outlined"
          name="si_num"
        />
        <TextField
          defaultValue={item?.certificate_no}
          sx={{ mb: 2 }}
          placeholder="Certificate Number"
          fullWidth
          variant="outlined"
          name="certificate_no"
        />
        <TextField
          defaultValue={item?.validity}
          sx={{ mb: 2 }}
          placeholder="Validity"
          fullWidth
          variant="outlined"
          name="validity"
        />
        <FormControl sx={{ mb: 3 }} fullWidth>
          <InputLabel>Select Instrument</InputLabel>
          <Select
            defaultValue={item?.instrument_code}
            label="Select Instrument"
            onChange={(e) => setCode(e.target.value)}
          >
            {instruments.map((item, i) => (
              <MenuItem value={item?.code_no} key={i}>
                {item?.code_no}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Button type="submit" variant="contained" fullWidth>
          Submit
        </Button>
      </form>
    </div>
  );
};

export default EditEquipmentForm;
