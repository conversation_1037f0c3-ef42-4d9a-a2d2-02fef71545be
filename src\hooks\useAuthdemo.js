import React, { useContext, useState, useEffect } from "react";
import axios from "axios";
import { useServerConnection } from "./ServerConnetionContext";
// import { toastMessageWarning } from '../tost';
const AuthContext = React.createContext();
const RoleContext = React.createContext();
const RoleSetterContext = React.createContext();
const UserInfoContext = React.createContext();
const UserInfoSetterContext = React.createContext();
const AuthSetterContext = React.createContext();
const AuthLogoutContext = React.createContext();
//
export function useAuthStatus() {
  return useContext(AuthContext);
}
export function useRole() {
  return useContext(RoleContext);
}
export function useRoleSetter() {
  return useContext(RoleSetterContext);
}
export function useUserInfo() {
  return useContext(UserInfoContext);
}
export function useUserInfoSetter() {
  return useContext(UserInfoSetterContext);
}
export function useAuthStatusSetter() {
  return useContext(AuthSetterContext);
}
export function useLogout() {
  return useContext(AuthLogoutContext);
}
//
export function AuthContextProvider({ children }) {
  const [authStatus, setAuthStatus] = useState(false);
  const [role, setRole] = useState("");
  const [userInfo, setUserInfo] = useState({}); // role context can be removed
  function handleAuthStatus({ email, password }) {
    // letter username will be used
    //
    const data = {
      email: email,
      password: password,
    };
    if (email && password) {
      axios({
        method: "post",
        headers: { "Content-Type": "application/json" },
        url: "",
        data: { ...data },
      })
        .then((res) => {
          if (res?.data?.status === "success") {
            //
            setAuthStatus(true);
            setRole(res.data.message.role);
            setUserInfo({
              email: res?.data.message.email,
              _id: res?.data.message._id,
              role: res?.data.message.role,
            });
            window.sessionStorage.setItem("authStatus", true);
            //window.sessionStorage.setItem('role', res?.data.message.role); // remove 1
            window.sessionStorage.setItem(
              "userInfo",
              JSON.stringify({
                email: res?.data.message.email,
                _id: res?.data.message._id,
                role: res?.data.message.role,
              }),
            ); // letter set an object //done
          }
        })
        .catch((e) => {
          // toastMessageWarning({"message": "Wrong credentials"})
          //
        });
    } else {
      // if refreshing of page happens
      var authFromLocal = window.sessionStorage.getItem("authStatus");
      // var roleFromLocal = window.sessionStorage.getItem('role')// remove 2
      var userInfoFromLocal = JSON.parse(
        window.sessionStorage.getItem("userInfo"),
      );
      if (authFromLocal) {
        setAuthStatus(authFromLocal);
        //setRole(roleFromLocal);
        setUserInfo({ ...userInfoFromLocal });
      }
    }
  }
  function handleLogout() {
    //
    window.sessionStorage.clear();
    setAuthStatus(false);
  }
  useEffect(() => {
    handleAuthStatus("", "");
  }, []);
  return (
    <AuthContext.Provider value={authStatus}>
      <AuthSetterContext.Provider value={handleAuthStatus}>
        <AuthLogoutContext.Provider value={handleLogout}>
          <RoleContext.Provider value={role}>
            <RoleSetterContext.Provider value={setRole}>
              <UserInfoContext.Provider value={userInfo}>
                <UserInfoSetterContext.Provider value={setUserInfo}>
                  {children}
                </UserInfoSetterContext.Provider>
              </UserInfoContext.Provider>
            </RoleSetterContext.Provider>
          </RoleContext.Provider>
        </AuthLogoutContext.Provider>
      </AuthSetterContext.Provider>
    </AuthContext.Provider>
  );
}
