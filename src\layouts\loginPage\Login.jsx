import React, { useEffect, useState } from "react";
import "./loginPage.scss";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../../hooks/AuthProvider";
import { db } from "../../firebase";
import { firebaseLooper } from "../../tools/tool";
import { toastMessage } from "../../tools/toast";
import {
  adminType_constant_temp,
  alluser,
  companies,
  companyId_constant,
} from "../../constants/data";
import UserSessionLog from "../../components/CFR-Report/UserSessionLog";
import SecurityLoggingFunction from "../../components/CFR-Report/SecurityLog";
import { useStateContext } from "../../context/ContextProvider";
import TextField from "@mui/material/TextField";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [users, setusers] = useState([]);
  const [allUsers, setAllUsers] = useState([]);
  const [companyData, setCompanyData] = useState([]);
  const { login, setAdminType } = useAuth();
  const navigate = useNavigate();
  const { currentColorLight, currentMode } = useStateContext();

  // useEffect(() => {
  //   console.log("login:", companyId_constant);
  //   console.log("login2:", adminType_constant_temp);
  //   if (
  //     companyId_constant == "Id_not_set" ||
  //     companyId_constant == "undefined" ||
  //     window.localStorage.getItem("companyId") != companyId_constant
  //   ) {
  //     window.location.reload(false);
  //   }
  // }, []);

  async function handleSubmit(e) {
    e.preventDefault();

    let activate = false;
    for (let index = 0; index < users.length; index++) {
      const element = users[index];
      if (email === element.email) {
        activate = true;
        setAdminType("admin");
        break;
      }
    }

    let inAllEmailList = allUsers?.find((data) => data?.email === email);

    if (activate == false) {
      return toastMessage({
        message: inAllEmailList
          ? "You are not from this company ! Please use a valid email address "
          : "User does not exist in the company ! Please use a valid email address",
      });
    }
    try {
      await login(email, password);
      UserSessionLog("Logged In", email.toLowerCase());
      navigate("/");
    } catch {
      SecurityLoggingFunction(email, "User Entered Incorrect Password");
      toastMessage({ message: "Failed to Login. Incorrect password ! " });
    }
  }

  console.log("Current Color Light:", currentColorLight);
  console.log("Current Mode:", currentMode);

  return (
    <section className="loginPage">
      <div className="illustrationContainer">
        <img src={companyData.url} alt="" />
      </div>
      <div
        className="formContainer"
        style={
          currentMode === "Dark"
            ? { backgroundColor: "#161C24", color: "white" }
            : { backgroundColor: currentColorLight }
        }
      >
        <div className="formTitle">Login</div>
        <div className="formDesc">
          Welcome back! Please login to your account
        </div>
        <form onSubmit={handleSubmit} className="form">
          <div className="mb-8">
            <TextField
              label="Email Address"
              onChange={(e) => setEmail(e.target.value)}
              id="userEmail"
              placeholder="eg. <EMAIL>"
              fullWidth
              size="small"
            />
          </div>
          <div>
            <TextField
              label="Password"
              type={showPassword ? "text" : "password"}
              onChange={(e) => setPassword(e.target.value)}
              id="password"
              placeholder="eg. password123"
              fullWidth
              size="small"
            />
            <div className="flex mt-1">
              <div>
                <input
                  type="checkbox"
                  value={showPassword}
                  onChange={(e) => setShowPassword(e.target.checked)}
                />
              </div>
              <div className="ml-2 text-xs font-bold self-center">
                Show Password
              </div>
            </div>
          </div>

          <div className="linkContainer">
            <Link to="/forgot-password" className="link">
              Forgot Password?
            </Link>
          </div>
          <button>Login</button>
        </form>
      </div>
    </section>
  );
};

export default Login;
